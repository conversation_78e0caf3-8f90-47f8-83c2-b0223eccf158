import { createSlice } from '@reduxjs/toolkit';
import {
    createAgentBankDetails,
    getAgentBankDetailsByAgentId,
    getAllAgentBankDetails,
    getAgentBankDetailsById,
    updateAgentBankDetails,
    deleteAgentBankDetails,
    deleteAgentFirstBankDetails,
    deleteAgentSecondBankDetails
} from '../../actions/action'; // Adjust the import path as necessary

const initialState = {
    agentBankDetails: [],
    loading: false,
    error: null,
};

const agentBankSlice = createSlice({
    name: 'agentBankDetails',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(createAgentBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(createAgentBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.agentBankDetails.push(action.payload); // Add new bank details to the state
            })
            .addCase(createAgentBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getAgentBankDetailsByAgentId.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentBankDetailsByAgentId.fulfilled, (state, action) => {
                state.loading = false;
                state.agentBankDetails = action.payload; // Set the fetched bank details
            })
            .addCase(getAgentBankDetailsByAgentId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getAllAgentBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllAgentBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.agentBankDetails = action.payload; // Set all bank details
            })
            .addCase(getAllAgentBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getAgentBankDetailsById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentBankDetailsById.fulfilled, (state, action) => {
                state.loading = false;
                // Optionally handle the fetched bank detail by ID
            })
            .addCase(getAgentBankDetailsById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(updateAgentBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(updateAgentBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                // Update the specific bank detail in the state
                const index = state.agentBankDetails.findIndex(detail => detail.id === action.payload.id);
                if (index !== -1) {
                    state.agentBankDetails[index] = action.payload;
                }
            })
            .addCase(updateAgentBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(deleteAgentBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteAgentBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                // Remove the deleted bank detail from the state
                state.agentBankDetails = state.agentBankDetails.filter(detail => detail.id !== action.payload);
            })
            .addCase(deleteAgentBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(deleteAgentFirstBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteAgentFirstBankDetails.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(deleteAgentSecondBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteAgentSecondBankDetails.fulfilled, (state, action) => {
                state.loading = false;
            });

            
    },
});

export const { clearError } = agentBankSlice.actions;

export default agentBankSlice.reducer;
