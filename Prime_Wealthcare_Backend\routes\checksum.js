/* const express = require('express');
const crypto = require('crypto');
const axios = require('axios'); // For submitting the updated body to another service.

const router = express.Router();

// Utility function for generating checksum
function generateChecksum(data) {
    const concatenatedString = data.join('|') + '|'; // Add trailing '|'
    return crypto.createHash('sha256').update(concatenatedString, 'utf8').digest('hex');
}

// Define the route
router.post('/submit-data', async (req, res) => {
    try {
        const {
            TransactionID, PaymentOption, ResponseURL,
            ProposalNumber, PremiumAmount, UserIdentifier,
            UserId, FirstName, LastName, Mobile, Email
        } = req.body;

        // Step 1: Prepare the data for checksum
        const data = [
            TransactionID, PaymentOption, ResponseURL,
            ProposalNumber, PremiumAmount, UserIdentifier,
            UserId, FirstName, LastName, Mobile, Email
        ];

        // Step 2: Generate checksum
        const checksum = generateChecksum(data);

        // Step 3: Add checksum to the body
        const updatedBody = { ...req.body, Checksum: checksum };

        // Optional: Step 4: Forward the updated body to another endpoint
        const submissionUrl = 'https://example.com/submit-payment'; // Replace with your target URL
        const response = await axios.post(submissionUrl, updatedBody);

        // Step 5: Respond to the frontend
        res.json({ success: true, message: 'Data submitted successfully', response: response.data });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

module.exports = router;
 */






const express = require('express');
const crypto = require('crypto');

const router = express.Router();

// Utility function for generating checksum
function generateChecksum(data) {
    const concatenatedString = data.join('|') + '|'; // Add trailing '|'
    return crypto.createHash('sha256').update(concatenatedString, 'utf8').digest('hex');
}

// Define the route
router.post('/generate-checksum', (req, res) => {
    const {
        TransactionID, PaymentOption, ResponseURL,
        ProposalNumber, PremiumAmount, UserIdentifier,
        UserId, FirstName, LastName, Mobile, Email
    } = req.body;

    const data = [
        TransactionID, PaymentOption, ResponseURL,
        ProposalNumber, PremiumAmount, UserIdentifier,
        UserId, FirstName, LastName, Mobile, Email
    ];

    try {
        const checksum = generateChecksum(data);
        res.json({ success: true, checksum });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

module.exports = router;
 