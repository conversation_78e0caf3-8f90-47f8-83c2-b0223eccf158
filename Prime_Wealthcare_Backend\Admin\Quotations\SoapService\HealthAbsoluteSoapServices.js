const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../knexfile');
const db = knex(knexConfig.development);
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');

// Environment variables
require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTION; // Use the SOAP Action from the .env file
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

// Generate Member XML dynamically
const generateMemberXML = (memberData) => {
    return memberData
        .map(
            (memberData, index) => `<Member>
            <MemberId>${index + 1}</MemberId>
            <AbhaNo />
            <InsuredName>${memberData.insuredName}</InsuredName>
            <InsuredDob>${formatDate(memberData.insuredDob)}</InsuredDob>  
            <InsuredGender></InsuredGender>
            <InsuredOccpn>${memberData.occupation}</InsuredOccpn>
            <CoverType>${memberData.coverType}</CoverType>
            <SumInsured>${memberData.sumInsured}</SumInsured>
            <DeductibleDiscount />
            <Relation>${memberData.relation}</Relation>
            <NomineeName>Test Nominee</NomineeName>
            <NomineeRelation></NomineeRelation>
            <AnualIncome />
            <Height>${memberData.insuredHeight}</Height>
            <Weight>${memberData.insuredWeight}</Weight>
            <NomineeAge>55</NomineeAge>
            <AppointeeName />
        <AptRelWithNominee />
        <Smoking>${memberData.isSmoking}</Smoking>
        <Tobacco>${memberData.isTobacco}</Tobacco>
        <IsGoodHealth>Y</IsGoodHealth>
        <IsExistingAbsolutePolicy>N</IsExistingAbsolutePolicy>
        <AdditionalInformation />
        ${generateNomineeDetailsXML(memberData.nominee || {})}
        </Member>`)
        .join("");
};

// Generate SOAP body dynamically
const SOAP_BODY = (uid, quotationData, membersXML) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
            <tem:Product>HealthAbsolute</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
  <Uid>${uid}</Uid>
 <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
  <SentToOutSourcePrint>0</SentToOutSourcePrint>
  <WinNo />
  <ApplicationNo />
  <PolicyHeader>
    <PolicyStartDate>${quotationData.start_date}</PolicyStartDate>
    <PolicyEndDate>${quotationData.end_date}</PolicyEndDate>
    <AgentCode>${process.env.AGENT_CODE}</AgentCode>
    <BranchCode>${process.env.BRANCH_CODE}</BranchCode>
    <MajorClass>FHA</MajorClass>
    <ContractType>FHA</ContractType>
    <METHOD>ENQ</METHOD>
    <PolicyIssueType>I</PolicyIssueType>
    <PolicyNo />
    <ClientID></ClientID>
    <ReceiptNo />
  </PolicyHeader>
  <POS_MISP>
    <Type />
    <PanNo />
  </POS_MISP>
  <Client>
    <ClientCategory />
    <ClientType>I</ClientType>
    <CreationType>C</CreationType>
    <Salutation></Salutation>
    <FirstName></FirstName>
    <LastName></LastName>
    <DOB></DOB>
    <Gender></Gender>
    <MaritalStatus></MaritalStatus>
    <Occupation></Occupation>
    <PANNo></PANNo>
    <GSTIN />
    <AadharNo />
    <CKYCNo></CKYCNo>
    <CKYCRefNo></CKYCRefNo>
    <EIANo />
    <Address1>
      <AddrLine1></AddrLine1>
      <AddrLine2></AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${quotationData.pincode}</Pincode>
      <City>Bhavnagar</City>
      <State>Gujarat</State>
      <Country>IND</Country>
      <AddressType>R</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo>9829876493</MobileNo>
      <EmailAddr><EMAIL></EmailAddr>
    </Address1>
    <Address2>
      <AddrLine1></AddrLine1>
      <AddrLine2></AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${quotationData.pincode}</Pincode>
      <City></City>
      <State>Gujarat</State>
      <Country>IND</Country>
      <AddressType>K</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo />
      <EmailAddr />
    </Address2>
    <VIPFlag>N</VIPFlag>
    <VIPCategory />
  </Client>
  <Receipt>
    <UniqueTranKey></UniqueTranKey>
    <CheckType />
    <BSBCode />
    <TransactionDate></TransactionDate>
    <ReceiptType>IVR</ReceiptType>
    <Amount></Amount>
    <TCSAmount />
    <TranRefNo></TranRefNo>
    <TranRefNoDate></TranRefNoDate>
  </Receipt>
    <Risk>
    <eNach>N</eNach>
    <PolicyType>${quotationData.policyType}</PolicyType>
    <Duration>${quotationData.duration}</Duration>
    <Installments>FULL</Installments>
    <PaymentType>CC</PaymentType>
    <IsFgEmployee>N</IsFgEmployee>
    <BranchReferenceID />
    <FGBankBranchStaffID />
    <BankStaffID />
    <BankCustomerID />
    <BancaChannel />
    <PartnerRefNo />
    <PayorID />
    <PayerName />
    <BeneficiaryDetails>
        ${membersXML}
    </BeneficiaryDetails>
  </Risk>
</Root>]]></tem:XML>
        </tem:CreatePolicy>
    </soapenv:Body>
</soapenv:Envelope>
`;

// Helper function to format date to DD/MM/YYYY
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
};

// Function to save SOAP response to database
const saveSoapResponse = async (quotationId, memberData, responseData) => {
    try {
        // Extract CreatePolicyResult with more robust error handling
        let createPolicyResult;

        if (responseData?.['s:Envelope']?.['s:Body']?.[0]?.['CreatePolicyResponse']?.[0]?.['CreatePolicyResult']?.[0]) {
            createPolicyResult = responseData['s:Envelope']['s:Body'][0]['CreatePolicyResponse'][0]['CreatePolicyResult'][0];
        } else {
            console.error('Response structure:', JSON.stringify(responseData, null, 2));
            throw new Error("Invalid response structure");
        }

        // Parse XML content with error handling
        let xmlContent;
        try {
            xmlContent = await parseStringPromise(createPolicyResult);
        } catch (parseError) {
            console.error('XML Parsing Error:', parseError);
            throw parseError;
        }

        // Validate XML structure
        if (!xmlContent?.Root?.Policy?.[0]) {
            throw new Error('Invalid XML structure: Missing Root or Policy data');
        }

        const policy = xmlContent.Root.Policy[0];
        const outputRes = policy.OutputRes?.[0];

        if (!outputRes) {
            throw new Error('Missing OutputRes in policy data');
        }

        // Prepare data for each member
        const soapResponses = memberData.map((member) => {
            // Find matching member in XML
            const xmlMember = policy.InputParameters?.[0]?.BeneficiaryDetails?.[0]?.Member?.find(
                m => m.MemberId?.[0] === String(member.member_id_no)
            );

            if (!xmlMember) {
                console.warn(`No XML data found for member ${member.member_id_no}`);
                return null;
            }

            return {
                quotation_id: quotationId,
                member_name: member.insuredName,
                cover_type: member.coverType,
                sum_insured: member.sumInsured,
                relation: member.relation,
                status: 'COMPLETED',
                base_premium: extractNumericValue(outputRes.BasePremium?.[0]),
                term_premium: extractNumericValue(outputRes.TermPremium?.[0]),
                family_discount_rate: extractNumericValue(outputRes.FmlyDiscRate?.[0]),
                family_discount: extractNumericValue(outputRes.FamilyDiscount?.[0]),
                premium_without_service_tax: extractNumericValue(outputRes.PremWithoutServTax?.[0]),
                premium_with_load: extractNumericValue(outputRes.PremWithLoad?.[0]),
                premium_amount: extractNumericValue(outputRes.PremiumAmt?.[0]),
                service_tax_rate: extractNumericValue(outputRes.ServiceTaxRate?.[0]),
                service_tax: extractNumericValue(outputRes.ServiceTax?.[0]),
                premium_with_service_tax: extractNumericValue(outputRes.PremWithServTax?.[0]),
                bmi: extractNumericValue(xmlMember.BMI?.[0]),
                bmi_loading_percent: extractNumericValue(xmlMember.BMILoadingPercent?.[0]),
                per_person_premium: extractNumericValue(xmlMember.PerPersonPremium?.[0])
            };
        }).filter(Boolean); // Remove null entries

        if (soapResponses.length === 0) {
            throw new Error('No valid responses to save');
        }

        return await db('soap_responses').insert(soapResponses);
    } catch (error) {
        console.error('SOAP Response Error:', error);
        throw error;
    }
};

// Helper function to safely extract numeric values
const extractNumericValue = (value) => {
    if (value === undefined || value === null) return null;

    // Convert to string first to handle different input types
    const stringValue = String(value);

    // Remove any non-numeric characters except decimal point
    const numericString = stringValue.replace(/[^\d.]/g, '');

    // Convert to number
    const numericValue = parseFloat(numericString);

    // Return number or null if conversion fails
    return isNaN(numericValue) ? null : numericValue;
};

// Modified sendSOAPRequest function
const sendSOAPRequest = async (memberData, quotationData, quotationId) => {
    try {
        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };

        // Ensure memberData is an array
        const membersArray = Array.isArray(memberData) ? memberData : [memberData];

        // Generate unique UID and Member XML
        const uniqueUID = uuidv4();
        const membersXML = generateMemberXML(membersArray);


        // Generate SOAP body dynamically
        const requestBody = SOAP_BODY(uniqueUID, quotationData, membersXML);
        console.log('SOAP Envelope:', requestBody);
        // Send the SOAP request
        const response = await axios.post(SOAP_API_URL, requestBody, { headers });

        // Parse the XML response
        const jsonResponse = await parseStringPromise(response.data);
        const logData = {
            quotation_number: quotationData.quotation_number,
            request_body: requestBody, // Raw XML request
            response_body: response.data, // Raw XML response
            status: 'SUCCESS',
            error_message: null,
            created_by: quotationData.Created_by || 'SYSTEM',
            customer_name: membersArray[0]?.insuredName || 'UNKNOWN',
            insurance_company: (quotationData.insurance_company || 'FG Insurance'),
            product_name: (quotationData.product || 'Health Total'),
            created_at: db.fn.now()
        };

        await db('quotations_logs').insert(logData);

        // Save the SOAP response to database
        await saveSoapResponse(quotationId, membersArray, jsonResponse);

        return jsonResponse;
    } catch (error) {
        console.error("SOAP Request Error Details:", {
            message: error.message,
            status: error.response ? error.response.status : "No status",
            data: error.response ? error.response.data : "No data",
        });

        // Optionally, save error response
        await saveSoapResponse(quotationId, memberData, {
            status: 'FAILED',
            error: error.message
        });

        throw new Error(`SOAP Request Failed: ${error.message}`);
    }
};

module.exports = { sendSOAPRequest };
