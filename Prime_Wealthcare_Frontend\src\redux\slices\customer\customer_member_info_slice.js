import { createSlice } from '@reduxjs/toolkit';
import { createCustomerMemberInfo, updateMember, deleteMember, getMemberById, getAllMembers, getMemberByCustomerId } from '../../actions/action';
import { toast } from 'react-toastify';

// customer Info Slice
const customer_member_info_slice = createSlice({
    name: 'customerMemberInfo',
    initialState: {
        customerMember: [], // to store list of customers
        customerMemberDetails: null, // to store a single customer's details
        loading: false, // for loading state
        error: null, // for error handling
    },
    reducers: {
        // Any additional synchronous actions can be defined here if needed
        clearCustomerMemberDetails(state) {
            state.customerMemberDetails = null;
            state.customerMember = [];
        },
    },
    extraReducers: (builder) => {
        // Handle createcustomerInfo (creating a new customer)
        builder
            .addCase(createCustomerMemberInfo.pending, (state) => {
                state.loading = true;
            })
            .addCase(createCustomerMemberInfo.fulfilled, (state, action) => {
                state.loading = false;
                state.customerMember.push(action.payload); // Add the new customer to the list
                //toast.success('Customer Member created successfully');
            })
            .addCase(createCustomerMemberInfo.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to create Member');
            })
            .addCase(updateMember.pending, (state) => {
                state.loading = true;
            })
            .addCase(updateMember.fulfilled, (state, action) => {
                const index = state.customerMember.findIndex((member) => member.id === action.payload.id);
                if (index !== -1) {
                    state.customerMember[index] = action.payload; // Update the member
                }
                state.loading = false;
                // toast.success('Member updated successfully');
            })
            .addCase(updateMember.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                //toast.error('Failed to update Member');
            })
            .addCase(deleteMember.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteMember.fulfilled, (state, action) => {
                state.customerMember = state.customerMember.filter((member) => member.id !== action.payload);
                state.loading = false;
                toast.success('Member deleted successfully');
            })
            .addCase(deleteMember.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to delete Member');
            })
            .addCase(getMemberById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getMemberById.fulfilled, (state, action) => {
                state.loading = false;
                state.customerMemberDetails = action.payload; // Store the customer details
            })
            .addCase(getMemberById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                //toast.error('Failed to load member');
            })
            .addCase(getMemberByCustomerId.pending, (state) => {
                state.loading = true;
            })
            .addCase(getMemberByCustomerId.fulfilled, (state, action) => {
                state.loading = false;
                state.customerMember = action.payload; // Store the members                
            })
            .addCase(getMemberByCustomerId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to load member');
            });
    },
});

// Export the synchronous actions, if any
export const { clearCustomerMemberDetails } = customer_member_info_slice.actions;

// Export the reducer to be included in the store
export default customer_member_info_slice.reducer;