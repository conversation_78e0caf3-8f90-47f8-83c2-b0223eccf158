import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Avatar } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import CustomTableNew from '../../components/table/CustomTableNew';
import SearchBar from '../../components/table/SearchBar';
import IconActions from '../../components/table/IconActions';
import DropDown from '../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../components/DeletePopup';
import SuccessPopup from '../../components/SuccessPopUp';
import { getAllCustomer, getCustomerAndAddress } from '../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../components/ExportToPDF';
import { usePermissions } from '../../hooks/usePermissions';

const CustomerMasterPage = () => {

    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [selectedOption, setSelectedOption] = useState('none');
    const [selectedRows, setSelectedRows] = useState([]);
    const [deletedIds, setDeletedIds] = useState([]);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const customer = useSelector(state => state.customerReducer.customerAndAddress);

    const  permissions  = usePermissions('Customer','Customer Master');


    useEffect(() => {
        dispatch(getCustomerAndAddress());
    }, [dispatch]);

    const processedCustomerData = customer?.map((item) => ({
        ...item,
        contact_name: item.customer_category === 'individual'
            ? item.full_name || 'NA'
            : item.company_name || 'NA'
    }));
    const columns = [
        // {  headerName: 'Actions'},
        {
            field: 'contact_name',
            headerName: 'Contact Name',
            valueGetter: (params) => {
                const customer = params.row;
                if (!customer) return 'NA'; // Fallback for missing row data
                return customer.customer_category === 'individual'
                    ? customer.first_name || 'NA'
                    : customer.company_name || 'NA';
            }
        },
        { field: 'account_name', headerName: 'Account Name' },
        { field: 'hierarchy', headerName: 'Hierarchy' },
        { field: 'current_city', headerName: 'City' },
        { field: 'current_state', headerName: 'State' },
        // { field: 'email', headerName: 'Email' },
        { field: 'mobile', headerName: 'Mobile' },
        { field: 'created_at', headerName: 'Created At' },
        { field: 'updated_at', headerName: 'Updated At' },
    ];

    const handleAdd = () => {
        navigate('/dashboard/customer-personal-information');
    };

    const handleExportToPDF = () => {
    };

    const handleAllClick = () => {
    };

    const handleActiveClick = () => {
    };

    const handleInactiveClick = () => {
    };

    const handleRefreshClick = () => {
    };

    const handleEdit = (id) => {
        if (!permissions?.can_edit) {
            return;
          }
        //navigate(`/customer/edit/${row.id}`);
        navigate(`/dashboard/customer-personal-information/${id}`);
    };

    const handleDelete = (row) => {
        setSelectedItem(row);
        setOpenDeletePopup(true);
    };

    const handleConfirmDelete = () => {
        if (selectedItem) {
            setDeletedIds([...deletedIds, selectedItem.id]);
            setOpenDeletePopup(false);
            setOpenSuccessPopup(true);
        }
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleReinstate = (row) => {
    };

    const handleView = (row) => {
        navigate(`/customer/view/${row.id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelectedRows => {
            if (prevSelectedRows.includes(id)) {
                return prevSelectedRows.filter(selectedId => selectedId !== id);
            } else {
                return [...prevSelectedRows, id];
            }
        });
    };

    const handleSelectAll = (isSelected) => {
        if (isSelected) {
            const allIds = customer.map(customer => customer.id);
            setSelectedRows(allIds);
        } else {
            setSelectedRows([]);
        }
    };

    const dataMapping = {
        'Contact Name': 'first_name',
        'Account Name': 'company_name',
        'Hierarchy': 'hierarchy',
        'City': 'city_name',
        'State': 'state',
        'Mobile': 'mobile',
        'Email': 'email',
        'Created At': 'created_at',
        'Updated At': 'updated_at',
        'Status': 'status'
    };
    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {/* Header Section */}
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 2, sm: 0 }
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: { xs: '100%', sm: 'auto' }
                    }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Customer" pageName="List" />
                    </Box>
                    <ButtonGroup
                        variant="outlined"
                        sx={{
                            borderRadius: 1,
                            width: { xs: '50%', sm: 'auto' },
                            '& .MuiButton-root': {
                                flex: { xs: 1, sm: 'initial' }
                            }
                        }}
                    >
                     {permissions.can_add && (
                        <Button
                            onClick={handleAdd}
                            sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
                        >
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={customer}
                            headNames={['Contact Name', 'Account Name', "Hierarchy", 'City', 'State', 'Mobile', 'Email', 'Created At', 'Updated At', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="customerData.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Customer Report"
                            disabled
                        />
                    </ButtonGroup>
                </Box>

                {/* Search and Filter Section */}
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mt: -1,
                    paddingBottom: '1rem',
                    ml: { xs: 2, sm: 5 },
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 2, sm: 0 }
                }}>
                    <DropDown
                        //  label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                        sx={{ width: { xs: '100%', sm: 'auto' } }}
                    />
                    <Box sx={{
                        display: 'flex',
                        gap: 2,
                        width: { xs: '100%', sm: 'auto' },
                        flexDirection: { xs: 'column', md: 'row' }
                    }}>
                        {/* <SearchBar
                            placeholder="Search..."
                            sx={{ width: { xs: '100%', sm: 'auto' } }}
                        />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        /> */}
                    </Box>
                </Box>

                {/* Table Section */}
                <Box sx={{
                    overflowX: 'auto',
                    width: '100%'
                }}>
                    <CustomTableNew
                        actionsInFirstColumn={true}
                        showEditButton={true}
                        data={processedCustomerData || []}
                        isCustomerPage={true}
                        columns={columns}
                        onEdit={handleEdit}
                        selectedRows={selectedRows}
                        onSelectionChange={handleSelectionChange}
                        onSelectAll={handleSelectAll}
                        showOnlyEdit={true}
                        deletedIds={deletedIds}
                        setDeletedIds={setDeletedIds}
                        sx={{
                            '& .MuiTableCell-root': {
                                padding: { xs: '8px 4px', sm: '16px' }
                            }
                        }}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.customer_full_name : ''}
            />

            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.customer_full_name : ''}
            />
        </Container>
    );
}

export default CustomerMasterPage;
