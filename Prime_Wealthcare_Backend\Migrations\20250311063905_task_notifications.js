/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.createTable('task_notifications', function (table) {
        // Primary key
        table.increments('id').primary();

        // Foreign keys
        table.integer('user_id').unsigned().notNullable();
        table.foreign('user_id').references('id').inTable('employee_personal_info').onDelete('CASCADE');

        table.integer('task_id').unsigned().notNullable();
        table.foreign('task_id').references('id').inTable('tasks').onDelete('CASCADE');

        // Message content
        table.text('message').notNullable();

        // Read status
        table.boolean('is_read').defaultTo(false);

        // Timestamp
        table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
        table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
        table.integer('created_by').unsigned().nullable();
        table.integer('updated_by').unsigned().nullable();
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
    return knex.schema.dropTable('task_notifications');
};
