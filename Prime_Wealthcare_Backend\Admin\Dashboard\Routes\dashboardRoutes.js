const express = require('express');
const dashboardController = require('../Controllers/dashboardController');
const router = express.Router();

router.get('/totalPremiumInTimePeriod', dashboardController.getTotalPremiumInTimePeriod);
// Get proposal data by insurance company
router.get('/proposals-by-company', dashboardController.getProposalsByInsuranceCompany);

router.get('/proposalsForCurrentFinancialYear', dashboardController.getCurrentFinancialYearProposals);

router.get('/proposals-by-agent', dashboardController.getProposalsByAgent);

router.get('/top10Branches', dashboardController.getTop10Branches);

router.get('/top20Agents', dashboardController.getTop20Agents);

module.exports = router;