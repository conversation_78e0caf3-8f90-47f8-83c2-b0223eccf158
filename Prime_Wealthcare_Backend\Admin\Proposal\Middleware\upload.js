const path = require('path');
const fs = require('fs');
const multer = require('multer');

const uploadDir = process.env.UPLOAD_DIR;

// Create uploads directory if it doesn't exist
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
} 
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      try {
        // You can still use req.body.data if you like, or just folder by policy_number:
        const policyNum = file.originalname; 
        const dir = path.join(uploadDir, 'proposals', policyNum);
        fs.mkdirSync(dir, { recursive: true });
        cb(null, dir);
      } catch (err) {
        console.error('✖ destination error:', err);
        cb(err);
      }
    },
  
    filename: (req, file, cb) => {
      try {
        // file.originalname === your policy_number (third param of append)
        const policyNum = file.originalname;
  
        // derive extension from MIME type
        let ext = '';
        switch (file.mimetype) {
          case 'application/pdf':   ext = '.pdf'; break;
          case 'image/jpeg':        ext = '.jpg'; break;
          case 'image/png':         ext = '.png'; break;
          default:
            // fallback if you appended name with extension already
            ext = path.extname(file.originalname);
        }
  
        const finalName = `${policyNum}${ext}`;
        cb(null, finalName);
      } catch (err) {
        cb(err);
      }
    }
  });

const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Only jpg, jpeg, png, and pdf files are allowed.'), false);
    }
};

const upload = multer({
    storage,
    fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    onError: function (err, next) {
        console.error('Multer encountered an error:', err);
        next(err);
    }
});

module.exports = upload;
