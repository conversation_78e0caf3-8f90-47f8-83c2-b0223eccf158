import React, { useEffect, useMemo, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Avatar } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import CustomTableNew from '../../components/table/CustomTableNew';
import SearchBar from '../../components/table/SearchBar';
import IconActions from '../../components/table/IconActions';
import DropDown from '../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../components/DeletePopup';
import SuccessPopup from '../../components/SuccessPopUp';
import { getAllMasterProducts, getAllQuotationsByUserId, getCustomerById } from '../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../components/ExportToPDF';
import { Grid, Typography } from '@mui/material';
//import { getAllQuotations } from '../../../../Prime_Wealthcare_Backend/Admin/Quotations/Models/quotations_model';
import { usePermissions } from '../../hooks/usePermissions';

const QuotationsListPage = () => {

    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { user } = useSelector(state => state.auth);
    const [selectedOption, setSelectedOption] = useState('none');
    const [selectedRows, setSelectedRows] = useState([]);
    const [deletedIds, setDeletedIds] = useState([]);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const customer = useSelector(state => state.customerReducer.customerDetails);
    const AllQuotations = useSelector(state => state.quotationReducer.quotationResponses);
    const [statusFilter, setStatusFilter] = useState('ALL');

    const permissions = usePermissions('Quotation', 'Quotation List');


    useEffect(() => {
        dispatch(getAllQuotationsByUserId(user.userId));
    }, [dispatch]);

    useEffect(() => {
        if (AllQuotations && AllQuotations.length > 0) {
            // Get unique customer IDs from quotations
            const customerIds = [...new Set(AllQuotations.map(quote => quote.customer_id))];

            // Fetch data for each unique customer
            customerIds.forEach(customerId => {
                if (customerId) {  // Add check for valid customer_id
                    dispatch(getCustomerById(customerId));
                }
            });
        }
    }, [AllQuotations, dispatch]);

    // Initialize with dummy data
    const sortedCustomer = AllQuotations || [];
    const products = useSelector(state => state.mainProductReducer.product);

    // Add this useEffect to fetch products
    useEffect(() => {
        dispatch(getAllMasterProducts());
    }, [dispatch]);

    const quotationCounts = useMemo(() => {
        if (!AllQuotations) return {
            total: 0,
            inProgress: 0,
            submitted: 0,
            lost: 0,
        };

        // Helper function to parse DD/MM/YYYY format
        const parseDate = (dateStr) => {
            const [day, month, year] = dateStr.split('/');
            return new Date(year, month - 1, day);
        };

        // Count submitted quotations
        const submittedQuotations = AllQuotations.filter(q => q.status === 'SUBMITTED');

        // Identify lost quotations (those past valid_until date AND status is pending)
        const lostQuotations = AllQuotations.filter(q => {
            if (!q.valid_until) return false;
            const validUntilDate = parseDate(q.valid_until);
            const currentDate = new Date();
            // Convert status to uppercase before comparison
            const status = q.status?.toUpperCase() || '';
            return validUntilDate < currentDate && status === 'PENDING';
        });

        const total = AllQuotations.length;
        const submitted = submittedQuotations.length;
        const lost = lostQuotations.length;

        return {
            total,
            inProgress: total - submitted - lost,
            submitted,
            lost,
        };
    }, [AllQuotations]);

    // Filter quotations based on status
    const filteredQuotations = useMemo(() => {
        if (!AllQuotations) return [];

        const parseDate = (dateStr) => {
            const [day, month, year] = dateStr.split('/');
            return new Date(year, month - 1, day);
        };

        // Convert statuses to uppercase
        const quotationsWithUppercaseStatus = AllQuotations.map(quotation => ({
            ...quotation,
            status: quotation.status?.toUpperCase() || ''
        }));

        switch (statusFilter) {
            case 'LOST':
                return quotationsWithUppercaseStatus.filter(q => {
                    if (!q.valid_until) return false;
                    const validUntilDate = parseDate(q.valid_until);
                    const currentDate = new Date();
                    return validUntilDate < currentDate && q.status === 'PENDING';
                });
            case 'SUBMITTED':
                return quotationsWithUppercaseStatus.filter(q => q.status === 'SUBMITTED');
            case 'IN_PROGRESS':
                return quotationsWithUppercaseStatus.filter(q => {
                    const isLost = q.valid_until &&
                        parseDate(q.valid_until) < new Date() &&
                        q.status === 'PENDING';
                    return !isLost && q.status !== 'SUBMITTED';
                });
            case 'ALL':
            default:
                return quotationsWithUppercaseStatus;
        }
    }, [AllQuotations, statusFilter]);

    const columns = [
        // {  headerName: 'Actions'},
        {
            field: 'quotation_number',
            headerName: 'Quotation Number',

        },
        { field: 'created_at', headerName: 'Quotation Date', type: 'date' },
        {
            field: 'first_name',
            headerName: 'Contact Name',


        },
        {
            field: 'mobile',
            headerName: 'Mobile',

        },
        // {
        //     field: 'email',
        //     headerName: 'Email',

        // },
        // { field: '', headerName: 'Sum Insured' },
        { field: 'product_name', headerName: 'Product' },

        { field: 'policyType', headerName: 'Product Type' },

        // { field: '', headerName: 'Sales Stage' },
        { field: 'agent_first_name', headerName: 'RM Name' },
        { field: 'agent_id', headerName: 'Code' },
        { field: 'group_code', headerName: 'Group Code' },
        { field: 'valid_until', headerName: 'Valid Until', type: 'date' },
        //   { field: 'created_at', headerName: 'Created At' },
        // { field: '', headerName: 'Updated At' },
    ];

    const handleAdd = () => {
        navigate('/dashboard/quotations');
    };

    const handleExportToPDF = () => {
    };

    const handleAllClick = () => {
    };

    const handleActiveClick = () => {
    };

    const handleInactiveClick = () => {
    };

    const handleRefreshClick = () => {
    };


    const handleView = (id, source_table) => {
        const quotation = AllQuotations.find(q => q.id === id && q.source_table === source_table);
        if (!quotation) {
            console.error('Quotation not found');
            return;
        }

        let tableSource;
        if (quotation.source_table === 'quotations') {
            tableSource = 'quotations';
        } else if (quotation.source_table === 'pa_quotations') {
            tableSource = 'pa_quotations';
        } else {
            console.error('Invalid source_table:', quotation.source_table);
            return;
        }

        navigate(`/dashboard/quotation-overview/${tableSource}/${id}`);
    };
    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelectedRows => {
            if (prevSelectedRows.includes(id)) {
                return prevSelectedRows.filter(selectedId => selectedId !== id);
            } else {
                return [...prevSelectedRows, id];
            }
        });
    };

    const handleSelectAll = (isSelected) => {
        if (isSelected) {
            const allIds = sortedCustomer.map(customer => customer.id);
            setSelectedRows(allIds);
        } else {
            setSelectedRows([]);
        }
    };

    const dataMapping = {
        'Contact Name': 'first_name',
        'Account Name': 'company_name',
        'Hierarchy': 'hierarchy',
        'City': 'city_name',
        'State': 'state',
        'Mobile': 'mobile',
        'Email': 'email',
        'Created At': 'created_at',
        'Updated At': 'updated_at',
        'Status': 'status'
    };
    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {/* Header Section */}
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 2, sm: 0 }
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: { xs: '100%', sm: 'auto' }
                    }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Quotation" pageName="List" />
                    </Box>
                    <ButtonGroup
                        variant="outlined"
                        sx={{
                            borderRadius: 1,
                            width: { xs: '50%', sm: 'auto' },
                            '& .MuiButton-root': {
                                flex: { xs: 1, sm: 'initial' }
                            }
                        }}
                    >
                        {permissions.can_add && (
                            <Button
                                onClick={handleAdd}
                                sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
                            >
                                New
                            </Button>
                        )}
                        <ExportToPDF
                            data={sortedCustomer}
                            headNames={['Contact Name', 'Account Name', "Hierarchy", 'City', 'State', 'Mobile', 'Email', 'Created At', 'Updated At', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="customerData.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Customer Report"
                            disabled
                        />
                    </ButtonGroup>
                </Box>

                {/* Search and Filter Section */}
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mt: -1,
                    paddingBottom: '1rem',
                    ml: { xs: 2, sm: 5 },
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 2, sm: 0 }
                }}>
                    <DropDown
                        //  label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },

                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                        sx={{ width: { xs: '100%', sm: 'auto' } }}
                    />
                    <Box sx={{
                        display: 'flex',
                        gap: 2,
                        width: { xs: '100%', sm: 'auto' },
                        flexDirection: { xs: 'column', md: 'row' }
                    }}>
                        {/* <SearchBar
                            placeholder="Search..."
                            sx={{ width: { xs: '100%', sm: 'auto' } }}
                        />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        /> */}
                    </Box>
                </Box>

                <Grid container spacing={3} padding={2}>
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setStatusFilter('ALL')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #16A085`,
                                cursor: 'pointer',
                                bgcolor: statusFilter === 'ALL' ? '#e8f5e9' : 'inherit',
                                '&:hover': { bgcolor: '#e8f5e9' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>All [Quote Stage]</Typography>
                            <Typography variant='h6' sx={{ color: '#16A085', fontWeight: 'bold' }}>{quotationCounts.total}</Typography>
                        </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setStatusFilter('IN_PROGRESS')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #FFC300`,
                                cursor: 'pointer',
                                bgcolor: statusFilter === 'IN_PROGRESS' ? '#fff3e0' : 'inherit',
                                '&:hover': { bgcolor: '#fff3e0' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>In Progress</Typography>
                            <Typography variant='h6' sx={{ color: '#FFC300', fontWeight: 'bold' }}>{quotationCounts.inProgress}</Typography>
                        </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setStatusFilter('SUBMITTED')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #7d3c98`,
                                cursor: 'pointer',
                                bgcolor: statusFilter === 'SUBMITTED' ? '#f3e5f5' : 'inherit',
                                '&:hover': { bgcolor: '#f3e5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Submitted</Typography>
                            <Typography variant='h6' sx={{ color: '#7d3c98', fontWeight: 'bold' }}>{quotationCounts.submitted}</Typography>
                        </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setStatusFilter('LOST')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #ec7063`,
                                cursor: 'pointer',
                                bgcolor: statusFilter === 'LOST' ? '#ffebee' : 'inherit',
                                '&:hover': { bgcolor: '#ffebee' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Lost</Typography>
                            <Typography variant='h6' sx={{ color: '#ec7063', fontWeight: 'bold' }}>{quotationCounts.lost}</Typography>
                        </Box>
                    </Grid>
                </Grid>
                {/* Table Section */}
                <Box sx={{
                    overflowX: 'auto',
                    width: '100%'
                }}>
                    <CustomTableNew
                        showViewButton={true}
                        //  onView={(id) => handleView(id)}
                        onView={(id, source_table) => handleView(id, source_table)}
                        actionsInFirstColumn={true}
                        data={filteredQuotations}
                        // isCustomerPage={true}
                        columns={columns}
                        // onEdit={handleEdit}
                        selectedRows={selectedRows}
                        onSelectionChange={handleSelectionChange}
                        onSelectAll={handleSelectAll}
                        // showOnlyEdit={true}
                        //  deletedIds={deletedIds}
                        //  setDeletedIds={setDeletedIds}
                        sx={{
                            '& .MuiTableCell-root': {
                                padding: { xs: '8px 4px', sm: '16px' }
                            }
                        }}
                    />
                </Box>
            </Box>




        </Container>
    );
}

export default QuotationsListPage;
