/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.createTable('agent_bank_details', (table) => {
        table.increments('id').primary(); // Auto-incrementing primary key
        table.integer('agent_id').nullable(); // Agent ID
        table.string('account_holder_name').nullable(); // Account holder name
        table.integer('bank_name').unsigned().nullable() // Bank name as foreign key
            .references('id').inTable('bank_list') // Reference to bank_list table
            .onDelete('CASCADE'); // Optional: define behavior on delete
        table.string('account_number').nullable(); // Account number
        table.string('IFSC_code').nullable(); // IFSC code
        table.string('account_Type').nullable(); // Account type
        table.string('canceled_cheque').nullable(); // Canceled cheque
        table.string('branch_name').nullable(); // Branch name

        table.string('account_holder_name_2').nullable(); // Second account holder name
        table.integer('bank_name_2').unsigned().nullable() // Second bank name as foreign key
            .references('id').inTable('bank_list') // Reference to bank_list table
            .onDelete('CASCADE'); // Optional: define behavior on delete
        table.string('account_number_2').nullable(); // Second account number
        table.string('IFSC_code_2').nullable(); // Second IFSC code
        table.string('account_Type_2').nullable(); // Second account type
        table.string('canceled_cheque_2').nullable(); // Second canceled cheque
        table.string('branch_name_2').nullable(); // Second branch name

        table.string('is_active_bank').nullable(); // Indicates if this is the active bank account
        table.boolean('status').defaultTo(true); // Status
        table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
        table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
    return knex.schema.dropTableIfExists('agent_bank_details');
};
