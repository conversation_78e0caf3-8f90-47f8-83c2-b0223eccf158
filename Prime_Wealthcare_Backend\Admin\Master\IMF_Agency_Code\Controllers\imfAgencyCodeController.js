const {
  getAllAgencyCodes,
  insertAgencyCode,
  getAgencyCodeById,
  updateAgencyCodeById,
  deleteAgencyCodeById,
  reinstateAgencyCodeById,
  getAgencyCodesByBranchName
} = require('../Model/IMFAgencyCode');
const moment = require('moment'); // Make sure moment.js is installed

// Helper function to format dates and check for expiration
const formatDate = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0'); // Get day and pad with zero if needed
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Get month (0-indexed) and pad with zero
  const year = d.getFullYear(); // Get full year
  return `${day}/${month}/${year}`; // Return formatted date as dd/mm/yyyy
};

const formatAgencyCodeWithExpiration = (code) => {
  const currentDate = new Date();

  // Format dates using the new formatDate function
  const formattedLicenseValidFrom = formatDate(code.license_valid_from);
  const formattedLicenseValidTill = formatDate(code.license_valid_till);

  // Check if license_valid_till has expired and set status to 0 if expired
  const isExpired = new Date(code.license_valid_till) < currentDate;
  const status = isExpired ? 0 : code.status;

  return {
    ...code,
    license_valid_from: formattedLicenseValidFrom,
    license_valid_till: formattedLicenseValidTill,
    status
  }
}

// Get all agency codes
const getAgencyCodes = async (req, res, next) => {
  try {
    let codes = await getAllAgencyCodes();

    // Format dates and check for expiration
    codes = codes.map(formatAgencyCodeWithExpiration);

    res.json(codes);
  } catch (error) {
    next(error);
  }
};

// Get agency code by ID
const getAgencyCode = async (req, res, next) => {
  try {
    let code = await getAgencyCodeById(req.params.id);
    if (!code) {
      return res.status(404).json({ error: 'Agency code not found' });
    }

    // Format dates and check for expiration
    code = formatAgencyCodeWithExpiration(code);

    res.json(code);
  } catch (error) {
    next(error);
  }
};


// Create new agency code
const createAgencyCode = async (req, res, next) => {
  try {

    // // Validate and format dates using moment.js
    // const licenseValidFrom = moment(req.body.license_valid_from, 'DD/MM/YYYY');
    // const licenseValidTill = moment(req.body.license_valid_till, 'DD/MM/YYYY');

    // // Check if the dates are valid
    // if (!licenseValidFrom.isValid() || !licenseValidTill.isValid()) {
    //   return res.status(200).json({ error: 'Invalid date format. Use DD/MM/YYYY.' });
    // }

    // // Update the body with formatted dates
    // req.body.license_valid_from = licenseValidFrom.format('YYYY-MM-DD');
    // req.body.license_valid_till = licenseValidTill.format('YYYY-MM-DD');

    await insertAgencyCode(req.body);
    res.json({ message: 'Successfully inserted new agency code' });
  } catch (error) {
    next(error);
  }
};

// Update agency code by ID
const updateAgencyCode = async (req, res, next) => {
  try {
    // Log the input data for debugging



    // Validate and format dates using moment.js
    const licenseValidFrom = moment(req.body.license_valid_from, 'DD/MM/YYYY');
    const licenseValidTill = moment(req.body.license_valid_till, 'DD/MM/YYYY');

    // Check if the dates are valid
    if (!licenseValidFrom.isValid() || !licenseValidTill.isValid()) {
      return res.status(400).json({ error: 'Invalid date format. Use DD/MM/YYYY.' });
    }

    // Update the body with formatted dates
    req.body.license_valid_from = licenseValidFrom.format('YYYY-MM-DD');
    req.body.license_valid_till = licenseValidTill.format('YYYY-MM-DD');

    // Check if the agency code exists
    const agencyCodeExists = await getAgencyCodeById(req.params.id);
    if (!agencyCodeExists) {
      return res.status(404).json({ error: 'Agency code not found' });
    }

    const result = await updateAgencyCodeById(req.params.id, req.body);
    if (result === 0) {
      return res.status(404).json({ error: 'Agency code not found or no record updated' });
    }
    res.json({ message: 'Successfully updated agency code' });
  } catch (error) {
    console.error('Error in updateAgencyCode controller:', error); // Log the error
    next(error);
  }
};

// Soft delete agency code by ID
const deleteAgencyCode = async (req, res, next) => {
  try {
    const result = await deleteAgencyCodeById(req.params.id);
    if (result === 0) {
      return res.status(404).json({ error: 'Agency code not found or no record deleted' });
    }
    res.json({ message: 'Successfully deleted (deactivated) agency code' });
  } catch (error) {
    next(error);
  }
};

// Reinstate agency code by ID
const reinstateAgencyCode = async (req, res, next) => {
  try {
    const result = await reinstateAgencyCodeById(req.params.id);
    if (result === 0) {
      return res.status(404).json({ error: 'Agency code not found or no record reinstated' });
    }
    res.json({ message: 'Successfully reinstated agency code' });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAgencyCodes,
  getAgencyCode,
  createAgencyCode,
  updateAgencyCode,
  deleteAgencyCode,
  reinstateAgencyCode
};