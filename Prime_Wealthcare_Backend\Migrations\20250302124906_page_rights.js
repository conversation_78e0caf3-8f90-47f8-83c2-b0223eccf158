exports.up = function (knex) {
    return knex.schema.createTable('page_rights', function (table) {
        table.increments('id').primary();       // Auto-increment primary key
        table.string('user_id').notNullable();  // ID can be either employee_id or agent_id
        table.string('module_name').notNullable(); // Module name (Dashboard, Master, etc.)
        table.string('page_name').notNullable(); // Page name within the module

        // Permissions
        table.boolean('can_add').defaultTo(false);  
        table.boolean('can_edit').defaultTo(false); 
        table.boolean('can_view').defaultTo(false); 
        table.boolean('can_delete').defaultTo(false); 

        // Status and Metadata
        table.boolean('status').defaultTo(true);  // Active status
        table.string('created_by');  // Creator
        table.string('updated_by');  // Last updated by
        table.timestamps(true, true); // created_at & updated_at

        // Add a composite unique constraint
        table.unique(['user_id', 'module_name', 'page_name'], 'unique_user_module_page');
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('page_rights');
};