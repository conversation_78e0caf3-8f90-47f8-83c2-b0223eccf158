import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { createMasterProduct, updateMasterProduct, getMasterProductById, getAllProducts, fetchInsuranceCompanies, getAllMasterProducts, searchMasterProduct } from '../../../redux/actions/action';
import DropDown from '../../../components/table/DropDown';
import { trimFormData } from '../../../utils/Reusable';

function ProductMasterForm() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const products = useSelector(state => state.mainProductReducer.products);
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
    const masterProduct = useSelector(state => state.productMasterReducer.product);
    const masterProducts = useSelector(state => state.productMasterReducer.products);


    const [formData, setFormData] = useState({
        main_product_id: '',
        main_product_name: '',
        insurance_company_id: '',
        insurance_company_name: '',
        product_name: ''
    });
    const [errors, setErrors] = useState({
        main_product_name: false,
        insurance_company_name: false,
        product_name: false
    });

    useEffect(() => {
        dispatch(getAllProducts());
        dispatch(fetchInsuranceCompanies());
        if (id) {
            dispatch(getMasterProductById(id));
        } else {
            setFormData({
                main_product_id: '',
                main_product_name: '',
                insurance_company_name: '',
                insurance_company_id: '',
                product_name: ''
            });
        }
    }, [id, dispatch]);

    useEffect(() => {
        if (masterProduct && id) {
            const selectedProduct = products.find(product => product.id === masterProduct.main_product_id);
            const selectedCompany = insuranceCompanies.find(company => company.id === masterProduct.insurance_company_id);

            setFormData({
                main_product_id: masterProduct.main_product_id || '',
                main_product_name: selectedProduct ? selectedProduct.main_product : '',
                insurance_company_id: masterProduct.insurance_company_id || '',
                insurance_company_name: selectedCompany ? selectedCompany.insurance_company_name : '',
                product_name: masterProduct.product_name || '',
            });
        }
    }, [masterProduct, id, products, insuranceCompanies]);

    const validate = () => {
        let tempErrors = {};
        if (!formData.product_name) {
            tempErrors.product_name = 'Product name is required';
        } else {
            const isDuplicate = masterProducts.find(product => product.product_name === formData.product_name && product.id !== Number(id))

            if (isDuplicate) {
                tempErrors.product_name = 'Product name already exists';
            }
        }
        if (!formData.insurance_company_name) {
            tempErrors.insurance_company_name = 'Insurance company is required';
        }
        if (!formData.main_product_name) {
            tempErrors.main_product_name = 'Main product is required';
        }
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    }

    const handleChange = (e) => {
        const { name, value } = e.target;
        if (value === ' ') {
            setErrors({
                ...errors,
                [name]: 'Do not start with a whitespace character'
            })
            return;
        }
        const data = value.toUpperCase().replace(/\s{2,}$/, ' ')
        setFormData({
            ...formData,
            [name]: data,
        });
        setErrors({
            ...errors,
            [name]: false
        })
        if (name === 'product_name' && value !== '') {
            dispatch(searchMasterProduct(value));
        }
    };

    const handleProductChange = (e) => {
        const selectedProductId = e.target.value;
        const selectedProduct = products.find(product => product.id === selectedProductId);
        setFormData({
            ...formData,
            main_product_id: selectedProductId,
            main_product_name: selectedProduct ? selectedProduct.main_product : ''
        });
        setErrors({
            ...errors,
            main_product_name: false
        })
    };

    const handleInsuranceCompanyChange = (e) => {
        const selectedCompanyId = e.target.value;
        const selectedCompany = insuranceCompanies.find(company => company.id === selectedCompanyId);
        setFormData({
            ...formData,
            insurance_company_id: selectedCompanyId,
            insurance_company_name: selectedCompany ? selectedCompany.insurance_company_name : ''
        });
        setErrors({
            ...errors,
            insurance_company_name: false
        })
    };

    const handleProductCreationAndUpdate = () => {
        const isValid = validate();
        if (!isValid) return;

        const { main_product_id, insurance_company_id, product_name } = formData;
        const data = { main_product_id, insurance_company_id, product_name };
        const productData = trimFormData(data);
        if (id) {
            dispatch(updateMasterProduct({ id, productData }));
        } else {
            dispatch(createMasterProduct(productData));
        }
        setTimeout(() => {
            dispatch(getAllMasterProducts());
        }, 500);
    }

    const handleSaveAndNew = () => {
        handleProductCreationAndUpdate();
        if (validate()) {
            setFormData({
                main_product_id: '',
                main_product_name: '',
                insurance_company_id: '',
                insurance_company_name: '',
                product_name: ''
            });
        }
    }

    const handleSave = () => {
        handleProductCreationAndUpdate();
        if (validate()) {
            navigate('/dashboard/product-master');
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/product-master');
    };

    return (
        <form>
            <Grid container spacing={2}>
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                    <Box sx={{ width: '100%', margin: '0 20px' }}>
                        <ModuleName moduleName="Master Product" pageName={id ? masterProduct?.status === 0 ? "View" : "Edit" : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!id && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleSaveAndNew}
                        >
                            Save & New
                        </Button>
                    )}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                        onClick={handleSave}
                    >
                        Save
                    </Button>
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>

                {/* Product Information */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
                        <h2>Master Product Information</h2>
                    </Box>
                </Grid>

                <Box sx={{ display: 'flex', justifyContent: 'space-evenly', width: '100%', flexWrap: 'wrap' }}>
                    {/* Insurance Company Dropdown */}
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <DropDown
                            label={'Insurance Company'}
                            name="insurance_company_id"
                            options={[
                                { label: 'None', value: '' },
                                ...insuranceCompanies.filter(company => company.status === 1).map(company => ({ label: company.insurance_company_name, value: company.id })),
                            ]}
                            value={formData.insurance_company_id}
                            onChange={handleInsuranceCompanyChange}
                            fullWidth
                            disabled={id && masterProduct.status === 0}
                            helperText={errors.insurance_company_name || ''}
                            required
                        />
                    </Grid>
                    
                    {/* Main Product Dropdown */}
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <DropDown
                            label={'Main Product'}
                            name="main_product_id"
                            options={[
                                { label: 'None', value: '' },
                                ...products.filter(product => product.status === 1).map(product => ({ label: product.main_product, value: product.id })),
                            ]}
                            value={formData.main_product_id}
                            onChange={handleProductChange}
                            fullWidth
                            disabled={id && masterProduct?.status === 0}
                            helperText={errors.main_product_name || ''}
                            required
                        />
                    </Grid>

                    {/* Product Name Custom TextField */}
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            name="product_name"
                            label="Product Name"
                            value={formData.product_name}
                            onChange={handleChange}
                            fullWidth
                            disabled={id && masterProduct.status === 0}
                            helperText={errors.product_name || ''}
                            isRequired
                        />
                    </Grid>
                </Box>
            </Grid>
        </form>
    );
}

export default ProductMasterForm;