import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Avatar } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/agentDeletePopup';
import SuccessPopup from '../../../components/agentSuccessPopup';
import {
    getAllAgentDetails,
    deleteAgentDetails,
    reinstateAgentDetails,
    getAgentsBySearch,
    getAgentsByCriteria
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions'
import AvatarImage from '../../../components/AvatarImage'; // Add this import

function AgentMasterPage() {
    const [selectedOption, setSelectedOption] = useState('none');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const agents = useSelector(state => state.agentReducer.agents);
    const [sortedAgents, setSortedAgents] = useState(agents || []);

    const permissions = usePermissions('Agent', 'Agent Master')

    useEffect(() => {
        dispatch(getAllAgentDetails());
    }, [dispatch])
    useEffect(() => {
        const fetchAgents = () => {
            dispatch(getAgentsByCriteria(selectedOption));
        };
        fetchAgents();
    }, [selectedOption, dispatch]);
    useEffect(() => {
        const filterAgentsByStatus = () => {
            if (statusFilter === 'all') {
                setSortedAgents(agents);
            } else if (statusFilter === 'none') {
                dispatch(getAllAgentDetails());
            } else {
                setSortedAgents(agents.filter(agent => agent.status === (statusFilter === 'active' ? 1 : 0)));
            }
        };
        filterAgentsByStatus();
    }, [statusFilter, agents]);

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleConfirmDelete = () => {
        dispatch(deleteAgentDetails(selectedItem.id))
            .then(() => {
                dispatch(getAllAgentDetails());
                setOpenDeletePopup(false);
                // Store the agent ID before clearing selectedItem
                const deletedAgentId = selectedItem.id;
                setSelectedItem({ ...selectedItem, id: deletedAgentId });
                setOpenSuccessPopup(true);
            })
            .catch(error => {
                setOpenDeletePopup(false);
            });
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/agent-personal-information');
    };

    const handleDelete = (id) => {
        handleOpenDeletePopup(agents.find(agent => agent.id === id));
    };

    const handleViewData = (id) => {
        navigate(`/dashboard/agent-master-overview/${id}`);
    };

    const handleReinstate = (id) => {
        dispatch(reinstateAgentDetails(id))
            .then(() => {
                dispatch(getAllAgentDetails());
            })
            .catch(error => {
                console.error('Failed to reinstate agent:', error);
            });
    };

    const handleEdit = (id) => {
        navigate(`/dashboard/agent-personal-information/${id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedAgents.map(agent => agent.id) : []);
    };

    const onSearch = (query) => {
        if (query === '') {
            dispatch(getAllAgentDetails());
        } else {
            dispatch(getAgentsBySearch(query));
        }
    };

    const handleAllClick = () => setStatusFilter('all');
    const handleActiveClick = () => setStatusFilter('active');
    const handleInactiveClick = () => setStatusFilter('inactive');
    const handleRefreshClick = () => setSelectedOption('none');

    const columns = [
        {
            field: 'photo',
            headerName: 'Image',
            renderCell: (params) => {
                // Get initials (first two words' first letters)
                const initials = params?.row?.full_name
                    ? params.row.full_name.split(' ').slice(0, 2).map(n => n[0]?.toUpperCase()).join('')
                    : '';
                return (
                    <AvatarImage
                        src={params?.row?.photo} // Always null to never show the image
                        alt={params.row.full_name}
                        size={50}
                    >
                        {initials}
                    </AvatarImage>
                );
            }
        },
        { field: 'full_name', headerName: 'Agent Name' },
        { field: 'agent_id', headerName: 'Agent Code' },
        { field: 'role_name', headerName: 'Role' },
        { field: 'first_reporting_manager_id', headerName: 'Leader Name' },
        { field: 'personal_mobile', headerName: 'Mobile' },
        { field: 'branch_name', headerName: 'Branch Name' },
        { field: 'branch_city', headerName: 'City' },
    ];

    const dataMapping = {
        'Agent Name': 'full_name',
        'Agent Code': 'agent_id',
        'Role': 'role_name',
        'Leader Name': 'first_reporting_manager_id',
        'Mobile': 'personal_mobile',
        'Branch Name': 'branch_name',
        'City': 'branch_city',
        'Status': 'status'
    };

    return (
        <Container maxWidth="xl" className="list-container">
            <Box className="flex-column-box">
                <Box className="flex-row-box">
                    <Box className="flex-row-box">
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Agent" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" className="button-group">
                        {permissions.can_add && (
                            <Button onClick={handleAdd} className="button-new">
                                New
                            </Button>
                        )}
                        <ExportToPDF
                            data={sortedAgents.map(agent => ({
                                ...agent,
                                status: agent.status === 1 ? 'Active' : 'Inactive'
                            }))}
                            headNames={['Agent Name', 'Agent Code', 'Role', 'Leader Name', 'Mobile', 'Branch Name', 'City', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="agents.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Agent Report"
                        />
                    </ButtonGroup>
                </Box>
                <Box className="dropdown-search">
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                    />
                    <Box className="flex-row-box">
                        <SearchBar placeholder="Search..." onSearch={onSearch} />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>
                <Box className="custom-table">
                    <CustomTable
                        data={sortedAgents}
                        columns={columns}
                        onDelete={permissions.can_delete ? handleDelete : null}
                        onEdit={permissions.can_edit ? handleEdit : null}
                        onReinstate={handleReinstate}
                        onSelectionChange={handleSelectionChange}
                        selectedRows={selectedRows}
                        onSelectAll={handleSelectAll}
                        handleViewData={handleViewData}
                    />
                </Box>
            </Box>
            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.full_name : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.full_name : ''}
                deletedAgentId={selectedItem ? selectedItem.id : null}
            />
        </Container>
    )
}

export default AgentMasterPage