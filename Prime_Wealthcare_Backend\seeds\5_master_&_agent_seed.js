exports.seed = async function (knex) {
    // Clear existing data before inserting new records
    await knex('areas').del();
    // Then insert the areas data
    await knex('areas').insert([
        {
            id: 1,
            pincode: '110085',
            city: 'NORTH DELHI',
            area: 'ROHINI',
            pincode_city: '110085NORTHDELHI',
            status: 1
        },
        {
            id: 2,
            pincode: '110085',
            city: 'NORTH DELHI',
            area: 'BEGAMPUR',
            pincode_city: '110085NORTHDELHI',
            status: 1
        },
        {
            id: 3,
            pincode: '110085',
            city: 'NORTH DELHI',
            area: 'RITHALA',
            pincode_city: '110085NORTHDELHI',
            status: 1
        },
        {
            id: 4,
            pincode: '380058',
            city: 'AHMEDABAD',
            area: 'SOUTH BOPAL',
            pincode_city: '380058AHMEDABAD',
            status: 1
        },
        {
            id: 5,
            pincode: '400083',
            city: 'MUMBAI',
            area: 'VIKHROLI-WEST',
            pincode_city: '400083MUMBAI',
            status: 1
        },
        {
            id: 6,
            pincode: '110019',
            city: 'SOUTH DELHI',
            area: 'NEW DELHI',
            pincode_city: '110019SOUTHDELHI',
            status: 1
        },
        {
            id: 7,
            pincode: '122009',
            city: 'GURGAON',
            area: 'GURUGRAM',
            pincode_city: '122009GURGAON',
            status: 1
        },
        {
            id: 8,
            pincode: '380009',
            city: 'AHMEDABAD',
            area: 'NAVRANGPURA',
            pincode_city: '380009AHMEDABAD',
            status: 1
        },
        {
            id: 9,
            pincode: '400020',
            city: 'MUMBAI',
            area: 'CHURCHGATE',
            pincode_city: '400020MUMBAI',
            status: 1
        },
        {
            id: 10,
            pincode: '122001',
            city: 'GURGAON',
            area: 'SECTOR 29',
            pincode_city: '122001GURGAON',
            status: 1
        },
        {
            id: 11,
            pincode: '382443',
            city: 'AHMEDABAD',
            area: 'ISHANPUR',
            pincode_city: '382443AHMEDABAD',
            status: 1
        }
    ]);

    // Clear existing data before inserting new records
    await knex('insurance_company').del();
    // Then insert the insurance company data
    await knex('insurance_company').insert([
        {
            id: 1,
            insurance_company_name: 'FUTURE GENRALI INDIA INSURANCE CO. LTD.',
            short_name: 'FUTURE GENRALI',
            ado_code: 'PUP9001',
            help_line_no: '9106734041',
            email_id: '<EMAIL>',
            zone_head_name: 'DINESH LALWANI',
            zone_head_number: '9879076780',
            company_logo: null,
            insurance_type: 'GENERAL INSURANCE',
            address_line1: '801 & 802, 8TH FLOOR, TOWER C',
            address_line2: 'EMBASSY 24X7 PARK, L.B.S. MARG, VIKHROLI-WEST, ',
            pincode: '400083',
            city: 'MUMBAI',
            state: 'MAHARASHTRA',
            pincode_city: null,
            area: '5',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 07:05:19',
            updated_at: '2024-12-24 07:05:19',
            status: '1'
        },
        {
            id: 2,
            insurance_company_name: 'CARE HEALTH INSURANCE CO. LTD',
            short_name: 'CARE',
            ado_code: '550076',
            help_line_no: '**********',
            email_id: '<EMAIL>',
            zone_head_name: 'SAMIR SHAH',
            zone_head_number: '**********',
            company_logo: null,
            insurance_type: 'GENERAL INSURANCE',
            address_line1: '5th Floor, 19, Chawla House, ',
            address_line2: 'Nehru Place',
            pincode: '110019',
            city: 'SOUTH DELHI',
            state: 'DELHI',
            pincode_city: null,
            area: '6',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 08:14:41',
            updated_at: '2024-12-26 08:14:41',
            status: '1'
        },
        {
            id: 3,
            insurance_company_name: 'NIVA BUPA HEALTH INSURANCE COMPANY LIMITED',
            short_name: 'NIVA BUPA',
            ado_code: 'MSP1009',
            help_line_no: '**********',
            email_id: '<EMAIL>',
            zone_head_name: 'BHAVESH MADHAK',
            zone_head_number: '**********',
            company_logo: null,
            insurance_type: 'GENERAL INSURANCE',
            address_line1: '7TH FLOOR, 701, SPG EMPRESSA',
            address_line2: 'BESIDE PASSPORT SEVA KENDRA, NR. MITHAKHALI SIX ROAD',
            pincode: '380009',
            city: 'AHMEDABAD',
            state: 'GUJARAT',
            pincode_city: null,
            area: '8',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 05:34:40',
            updated_at: '2024-12-27 05:34:40',
            status: '1'
        },
        {
            id: 4,
            insurance_company_name: 'HDFC ERGO GENERAL INSURANCE COMPANY LIMITED',
            short_name: 'HDFC ERGO',
            ado_code: 'DPP5005',
            help_line_no: '9106435008',
            email_id: '<EMAIL>',
            zone_head_name: 'VIKRAM JAIN',
            zone_head_number: '7768999049',
            company_logo: null,
            insurance_type: 'GENERAL INSURANCE',
            address_line1: '1st Floor, HDFC House,',
            address_line2: '165-166 Backbay Reclamation,  H. T. Parekh Marg',
            pincode: '400020',
            city: 'MUMBAI',
            state: 'MAHARASHTRA',
            pincode_city: null,
            area: '9',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 06:10:00',
            updated_at: '2024-12-27 06:10:00',
            status: '1'
        },
        {
            id: 5,
            insurance_company_name: 'IFFCO TOKIO GENERAL INSURANCE CO. LTD',
            short_name: 'IFFCO TOKIO',
            ado_code: '64000179',
            help_line_no: '9106586962',
            email_id: '<EMAIL>',
            zone_head_name: 'KALPESH SAMNANI',
            zone_head_number: '9033260275',
            company_logo: null,
            insurance_type: 'GENERAL INSURANCE',
            address_line1: 'IFFCO Tower, Plot No. 3',
            address_line2: 'Sector 29,',
            pincode: '122001',
            city: 'GURGAON',
            state: 'HARYANA',
            pincode_city: null,
            area: '10',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:19:18',
            updated_at: '2024-12-27 07:19:18',
            status: '1'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('imf_branches').del();
    // Then insert the IMF branches data
    await knex('imf_branches').insert([
        {
            id: 1,
            branch_name: 'AHMEDABAD',
            branch_code: 'ADO123456',
            email: '<EMAIL>',
            help_line_number: '7227805005',
            branch_manager_name: 'MITSU PANDYA',
            branch_manager_number: '9123558486',
            address_line1: '510 Shivalik Satyamev Opp Vakilsaheb Bridge',
            address_line2: 'Ambali Bopal',
            pincode: '380058',
            city: 'AHMEDABAD',
            pincode_city: null,
            state: 'GUJARAT',
            area: '4',
            status: '1',
            created_at: '2024-12-24 01:55:24',
            updated_at: '2024-12-24 01:55:24'
        },
        {
            id: 2,
            branch_name: 'BHAVNAGAR',
            branch_code: 'BVN',
            email: '<EMAIL>',
            help_line_number: '9104095005',
            branch_manager_name: 'SANKET BHAI',
            branch_manager_number: '9104095005',
            address_line1: '403-404, Bhayani Skyline',
            address_line2: 'Nr. Prayosha Fast Food, Attabhai Chowk',
            pincode: '364002',
            city: 'BHAVNAGAR',
            pincode_city: null,
            state: 'GUJARAT',
            area: null,
            status: '1',
            created_at: '2025-01-07 11:32:46',
            updated_at: '2025-01-07 11:32:46'
        },
        {
            id: 3,
            branch_name: 'SURAT OFFICE',
            branch_code: 'SUT',
            email: '<EMAIL>',
            help_line_number: '7405425005',
            branch_manager_name: 'RIDDHIBEN KISHANKUMAR MANGUKIYA',
            branch_manager_number: '7405425005',
            address_line1: '262, Silverstone Arcade',
            address_line2: 'Causeway Rd, Near D-mart, Katargam',
            pincode: '395004',
            city: 'SURAT',
            pincode_city: null,
            state: 'GUJARAT',
            area: null,
            status: '1',
            created_at: '2025-01-07 11:39:25',
            updated_at: '2025-01-07 11:39:25'
        },
        {
            id: 5,
            branch_name: 'VADODARA OFFICE',
            branch_code: 'VAD',
            email: '<EMAIL>',
            help_line_number: '9909507640',
            branch_manager_name: 'SOBHANA BEN SOLANKI',
            branch_manager_number: '9725655005',
            address_line1: '17, Aarya Elite-1,',
            address_line2: 'Nr. Deep Party Plot, Atladara',
            pincode: '390012',
            city: 'VADODARA',
            pincode_city: null,
            state: 'GUJARAT',
            area: null,
            status: '1',
            created_at: '2025-01-07 11:41:45',
            updated_at: '2025-01-07 11:41:45'
        },
        {
            id: 6,
            branch_name: 'RAJKOT OFFICE',
            branch_code: 'RTJ',
            email: '<EMAIL>',
            help_line_number: '8487989805',
            branch_manager_name: 'MANAGE BY BHAVNAGAR OFFICE',
            branch_manager_number: '9104095005',
            address_line1: '208, Nath Edifice,',
            address_line2: 'Dr. Yagnik Road, Opp. Police Commissioner Office,rajkot',
            pincode: '360001',
            city: 'RAJKOT',
            pincode_city: null,
            state: 'GUJARAT',
            area: null,
            status: '1',
            created_at: '2025-01-07 11:44:02',
            updated_at: '2025-01-07 11:44:02'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('main_product').del();
    // Then insert the main product data
    await knex('main_product').insert([
        {
            id: 1,
            main_product: 'HEALTH',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-23 15:36:01',
            updated_at: '2024-12-24 11:07:33',
            status: '1'
        },
        {
            id: 2,
            main_product: 'SMALL & MEDIUM ENTERPRISE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:17:18',
            updated_at: '2024-12-24 12:17:18',
            status: '1'
        },
        {
            id: 3,
            main_product: 'INDIVIDUAL PERSONAL ACCIDENT [IPA]',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:17:28',
            updated_at: '2024-12-24 12:17:28',
            status: '1'
        },
        {
            id: 4,
            main_product: 'TRAVEL & OVERSEAS',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:17:46',
            updated_at: '2024-12-24 12:17:46',
            status: '1'
        },
        {
            id: 5,
            main_product: 'WORKMEN COMPONSATION',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:17:58',
            updated_at: '2024-12-24 12:17:58',
            status: '1'
        },
        {
            id: 6,
            main_product: 'MOTOR',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:18:08',
            updated_at: '2024-12-24 12:18:08',
            status: '1'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('product_master').del();
    // Then insert the product master data
    await knex('product_master').insert([
        {
            id: 1,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FG HEALTH ABSOLUTE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-23 15:36:01',
            updated_at: '2024-12-27 05:32:25',
            status: '1'
        },
        {
            id: 2,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FG ADVANTAGE TOP UP',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-23 15:36:01',
            updated_at: '2024-12-24 12:20:06',
            status: '1'
        },
        {
            id: 3,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FG VARISHTHA BIMA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-23 15:36:01',
            updated_at: '2024-12-24 12:19:45',
            status: '1'
        },
        {
            id: 4,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FG HEALTH TOTAL',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 10:44:52',
            updated_at: '2024-12-24 10:44:52',
            status: '1'
        },
        {
            id: 5,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FG HEALTH SURAKSHA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 11:25:09',
            updated_at: '2024-12-24 11:25:09',
            status: '1'
        },
        {
            id: 6,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FG D.I.Y HEALTH',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:20:40',
            updated_at: '2024-12-24 12:20:40',
            status: '1'
        },
        {
            id: 7,
            main_product_id: '3',
            insurance_company_id: '1',
            product_name: 'FG ACCIDENT SURAKSHA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 12:21:06',
            updated_at: '2024-12-24 12:21:06',
            status: '1'
        },
        {
            id: 8,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'HOSPI-CASH',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 08:17:37',
            updated_at: '2024-12-26 08:17:37',
            status: '1'
        },
        {
            id: 9,
            main_product_id: '1',
            insurance_company_id: '1',
            product_name: 'FUTURE VECTOR CARE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 08:17:58',
            updated_at: '2024-12-26 08:17:58',
            status: '1'
        },
        {
            id: 10,
            main_product_id: '4',
            insurance_company_id: '1',
            product_name: 'TRAVEL SURKSHA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 08:18:27',
            updated_at: '2024-12-26 08:18:27',
            status: '1'
        },
        {
            id: 11,
            main_product_id: '1',
            insurance_company_id: '2',
            product_name: 'CARE SUPREME',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:47:17',
            updated_at: '2024-12-26 09:47:17',
            status: '1'
        },
        {
            id: 12,
            main_product_id: '1',
            insurance_company_id: '2',
            product_name: 'CARE ADVANTAGE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:47:36',
            updated_at: '2024-12-26 09:47:36',
            status: '1'
        },
        {
            id: 13,
            main_product_id: '1',
            insurance_company_id: '2',
            product_name: 'CARE FREEDOM',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:49:40',
            updated_at: '2024-12-26 09:49:40',
            status: '1'
        },
        {
            id: 14,
            main_product_id: '1',
            insurance_company_id: '2',
            product_name: 'CARE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:50:05',
            updated_at: '2024-12-26 09:50:05',
            status: '1'
        },
        {
            id: 15,
            main_product_id: '1',
            insurance_company_id: '2',
            product_name: 'CARE PLUS YOUTH PLAN',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:52:07',
            updated_at: '2024-12-26 09:52:07',
            status: '1'
        },
        {
            id: 16,
            main_product_id: '1',
            insurance_company_id: '2',
            product_name: 'CARE PLUS COMPLETE PLAN',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:54:21',
            updated_at: '2024-12-26 09:54:21',
            status: '1'
        },
        {
            id: 17,
            main_product_id: '4',
            insurance_company_id: '2',
            product_name: 'NEW EXPLORE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:54:43',
            updated_at: '2024-12-26 09:54:43',
            status: '1'
        },
        {
            id: 18,
            main_product_id: '4',
            insurance_company_id: '2',
            product_name: 'TRAVEL',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 09:55:54',
            updated_at: '2024-12-26 09:55:54',
            status: '1'
        },
        {
            id: 19,
            main_product_id: '1',
            insurance_company_id: '4',
            product_name: 'OPTIMA RESTORE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:47:31',
            updated_at: '2024-12-27 07:47:31',
            status: '1'
        },
        {
            id: 20,
            main_product_id: '1',
            insurance_company_id: '4',
            product_name: 'OPTIMA SECURE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:47:48',
            updated_at: '2024-12-27 07:47:48',
            status: '1'
        },
        {
            id: 21,
            main_product_id: '1',
            insurance_company_id: '4',
            product_name: 'MY HEALTH SURAKSHA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:49:22',
            updated_at: '2024-12-27 07:49:22',
            status: '1'
        },
        {
            id: 22,
            main_product_id: '1',
            insurance_company_id: '4',
            product_name: 'ENERGY',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:49:42',
            updated_at: '2024-12-27 07:49:42',
            status: '1'
        },
        {
            id: 23,
            main_product_id: '1',
            insurance_company_id: '4',
            product_name: 'MEDISURE SUPER TOPUP',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:50:02',
            updated_at: '2024-12-27 07:50:02',
            status: '1'
        },
        {
            id: 24,
            main_product_id: '3',
            insurance_company_id: '4',
            product_name: 'MY HEALTH KOTY SURAKSHA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:50:32',
            updated_at: '2024-12-27 07:50:32',
            status: '1'
        },
        {
            id: 25,
            main_product_id: '1',
            insurance_company_id: '4',
            product_name: 'OPTIMA SENIOR',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:50:48',
            updated_at: '2024-12-27 07:50:48',
            status: '1'
        },
        {
            id: 26,
            main_product_id: '4',
            insurance_company_id: '4',
            product_name: 'TRAVEL INSURANCE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:51:18',
            updated_at: '2024-12-27 07:51:18',
            status: '1'
        },
        {
            id: 27,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'REASSURE 2.0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:57:45',
            updated_at: '2024-12-27 07:57:45',
            status: '1'
        },
        {
            id: 28,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HC V5 HEALTH INSURANCE PLAN',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:58:08',
            updated_at: '2024-12-27 07:58:08',
            status: '1'
        },
        {
            id: 29,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'REASSURE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:58:28',
            updated_at: '2024-12-27 07:58:28',
            status: '1'
        },
        {
            id: 30,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HEALTH PREMIA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:58:48',
            updated_at: '2024-12-27 07:58:48',
            status: '1'
        },
        {
            id: 31,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HEALTH RECHARGE V2',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 07:59:08',
            updated_at: '2024-12-27 07:59:08',
            status: '1'
        },
        {
            id: 32,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HEALTH PAULSE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:00:15',
            updated_at: '2024-12-27 08:00:15',
            status: '1'
        },
        {
            id: 33,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HART BEAT V5',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:00:39',
            updated_at: '2024-12-27 08:00:39',
            status: '1'
        },
        {
            id: 34,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'SENIOR FIRST',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:01:01',
            updated_at: '2024-12-27 08:01:01',
            status: '1'
        },
        {
            id: 35,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'AROGYA SANJEEVANI',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:01:27',
            updated_at: '2024-12-27 08:01:27',
            status: '1'
        },
        {
            id: 36,
            main_product_id: '3',
            insurance_company_id: '3',
            product_name: 'PERSONAL ACCIDENT',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:01:52',
            updated_at: '2024-12-27 08:01:52',
            status: '1'
        },
        {
            id: 37,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HEALTH COMPANION',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:02:46',
            updated_at: '2024-12-27 08:02:46',
            status: '1'
        },
        {
            id: 38,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'HEALTH REACHARGE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:03:04',
            updated_at: '2024-12-27 08:03:04',
            status: '1'
        },
        {
            id: 39,
            main_product_id: '1',
            insurance_company_id: '3',
            product_name: 'ASPIRE',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:03:31',
            updated_at: '2024-12-27 08:03:31',
            status: '1'
        },
        {
            id: 40,
            main_product_id: '1',
            insurance_company_id: '5',
            product_name: 'HEALTH PROTECTOR',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:12:55',
            updated_at: '2024-12-27 08:12:55',
            status: '1'
        },
        {
            id: 41,
            main_product_id: '3',
            insurance_company_id: '5',
            product_name: 'IFFCO IPA',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 08:13:43',
            updated_at: '2024-12-27 08:13:43',
            status: '1'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('role_management').del();
    // Then insert the role management data
    await knex('role_management').insert([
        {
            id: 1,
            role_name: 'IT EXECUTIVE',
            department_name: 'IT',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-24 09:49:55',
            updated_at: '2024-12-24 09:49:55',
            status: '1'
        },
        {
            id: 2,
            role_name: 'RISK-MANAGER',
            department_name: 'MARKETING',
            created_by: '1',
            updated_by: '1',
            created_at: '2025-01-07 05:11:09',
            updated_at: '2025-01-07 05:11:09',
            status: '1'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('sub_product').del();
    // Then insert the sub product data
    await knex('sub_product').insert([
        {
            id: 4,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '1',
            sub_product_name: 'CLASSIC',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '60',
            post_hospitalization_days: '90',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:08:27',
            updated_at: '2024-12-26 05:12:31',
            status: '1'
        },
        {
            id: 5,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '1',
            sub_product_name: 'PLATINUM',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:20:01',
            updated_at: '2024-12-26 05:24:10',
            status: '0'
        },
        {
            id: 6,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '1',
            sub_product_name: 'SIGNATURE',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:26:03',
            updated_at: '2024-12-26 05:27:58',
            status: '0'
        },
        {
            id: 7,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '4',
            sub_product_name: 'VITAL',
            co_pay: '5',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:28:50',
            updated_at: '2024-12-26 05:32:23',
            status: '1'
        },
        {
            id: 8,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '4',
            sub_product_name: 'SUPERIOR',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:43:37',
            updated_at: '2024-12-26 05:45:12',
            status: '0'
        },
        {
            id: 9,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '4',
            sub_product_name: 'PREMIERE',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:46:09',
            updated_at: '2024-12-26 05:46:40',
            status: '0'
        },
        {
            id: 10,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '2',
            sub_product_name: 'SUPREME',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 05:48:11',
            updated_at: '2024-12-26 06:32:53',
            status: '1'
        },
        {
            id: 11,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '2',
            sub_product_name: 'ELITE',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 06:42:17',
            updated_at: '2024-12-26 06:46:58',
            status: '1'
        },
        {
            id: 12,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '3',
            sub_product_name: 'VARISHTA BIMA',
            co_pay: '20',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 06:49:11',
            updated_at: '2024-12-26 06:52:13',
            status: '1'
        },
        {
            id: 13,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '5',
            sub_product_name: 'GOLD',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 07:09:45',
            updated_at: '2024-12-26 07:38:23',
            status: '1'
        },
        {
            id: 14,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '5',
            sub_product_name: 'PLATINUM',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 07:39:29',
            updated_at: '2024-12-26 07:41:16',
            status: '1'
        },
        {
            id: 15,
            main_product_id: '3',
            insurance_company_id: '1',
            product_master_id: '7',
            sub_product_name: 'ACCIDENT SURAKSHA',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 07:43:41',
            updated_at: '2024-12-26 07:45:25',
            status: '1'
        },
        {
            id: 17,
            main_product_id: '1',
            insurance_company_id: '1',
            product_master_id: '9',
            sub_product_name: 'VECTOR CARE',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 08:24:05',
            updated_at: '2024-12-26 08:25:04',
            status: '1'
        },
        {
            id: 18,
            main_product_id: '1',
            insurance_company_id: '2',
            product_master_id: '11',
            sub_product_name: 'CARE SUPREME',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 10:15:48',
            updated_at: '2024-12-26 10:26:18',
            status: '1'
        },
        {
            id: 19,
            main_product_id: '1',
            insurance_company_id: '2',
            product_master_id: '13',
            sub_product_name: 'CARE FREEDOM',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 10:33:42',
            updated_at: '2024-12-26 10:40:37',
            status: '1'
        },
        {
            id: 20,
            main_product_id: '1',
            insurance_company_id: '2',
            product_master_id: '12',
            sub_product_name: 'CARE ADVANTAGE',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 10:45:51',
            updated_at: '2024-12-26 10:54:59',
            status: '1'
        },
        {
            id: 21,
            main_product_id: '1',
            insurance_company_id: '2',
            product_master_id: '15',
            sub_product_name: 'CARE PLUS YOUTH',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 10:59:32',
            updated_at: '2024-12-26 11:01:51',
            status: '1'
        },
        {
            id: 22,
            main_product_id: '1',
            insurance_company_id: '2',
            product_master_id: '16',
            sub_product_name: 'CARE PLUS COMPLETE',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 11:24:14',
            updated_at: '2024-12-26 11:51:16',
            status: '1'
        },
        {
            id: 23,
            main_product_id: '1',
            insurance_company_id: '2',
            product_master_id: '14',
            sub_product_name: 'CARE',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-26 12:18:07',
            updated_at: '2024-12-26 12:24:10',
            status: '1'
        },
        {
            id: 24,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '22',
            sub_product_name: 'GOLD',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 11:51:18',
            updated_at: '2024-12-27 11:57:15',
            status: '1'
        },
        {
            id: 25,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '22',
            sub_product_name: 'SILVER',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-27 12:00:37',
            updated_at: '2024-12-27 12:23:19',
            status: '1'
        },
        {
            id: 26,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '21',
            sub_product_name: 'SILVER SMART',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:21:30',
            updated_at: '2024-12-28 07:22:01',
            status: '1'
        },
        {
            id: 27,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '21',
            sub_product_name: 'GOLD SMART',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:23:07',
            updated_at: '2024-12-28 07:25:04',
            status: '1'
        },
        {
            id: 28,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '21',
            sub_product_name: 'PLATINUM SMART',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:25:58',
            updated_at: '2024-12-28 07:28:09',
            status: '1'
        },
        {
            id: 29,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '19',
            sub_product_name: 'OPTIMA RESTORE',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:30:24',
            updated_at: '2024-12-28 07:32:45',
            status: '1'
        },
        {
            id: 30,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '20',
            sub_product_name: 'OPTIMA SECURE',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:34:37',
            updated_at: '2024-12-28 07:42:27',
            status: '1'
        },
        {
            id: 31,
            main_product_id: '1',
            insurance_company_id: '4',
            product_master_id: '24',
            sub_product_name: 'MY HEALTH KOTY SURAKSHA',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:43:41',
            updated_at: '2024-12-28 07:45:47',
            status: '1'
        },
        {
            id: 32,
            main_product_id: '4',
            insurance_company_id: '4',
            product_master_id: '26',
            sub_product_name: 'SILVER',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 07:51:03',
            updated_at: '2024-12-28 08:02:10',
            status: '1'
        },
        {
            id: 33,
            main_product_id: '1',
            insurance_company_id: '3',
            product_master_id: '27',
            sub_product_name: 'PLATINUM',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 08:04:54',
            updated_at: '2024-12-28 08:08:40',
            status: '1'
        },
        {
            id: 34,
            main_product_id: '1',
            insurance_company_id: '3',
            product_master_id: '27',
            sub_product_name: 'TITANIUM',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 08:19:05',
            updated_at: '2024-12-28 08:21:59',
            status: '1'
        },
        {
            id: 35,
            main_product_id: '1',
            insurance_company_id: '3',
            product_master_id: '29',
            sub_product_name: 'REASSURE',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 08:23:34',
            updated_at: '2024-12-28 08:29:15',
            status: '1'
        },
        {
            id: 36,
            main_product_id: '1',
            insurance_company_id: '3',
            product_master_id: '28',
            sub_product_name: 'HC V5 HEALTH INSURANCE PLAN',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 10:24:25',
            updated_at: '2024-12-28 10:28:14',
            status: '1'
        },
        {
            id: 37,
            main_product_id: '3',
            insurance_company_id: '3',
            product_master_id: '36',
            sub_product_name: 'VARIANT 1',
            co_pay: '0',
            child_separation_age: '26',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 10:32:05',
            updated_at: '2024-12-28 11:07:52',
            status: '1'
        },
        {
            id: 38,
            main_product_id: '3',
            insurance_company_id: '3',
            product_master_id: '36',
            sub_product_name: 'VARIANT 2',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:09:25',
            updated_at: '2024-12-28 11:13:02',
            status: '1'
        },
        {
            id: 39,
            main_product_id: '3',
            insurance_company_id: '3',
            product_master_id: '36',
            sub_product_name: 'VARIANT 3',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:23:45',
            updated_at: '2024-12-28 11:27:20',
            status: '1'
        },
        {
            id: 40,
            main_product_id: '3',
            insurance_company_id: '3',
            product_master_id: '36',
            sub_product_name: 'VARIANT 4',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:28:57',
            updated_at: '2024-12-28 11:32:25',
            status: '1'
        },
        {
            id: 41,
            main_product_id: '3',
            insurance_company_id: '3',
            product_master_id: '36',
            sub_product_name: 'VARIANT 5',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:35:07',
            updated_at: '2024-12-28 11:38:51',
            status: '1'
        },
        {
            id: 42,
            main_product_id: '3',
            insurance_company_id: '3',
            product_master_id: '36',
            sub_product_name: 'VARIANT 6',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:39:52',
            updated_at: '2024-12-28 11:44:52',
            status: '1'
        },
        {
            id: 43,
            main_product_id: '4',
            insurance_company_id: '2',
            product_master_id: '17',
            sub_product_name: 'CARE SILVER',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:50:41',
            updated_at: '2024-12-28 11:51:58',
            status: '1'
        },
        {
            id: 44,
            main_product_id: '4',
            insurance_company_id: '2',
            product_master_id: '17',
            sub_product_name: 'CARE GOLD',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:52:43',
            updated_at: '2024-12-28 11:54:18',
            status: '1'
        },
        {
            id: 45,
            main_product_id: '4',
            insurance_company_id: '2',
            product_master_id: '17',
            sub_product_name: 'CARE PLATINUM',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:54:57',
            updated_at: '2024-12-28 11:55:45',
            status: '1'
        },
        {
            id: 46,
            main_product_id: '1',
            insurance_company_id: '5',
            product_master_id: '40',
            sub_product_name: '300000',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: null,
            post_hospitalization_days: null,
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:57:42',
            updated_at: '2024-12-28 11:58:20',
            status: '0'
        },
        {
            id: 47,
            main_product_id: '1',
            insurance_company_id: '5',
            product_master_id: '40',
            sub_product_name: 'HEALTH PROTECTOR',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 11:59:03',
            updated_at: '2024-12-28 12:00:25',
            status: '1'
        },
        {
            id: 48,
            main_product_id: '3',
            insurance_company_id: '5',
            product_master_id: '41',
            sub_product_name: 'IPA',
            co_pay: '0',
            child_separation_age: '25',
            pre_hospitalization_days: '0',
            post_hospitalization_days: '0',
            created_by: '1',
            updated_by: '1',
            created_at: '2024-12-28 12:06:00',
            updated_at: '2024-12-28 12:06:27',
            status: '1'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('sub_product_age_sum').del();
    // Then insert the sub product age sum data
    await knex('sub_product_age_sum').insert([
        {
            id: 6,
            sub_product_id: '4',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 7,
            sub_product_id: '4',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 8,
            sub_product_id: '4',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 9,
            sub_product_id: '5',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 10,
            sub_product_id: '5',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 11,
            sub_product_id: '5',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 12,
            sub_product_id: '5',
            min_age: '0',
            max_age: '100',
            sum_insured: '3000000'
        },
        {
            id: 13,
            sub_product_id: '6',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 14,
            sub_product_id: '6',
            min_age: '0',
            max_age: '100',
            sum_insured: '7500000'
        },
        {
            id: 15,
            sub_product_id: '7',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 16,
            sub_product_id: '7',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 17,
            sub_product_id: '7',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 18,
            sub_product_id: '8',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 19,
            sub_product_id: '8',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 20,
            sub_product_id: '8',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 21,
            sub_product_id: '9',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 22,
            sub_product_id: '10',
            min_age: '0',
            max_age: '100',
            sum_insured: '50000'
        },
        {
            id: 23,
            sub_product_id: '10',
            min_age: '0',
            max_age: '100',
            sum_insured: '100000'
        },
        {
            id: 24,
            sub_product_id: '10',
            min_age: '0',
            max_age: '100',
            sum_insured: '150000'
        },
        {
            id: 25,
            sub_product_id: '10',
            min_age: '0',
            max_age: '100',
            sum_insured: '200000'
        },
        {
            id: 26,
            sub_product_id: '10',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 27,
            sub_product_id: '10',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 28,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '50000'
        },
        {
            id: 29,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '100000'
        },
        {
            id: 30,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '150000'
        },
        {
            id: 31,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '200000'
        },
        {
            id: 32,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 33,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 34,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '750000'
        },
        {
            id: 35,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 36,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 37,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 38,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 39,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '3000000'
        },
        {
            id: 40,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '4000000'
        },
        {
            id: 41,
            sub_product_id: '11',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 42,
            sub_product_id: '12',
            min_age: '60',
            max_age: '99',
            sum_insured: '100000'
        },
        {
            id: 43,
            sub_product_id: '12',
            min_age: '60',
            max_age: '99',
            sum_insured: '200000'
        },
        {
            id: 44,
            sub_product_id: '12',
            min_age: '60',
            max_age: '99',
            sum_insured: '300000'
        },
        {
            id: 45,
            sub_product_id: '12',
            min_age: '60',
            max_age: '99',
            sum_insured: '400000'
        },
        {
            id: 46,
            sub_product_id: '12',
            min_age: '60',
            max_age: '99',
            sum_insured: '500000'
        },
        {
            id: 47,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '200000'
        },
        {
            id: 48,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '250000'
        },
        {
            id: 49,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 50,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '350000'
        },
        {
            id: 51,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '400000'
        },
        {
            id: 52,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '450000'
        },
        {
            id: 53,
            sub_product_id: '13',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 54,
            sub_product_id: '14',
            min_age: '0',
            max_age: '100',
            sum_insured: '600000'
        },
        {
            id: 55,
            sub_product_id: '14',
            min_age: '0',
            max_age: '100',
            sum_insured: '750000'
        },
        {
            id: 56,
            sub_product_id: '14',
            min_age: '0',
            max_age: '100',
            sum_insured: '800000'
        },
        {
            id: 57,
            sub_product_id: '14',
            min_age: '0',
            max_age: '100',
            sum_insured: '900000'
        },
        {
            id: 58,
            sub_product_id: '15',
            min_age: '18',
            max_age: '70',
            sum_insured: '1000000'
        },
        {
            id: 59,
            sub_product_id: '15',
            min_age: '18',
            max_age: '70',
            sum_insured: '2000000'
        },
        {
            id: 60,
            sub_product_id: '15',
            min_age: '18',
            max_age: '70',
            sum_insured: '1500000'
        },
        {
            id: 61,
            sub_product_id: '15',
            min_age: '18',
            max_age: '70',
            sum_insured: '5000000'
        },
        {
            id: 67,
            sub_product_id: '17',
            min_age: '0',
            max_age: '100',
            sum_insured: '50000'
        },
        {
            id: 68,
            sub_product_id: '18',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 69,
            sub_product_id: '18',
            min_age: '0',
            max_age: '100',
            sum_insured: '700000'
        },
        {
            id: 70,
            sub_product_id: '18',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 71,
            sub_product_id: '18',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 72,
            sub_product_id: '18',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 73,
            sub_product_id: '18',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 74,
            sub_product_id: '19',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 75,
            sub_product_id: '19',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 76,
            sub_product_id: '19',
            min_age: '0',
            max_age: '100',
            sum_insured: '700000'
        },
        {
            id: 77,
            sub_product_id: '19',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 78,
            sub_product_id: '20',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 79,
            sub_product_id: '20',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 80,
            sub_product_id: '20',
            min_age: '0',
            max_age: '100',
            sum_insured: '10000000'
        },
        {
            id: 81,
            sub_product_id: '21',
            min_age: '0',
            max_age: '35',
            sum_insured: '500000'
        },
        {
            id: 82,
            sub_product_id: '21',
            min_age: '0',
            max_age: '35',
            sum_insured: '700000'
        },
        {
            id: 83,
            sub_product_id: '21',
            min_age: '0',
            max_age: '35',
            sum_insured: '1000000'
        },
        {
            id: 84,
            sub_product_id: '21',
            min_age: '0',
            max_age: '35',
            sum_insured: '2500000'
        },
        {
            id: 85,
            sub_product_id: '22',
            min_age: '36',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 86,
            sub_product_id: '22',
            min_age: '36',
            max_age: '100',
            sum_insured: '700000'
        },
        {
            id: 87,
            sub_product_id: '22',
            min_age: '36',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 88,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 89,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '700000'
        },
        {
            id: 90,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 91,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 92,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 93,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 94,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '3000000'
        },
        {
            id: 95,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '4000000'
        },
        {
            id: 96,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 97,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '6000000'
        },
        {
            id: 98,
            sub_product_id: '23',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 99,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '300000'
        },
        {
            id: 100,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 101,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 102,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 103,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 104,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 105,
            sub_product_id: '24',
            min_age: '18',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 106,
            sub_product_id: '25',
            min_age: '18',
            max_age: '65',
            sum_insured: '300000'
        },
        {
            id: 107,
            sub_product_id: '25',
            min_age: '18',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 108,
            sub_product_id: '25',
            min_age: '18',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 109,
            sub_product_id: '25',
            min_age: '18',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 110,
            sub_product_id: '25',
            min_age: '18',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 111,
            sub_product_id: '25',
            min_age: '18',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 112,
            sub_product_id: '26',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 113,
            sub_product_id: '27',
            min_age: '0',
            max_age: '100',
            sum_insured: '750000'
        },
        {
            id: 114,
            sub_product_id: '27',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 115,
            sub_product_id: '27',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 116,
            sub_product_id: '28',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 117,
            sub_product_id: '28',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 118,
            sub_product_id: '28',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 119,
            sub_product_id: '28',
            min_age: '0',
            max_age: '100',
            sum_insured: '7500000'
        },
        {
            id: 120,
            sub_product_id: '29',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 122,
            sub_product_id: '29',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 123,
            sub_product_id: '29',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 124,
            sub_product_id: '29',
            min_age: '0',
            max_age: '100',
            sum_insured: '2500000'
        },
        {
            id: 125,
            sub_product_id: '29',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 126,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 127,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 128,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 129,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 130,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 131,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 132,
            sub_product_id: '30',
            min_age: '0',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 133,
            sub_product_id: '31',
            min_age: '18',
            max_age: '80',
            sum_insured: '1000000'
        },
        {
            id: 134,
            sub_product_id: '31',
            min_age: '18',
            max_age: '80',
            sum_insured: '2500000'
        },
        {
            id: 135,
            sub_product_id: '31',
            min_age: '18',
            max_age: '80',
            sum_insured: '5000000'
        },
        {
            id: 136,
            sub_product_id: '31',
            min_age: '18',
            max_age: '80',
            sum_insured: '10000000'
        },
        {
            id: 137,
            sub_product_id: '32',
            min_age: '0',
            max_age: '70',
            sum_insured: '50000'
        },
        {
            id: 138,
            sub_product_id: '33',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 139,
            sub_product_id: '33',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 140,
            sub_product_id: '33',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 141,
            sub_product_id: '33',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 142,
            sub_product_id: '33',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 143,
            sub_product_id: '33',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 144,
            sub_product_id: '34',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 145,
            sub_product_id: '34',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 146,
            sub_product_id: '34',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 147,
            sub_product_id: '34',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 148,
            sub_product_id: '34',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 149,
            sub_product_id: '34',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 150,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '300000'
        },
        {
            id: 151,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '400000'
        },
        {
            id: 152,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 153,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '750000'
        },
        {
            id: 154,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 155,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '1250000'
        },
        {
            id: 156,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 157,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 158,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 159,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 160,
            sub_product_id: '35',
            min_age: '0',
            max_age: '65',
            sum_insured: '7500000'
        },
        {
            id: 161,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 162,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '750000'
        },
        {
            id: 163,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '1000000'
        },
        {
            id: 164,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '1250000'
        },
        {
            id: 165,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '1500000'
        },
        {
            id: 166,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '2000000'
        },
        {
            id: 167,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '3000000'
        },
        {
            id: 168,
            sub_product_id: '36',
            min_age: '0',
            max_age: '100',
            sum_insured: '5000000'
        },
        {
            id: 169,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 170,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 171,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 172,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 173,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 174,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '3000000'
        },
        {
            id: 175,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '4000000'
        },
        {
            id: 176,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 177,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '6000000'
        },
        {
            id: 178,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '7000000'
        },
        {
            id: 179,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '8000000'
        },
        {
            id: 180,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '9000000'
        },
        {
            id: 181,
            sub_product_id: '37',
            min_age: '18',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 182,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 183,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 184,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 185,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 186,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 187,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '3000000'
        },
        {
            id: 188,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '4000000'
        },
        {
            id: 189,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 190,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '6000000'
        },
        {
            id: 191,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '7000000'
        },
        {
            id: 192,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '8000000'
        },
        {
            id: 193,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '9000000'
        },
        {
            id: 194,
            sub_product_id: '38',
            min_age: '0',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 195,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 196,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 197,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 198,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 199,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 200,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '3000000'
        },
        {
            id: 201,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '4000000'
        },
        {
            id: 202,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 203,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '6000000'
        },
        {
            id: 204,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '7000000'
        },
        {
            id: 205,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '8000000'
        },
        {
            id: 206,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '9000000'
        },
        {
            id: 207,
            sub_product_id: '39',
            min_age: '0',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 208,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 209,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 210,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 211,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 212,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 213,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '3000000'
        },
        {
            id: 214,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '4000000'
        },
        {
            id: 215,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 216,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '6000000'
        },
        {
            id: 217,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '7000000'
        },
        {
            id: 218,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '8000000'
        },
        {
            id: 219,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '9000000'
        },
        {
            id: 220,
            sub_product_id: '40',
            min_age: '0',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 221,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 222,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 223,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 224,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 225,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 226,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '3000000'
        },
        {
            id: 227,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '4000000'
        },
        {
            id: 228,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 229,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '6000000'
        },
        {
            id: 230,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '7000000'
        },
        {
            id: 231,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '8000000'
        },
        {
            id: 232,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '9000000'
        },
        {
            id: 233,
            sub_product_id: '41',
            min_age: '0',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 234,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '500000'
        },
        {
            id: 235,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '1000000'
        },
        {
            id: 236,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '1500000'
        },
        {
            id: 237,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '2000000'
        },
        {
            id: 238,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '2500000'
        },
        {
            id: 239,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '3000000'
        },
        {
            id: 240,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '4000000'
        },
        {
            id: 241,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '5000000'
        },
        {
            id: 242,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '6000000'
        },
        {
            id: 243,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '7000000'
        },
        {
            id: 244,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '8000000'
        },
        {
            id: 245,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '9000000'
        },
        {
            id: 246,
            sub_product_id: '42',
            min_age: '0',
            max_age: '65',
            sum_insured: '10000000'
        },
        {
            id: 247,
            sub_product_id: '43',
            min_age: '0',
            max_age: '100',
            sum_insured: '50000'
        },
        {
            id: 248,
            sub_product_id: '43',
            min_age: '0',
            max_age: '100',
            sum_insured: '100000'
        },
        {
            id: 249,
            sub_product_id: '44',
            min_age: '0',
            max_age: '100',
            sum_insured: '50000'
        },
        {
            id: 250,
            sub_product_id: '44',
            min_age: '0',
            max_age: '100',
            sum_insured: '100000'
        },
        {
            id: 251,
            sub_product_id: '44',
            min_age: '0',
            max_age: '100',
            sum_insured: '200000'
        },
        {
            id: 252,
            sub_product_id: '44',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 253,
            sub_product_id: '44',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 254,
            sub_product_id: '45',
            min_age: '0',
            max_age: '100',
            sum_insured: '50000'
        },
        {
            id: 255,
            sub_product_id: '45',
            min_age: '0',
            max_age: '100',
            sum_insured: '100000'
        },
        {
            id: 256,
            sub_product_id: '45',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 257,
            sub_product_id: '45',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 258,
            sub_product_id: '46',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 259,
            sub_product_id: '47',
            min_age: '0',
            max_age: '100',
            sum_insured: '300000'
        },
        {
            id: 260,
            sub_product_id: '47',
            min_age: '0',
            max_age: '100',
            sum_insured: '350000'
        },
        {
            id: 261,
            sub_product_id: '47',
            min_age: '0',
            max_age: '100',
            sum_insured: '400000'
        },
        {
            id: 262,
            sub_product_id: '47',
            min_age: '0',
            max_age: '100',
            sum_insured: '450000'
        },
        {
            id: 263,
            sub_product_id: '47',
            min_age: '0',
            max_age: '100',
            sum_insured: '500000'
        },
        {
            id: 264,
            sub_product_id: '47',
            min_age: '0',
            max_age: '100',
            sum_insured: '200000'
        },
        {
            id: 265,
            sub_product_id: '48',
            min_age: '0',
            max_age: '100',
            sum_insured: '400000'
        }
    ]);

    // Clear existing data before inserting new records
    await knex('sub_product_rider').del();
    // Then insert the sub product rider data
    await knex('sub_product_rider').insert([
        {
            id: 1,
            sub_product_id: '7',
            rider_details: 'RIDER 1'
        },
        {
            id: 2,
            sub_product_id: '7',
            rider_details: 'RIDER 2'
        },
        {
            id: 3,
            sub_product_id: '18',
            rider_details: 'AIR AMBULANCES'
        },
        {
            id: 4,
            sub_product_id: '18',
            rider_details: 'CUMULATIVE BONUS SUPER'
        },
        {
            id: 5,
            sub_product_id: '18',
            rider_details: 'INSTANT COVER'
        },
        {
            id: 6,
            sub_product_id: '18',
            rider_details: 'PED WAIT PERIOD MODIFICATION'
        },
        {
            id: 7,
            sub_product_id: '18',
            rider_details: 'WELLNESS BENEFIT'
        },
        {
            id: 8,
            sub_product_id: '18',
            rider_details: 'ANNUAL HEALTH CHECK UP'
        },
        {
            id: 9,
            sub_product_id: '18',
            rider_details: 'CLAIM SHIELD'
        },
        {
            id: 10,
            sub_product_id: '20',
            rider_details: 'NCB SUPER'
        },
        {
            id: 11,
            sub_product_id: '20',
            rider_details: 'SMART SELECT'
        },
        {
            id: 12,
            sub_product_id: '20',
            rider_details: 'AIR AMBULANCE'
        },
        {
            id: 13,
            sub_product_id: '20',
            rider_details: 'ANNUAL HEALTH CHECK UP'
        },
        {
            id: 14,
            sub_product_id: '20',
            rider_details: 'ROOM RENT MODIFICATION'
        },
        {
            id: 15,
            sub_product_id: '20',
            rider_details: 'CARE SHIELD'
        },
        {
            id: 16,
            sub_product_id: '20',
            rider_details: 'DEDUCTION IN PED'
        },
        {
            id: 17,
            sub_product_id: '21',
            rider_details: 'SMART SELECT'
        },
        {
            id: 18,
            sub_product_id: '21',
            rider_details: 'MATERNITY'
        },
        {
            id: 19,
            sub_product_id: '21',
            rider_details: 'INTERNATIONAL SECOND OPINION'
        },
        {
            id: 20,
            sub_product_id: '22',
            rider_details: 'SMART SELECT'
        },
        {
            id: 21,
            sub_product_id: '22',
            rider_details: 'INTERNATIONAL SECOND OPINION'
        },
        {
            id: 22,
            sub_product_id: '23',
            rider_details: 'UNLIMITED RECHARGE'
        },
        {
            id: 23,
            sub_product_id: '23',
            rider_details: 'EVERYDAY CARE'
        },
        {
            id: 24,
            sub_product_id: '23',
            rider_details: 'REDUCTION IN PED WAITING PERIOD'
        },
        {
            id: 25,
            sub_product_id: '23',
            rider_details: 'OPD CARE'
        },
        {
            id: 26,
            sub_product_id: '23',
            rider_details: 'AIR AMBULANCE'
        },
        {
            id: 27,
            sub_product_id: '23',
            rider_details: 'CARE SHIELD'
        },
        {
            id: 28,
            sub_product_id: '27',
            rider_details: 'UNLIMITED RESTORE BENEFIT'
        },
        {
            id: 29,
            sub_product_id: '28',
            rider_details: 'UNLIMITED RESTORE BENEFIT'
        },
        {
            id: 30,
            sub_product_id: '29',
            rider_details: 'PROTECTOR RIDER'
        },
        {
            id: 31,
            sub_product_id: '29',
            rider_details: 'UNLIMITED RESTORE BENIFIT'
        },
        {
            id: 32,
            sub_product_id: '33',
            rider_details: 'HOSPITAL CASH'
        },
        {
            id: 33,
            sub_product_id: '33',
            rider_details: 'SAFEGUARD'
        },
        {
            id: 34,
            sub_product_id: '33',
            rider_details: 'SAFEGUARD +'
        },
        {
            id: 35,
            sub_product_id: '33',
            rider_details: 'MEDICAL PRACTITIONER'
        },
        {
            id: 36,
            sub_product_id: '34',
            rider_details: 'HOSPITAL CASH'
        },
        {
            id: 37,
            sub_product_id: '34',
            rider_details: 'SAFE GUARD'
        },
        {
            id: 38,
            sub_product_id: '34',
            rider_details: 'SAFE GUARD +'
        },
        {
            id: 39,
            sub_product_id: '34',
            rider_details: 'MEDICAL PRACTITIONER'
        },
        {
            id: 40,
            sub_product_id: '35',
            rider_details: 'HOSPITAL CASH'
        },
        {
            id: 41,
            sub_product_id: '35',
            rider_details: 'SAFEGUARD'
        },
        {
            id: 42,
            sub_product_id: '35',
            rider_details: 'MEDICAL PRACTITIONER'
        },
        {
            id: 43,
            sub_product_id: '36',
            rider_details: 'HOSPITAL CASH'
        },
        {
            id: 44,
            sub_product_id: '36',
            rider_details: 'SAFE GUARD'
        }
    ]);
    await knex('agents').del();
    // Then insert the agents data
    await knex('agents').insert([
        {
            id: 1,
            full_name: 'PIYUSH PANDYA',
            gender_id: 1,
            education_id: 13,
            marital_status_id: 5,
            marriage_date: '1995-01-01',
            aadhar_number: '**************',
            pan_number: '**********',
            personal_email: '<EMAIL>',
            personal_mobile: '**********',
            dob: '1975-10-23',
            blood_group: '59',
            driving_license_no: 'GJ-0420120000977',
            agent_id: 'RM-0001',
            password: '$2b$10$sGHxM2Keu8HGrqZ3goqAS.R.P60UI8PguQ4xdNCOGez77mo/u9AEu',
            role_id: 2,
            branch_id: 1,
            first_reporting_manager_id: 'PWS-ADM001',
            second_reporting_manager_id: 'PWS-ADM002',
            official_email: '<EMAIL>',
            official_mobile: '**********',
            date_of_joining: '2014-12-10',
            photo: '',
            aadhar_card_front: '',
            aadhar_card_back: '',
            pan_card: '',
            signed_offer_letter_card: null,
            driving_license_card: null,
            status: 1,
            created_by: 1,
            updated_by: 1,
            created_at: '2025-01-07 05:43:44',
            updated_at: '2025-01-07 05:43:44'
        }
    ]);

};
