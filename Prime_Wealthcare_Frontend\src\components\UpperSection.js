import { <PERSON>, <PERSON>u, <PERSON>uI<PERSON>, <PERSON>con<PERSON>utton, <PERSON>ge, Popover, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { logoutUser, validateToken, getTaskNotifications, fetchEmployeeByUserId, updateTaskNotification } from '../redux/actions/action';
import { clearUserSession } from '../utils/storage';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { keyframes } from '@mui/system';
import { clearTaskNotifications } from '../redux/slices/tasks/taskSlice';

const pulse = keyframes`
  0% { transform: scale(1); } 
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

function UpperSection() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [anchorEl, setAnchorEl] = useState(null);
    const [notificationAnchorEl, setNotificationAnchorEl] = useState(null);
    const [username, setUsername] = useState('');
    const [isLoading, setIsLoading] = useState(true);
    const [isMobile, setIsMobile] = useState(false);
    const [currentEmployeeId, setCurrentEmployeeId] = useState(null);
    const user = useSelector((state) => state.auth.user);
    const isAuthenticated = useSelector((state) => state.auth.isAuthenticated);
    const notifications = useSelector((state) => {
        const allNotifications = state.tasks.notifications;

        // Convert task notifications to the expected format
        const formattedNotifications = allNotifications.taskNotifications.map(notification => ({
            id: notification.task_id,
            user_id: notification.user_id,
            task_code: notification.task_code,
            title: notification.title,
            //   description: notification.message,
            timestamp: notification.created_at,
            is_read: notification.is_read === 1
        }));

        // Filter notifications for current employee and unread status
        const filteredNotifications = formattedNotifications.filter(notification =>
            notification.user_id === currentEmployeeId &&
            !notification.is_read
        );

        return {
            ...allNotifications,
            newTasks: filteredNotifications,
            unreadCount: filteredNotifications.length
        };
    });

    useEffect(() => {
        setIsMobile(window.innerWidth <= 768);
    }, [window.innerWidth]);

    useEffect(() => {
        const fetchCurrentEmployee = async () => {
            if (user?.userId) {
                try {
                    const currentEmployeeResponse = await dispatch(fetchEmployeeByUserId(user.userId)).unwrap();

                    if (currentEmployeeResponse) {
                        setCurrentEmployeeId(currentEmployeeResponse.id);
                    }
                } catch (error) {
                    console.error('Error fetching current employee:', error);
                }
            }
        };

        fetchCurrentEmployee();
    }, [dispatch, user]);


    useEffect(() => {
        const initializeUser = async () => {
            setIsLoading(true);

            // First try to get user from Redux store
            if (user && Object.keys(user).length > 0) {
                const displayName = user.userName || user.full_name || 'Guest';
                setUsername(displayName);
                setIsLoading(false);
                return;
            }

            // If no Redux user, try session storage
            const storedUser = sessionStorage.getItem('user');
            if (storedUser) {
                const parsedUser = JSON.parse(storedUser);
                const displayName = parsedUser.employee_full_name || parsedUser.username || parsedUser.userId || parsedUser.user_id;
                setUsername(displayName);
                setIsLoading(false);
                return;
            }

            // If neither exists, check token and validate
            const token = sessionStorage.getItem('token') || localStorage.getItem('token');
            if (token && !isAuthenticated) {
                try {
                    const result = await dispatch(validateToken(token)).unwrap();
                    if (result?.user) {
                        const displayName = result.user.employee_full_name ||
                            result.user.username ||
                            result.user.userId ||
                            result.user.user_id;
                        setUsername(displayName);
                    }
                } catch (error) {
                    console.error('❌ Token validation failed:', error);
                    navigate('/');
                }
            } else if (!token) {
                navigate('/');
            }

            setIsLoading(false);
        };

        initializeUser();
    }, [user, isAuthenticated, dispatch, navigate]);

    useEffect(() => {
        const initializeUserAndNotifications = async () => {
            if (user?.userId) {
                try {
                    // First get employee details
                    const employeeResponse = await dispatch(fetchEmployeeByUserId(user.userId)).unwrap();
                    if (employeeResponse?.id) {
                        // Use employee ID for notifications
                        dispatch(getTaskNotifications(employeeResponse.id));
                    }
                } catch (error) {
                    console.error('Error fetching employee details:', error);
                }
            }
        };

        initializeUserAndNotifications();
    }, [dispatch, user]);

    const handleUserIconClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleLogout = async () => {
        try {
            navigate('/');
            // Then perform logout actions
            await dispatch(logoutUser());
            clearUserSession();
            setUsername('');
            setAnchorEl(null);
        } catch (error) {
            console.error('❌ Logout failed:', error);
        }
    };

    const handleNotificationClick = async (task) => {
        try {
            await dispatch(updateTaskNotification({
                taskId: task.id,
                notificationData: {
                    updated_by: currentEmployeeId
                }
            })).unwrap();

            // Refresh notifications after update
            if (currentEmployeeId) {
                dispatch(getTaskNotifications(currentEmployeeId));
            }

            // Close notification popover
            setNotificationAnchorEl(null);
            
            // Navigate to the task board page
            navigate('/dashboard/task-board');
        } catch (error) {
            console.error('Error updating notification:', error);
        }
    };

    if (isLoading) {
        return <Box>Loading...</Box>; // Or your loading component
    }

    return (
        <Box className='Navbar' component='nav'>
            <Box className='navbar-upper-section'>
                <img
                    className={`company-logo ${isMobile ? 'mobile-logo' : 'desktop-logo'}`}
                    src={isMobile ? '/logo_icon.png' : '/logo.png'}
                    alt='company_logo'
                    onClick={() => navigate('/dashboard/crm-dashboard')}
                />
                <Box className='upper-section-right-side'>
                    <IconButton onClick={(e) => setNotificationAnchorEl(e.currentTarget)}>
                        <Badge
                            badgeContent={notifications.unreadCount}
                            color="error"
                            sx={{
                                '& .MuiBadge-badge': {
                                    animation: notifications.unreadCount > 0 ? `${pulse} 1s infinite` : 'none',
                                    // Make the badge number smaller
                                },

                            }}
                        >
                            <NotificationsIcon sx={{ fontSize: 35, color: '#528a7e' }} /> {/* Increase the size of the bell icon and change its color */}
                        </Badge>
                    </IconButton>

                    <Popover
                        open={Boolean(notificationAnchorEl)}
                        anchorEl={notificationAnchorEl}
                        onClose={() => {
                            setNotificationAnchorEl(null);
                            dispatch(clearTaskNotifications());
                        }}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                    >
                        <Box sx={{ p: 2, width: 300, maxHeight: 400, overflow: 'auto' }}>
                            {notifications.newTasks.length > 0 ? (
                                notifications.newTasks.map((task) => (
                                    <Box
                                        key={task.id}
                                        onClick={() => handleNotificationClick(task)}
                                        sx={{
                                            mb: 2,
                                            p: 2,
                                            borderRadius: 1,
                                            bgcolor: task.is_read ? '#f5f5f5' : '#eef6ff',
                                            border: '1px solid #e0e0e0',
                                            borderLeft: '4px solid #1976d2',
                                            '&:hover': {
                                                bgcolor: '#e3f2fd',
                                                cursor: 'pointer'
                                            }
                                        }}
                                    >
                                        <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 600 }}>
                                            {task.task_code}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {task.title}
                                        </Typography>
                                        <Box sx={{
                                            mt: 1,
                                            pt: 1,
                                            borderTop: '1px solid #e0e0e0',
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center'
                                        }}>
                                            <Typography variant="caption" color="text.secondary">
                                                Created: {new Date(task.timestamp).toLocaleString()}
                                            </Typography>

                                        </Box>
                                    </Box>
                                ))
                            ) : (
                                <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                                    No new notifications
                                </Typography>
                            )}
                        </Box>
                    </Popover>

                    <img
                        className='upper-section-icon'
                        src='/Group.png'
                        alt='user_icon'
                        onClick={handleUserIconClick}
                    />
                    {!isMobile &&
                        <p className='user-name'>{username || 'Guest'}</p>
                    }
                    <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={handleClose}
                    >
                        <MenuItem onClick={handleLogout}>Logout</MenuItem>
                    </Menu>
                </Box>
            </Box>
        </Box>
    );
}

export default UpperSection;