const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const { sendEmail } = require('../../../services/emailService');  // Assuming emailService.js is inside services folder

// Function to generate a password based on the agent's first name
const generatePasswordFromFirstName = (firstName) => {
    return `${firstName}@123`;
};

const generateUserId = async () => {
    const lastUser = await db('agents').orderBy('agent_id', 'desc').first();
    let userId = lastUser ? lastUser.agent_id : 'RM-0000';

    let numericPart = parseInt(userId.slice(3), 10);

    numericPart++;

    const newUserId = `RM-${String(numericPart).padStart(4, '0')}`;
    const exists = await db('agents').where({ agent_id: newUserId }).first();
    return exists ? generateUserId() : newUserId;
};

// Getting agents by branch ID
const getAgentByBranchName = async (id) => {
    try {
        return await db('agents')
            .select('agents.*')
            .where('agents.branch_id', id);
    } catch (error) {
        throw error;
    }
};

// Function to increment the alphabetical part of the user ID (AAA -> AAB -> ZZZ)
const incrementPrefix = (prefix) => {
    let chars = prefix.split('');

    for (let i = chars.length - 1; i >= 0; i--) {
        if (chars[i] === 'Z') {
            chars[i] = 'A';
        } else {
            chars[i] = String.fromCharCode(chars[i].charCodeAt(0) + 1);
            break;
        }
    }

    return chars.join('');
};


// Create a new agent
const create = async (data) => {
    try {
        data.password = generatePasswordFromFirstName(data.full_name.split(' ')[0]);
        data.agent_id = await generateUserId();
        if (data.marriage_date === '' || data.marriage_date === 'null' || data.marriage_date === null || data.marriage_date === undefined) {
            data.marriage_date = null;
        }

        // Insert data and get the inserted id
        const [id] = await db('agents').insert(data);
        const host = process.env.CORS_ORIGIN_HOST;
        const port = process.env.CORS_ORIGIN_PORT;

        // Prepare the email content with a login link
        const subject = 'Welcome to Prime Wealthcare';
        const text = `Dear ${data.full_name},\n\nYou have been successfully registered as an agent at Prime Wealthcare.\n\nYour User ID: ${data.agent_id}\nYour Password: ${data.password}\n\nPlease use the link below to log in and access your account:\n\n${host}:${port}\n\nBest regards,\nPrime Wealthcare Team`;

        // HTML content for the email to include the clickable link
        const htmlContent = `
               <p>Dear ${data.full_name},</p>
               <p>You have been successfully registered as an agent at Prime Wealthcare.</p>
               <p>Your User ID: <strong>${data.agent_id}</strong></p>
               <p>Your Password: <strong>${data.password}</strong></p>
               <p>Please click the link below to log in and access your account:</p>
               <p><a href=${host}:${port} target="_blank">Login to Prime Wealthcare</a></p>
               <p><strong>Important:</strong> For security reasons, you will be required to change your password upon first login.</p>
               <p>Best regards,<br>Prime Wealthcare Team</p>
           `;

        // Send email to official_email if provided
        if (data.official_email) {
            await sendEmail(data.official_email, subject, text, htmlContent);
        }

        // Send email to personal_email (assuming it's required)
        if (data.personal_email) {
            await sendEmail(data.personal_email, subject, text, htmlContent);
        }

        // Return the created agent with all joined data
        return id;
    } catch (error) {
        console.error("Error creating agent: ", error);
        throw error;
    }
};

// Find all agents with joined data
const findAll = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            );
    } catch (error) {
        throw error;
    }
};

// Find agent by ID with joined data
const findByIdForEdit = async (id) => {
    try {
        const data = await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.full_name',
                'agents.gender_id',
                'agents.education_id',
                'agents.aadhar_number',
                'agents.pan_number',
                'agents.personal_email',
                'agents.personal_mobile',
                'agents.dob',
                'agents.blood_group',
                'agents.marital_status_id',
                'agents.driving_license_no',
                'agents.agent_id',
                'agents.role_id',
                'agents.branch_id',
                'agents.first_reporting_manager_id',
                'agents.second_reporting_manager_id',
                'agents.official_email',
                'agents.official_mobile',
                'agents.date_of_joining',
                'agents.photo',
                'agents.aadhar_card_front',
                'agents.aadhar_card_back',
                'agents.pan_card',
                'agents.signed_offer_letter_card',
                'agents.driving_license_card',
                'agents.marriage_date',
                'agents.status'
            )
            .where('agents.id', id)
            .first();

        return data;
    } catch (error) {
        console.error("Error finding agent by ID:", error);
        throw error;
    }
}

const findById = async (id) => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'role_management.department_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .where('agents.id', id)
            .first();
    } catch (error) {
        console.error("Error finding agent by ID:", error);
        throw error;
    }
};

// Update agent by ID with updated data and return joined result
const updateById = async (id, data) => {
    try {
        data.updated_at = getCurrentTimestamp();
        if (data.marriage_date === 'null') {
            data.marriage_date = null;
        }
        await db('agents').where({ id }).update(data);

        // Return the updated agent with all joined data
        return await findById(id);
    } catch (error) {
        console.error("Error updating agent by ID:", error);
        throw error;
    }
};

// Soft delete agent by ID and return the updated agent with joined data
const softDeleteById = async (id) => {
    try {
        await db('agents').where({ id }).update({ status: false, updated_at: getCurrentTimestamp() });
        // Return the updated agent with all joined data
        return await findById(id);
    } catch (error) {
        throw error;
    }
};

// Reinstate agent by ID and return the updated agent with joined data
const reinstateById = async (id) => {
    try {
        await db('agents').where({ id }).update({ status: true, updated_at: getCurrentTimestamp() });
        // Return the reinstated agent with all joined data
        return await findById(id);
    } catch (error) {
        throw error;
    }
};

// Find agents by agent name (partial match)
const findByName = async (query) => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .where(function () {
                this.where('agents.full_name', 'like', `%${query}%`)
                    .orWhere('agents.agent_id', 'like', `%${query}%`)
                    .orWhere('role_management.role_name', 'like', `%${query}%`)
                    .orWhere('agents.personal_mobile', 'like', `%${query}%`)
                    .orWhere('imf_branches.branch_name', 'like', `%${query}%`)
                    .orWhere('locations.city', 'like', `%${query}%`);
            });
    } catch (error) {
        console.error("Error finding agents by name:", error);
        throw error;
    }
};

// New agents created last week
const newLastWeek = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .where('agents.created_at', '<', db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'));
    } catch (error) {
        console.error("Error fetching new agents from last week:", error);
        throw error;
    }
};

// New agents created this week
const newThisWeek = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .whereBetween('agents.created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ]);
    } catch (error) {
        console.error("Error fetching new agents this week:", error);
        throw error;
    }
};

// Deactivated agents this week
const deactivatedThisWeek = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .where('agents.status', 0)
            .whereBetween('agents.updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);
    } catch (error) {
        console.error("Error fetching deactivated agents this week:", error);
        throw error;
    }
};

// Deactivated agents last week
const deactivatedLastWeek = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .where('agents.status', 0)
            .whereBetween('agents.updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);
    } catch (error) {
        console.error("Error fetching deactivated agents last week:", error);
        throw error;
    }
};

// Agents edited this week
const editedThisWeek = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .whereBetween('agents.updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ])
            .whereNot('agents.created_at', '=', db.raw('agents.updated_at'));
    } catch (error) {
        console.error("Error fetching edited agents this week:", error);
        throw error;
    }
};

// Agents edited last week
const editedLastWeek = async () => {
    try {
        return await db('agents')
            .leftJoin('pick_list as gender', function () {
                this.on('agents.gender_id', '=', 'gender.id')
                    .andOn('gender.type_name', '=', db.raw('?', ['Gender']));
            })
            .leftJoin('pick_list as education', function () {
                this.on('agents.education_id', '=', 'education.id')
                    .andOn('education.type_name', '=', db.raw('?', ['Education']));
            })
            .leftJoin('pick_list as marital_status', function () {
                this.on('agents.marital_status_id', '=', 'marital_status.id')
                    .andOn('marital_status.type_name', '=', db.raw('?', ['Marital Status']));
            })
            .leftJoin('imf_branches', 'agents.branch_id', 'imf_branches.id')
            .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
            .leftJoin('role_management', 'agents.role_id', 'role_management.id')
            .leftJoin('agents as first_mgr', 'agents.first_reporting_manager_id', 'first_mgr.id')
            .leftJoin('agents as second_mgr', 'agents.second_reporting_manager_id', 'second_mgr.id')
            .select(
                'agents.*',
                'gender.label_name as gender',
                'education.label_name as education',
                'marital_status.label_name as marital_status',
                'imf_branches.branch_name',
                'locations.city as branch_city',
                'role_management.role_name',
                'first_mgr.full_name as first_manager_name',
                'second_mgr.full_name as second_manager_name'
            )
            .whereBetween('agents.updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ])
            .whereNot('agents.created_at', '=', db.raw('agents.updated_at'));
    } catch (error) {
        console.error("Error fetching edited agents last week:", error);
        throw error;
    }
};

module.exports = {
    create,
    findAll,
    findById,
    findByIdForEdit,
    findByName,
    getAgentByBranchName,
    updateById,
    softDeleteById,
    reinstateById,
    newLastWeek,
    newThisWeek,
    deactivatedThisWeek,
    deactivatedLastWeek,
    editedThisWeek,
    editedLastWeek,

};
