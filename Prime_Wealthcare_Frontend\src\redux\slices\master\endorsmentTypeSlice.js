import { createSlice } from '@reduxjs/toolkit';
import {
    getAllEndorsments,
    getEndorsmentById,
    getEndorsmentByName,
    createEndorsment,
    updateEndorsment,
    deleteEndorsment,
    reinstateEndorsment,
    getFilterEndorsmentData,
} from '../../actions/action';
import { toast } from 'react-toastify'; // Assuming you're using react-toastify

const endorsmentTypeSlice = createSlice({
    name: 'endorsments',
    initialState: {
        endorsments: [],
        endorsment: null,
        status: 'idle',
        error: null,
    },
    extraReducers: (builder) => {
        builder
            // Get All Endorsments
            .addCase(getAllEndorsments.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getAllEndorsments.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.endorsments = action.payload;
            })
            .addCase(getAllEndorsments.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to fetch endorsments');
            })

            // Get Endorsment By ID
            .addCase(getEndorsmentById.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getEndorsmentById.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.endorsment = action.payload;
            })
            .addCase(getEndorsmentById.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to fetch endorsment details');
            })

            // Search Endorsment by Name
            .addCase(getEndorsmentByName.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getEndorsmentByName.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.endorsments = action.payload;
            })
            .addCase(getEndorsmentByName.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to find endorsments');
            })

            // Create Endorsment
            .addCase(createEndorsment.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(createEndorsment.fulfilled, (state, action) => {
                state.endorsments.push(action.payload);
                toast.success('Endorsment created successfully');
            })
            .addCase(createEndorsment.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to create endorsment');
            })

            // Update Endorsment
            .addCase(updateEndorsment.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(updateEndorsment.fulfilled, (state, action) => {
                state.endorsment = action.payload;
                toast.success('Endorsment updated successfully');
            })
            .addCase(updateEndorsment.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to update endorsment');
            })

            // Delete Endorsment
            .addCase(deleteEndorsment.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(deleteEndorsment.fulfilled, (state, action) => {
                state.endorsments = state.endorsments.filter(endorsment => endorsment.id !== action.payload);
                toast.success('Endorsment deleted successfully');
            })
            .addCase(deleteEndorsment.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to delete endorsment');
            })

            // Reinstate Endorsment
            .addCase(reinstateEndorsment.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(reinstateEndorsment.fulfilled, (state, action) => {
                const index = state.endorsments.findIndex(endorsment => endorsment.id === action.payload.id);
                if (index !== -1) {
                    state.endorsments[index] = action.payload;
                }
                toast.success('Endorsment reinstated successfully');
            })
            .addCase(reinstateEndorsment.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to reinstate endorsment');
            })

            // Filtered Results
            .addCase(getFilterEndorsmentData.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getFilterEndorsmentData.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.endorsments = action.payload;
            })
            .addCase(getFilterEndorsmentData.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to filter endorsments');
            });
    },
});

export default endorsmentTypeSlice.reducer;
