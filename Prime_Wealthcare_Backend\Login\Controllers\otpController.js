const crypto = require('crypto');
const { sendEmail } = require('../../services/emailService'); // Assuming you already have email service set up
const knexConfig = require('../../knexfile');
const knex = require('knex')(knexConfig.development);
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const SECRET_KEY = 'your_secret_key';

// Send OTP to the user via email
const sendOTP = async (req, res) => {
    const { email } = req.body; // Only email is required from frontend

    try {
        // Check if there's a recent OTP that's not expired
        const recentOTP = await knex('otp')
            .where({ email })
            .andWhere('expires_at', '>', new Date())
            .first();

        // Optional: Add rate limiting - only allow resend after 1 minute
        if (recentOTP) {
            const timeSinceLastOTP = Date.now() - new Date(recentOTP.created_at).getTime();
            if (timeSinceLastOTP < 60000) { // 60000 ms = 1 minute
                return res.status(429).json({ 
                    message: 'Please wait 1 minute before requesting another OTP',
                    retryAfter: Math.ceil((60000 - timeSinceLastOTP) / 1000) // seconds to wait
                });
            }
        }

        // Delete any existing OTP for this email
        await knex('otp').where({ email }).del();

        // Check if the email exists in agents or employees
        let user = await knex('agents').where({ personal_email: email }).first();
        let userType = 'agent'; // Default userType is 'agent'

        if (!user) {
            // If not found in agents, check employees
            user = await knex('employee_personal_info').where({ personal_email: email }).first();
            userType = 'employee'; // If found in employee table, set userType to 'employee'
        }

        if (!user) {
            // If email is not found in either table, return error
            return res.status(404).json({ message: 'User with this email not found.' });
        }

        // Generate OTP (e.g., a 6-digit code)
        const otp = crypto.randomInt(100000, 999999).toString();

        // Store OTP in DB with expiration time (e.g., 10 minutes from now)
        const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes

        // Create email content
        const emailSubject = 'Your Password Reset OTP';
        const emailText = `Your OTP for password reset is: ${otp}`;
        const emailHtml = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">Password Reset OTP</h2>
                <p>Your OTP for password reset is: <strong style="font-size: 20px;">${otp}</strong></p>
                <p>This OTP will expire in 10 minutes.</p>
            </div>
        `;

        // Store OTP in DB
        await knex('otp').insert({
            otp_code: otp,
            expires_at: otpExpiresAt,
            email,
            user_type: userType,
            created_at: new Date()
        });

        // Send email using your service
        await sendEmail(email, emailSubject, emailText, emailHtml);

        return res.status(200).json({ message: 'OTP sent to your email.' });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

const verifyOTP = async (req, res) => {
  const { otp, email } = req.body;

  try {
    // Fetch the user ID from either agents or employees based on email
    let user = await knex('agents').where({ personal_email: email }).first();
    let userType = 'agent';

    if (!user) {
      user = await knex('employee_personal_info').where({ personal_email: email }).first();
      userType = 'employee';
    }

    if (!user) {
      return res.status(404).json({ message: 'User with this email not found.' });
    }

    const userId = userType === 'agent' ? user.agent_id : user.user_id;

    // Check if the OTP is valid and has not expired
    const otpRecord = await knex('otp')
      .where('otp_code', otp)          // Correct column name: otp_code
      .andWhere('expires_at', '>', new Date())
      .andWhere('email', email)        // Use email to link the OTP to the user
      .first();

    if (!otpRecord) {
      return res.status(400).json({ message: 'Invalid or expired OTP.' });
    }

    // OTP is valid, proceed to the next steps (e.g., password reset)
    return res.status(200).json({ message: 'OTP verified successfully.' });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};


const resetPassword = async (req, res) => {
  const { newPassword, confirmPassword, otp } = req.body;

  // Validate password input
  if (newPassword !== confirmPassword) {
      return res.status(400).json({ message: 'Passwords do not match' });
  }

  if (newPassword.length < 6) {
      return res.status(400).json({ message: 'Password must be at least 6 characters long' });
  }

  try {
      // Find OTP record in the database
      const otpRecord = await knex('otp').where({ otp_code: otp }).first();

      if (!otpRecord) {
          return res.status(400).json({ message: 'Invalid or expired OTP' });
      }

      // Check if OTP is expired
      if (new Date() > new Date(otpRecord.expires_at)) {
          return res.status(400).json({ message: 'OTP has expired' });
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Find the user using the OTP email and user_type
      const email = otpRecord.email; // Assuming OTP contains the email
      let user;

      // Use the user_type stored in OTP record to find the user in the appropriate table
      if (otpRecord.user_type === 'agent') {
          user = await knex('agents').where({ personal_email: email }).first();
      } else if (otpRecord.user_type === 'employee') {
          user = await knex('employee_personal_info').where({ personal_email: email }).first();
      }

      if (!user) {
          return res.status(404).json({ message: 'User not found' });
      }

      // Update the password in the respective table
      if (otpRecord.user_type === 'agent') {
          await knex('agents').where({ personal_email: email }).update({ password: hashedPassword });
      } else if (otpRecord.user_type === 'employee') {
          await knex('employee_personal_info').where({ personal_email: email }).update({ password: hashedPassword });
      }

      // Optionally, delete OTP after successful password reset
      await knex('otp').where({ otp_code: otp }).del(); // Clear the OTP from the table

      return res.status(200).json({ message: 'Password reset successfully' });
  } catch (error) {
      console.error('Error resetting password:', error);
      return res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
    sendOTP,
    verifyOTP,
    resetPassword
};








/* const crypto = require('crypto');
const { sendEmail } = require('../../services/emailService'); // Assuming you already have email service set up
const knexConfig = require('../../knexfile');
const knex = require('knex')(knexConfig.development);
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const SECRET_KEY = 'your_secret_key';

const sendOTP = async (req, res) => {
    const { email } = req.body;

    try {
        // Check if the email exists in agents or employees
        let user = await knex('agents').where({ personal_email: email }).first();
        let userType = 'agent';

        if (!user) {
            user = await knex('employee_personal_info').where({ personal_email: email }).first();
            userType = 'employee';
        }

        if (!user) {
            return res.status(404).json({ message: 'User with this email not found.' });
        }

        // Generate OTP (e.g., a 6-digit code)
        const otp = crypto.randomInt(100000, 999999).toString();

        // Store OTP in DB with expiration time (e.g., 10 minutes from now)
        const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000);
        await knex('password_reset_tokens').insert({
            user_id: userType === 'agent' ? user.agent_id : user.user_id,
            otp,
            expires_at: otpExpiresAt,
            user_type: userType
        });

        // Send OTP via email
        await sendEmail(email, 'Your Password Reset OTP', `Your OTP is ${otp}`);

        return res.status(200).json({ message: 'OTP sent to your email.' });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

const verifyOtp = async (req, res) => {
  const { userId, otp, userType } = req.body;

  try {
      // Check if the OTP is valid and hasn't expired
      const token = await knex('password_reset_tokens')
          .where({ user_id: userId, otp, user_type: userType })
          .andWhere('expires_at', '>', new Date())
          .first();

      if (!token) {
          return res.status(400).json({ message: 'Invalid or expired OTP.' });
      }

      // OTP is valid
      return res.status(200).json({ message: 'OTP verified successfully. You can now reset your password.' });
  } catch (error) {
      return res.status(500).json({ message: error.message });
  }
};



module.exports = { sendOTP,verifyOtp };






 */























/* const crypto = require('crypto');
const { sendEmail } = require('../../services/emailService'); // Assuming you already have email service set up
const knexConfig = require('../../knexfile');
const knex = require('knex')(knexConfig.development);
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const SECRET_KEY = 'your_secret_key';

const SALT_ROUNDS = 10; // Number of salt rounds for bcrypt


// Generate OTP and send to the user's email
const sendOTP = async (req, res) => {
  const { email,userType } = req.body;

  // Check if the user exists in the database (agent or employee)
  try {
    let user;
    if (userType === 'agent') {
      user = await knex('agents').where({ personal_email: email }).first();
    } else if (userType === 'employee') {
      user = await knex('employee_personal_info').where({ personal_email: email }).first();
    }

    if (!user) {
      return res.status(404).json({ message: 'Email not found' });
    }

    // Generate a 6-digit OTP
    const otp = crypto.randomInt(100000, 999999).toString();

    // Save OTP and expiry time (let's say 15 minutes)
    const otpExpiry = Date.now() + 15 * 60 * 1000; // 15 minutes in milliseconds
    await knex('otp').insert({
      email,
      otp_code: otp,  // Use otp_code column
      expires_at: new Date(otpExpiry), // Use expires_at column
     // user_type: userType,
    });

    // Send OTP via email
    const subject = 'Password Reset OTP';
    const html = `<p>Your OTP for resetting the password is <strong>${otp}</strong>. It is valid for 15 minutes.</p>`;
    await sendEmail(email, subject, '', html);

    return res.status(200).json({ message: 'OTP sent successfully' });
  } catch (error) {
    console.error('Error sending OTP:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

const verifyOTP = async (req, res) => {
    const { email, otp } = req.body;

    try {
        // Query the OTP table to find a matching email and OTP code, and check if the OTP is not expired
        const otpRecord = await knex('otp')
            .where({ email: email, otp_code: otp })
            .andWhere('expires_at', '>', new Date())
            .first(); // Use .first() to return a single record or null

        if (!otpRecord) {
            return res.status(400).json({ message: 'Invalid or expired OTP' });
        }

       
        // OTP is valid, generate JWT token containing the email
        const token = jwt.sign({ email: email, userType: 'agent' }, SECRET_KEY, { expiresIn: '15m' }); // Token valid for 15 mins

        //return res.status(200).json({ message: 'OTP verified successfully', token });
         // Return the token to the client
         return res.status(200).json({
            message: 'OTP verified successfully',
            token: `Bearer ${token}`  // Token sent in the response
        });

    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal server error' });
    }
};

const resetPassword = async (req, res) => {
    const { newPassword, confirmPassword } = req.body;
    const token = req.headers.authorization.split(' ')[1]; // Extract token from Bearer header
  
    // Validate password input
    if (newPassword !== confirmPassword) {
      return res.status(400).json({ message: 'Passwords do not match' });
    }
  
    if (newPassword.length < 6) {
      return res.status(400).json({ message: 'Password must be at least 6 characters long' });
    }
  
    try {
      // Verify the token and extract the email
      const decoded = jwt.verify(token, SECRET_KEY);
      const email = decoded.email;
  
      // Check user type (you could send this in the token as well if needed)
      let user;
      if (decoded.userType === 'agent') {
        user = await knex('agents').where({ personal_email: email }).first();
      } else if (decoded.userType === 'employee') {
        user = await knex('employee_personal_info').where({ personal_email: email }).first();
      }
  
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
  
      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);
  
      // Update password in the correct table
      if (decoded.userType === 'agent') {
        await knex('agents').where({ personal_email: email }).update({ password: hashedPassword });
      } else if (decoded.userType === 'employee') {
        await knex('employee_personal_info').where({ personal_email: email }).update({ password: hashedPassword });
      }
  
      // Delete OTP after successful reset
      await knex('otp').where({ email }).del();
  
      return res.status(200).json({ message: 'Password reset successfully' });
    } catch (error) {
      console.error('Error resetting password:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };

  module.exports = {
    sendOTP,
    verifyOTP,
    resetPassword
  };

 */