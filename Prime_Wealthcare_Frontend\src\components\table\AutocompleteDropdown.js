import React from 'react';
import {
    Autocomplete,
    TextField,
    FormControl,
    FormHelperText,
} from '@mui/material';

const AutocompleteDropdown = ({
    label,
    helperText,
    required,
    options,
    value,
    onChange,
    fullWidth = false,
    width = 'auto',
    range = null,
    info,
    ...rest
}) => {
    // Convert range to options if range is provided
    const getOptions = () => {
        if (range && Array.isArray(range) && range.length >= 2) {
            const [start, end, step = 1] = range;
            const rangeArray = [];
            for (let i = start; i <= end; i += step) {
                rangeArray.push({ label: i.toString(), value: i });
            }
            return rangeArray;
        }
        return options;
    };


    return (
        <FormControl
            fullWidth={fullWidth}
            sx={{ width: fullWidth ? '100%' : width }}
        >
            <Autocomplete
                value={value ? getOptions().find(option => option.value === value) : null}
                onChange={(event, newValue) => {
                    if (newValue) {
                        if (newValue.value) {
                            onChange({ name: rest.name, value: newValue.value });
                        }
                    } else {
                        onChange({ name: rest.name, value: '' })
                    }
                }}
                options={getOptions()}
                getOptionLabel={(option) => option.label || ''}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                borderLeft: required ? '3px solid red !important' : 'none',
                                borderRadius: '8px',
                                '&.Mui-disabled': {
                                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                },
                            },
                        }}
                    />
                )}
                {...rest}
            />
            {helperText && <FormHelperText sx={{ color: 'red' }}>{helperText}</FormHelperText>}
            {info && <FormHelperText sx={{ color: 'green' }}>{info}</FormHelperText>}
        </FormControl>
    );
};

export default AutocompleteDropdown;
