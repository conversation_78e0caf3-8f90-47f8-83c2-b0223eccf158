import { createSlice } from '@reduxjs/toolkit';
import {
    getAllProducts,
    getProductById,
    getProductByName,
    createProduct,
    updateProduct,
    deleteProduct,
    reinstateProduct,
    getFilterData,
} from '../../actions/action';
import { toast } from 'react-toastify';
const mainProductSlice = createSlice({
    name: 'products',
    initialState: {
        products: [],
        currentProduct: null,
        status: 'idle',
        error: null,
    },
    extraReducers: (builder) => {
        builder
            // Get All Products
            .addCase(getAllProducts.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getAllProducts.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.products = action.payload;
            })
            .addCase(getAllProducts.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to load main products');
            })

            // Get Product By ID
            .addCase(getProductById.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getProductById.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.currentProduct = action.payload;
            })
            .addCase(getProductById.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to load main product details');
            })

            // Search Product by Name
            .addCase(getProductByName.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getProductByName.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.products = action.payload;
            })
            .addCase(getProductByName.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Main products not found');
            })

            // Create Product
            .addCase(createProduct.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(createProduct.fulfilled, (state, action) => {
                state.products.push(action.payload);
                toast.success('Main product created successfully');
            })
            .addCase(createProduct.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to create main product');
            })

            // Update Product
            .addCase(updateProduct.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(updateProduct.fulfilled, (state, action) => {
                const index = state.products.findIndex(product => product.id === action.payload.id);
                if (index !== -1) {
                    state.products[index] = action.payload;
                }
                toast.success('Main product updated successfully');
            })
            .addCase(updateProduct.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to update main product');
            })

            // Delete Product
            .addCase(deleteProduct.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(deleteProduct.fulfilled, (state, action) => {
                state.products = state.products.filter(product => product.id !== action.payload);
                toast.success('Main product deleted successfully');
            })
            .addCase(deleteProduct.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to delete product');
            })

            // Reinstate Product
            .addCase(reinstateProduct.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(reinstateProduct.fulfilled, (state, action) => {
                const index = state.products.findIndex(product => product.id === action.payload.id);
                if (index !== -1) {
                    state.products[index] = action.payload;
                    toast.success('Product reinstated successfully');
                }
            })
            .addCase(reinstateProduct.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to reinstate product');
            })

            // Filtered Results
            .addCase(getFilterData.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getFilterData.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.products = action.payload;
            })
            .addCase(getFilterData.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to apply filter');
            });
    },
});

export default mainProductSlice.reducer;
