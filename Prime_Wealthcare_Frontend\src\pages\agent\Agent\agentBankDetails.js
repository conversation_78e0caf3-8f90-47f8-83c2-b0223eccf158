import React, { useEffect, useState } from 'react';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import Dropdown from '../../../components/table/DropDown';
import CheckboxWithLabel from '../../../components/CheckboxWithLabel';
import CustomSection from '../../../components/CustomSection';
import DeleteIcon from '@mui/icons-material/Delete';
import { IconButton } from '@mui/material';
import { Typography } from '@mui/material';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAllBanks, createAgentBankDetails, updateAgentBankDetails, getAgentBankDetailsById, deleteAgentFirstBankDetails, deleteAgentSecondBankDetails } from '../../../redux/actions/action';
import { toast } from 'react-toastify';

// Add helper functions at the top
const hasFirstBankDetails = (data) => {
    return !!(data.account_holder_name || data.bank_name || data.account_number);
};

const hasSecondBankDetails = (data) => {
    return !!(data.account_holder_name_2 || data.bank_name_2 || data.account_number_2);
};

const AgentBankDetails = () => {
    const { id, view } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();

    const agentBankState = useSelector((state) => state.agentBank) || {};
    const { loading = false, error = null } = agentBankState;
    const isEditMode = location.pathname.includes('edit');
    const isViewMode = location.pathname.includes('view');
    const [formData, setFormData] = useState({
        account_holder_name: '',
        bank_name: '',
        account_number: '',
        IFSC_code: '',
        account_Type: '',
        status: true,
        account_holder_name_2: '',
        bank_name_2: '',
        account_number_2: '',
        IFSC_code_2: '',
        account_Type_2: '',
        canceled_cheque_2: '',
        branch_name: '',
        branch_name_2: '',
        is_active_bank: 'first',
    });

    const [formErrors, setFormErrors] = useState({
        account_holder_name: '',
        bank_name: '',
        account_number: '',
        IFSC_code: '',
        account_Type: '',
        status: true,
        account_holder_name_2: '',
        bank_name_2: '',
        account_number_2: '',
        IFSC_code_2: '',
        account_Type_2: '',
        canceled_cheque_2: '',
    });

    const [banks, setBanks] = useState([]);

    const validateForm = () => {
        let errors = {};
        let isValid = true;

        if (!formData.account_holder_name) {
            errors.account_holder_name = "Account holder name is required.";
            isValid = false;
        }

        if (!formData.bank_name) {
            errors.bank_name = "Bank name is required.";
            isValid = false;
        }

        if (!formData.account_number) {
            errors.account_number = "Account number is required.";
            isValid = false;
        }

        if (!formData.IFSC_code) {
            errors.IFSC_code = "IFSC code is required.";
            isValid = false;
        }

        if (!formData.account_Type) {
            errors.account_Type = "Account type is required.";
            isValid = false;
        }

        if (!formData.branch_name) {
            errors.branch_name = "Branch name is required.";
            isValid = false;
        }

        const isSecondBankDetailsFilled = Object.values({
            account_holder_name_2: formData.account_holder_name_2,
            bank_name_2: formData.bank_name_2,
            branch_name_2: formData.branch_name_2,
            account_number_2: formData.account_number_2,
            IFSC_code_2: formData.IFSC_code_2,
            account_Type_2: formData.account_Type_2,
        }).some(field => field);

        if (isSecondBankDetailsFilled) {
            if (!formData.account_holder_name_2) {
                errors.account_holder_name_2 = "Second account holder name is required.";
                isValid = false;
            }
            if (!formData.bank_name_2) {
                errors.bank_name_2 = "Second bank name is required.";
                isValid = false;
            }
            if (!formData.branch_name_2) {
                errors.branch_name_2 = "Second branch name is required.";
                isValid = false;
            }
            if (!formData.account_number_2) {
                errors.account_number_2 = "Second account number is required.";
                isValid = false;
            }
            if (!formData.IFSC_code_2) {
                errors.IFSC_code_2 = "Second IFSC code is required.";
                isValid = false;
            }
            if (!formData.account_Type_2) {
                errors.account_Type_2 = "Second account type is required.";
                isValid = false;
            }
        }
        setFormErrors(errors);
        return isValid;
    }

    useEffect(() => {
        if (id && (isEditMode || isViewMode)) {
            dispatch(getAgentBankDetailsById(id)).then((action) => {
                if (action.payload) {
                    const bank = banks.find(bank => bank.id === action.payload.bank_name);
                    const bankName = bank ? bank.label_name : '';
                    setFormData({
                        account_holder_name: action.payload.account_holder_name || '',
                        bank_name: action.payload.bank_name || '',
                        branch_name: action.payload.branch_name || '',
                        account_number: action.payload.account_number || '',
                        IFSC_code: action.payload.IFSC_code || '',
                        account_Type: action.payload.account_Type || '',
                        status: action.payload.status || true,
                        account_holder_name_2: action.payload.account_holder_name_2 || '',
                        bank_name_2: action.payload.bank_name_2 || '',
                        branch_name_2: action.payload.branch_name_2 || '',
                        account_number_2: action.payload.account_number_2 || '',
                        IFSC_code_2: action.payload.IFSC_code_2 || '',
                        account_Type_2: action.payload.account_Type_2 || '',
                        canceled_cheque_2: action.payload.canceled_cheque_2 || '',
                        is_active_bank: action.payload.is_active_bank || '',
                    });
                }
            });
        }
    }, [id, isEditMode, dispatch, banks]);

    useEffect(() => {
        dispatch(fetchAllBanks()).then((action) => {
            if (action.payload) {
                setBanks(action.payload);
            }
        });
    }, [dispatch]);

    const handleChange = (e) => {
        const { name, value } = e.target;

        if (name === 'account_holder_name' || name === 'account_holder_name_2' || name === 'branch_name' || name === 'branch_name_2') {
            const alphabeticValue = value.replace(/[^a-zA-Z\s]/g, '').toUpperCase();
            setFormData((prevData) => ({
                ...prevData,
                [name]: alphabeticValue,
            }));
        } else if (name === 'account_number' || name === 'account_number_2' || name === 'IFSC_code' || name === 'IFSC_code_2') {
            const alphanumericValue = value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
            setFormData((prevData) => ({
                ...prevData,
                [name]: alphanumericValue,
            }));
        } else {
            setFormData((prevData) => ({
                ...prevData,
                [name]: value,
            }));
        }
    }

    const handleSave = () => {
        const isValid = validateForm();
        if (!isValid) return;

        if (!formData.is_active_bank) {
            toast.error('Please select an active bank account');
            return;
        }

        if (formData.is_active_bank === 'first' && !hasFirstBankDetails(formData)) {
            toast.error('First bank details are incomplete');
            return;
        }

        if (formData.is_active_bank === 'second' && !hasSecondBankDetails(formData)) {
            toast.error('Second bank details are incomplete');
            return;
        }

        const dataToSave = {
            ...formData,
            agent_id: id,
            bank_name_2: formData.bank_name_2 || null,
            account_holder_name_2: formData.account_holder_name_2 || null,
            branch_name_2: formData.branch_name_2 || null,
            account_number_2: formData.account_number_2 || null,
            IFSC_code_2: formData.IFSC_code_2 || null,
            account_Type_2: formData.account_Type_2 || null
        };

        if (isEditMode) {
            dispatch(updateAgentBankDetails({ id, data: dataToSave }))
                .then(() => {
                    toast.success('Agent Bank Details updated successfully!');
                    navigate(`/dashboard/agent-master-overview/${id}`);
                })
                .catch((error) => {
                    toast.error('Failed to update Agent Bank Details.');
                });
        } else {
            dispatch(createAgentBankDetails(dataToSave))
                .then(() => {
                    toast.success('Agent Bank Details created successfully!');
                    navigate(`/dashboard/agent-master-overview/${id}`);
                })
                .catch((error) => {
                    toast.error('Failed to create Agent Bank Details.');
                });
        }
    }

    const handleCancel = () => {
        navigate('/dashboard/agent-master');
    };

    const handleEdit = () => {
    };

    const handleDelete = async (accountNumber) => {
        const confirmDelete = window.confirm("Are you sure you want to delete this bank detail?");
        if (!confirmDelete) return;

        try {
            const deleteAction = accountNumber === 1 ? deleteAgentFirstBankDetails(id) : deleteAgentSecondBankDetails(id);
            const hasOtherBank = accountNumber === 1
                ? hasSecondBankDetails(formData)
                : hasFirstBankDetails(formData);

            await dispatch(deleteAction).unwrap();

            if (!hasOtherBank) {
                const emptyFormState = {
                    account_holder_name: '',
                    bank_name: '',
                    account_number: '',
                    branch_name: '',
                    IFSC_code: '',
                    account_Type: '',
                    account_holder_name_2: '',
                    bank_name_2: '',
                    account_number_2: '',
                    branch_name_2: '',
                    IFSC_code_2: '',
                    account_Type_2: '',
                    is_active_bank: ''
                };
                setFormData(emptyFormState);
                navigate(`/dashboard/agent-master-overview/${id}`);
            } else {
                setFormData(prevData => ({
                    ...prevData,
                    ...(accountNumber === 1 ? {
                        account_holder_name: '',
                        bank_name: '',
                        account_number: '',
                        branch_name: '',
                        IFSC_code: '',
                        account_Type: '',
                        is_active_bank: 'second'
                    } : {
                        account_holder_name_2: '',
                        bank_name_2: '',
                        account_number_2: '',
                        branch_name_2: '',
                        IFSC_code_2: '',
                        account_Type_2: '',
                        is_active_bank: 'first'
                    })
                }));
            }

            toast.success(`Bank details deleted successfully!`);
        } catch (error) {
            console.error('Delete error:', error);
            toast.error('Failed to delete bank details.');
        }
    };

    return (
        <Box sx={{ paddingLeft: '40px', paddingRight: '40px', paddingBottom: '40px' }}>
            <form>
                <Grid container style={{ display: 'flex' }}>
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end', paddingBottom: '20px' }}>
                        <>
                            {!isViewMode && (
                                <Button onClick={handleSave} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}>
                                    Save
                                </Button>
                            )}
                        </>
                        <Button onClick={handleCancel} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }} >
                            Cancel
                        </Button>
                    </Grid>
                </Grid>
                <Grid sx={{ display: 'flex', width: '100%', }}>
                    <Box
                        sx={{
                            width: '100%',
                            backgroundColor: '#f0f0f0',
                            display: "flex",
                            alignItems: "center",
                            padding: "10px",
                            borderRadius: "4px",
                            height: "60px",
                            fontSize: "18px",
                            fontStyle: "normal",
                            fontWeight: "700",
                            lineHeight: "27px",
                            color: '#4C5157',
                            borderRadius: '.3rem'
                        }}
                    >
                        <input
                            style={{
                                marginRight: '10px',
                                accentColor: '#528a7e',
                                transform: 'scale(1.5)',
                                opacity: hasFirstBankDetails(formData) ? 1 : 0.5
                            }}
                            type="radio"
                            name="is_active_bank"
                            value="first"
                            disabled={isViewMode || !hasFirstBankDetails(formData)}
                            checked={formData.is_active_bank === 'first'}
                            onChange={() => setFormData({ ...formData, is_active_bank: 'first' })}
                        />
                        <h5>Bank Details</h5>
                        {formData.account_holder_name && (
                            <IconButton aria-label="delete" onClick={() => handleDelete(1)} disabled={isViewMode}>
                                <DeleteIcon />
                            </IconButton>
                        )}
                        <div
                            style={{
                                height: "100vh",
                                display: "flex",
                                alignItems: "center",
                            }}
                        ></div>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Account Holder Full Name"
                            name="account_holder_name"
                            value={formData.account_holder_name}
                            onChange={handleChange}
                            error={!!formErrors.account_holder_name}
                            helperText={formErrors.account_holder_name}
                            isRequired
                            disabled={isViewMode}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <Dropdown
                            label="Bank Name"
                            name="bank_name"
                            options={banks.map(bank => ({ label: bank.label_name, value: bank.id }))}
                            value={formData.bank_name}
                            onChange={handleChange}
                            fullWidth
                            required
                            error={!!formErrors.bank_name}
                            helperText={formErrors.bank_name}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='branch_name'
                            label='Branch Name'
                            fullWidth
                            isRequired
                            value={formData.branch_name}
                            error={!!formErrors.branch_name}
                            helperText={formErrors.branch_name}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='account_number'
                            label='Account Number'
                            fullWidth
                            isRequired
                            value={formData.account_number}
                            error={!!formErrors.account_number}
                            helperText={formErrors.account_number}
                            disabled={isViewMode}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='IFSC_code'
                            label='IFSC Code'
                            fullWidth
                            isRequired
                            value={formData.IFSC_code}
                            error={!!formErrors.IFSC_code}
                            helperText={formErrors.IFSC_code}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Account Type"
                            name="account_Type"
                            options={[
                                { label: 'Savings', value: 'savings' },
                                { label: 'Current', value: 'current' }
                            ]}
                            value={formData.account_Type}
                            onChange={handleChange}
                            fullWidth
                            required
                            error={!!formErrors.account_Type}
                            helperText={formErrors.account_Type}
                            disabled={isViewMode}
                        />
                    </Grid>
                </Grid>
                <Grid sx={{ display: 'flex', width: '100%', }}>
                    <Box
                        sx={{
                            width: '100%',
                            backgroundColor: '#f0f0f0',
                            display: "flex",
                            alignItems: "center",
                            padding: "10px",
                            borderRadius: "4px",
                            height: "60px",
                            fontSize: "18px",
                            fontStyle: "normal",
                            fontWeight: "700",
                            lineHeight: "27px",
                            color: '#4C5157',
                            borderRadius: '.3rem'
                        }}
                    >
                        <input
                            style={{
                                marginRight: '10px',
                                accentColor: '#528a7e',
                                transform: 'scale(1.5)',
                                opacity: hasSecondBankDetails(formData) ? 1 : 0.5
                            }}
                            type="radio"
                            name="is_active_bank"
                            value="second"
                            disabled={isViewMode || !hasSecondBankDetails(formData)}
                            checked={formData.is_active_bank === 'second'}
                            onChange={() => setFormData({ ...formData, is_active_bank: 'second' })}
                        />
                        <h5>Bank Details</h5>
                        {formData.account_holder_name_2 && (
                            <IconButton aria-label="delete" onClick={() => handleDelete(2)} disabled={isViewMode}>
                                <DeleteIcon />
                            </IconButton>
                        )}
                        <div
                            style={{
                                height: "100vh",
                                display: "flex",
                                alignItems: "center",
                            }}
                        ></div>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Account Holder Full Name"
                            name="account_holder_name_2"
                            value={formData.account_holder_name_2}
                            onChange={handleChange}
                            error={!!formErrors.account_holder_name_2}
                            helperText={formErrors.account_holder_name_2}
                            isRequired
                            disabled={isViewMode}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <Dropdown
                            label="Bank Name"
                            name="bank_name_2"
                            options={banks.map(bank => ({ label: bank.label_name, value: bank.id }))}
                            value={formData.bank_name_2}
                            onChange={handleChange}
                            fullWidth
                            required
                            error={!!formErrors.bank_name_2}
                            helperText={formErrors.bank_name_2}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='branch_name_2'
                            label='Branch Name'
                            fullWidth
                            isRequired
                            value={formData.branch_name_2}
                            error={!!formErrors.branch_name_2}
                            helperText={formErrors.branch_name_2}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='account_number_2'
                            label='Account Number'
                            fullWidth
                            isRequired
                            value={formData.account_number_2}
                            error={!!formErrors.account_number_2}
                            helperText={formErrors.account_number_2}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='IFSC_code_2'
                            label='IFSC Code'
                            fullWidth
                            isRequired
                            value={formData.IFSC_code_2}
                            error={!!formErrors.IFSC_code_2}
                            helperText={formErrors.IFSC_code_2}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Account Type"
                            name="account_Type_2"
                            options={[
                                { label: 'Savings', value: 'savings' },
                                { label: 'Current', value: 'current' }
                            ]}
                            value={formData.account_Type_2}
                            onChange={handleChange}
                            fullWidth
                            required
                            error={!!formErrors.account_Type_2}
                            helperText={formErrors.account_Type_2}
                            disabled={isViewMode}
                        />
                    </Grid>
                </Grid>
                {loading && <p>Loading...</p>}
                {error && <p style={{ color: 'red' }}>{error}</p>}
            </form>
        </Box>
    );
};

export default AgentBankDetails;