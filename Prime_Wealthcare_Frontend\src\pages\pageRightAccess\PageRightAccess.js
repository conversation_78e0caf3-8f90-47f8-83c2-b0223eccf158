import React, { useState, useEffect } from 'react';
import {
    <PERSON>rid,
    Box,
    Typography,
    FormControlLabel,
    Checkbox,
    Button,
    FormControl,
    InputLabel,
    Select,
    MenuItem, 
} from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchUserAccessRights, createPageRights, fetchAllUsers } from '../../redux/actions/action';
import ModuleName from '../../components/table/ModuleName';
import AutocompleteDropdown from '../../components/table/AutocompleteDropdown';
import { toast } from 'react-toastify';

function PageRightAccess() {
    const [selectedUser, setSelectedUser] = useState('');
    const [permissions, setPermissions] = useState({});
    const accessRights = useSelector((state) => state.auth.accessRights);
    const dispatch = useDispatch();
    const users = useSelector((state) => state.users.users);
    const loading = useSelector((state) => state.users.loading);
    const navigate = useNavigate();

    // Add console logs for initial render
    useEffect(() => {
        
        const fetchUsers = async () => {
            try {
                await dispatch(fetchAllUsers());
            } catch (error) {
                console.error('Error fetching users:', error);
            }
        };

        fetchUsers();
    }, [dispatch]);

    useEffect(() => {
        if (accessRights) {
            const initialPermissions = {};
            accessRights.forEach(right => {
                const key = `${right.module_name}-${right.page_name}`;
                initialPermissions[key] = {
                    can_add: right.can_add || false,
                    can_edit: right.can_edit || false,
                    can_view: right.can_view || false,
                    can_delete: right.can_delete || false
                };
            });
            setPermissions(initialPermissions);
        }
    }, [accessRights]);

    // Clear permissions when component mounts
    useEffect(() => {
        setPermissions({});
    }, []);

    const handlePermissionChange = (module, page, permission) => {
        const key = `${module}-${page}`;
        setPermissions(prev => ({
            ...prev,
            [key]: {
                ...(prev[key] || {}),
                [permission]: !prev[key]?.[permission]
            }
        }));
    };

    const handleSave = async () => {
        
        if (!selectedUser) {
            console.error('No user selected');
            return;
        }

        try {
            // Format the data according to your backend expectations
            const modulesToSave = sections.map(section => {
                const sectionPermissions = {};
                section.items.forEach(item => {
                    const key = `${section.title}-${item}`;
                    sectionPermissions[item] = {
                        can_add: permissions[key]?.can_add || false,
                        can_edit: permissions[key]?.can_edit || false,
                        can_view: permissions[key]?.can_view || false,
                        can_delete: permissions[key]?.can_delete || false
                    };
                });

                return {
                    title: section.title,
                    pages: section.items.map(item => ({
                        page_name: item,
                        permissions: {
                            add: permissions[`${section.title}-${item}`]?.can_add || false,
                            edit: permissions[`${section.title}-${item}`]?.can_edit || false,
                            view: permissions[`${section.title}-${item}`]?.can_view || false,
                            delete: permissions[`${section.title}-${item}`]?.can_delete || false
                        }
                    }))
                };
            });


            const payload = {
                user_id: selectedUser,
                user_type: users.find(u => u.user_id === selectedUser)?.user_type,
                modules: modulesToSave
            };


            const response = await dispatch(createPageRights(payload)).unwrap();

            if (response.success) {
                // Show success message
                toast.success('Permissions saved successfully!');
            } else {
                // Show error message
                toast.error('Failed to save permissions!');
            }
        } catch (error) {
            console.error('Error saving permissions:', error);
            alert('Error saving permissions: ' + (error.message || 'Unknown error'));
        }
    };

    const handleCancel = () => {
        navigate('/crm-dashboard');
    };

    const handleAskPermissions = () => {
        // Set all permissions to true
        const newPermissions = {};
        sections.forEach(section => {
            section.items.forEach(item => {
                const key = `${section.title}-${item}`;
                newPermissions[key] = {
                    can_add: true,
                    can_edit: true,
                    can_view: true,
                    can_delete: section.title !== 'Customer' && section.title !== 'Quotation' && section.title !== 'Quick Quotation' && section.title !== 'Proposal'
                };
            });
        });
        setPermissions(newPermissions);
    };

    const handleRemoveAll = () => {
        // Set all permissions to false
        const newPermissions = {};
        sections.forEach(section => {
            section.items.forEach(item => {
                const key = `${section.title}-${item}`;
                newPermissions[key] = {
                    can_add: false,
                    can_edit: false,
                    can_view: false,
                    can_delete: false
                };
            });
        });
        setPermissions(newPermissions);
    };

    const sections = [
        {
            title: 'Dashboard',
            items: ['CRM Dashboard', 'HR Dashboard'],
        },
        {
            title: 'Master',
            items: ['Area Master','Area Form', 'Insurance Company','Insurance Company Form', 
                'IMF Branch','IMF Branch Form','Insurance Branch','Insurance Branch Form',
                'Main Product','Main Product Form','Product Master','Product Master Form',
                'Sub Product','Sub Product Form','Network Master','Network Form',
                'Disease Master','Disease Master Form', 'Role Management', 'Role List Form',
                'Endorsement Type','Endorsement Type Form','Page Right Access',]
        },
        {
            title: 'User Management',
            items: [
                'Employee', // New subtitle
                'Employee Personal Information',
                'Employee Overview',
                'Employee Address',
                'Salary Form',
                'Employee Salary Details',
                'Employee Bank Details',
                'Employee Bank Fotm'
                
            ],
        },
        {
          'title':'Agent',
          'items':[
            'Agent Master',
            'Agent Personal Information',
            'Agent Overview',
            'Agent Address',
            'Agent Bank Details',
            'Agent Bank Form',
            ]
          
        },
        {
          'title':'Customer',
          'items':[
            'Customer Master',
            'Customer Personal Information',
            'Customer Address',
            'Customer Member Information',
            'Customer Grouping',
            'Documents',
            'Follow Up',
            'Quotations',
            'Proposal'
          ]
        },
        {
          'title':'Quotation',
          'items':[
         'Quotation List',
         'Quotation Create',
         'Quotation Overview',
          ]
        },
        {
          'title':'Quick Quotation',
          'items':[
            'Quick Quotation Create',
            'Quick Quotation Overview'
          ]
        },
        {
          'title':'Proposal',
          'items':[
            'Proposal Create', 
            'Proposal List',
            'Payment Form',
            'CKYC Form',
            'Renewal Create',
            'Roll Over',
            'Migration'
          ] 
        },
        /* {
            title: 'Master',
            items: ['Page Right Access'],
        }, */
        {
            'title': 'Task Management',
            'items': [
                'Task Board',
                'Employee Tasks'   
                    ]
        },
        {
            'title': 'Renewals',
            'items': [
                'Generate Renewals',
                'Renew Retention'
            ]
        },
        {
            'title': 'Reports',
            'items': [
                'Business Report',
                'Loan Report'  // Note: I'm using "Loan Report" to differentiate from "Loan" module
            ]
        },
        {
            'title': 'Loan',
            'items': [
                'Employee Loan',
                'Employee Loan Form', // Create/Edit form
                'Employee Loan EMI', // EMI details page
                'Agent Loan', // List page
                'Agent Loan Form', // Create/Edit form
                'Agent Loan EMI', // EMI details page
                ]
        },
        {
            'title': 'Commission',
            'items': [
                'Commission Master',
                'Commission Rate',
                'Generate Commission',
                'Commission Report'
            ]
        },
        {
            'title': 'Claim',
            'items': [
                'Generate Claim',
                'Pending Claims',
                'Rejected Claims',
                'Claim Reports'
            ]
        }
    ];

    const hasAccess = (module, page, permission) => {
        const key = `${module}-${page}`;
        return permissions[key]?.[permission] || false;
    };

    // Modified handleUserChange
    const handleUserChange = (event) => {
        const userId = event.target.value;
        setSelectedUser(userId);
        
        // Clear existing permissions first
        setPermissions({});
        
        if (userId) {
            dispatch(fetchUserAccessRights(userId))
                .unwrap()
                .then(response => {
                    if (response.success && response.data) {
                        const initialPermissions = {};
                        response.data.forEach(right => {
                            const key = `${right.module_name}-${right.page_name}`;
                            initialPermissions[key] = {
                                can_add: Boolean(right.can_add),
                                can_edit: Boolean(right.can_edit),
                                can_view: Boolean(right.can_view),
                                can_delete: Boolean(right.can_delete)
                            };
                        });
                        setPermissions(initialPermissions);
                    }
                })
                .catch(error => {
                    console.error('Error fetching user rights:', error);
                });
        }
    };

    // Disable checkboxes if no user is selected
    const isCheckboxDisabled = !selectedUser;

    return (
        <form>
            <Grid container spacing={2}>
                {/* Header Section - Make Sticky */}
                <Grid 
                    item 
                    xs={12} 
                    sx={{
                        position: 'sticky',
                        top: 0,
                        zIndex: 1000,
                        backgroundColor: '#fff',
                    }}
                >
                    <Grid container spacing={2}>
                        <Grid item xs={8} sx={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{
                                    width: '30px',
                                    marginLeft:'14px',
                                    backgroundColor: 'green',
                                }}
                            />
                            <ModuleName moduleName="Page Rights" pageName="Create" />
                        </Grid>
                        <Grid
                            item
                            xs={4}
                            sx={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                alignItems: 'center',
                                gap: '16px',
                            }}
                        >
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{
                                    maxWidth: '100px',
                                    width: '100%',
                                    color: 'green',
                                    borderColor: 'green',
                                    textTransform: 'none',
                                }}
                                onClick={handleSave}
                            >
                                Save
                            </Button>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{
                                    maxWidth: '100px',
                                    width: '100%',
                                    mx: 1.5,
                                    color: 'red',
                                    borderColor: 'red',
                                    textTransform: 'none',
                                }}
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>

                {/* Access Information Section - Make Sticky */}
                <Grid 
                    item 
                    xs={12} 
                    sx={{
                        position: 'sticky',
                        top: '70px', // Adjust based on the height of the header
                        zIndex: 999,
                        backgroundColor: '#fff',
                    }}
                >
                    <Box
                        sx={{
                            backgroundColor: '#E7E7E7',
                            borderWidth: '1px 0px',
                            borderStyle: 'solid',
                            borderColor: '#B2BAC2',
                            padding: '15px 50px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <Typography variant="h6" sx={{ fontWeight: '' }}>
                           Page Right Access 
                        </Typography>
                        <Box>
                            <Button
                                variant="text"
                                color="success"
                                onClick={handleAskPermissions}
                                sx={{ textTransform: 'none', marginRight: '16px' }}
                            >
                                Ask Permissions
                            </Button>
                            <Button
                                variant="text"
                                color="error"
                                onClick={handleRemoveAll}
                                sx={{ textTransform: 'none' }}
                            >
                                Remove All
                            </Button>
                        </Box>
                    </Box>
                </Grid>

                {/* Modified Dropdown - Using AutocompleteDropdown */}
                <Grid item xs={12} sx={{ marginLeft: 4, marginTop: 1 }}>
                    <AutocompleteDropdown
                        label="Select User"
                        name="user"
                        options={users
                            .filter(user => user.status === 1) // Only show active users
                            .map(user => ({
                                label: `${user.name} (${user.user_id})`,
                                value: user.user_id
                            }))}
                        value={selectedUser}
                        onChange={(event) => {
                            const userId = event.value;
                            setSelectedUser(userId);
                            
                            // Clear existing permissions first
                            setPermissions({});
                            
                            if (userId) {
                                dispatch(fetchUserAccessRights(userId))
                                    .unwrap()
                                    .then(response => {
                                        if (response.success && response.data) {
                                            const initialPermissions = {};
                                            response.data.forEach(right => {
                                                const key = `${right.module_name}-${right.page_name}`;
                                                initialPermissions[key] = {
                                                    can_add: Boolean(right.can_add),
                                                    can_edit: Boolean(right.can_edit),
                                                    can_view: Boolean(right.can_view),
                                                    can_delete: Boolean(right.can_delete)
                                                };
                                            });
                                            setPermissions(initialPermissions);
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error fetching user rights:', error);
                                    });
                            }
                        }}
                        disabled={loading}
                        //fullWidth
                        width="350px"
                    />
                </Grid>

                  {/* Dynamic Sections */}
                  <Grid item xs={12} sx={{margin:4}}>
                    {sections.map((section, sectionIndex) => (
                        <Box key={sectionIndex} sx={{ marginBottom: '30px' }}>
                            <Grid container spacing={2} alignItems="center">
                                {/* Section Header */}
                                <Grid item xs={4}>
                                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                                        {section.title}
                                    </Typography>
                                </Grid>
                                <Grid item xs={8}>
                                    <Box sx={{ display: 'flex', gap: '3rem' }}>
                                        {<Typography variant="body1">Add</Typography>}
                                        {section.title !== 'Quick Quotation' && section.title !== 'Quotation' && <Typography variant="body1">Edit</Typography>}
                                        <Typography variant="body1">View</Typography>
                                        {section.title !== 'Customer' && section.title !== 'Quotation' && section.title !== 'Quick Quotation' && section.title !== 'Proposal' && section.title !== 'Page Rights' && <Typography variant="body1">Delete</Typography>}
                                    </Box>
                                </Grid>
                            </Grid>

                            {/* Items with Checkboxes */}
                            {section.items.map((item, itemIndex) => (
                                <Grid
                                    container
                                    spacing={2}
                                    alignItems="center"
                                    key={itemIndex}
                                    sx={{
                                        padding: '0.5rem 0',
                                        borderBottom: '1px solid #ddd',
                                        '&:last-child': { borderBottom: 'none' },
                                    }}
                                >
                                    <Grid item xs={4}>
                                        <Typography variant="body1">{item}</Typography>
                                    </Grid>
                                    <Grid item xs={8}>
                                        <Box sx={{ display: 'flex', gap: '2rem' }}>
                                         { (
                                            <FormControlLabel
                                                control={
                                                    <Checkbox
                                                        checked={hasAccess(section.title, item, 'can_add')}
                                                        onChange={() => handlePermissionChange(section.title, item, 'can_add')}
                                                    />
                                                }
                                                label=""
                                            />
                                            )}
                                            {section.title !== 'Quick Quotation' && section.title !== 'Quotation' && (
                                            <FormControlLabel
                                                control={
                                                    <Checkbox
                                                        checked={hasAccess(section.title, item, 'can_edit')}
                                                        onChange={() => handlePermissionChange(section.title, item, 'can_edit')}
                                                    />
                                                }
                                                label=""
                                            />
                                            )}
                                            <FormControlLabel
                                                control={
                                                    <Checkbox
                                                        checked={hasAccess(section.title, item, 'can_view')}
                                                        onChange={() => handlePermissionChange(section.title, item, 'can_view')}
                                                    />
                                                }
                                                label=""
                                            />
                                            {section.title !== 'Customer' && section.title !== 'Quotation' && section.title !== 'Quick Quotation' &&  section.title !== 'Proposal' && section.title !== 'Page Rights' && (
                                                <FormControlLabel
                                                    control={
                                                        <Checkbox
                                                            checked={hasAccess(section.title, item, 'can_delete')}
                                                            onChange={() => handlePermissionChange(section.title, item, 'can_delete')}
                                                        />
                                                    }
                                                    label=""
                                                />
                                            )}
                                        </Box>
                                    </Grid>
                                </Grid>
                            ))}
                        </Box>
                    ))}
                </Grid>
            </Grid>
        </form>
    );
}

export default PageRightAccess;
