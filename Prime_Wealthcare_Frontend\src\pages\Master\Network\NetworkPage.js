import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
    deleteNetwork,
    getAllNetworks,
    reinstateNetwork,
    getNetworkByName,
    getNetworksByCriteria,
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';


function NetworkPage() {
    const [selectedOption, setSelectedOption] = useState('none');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const networks = useSelector(state => state.networkReducer.networks);
    const [sortedNetworks, setSortedNetworks] = useState(networks || []);
    const  permissions  = usePermissions('Master', 'Network Master');

    useEffect(() => {
        dispatch(getAllNetworks());
    }, [dispatch])
    useEffect(() => {
        const fetchNetworks = () => {
            dispatch(getNetworksByCriteria(selectedOption));
        };
        fetchNetworks();
    }, [selectedOption, dispatch]);

    useEffect(() => {
        const filterNetworksByStatus = () => {
            if (statusFilter === 'all') {
                setSortedNetworks(networks);
            } else if (statusFilter === 'none') {
                dispatch(getAllNetworks());
            } else {
                setSortedNetworks(networks.filter(network => network.status === (statusFilter === 'active' ? 1 : 0)));
            }
        };

        filterNetworksByStatus();
    }, [statusFilter, networks, dispatch]);

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleConfirmDelete = () => {

        dispatch(deleteNetwork(selectedItem.id))
            .then(() => {
                dispatch(getAllNetworks());
                setOpenDeletePopup(false);
                setOpenSuccessPopup(true);
            })
            .catch(error => {
                console.error('Failed to delete network:', error);
                setOpenDeletePopup(false);
            });
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/network-form');
    };

    const handleDelete = (id) => {
        handleOpenDeletePopup(networks.find(network => network.id === id));
    };

    const handleReinstate = (id) => {
        dispatch(reinstateNetwork(id))
            .then(() => {
                dispatch(getAllNetworks());
            })
            .catch(error => {
                console.error('Failed to reinstate network:', error);
            });
    };

    const handleEdit = (id) => {
        navigate(`/dashboard/network-form/edit/${id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedNetworks.map(network => network.id) : []);
    };



    const onSearch = (query) => {
        if (query === '') {
            dispatch(getAllNetworks());
        } else {
            dispatch(getNetworkByName(query));
        }
    };

    const handleAllClick = () => setStatusFilter('all');
    const handleActiveClick = () => setStatusFilter('active');
    const handleInactiveClick = () => setStatusFilter('inactive');
    const handleRefreshClick = () => {
        setSelectedOption('none');
        dispatch(getAllNetworks());
    }

    const columns = [
        { field: 'hospital_name', headerName: 'Hospital Name' },
        { field: 'insurance_company_name', headerName: 'Inc Co.' },
        { field: 'area_name', headerName: 'Area' },
        { field: 'city', headerName: 'City' },
        { field: 'state', headerName: 'State' },
        { field: 'email_id', headerName: 'Email' },
        { field: 'helpline_number', headerName: 'Phone' },
        { field: 'map', headerName: 'Map' },
    ];

    const dataMapping = {
        ID: 'id',
        'Hospital Name': 'hospital_name',
        'Inc Co.': 'insurance_company_name',
        Area: 'area_name',
        City: 'city',
        State: 'state',
        Email: 'email_id',
        Phone: 'helpline_number',
        Address: 'combined_address',
        Status: 'status',
    };

    const transformedNetworks = sortedNetworks.map(network => ({
        ...network,
        combined_address: `${network.address_line_1}, ${network.address_line_2}`.trim(),
    }));

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Network" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                        <Button onClick={handleAdd} sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}>
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={transformedNetworks.map(network => ({
                                ...network,
                                status: network.status === 1 ? 'Active' : 'Inactive'
                            }))}
                            headNames={['ID', 'Hospital Name', 'Inc Co.', 'Area', 'City', 'State', 'Email', 'Phone', 'Address', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="networks.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Network Report"
                            isLastColumnWide={true}
                        />
                    </ButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingInline: '1rem', ml: 5 }}>
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SearchBar placeholder="Search..." onSearch={onSearch} />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>

                <Box sx={{ mt: -1, maxHeight: '400px' }}>
                    <CustomTable
                        data={sortedNetworks}
                        columns={columns}
                        onDelete={permissions.can_delete ? handleDelete : null}
                        onEdit={permissions.can_edit ? handleEdit : null}
                        onReinstate={handleReinstate}
                        onSelectionChange={handleSelectionChange}
                        selectedRows={selectedRows}
                        onSelectAll={handleSelectAll}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.hospital_name : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.hospital_name : ''}
            />
        </Container>
    )
}

export default NetworkPage