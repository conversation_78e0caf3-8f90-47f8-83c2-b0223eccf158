import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Typography, Paper } from '@mui/material';
import { currentFinancialYear } from '../../utils/Reusable';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const ProposalBarChart = ({ proposalData, height }) => {
  // 1) ALWAYS call hooks first:
  const data = useMemo(() => {
    // provide a safe default when data is missing
    const empty = { labels: [], datasets: [] };
    if (!proposalData) return empty;

    const keys = ['NEW','RENEW','ROLLOVER','MIGRATION'];
    return {
      labels: keys,
      datasets: [
        {
          label: 'SUCCESS',
          data: keys.map(k => proposalData[k]?.SUCCESS || 0),
          backgroundColor: '#36A2EB'
        },
        {
          label: 'PENDING',
          data: keys.map(k => proposalData[k]?.PENDING || 0),
          backgroundColor: '#FFCE56'
        },
        {
          label: 'CANCELLED',
          data: keys.map(k => proposalData[k]?.CANCELLED || 0),
          backgroundColor: '#FF6384'
        }
      ]
    };
  }, [proposalData]);

  const [stepSize, suggestedMax] = useMemo(() => {
    const all = data.datasets.flatMap(ds => ds.data);
    const max = Math.max(...all, 0);
    const ticks = Math.min(10, Math.ceil(max / 5));
    const size  = Math.ceil(max / ticks);
    return [size, size * ticks];
  }, [data]);

  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: { stacked: true },
      y: {
        stacked: true,
        beginAtZero: true,
        suggestedMax,
        ticks: {
          stepSize,
          maxTicksLimit: 10,
          callback: v => Math.round(v)
        }
      }
    },
    plugins: {
      legend: { position: 'bottom' },
      tooltip: {
        callbacks: {
          footer: items => {
            const total = items.reduce((sum, i) => sum + i.parsed.y, 0);
            return `Total: ${total}`;
          }
        }
      }
    }
  }), [stepSize, suggestedMax]);

  // 2) NOW it’s safe to return loading state
  if (!proposalData) {
    return <div>Loading…</div>;
  }

  return (
    <Paper
      elevation={3}
      sx={{
        p: 2, borderRadius: 2, height,
        bgcolor: '#fff',
        display: 'flex', flexDirection: 'column',
        '&:hover': { boxShadow: '0 8px 16px rgba(0,0,0,0.1)' }
      }}
    >
      <Typography
        variant="h6"
        sx={{ mb: 2, fontWeight: 'bold', color: '#333', textAlign: 'center' }}
      >
        Proposal Distribution by Status (FY {currentFinancialYear()})
      </Typography>
      <div style={{ flex: 1, width: '100%' }}>
        <Bar data={data} options={options} />
      </div>
    </Paper>
  );
};

ProposalBarChart.propTypes = {
  proposalData: PropTypes.shape({
    NEW:       PropTypes.objectOf(PropTypes.number).isRequired,
    RENEW:     PropTypes.objectOf(PropTypes.number).isRequired,
    ROLLOVER:  PropTypes.objectOf(PropTypes.number).isRequired,
    MIGRATION: PropTypes.objectOf(PropTypes.number).isRequired
  }).isRequired,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
};

ProposalBarChart.defaultProps = { height: 400 };

export default ProposalBarChart;
