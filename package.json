{"name": "prime-wealthcare", "version": "1.0.0", "description": "Prime WealthCare Application", "scripts": {"start:backend": "cd Prime_Wealthcare_Backend_UAT && cross-env PORT=6000 npm start", "start:frontend": "cd Prime_Wealthcare_Frontend && cross-env PORT=4000 npm start", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "install:backend": "cd Prime_Wealthcare_Backend && npm install", "install:frontend": "cd Prime_Wealthcare_Frontend && npm install", "install:all": "npm install && npm run install:backend && npm run install:frontend", "build:frontend": "cd Prime_Wealthcare_Frontend && npm run build", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "pm2:start:frontend": "pm2 start npm --name Prime_Wealthcare_Frontend_UAT -- start --prefix Prime_Wealthcare_Frontend", "pm2:start:backend": "cd Prime_Wealthcare_Backend && pm2 start app.js --name Prime_Wealthcare_Backend_UAT", "pm2:start": "npm run pm2:start:backend && npm run pm2:start:frontend", "pm2:stop": "pm2 stop Prime_Wealthcare_Backend_UAT Prime_Wealthcare_Frontend_UAT", "pm2:restart": "pm2 restart Prime_Wealthcare_Backend_UAT Prime_Wealthcare_Frontend_UAT", "pm2:delete": "pm2 delete Prime_Wealthcare_Backend_UAT Prime_Wealthcare_Frontend_UAT", "pm2:status": "pm2 status"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}}