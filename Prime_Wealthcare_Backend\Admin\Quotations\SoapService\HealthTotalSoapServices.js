const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../knexfile');
const db = knex(knexConfig.development);
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');


// Environment variables
require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTION; // Use the SOAP Action from the .env file
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

// Generate Member XML dynamically
const generateMemberXML = (memberData) => {
    return memberData
        .map(
            (memberData, index) => `<Member>
            <MemberId>${index + 1}</MemberId>
            <AbhaNo />
            <InsuredName>${memberData.insuredName}</InsuredName>
            <InsuredDob>${formatDate(memberData.insuredDob)}</InsuredDob>  
            <InsuredGender></InsuredGender>
            <InsuredOccpn>${memberData.occupation}</InsuredOccpn>
            <CoverType>${memberData.coverType}</CoverType>
            <SumInsured>${memberData.sumInsured}</SumInsured>
            <DeductibleDiscount />
            <Relation>${memberData.relation}</Relation>
            <NomineeName>Test Nominee</NomineeName>
            <NomineeRelation></NomineeRelation>
            <AnualIncome />
            <Height>${memberData.insuredHeight}</Height>
            <Weight>${memberData.insuredWeight}</Weight>
            <NomineeAge>45</NomineeAge>
            <AppointeeName />
            <AptRelWithominee />
             <MedicalLoading>${memberData.medicalLoading}</MedicalLoading>
                <PreExstDisease>N</PreExstDisease>
                <DiseaseMedicalHistoryList>
                  <DiseaseMedicalHistory>
                    <PreExistingDiseaseCode />
                    <MedicalHistoryDetail />
                  </DiseaseMedicalHistory>
                </DiseaseMedicalHistoryList>
                        ${generateNomineeDetailsXML(memberData.nominee || {})}
        </Member>`
        )
        .join("");
};

// Generate SOAP body dynamically
const SOAP_BODY = (uid, quotationData, membersXML) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
            <tem:Product>HealthTotal</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
  <Uid>${uid}</Uid>
 <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
  <SentToOutSourcePrint>0</SentToOutSourcePrint>
  <WinNo />
  <ApplicationNo />
  <PolicyHeader>
    <PolicyStartDate>${quotationData.start_date}</PolicyStartDate>
    <PolicyEndDate>${quotationData.end_date}</PolicyEndDate>
   <AgentCode>${process.env.AGENT_CODE}</AgentCode>
    <BranchCode>${process.env.BRANCH_CODE}</BranchCode>
    <MajorClass>HTO</MajorClass>
    <ContractType>HTO</ContractType>
    <METHOD>ENQ</METHOD>
    <PolicyIssueType>I</PolicyIssueType>
    <PolicyNo />
    <ClientID></ClientID>
    <ReceiptNo />
  </PolicyHeader>
  <POS_MISP>
    <Type />
    <PanNo />
  </POS_MISP>
  <Client>
    <ClientCategory />
    <ClientType>I</ClientType>
    <CreationType>C</CreationType>
    <Salutation></Salutation>
    <FirstName></FirstName>
    <LastName></LastName>
    <DOB></DOB>
    <Gender></Gender>
    <MaritalStatus></MaritalStatus>
    <Occupation></Occupation>
    <PANNo/>
    <GSTIN />
    <AadharNo />
    <CKYCNo></CKYCNo>
    <CKYCRefNo/>
    <EIANo />
    <Address1>
      <AddrLine1></AddrLine1>
      <AddrLine2></AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${quotationData.pincode}</Pincode>
      <City>Bhavnagar</City>
      <State>Gujarat</State>
      <Country>IND</Country>
      <AddressType>R</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo>9829876493</MobileNo>
      <EmailAddr><EMAIL></EmailAddr>
    </Address1>
    <Address2>
      <AddrLine1></AddrLine1>
      <AddrLine2></AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${quotationData.pincode}</Pincode>
      <City>Bhavnagar</City>
      <State>Gujarat</State>
      <Country>IND</Country>
      <AddressType>K</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo />
      <EmailAddr />
    </Address2>
    <VIPFlag>N</VIPFlag>
    <VIPCategory />
  </Client>
  <Receipt>
    <UniqueTranKey></UniqueTranKey>
    <CheckType />
    <BSBCode />
    <TransactionDate></TransactionDate>
    <ReceiptType>IVR</ReceiptType>
    <Amount></Amount>
    <TCSAmount />
    <TranRefNo></TranRefNo>
    <TranRefNoDate></TranRefNoDate>
  </Receipt>
    <Risk>
    <PolicyType>${quotationData.policyType}</PolicyType>
    <Duration>${quotationData.duration}</Duration>
    <Installments>FULL</Installments>
    <PaymentType>CC</PaymentType>
    <IsFgEmployee>N</IsFgEmployee>
    <BranchReferenceID />
    <FGBankBranchStaffID />
    <BankStaffID />
    <BankCustomerID />
    <BancaChannel />
    <PartnerRefNo />
    <PayorID />
    <PayerName />
    <BeneficiaryDetails>
        ${membersXML}
    </BeneficiaryDetails>
  </Risk>
</Root>]]></tem:XML>
        </tem:CreatePolicy>
    </soapenv:Body>
</soapenv:Envelope>
`;

// Helper function to format date to DD/MM/YYYY
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
};

// Function to save SOAP response to database
const saveSoapResponse = async (quotationId, memberData, responseData) => {
    try {
        // Log the entire responseData for debugging

        // Extract CreatePolicyResult directly with safe navigation
        const createPolicyResult =
            responseData?.['s:Envelope']?.['s:Body']?.[0]?.['CreatePolicyResponse']?.[0]?.['CreatePolicyResult']?.[0];

        if (!createPolicyResult) {
            console.error('Unable to extract CreatePolicyResult from response');
            throw new Error('Unable to extract CreatePolicyResult from response'); // Exit early if no CreatePolicyResult is found
        }

        // Try parsing the XML content
        let xmlContent;
        try {
            xmlContent = await parseStringPromise(createPolicyResult);
        } catch (parseError) {
            console.error('XML Parsing Error:', parseError);
            throw new Error('XML Parsing Error'); // Exit early if XML parsing fails
        }

        // Ensure the parsed XML contains the expected structure
        if (!xmlContent || !xmlContent.Root || !xmlContent.Root.Policy) {
            console.error('Invalid XML structure or missing expected data');
            throw new Error('Invalid XML structure or missing expected data');
        }

        // Prepare data for each member with extensive logging
        const soapResponses = memberData.map((member, index) => {

            // Find the corresponding member in the XML using member_id_no
            const xmlMember = xmlContent.Root.Policy[0].InputParameters[0].BeneficiaryDetails[0].Member
                .find(m => m.MemberId[0] === String(member.member_id_no));

            if (!xmlMember) {
                console.warn(`No matching XML member found for member_id_no: ${member.member_id_no}`);
                return null; // Skip this member if no matching XML data is found
            }

            // Extract data from the parsed XML
            const policyOutputRes = xmlContent.Root.Policy[0].OutputRes[0];

            const responseObj = {
                quotation_id: quotationId,
                member_name: member.insuredName,
                cover_type: member.coverType,
                sum_insured: member.sumInsured,
                relation: member.relation,
                status: 'COMPLETED',
                base_premium: extractNumericValue(policyOutputRes.BasePremium[0]),
                term_premium: extractNumericValue(policyOutputRes.TermPremium[0]),
                family_discount_rate: extractNumericValue(policyOutputRes.FmlyDiscRate[0]),
                family_discount: extractNumericValue(policyOutputRes.FamilyDiscount[0]),
                premium_without_service_tax: extractNumericValue(policyOutputRes.PremWithoutServTax[0]),
                premium_with_load: extractNumericValue(policyOutputRes.PremWithLoad[0]),
                premium_amount: extractNumericValue(policyOutputRes.PremiumAmt[0]),
                service_tax_rate: extractNumericValue(policyOutputRes.ServiceTaxRate[0]),
                service_tax: extractNumericValue(policyOutputRes.ServiceTax[0]),
                premium_with_service_tax: extractNumericValue(policyOutputRes.PremWithServTax[0]),
                bmi: xmlMember?.BMI ? extractNumericValue(xmlMember.BMI[0]) : null,
                bmi_loading_percent: xmlMember?.BMILoadingPercent ? extractNumericValue(xmlMember.BMILoadingPercent[0]) : null,
                per_person_premium: xmlMember?.PerPrsnPremium ? extractNumericValue(xmlMember.PerPrsnPremium[0]) : null
            };

            return responseObj;
        }).filter(response => response !== null); // Filter out invalid responses

        // Only insert into the database if there are valid responses
        if (soapResponses.length > 0) {
            const insertedResponses = await db('soap_responses').insert(soapResponses);
            return insertedResponses;
        } else {
            throw new Error('No valid member responses to insert');
        }
    } catch (error) {
        console.error('Comprehensive Error in saving SOAP response:', error);
        throw error;
    }
};

// Helper function to safely extract numeric values
const extractNumericValue = (value) => {
    if (value === undefined || value === null) return null;

    // Convert to string first to handle different input types
    const stringValue = String(value);

    // Remove any non-numeric characters except decimal point
    const numericString = stringValue.replace(/[^\d.]/g, '');

    // Convert to number
    const numericValue = parseFloat(numericString);

    // Return number or null if conversion fails
    return isNaN(numericValue) ? null : numericValue;
};

// Modified sendSOAPRequestHealthTotal function
const sendSOAPRequestHealthTotal = async (memberData, quotationData, quotationId) => {
    try {
        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };
        // Ensure memberData is an array
        const membersArray = Array.isArray(memberData) ? memberData : [memberData];

        // Generate unique UID and Member XML
        const uniqueUID = uuidv4();
        const membersXML = generateMemberXML(membersArray);

        // Generate SOAP body dynamically
        const requestBody = SOAP_BODY(uniqueUID, quotationData, membersXML);
        console.log('SOAP Envelope:', requestBody);
        // Send the SOAP request
        const response = await axios.post(SOAP_API_URL, requestBody, { headers });
        console.log('SOAP Response:', response.data);
        
        
        // Parse the XML response
       const jsonResponse = await parseStringPromise(response.data);
        const logData = {
            quotation_number: quotationData.quotation_number,
            request_body: requestBody, // Raw XML request
            response_body: response.data, // Raw XML response
            status: 'SUCCESS',
            error_message: null,
            created_by: quotationData.Created_by || 'SYSTEM',
            customer_name: membersArray[0]?.insuredName || 'UNKNOWN',
            insurance_company: (quotationData.insurance_company || 'FG Insurance'),
            product_name: (quotationData.product || 'Health Total'),
            created_at: db.fn.now()
        };

        await db('quotations_logs').insert(logData);

        // Save the SOAP response to database
        await saveSoapResponse(quotationId, membersArray, jsonResponse);
        return jsonResponse;
    } catch (error) {
        console.error("SOAP Request Error Details:", {
            message: error.message,
            status: error.response ? error.response.status : "No status",
            data: error.response ? error.response.data : "No data",
        });

        // Optionally, save error response
        await saveSoapResponse(quotationId, memberData, {
            status: 'FAILED',
            error: error.message
        });

        throw new Error(`SOAP Request Failed: ${error.message}`);
    }
};

module.exports = { sendSOAPRequestHealthTotal };
