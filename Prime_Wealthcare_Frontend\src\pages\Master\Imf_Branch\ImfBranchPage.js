import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
  softDeleteImfBranch, fetchAllImfBranches, reinstateImfBranch,
  fetchIMFBranchesByCriteria, 
  searchIMFBranches
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';

const ImfBranchPage = () => {
  const [selectedOption, setSelectedOption] = useState('none');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRows, setSelectedRows] = useState([]);

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [selectedItem, setSelectedItem] = useState(null);
  const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const { data } = useSelector(state => state.imfBranchReducer); // Make sure state slice is correct
  const [filteredCompanies, setFilteredCompanies] = useState(data || []);

  const  permissions  = usePermissions('Master' ,'IMF Branch');

  useEffect(() => {
    dispatch(fetchAllImfBranches());
  }, [dispatch]);

  useEffect(() => {
    setFilteredCompanies(data);
  }, [data]);

  useEffect(() => {
    if (selectedOption) {
      dispatch(fetchIMFBranchesByCriteria(selectedOption));
    }
  }, [selectedOption, dispatch]);

  useEffect(() => {
    const filterCompaniesByStatus = () => {
      if (statusFilter === 'all') {
        setFilteredCompanies(data);
      } else {
        setFilteredCompanies(data.filter(company => company.status === (statusFilter === 'active' ? 1 : 0)));
      }
    };

    filterCompaniesByStatus();
  }, [statusFilter, data]);

  useEffect(() => {
    if (data && data.length > 0) {
      // Sort by 'created_at' in reverse order (newest first)
      const sortedData = [...data].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      setFilteredCompanies(sortedData);
    }
  }, [data]);
  

  const onSearch = (query) => {
    if (query === '') {
      dispatch(fetchAllImfBranches());
    } else {
      dispatch(searchIMFBranches(query));
    }
  };

  const handleDelete = (id) => {
    handleOpenDeletePopup(data.find(branch => branch.id === id));
  }

  const handleOpenDeletePopup = (item) => {
    setSelectedItem(item);
    setOpenDeletePopup(true);
  };

  const handleCloseDeletePopup = () => {
    setOpenDeletePopup(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    dispatch(softDeleteImfBranch(selectedItem.id))
      .then(() => {
        dispatch(fetchAllImfBranches());
        setOpenDeletePopup(false);
        setOpenSuccessPopup(true);
      })
      .catch(error => {
        console.error("Failed to delete branch:", error);
        setOpenDeletePopup(false);
      });
  };

  const handleCloseSuccessPopup = () => {
    setOpenSuccessPopup(false);
  };

  const handleAdd = () => {
    navigate('/dashboard/imf-branch-form');
  };

  const handleReinstate = (id) => {
    dispatch(reinstateImfBranch(id))
      .then(() => {
        dispatch(fetchAllImfBranches());
      })
      .catch(error => {
        console.error("Failed to reinstate branch:", error);
      });
  };

  const handleEdit = (id) => {
    const selectedBranch = data.find(branch => branch.id === id);
    if (selectedBranch) {
      navigate(`/dashboard/imf-branch-form/edit/${id}`, {
        state: {
          data: selectedBranch, // Pass the branch data to the edit form
        }
      });
    } else {
      console.error("Branch not found for id:", id);
    }
  };

  const handleSelectionChange = (id) => {
    setSelectedRows(prevSelected =>
      prevSelected.includes(id)
        ? prevSelected.filter(rowId => rowId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSelectAll = (isSelected) => {
    setSelectedRows(isSelected ? data.map(branch => branch.id) : []);
  };

  const handleAllClick = () => setStatusFilter('all');
  const handleActiveClick = () => setStatusFilter('active');
  const handleInactiveClick = () => setStatusFilter('inactive');
  const handleRefreshClick = () => {
    setSelectedOption('none');
    dispatch(fetchAllImfBranches());
  }

 const formatDate = (timestamp) => {
  if (!timestamp) return 'N/A'; // Handle empty date
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-GB'); // dd/mm/yyyy format
};

  const columns = [
    { field: 'id', headerName: 'ID' },
    { field: 'branch_name', headerName: 'Branch Name' },
    { field: 'branch_code', headerName: 'Branch Code' },
    { field: 'help_line_number', headerName: 'Helpline Number' },
    { field: 'branch_manager_name', headerName: 'Branch Manager' },
    { field: 'city', headerName: 'City' },
    { field: 'state', headerName: 'State' },
    {
      field: 'created_at',
      headerName: 'Created At',
      render: (row) => formatDate(row.created_at), // Format the date here
    },

  ];

  const dataMapping = {
    ID: 'id',
    'Branch Name': 'branch_name',
    'Branch Code': 'branch_code',
    'Helpline Number': 'help_line_number',
    'Branch Manager': 'branch_manager_name',
    City: 'city',
    State: 'state',
    'Created At': 'created_at',
    Status: 'status',
  };

  return (
    <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/image.png"
              alt="module icon"
              style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
            />
            <ModuleName moduleName="IMF Branch" pageName="List" />
          </Box>
          <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
            {permissions.can_add && (
            <Button
              onClick={handleAdd}
              sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
            >
              New
            </Button>
            )}
            <ExportToPDF
              data={filteredCompanies.map(branch => ({
                ...branch,
                status: branch.status === 1 ? 'Active' : 'Inactive'
              }))}
              headNames={['ID', 'Branch Name', 'Branch Code', 'Helpline Number', 'Branch Manager', 'City', 'State', 'Status']}
              selectedRows={selectedRows}
              imageUrl="/logo.png"
              watermarkUrl="/gray-logo.png"
              fileName="imf_branches.pdf"
              dataMapping={dataMapping}
              headerTitle="IMF Branch Report"
            />
          </ButtonGroup>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingBottom: '1rem', ml: 5 }}>
          <DropDown
            label=""
            value={selectedOption}
            onChange={(e) => setSelectedOption(e.target.value)}
            options={[
              { value: 'none', label: 'None' },
              { value: 'newLastWeek', label: 'New Last Week' },
              { value: 'newThisWeek', label: 'New this Week' },
              { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
              { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
              { value: 'editedLastWeek', label: 'Edited Last Week' },
              { value: 'editedThisWeek', label: 'Edited This Week' },
            ]}
          />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchBar placeholder="Search......" onSearch={onSearch} />
            <IconActions
              onAllClick={handleAllClick}
              onActiveClick={handleActiveClick}
              onInactiveClick={handleInactiveClick}
              onRefreshClick={handleRefreshClick}
            />
          </Box>
        </Box>
        <CustomTable
          data={filteredCompanies}
          columns={columns}
          selectedRows={selectedRows}
          onSelectionChange={handleSelectionChange}
          onSelectAll={handleSelectAll}
          onDelete={permissions.can_delete ? handleDelete : null}
          onEdit={permissions.can_edit ? handleEdit : null}
          onReinstate={handleReinstate}
          ActionComponent={IconActions}
        />
      </Box>

      <DeletePopup
        open={openDeletePopup}
        onClose={handleCloseDeletePopup}
        onConfirm={handleConfirmDelete}
        modulename={selectedItem ? selectedItem.branch_name : ''}
      />
      <SuccessPopup
        open={openSuccessPopup}
        onClose={handleCloseSuccessPopup}
        modulename={selectedItem ? selectedItem.branch_name : ''}
      />
    </Container>
  );
};

export default ImfBranchPage;