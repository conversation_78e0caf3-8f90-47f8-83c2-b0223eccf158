import { createSlice } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { createEmployeeSalary, deleteEmployeeSalary, fetchAllEmployeeSalary, fetchEmployeeSalaryById, updateEmployeeSalary } from '../../actions/action';

const initialState = {
    salaries: [],
    currentSalary: null,
    isLoading: false,
    error: null,
}

const employeeSalarySlice = createSlice({
    name: 'employeeSalary',
    initialState,
    reducers: {
        clearCurrentSalary: (state) => {
            state.currentSalary = null;
        }
    },
    extraReducers: (builder) => {
        builder
            //Create Employee Salary
            .addCase(createEmployeeSalary.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(createEmployeeSalary.fulfilled, (state, action) => {
                state.isLoading = false;
                state.salaries.push(action.payload);
                toast.success('Employee salary created successfully');
            })
            .addCase(createEmployeeSalary.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to create employee salary');
            })

            // Get all salaries of the employee
            .addCase(fetchAllEmployeeSalary.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchAllEmployeeSalary.fulfilled, (state, action) => {
                state.isLoading = false;
                state.salaries = action.payload;
            })
            .addCase(fetchAllEmployeeSalary.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee salaries');
            })
            .addCase(fetchEmployeeSalaryById.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchEmployeeSalaryById.fulfilled, (state, action) => {
                state.isLoading = false;
                state.currentSalary = action.payload;
            })
            .addCase(fetchEmployeeSalaryById.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee salary');
            })
            .addCase(updateEmployeeSalary.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(updateEmployeeSalary.fulfilled, (state, action) => {
                state.isLoading = false;
                state.salaries = state.salaries.map((salary) => {
                    if (salary.id === action.payload.id) {
                        return action.payload;
                    }
                    return salary;
                })
                toast.success('Employee salary updated successfully');
            })
            .addCase(updateEmployeeSalary.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to update employee salary');
            })
            .addCase(deleteEmployeeSalary.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(deleteEmployeeSalary.fulfilled, (state, action) => {
                state.isLoading = false;
                state.salaries = state.salaries.filter((salary) => salary.id !== action
                    .payload);
                toast.success('Employee salary deleted successfully');
            })
            .addCase(deleteEmployeeSalary.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to delete employee salary');
            });
    }
});

export const { clearCurrentSalary } = employeeSalarySlice.actions;

export default employeeSalarySlice.reducer;