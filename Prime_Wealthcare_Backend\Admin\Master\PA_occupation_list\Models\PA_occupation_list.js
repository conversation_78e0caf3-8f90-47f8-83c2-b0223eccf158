const knexConfig = require('../../../../knexfile');
const knex = require('knex')(knexConfig.development);

class PickList {
    static async getAll() {
        return knex('pa_occupation_list').where('is_active', true); // Fetch only active records
    }

    static async getByTypeName(typeName) {
        return knex('pa_occupation_list').where('type_name', typeName).andWhere('is_active', true);
    }

    static async create(PA_occupation_listData) {
        return knex('pa_occupation_list').insert(PA_occupation_listData).returning('*');
    }

    static async update(id, PA_occupation_listData) {
        return knex('pa_occupation_list').where({ id }).update(PA_occupation_listData).returning('*');
    }

    static async delete(id) {
        return knex('pa_occupation_list').where({ id }).del();
    }

    static async softDelete(id) {
        return knex('pa_occupation_list').where({ id }).update({ is_active: false }).returning('*');
    }

    static async reinstate(id) {
        return knex('pa_occupation_list').where({ id }).update({ is_active: true }).returning('*');
    }
}

module.exports = PickList;
