import React, { useRef, useState } from 'react';
import { Grid, Box, Typography, Button, CircularProgress, Snackbar, Alert } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { verifyOTP, forgotPassword } from '../../redux/actions/action'; // Import the action

const OtpVerification = () => {
  const [otp, setOtp] = useState(new Array(6).fill(""));
  const inputRefs = useRef([]);
  const navigate = useNavigate(); // Initialize navigate
  const dispatch = useDispatch();

  const { loading, error, success ,email} = useSelector((state) => state.auth); // Access the Redux state

  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'info' });

  const handleChange = (element, index) => {
    const value = element.value;
    if (isNaN(value)) return;

    // Set the OTP value
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Focus the next input if the current input is filled
    if (value !== "" && index < 5) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handleKeyDown = (element, index) => {
    if (element.key === "Backspace" && otp[index] === "") {
      if (index > 0) {
        inputRefs.current[index - 1].focus();
      }
    }
  };

  const handleSubmit = () => {
    const otpString = otp.join(""); 
  
    // Ensure email is available before dispatching
    if (email) {
      dispatch(verifyOTP({ email, otp: otpString }))
        .then((action) => {
          if (action.type === 'auth/verifyOTP/fulfilled') {
            navigate('/new-password');  // Navigate to new-password page on success
          } else {
            setAlert({
              open: true,
              message: 'OTP verification failed',
              severity: 'error'
            });
          }
        })
        .catch((error) => {
          setAlert({
            open: true,
            message: error.message || 'OTP verification failed',
            severity: 'error'
          });
        });
    } else {
      setAlert({
        open: true,
        message: 'Email is missing',
        severity: 'error'
      });
    }
  };
  
  const handleResendOtp = async () => {
    if (resendDisabled || !email) return;

    try {
      await dispatch(forgotPassword({ email })).unwrap();
      
      // Show success message
      setAlert({
        open: true,
        message: 'OTP has been resent to your email',
        severity: 'success'
      });

      // Disable resend button and start countdown
      setResendDisabled(true);
      setCountdown(60);

      // Start countdown timer
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error) {
      setAlert({
        open: true,
        message: error.message || 'Failed to resend OTP',
        severity: 'error'
      });
    }
  };

  const handleCloseAlert = () => {
    setAlert({ ...alert, open: false });
  };

  return (
    <Grid container style={{ height: '100vh', overflow: 'hidden' }}>
      <Grid item xs={12} md={6} style={{ backgroundImage: `url(/background.png)`, backgroundSize: 'cover', backgroundPosition: 'left' }}>
      </Grid>
      <Grid
        item
        xs={12}
        md={6}
        display="flex"
        alignItems="center"
        justifyContent="center"
        style={{ backgroundColor: '#DDF2ED', padding: '12px', height: '100%' }}
      >
        <Box sx={{ width: '100%', maxWidth: 800, padding: 2, borderRadius: 2, alignItems: 'center', height: 'auto', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>

          {/* Logo Image */}
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <img
              src="/logo1.png"
              alt="Logo"
              style={{ width: 250 }}
            />
          </Box>

          {/* Illustration Image */}
          <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: 1 }}>
            <img
              src="/illustration.png"
              alt="Illustration"
              style={{ width: '50%' }}
            />
          </Box>

          {/* OTP Verification */}
          <Box sx={{ textAlign: 'center', mt: 2, mb: 3 }}>
            <Typography variant="h5" gutterBottom>
              OTP Verification
            </Typography>
            <Typography variant="body1" gutterBottom>
              Please Enter OTP to Verify
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', marginBottom: '16px' }}>
            {otp.map((data, index) => (
              <input
                type="text"
                name="otp"
                maxLength="1"
                key={index}
                value={data}
                onChange={e => handleChange(e.target, index)}
                onKeyDown={e => handleKeyDown(e, index)}
                ref={(el) => inputRefs.current[index] = el}
                style={{
                  width: "40px",
                  height: "40px",
                  margin: "0 5px",
                  textAlign: "center",
                  fontSize: "20px",
                  borderRadius: "4px",
                  border: "1px solid #ccc"
                }}
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center', marginLeft: '240px' }}>
            <Typography 
              variant="body2" 
              color={resendDisabled ? 'text.secondary' : 'primary'}
              onClick={handleResendOtp}
              sx={{ 
                cursor: resendDisabled ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                '&:hover': {
                  textDecoration: resendDisabled ? 'none' : 'underline'
                }
              }}
            >
              Resend OTP {countdown > 0 && `(${countdown}s)`}
              {loading && <CircularProgress size={16} sx={{ ml: 1 }} />}
            </Typography>
          </Box>
          <Box sx={{
            display: 'flex', justifyContent: 'center', width: '100%', mt: 2, mb: 1,
            pb: { xs: 1, md: 0 },
          }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              sx={{
                maxWidth: '250px', width: '100%', backgroundColor: '#1A6A62',
                padding: '12px 0',

              }}
            >
              Verify
            </Button>
          </Box>
        </Box>
      </Grid>
      <Snackbar 
        open={alert.open} 
        autoHideDuration={6000} 
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseAlert} 
          severity={alert.severity} 
          sx={{ width: '100%' }}
        >
          {alert.message}
        </Alert>
      </Snackbar>
    </Grid>
  );
};

export default OtpVerification;