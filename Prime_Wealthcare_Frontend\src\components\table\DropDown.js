import React from 'react';
import { FormControl, InputLabel, MenuItem, Select, FormHelperText } from '@mui/material';
import InputAdornment from '@mui/material/InputAdornment';

const Dropdown = ({
  label,
  helperText,
  required,
  options,
  value,
  onChange,
  fullWidth = false,
  width = 'auto', // Add width prop with default value
  range = null, // range can be an array like [start, end, step]
  prefixSymbol,  // Add this new prop
  ...rest
}) => {
  // Helper function to generate a range of integers
  const generateRange = (start, end, step = 1) => {
    const rangeArray = [];
    for (let i = start; i <= end; i += step) {
      rangeArray.push(i);
    }
    return rangeArray;
  };

  // Render the menu items based on options or range
  const renderMenuItems = () => {
    if (range && Array.isArray(range) && range.length >= 2) {
      // Generate range if range prop is provided
      const [start, end, step] = range;
      const rangeOptions = generateRange(start, end, step);
      return rangeOptions.map((option) => (
        <MenuItem key={option} value={option}>
          {option}
        </MenuItem>
      ));
    } else if (Array.isArray(options)) {
      // Default behavior for options array of objects
      return options.map((option) => (
        <MenuItem key={option.value} value={option.value}>
          {option.label}
        </MenuItem>
      ));
    }
  };

  return (
    <FormControl
      fullWidth={fullWidth}
      sx={{ width: fullWidth ? '100%' : width }}
      required={required}
    >
      <InputLabel
        shrink={value !== undefined && value !== null && value !== ''}
        sx={{
          '& .MuiFormLabel-asterisk': {
            display: 'none',
          },
          transform: Boolean(value) ? 'translate(14px, -9px) scale(0.75)' : undefined,
          padding: Boolean(value) ? '0 4px' : prefixSymbol ? '0 28px' : undefined,
          backgroundColor: 'transparent',
        }}
      >
        {label}
      </InputLabel>
      <Select
        value={value}
        onChange={onChange}
        label={label}
        startAdornment={prefixSymbol && (
          <InputAdornment position="start">
            <span style={{
              marginRight: '8px',
              color: '#888'
            }}>
              {prefixSymbol}
            </span>
          </InputAdornment>
        )}
        MenuProps={{
          PaperProps: {
            style: {
              maxHeight: 250,
              maxWidth: 250,
              overflowX: 'auto',
            },
          },
        }}
        {...rest}
        sx={{
          borderLeft: required ? '3px solid red !important' : 'none',
          borderRadius: '8px',
          backgroundColor: rest.disabled ? 'rgba(0, 0, 0, 0.05)' : 'transparent',
          '& .MuiSelect-select': {
            backgroundColor: 'transparent',
          }
        }}
      >
        {renderMenuItems()}
      </Select>
      {helperText && <FormHelperText sx={{ color: 'red' }}>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default Dropdown;