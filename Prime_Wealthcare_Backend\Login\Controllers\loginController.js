const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const knexConfig = require('../../knexfile');
const knex = require('knex')(knexConfig.development);
const ms = require('ms'); 

// Use environment variables for the secret key
const SECRET_KEY = process.env.SECRET_KEY;
if (!SECRET_KEY) {
    throw new Error('SECRET_KEY environment variable is not set');
}
// Token expiration time
const TOKEN_EXPIRATION = process.env.TOKEN_EXPIRATION || '1h'; // Fallback to 1h if not set

const login = async (req, res) => {
    const { userId, password } = req.body;

    try {
        // Search for the user in agents table first
        let user = await knex('agents').where({ agent_id: userId }).first();
        let userType = 'agent';

        // If not found, search in employees table
        if (!user) {
            user = await knex('employee_personal_info').where({ user_id: userId }).first();
            userType = 'employee';
        }

        // If no user is found in either table
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        
        // Check if user is active (status = 1)
        if (user.status === 0) {
            return res.status(403).json({ message: 'Your account is inactive. Please contact the administrator.' });
        }

        // Check if the password is encrypted (bcrypt hash)
        const isEncrypted = user.password.startsWith('$2b$');
        if (!isEncrypted) {
            // First-time login: password stored in plain text
            if (password === user.password) {
                return res.status(200).json({
                    firstLogin: true,
                    message: 'First-time login. Please change your password.',
                    userId: userType === 'agent' ? user.agent_id : user.user_id,
                    userType,
                    status: user.status, // Include numeric status
                });
            } else {
                return res.status(400).json({ message: 'Invalid password' });
            }
        }

        // Regular login: compare hashed password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.status(400).json({ message: 'Invalid password' });
        }

        // Generate JWT token
        const token = jwt.sign(
            { id: user.id, userId, userType },
            SECRET_KEY,
            { algorithm: 'HS256', expiresIn: TOKEN_EXPIRATION }
        );

        // Store session information in the database
        await knex('user_login').insert({
            user_id: userId,
            user_type: userType,
            token,
            login_time: knex.fn.now(),
            expires_at: new Date(Date.now() + ms(TOKEN_EXPIRATION)), // Store token expiration
            is_active: true,
        });
        // Return the token and user details
        return res.status(200).json({
            token,
            status: user.status, // Include numeric status
            expiresIn: TOKEN_EXPIRATION,
        });
    } catch (error) {
        console.error('Error during login:', error);
        return res.status(500).json({ message: 'An unexpected error occurred' });
    }
};

const logout = async (req, res) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];

        if (token) {
            await knex('user_login')
                .where({ token })
                .update({ is_active: false });
        }

        return res.status(200).json({ message: 'Logout successful' });
    } catch (error) {
        console.error('Error during logout:', error);
        return res.status(500).json({ message: 'Failed to logout' });
    }
};


const validateToken = async (req, res) => {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
        return res.status(401).json({ message: 'Token missing or malformed' });
    }

    try {
        const decoded = jwt.verify(token, SECRET_KEY);
        // Check if the session is active
        const userSession = await knex('user_login').where({ token }).first();
        
        if (!userSession || !userSession.is_active) {
            return res.status(401).json({ message: 'Session is inactive' });
        }

        // Fetch user details based on userType
        let userDetails;
        if (decoded.userType === 'employee') {
            userDetails = await knex('employee_personal_info')
                .where({ user_id: decoded.userId })
                .select('employee_full_name', 'status')
                .first();
        } else if (decoded.userType === 'agent') {
            userDetails = await knex('agents')
                .where({ agent_id: decoded.userId })
                .select('full_name', 'status')
                .first();
        }

        // Check if user account is still active
        if (userDetails && userDetails.status === 0) {
            return res.status(403).json({ message: 'Your account has been deactivated' });
        }

        return res.status(200).json({
            message: 'Token is valid',
            userId: decoded.userId,
            userType: decoded.userType,
            userName: decoded.userType === 'employee' ? userDetails?.employee_full_name : userDetails?.full_name,
            status: userDetails?.status
        });
    } catch (error) {
        console.error('Error during token validation:', error);
        return res.status(403).json({ message: 'Invalid token' });
    }
};

module.exports = {
    login,
    logout,
    validateToken,
};
