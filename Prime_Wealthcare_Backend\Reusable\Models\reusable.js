const knexConfig = require('../../knexfile');
const db = require('knex')(knexConfig.development);

exports.findById = async (id) => {
    const agentData = await db('agents').where({ agent_id: id }).first();
    const customerData = await db('employee_personal_info').where({ user_id: id }).first();

    return agentData || customerData || null;
}

exports.getMasterProductsByQuotationId = async (quotation_id) => {
    // Get quotation and product details
    const quotation = await db('quotations')
        .where('quotation_id', quotation_id)
        .first();

    if (!quotation) return null;

    // Get product_master id using product name
    const productMaster = await db('product_master')
        .where('product_name', quotation.product)
        .first();

    if (!productMaster) return null;

    // Get soap response to get cover_type
    const soapResponse = await db('soap_responses')
        .where('quotation_id', quotation_id)
        .first();

    if (!soapResponse) return null;

    // Get sub_product id using cover_type
    const subProduct = await db('sub_product')
        .where('sub_product_name', soapResponse.cover_type)
        .where('product_master_id', productMaster.id)
        .first();

    if (!subProduct) return null;

    return {
        product_master_id: productMaster.id,
        sub_product_id: subProduct.id
    };
}

exports.getNomineeRelations = async () => {
    return await db('fg_nominee_relation').where('is_active', true);
}

exports.getAllUsers = async () => {
    const employees = await db('employee_personal_info')
        .select('user_id', 'employee_full_name as name','status'); // Adjust the fields as necessary

    const agents = await db('agents') // Assuming you have an 'agents' table
        .select('agent_id as user_id', 'full_name as name', 'status'); // Adjust the fields as necessary

    // Combine the results
    return [...employees, ...agents];
};