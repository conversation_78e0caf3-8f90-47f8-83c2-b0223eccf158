const express = require('express');
const agentAddressController = require('../Controllers/agentAddressController');
const router = express.Router();

// Route to get all Agent Addresses
router.get('/', agentAddressController.getAllAddresses);

// Route to get an Agent Address by ID
router.get('/:id', agentAddressController.getAddressById);

// Route to create a new Agent Address
router.post('/', agentAddressController.createAddress);

// Route to update an Agent Address by ID
router.put('/:id', agentAddressController.updateAddress);

// Route to delete (deactivate) an Agent Address by ID
router.delete('/:id', agentAddressController.deleteAddress);

module.exports = router;
