const express = require('express');
const router = express.Router();
const customerDocumentationController = require('../Controllers/customer_documentation_controller.js');
const upload = require('../Middleware/upload');

const uploadFields = upload.fields([
    { name: 'document_path', maxCount: 1 },
])

router.get('/', customerDocumentationController.getAll);
router.post('/', uploadFields, customerDocumentationController.create);
router.get('/:id', customerDocumentationController.getById);
router.get('/customer/:id', customerDocumentationController.getByCustomerId);
router.put('/:id', uploadFields, customerDocumentationController.update);
router.delete('/:id', customerDocumentationController.delete);

module.exports = router;