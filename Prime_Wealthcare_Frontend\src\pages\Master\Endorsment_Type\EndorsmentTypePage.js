import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
    deleteEndorsment,
    getAllEndorsments,
    reinstateEndorsment,
    getEndorsmentByName,
    getFilterEndorsmentData,
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';


function EndorsmentTypePage() {
    const [selectedOption, setSelectedOption] = useState('none');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const endorsments = useSelector(state => state.endorsmentTypeReducer.endorsments);
    const [sortedEndorsments, setSortedEndorsments] = useState(endorsments || []);

    const permissions  = usePermissions('Master', 'Endorsement Type');

    useEffect(() => {
        dispatch(getAllEndorsments());
    }, [dispatch])
    useEffect(() => {
        const fetchEndorsments = () => {
            dispatch(getFilterEndorsmentData(selectedOption));
        };
        fetchEndorsments();
    }, [selectedOption, dispatch]);
    useEffect(() => {
        const filterEndorsmentsByStatus = () => {
            if (statusFilter === 'all') {
                setSortedEndorsments(endorsments);
            } else if (statusFilter === 'none') {
                dispatch(getAllEndorsments());
            } else {
                setSortedEndorsments(endorsments.filter(endorsment => endorsment.status === (statusFilter === 'active' ? 1 : 0)));
            }
        };

        filterEndorsmentsByStatus();
    }, [statusFilter, endorsments]);

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleConfirmDelete = () => {
        dispatch(deleteEndorsment(selectedItem.id))
            .then(() => {
                dispatch(getAllEndorsments());
                setOpenDeletePopup(false);
                setOpenSuccessPopup(true);
            })
            .catch(error => {
                console.error('Failed to delete endorsment:', error);
                setOpenDeletePopup(false);
            });
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/endorsment-type-form');
    };

    const handleDelete = (id) => {
        handleOpenDeletePopup(endorsments.find(endorsment => endorsment.id === id));
    };

    const handleReinstate = (id) => {
        dispatch(reinstateEndorsment(id))
            .then(() => {
                dispatch(getAllEndorsments());
            })
            .catch(error => {
                console.error('Failed to reinstate endorsment:', error);
            });
    };

    const handleEdit = (id) => {
        navigate(`/dashboard/endorsment-type-form/edit/${id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedEndorsments.map(endorsment => endorsment.id) : []);
    };

    const onSearch = (query) => {
        if (query === '') {
            dispatch(getAllEndorsments());
        } else {
            dispatch(getEndorsmentByName(query));
        }
    };

    const handleAllClick = () => setStatusFilter('all');
    const handleActiveClick = () => setStatusFilter('active');
    const handleInactiveClick = () => setStatusFilter('inactive');
    const handleRefreshClick = () => {
        setSelectedOption('none');
        dispatch(getAllEndorsments());
    }

    const columns = [
        { field: 'endorsment_type', headerName: 'Endorsment Type' },
        { field: 'endorsment_name', headerName: 'Endorsment Name' },
        { field: 'endorsment_description', headerName: 'Endorsment Description' },
    ];

    const dataMapping = {
        ID: 'id',
        'Endorsment Type': 'endorsment_type',
        'Endorsment Name': 'endorsment_name',
        'Endorsment Description': 'endorsment_description',
        'Status': 'status',
    };

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Endorsment Type" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                        <Button onClick={handleAdd} sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}>
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={sortedEndorsments.map(endorsment => ({
                                ...endorsment,
                                status: endorsment.status === 1 ? 'Active' : 'Inactive'
                            }))}
                            headNames={['ID', 'Endorsment Type', 'Endorsment Name', 'Endorsment Description', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="endorsments.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Endorsment Report"
                        />
                    </ButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingInline: '1rem', ml: 5 }}>
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SearchBar placeholder="Search..." onSearch={onSearch} />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>

                <Box sx={{ mt: -1, maxHeight: '400px' }}>
                    <CustomTable
                        data={sortedEndorsments}
                        columns={columns}
                        onDelete={permissions.can_delete ? handleDelete: null}
                        onEdit={permissions.can_edit ? handleEdit: null}
                        onReinstate={handleReinstate}
                        onSelectionChange={handleSelectionChange}
                        selectedRows={selectedRows}
                        onSelectAll={handleSelectAll}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.endorsment_name : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.endorsment_name : ''}
            />
        </Container>
    )
}

export default EndorsmentTypePage