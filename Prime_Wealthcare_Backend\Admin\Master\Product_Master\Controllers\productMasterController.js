// controllers/productMasterController.js

const ProductMaster = require('../Models/productMaster');

// Get all products
exports.getAllProducts = async (req, res, next) => {
    try {
        const data = await ProductMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

exports.getMasterProductByMainProductAndInsuranceCompany = async (req, res, next) => {
    try {
        const { mainProductId, insuranceCompanyId } = req.params;
        const data = await ProductMaster.getMasterProductByMainProductAndInsuranceCompany(mainProductId, insuranceCompanyId);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
}

// Get product by ID
exports.getProductById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const product = await ProductMaster.getById(id);
        if (product) {
            res.status(200).json(product);
        } else {
            res.status(404).json({ message: 'Product not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Create new product
exports.createProduct = async (req, res, next) => {
    try {
        const productData = req.body;
        await ProductMaster.create(productData);
        res.status(201).json({ message: 'Product created successfully' });
    } catch (error) {
        next(error);
    }
};

// Update product by ID
exports.updateProduct = async (req, res, next) => {
    try {
        const { id } = req.params;
        const productData = req.body;
        const data = await ProductMaster.update(id, productData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Soft delete (deactivate) product by ID
exports.deleteProduct = async (req, res, next) => {
    try {
        const { id } = req.params;
        await ProductMaster.delete(id);
        res.status(200).json({ message: 'Product deactivated successfully' });
    } catch (error) {
        next(error);
    }
};

// Reinstate product by ID
exports.reinstateProduct = async (req, res,next) => {
    try {
        const { id } = req.params;
        await ProductMaster.reinstate(id);
        res.status(200).json({ message: 'Product reinstated successfully' });
    } catch (error) {
        next(error);
    }
};

exports.getMasterProductByName = async (req, res, next) => {
    try {
        const { name } = req.params;
        const product = await ProductMaster.getByName(name);
        res.status(200).json(product);
    } catch (error) {
        next(error);
    }
};

exports.getMasterProductsByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await ProductMaster.getAll();
                break;
            case 'newLastWeek':
                data = await ProductMaster.newLastWeek();
                break;
            case 'newThisWeek':
                data = await ProductMaster.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await ProductMaster.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await ProductMaster.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await ProductMaster.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await ProductMaster.editedLastWeek();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }


        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
}
