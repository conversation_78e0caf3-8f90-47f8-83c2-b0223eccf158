const axios = require('axios');
const xml2js = require('xml2js');
const knexConfig = require('../../../knexfile');
const knex = require('knex')(knexConfig.development);
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');

const SOAP_API_URL = process.env.SOAP_API_URL;
const SOAP_ACTION = process.env.SOAP_ACTION;
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

const createSoapEnvelope = (data) => {
    // Map frontend cover names to SOAP API cover codes and names
    const coverMapping = {
        'RF': {
            code: 'RF',
            name: 'Repatriation and Funeral Expenses',
            type: 'S'
        },
        'AA': {
            code: 'AA',
            name: 'Adaptation Allowance',
            type: 'S'
        },
        'CS': {
            code: 'CS',
            name: 'Child Education Support',
            type: 'S'
        },
        'FT': {
            code: 'FT',
            name: 'Family Transportation Allowance',
            type: 'S'
        },
        'HC': {
            code: 'HC',
            name: 'Hospital Cash Allowance',
            type: 'S'
        },
        'LP': {
            code: 'LP',
            name: 'Loan Protecter',
            type: 'S'
        },
        'LS': {
            code: 'LS',
            name: 'Life Support Benefit',
            type: 'S'
        },
        'ME': {
            code: 'ME',
            name: 'Accidental Hospitalisation',
            type: 'S'
        },
        'AM': {
            code: 'AM',
            name: 'Accidental Medical Expenses',
            type: 'S'
        },
        'BB': {
            code: 'BB',
            name: 'Broken Bones',
            type: 'S'
        }
    };

    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
        <soapenv:Header/> 
        <soapenv:Body>
            <tem:CreatePolicy>
                <tem:Product>PA</tem:Product>
                <tem:XML><![CDATA[<Root>
    <Uid>${data.Uid}</Uid>
  <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
    <SentToOutSourcePrint>0</SentToOutSourcePrint>
    <WinNo/>
    <ApplicationNo/>
    <PolicyHeader>
        <PolicyStartDate></PolicyStartDate>
        <PolicyEndDate></PolicyEndDate>
       <AgentCode>${process.env.AGENT_CODE}</AgentCode>
    <BranchCode>${process.env.BRANCH_CODE}</BranchCode>
        <MajorClass>PAC</MajorClass>
        <ContractType>PAL</ContractType>
        <METHOD>ENQ</METHOD>
        <PolicyIssueType>I</PolicyIssueType>
        <PolicyNo/>
        <ClientID/>
        <ReceiptNo/>
    </PolicyHeader>
        <POS_MISP>
        <Type></Type>
        <PanNo></PanNo>
    </POS_MISP>
    <Client>
        <ClientType></ClientType>
        <CreationType></CreationType>
        <Salutation>MR</Salutation>
        <FirstName>Test</FirstName>
        <LastName>Test</LastName>
        <DOB></DOB>
        <Gender></Gender>
        <MaritalStatus></MaritalStatus>
        <Occupation></Occupation>
        <PANNo></PANNo>
        <GSTIN/>
        <AadharNo/>
        <CKYCNo></CKYCNo>
        <CKYCRefNo></CKYCRefNo>
        <EIANo></EIANo>
        <Address1>
            <AddrLine1>Test Address</AddrLine1>
            <AddrLine2></AddrLine2>
            <AddrLine3/>
            <Landmark/>
            <Pincode>${data.Client.Address1.Pincode}</Pincode>
            <City>Test City</City>
            <State></State>
            <Country>IND</Country>
            <AddressType>R</AddressType>
            <HomeTelNo/>
            <OfficeTelNo/>
            <FAXNO/>
            <MobileNo></MobileNo>
            <EmailAddr></EmailAddr>
        </Address1>
        <Address2>
            <AddrLine1>Test Address</AddrLine1>
            <AddrLine2></AddrLine2>
            <AddrLine3/>
            <Landmark/>
            <Pincode>${data.Client.Address1.Pincode}</Pincode>
            <City></City>
            <State></State>
            <Country>IND</Country>
            <AddressType>R</AddressType>
            <HomeTelNo/>
            <OfficeTelNo/>
            <FAXNO/>
            <MobileNo></MobileNo>
            <EmailAddr></EmailAddr>
        </Address2>
        <VIPFlag>N</VIPFlag>
        <VIPCategory/>
    </Client>
    <Receipt>
        <UniqueTranKey></UniqueTranKey>
        <CheckType/>
        <BSBCode/>
        <TransactionDate></TransactionDate>
        <ReceiptType></ReceiptType>
        <Amount></Amount>
        <TCSAmount/>
        <TranRefNo></TranRefNo>
        <TranRefNoDate></TranRefNoDate>
    </Receipt>
    <Risk>
        <IsfgEmployee>N</IsfgEmployee>
        <Duration>${data.Risk.Duration}</Duration>
        <Installments>FULL</Installments>
        <PaymentType/>
        <Discount/>
        <CoverageClass>Individual</CoverageClass>
        <CoverageClassCode>PAL</CoverageClassCode>
        <Plan>Accident Suraksha</Plan>
        <Unit>1</Unit>
        <Claimfreeyears>0</Claimfreeyears>
        <OccupationClass>Y</OccupationClass>
        <CumulativeBonusAmount/>
        <AlreadyPAPolicy/>
        <AdditionalRemarks/>
        <PendingRemarks/>
        <BranchReferenceID/>
        <FGBankBranchStaffID/>
        <BankStaffID/>
        <BankCustomerID/>
        <BancaChannel/>
        <PartnerRefNo/>
        <PayorID/>
        <PayerName/>
        ${data.BeneficiaryDetails.Member.map(member => `
        <Insured>
            <FirstName></FirstName>
            <LastName></LastName>
            <NomineeName></NomineeName>
            <NomineesRelationshipWithInsured></NomineesRelationshipWithInsured>
            <Gender></Gender>
            <Birthdate>${member.InsuredDob}</Birthdate>
            <Age>28</Age>
            <AgeIndicater>Y</AgeIndicater>
            <Nationality>IND</Nationality>
            <OccupationCode>${member.InsuredOccpn}</OccupationCode>
            <RelationshipWithApplicant>${member.Relation}</RelationshipWithApplicant>
            <AppointeeName/>
            <AppointeeRelationshipwithNominee/>
            <PreExistingDisease>N</PreExistingDisease>
            <AnnualIncome>${member.AnnualIncome}</AnnualIncome>
            <PrimaryCoverReq>Y</PrimaryCoverReq>
            <Exclusion/>
            <CumulativeBonus/>
            ${generateNomineeDetailsXML(member, member.MemberId - 1, true)}
            <PrimaryCover>
                <Cover>
                    <CoverCode>AD</CoverCode>
                    <CoverName>Accidental death</CoverName>
                    <SumInsured>${member.SumInsured_AD}</SumInsured>
                    <Premium/>
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>PP</CoverCode>
                    <CoverName>Permanent Partial Disabilement</CoverName>
                    <SumInsured>${member.SumInsured_PP}</SumInsured>
                    <Premium/>
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>PT</CoverCode>
                    <CoverName>Permanent Total Disablement</CoverName>
                    <SumInsured>${member.SumInsured_PT}</SumInsured>
                    <Premium/>
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>TT</CoverCode>
                    <CoverName>Temporary Total Disablement</CoverName>
                    <SumInsured>${member.SumInsured_TTD}</SumInsured>
                    <Premium/>
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
            </PrimaryCover>
            <AdditionalCoverReq>${member.AdditionalCover.length > 0 ? 'Y' : 'N'}</AdditionalCoverReq>
            <AdditionalCover>
                ${member.AdditionalCover.map(cover => `
                    <Cover>
                        <CoverCode>${cover.coverCode}</CoverCode>
                        <CoverName>${coverMapping[cover.coverCode].name}</CoverName>
                        <SumInsured>${cover.sumInsured}</SumInsured>
                        <Premium/>
                        <CoverType>${coverMapping[cover.coverCode].type}</CoverType>
                        <Times>*</Times>
                        <Benefit>*</Benefit>
                    </Cover>
                `).join('')}
            </AdditionalCover>
        </Insured>
        `).join('')}
        <GrossPremium></GrossPremium>
        <ServiceTax></ServiceTax>
    </Risk>
</Root>]]></tem:XML>
            </tem:CreatePolicy>
        </soapenv:Body>
    </soapenv:Envelope>`;
};


const parseEMIDetails = (policyDetails) => {
    try {
        const emiDetails = [];
        const rawEmiDetails = policyDetails?.Root?.Policy?.NewDataSet?.EMIDetails || [];

        // Ensure rawEmiDetails is an array
        const emiArray = Array.isArray(rawEmiDetails) ? rawEmiDetails : [rawEmiDetails];

        emiArray.forEach(emi => {
            // Remove whitespace and parse as float with exact precision
            const cleanAndParse = (value) => {
                if (!value) return 0;
                return parseFloat(value.toString().trim());
            };

            emiDetails.push({
                duration: emi.Duration,
                fullPayment: {
                    premium: cleanAndParse(emi.FullPaymentPremium),
                    tax: cleanAndParse(emi.FullPaymentTax),
                    totalAmount: cleanAndParse(emi.TotalFullPayment)
                },
                discounts: {
                    family: {
                        percentage: cleanAndParse(emi.FamilyDiscountPerc),
                        amount: cleanAndParse(emi.FamilyDiscountAmt)
                    },
                    longTerm: {
                        percentage: cleanAndParse(emi.LongTermDiscountPercent),
                        amount: cleanAndParse(emi.LongTermDiscountAmount)
                    },

                }
            });
        });

        return emiDetails;
    } catch (error) {
        console.error('Error parsing EMI details:', error);
        return [];
    }
};

/* // Add this function to get occupation api_name from ID
const getOccupationApiName = async (occupationId) => {
    try {
        const occupation = await knex('pick_list')
            .where({
                type_name: 'Future_genrali_Occupation',
                id: occupationId
            })
            .first();
        
        return occupation ? occupation.api_name : null;
    } catch (error) {
        console.error('Error fetching occupation:', error);
        throw new Error('Failed to fetch occupation details');
    }
}; */

const sendSOAPRequest = async (data) => {
    try {
        const membersWithOccupation = data.BeneficiaryDetails.Member;

        // Modify the data before creating SOAP envelope
        const modifiedData = {
            ...data,
            Product: data.Product,
            VendorCode: 'webagg',
            VendorUserId: 'webagg',
            PolicyHeader: {
                ...data.PolicyHeader,
                AgentCode: '60000272',
                BranchCode: '10',
                MajorClass: 'PAC',
                ContractType: 'PAL'
            },
            BeneficiaryDetails: {
                ...data.BeneficiaryDetails,
                Member: membersWithOccupation
            }
        };

        const soapEnvelope = createSoapEnvelope(modifiedData);

        console.log(soapEnvelope);
        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };

        const response = await axios.post(SOAP_API_URL, soapEnvelope, { headers });

        // Parse the response
        const parser = new xml2js.Parser({ explicitArray: false });
        const parsedResponse = await parser.parseStringPromise(response.data);

        const createPolicyResult = parsedResponse['s:Envelope']['s:Body']
            .CreatePolicyResponse.CreatePolicyResult;

        // Parse the policy details
        let policyDetails;
        try {
            if (createPolicyResult) {
                policyDetails = await parser.parseStringPromise(createPolicyResult);

                // Parse EMI details
                const emiDetails = parseEMIDetails(policyDetails);

                // Get premium details from NewDataSet.Premium with proper parsing
                const premiumDetails = policyDetails?.Root?.Policy?.NewDataSet?.Premium;
                const cleanAndParse = (value) => {
                    if (!value) return 0;
                    return parseFloat(value.toString().trim());
                };

                return {
                    status: 'success',
                    data: {
                        /* premium: {
                            totalPremium: cleanAndParse(premiumDetails?.TotalPremium),
                            totalPremiumWithoutDiscount: cleanAndParse(premiumDetails?.TotalPremiumWithoutDiscount),
                            serviceTax: cleanAndParse(premiumDetails?.ServiceTax),
                            premiumWithServiceTax: cleanAndParse(premiumDetails?.PremiumWithServiceTax),
                            discounts: {
                                percentage: cleanAndParse(premiumDetails?.DiscountPercent),
                                amount: cleanAndParse(premiumDetails?.DiscountAmount)
                            }
                        }, */
                        emiDetails: emiDetails
                    },
                    timestamp: new Date().toISOString()
                };
            }
        } catch (parseError) {
            console.error('Error parsing policy details:', parseError);
        }

        return {
            status: 'error',
            message: 'Failed to parse policy details',
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error('SOAP Request Error:', error);
        throw new Error(error.message);
    }
};

module.exports = {
    sendSOAPRequest
}; 