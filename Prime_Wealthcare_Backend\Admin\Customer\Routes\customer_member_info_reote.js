const express = require('express');
const customerMemberController = require('../Controllers/customer_member_info_controller');
const router = express.Router();

// Route to create a new customer info
router.post('/', customerMemberController.createCustomerMember);

router.get('/', customerMemberController.getCustomerMember);

router.get('/:id', customerMemberController.getCostomerMemberById);

router.get('/customer_id/:id', customerMemberController.getCostomerMemberByCustomerId);

router.put('/:id', customerMemberController.updateCustomerMember);

router.delete('/:id', customerMemberController.deleteCustomerMember);

module.exports = router;