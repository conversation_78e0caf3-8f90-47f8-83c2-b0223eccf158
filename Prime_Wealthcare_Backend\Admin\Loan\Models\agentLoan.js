const knexConfig = require('../../../knexfile');
const { getCurrentTimestamp, formatDateToDDMMYYYY } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new agent loan and its associated EMIs
const create = async (loanData) => {
    try {
        const loanResult = await db('agent_loan').insert(loanData);
        const loanId = loanResult[0];
        return loanId;
    } catch (error) {
        console.error("Error creating loan:", error);
        throw error;
    }
};

// Find all agent loans with agent details and loan type information
const findAgentLoans = async () => {
    try {
        const loans = await db('agent_loan')
            .leftJoin('pick_list', 'agent_loan.loan_type', '=', 'pick_list.id')
            .leftJoin('agents', 'agent_loan.agent_id', '=', 'agents.id')
            .select(
                'agent_loan.id',
                'agent_loan.loan_id',
                'agent_loan.admin_approval',
                'agent_loan.loan_amount',
                'agent_loan.paid_amount',
                db.raw('agent_loan.loan_amount - COALESCE(agent_loan.paid_amount, 0) as balance_amount'),
                'agent_loan.emi',
                'agent_loan.issue_date',
                'agent_loan.tenure',
                'agent_loan.end_date',
                'agent_loan.status',
                'pick_list.label_name as loan_type',
                db.raw("CONCAT(SUBSTRING_INDEX(agents.full_name, ' ', 1), ' (', agents.agent_id, ')') as agent_name")
            )
            .distinct('agent_loan.id')
            .orderBy('id', 'desc');

        // Format tenure from months to years and months
        return loans.map(loan => {
            return {
                ...loan,
                tenure: `${loan.tenure} months`
            };
        });
    } catch (error) {
        throw error;
    }
};
// Find all EMIs related to a given loan ID
const findAgentLoanEmi = async (loanId) => {
    try {
        return await db('agent_loan_emi').where({ agent_loan_id: loanId });
    } catch (error) {
        throw error;
    }
};

// Find a loan by ID and return loan + agent data
const findAgentLoanById = async (id) => {
    try {
        return await db('agent_loan').
            // join('agents', 'agent_loan.agent_id', '=', 'agents.id').
            // join('agent_loan_emi', 'agent_loan.id', '=', 'agent_loan_emi.agent_loan_id').
            where('agent_loan.id', id).first();
    } catch (error) {
        throw error;
    }
};

// Update loan by ID
const updateLoan = async (id, data) => {
    try {
        if (data.admin_approval === 'APPROVED') {
            //Find the approved agent loans
            const agentLoans = await db('agent_loan').where('admin_approval', 'APPROVED');
            const loan_id = "PWS-AG-" + String(agentLoans.length + 1).padStart(5, '0');
            const updatedData = {
                ...data,
                loan_id: loan_id,
                updated_at: getCurrentTimestamp(),
            }
            // Update the loan
            const agent_loan = await db('agent_loan').where({ id }).update(updatedData);
            if (agent_loan) {
                // Create the EMIs
                let emiData = [];
                const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                let loan_amount = data.loan_amount;
                const emi_amount = Math.round(loan_amount / updatedData.tenure);
                let currentYear = new Date(updatedData.start_date).getFullYear(); // Get current year from start_date
                for (let i = 0; i < updatedData.tenure; i++) {
                    const emi_element = {
                        month: months[(new Date(updatedData.start_date).getMonth() + i + 1) % 12],
                        agent_loan_id: loan_id,
                        year: currentYear,
                        emi_amount: i !== updatedData.tenure - 1 ? emi_amount : loan_amount,
                        created_by: updatedData.updated_by,
                        updated_by: updatedData.updated_by,
                    }
                    emiData.push(emi_element);
                    loan_amount -= emi_amount;
                    if (months[(new Date(updatedData.start_date).getMonth() + i) % 12] === 'December') {
                        currentYear++;
                    }
                }
                const checkIfEmiExists = await db('agent_loan_emi').where({ agent_loan_id: loan_id });
                if (checkIfEmiExists.length === 0) {
                    return await db('agent_loan_emi').insert(emiData);
                }
            }
        } else if (data.admin_approval === 'REJECTED') {
            return await db('agent_loan').where({ id }).update({ status: 0, admin_approval: 'REJECTED', updated_at: getCurrentTimestamp() });
        }
    } catch (error) {
        throw error;
    }
};

// Update EMI by ID
const updateEmi = async (id, data) => {
    try {
        const { paid_amount, ...emiData } = data;
        const agentLoan = await db('agent_loan').where({ loan_id: emiData.agent_loan_id }).update({ paid_amount, updated_at: getCurrentTimestamp(), updated_by: data.updated_by });
        if (agentLoan) {
            return await db('agent_loan_emi').where({ id }).update({ ...emiData, updated_at: getCurrentTimestamp() });
        }
    } catch (error) {
        throw error;
    }
};

// Soft delete a loan by ID
const deleteLoan = async (id) => {
    try {
        return await db('agent_loan').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
    } catch (error) {
        throw error;
    }
};

// Soft delete an EMI by ID
const deleteEmi = async (id) => {
    try {
        return await db('agent_loan_emi').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findAgentLoans,
    findAgentLoanEmi,
    findAgentLoanById,
    updateLoan,
    updateEmi,
    deleteLoan,
    deleteEmi,
};