import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Typography, Grid, Avatar } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { createCustomerDocument, deleteCustomerDocument, getAllCustomer, getAreaById, getAreasByPincodeAndCity, getCustomerAddressByCustomerId, getCustomerById, getCustomerDocumentsByCustomerId, getMemberByCustomerId, updateCustomerDocument } from '../../redux/actions/action';
import DetailsTable from '../../components/table/DetailsTable';
import Dropdown from '../../components/table/DropDown';
import CustomTextField from '../../components/CustomTextField';
import CustomFileUpload from '../../components/CustomFileUpload';

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-GB').format(date);
}

function CustomerDocumentationUpload() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const documentTypeOptions = useSelector(state => state.pickListReducer.documentTypeOptions);
    const customerInformation = useSelector(state => state.customerReducer.customerDetails)
    const customerAddress = useSelector(state => state.customerAddressReducer.currentAddress);
    const area = useSelector(state => state.areaManagementReducer.area);
    const customerDocuments = useSelector(state => state.customerDocumentationReducer.customerDocumentation);
    const [members, setMembers] = useState([]);
    const [info, setInfo] = useState('');
    const [editMode, setEditMode] = useState(false);
    const [editId, setEditId] = useState(null);

    const [formData, setFormData] = useState({
        member_id: '',
        document_type_id: '',
        document_id: '',
        document_path: ''
    });

    const [formErrors, setFormErrors] = useState({
        member_id: '',
        document_type_id: '',
        document_id: '',
        document_path: ''
    });

    const [tableData, setTableData] = useState([]);

    useEffect(() => {
        if (customerDocuments && Array.isArray(customerDocuments)) {
            const mappedData = customerDocuments.map(doc => ({
                id: doc.id,
                Member_Name: doc.full_name || 'N/A',
                Relation: doc.relation || 'N/A',
                Document_Type: doc.document_type || 'N/A',
                Document_Number: doc.document_id || 'N/A',
                Attachment: doc.document_path || 'N/A',
                Assigned_To: doc.created_by || 'N/A',
                Created_At: formatDate(doc.created_at),
                Updated_At: formatDate(doc.updated_at)
            }));
            setTableData(mappedData);
        }
    }, [customerDocuments]);

    useEffect(() => {
        dispatch(getAllCustomer());
    }, [dispatch])

    useEffect(() => {
        if (customerAddress) {
            let area_id = null;
            if (customerAddress?.used_address === 'current') {
                dispatch(getAreasByPincodeAndCity(customerAddress?.current_pincode, customerAddress?.current_city))
                area_id = customerAddress?.current_area;
            } else {
                dispatch(getAreasByPincodeAndCity(customerAddress?.permanent_pincode, customerAddress?.permanent_city));
                area_id = customerAddress?.permanent_area;
            }
            if (area_id) {
                dispatch(getAreaById(area_id));
            }
        }
    }, [customerInformation, customerAddress])

    useEffect(() => {
        if (id) {
            dispatch(getCustomerById(id));
            dispatch(getCustomerAddressByCustomerId(id));
            dispatch(getMemberByCustomerId(id)).then(action => {
                if (action.payload) {
                    setMembers(action.payload);
                }
            });
            dispatch(getCustomerDocumentsByCustomerId(id));
            dispatch(getMemberByCustomerId(id))
                .then((action) => {
                    if (action.payload && Array.isArray(action.payload)) {
                        const activeMembers = action.payload.filter(member => member.status === 1);
                        setMembers(activeMembers);
                    }
                })
                .catch(error => {
                    console.error('Error fetching members:', error);
                });
        }
    }, [id, dispatch])

    const handleAdd = () => {
        navigate('/dashboard/customer-master');
    };
    const handleDelete = (id) => {
        const selectedDocument = tableData.find(item => item.id === id);
        const document = documentTypeOptions.find(option => option.label_name === selectedDocument.Document_Type);
        dispatch(deleteCustomerDocument(id));
    }

    const handleCancel = () => {
        navigate(`/dashboard/customer-follow-up/${id}`);
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        if (value === ' ') {
            setFormErrors(prev => ({
                ...prev,
                [name]: 'Do not start with a whitespace character'
            }));
            return;
        }
        let data = value;
        if (name === 'member_id') {
            setFormData(prev => ({
                ...prev,
                member_name: members.find(member => member.id === value)?.full_name
            }));
        }

        if (name === 'document_type_id') {
            const selectedDoc = documentTypeOptions.find(option => option.id === value);
            if (!selectedDoc) return;
            const document = selectedDoc.api_name;
            const label_name = selectedDoc.label_name.replace(/\s+/g, '_');
            setFormData(prev => ({
                ...prev,
                document_id: '',
                document_name: label_name
            }));
            switch (document) {
                case 'DOC_Passport':
                    setInfo('Example: ********');
                    break;
                case 'DOC_DrivingLicense':
                    setInfo('Example: DL-0420190000000');
                    break;
                case 'DOC_AadharCard':
                    setInfo('Example: 1234-5678-9012');
                    break;
                case 'DOC_PANCard':
                    setInfo('Example: **********');
                    break;
                case 'DOC_VoterID':
                    setInfo('Example: **********');
                    break;
                default:
                    setInfo('');
            }
        }

        if (name === 'document_id') {
            const selectedDoc = documentTypeOptions.find(option => option.id === formData.document_type_id);
            if (!selectedDoc) return;

            const document = selectedDoc.api_name;
            data = data.toUpperCase();
            switch (document) {
                case 'DOC_Passport':
                    if (value.length > 8) return;
                    const passportLetter = value.slice(0, 1);
                    const passportNumbers = value.slice(1);

                    if (passportLetter && !/^[A-Za-z]$/.test(passportLetter)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'First character must be a letter'
                        }));
                        return;
                    }

                    if (passportNumbers && !/^\d*$/.test(passportNumbers)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'Must be followed by numbers only'
                        }));
                        return;
                    }

                    data = value.toUpperCase();
                    break;
                case 'DOC_DrivingLicense':
                    if (value.length > 16) return;
                    if (value.length <= 2) {
                        if (!/^[A-Z]*$/.test(value.toUpperCase())) {
                            setFormErrors(prevErrors => ({
                                ...prevErrors,
                                [name]: 'First two characters must be letters'
                            }));
                            return;
                        }
                        data = value.toUpperCase();
                    } else {
                        const letters = value.slice(0, 2).toUpperCase();
                        const rest = value.slice(2).replace(/[^0-9]/g, '');
                        if (!/^[A-Z]{2}$/.test(letters)) {
                            setFormErrors(prevErrors => ({
                                ...prevErrors,
                                [name]: 'First two characters must be letters'
                            }));
                            return;
                        }
                        if (!/^\d*$/.test(rest)) {
                            setFormErrors(prevErrors => ({
                                ...prevErrors,
                                [name]: 'Characters after hyphen must be numbers'
                            }));
                            return;
                        }
                        data = `${letters}-${rest}`;
                    }
                    break;
                case 'DOC_AadharCard':
                    if (value.length > 14) return;
                    if (!/^\d*$/.test(value.replace(/-/g, ''))) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'Must contain only numbers'
                        }));
                        return;
                    }

                    // Format with dashes
                    const cleanValue = value.replace(/-/g, '');
                    if (cleanValue.length > 4) {
                        data = `${cleanValue.slice(0, 4)}-${cleanValue.slice(4)}`;
                        if (cleanValue.length > 8) {
                            data = `${data.slice(0, 9)}-${cleanValue.slice(8)}`;
                        }
                    } else {
                        data = cleanValue;
                    }
                    break;
                case 'DOC_PANCard':
                    if (value.length > 10) return;
                    const firstFive = value.slice(0, 5);
                    const middleFour = value.slice(5, 9);
                    const lastChar = value.slice(9, 10);

                    if (!/^[A-Za-z]*$/.test(firstFive)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'First 5 characters must be letters'
                        }));
                        return;
                    }

                    if (middleFour && !/^\d*$/.test(middleFour)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'Characters 6-9 must be numbers'
                        }));
                        return;
                    }

                    if (lastChar && !/^[A-Za-z]$/.test(lastChar)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'Last character must be a letter'
                        }));
                        return;
                    }

                    data = value.toUpperCase();
                    break;
                case 'DOC_VoterID':
                    if (value.length > 10) return;
                    const voterIdLetters = value.slice(0, 3);
                    const voterIdNumbers = value.slice(3);

                    if (voterIdLetters && !/^[A-Za-z]*$/.test(voterIdLetters)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'First three characters must be letters'
                        }));
                        return;
                    }

                    if (voterIdNumbers && !/^\d*$/.test(voterIdNumbers)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'Must be followed by numbers only'
                        }));
                        return;
                    }

                    data = value.toUpperCase();
                    break;
                default:
                    break;
            }
        }
        setFormData(prev => ({
            ...prev,
            [name]: data,
        }));
        setFormErrors(prev => ({
            ...prev,
            [name]: ''
        }));
    };

    const handleFileSelect = (file) => {
        setFormData(prev => ({
            ...prev,
            document_path: file
        }));
        setFormErrors(prev => ({
            ...prev,
            document_path: ''
        }));
    };

    const handleEdit = (id) => {
        const documentToEdit = customerDocuments.find(doc => doc.id === id);
        if (documentToEdit) {
            setFormData({
                member_id: documentToEdit.member_id,
                document_type_id: documentToEdit.document_type_id,
                document_id: documentToEdit.document_id,
                document_path: documentToEdit.document_path,
                document_name: documentToEdit.document_name
            });
            setEditMode(true);
            setEditId(id);
        }
        setFormErrors({
            member_id: '',
            document_type_id: '',
            document_id: '',
            document_path: ''
        });
    };

    const handleDocumentAdd = async () => {
        const errors = {};
        if (!formData.member_id) errors.member_id = 'Member is required';
        if (!formData.document_type_id) errors.document_type_id = 'Document type is required';
        if (!formData.document_id) errors.document_id = 'Document ID is required';
        if (!formData.document_path) errors.document_path = 'File is required';

        if (Object.keys(errors).length > 0) {
            setFormErrors(errors);
            return;
        }

        try {
            const documentData = new FormData();
            const documentType = documentTypeOptions.find(option => option.id === formData.document_type_id)?.label_name;

            // Add your fields
            documentData.append('customer_id', Number(id));
            documentData.append('member_id', formData.member_id);
            documentData.append('document_type_id', formData.document_type_id);
            documentData.append('document_type', documentType);
            documentData.append('document_id', formData.document_id);

            // Extract the file from the object structure
            if (formData.document_path && formData.document_path.image && formData.document_path.image instanceof File) {
                // This is the correct approach - extract the file from the wrapper object
                documentData.append('document_path', formData.document_path.image);
                // Use the actual file name
                documentData.append('document_name', formData.document_path.image.name);
            } else if (formData.document_path instanceof File) {
                // Direct file object
                documentData.append('document_path', formData.document_path);
                documentData.append('document_name', formData.document_path.name);
            } else {
                console.error("Invalid file object:", formData.document_path);
                return; // Don't proceed with submission
            }

            if (editMode) {
                await dispatch(updateCustomerDocument({ id: editId, documentData }));
                dispatch(getCustomerDocumentsByCustomerId(id));
                setEditMode(false);
                setEditId(null);
            } else {
                await dispatch(createCustomerDocument(documentData));
                dispatch(getCustomerDocumentsByCustomerId(id));
            }

            // Reset form with empty values
            setFormData({
                member_id: '',
                document_type_id: '',
                document_id: '',
                document_path: ''
            });
            setInfo('');
        } catch (error) {
            console.error('Error handling document:', error);
        }
    };

    const handleFormReset = () => {
        setFormData({
            member_id: '',
            document_type_id: '',
            document_id: '',
            document_path: ''
        });
        setFormErrors({
            member_id: '',
            document_type_id: '',
            document_id: '',
            document_path: ''
        });
        setEditMode(false);
        setEditId(null);
        setInfo('');
    };

    const renderAvatar = () => {
        const { photo, first_name, last_name } = customerInformation || {};
        const initials = first_name && last_name ? `${first_name[0]}${last_name[0]}`.toUpperCase() : 'N/A';
        return (
            <Avatar
                alt={`${first_name} ${last_name}`}
                src={photo || ''}
                sx={{ width: photo ? 100 : 50, height: photo ? 100 : 50 }}
            >
                {!photo && initials}
            </Avatar>
        );
    };

    const isFormValid = () => {
        return formData.member_id &&
            formData.document_type_id &&
            formData.document_id &&
            formData.document_path;
    };

    const getAvailableDocumentTypes = (memberId) => {
        if (!memberId || editMode) return documentTypeOptions;

        // Check if customerDocuments exists and is an array
        if (!Array.isArray(customerDocuments)) return documentTypeOptions;

        // Get all documents already assigned to this member
        const assignedDocs = customerDocuments.filter(doc => doc.member_id === memberId)
            .map(doc => doc.document_type_id);

        // Filter out document types that are already assigned
        return documentTypeOptions.filter(option =>
            !assignedDocs.includes(option.id)
        );
    };

    return (
        <Box sx={{
            paddingLeft: { xs: '20px', md: '40px' },
            paddingRight: { xs: '20px', md: '40px' },
            paddingBottom: '40px'
        }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Grid container spacing={{ xs: 1, md: 2 }} style={{ display: 'flex', alignItems: 'center' }}>
                    <Grid item xs={12} md={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName='Contacts' pageName="Attachments > Create" />
                    </Grid>

                    <Grid item xs={12} md={4} sx={{
                        display: 'flex',
                        justifyContent: { xs: 'center', md: 'flex-end' },
                        flexWrap: { xs: 'wrap', md: 'nowrap' },
                        gap: { xs: 1, md: 0 }
                    }}>
                        <Button
                            onClick={handleCancel}
                            sx={{
                                minWidth: { xs: '45%', md: '100px' },
                                mx: { xs: 0.5, md: 1 },
                                borderColor: 'red',
                                color: 'red',
                                textTransform: 'none'
                            }}
                        >
                            Cancel
                        </Button>
                    </Grid>
                </Grid>

                <Box display="flex"
                    flexDirection={{ xs: 'column', sm: 'row' }}
                    p={2}
                    sx={{
                        padding: { xs: '0.5rem', sm: '1rem' },
                        borderBlock: '1px solid black',
                        gap: { xs: 2, sm: 0 }
                    }}
                >
                    <Box sx={{
                        mr: { xs: 0, sm: 2 },
                        display: 'flex',
                        justifyContent: { xs: 'center', sm: 'flex-start' }
                    }}>
                        {renderAvatar()}
                    </Box>

                    <Box sx={{ flex: 1 }}>
                        <Grid container spacing={{ xs: 1, sm: 2 }} sx={{
                            alignItems: 'center',
                            textAlign: { xs: 'center', sm: 'left' }
                        }}>
                            <Grid item xs={12} sm={6} md={4}>
                                <Typography noWrap sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                                    <strong>Full Name:</strong> {customerInformation?.first_name} {customerInformation?.last_name}
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4}>
                                <Typography noWrap sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                                    <strong>Mobile:</strong> {customerInformation?.mobile || customerInformation?.whatsApp_number}
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4}>
                                <Typography noWrap sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                                    <strong>Email:</strong> {customerInformation?.email}
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4}>
                                <Typography noWrap sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                                    <strong>Area: </strong>{area?.area || null}
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4}>
                                <Typography noWrap sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                                    <strong>City:</strong> {customerAddress?.used_address === 'permanent' ? customerAddress?.permanent_city : customerAddress?.current_city}
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4}>
                                <Typography noWrap sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                                    <strong>State:</strong> {customerAddress?.used_address === 'permanent' ? customerAddress?.permanent_state : customerAddress?.current_state}
                                </Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </Box>

                <Box sx={{
                    width: '100%',
                    backgroundColor: '#f0f0f0',
                    p: { xs: 1, sm: 2 },
                    borderRadius: '4px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}>
                    <Typography variant="h6" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                        Documentation Details
                    </Typography>
                    <Grid item xs={12} md={4} sx={{
                        display: 'flex',
                        justifyContent: { xs: 'center', md: 'flex-end' },
                        flexWrap: { xs: 'wrap', md: 'nowrap' },
                        gap: { xs: 1, md: 0 }
                    }}>
                        <Button
                            onClick={handleDocumentAdd}
                            disabled={!isFormValid()}
                            sx={{
                                minWidth: { xs: '45%', md: '100px' },
                                mx: { xs: 0.5, md: 1 },
                                borderColor: 'primary.main',
                                textTransform: 'none'
                            }}
                        >
                            {editMode ? 'Save' : 'Add'}
                        </Button>
                        {editMode || formData.member_id || formData.document_type_id || formData.document_id || formData.document_path ? (
                            <Button
                                onClick={handleFormReset}
                                sx={{
                                    minWidth: { xs: '45%', md: '100px' },
                                    mx: { xs: 0.5, md: 1 },
                                    borderColor: 'red',
                                    color: 'red',
                                    textTransform: 'none'
                                }}
                            >
                                Cancel
                            </Button>
                        ) : null}
                    </Grid>
                </Box>

                <Grid container spacing={{ xs: 1, sm: 2 }} sx={{ p: { xs: 1, sm: 2 } }}>
                    <Grid item xs={12} sm={6} md={3}>
                        <Dropdown
                            label="Member Name"
                            name="member_id"
                            options={members?.map(member => ({
                                label: member.full_name,
                                value: member.id
                            }))}
                            value={formData.member_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.member_id || ''}
                            required
                            disabled={editMode}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    width: '100%'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <Dropdown
                            label="Document Type"
                            name="document_type_id"
                            options={getAvailableDocumentTypes(formData.member_id).map(option => ({
                                label: option.label_name,
                                value: option.id
                            }))}
                            value={formData.document_type_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.document_type_id || ''}
                            required
                            disabled={editMode || !formData.member_id}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    width: '100%'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            name="document_id"
                            label="Document Id Number"
                            value={formData.document_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.document_id || ''}
                            isRequired
                            disabled={!formData.document_type_id}
                            info={info || ''}
                            sx={{
                                width: '100%'
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomFileUpload
                            name="document_path"
                            section_name="document"
                            accept=".jpeg,.png,.jpg,.pdf"
                            label="Upload File"
                            insertedFile={!formData.document_path ? null : {
                                section_name: 'document',
                                name: 'File',
                                url: formData.document_path
                            }}
                            onFileSelect={handleFileSelect}
                            helperText={formErrors.document_path || 'Upload in Jpeg, Png, Jpg, PDF format'}
                            error={!!formErrors.document_path}
                            isRequired
                            sx={{
                                width: '100%'
                            }}
                        />
                    </Grid>
                </Grid>

                <Box sx={{
                    width: '100%',
                    overflowX: 'auto',
                    p: { xs: 1, sm: 2 }
                }}>
                    <DetailsTable
                        tableHeadings={[
                            "Member_Name",
                            'Relation',
                            'Document_Type',
                            'Document_Number',
                            'Attachment',
                            'Assigned_To',
                            'Created_At',
                            'Updated_At',
                        ]}
                        tableData={tableData}
                        noDataMessage="No data present"
                        open={true}
                        page={0}
                        rowsPerPage={10}
                        onDelete={editMode ? null : handleDelete}
                        onEdit={editMode ? null : handleEdit}
                        disabled={editMode}
                        sx={{
                            '& .MuiTable-root': {
                                minWidth: 650
                            }
                        }}
                    />
                </Box>

                <Grid container spacing={{ xs: 1, sm: 2 }} sx={{ p: { xs: 1, sm: 2 } }}>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            name="created_by"
                            label="Created By"
                            value={customerInformation?.created_by || ''}
                            fullWidth
                            isRequired
                            disabled
                            sx={{
                                width: '100%'
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            name="created_at"
                            label="Created At"
                            value={formatDate(customerInformation?.created_at) || ''}
                            fullWidth
                            isRequired
                            disabled
                            sx={{
                                width: '100%'
                            }}
                        />
                    </Grid>
                </Grid>
            </Box>
        </Box>
    );
}

export default CustomerDocumentationUpload;
