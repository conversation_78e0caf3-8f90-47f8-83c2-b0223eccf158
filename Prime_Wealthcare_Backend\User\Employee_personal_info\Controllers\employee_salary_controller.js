const EmployeeSalary = require('../Models/employee_salary_model');

exports.createEmployeeSalary = async (req, res) => {
    try {
        const data = req.body;
        const result = await EmployeeSalary.create(data);
        res.status(201).json(result);
    } catch (error) {
        res.status(500).json({ message: "Error creating EmployeeSalary", error });
    }
}

exports.getAllEmployeeSalary = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await EmployeeSalary.getAll(id);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ message: "Error retrieving EmployeeSalary", error });
    }
}

exports.getEmployeeSalaryByEmployeeId = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await EmployeeSalary.findByEmployeeId(id);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ message: "Error retrieving EmployeeSalary", error });
    }
}

exports.getEmployeeSalaryById = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await EmployeeSalary.findById(id);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ message: "Error retrieving EmployeeSalary", error });
    }
}

exports.updateEmployeeSalary = async (req, res) => {
    try {
        const { id } = req.params;
        console.log('updating the employee salary')
        const data = req.body;
        const result = await EmployeeSalary.update(id, data);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ message: "Error updating EmployeeSalary", error });
    }
}

exports.deleteEmployeeSalary = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await EmployeeSalary.deleteById(id);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ message: "Error deleting EmployeeSalary", error });
    }
}