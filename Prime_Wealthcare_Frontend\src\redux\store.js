import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from 'redux-persist';
import { combineReducers } from 'redux';
import storage from 'redux-persist/lib/storage';
import roleManagementReducer from "./slices/master/roleManagementSlice";
import areaManagementReducer from "./slices/master/areaManagementSlice";
import mainProductReducer from "./slices/master/mainProductSlice";
import loading from './slices/loadingSlice';
import toast from './slices/toastSlice';
import insuranceBranchReducer from './slices/master/insuranceCompanyBranchSlice';
import insuranceCompanyReducer from './slices/master/insuranceCompanySlice';
import insuranceTypeReducer from './slices/master/insuranceTypeSlice';
import imfAgencyCodeReducer from './slices/master/imfAgencyCodeSlice';
import productMasterReducer from "./slices/master/productMasterSlice";
import subProductReducer from './slices/master/subProductSlice';
import imfBranchReducer from './slices/master/imfBranchSlice';
import commissionRateReducer from "./slices/master/commissionRateSlice";
import savedRowsReducer from './slices/master/savedRowsSlice';
import endorsmentTypeReducer from './slices/master/endorsmentTypeSlice'
import diseaseMasterReducer from "./slices/master/diseaseMasterSlice";
import networkReducer from "./slices/master/networkSlice";
import agentReducer from "./slices/agent/agentMasterSlice";
import pickListReducer from "./slices/pickListSlice";
import PA_occupationReducer from "./slices/PA_occupation_list_slice"
import employeeInfoReducer from './slices/User/employeeInfoSlice';
import authReducer from './slices/authSlice'
import customerReducer from './slices/customer/customer_info_slice'
import customerMemberReducer from './slices/customer/customer_member_info_slice'
import customerGroupingReducer from './slices/customer/customer_grouping_slice'
import customerAddressReducer from './slices/customer/customer_address_slice';
import customerDocumentationReducer from './slices/customer/customer_documentation_slice';
import employeeAddressReducer from './slices/User/employeeAddressSlice';
import quotationReducer from './slices/Quotation/QuotationSlice';
import proposalReducer from './slices/proposal/proposalSlice';
import paymentReducer from './slices/payment/paymentSlice';
import employeeSalaryReducer from './slices/User/employeeSalarySlice'
import pageRightsReducer from './slices/pageRightsSlice';
import users from './slices/userSlice';
import agentLoanReducer from './slices/loans/agentLoanSlice';
import employeeLoanReducer from './slices/loans/employeeLoanSlice';
import taskReducer from './slices/tasks/taskSlice';

import bankListReducer from './slices/master/bankListSlice';
import employeeBankReducer from './slices/User/employeeBankSlice';
import agentBankReducer from './slices/agent/agentBankSlice';
import rolloverMigrationReducer from './slices/proposal/rolloverMigrationSlice';
import dashboardReducer from './slices/dashboardSlice';
import reportsReducer from './slices/reports/reportsSlice'

import renewalsReducer from './slices/Renewals/Renewals_mappingSlice';
// Configure persist
const persistConfig = {
    key: 'root',
    storage,
    whitelist: [
        'auth',
        'paymentReducer'
    ]
};

// Combine all reducers using combineReducers
const rootReducer = combineReducers({
    roleManagementReducer,
    areaManagementReducer,
    mainProductReducer,
    loading,
    toast,
    insuranceCompanyReducer,
    insuranceTypeReducer,
    insuranceBranchReducer,
    imfAgencyCodeReducer,
    productMasterReducer,
    subProductReducer,
    imfBranchReducer,
    commissionRateReducer,
    savedRows: savedRowsReducer,
    endorsmentTypeReducer,
    diseaseMasterReducer,
    networkReducer,
    employeeInfoReducer,
    agentReducer,
    pickListReducer,
    auth: authReducer,
    customerReducer,
    customerMemberReducer,
    customerGroupingReducer,
    customerAddressReducer,
    customerDocumentationReducer,
    employeeAddressReducer,
    quotationReducer,
    paymentReducer,
    proposalReducer,
    PA_occupationReducer,
    employeeSalaryReducer,
    pageRights: pageRightsReducer,
    users,
    bankListReducer,
    employeeBankReducer,
    agentBankReducer,
    agentLoanReducer,
    employeeLoanReducer,
    tasks: taskReducer,
    rolloverMigrationReducer,
    dashboard: dashboardReducer,
    reports: reportsReducer,
    renewals: renewalsReducer,
});

// Create the persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE']
            }
        })
});

export const persistor = persistStore(store);
export default store;