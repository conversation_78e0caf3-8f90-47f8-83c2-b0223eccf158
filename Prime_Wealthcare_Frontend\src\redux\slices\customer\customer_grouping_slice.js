import { createSlice } from '@reduxjs/toolkit';
import { createCustomerGrouping, getAllGroups, updateGrouping } from '../../actions/action';

const customerGroupingSlice = createSlice({
    name: 'customerGrouping',
    initialState: {
        groups: [],
        loading: false, // 'idle' | 'loading' | 'succeeded' | 'failed'
        error: null,
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            // Handle createCustomerGrouping lifecycle
            .addCase(createCustomerGrouping.pending, (state) => {
                state.loading = true;
            })
            .addCase(createCustomerGrouping.fulfilled, (state, action) => {
                state.loading = false;
                state.groups.push(action.payload); // Add the new group to the list
            })
            .addCase(createCustomerGrouping.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            // Handle getAllGroups lifecycle
            .addCase(getAllGroups.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllGroups.fulfilled, (state, action) => {
                state.loading = false;
                state.groups = action.payload; // Populate the groups list
            })
            .addCase(getAllGroups.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            // Handle updateGrouping lifecycle
            .addCase(updateGrouping.pending, (state) => {
                state.loading = true;
            })
            .addCase(updateGrouping.fulfilled, (state, action) => {
                state.loading = false;
                // Find and update the group in the groups list
                const updatedGroup = action.payload;
                const index = state.groups.findIndex((group) => group.id === updatedGroup.id);
                if (index !== -1) {
                    state.groups[index] = updatedGroup;
                }
            })
            .addCase(updateGrouping.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });

    },
});

export default customerGroupingSlice.reducer;
