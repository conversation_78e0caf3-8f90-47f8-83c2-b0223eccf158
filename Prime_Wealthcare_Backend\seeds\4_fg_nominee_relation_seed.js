exports.seed = async function (knex) {
    // Delete existing entries
    await knex('fg_nominee_relation').del();

    await knex('fg_nominee_relation').insert([
        {
            type_name: 'Relation',
            api_name: 'SELF',
            label_name: 'SELF',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'SPOU',
            label_name: 'SPOUSE',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'S<PERSON>',
            label_name: 'SON',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'DAUG',
            label_name: 'DAUGHTER',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'MOTH',
            label_name: 'MOTH<PERSON>',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'FATH',
            label_name: 'FATHER',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'WIFE',
            label_name: 'WIF<PERSON>',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'HUS<PERSON>',
            label_name: 'HUSBAND',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'CHLD',
            label_name: 'CHILD',
            is_active: true,
        },
        {
            type_name: 'Relation',
            api_name: 'BROT',
            label_name: 'BROTHER',
            is_active: true,
        }
        , {
            type_name: 'Relation',
            api_name: 'SIST',
            label_name: 'SISTER',
            is_active: true,
        }
    ]);
};