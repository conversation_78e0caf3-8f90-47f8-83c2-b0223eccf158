// Ensure required modules are imported
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Define the base upload directory
const uploadDir = process.env.UPLOAD_DIR;

// Check if the base uploads directory exists; if not, create it
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage settings
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const customerId = Array.isArray(req.body.customer_id) ? req.body.customer_id[0] : req.body.customer_id;
    const customerFolder = path.join(uploadDir, 'customer', customerId);
    if (!fs.existsSync(customerFolder)) {
      fs.mkdirSync(customerFolder, { recursive: true });
    }
    cb(null, customerFolder);
  },
  filename: (req, file, cb) => {
    const data = req.body;
    const extension = path.extname(file.originalname);
    const filename = `${data.document_type}${extension}`;
    cb(null, filename);
  },
});

// Add file filter to restrict file types
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only jpg, jpeg, png, and pdf files are allowed.'), false);
  }
};

// Modify the multer setup to include more logging
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB size limit
  },
  onError: function (err, next) {
    console.error('Multer error:', err);
    next(err);
  }
});

module.exports = upload;