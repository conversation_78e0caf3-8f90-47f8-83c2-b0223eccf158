import React from 'react';
import { Dialog, DialogActions, DialogTitle, Button } from '@mui/material';

const DeletePopup = ({ open, onClose, onConfirm, modulename, message }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '376px',
          height: '210px',
          padding: '10px 0px 0px 0px',
          gap: '15px',
          borderRadius: '10px',
          position: 'relative',
          margin: 'auto',
          opacity: 1,
          borderRight: '2px solid red',
          borderLeft: '2px solid red',
          borderBottom: '4px solid red',
        },
      }}
    >
      <DialogTitle>
        {message || `Are you sure you want to deactivate ${modulename}?`}
      </DialogTitle>
      <DialogActions
        style={{
          justifyContent: 'center',
        }}
      >
        <Button
          onClick={onConfirm}
          sx={{ backgroundColor: '#528A7E', color: 'white', '&:hover': { backgroundColor: '#3f6e5e' } }}
        >
          Yes
        </Button>
        <Button
          onClick={onClose}
          sx={{ backgroundColor: 'red', color: 'white', '&:hover': { backgroundColor: '#d32f2f' } }}
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeletePopup;
