const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../knexfile');
const db = knex(knexConfig.development);
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');
// Environment variables
require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URL;
const SOAP_ACTION = process.env.SOAP_ACTION;
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

// Generate Insured XML dynamically
const generateInsuredXML = (memberData) => {


    return memberData
        .map((memberData, index) => `<Insured>
            <FirstName>${memberData.insuredName}</FirstName>
            <LastName></LastName>
            <NomineeName>ghjgh jghjghj</NomineeName>
            <NomineesRelationshipWithInsured>FATH</NomineesRelationshipWithInsured>
            <Gender></Gender>
            <Birthdate>${memberData.insuredDob}</Birthdate>
            <Age>18</Age>
            <AgeIndicater>Y</AgeIndicater>
            <Nationality>IND</Nationality>
            <OccupationCode>${memberData.occupation}</OccupationCode>
            <RelationshipWithApplicant>${memberData.relation}</RelationshipWithApplicant>
            <AppointeeName/>
            <AppointeeRelationshipwithNominee/>
            <PreExistingDisease>N</PreExistingDisease>
            <AnnualIncome>${memberData.annual_income}</AnnualIncome>
            <PrimaryCoverReq>Y</PrimaryCoverReq>
            <Exclusion/>
            <CumulativeBonus/>
            ${generateNomineeDetailsXML(memberData.nominee || {})}
            <PrimaryCover>
                <Cover>
                    <CoverCode>AD</CoverCode>
                    <CoverName>Accidental death</CoverName>
                    <SumInsured>${memberData.ad_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover >
                <Cover>
                    <CoverCode>PP</CoverCode>
                    <CoverName>Permanent Partial Disabilement</CoverName>
                    <SumInsured>${memberData.pp_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>PT</CoverCode>
                    <CoverName>Permanent Total Disablement</CoverName>
                    <SumInsured>${memberData.pt_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>TT</CoverCode>
                    <CoverName>Temporary Total Disablement</CoverName>
                    <SumInsured>${memberData.tt_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
            </PrimaryCover >
            <AdditionalCoverReq>Y</AdditionalCoverReq >
             <AdditionalCover>
                <Cover>
                    <CoverCode>RF</CoverCode>
                    <CoverName>Repatriation and Funeral Expenses</CoverName>
                    <SumInsured>${memberData.RF_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>
                ${memberData.AA_suminsured > 0 ? `
                <Cover>
                    <CoverCode>AA</CoverCode>
                    <CoverName>Adaptation Allowance</CoverName>
                    <SumInsured>${memberData.AA_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.CS_suminsured > 0 && (memberData.relation === 'SELF' || memberData.relation === 'SPOU') ? `
                <Cover>
                    <CoverCode>CS</CoverCode>
                    <CoverName>Child Education Support</CoverName>
                    <SumInsured>${memberData.CS_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.FT_suminsured > 0 ? `
                <Cover>
                    <CoverCode>FT</CoverCode>
                    <CoverName>Family Transportation Allowance</CoverName>
                    <SumInsured>${memberData.FT_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.HC_suminsured > 0 ? `
                <Cover>
                    <CoverCode>HC</CoverCode>
                    <CoverName>Hospital Cash Allowance</CoverName>
                    <SumInsured>${memberData.HC_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.ME_suminsured > 0 ? `
                <Cover>
                    <CoverCode>ME</CoverCode>
                    <CoverName>Accidental Hospitalisation</CoverName>
                    <SumInsured>${memberData.ME_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.LP_suminsured > 0 && (memberData.relation === 'SELF' || memberData.relation === 'SPOU') ? `
                <Cover>
                    <CoverCode>LP</CoverCode>
                    <CoverName>Loan Protecter</CoverName>
                    <SumInsured>${memberData.LP_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.LS_suminsured > 0 ? `
                <Cover>
                    <CoverCode>LS</CoverCode>
                    <CoverName>Life Support Benifits</CoverName>
                    <SumInsured>${memberData.LS_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.AM_suminsured > 0 ? `
                <Cover>
                    <CoverCode>AM</CoverCode>
                    <CoverName>Accidental Medical Expenses</CoverName>
                    <SumInsured>${memberData.AM_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.BB_suminsured > 0 ? `
                <Cover>
                    <CoverCode>BB</CoverCode>
                    <CoverName>Broken Bones</CoverName>
                    <SumInsured>${memberData.BB_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
            </AdditionalCover>
        </Insured > `)
        .join('');
};
const SOAP_BODY = (uid, quotationData, insuredXML, coverageClass) => `
  <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
            <tem:Product>PA</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
    <Uid>${uid}</Uid>
    <VendorCode>${VENDOR_CODE}</VendorCode>
   <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
    <SentToOutSourcePrint>0</SentToOutSourcePrint>
    <WinNo/>
    <ApplicationNo/>
    <PolicyHeader>
        <PolicyStartDate></PolicyStartDate>
        <PolicyEndDate></PolicyEndDate>
        <AgentCode>60113084</AgentCode>
        <BranchCode>24</BranchCode>
        <MajorClass>PAC</MajorClass>
        <ContractType>PAL</ContractType>
        <METHOD>ENQ</METHOD>
        <PolicyIssueType>I</PolicyIssueType>
        <PolicyNo/>
        <ClientID/>
        <ReceiptNo/>
    </PolicyHeader>
    <POS_MISP>
        <Type></Type>
        <PanNo></PanNo>
    </POS_MISP>
    <Client>
        <ClientType>I</ClientType>
        <CreationType>C</CreationType>
        <Salutation></Salutation>
        <FirstName></FirstName>
        <LastName></LastName>
        <DOB></DOB>
        <Gender></Gender>
        <MaritalStatus></MaritalStatus>
        <Occupation>MAIN</Occupation>
        <PANNo></PANNo>
        <GSTIN/>
        <AadharNo />
        <CKYCNo></CKYCNo>
        <CKYCRefNo></CKYCRefNo>
        <EIANo></EIANo>
        <Address1>
            <AddrLine1>403/404 Bhavani Skyline, Atabhai Road</AddrLine1>
            <AddrLine2>Nr. Piyusha Fast Food, Opp. Jogger's Park</AddrLine2>
            <AddrLine3/>
            <Landmark />
            <Pincode>${quotationData.pincode}</Pincode>
            <City>Bhavnagar</City>
            <State>Gujarat</State>
            <Country>IND</Country>
            <AddressType>R</AddressType>
            <HomeTelNo/>
            <OfficeTelNo/>
            <FAXNO />
            <MobileNo>7340803730</MobileNo>
            <EmailAddr><EMAIL></EmailAddr>
        </Address1>
        <Address2>
            <AddrLine1>403/404 Bhavani Skyline, Atabhai Road</AddrLine1>
            <AddrLine2>Nr. Piyusha Fast Food, Opp. Jogger's Park</AddrLine2>
            <AddrLine3/>
            <Landmark />
            <Pincode>${quotationData.pincode}</Pincode>
            <City>Bhavnagar</City>
            <State>Gujarat</State>
            <Country>IND</Country>
            <AddressType>R</AddressType>
            <HomeTelNo/>
            <OfficeTelNo/>
            <FAXNO/>
            <MobileNo>7340803730</MobileNo>
            <EmailAddr><EMAIL></EmailAddr>
        </Address2>
        <VIPFlag>N</VIPFlag>
        <VIPCategory/>
    </Client>
    <Receipt>
        <UniqueTranKey>060120251320</UniqueTranKey>
        <CheckType/>
        <BSBCode/>
        <TransactionDate>06/01/2025</TransactionDate>
        <ReceiptType>IVR</ReceiptType>
        <Amount></Amount>
        <TCSAmount/>
        <TranRefNo>060120251322</TranRefNo>
        <TranRefNoDate>06/01/2025</TranRefNoDate>
    </Receipt>
    <Risk>
        <IsfgEmployee>S</IsfgEmployee>
        <Duration></Duration>
        <Installments></Installments>
        <PaymentType/>
        <Discount/>
        <CoverageClass>${coverageClass}</CoverageClass>
        <CoverageClassCode>PAL</CoverageClassCode>
        <Plan>  accident suraksha</Plan>
        <Unit>1</Unit>
        <Claimfreeyears>0</Claimfreeyears>
        <OccupationClass>3</OccupationClass>
        <CumulativeBonusAmount/>
        <AlreadyPAPolicy/>
        <AdditionalRemarks/>
        <PendingRemarks/>
        <BranchReferenceID/>
        <FGBankBranchStaffID/>
        <BankStaffID/>
        <BankCustomerID/>
        <BancaChannel/>
        <PartnerRefNo/>
        <PayorID/>
        <PayerName/>
         ${insuredXML}
          <GrossPremium></GrossPremium>
        <ServiceTax></ServiceTax>
     </Risk>
</Root>]]></tem:XML>
          </tem:CreatePolicy>
   </soapenv:Body>
</soapenv:Envelope>` ;
// Helper function to format date
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};

// Save SOAP response to database
const savePASoapResponse = async (quotationId, memberData, responseData, trx) => {

    try {
        const createPolicyResult = responseData?.['s:Envelope']?.['s:Body']?.[0]?.['CreatePolicyResponse']?.[0]?.['CreatePolicyResult']?.[0];
        if (!createPolicyResult) {
            throw new Error("Unable to extract CreatePolicyResult from response");
        }

        const xmlContent = await parseStringPromise(createPolicyResult);
        const policyData = xmlContent?.Root?.Policy?.[0]?.NewDataSet?.[0];

        if (!policyData) {
            throw new Error('Invalid response structure: Missing Policy data');
        }

        // Extract EMI details array
        const emiDetails = policyData.EMIDetails || [];

        // Create records for each EMI detail
        const records = emiDetails.map(emi => ({
            pa_quotation_id: quotationId,
            duration: extractNumericValue(emi.Duration?.[0]),
            full_payment_premium: extractNumericValue(emi.FullPaymentPremium?.[0]),
            full_payment_tax: extractNumericValue(emi.FullPaymentTax?.[0]),
            total_full_payment: extractNumericValue(emi.TotalFullPayment?.[0]),
            family_discount_perc: extractNumericValue(emi.FamilyDiscountPerc?.[0]),
            family_discount_amt: extractNumericValue(emi.FamilyDiscountAmt?.[0]),
            long_term_discount_percent: extractNumericValue(emi.LongTermDiscountPercent?.[0]),
            long_term_discount_amount: extractNumericValue(emi.LongTermDiscountAmount?.[0]),
            status: 'COMPLETED'
        }));
        if (records.length > 0) {
            // Use the passed transaction
            const insertedResponses = await trx('pa_responses').insert(records);
            // console.log('Inserted Responses:', insertedResponses);
            return insertedResponses;
        } else {
            throw new Error('No valid member responses to insert');
        }

    } catch (error) {
        console.error('Error in saving PA SOAP response:', error);
        throw error;
    }
};
// Helper function to extract numeric values
const extractNumericValue = (value) => {
    if (!value) return null;
    const numericValue = parseFloat(String(value).replace(/[^\d.]/g, ''));
    return isNaN(numericValue) ? null : numericValue;
};

// Main function to send SOAP request
const sendPASoapRequest = async (memberData, quotationData, quotationId, trx) => {
    try {
        // console.log('Received Member Data:', memberData);
        // console.log('Received Quotation Data:', quotationData);
        // console.log('Received Quotation ID:', quotationId);
        // Validate required data
        if (!quotationData || !quotationData.pincode) {
            throw new Error('Missing required field: pincode in quotationData');
        }

        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };

        const membersArray = Array.isArray(memberData) ? memberData : [memberData];
        const uniqueUID = uuidv4();
        const insuredXML = generateInsuredXML(membersArray);
        const coverageClass = membersArray.length === 1 && membersArray[0].relation === 'SELF' ? 'Individual' : 'Family';

        const requestBody = SOAP_BODY(uniqueUID, quotationData, insuredXML, coverageClass);
        console.log(requestBody, 'requestBody');
        const response = await axios.post(SOAP_API_URL, requestBody, { headers });
        const jsonResponse = await parseStringPromise(response.data);
        const logData = {
            quotation_number: quotationData.quotation_number,
            request_body: requestBody, // Raw XML request
            response_body: response.data, // Raw XML response
            status: 'SUCCESS',
            error_message: null,
            created_by: quotationData.Created_by || 'SYSTEM',
            customer_name: membersArray[0]?.insuredName || 'UNKNOWN',
            insurance_company: (quotationData.insurance_company || 'FG Insurance'),
            product_name: (quotationData.product || 'Health Total'),
            created_at: db.fn.now()
        };

        await db('quotations_logs').insert(logData);
        console.log('PA SOAP Response:', JSON.stringify(jsonResponse, null, 2));

        await savePASoapResponse(quotationId, membersArray, jsonResponse, trx);
        return jsonResponse;

    } catch (error) {
        console.error("PA SOAP Request Error:", error);
        throw {
            status: 'error',
            message: error.message || 'Failed to process the PA request',
            details: error.response?.data || 'Unable to complete the SOAP request',
            quotation_id: quotationId
        };
    }
};

module.exports = { sendPASoapRequest };
