const knexConfig = require('../../../knexfile');
const { sendSOAPRequest } = require('../SoapService/QuotationsoapService');
const db = require('knex')(knexConfig.development);

// Fetch all members for a quotation
const getAllMembersByQuotationId = async (quotationId) => {
  return db('quotation_member')
    .join('quotations', 'quotation_member.quotation_id', '=', 'quotations.quotation_id')
    .where('quotation_member.quotation_id', quotationId)
    .select('quotation_member.*', 'quotations.*');
};

// Fetch a single member by ID
const getMemberById = async (id) => {
  return db('quotation_member').where('id', id).first();
};

// Create a new member
const createMember = async (memberData) => {
  // sendSOAPRequest(memberData);  
  return db('quotation_member').insert(memberData).returning('*');
};

// Update a member
const updateMember = async (id, updatedData) => {
  return db('quotation_member')
    .where('id', id)
    .update(updatedData)
    .returning('*');
};

// Delete a member
const deleteMember = async (id) => {
  return db('quotation_member').where('id', id).del();
};

module.exports = {
  getAllMembersByQuotationId,
  getMemberById,
  createMember,
  updateMember,
  deleteMember,
};
