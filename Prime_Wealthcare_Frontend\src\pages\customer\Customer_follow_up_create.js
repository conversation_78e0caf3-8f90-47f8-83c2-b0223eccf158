import React, { useEffect, useState } from 'react';
import { Box, Container, Button, Typography, Grid, Avatar, ButtonGroup } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import DetailsDropdown from '../../components/table/DetailsDropdown';
import CustomSection from '../../components/CustomSection';
import { fetchEmployeeData, getAllAgentDetails, getAreaById, getAreasByPincodeAndCity, getCustomerAddressByCustomerId, getCustomerById, getCustomerDocumentsByCustomerId, getMemberByCustomerId, getEmployeeOrAgentById, deleteCustomerDocument } from '../../redux/actions/action';
import { getAllRoles } from '../../redux/actions/action';
import CustomFileUpload from '../../components/CustomFileUpload';
import CustomTextField from '../../components/CustomTextField';
import CustomCheckbox from '../../components/CheckboxWithLabel';
import Dropdown from '../../components/table/DropDown';
import { TextField, FormControl, InputLabel, Select, MenuItem, Checkbox, ListItemText, FormControlLabel } from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import ExportToPDF from '../../components/ExportToPDF';


function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-GB').format(date); // 'en-GB' formats date as dd/mm/yyyy
}

function maskDOB(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return `**/**/${date.getFullYear()}`; // Only show year
}

function maskMobileNumber(number) {
    if (!number) return 'N/A';
    return number.slice(0, 1) + '******' + number.slice(7);
}

function maskEmail(email) {
    if (!email) return 'N/A';
    const [localPart, domain] = email.split('@');
    const firstChar = localPart.charAt(0);
    const lastTwoChars = localPart.slice(-2);
    const maskedPart = '*'.repeat(Math.max(0, localPart.length - 3));
    return `${firstChar}${maskedPart}${lastTwoChars}@${domain}`;
}

function Customer_follow_up_create() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const items = useSelector(state => state.areaManagementReducer.items);
    const [sortedItems, setSortedItems] = useState(items || []);

    const agentInformation = [{

    }];
    const customerInformation = useSelector(state => state.customerReducer.customerDetails);
    const customerAddress = useSelector(state => state.customerAddressReducer.currentAddress);
    const customerDocuments = useSelector(state => state.customerDocumentationReducer.customerDocumentation);
    const area = useSelector(state => state.areaManagementReducer.area);
    const agents = useSelector(state => state.agentReducer.agents);
    const employees = useSelector(state => state.employeeInfoReducer.employees);
    const existingGroups = useSelector(state => state.customerGroupingReducer.groups);

    const [members, setMembers] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [assignedTo, setAssignedTo] = useState([]);

    useEffect(() => {
        if (customerInformation?.assigned_to) {
            dispatch(getEmployeeOrAgentById(customerInformation?.assigned_to)).then(res => {
                setAssignedTo(res.payload.employee_full_name || res.payload.full_name || 'N/A');
            })
        }
    }, [id, customerInformation])

    useEffect(() => {
        if (customerDocuments && Array.isArray(customerDocuments)) {
            const mappedData = customerDocuments.map(doc => ({
                id: doc.id,
                Member_Name: doc.full_name || 'N/A',
                Relation: doc.relation || 'N/A',
                Document_Type: doc.document_type || 'N/A',
                Document_Number: doc.document_id || 'N/A',
                Attachment: doc.document_path || 'N/A',
                Assigned_To: doc.created_by || 'N/A',
                Created_At: formatDate(doc.created_at),
                Updated_At: formatDate(doc.updated_at)
            }));
            setTableData(mappedData);
        }
    }, [customerDocuments]);
    useEffect(() => {
        if (customerAddress) {
            let area_id = null;
            if (customerAddress?.used_address === 'current') {
                dispatch(getAreasByPincodeAndCity(customerAddress?.current_pincode, customerAddress?.current_city))
                area_id = customerAddress?.current_area;
            } else {
                dispatch(getAreasByPincodeAndCity(customerAddress?.permanent_pincode, customerAddress?.permanent_city));
                area_id = customerAddress?.permanent_area;
            }
            if (area_id) {
                dispatch(getAreaById(area_id));
            }
        }
    }, [customerInformation, customerAddress])

    useEffect(() => {
        dispatch(getCustomerById(id));
        dispatch(getCustomerAddressByCustomerId(id));
        dispatch(getMemberByCustomerId(id));
        dispatch(getCustomerDocumentsByCustomerId(id));
        dispatch(getAllAgentDetails());
        dispatch(fetchEmployeeData());
        //dispatch(getAllRoles());
    }, [id, dispatch]);

    useEffect(() => {
        setSortedItems(items);
    }, [items]);

    const handleAdd = () => {
        navigate('/dashboard/customer-personal-information'); // Navigate to the create personal detail page
    };

    const handleExportToPDF = () => {
        // const doc = new jsPDF({ orientation: 'landscape' });
        // autoTable(doc, {
        //     head: [['Full Name', 'User ID', 'Office Mobile', 'Office Email', 'Department', 'Role', 'Personal Mobile', 'Personal Email', 'Joining Date', 'Branch Name', 'City', 'Date of Birth']],
        //     body: [[
        //         dataByCustomerId.employee_full_name,
        //         employeeInformation.user_id,
        //         employeeInformation.official_mobile,
        //         employeeInformation.official_email,
        //         employeeInformation.department_name,
        //         employeeInformation.role_name,
        //         employeeInformation.personal_mobile,
        //         employeeInformation.personal_email,
        //         formatDate(employeeInformation.date_of_joining),
        //         employeeInformation.branch_name,

        //         formatDate(employeeInformation.date_of_birth)
        //     ]],
        // });
        // doc.save('employee_personal_information.pdf');
    };

    const handleCreate = () => {
        navigate(`/dashboard/customer-documentation-upload/${id}`);
    };
    const handleCancel = () => {
        navigate('/dashboard/customer-Master');
    };
    const handleDelete = (id) => {
        dispatch(deleteCustomerDocument(id));
    }

    const renderAvatar = () => {
        const { photo, first_name } = customerInformation|| {};
        const initials = first_name ? first_name.split(' ').slice(0, 2).map(n => n[0].toUpperCase()).join('') : 'N/A';
        return (
            <Avatar
                alt={first_name}
                src={photo || ''}
                sx={{ width: photo ? 100 : 50, height: photo ? 100 : 50 }}
            >
                {!photo && initials}
            </Avatar>
        );
    };

    const dataMapping = {
        'Full Name': row => `${row.first_name} ${row.last_name}`,
        'Mobile': row => maskMobileNumber(row.mobile),
        'Email': row => maskEmail(row.email),
        'Area': row => area?.area || 'N/A',
        'City': row => customerAddress?.used_address === 'current' ? customerAddress?.current_city : customerAddress?.permanent_city,
        'State': row => customerAddress?.used_address === 'current' ? customerAddress?.current_state : customerAddress?.permanent_state,
        'Assigned To': row => assignedTo || 'N/A',
        'Created At': row => formatDate(row.created_at)
    };

    return (
        <Container maxWidth="xl">
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName='Customer' pageName='View' />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        <Button
                            onClick={handleAdd}
                            sx={{
                                borderTopRightRadius: 0,
                                borderBottomRightRadius: 0,
                                border: '1px solid',
                                borderColor: 'primary.main'
                            }}
                        >
                            New
                        </Button>
                        <ExportToPDF
                            data={[{
                                ...customerInformation,
                                first_name: customerInformation?.first_name || 'N/A',
                                last_name: customerInformation?.last_name || 'N/A',
                                mobile: customerInformation?.mobile,
                                email: customerInformation?.email,
                                created_at: customerInformation?.created_at
                            }]}
                            headNames={['Full Name', 'Mobile', 'Email', 'Area', 'City', 'State', 'Assigned To', 'Created At']}
                            selectedRows={[customerInformation?.id]}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="customer_details.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Customer Details Report"
                            disabled
                        />
                        <Button
                            onClick={handleCancel}
                            sx={{
                                color: 'red',
                                borderTopLeftRadius: 0,
                                borderBottomLeftRadius: 0,
                                mr: '8px',
                                borderBlock: '1px solid red',
                                borderRight: '1px solid red'
                            }}
                        >
                            Cancel
                        </Button>
                    </ButtonGroup>
                </Box>
                <Grid container >
                    <CustomSection titles={['Overview', 'Personal Details', 'Member Information', 'Address', 'Grouping']} page='customer' customerType={customerInformation?.customer_category} />
                </Grid>
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem', borderBottom: '1px solid black' }}>
                    <Box mr={2}>
                        {renderAvatar()}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                        <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                            <Grid item xs={12} sm={6} md={4} lg={4}>
                                <Typography noWrap><strong>Full Name:</strong> {customerInformation?.first_name} {customerInformation?.last_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={4}>
                                <Typography noWrap><strong>Mobile:</strong> {maskMobileNumber(customerInformation?.mobile)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={4}>
                                <Typography noWrap><strong>Email:</strong> {maskEmail(customerInformation?.email)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={4}>
                                <Typography noWrap><strong>Area:</strong> {area?.area}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={4}>
                                <Typography noWrap><strong>City:</strong> {customerAddress?.used_address === 'current' ? customerAddress?.current_city : customerAddress?.permanent_city}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={4}>
                                <Typography noWrap><strong>State:</strong> {customerAddress?.used_address === 'current' ? customerAddress?.current_state : customerAddress?.permanent_state}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </Box>
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 3rem' }}>
                    <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                        <Grid item xs={12} sm={6} md={4} lg={4}>
                            <Typography noWrap><strong>Group Id:</strong> {customerInformation?.group_id}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={4}>
                            <Typography noWrap><strong>Head Name:</strong> {maskMobileNumber(agentInformation?.personal_mobile)}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={4}>
                            <Typography noWrap><strong>Relation:</strong> {maskEmail(agentInformation?.personal_email)}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={4}>
                            <Typography noWrap><strong>Head Mobile Number:</strong> {agentInformation?.branch_city}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={4}>
                            <Typography noWrap><strong>Head Email:</strong> {agentInformation?.branch_city}</Typography>
                        </Grid>
                    </Grid>
                </Box>
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem', borderBottom: '1px solid black' }}>
                    <h3>Detailed Summary</h3>
                </Box>
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem' }}>
                    <Typography color='lightgray'>Comments History</Typography>
                </Box>
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem', borderBottom: '1px solid black' }}>
                    <h3>User Info</h3>
                </Box>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={4}>
                        <CustomTextField
                            label="Assigned To"
                            name="assigned_to"
                            value={assignedTo}
                            // onChange={handleChange}
                            fullWidth
                            // helperText={formErrors.assigned_to || ''}
                            required
                            disabled
                        />
                    </Grid>
                    <Grid item xs={4}>
                        <CustomTextField
                            name="created_at"
                            label="Created At"
                            value={formatDate(customerInformation?.created_at)}
                            // onChange={handleChange}
                            fullWidth
                            // helperText={formErrors.full_name || ''}
                            isRequired
                            disabled
                        />
                    </Grid>
                </Grid>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1.25rem', paddingBottom: '2rem' }}>
                    <DetailsDropdown
                        headerText={`Documents (${tableData?.length})`}
                        tableHeadings={['Member Name', 'Relation', 'Document Type', 'Document Number', 'Attachment', 'Assigned To', 'Created At', 'Updated At']}
                        tableData={tableData?.map(data => ({
                            'Member Name': data.Member_Name,
                            'Relation': data.Relation,
                            'Document Type': data.Document_Type,
                            'Document Number': data.Document_Number,
                            'Attachment': data.Attachment,
                            'Assigned To': data.Assigned_To,
                            'Created At': data.Created_At,
                            'Updated At': data.Updated_At
                        }))}
                        showSearch={true}
                        handleCreate={handleCreate}
                        onDelete={handleDelete}
                        optionsColumn={false}
                    />
                </Box>
            </Box>
        </Container >
    );
}

export default Customer_follow_up_create;
