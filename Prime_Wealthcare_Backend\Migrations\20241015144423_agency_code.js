exports.up = function(knex) {
  return knex.schema.hasTable('agency_code').then(function(exists) {
    if (!exists) {
      return knex.schema.createTable('agency_code', function(table) {
        table.increments('id');
        
        table.integer('insurance_company_id').unsigned().notNullable();
        table.foreign('insurance_company_id').references('id').inTable('insurance_company').onDelete('CASCADE');
        
        table.integer('imf_branch_id').unsigned().notNullable();
        table.foreign('imf_branch_id').references('id').inTable('imf_branches').onDelete('CASCADE');

        table.string('insurance_co_branch_name', 255).notNullable();
        table.string('agency_code', 255);
        table.string('agent_name', 255).notNullable();
        table.string('license_no', 255);
        table.date('license_valid_from');
        table.date('license_valid_till');
        table.boolean('status').notNullable().defaultTo(true);
        table.timestamps(true, true);
      });
    }
  });
};

exports.down = function(knex) {
  return knex.schema.dropTableIfExists('agency_code');
};
