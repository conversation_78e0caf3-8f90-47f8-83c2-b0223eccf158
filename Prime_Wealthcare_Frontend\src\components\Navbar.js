import { useState, useEffect } from 'react';
import { ExpandMore } from '@mui/icons-material';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Typography,
} from '@mui/material';
import '../styles/Navbar.scss';
import { useNavigate } from 'react-router-dom';
import UpperSection from './UpperSection';
import { useSelector, useDispatch } from 'react-redux';
import { fetchUserAccessRights, createPageRights, fetchAllUsers } from '../redux/actions/action';


function Navbar() {
    const [expanded, setExpanded] = useState(false);
    const [childExpanded, setChildExpanded] = useState(false);
    const [visibleItems, setVisibleItems] = useState([]);
    const [overflowItems, setOverflowItems] = useState([]);
    const [nestedExpanded, setNestedExpanded] = useState(false);
    const accessRights = useSelector((state) => state.auth.accessRights);
    const currentUser = useSelector((state) => state.auth.user);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const verticalItems = [
        {
            title: "Master",
            path: "",
            children: [
                { title: "Area Master", path: "area-list" },
                { title: "Insurance Company", path: "insurance-company" },
                { title: "IMF Branch", path: "imf-branch" },
                { title: 'Insurance Branch', path: 'insurance-branch' },
                { title: "Main Product", path: "main-product-list" },
                { title: "Product Master", path: "product-master" },
                { title: "Sub Product", path: "sub-product" },
                { title: "Network Master", path: "network-list" },
                { title: "Disease Master", path: "disease-master" },
                { title: "Role Management", path: "role-list" },
                { title: "Endorsement Type", path: "endorsment-type" },
                { title: "Page Right Access", path: "page-right-access" }
            ],
        },
        {
            title: "User",
            path: "",
            children: [
                {
                    title: "Employee Master",
                    path: "employee-Master",
                },
            ],
        },
        {
            title: "Agent",
            path: "",
            children: [
                { title: "Agent", path: "agent-master" },
            ],
        },
        {
            title: "Loan",
            path: "",
            children: [
                { title: "Employee Loan", path: "employee-loans" },
                { title: "Agent Loan", path: "agent-loans" },
            ],
        },
        {
            title: "Commission",
            path: "",
            children: [
                { title: "Commission Master", path: "commission-rate-list" },
                { title: "Generate Commission", path: "" },
                { title: "Commission Report", path: "" },
            ],
        }
    ];

    const horizontalItems = [
        {
            title: "Dashboard",
            path: "",
            children: [
                { title: "CRM Dashboard", path: "/crm-dashboard" },
                { title: "HR Dashboard", path: "" },
            ],
        },
        {
            title: "Task",
            path: "",
            children: [
                { title: "Task", path: "task-board" },
            ],
        },
        {
            title: "Customer",
            path: "",
            children: [
                { title: "Customer Details", path: "customer-master" },
                { title: "Grouping Customer", path: "" },
            ],
        },
        {
            title: "Quotations",
            path: "",
            children: [
                { title: "Quotations", path: "quotations-list" },
                { title: "Quick Quotation", path: "quick-quotation" },
            ],
        },
        {
            title: "Proposal",
            path: "",
            children: [
                { title: "Health", path: "proposals" },
                { title: 'Motor', path: '' },
                { title: 'SME', path: '' },
                { title: 'Life', path: '' }
            ],
        },
        {
            title: "Policy",
            path: "",
            children: [
                { title: "Generate Policy", path: "" },
            ],
        },
        {
            title: "Renewals",
            path: "",
            children: [
                { title: "Generate Renewals", path: "renewals-list" },

                { title: "Renew Retention", path: "" },
            ],
        },
        {
            title: "Claim",
            path: "",
            children: [
                { title: "Generate Claim", path: "" },
                { title: "Pending Claims", path: "" },
                { title: "Rejected Claims", path: "" },
                { title: "Claim Reports", path: "" },
            ],
        },
        {
            title: "Reports",
            path: "",
            children: [
                { title: "Business Report", path: "reports" }, // Assuming this is the correct path for reports 
                { title: "Loan", path: "" },
            ],
        },
    ];


    // Fetch user access rights on component mount
    useEffect(() => {
        if (currentUser && currentUser.userId) {
            dispatch(fetchUserAccessRights(currentUser.userId));
        }
    }, [dispatch, currentUser]);

    // Function to check if user has permission to view a specific item
    const hasViewPermission = (moduleName, pageName) => {
        if (!accessRights || accessRights.length === 0) {
            return false;
        }

        // Module name mapping (for cases where menu module name differs from permission module name)
        const moduleNameMap = {
            "User": "User Management",
            "Task": "Task Management",
            "Quotations": "Quotation",
        };

        // Map of menu item titles to permission page names
        const pageNameMap = {
            // Dashboard items
            "CRM Dashboard": "CRM Dashboard",
            "HR Dashboard": "HR Dashboard",

            // Master items
            "Area Master": "Area Master",
            "Insurance Company": "Insurance Company",
            "IMF Branch": "IMF Branch",
            "Insurance Branch": "Insurance Branch",
            "Main Product": "Main Product",
            "Product Master": "Product Master",
            "Sub Product": "Sub Product",
            "Network Master": "Network Master",
            "Disease Master": "Disease Master",
            "Role Management": "Role Management",
            "Endorsement Type": "Endorsement Type",
            "Page Right Access": "Page Right Access", // Fixed: removed extra quote

            // User Management items
            "Employee Master": "Employee",

            // Agent items
            "Agent": "Agent Master",

            // Customer items
            "Customer Details": "Customer Master",
            "Grouping Customer": "Customer Grouping",
            "Customer Documents": "Documents",
            "Customer Follow Up": "Follow Up",

            // Quotation items
            "Quotations": "Quotation List",
            "Quick Quotation": "Quick Quotation Create",

            // Proposal items
            "Health": "Proposal List",
            "Motor": "Proposal List",
            "SME": "Proposal List",
            "Life": "Proposal List",
            "Proposal Payment": "Payment Form",
            "CKYC": "CKYC Form",
            "Proposal Renewal": "Renewal Create",
            "Roll Over": "Roll Over",
            "Migration": "Migration",

            // Add Renewals mappings
            "Generate Renewals": "Generate Renewals",
            "Renew Retention": "Renew Retention",

            // Add Reports mappings
            "Business Report": "Business Report",
            "Loan": "Loan Report",

            /*  // Add Commission mappings
 
             // Add Commission mappings
             "Commission Master": "Commission Master",
             "Generate Commission": "Generate Commission",
             "Commission Report": "Commission Report",
              */

            // Add Claim mappings
            "Generate Claim": "Generate Claim",
            "Pending Claims": "Pending Claims",
            "Rejected Claims": "Rejected Claims",
            "Claim Reports": "Claim Reports",

            "Task": "Task Board",

            // Add more mappings based on your permissions data
        };

        // Special case for Quick Quotation which is under Quotations menu but is its own module
        if (moduleName === "Quotations" && pageName === "Quick Quotation") {
            return accessRights.some(right =>
                right.module_name === "Quick Quotation" &&
                right.page_name === "Quick Quotation Create" &&
                (right.can_view === 1 || right.can_view === true)
            );
        }

        // Get the actual module and page names to look for in permissions
        const permissionModuleName = moduleNameMap[moduleName] || moduleName;
        const permissionPageName = pageNameMap[pageName] || pageName;

        // Debug: Log the mapping if needed

        // Look for matching module and page in access rights with more detailed logging
        const hasPermission = accessRights.some(right => {
            const moduleMatch = right.module_name === permissionModuleName;
            const pageMatch = right.page_name === permissionPageName;
            const viewPermission = right.can_view === 1 || right.can_view === true;

            return moduleMatch && pageMatch && viewPermission;
        });

        return hasPermission;
    };

    // Filter menu items based on permissions
    const filterMenuItems = (items) => {
        return items.map(item => {
            // Create a copy of the item
            const filteredItem = { ...item };

            // If the item has children, filter them
            if (item.children && item.children.length > 0) {
                const filteredChildren = item.children.filter(child =>
                    hasViewPermission(item.title, child.title)
                );

                filteredItem.children = filteredChildren;
            }

            // Only return items that have at least one accessible child
            return filteredItem.children && filteredItem.children.length > 0 ? filteredItem : null;
        }).filter(Boolean); // Remove null items
    };

    // Apply permissions to menu items
    const filteredHorizontalItems = accessRights ? filterMenuItems(horizontalItems) : [];
    const filteredVerticalItems = accessRights ? filterMenuItems(verticalItems) : [];

    // Update the visibleItems and overflowItems state with filtered items
    useEffect(() => {
        if (filteredHorizontalItems.length > 0) {
            const handleResize = () => {
                const navbar = document.querySelector('.navbar-lower-section');
                if (!navbar) return;

                const itemWidth = 150;
                const availableWidth = navbar.clientWidth - 60 + (window.innerWidth * 0.3);
                const maxItems = Math.floor(availableWidth / itemWidth);

                const newVisibleItems = filteredHorizontalItems.slice(0, maxItems);
                const newOverflowItems = filteredHorizontalItems.slice(maxItems);

                if (visibleItems.length !== newVisibleItems.length) {
                    setVisibleItems(newVisibleItems);
                    setOverflowItems(newOverflowItems);
                }
            };

            let timeoutId;
            const debouncedHandleResize = () => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(handleResize, 100);
            };

            debouncedHandleResize();
            window.addEventListener('resize', debouncedHandleResize);

            return () => {
                window.removeEventListener('resize', debouncedHandleResize);
                clearTimeout(timeoutId);
            };
        }
    }, [filteredHorizontalItems, visibleItems.length]); // Add dependencies

    useEffect(() => {
        const handleTouchOutside = (event) => {
            if (!event.target.closest('.accordion')) {
                setExpanded(false);
                setChildExpanded(false);
            }
        };

        document.addEventListener('touchstart', handleTouchOutside);
        return () => document.removeEventListener('touchstart', handleTouchOutside);
    }, []);

    // Handle the expansion of the parent accordion
    const handleParentExpansion = (panel) => {
        setExpanded(panel);
    };

    // Handle the expansion of child accordions
    const handleChildExpansion = (panel) => {
        setChildExpanded(panel);
    };

    // Collapse parent accordion when mouse leaves the parent
    const handleParentMouseLeave = (panel) => {
        if (expanded === panel) {
            setExpanded(false);
        }
    };
    // Collapse child accordion when mouse leaves the child
    const handleChildMouseLeave = (panel) => {
        if (childExpanded === panel) {
            setChildExpanded(false);
        }
    };

    // Handle navigation and collapse all accordions
    const handleNavigation = (path) => {
        setExpanded(false);
        setChildExpanded(false);
        if (path) navigate(path);
    };

    // Add handler for nested accordion
    const handleNestedExpansion = (panel) => {
        setNestedExpanded(panel);
    };

    const handleNestedMouseLeave = (panel) => {
        if (nestedExpanded === panel) {
            setNestedExpanded(false);
        }
    };

    // Modify the renderChildren function to check permissions
    const renderChildren = (children, parentPanelId) => {
        if (!children) return null;

        return children.map((child, childIndex) => {
            if (child.children) {
                const nestedPanelId = `${parentPanelId}-nested-${childIndex}`;
                return (
                    <Box
                        key={nestedPanelId}
                        className='nested-menu-item'
                        onMouseEnter={() => handleNestedExpansion(nestedPanelId)}
                        onMouseLeave={() => handleNestedMouseLeave(nestedPanelId)}
                    >
                        <AccordionDetails
                            className='accordion-details nested-parent'
                        >
                            <Box className='accordion-summary-detail'>
                                <Typography className='accordion-child-title'>
                                    {child.title}
                                </Typography>
                                <ExpandMore className={`nested-arrow arrow ${nestedExpanded === nestedPanelId ? 'rotated' : ''}`} />
                            </Box>
                        </AccordionDetails>
                        {nestedExpanded === nestedPanelId && (
                            <Box className="nested-children-container">
                                {renderChildren(child.children, nestedPanelId)}
                            </Box>
                        )}
                    </Box>
                );
            }
            return (
                <AccordionDetails
                    onClick={() => handleNavigation(child.path)}
                    key={`${parentPanelId}-child-${childIndex}`}
                    className='accordion-details'
                >
                    <Typography className='accordion-child-title'>
                        {child.title}
                    </Typography>
                </AccordionDetails>
            );
        });
    };

    return (
        <Box className='Navbar' component='nav' sx={{
            position: 'sticky',
            top: 0,
            zIndex: 1100,
            backgroundColor: 'background.paper'
        }}>
            {/* Upper Section */}
            <UpperSection />

            {/* Lower Section */}
            <Box className='navbar-lower-section'>
                <Accordion
                    key='more'
                    expanded={expanded === 'more'}
                    onMouseEnter={() => handleParentExpansion('more')}
                    onMouseLeave={() => handleParentMouseLeave('more')}
                    onTouchStart={() => handleParentExpansion('more')}
                    className='accordion nine-dots'
                    disableGutters
                >
                    <AccordionSummary
                        aria-controls='more-content'
                        id='more-header'
                    >
                        <img src='/icon.svg' alt="nine-dot-icon" />
                    </AccordionSummary>
                    <Box className="accordion-details-container">
                        <AccordionDetails className='accordion-details'>
                            {/* Combine filteredVerticalItems with overflowItems */}
                            {[...filteredVerticalItems, ...overflowItems].map((item, index) => {
                                const panelId = `panel${index}`;
                                return (
                                    <Accordion
                                        key={panelId}
                                        expanded={childExpanded === panelId}
                                        onMouseEnter={() => handleChildExpansion(panelId)}
                                        onMouseLeave={() => handleChildMouseLeave(panelId)}
                                        onTouchStart={() => handleChildExpansion(panelId)}
                                        className='accordion child-accordion'
                                        disableGutters
                                    >
                                        <AccordionSummary
                                            aria-controls={`${panelId}-content`}
                                            id={`${panelId}-header`}
                                            className='accordion-summary'
                                        // onClick={() => handleNavigation(item.path)}
                                        >
                                            <Box
                                                className='accordion-summary-detail'
                                            >
                                                <Typography className='accordion-title'>
                                                    {item.title}
                                                </Typography>
                                                <ExpandMore className={`arrow ${childExpanded === panelId ? 'rotated' : ''}`} />
                                            </Box>
                                        </AccordionSummary>
                                        <Box className="child-accordion-details-container">
                                            {renderChildren(item.children, panelId)}
                                        </Box>
                                    </Accordion>
                                );
                            })}
                        </AccordionDetails>
                    </Box>
                </Accordion>

                {/* Render only visible horizontal filtered items */}
                {visibleItems.map((item, index) => {
                    const panelId = `panel${index}`;
                    return (
                        <Accordion
                            key={panelId}
                            expanded={expanded === panelId}
                            onMouseEnter={() => handleParentExpansion(panelId)}
                            onMouseLeave={() => handleParentMouseLeave(panelId)}
                            onTouchStart={() => handleParentExpansion(panelId)}
                            className='accordion'
                            disableGutters
                        >
                            <AccordionSummary
                                aria-controls={`${panelId}-content`}
                                id={`${panelId}-header`}
                                className='accordion-summary'
                            >
                                <Box
                                    className={`accordion-summary-detail ${expanded === panelId ? 'border' : ''}`}
                                >
                                    <Typography className='accordion-title'>
                                        {item.title}
                                    </Typography>
                                    <ExpandMore className={`arrow ${expanded === panelId ? 'rotated' : ''}`} />
                                </Box>
                            </AccordionSummary>
                            <Box className="accordion-details-container">
                                <AccordionDetails className='accordion-details'>
                                    {renderChildren(item.children, panelId)}
                                </AccordionDetails>
                            </Box>
                        </Accordion>
                    );
                })}
            </Box>
        </Box>
    );
}

export default Navbar;
