const knexConfig = require('../../../knexfile');
const { sendSOAPRequest } = require('../SoapService/HealthAbsoluteSoapServices');
const { sendSOAPRequestHealthTotal } = require('../SoapService/HealthTotalSoapServices');
const { sendSOAPRequestAdvantageTopUp } = require('../SoapService/AdvantageTopupSoapServices');
const { sendSOAPRequestHealthSuraksha } = require('../SoapService/HealthSurakshaSoapServices');
const { sendSOAPRequestVarishtaBima } = require('../SoapService/VarishtaBimaSoapServices');
//const getCostomerAddressByCustomerId = require('../../Customer/Routes/customer_address_route');
const db = require('knex')(knexConfig.development);


const getQuotationWithResponseAndMembersById = async (quotationId) => {
  try {
    const results = await db('quotations as q')
      .leftJoin('quotation_member as qm', 'q.quotation_id', 'qm.quotation_id')
      .leftJoin('soap_responses as sr', function () {
        this.on('q.quotation_id', '=', 'sr.quotation_id')
          .andOn('qm.insuredName', '=', 'sr.member_name'); // Match member names
      })

      .where('q.quotation_id', quotationId) // Filter by quotation ID
      .select(
        'q.quotation_id',
        'q.duration',
        'q.quotation_number',
        'q.product',
        'q.coPay',
        'q.insurance_company',
        'q.main_product',
        //  'q.venderCode',
        //  'q.majorClass',
        //  'q.contractType',
        //  'q.policyIssueType',
        'q.policyType',
        // 'q.paymentType',
        'q.customer_id',
        'q.agent_id',
        'q.status as quotation_status',
        db.raw("DATE_FORMAT(q.Created_at, '%d/%m/%Y') as quotation_created_at"),
        db.raw("DATE_FORMAT(q.Updated_at, '%d/%m/%Y') as quotation_updated_at"),

        'qm.id as quotation_member_id',
        'qm.member_id',
        'qm.insuredName',
        db.raw("DATE_FORMAT(qm.insuredDob, '%d/%m/%Y') as insured_dob"),
        'qm.sub_product_id as member_cover_type',
        'qm.sumInsured as member_sum_insured',
        'qm.relation as member_relation',
        'qm.status as member_status',
        'qm.deductableDiscount as member_deductable_discount',
        'sr.id as soap_response_id',
        'sr.base_premium',
        'sr.term_premium',
        'sr.family_discount_rate',
        'sr.family_discount',
        'sr.premium_without_service_tax',
        'sr.premium_with_load',
        'sr.premium_amount',
        'sr.service_tax_rate',
        'sr.service_tax',
        'sr.premium_with_service_tax',
        'sr.member_name',
        'sr.relation as soap_relation',
        'sr.cover_type as soap_cover_type',
        'sr.sum_insured as soap_sum_insured',
        'sr.bmi',
        'sr.bmi_loading_percent',
        'sr.per_person_premium',
        'sr.status as soap_status',
        'sr.copay_percentage',
        'sr.co_pay_amount',
        db.raw("DATE_FORMAT(sr.created_at, '%d/%m/%Y') as soap_created_at"),
        db.raw("DATE_FORMAT(sr.updated_at, '%d/%m/%Y') as soap_updated_at")
      )
      .distinct(); // Use DISTINCT to avoid duplicates

    return results;
  } catch (error) {
    console.error('Error fetching quotation with responses and members by ID:', error);
    throw new Error('Failed to fetch quotation with responses and members by ID');
  }
};



// };
// const getQuotationWithResponseAndMembers = async () => {
//   try {
//     const results = await db('quotations as q')
//       .leftJoin('customer_personal_info as c', 'q.customer_id', 'c.id')
//       .leftJoin('agents as a', 'q.agent_id', 'a.id')
//       .leftJoin('product_master as pm', 'q.product', 'pm.id')
//       .select(
//         'q.quotation_id as id',
//         'q.quotation_number',
//         db.raw("CONCAT(c.first_name, ' ', COALESCE(c.last_name, '')) as first_name"),
//         'c.mobile',
//         'c.email',
//         'q.product',
//         'pm.product_name',
//         db.raw(`
//           CASE 
//             WHEN q.policyType LIKE '%I' THEN 'Individual'
//             WHEN q.policyType LIKE '%F' THEN 'Family Floater'
//             ELSE q.policyType
//           END as policyType
//         `),
//         'c.group_code',
//         'c.assigned_to',
//         'q.status',
//         'q.agent_id',
//         'a.full_name as agent_first_name',
//         'a.agent_id',

//         db.raw("DATE_FORMAT(q.created_at, '%d/%m/%Y') as created_at"),
//         db.raw("DATE_FORMAT(q.updated_at, '%d/%m/%Y') as updated_at"),
//         db.raw("DATE_FORMAT(DATE_ADD(q.created_at, INTERVAL 30 DAY), '%d/%m/%Y') as valid_until")


//       )
//       .orderBy('q.Created_at', 'desc');

//     return results;
//   } catch (error) {
//     console.error('Error fetching quotations with customer details:', error);
//     throw new Error('Failed to fetch quotations with customer details');
//   }
// };

const getQuotationWithResponseAndMembers = async () => {
  try {
    // Query for regular quotations
    const regularQuotations = db('quotations as q')
      .select([
        'q.quotation_id as id',
        'q.quotation_number',
        db.raw(`CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as first_name`),
        'c.mobile',
        'c.email',
        'q.product',
        'pm.product_name',
        db.raw(`CASE 
                  WHEN q.policyType LIKE '%I' THEN 'Individual'
                  WHEN q.policyType LIKE '%F' THEN 'Family Floater'
                  ELSE q.policyType
              END as policyType`),
        'c.group_code',
        'c.assigned_to',
        'q.status',
        'q.agent_id',
        'a.full_name as agent_first_name',
        'a.agent_id',
        db.raw("DATE_FORMAT(q.created_at, '%d/%m/%Y') as created_at"),
        db.raw("DATE_FORMAT(q.updated_at, '%d/%m/%Y') as updated_at"),
        db.raw("DATE_FORMAT(DATE_ADD(q.created_at, INTERVAL 30 DAY), '%d/%m/%Y') as valid_until")
      ])
      .leftJoin('customer_personal_info as c', 'q.customer_id', 'c.id')
      .leftJoin('agents as a', 'q.agent_id', 'a.id')
      .leftJoin('product_master as pm', 'q.product', 'pm.id')
      .orderBy('q.created_at', 'desc');

    // Query for PA quotations
    const paQuotations = db('pa_quotations as pq')
      .select([
        'pq.pa_quotation_id as id',
        'pq.quotation_number',
        db.raw(`CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as first_name`),
        'c.mobile',
        'c.email',
        'pq.product',
        'pm.product_name',
        db.raw("'Individual' as policyType"),
        'c.group_code',
        'c.assigned_to',
        'pq.status',
        'pq.agent_id',
        'a.full_name as agent_first_name',
        'a.agent_id',
        db.raw("DATE_FORMAT(pq.created_at, '%d/%m/%Y') as created_at"),
        db.raw("DATE_FORMAT(pq.updated_at, '%d/%m/%Y') as updated_at"),
        db.raw("DATE_FORMAT(DATE_ADD(pq.created_at, INTERVAL 30 DAY), '%d/%m/%Y') as valid_until")
      ])
      .leftJoin('customer_personal_info as c', 'pq.customer_id', 'c.id')
      .leftJoin('agents as a', 'pq.agent_id', 'a.id')
      .leftJoin('product_master as pm', 'pq.product', 'pm.id')
      .orderBy('pq.created_at', 'desc');

    // Combine and execute both queries
    const [regularResults, paResults] = await Promise.all([regularQuotations, paQuotations]);

    // Add source table identifier to each result
    const modifiedRegularResults = regularResults.map(quote => ({
      ...quote,
      source_table: 'quotations'
    }));

    const modifiedPaResults = paResults.map(quote => ({
      ...quote,
      source_table: 'pa_quotations'
    }));

    // Combine and sort results by creation date
    const allQuotations = [...modifiedRegularResults, ...modifiedPaResults]
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    return allQuotations;
  } catch (error) {
    console.error('Error fetching quotations with customer details:', error);
    throw new Error('Failed to fetch quotations with customer details');
  }
};



// Fetch all quotations
const getAllQuotations = async () => {
  return db('quotations').select('*');
}

const getAllQuotationsByUserId = async (userId) => {
  try {

    // Function to get agent IDs for employee
    const getAgentIdsForEmployee = async (userId) => {
      const employee = await db('employee_personal_info')
        .where('user_id', userId)
        .first();

      if (!employee || !employee.branch_id) {
        return [];
      }

      const branchIds = employee.branch_id.split(',').map(id => id.trim());
      const agents = await db('agents')
        .whereIn('branch_id', branchIds)
        .select('id');

      return agents.map(agent => agent.id);
    };

    // Build queries without executing them yet
    let regularQuotationsQuery = db('quotations as q')
      .select([
        'q.quotation_id as id',
        'q.quotation_number',
        db.raw(`CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as first_name`),
        'c.mobile',
        'c.email',
        'q.product',
        'pm.product_name',
        db.raw(`CASE 
                  WHEN q.policyType LIKE '%I' THEN 'Individual'
                  WHEN q.policyType LIKE '%F' THEN 'Family Floater'
                  ELSE q.policyType
              END as policyType`),
        'c.group_code',
        'c.assigned_to',
        'q.status',
        'q.agent_id',
        'a.full_name as agent_first_name',
        'a.agent_id',
        db.raw("DATE_FORMAT(q.created_at, '%d/%m/%Y') as created_at"),
        db.raw("DATE_FORMAT(q.updated_at, '%d/%m/%Y') as updated_at"),
        db.raw("DATE_FORMAT(DATE_ADD(q.created_at, INTERVAL 30 DAY), '%d/%m/%Y') as valid_until")
      ])
      .leftJoin('customer_personal_info as c', 'q.customer_id', 'c.id')
      .leftJoin('agents as a', 'q.agent_id', 'a.id')
      .leftJoin('product_master as pm', 'q.product', 'pm.id');

    let paQuotationsQuery = db('pa_quotations as pq')
      .select([
        'pq.pa_quotation_id as id',
        'pq.quotation_number',
        db.raw(`CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as first_name`),
        'c.mobile',
        'c.email',
        'pq.product',
        'pm.product_name',
        db.raw("'Individual' as policyType"),
        'c.group_code',
        'c.assigned_to',
        'pq.status',
        'pq.agent_id',
        'a.full_name as agent_first_name',
        'a.agent_id',
        db.raw("DATE_FORMAT(pq.created_at, '%d/%m/%Y') as created_at"),
        db.raw("DATE_FORMAT(pq.updated_at, '%d/%m/%Y') as updated_at"),
        db.raw("DATE_FORMAT(DATE_ADD(pq.created_at, INTERVAL 30 DAY), '%d/%m/%Y') as valid_until")
      ])
      .leftJoin('customer_personal_info as c', 'pq.customer_id', 'c.id')
      .leftJoin('agents as a', 'pq.agent_id', 'a.id')
      .leftJoin('product_master as pm', 'pq.product', 'pm.id');

    // Apply filters based on user role
    if (userId.includes('ADM')) {
      // Admin sees all quotations, no additional filters needed
    } else if (userId.includes('RM')) {
      // RM sees only their quotations
      const agent = await db('agents').where('agent_id', userId).first();
      regularQuotationsQuery = regularQuotationsQuery.where('q.agent_id', agent.id);
      paQuotationsQuery = paQuotationsQuery.where('pq.agent_id', agent.id);
    } else {
      // Other employees see quotations for agents in their branches
      const agentIds = await getAgentIdsForEmployee(userId);

      if (agentIds.length === 0) {
        return [];
      }

      regularQuotationsQuery = regularQuotationsQuery.whereIn('q.agent_id', agentIds);
      paQuotationsQuery = paQuotationsQuery.whereIn('pq.agent_id', agentIds);
    }

    // Add ordering
    regularQuotationsQuery = regularQuotationsQuery.orderBy('q.created_at', 'desc');
    paQuotationsQuery = paQuotationsQuery.orderBy('pq.created_at', 'desc');

    // Execute queries only after all conditions have been applied
    const [regularResults, paResults] = await Promise.all([
      regularQuotationsQuery,
      paQuotationsQuery
    ]);

    // console.log('this is the RM query', {
    //   regularResults,
    //   paResults
    // })

    // Add source table identifiers
    const modifiedRegularResults = regularResults.map(quote => ({
      ...quote,
      source_table: 'quotations'
    }));

    const modifiedPaResults = paResults.map(quote => ({
      ...quote,
      source_table: 'pa_quotations'
    }));

    // Combine and sort results by creation date
    const allQuotations = [...modifiedRegularResults, ...modifiedPaResults]
      .sort((a, b) => {
        const dateA = new Date(b.created_at.split('/').reverse().join('-'));
        const dateB = new Date(a.created_at.split('/').reverse().join('-'));
        return dateA - dateB;
      });

    return allQuotations;
  } catch (error) {
    console.error('Error fetching quotations by user ID:', error);
    throw new Error('Failed to fetch quotations by user ID');
  }
}

// Fetch a single quotation by ID
const getQuotationById = async (id) => {
  return db('quotations').where('quotation_id', id).first();
}

// Get data by quotation number
const getQuotationByQuotationNumber = async (number) => {
  try {

    const quotation = await db('quotations as q')
      .distinct('q.*', 'sr.*')
      .leftJoin('quotation_member as qm', 'q.quotation_id', 'qm.quotation_id')
      .leftJoin('soap_responses as sr', 'q.quotation_id', 'sr.quotation_id')
      .where('q.quotation_number', number)

    return quotation;

  } catch (error) {
    console.error('Error fetching quotation by Quotation Number:', error);
    throw new Error("Error fetching quotation by Quotation Number");
  }
};


const createQuotationWithMembers = async (req, res) => {
  const trx = await db.transaction();
  try {
    const { quotationData, membersData } = req.body; // Expecting both quotation and members data in the request body

    // Check if membersData is defined and is an array
    if (!Array.isArray(membersData)) {
      throw new Error('Members data must be an array');
    }
    const QuotationDataToSave = {
      quotation_number: quotationData.quotation_number,
      product: quotationData.product,
      insurance_company: quotationData.insurance_company,
      main_product: quotationData.main_product,
      policyType: quotationData.policyType,
      customer_id: quotationData.customer_id,
      agent_id: quotationData.agent_id,
      status: quotationData.status,
      Created_by: quotationData.Created_by,
      duration: quotationData.duration,
      coPay: quotationData.copay,
    }

    // Create the quotation
    const createdQuotation = await trx('quotations').insert(QuotationDataToSave);
    const quotationId = createdQuotation[0]; // Get the last inserted ID

    const getPickListAPI = async (id) => {
      if (!id) return null;
      const pickListItem = await db('pick_list')
        .where('label_name', id)
        .first();
      return pickListItem ? pickListItem.api_name : null;
    };
    // Prepare member data with the quotation ID
    const membersWithQuotationId = Array.isArray(membersData)
      ? await Promise.all(membersData.map(async member => ({
        ...member,
        relation: await getPickListAPI(member.relation) || null,
        occupation: await getPickListAPI(member.occupation) || null,
        quotation_id: quotationId,
        medicalLoading: member.isSmoking === 'Y' ? 10 : 0,
      })))
      : [];

    const filteredMembers = membersWithQuotationId.map(member => ({
      member_id_no: member.member_id_no,
      member_id: member.member_id,
      quotation_id: quotationId,
      insuredName: member.insuredName,
      insuredDob: member.insuredDob,
      sub_product_id: member.cover_type_id,
      sumInsured: member.sumInsured,
      deductableDiscount: member.deductableDiscount,
      relation: member.relation,
      status: member.status,
      Created_by: member.Created_by,
    }));
    // Insert all members
    await trx('quotation_member').insert(filteredMembers);

    const customerId = quotationData.customer_id;

    // Fetch the pincode from Customer_address table
    const pincode = await getCostomerAddressByCustomerId(customerId);
    // Fetch the created quotation details
    const quotationDataWithPincode = {
      ...quotationData,
      pincode: pincode
    };

    // Add start and end dates based on duration
    const startDate = new Date(); // Current date
    const endDate = new Date();
    const duration = parseInt(quotationData.duration) || 12; // Default to 12 if not specified
    endDate.setMonth(endDate.getMonth() + duration); // Add months based on duration

    // Format dates as DD/MM/YYYY
    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    // Add formatted dates to quotation data
    const quotationDataWithDates = {
      ...quotationDataWithPincode,
      start_date: formatDate(startDate),
      end_date: formatDate(endDate)
    };

    let soapResponse = false;

    const COVER_TYPE_SUM_INSURED_VARISHTA = {
      'VARISHTA BIMA': [500000, 750000, 1000000],
    };

    const COVER_TYPE_SUM_INSURED_HEALTHSURAKSHA = {
      GOLD: [500000],
      PLATINUM: [600000, 750000, 800000, 900000, 1000000],
      TOPAZ: [100000, 200000, 300000, 400000, 500000],
      //RUBY: [600000, 750000, 1000000]
    };

    const COVER_TYPE_SUM_INSURED = {
      CLASSIC: [500000, 1000000],
      //PLATINUM: [1500000, 2000000, 2500000],
    };

    const COVER_TYPE_SUM_INSURED_HTF = {
      VITAL: [500000, 1000000],
      //SUPERIOR: [1500000, 2000000, 2500000],
      //PREMIUM: [5000000, 10000000],
    };
    const COVER_TYPE_SUM_INSURED_ADVANTAGE_TOPUP = {
      '50000': [500000],
      '100000': [500000],
      '200000': [500000, 750000, 1000000],
      '300000': [500000, 750000, 1000000],
      '400000': [500000, 750000, 1000000],
      '500000': [500000, 750000, 1000000],
      '750000': [750000, 1000000],
      '1000000': [1000000],
      // '1500000': [1500000, 2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
      // '2000000': [2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
      //'2500000': [2500000, 3000000, 4000000, 5000000],
      //'3000000': [3000000, 4000000, 5000000, 10000000],
      //'4000000': [4000000, 5000000, 10000000]
    };

    async function processCoverType(coverTypeMapping, membersWithQuotationId, quotationDataWithDates, quotationId, sendRequestFunction) {
      const coverType = membersData[0].coverType?.toUpperCase();
      if (!coverTypeMapping[coverType]) {
        console.log(`Unknown cover type: ${coverType}`);
        return;
      }

      const valueArray = coverTypeMapping[coverType];
      for (const value of valueArray) {
        const membersWithQuotationIdArray = membersWithQuotationId.map(singleMember => ({
          ...singleMember,
          sumInsured: value,
        }));

        console.log(JSON.stringify(membersWithQuotationIdArray, null, 2));
        await sendRequestFunction(membersWithQuotationIdArray, quotationDataWithDates, quotationId);
      }
    }

    // Main Logic
    if (quotationData.policyType === 'HAF' && quotationData.productMasterId === 'FG HEALTH ABSOLUTE') {
      await processCoverType(
        COVER_TYPE_SUM_INSURED,
        membersWithQuotationId,
        quotationDataWithDates,
        quotationId,
        sendSOAPRequest,
        trx
      );
      soapResponse = true;
    } else if (quotationData.policyType === 'HTF' && quotationData.productMasterId === 'FG HEALTH TOTAL') {
      await processCoverType(
        COVER_TYPE_SUM_INSURED_HTF,
        membersWithQuotationId,
        quotationDataWithDates,
        quotationId,
        sendSOAPRequestHealthTotal,
        trx
      );
      soapResponse = true;
    } else if (quotationData.policyType === 'HTF' && quotationData.productMasterId === 'FG ADVANTAGE TOP UP') {
      // Get the deductible from the first member
      const deductible = membersWithQuotationId[0].deductableDiscount;
      const sumInsuredOptions = COVER_TYPE_SUM_INSURED_ADVANTAGE_TOPUP[deductible];

      // Check if sumInsuredOptions is defined
      if (!sumInsuredOptions) {
        console.log(`No sum insured options found for deductible: ${deductible}`);
        throw new Error(`No sum insured options found for deductible: ${deductible}`);
      }

      // Iterate through sum insured options
      for (const value of sumInsuredOptions) {
        const membersWithQuotationIdArray = membersWithQuotationId.map(singleMember => ({
          ...singleMember,
          sumInsured: value,
        }));

        await sendSOAPRequestAdvantageTopUp(membersWithQuotationIdArray, quotationDataWithDates, quotationId, trx);
        soapResponse = true;
      }

    } else if (quotationData.policyType === 'FHF' && quotationData.productMasterId === 'FG HEALTH SURAKSHA') {
      await processCoverType(
        COVER_TYPE_SUM_INSURED_HEALTHSURAKSHA,
        membersWithQuotationId,
        quotationDataWithDates,
        quotationId,
        sendSOAPRequestHealthSuraksha,
        trx
      );
      soapResponse = true;
    }
    else if (quotationData.policyType === 'VBF' && quotationData.productMasterId === 'FG VARISHTHA BIMA') {
      await processCoverType(
        COVER_TYPE_SUM_INSURED_VARISHTA,
        membersWithQuotationId,
        quotationDataWithDates,
        quotationId,
        sendSOAPRequestVarishtaBima,
        trx
      );
      soapResponse = true;
    }

    else if (quotationData.policyType === 'HAI' && quotationData.productMasterId === 'FG HEALTH ABSOLUTE') {
      await sendSOAPRequest(membersWithQuotationId, quotationDataWithDates, quotationId, trx);
      soapResponse = true;
    } else if (quotationData.policyType === 'HTI' && quotationData.productMasterId === 'FG HEALTH TOTAL') {
      await sendSOAPRequestHealthTotal(membersWithQuotationId, quotationDataWithDates, quotationId, trx);
      soapResponse = true;
    } else if (quotationData.policyType === 'HTI' && quotationData.productMasterId === 'FG ADVANTAGE TOP UP') {
      await sendSOAPRequestAdvantageTopUp(membersWithQuotationId, quotationDataWithDates, quotationId, trx);
      soapResponse = true;
    } else if (quotationData.policyType === 'FHI' && quotationData.productMasterId === 'FG HEALTH SURAKSHA') {
      await sendSOAPRequestHealthSuraksha(membersWithQuotationId, quotationDataWithDates, quotationId, trx);
      soapResponse = true;
    }
    else if (quotationData.policyType === 'VBI' && quotationData.productMasterId === 'FG VARISHTHA BIMA') {
      await sendSOAPRequestVarishtaBima(membersWithQuotationId, quotationDataWithDates, quotationId, trx);
      soapResponse = true;
    }
    else {
      console.log('Unknown policy type or product.');
    }

    if (!soapResponse) {
      throw new Error('No SOAP response found');
    }

    //     await trx.commit(); // Commit the transaction
    //     res.status(200).json({
    //       message: "Quotation and members added successfully",
    //       data: {
    //         quotation: quotationDataWithDates,
    //         members: filteredMembers,
    //       },
    //     });
    //   } catch (error) {
    //     await trx.rollback(); // Rollback on error
    //     console.error("Transaction failed:", error);
    //     res.status(500).json({
    //       message: "An error occurred while processing the request",
    //       error: error.message,
    //     });
    //   }
    // }

    await trx.commit();
    res.status(201).json({
      message: 'Quotation created successfully with all related data',
      quotation_id: quotationId
    });

  } catch (error) {
    await trx.rollback();

    // Format the error response
    const errorResponse = {
      status: 'error',
      message: error.message || 'An error occurred while creating the quotation',
      // You can add additional error details if needed
      details: error.response?.data || error.data
    };

    // Send the formatted error response
    return res.status(400).json(errorResponse);
  }

};

const getCostomerAddressByCustomerId = async (customerId) => {
  try {
    // Query the database for the customer address
    const address = await db('customer_address')
      .select('current_pincode')
      .where('customer_id', customerId)
      .first();

    if (!address) {
      throw new Error(`No address found for customer_id: ${customerId}`);
    }

    return address.current_pincode;
  } catch (error) {
    console.error('Error fetching customer address:', error);
    throw error;
  }
};


// Update an existing quotation
const updateQuotation = async (id, updatedData) => {
  return db('quotations')
    .where('quotation_number', id)
    .update(updatedData)
    .returning('*');
}

module.exports = {
  //createQuotation,
  getAllQuotations,
  getAllQuotationsByUserId,
  getQuotationById,
  updateQuotation,
  createQuotationWithMembers,
  getQuotationWithResponseAndMembers,
  getQuotationWithResponseAndMembersById,
  getCostomerAddressByCustomerId,
  getQuotationByQuotationNumber
};
