import { createSlice } from '@reduxjs/toolkit';
import { getAllPAoccupationLists } from '../actions/action';

const initialState = {
    PA_occupation_Lists: [],
    isLoading: false,
    error: null,
    OccupationOptions: [],

};

const PA_occupation_list_slice = createSlice({
    name: 'PAoccupationList',
    initialState,
    extraReducers: (builder) => {
        builder.addCase(getAllPAoccupationLists.pending, (state) => {
            state.isLoading = true;
        })
            .addCase(getAllPAoccupationLists.fulfilled, (state, action) => {
                state.isLoading = false;
                state.PA_occupation_Lists = action.payload;
                // Organize options by type
                state.OccupationOptions = action.payload.filter(item => item.type_name === 'occupation');

            })
            .addCase(getAllPAoccupationLists.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.error.message;
            })
    }
});

export default PA_occupation_list_slice.reducer;