const express = require('express');
const EmployeeAddressController = require('../Controllers/employee_address_controller');
const router = express.Router();

// Route to create a new Employee info
router.post('/', EmployeeAddressController.createEmployeeAddress);

router.get('/employee_id/:id', EmployeeAddressController.getEmployeeAddressByEmployeeId);

router.get('/', EmployeeAddressController.getEmployeeAddress);

router.get('/:id', EmployeeAddressController.getCostomerAddressById);

router.put('/:id', EmployeeAddressController.updateEmployeeAddress);

router.delete('/:id', EmployeeAddressController.deleteEmployeeAddress);

module.exports = router;