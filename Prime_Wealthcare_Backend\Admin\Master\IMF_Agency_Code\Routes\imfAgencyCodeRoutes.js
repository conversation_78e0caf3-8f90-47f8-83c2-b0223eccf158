const express = require('express');
const router = express.Router();
const agencyCodeController = require('../Controllers/imfAgencyCodeController');

router.get('/',agencyCodeController. getAgencyCodes);
router.get('/:id',agencyCodeController. getAgencyCode);
router.post('/',agencyCodeController. createAgencyCode);
router.put('/:id', agencyCodeController.updateAgencyCode);
router.delete('/:id',agencyCodeController. deleteAgencyCode);
router.put('/reinstate/:id',agencyCodeController. reinstateAgencyCode);
//router.get('/agency-codes/:branchName', agencyCodeController.getAgencyCodesByBranch);

module.exports = router;