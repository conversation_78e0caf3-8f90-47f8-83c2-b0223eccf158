import { createSlice } from '@reduxjs/toolkit';
import {
    getAllNetworks,
    getNetworkById,
    getNetworkByName,
    createNetwork,
    updateNetwork,
    deleteNetwork,
    reinstateNetwork,
    getNetworksByCriteria,
    getNetworkByInsuranceCompanyId,
} from '../../actions/action'; // Adjust path as needed
import { toast } from 'react-toastify'; // Assuming you're using react-toastify

const networkSlice = createSlice({
    name: 'networks',
    initialState: {
        networks: [],
        network: null,
        status: 'idle',
        error: null,
    },
    extraReducers: (builder) => {
        builder
            // Get All Networks
            .addCase(getAllNetworks.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getAllNetworks.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.networks = action.payload;
            })
            .addCase(getAllNetworks.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
            })

            // Get Network By Insurance Company ID
            .addCase(getNetworkByInsuranceCompanyId.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getNetworkByInsuranceCompanyId.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.networks = action.payload;
            })
            .addCase(getNetworkByInsuranceCompanyId.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to find networks');
            })

            // Get Network By ID
            .addCase(getNetworkById.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getNetworkById.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.network = action.payload;
            })
            .addCase(getNetworkById.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
            })

            // Get Network By Name
            .addCase(getNetworkByName.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getNetworkByName.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.networks = action.payload;
            })
            .addCase(getNetworkByName.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to find networks');
            })

            // Create Network
            .addCase(createNetwork.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(createNetwork.fulfilled, (state, action) => {
                state.networks.push(action.payload);
                toast.success('Network created successfully');
            })
            .addCase(createNetwork.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to create network');
            })

            // Update Network
            .addCase(updateNetwork.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(updateNetwork.fulfilled, (state, action) => {
                const index = state.networks.findIndex(network => network.id === action.payload.id);
                if (index !== -1) {
                    state.networks[index] = action.payload;
                }
                toast.success('Network updated successfully');
            })
            .addCase(updateNetwork.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to update network');
            })

            // Delete Network
            .addCase(deleteNetwork.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(deleteNetwork.fulfilled, (state, action) => {
                state.networks = state.networks.filter(network => network.id !== action.payload);
                toast.success('Network deleted successfully');
            })
            .addCase(deleteNetwork.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to delete network');
            })

            // Reinstate Network
            .addCase(reinstateNetwork.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(reinstateNetwork.fulfilled, (state, action) => {
                const index = state.networks.findIndex(network => network.id === action.payload.id);
                if (index !== -1) {
                    state.networks[index] = action.payload;
                }
                toast.success('Network reinstated successfully');
            })
            .addCase(reinstateNetwork.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to reinstate network');
            })

            // Get Networks by Criteria
            .addCase(getNetworksByCriteria.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getNetworksByCriteria.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.networks = action.payload;
            })
            .addCase(getNetworksByCriteria.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
            });
    },
});

export default networkSlice.reducer;
