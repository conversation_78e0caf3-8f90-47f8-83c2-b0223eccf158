const SubProductAgeSum = require('../Models/subProductAgeSum');

// Create new product
exports.create = async (req, res, next) => {
    try {
        const productData = req.body;
        const data = await SubProductAgeSum.create(productData);
        res.status(201).json({
            ...productData,
            id: data
        });
    } catch (error) {
        next(error);
    }
};

// Update product by ID
exports.update = async (req, res, next) => {
    try {
        const { id } = req.params;
        const productData = req.body;
        const data = await SubProductAgeSum.update(id, productData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Soft delete (deactivate) product by ID
exports.delete = async (req, res, next) => {
    try {
        const { id } = req.params;
        await SubProductAgeSum.delete(id);
        res.status(200).json({ message: 'Sub product deactivated successfully' });
    } catch (error) {

        next(error);
    }
};