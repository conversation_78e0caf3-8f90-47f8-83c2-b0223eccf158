import React from 'react';
import { useSelector } from 'react-redux';
import Backdrop from '@mui/material/Backdrop';
import Box from '@mui/material/Box';

const Loader = () => {
  const isLoading = useSelector((state) => state.loading.isLoading);

  return React.createElement(
    Backdrop,
    {
      sx: {
        color: '#fff',
        zIndex: (theme) => theme.zIndex.drawer + 1,
        backgroundColor: 'rgba(0, 0, 0, 0.7)'
      },
      open: isLoading
    },
    React.createElement(Box, {
      sx: {
        position: 'relative',
        width: '80px',
        height: '80px',
        '&::before': {
          content: '"P"',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          fontSize: '64px',
          fontWeight: 'bold',
          color: '#1976d2',
          animation: 'glow 2s ease-in-out infinite',
        },
        '@keyframes glow': {
          '0%': {
            textShadow: '0 0 5px #1976d2, 0 0 10px #1976d2, 0 0 15px #1976d2',
            transform: 'translate(-50%, -50%) scale(1) rotate(0deg)',
          },
          '50%': {
            textShadow: '0 0 20px #1976d2, 0 0 30px #1976d2, 0 0 40px #1976d2',
            transform: 'translate(-50%, -50%) scale(1.2) rotate(5deg)',
          },
          '100%': {
            textShadow: '0 0 5px #1976d2, 0 0 10px #1976d2, 0 0 15px #1976d2',
            transform: 'translate(-50%, -50%) scale(1) rotate(0deg)',
          },
        },
      }
    })
  );
};

export default Loader; 