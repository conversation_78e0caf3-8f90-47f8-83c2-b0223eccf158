const imfBranchModel = require('../Models/imfBranch');

// Controller to get all IMF branches
const getAllIMFBranches = async (req, res, next) => {
  try {
    const branches = await imfBranchModel.getAllIMFBranches();
    res.status(200).json(branches);
  } catch (error) {
    //console.error('Error fetching IMF branches:', error);
    next(error);
  }
};

// Controller to get an IMF branch by ID
const getIMFBranchById = async (req, res, next) => {
  const { id } = req.params;
  try {
    const branch = await imfBranchModel.getIMFBranchById(id);
    if (branch) {
      res.status(200).json(branch);
    } else {
      res.status(404).json({ message: 'IMF Branch not found' });
    }
  } catch (error) {
   // console.error('Error fetching IMF branch:', error);
   next(error);
  }
};

// Controller to create a new IMF branch
const createIMFBranch = async (req, res, next) => {
  const branchData = req.body;
  try {
    const newBranch = await imfBranchModel.createIMFBranch(branchData);
    res.status(201).json({ message: 'IMF Branch created successfully', id: newBranch[0] });
  } catch (error) {
    //console.error('Error creating IMF Branch:', error);
    next(error);
  }
};

// Controller to update an existing IMF branch
const updateIMFBranch = async (req, res, next) => {
  const { id } = req.params;
  const branchData = req.body;
  try {
    const updatedRows = await imfBranchModel.updateIMFBranch(id, branchData);
    if (updatedRows) {
      res.status(200).json({ message: 'IMF Branch updated successfully' });
    } else {
      res.status(404).json({ message: 'IMF Branch not found' });
    }
  } catch (error) {
    console.error('Error updating IMF Branch:', error);
    next(error);
  }
};

// Controller to soft delete an IMF branch
const softDeleteIMFBranch = async (req, res, next) => {
  const { id } = req.params;
  try {
    const deletedRows = await imfBranchModel.softDeleteIMFBranch(id);
    if (deletedRows) {
      res.status(200).json({ message: 'IMF Branch soft deleted successfully' });
    } else {
      res.status(404).json({ message: 'IMF Branch not found' });
    }
  } catch (error) {
    next(error);
  }
};

// Controller to reinstate a soft-deleted IMF branch
const reinstateIMFBranch = async (req, res, next) => {
  const { id } = req.params;
  try {
    const reinstatedRows = await imfBranchModel.reinstateIMFBranch(id);
    if (reinstatedRows) {
      res.status(200).json({ message: 'IMF Branch reinstated successfully' });
    } else {
      res.status(404).json({ message: 'IMF Branch not found' });
    }
  } catch (error) {
    //console.error('Error reinstating IMF Branch:', error);
    next(error);
  }
};
// Controller to search IMF branch by name
const searchIMFBranch = async (req, res, next) => {
  const { name } = req.params;
  try {
    const branches = await imfBranchModel.getIMFBranchesByName(name);
    res.status(200).json(branches);
  } catch (error) {
    next(error);
  }
};

// Controller to get IMF branches by criteria
const getIMFBranchesByCriteria = async (req, res, next) => {
  const { criteria } = req.params;
  let data;

  try {
    switch (criteria) {
      case 'none':
        data = await imfBranchModel.getAllIMFBranches();
        break;
      case 'newLastWeek':
        data = await imfBranchModel.newLastWeek();
        break;
      case 'newThisWeek':
        data = await imfBranchModel.newThisWeek();
        break;
      case 'deactivatedThisWeek':
        data = await imfBranchModel.deactivatedThisWeek();
        break;
      case 'deactivatedLastWeek':
        data = await imfBranchModel.deactivatedLastWeek();
        break;
      case 'editedThisWeek':
        data = await imfBranchModel.editedThisWeek();
        break;
      case 'editedLastWeek':
        data = await imfBranchModel.editedLastWeek();
        break;
      default:
        return res.status(400).json({ message: 'Invalid criteria' });
    }

    res.status(200).json(data);
  } catch (error) {
    //console.error('Error fetching IMF branches by criteria:', error);
    next(error);
  }
};



module.exports = {
  getAllIMFBranches,
  getIMFBranchById,
  createIMFBranch,
  updateIMFBranch,
  softDeleteIMFBranch,
  reinstateIMFBranch,
  getIMFBranchesByCriteria,
  searchIMFBranch
  
};
