exports.up = function (knex) {
    return knex.schema.hasTable('agent_loan').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('agent_loan', (table) => {
                table.increments('id').primary();
                table.integer('agent_id').unsigned().references('id').inTable('agents').onDelete('CASCADE').notNullable();
                table.string('loan_id').nullable();
                table.index('loan_id');
                table.integer('loan_amount').notNullable();
                table.string('loan_type').notNullable();
                table.integer('paid_amount').notNullable();
                table.integer('emi').notNullable();
                table.date('issue_date').nullable();
                table.date('start_date').nullable();
                table.date('end_date').nullable();
                table.integer('tenure').notNullable();
                table.text('remarks').nullable();
                table.string('admin_approval').notNullable();
                table.timestamp('created_at').defaultTo(knex.fn.now());
                table.string('created_by').notNullable();
                table.timestamp('updated_at').defaultTo(knex.fn.now());
                table.string('updated_by').notNullable();
                table.boolean('status').notNullable().defaultTo(true);
            });
        }
    }).then(() => {
        return knex.schema.hasTable('agent_loan_emi').then(function (exists) {
            if (!exists) {
                return knex.schema.createTable('agent_loan_emi', (table) => {
                    table.increments('id').primary();
                    table.string('agent_loan_id').references('loan_id').inTable('agent_loan').onDelete('CASCADE').notNullable;
                    table.string('month').notNullable();
                    table.integer('year').notNullable();
                    table.integer('emi_amount').notNullable();
                    table.integer('closing_balance').nullable();
                    table.date('paid_date').nullable();
                    table.timestamp('created_at').defaultTo(knex.fn.now());
                    table.string('created_by').notNullable();
                    table.timestamp('updated_at').defaultTo(knex.fn.now());
                    table.string('updated_by').notNullable();
                    table.boolean('status').notNullable().defaultTo(true);
                });
            }
        });
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('agent_loan_emi').then(() => {
        return knex.schema.dropTableIfExists('agent_loan');
    });
};
