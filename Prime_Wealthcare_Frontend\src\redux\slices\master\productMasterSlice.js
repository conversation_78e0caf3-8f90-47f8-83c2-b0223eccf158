import { createSlice } from '@reduxjs/toolkit';
import {
    getAllMasterProducts,
    createMasterProduct,
    getMasterProductById,
    updateMasterProduct,
    deleteMasterProduct,
    reinstateMasterProduct,
    searchMasterProduct,
    getFilterMasterProducts,
    getMasterProductByMainProductAndInsuranceCompany,
} from '../../actions/action';
import { toast } from 'react-toastify';

const initialState = {
    products: [],
    product: {}, // For single product details
    loading: false,
    error: null,
};

const productMasterSlice = createSlice({
    name: 'productMaster',
    initialState,
    reducers: {
        clearProductMaster: (state) => {
            state.products = [];
            state.product = {};
        },
    },
    extraReducers: (builder) => {
        builder
            // Get all products
            .addCase(getAllMasterProducts.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAllMasterProducts.fulfilled, (state, action) => {
                state.loading = false;
                state.products = action.payload; // Replace products array
            })
            .addCase(getAllMasterProducts.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || action.error.message;
                toast.error('Failed to load products'); // Error toast
            })

            // Get product by main product and insurance company
            .addCase(getMasterProductByMainProductAndInsuranceCompany.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMasterProductByMainProductAndInsuranceCompany.fulfilled, (state, action) => {
                state.loading = false;
                state.products = action.payload; // Set the product field for detailed view
            })
            .addCase(getMasterProductByMainProductAndInsuranceCompany.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load master product details'); // Error toast
            })

            // Create a new product
            .addCase(createMasterProduct.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createMasterProduct.fulfilled, (state, action) => {
                state.loading = false;
                state.products.push(action.payload); // Add the new product to the array
                toast.success('Master product created successfully'); // Success toast
            })
            .addCase(createMasterProduct.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to create master product'); // Error toast
            })

            // Get product by ID
            .addCase(getMasterProductById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getMasterProductById.fulfilled, (state, action) => {
                state.loading = false;
                state.product = action.payload; // Set the product field for detailed view
            })
            .addCase(getMasterProductById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load master product details'); // Error toast
            })

            // Update an existing product
            .addCase(updateMasterProduct.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateMasterProduct.fulfilled, (state, action) => {
                state.loading = false;

                const index = state.products.findIndex(
                    (product) => product.id === action.payload.id
                );
                if (index !== -1) {
                    state.products[index] = action.payload; // Update the product in the array
                    toast.success('Master product updated successfully'); // Success toast
                }
            })
            .addCase(updateMasterProduct.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to update master product'); // Error toast
            })

            // Delete product by ID
            .addCase(deleteMasterProduct.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteMasterProduct.fulfilled, (state, action) => {
                state.loading = false;
                state.products = state.products.filter(
                    (product) => product.id !== action.payload
                ); // Remove the deleted product from the array
                toast.success('Master product deleted successfully'); // Success toast
            })
            .addCase(deleteMasterProduct.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to delete master product'); // Error toast
            })

            // Reinstate a deleted product by ID
            .addCase(reinstateMasterProduct.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(reinstateMasterProduct.fulfilled, (state, action) => {
                state.loading = false;
                state.products.push(action.payload); // Add the reinstated product to the array
                toast.success('Master product reinstated successfully'); // Success toast
            })
            .addCase(reinstateMasterProduct.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to reinstate master product'); // Error toast
            })

            // Search master product by name
            .addCase(searchMasterProduct.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(searchMasterProduct.fulfilled, (state, action) => {
                state.loading = false;
                state.products = action.payload; // Replace the products with search results
            })
            .addCase(searchMasterProduct.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to find master products'); // Error toast
            })

            // Filter master products by criteria
            .addCase(getFilterMasterProducts.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getFilterMasterProducts.fulfilled, (state, action) => {
                state.loading = false;
                state.products = action.payload; // Replace the products with filtered results
            })
            .addCase(getFilterMasterProducts.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to apply filter'); // Error toast
            });
    },
});

export const { clearProductMaster } = productMasterSlice.actions;
export default productMasterSlice.reducer;
