const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../knexfile');
const db = knex(knexConfig.development);
const {generateNomineeDeatilsXML} = require('../../../Reusable/xmlComponents')

// Environment variables
require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URL;
const SOAP_ACTION = process.env.SOAP_ACTION;
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;


// Generate Insured XML dynamically
const generateInsuredXML = (memberData) => {

    //const hasChildRelation = memberData.some(member => member.relation === 'SON' || member.relation === 'DAUG');

    return memberData
        .map((memberData, index) => `
        <Insured>
            <FirstName>${memberData.first_name}</FirstName>
            <LastName>${memberData.last_name}</LastName>
            <NomineeName>${memberData.nominee_name}</NomineeName>
            <NomineesRelationshipWithInsured>${memberData.nominee_relation}</NomineesRelationshipWithInsured>
            <Gender>${memberData.gender}</Gender>
            <Birthdate>${memberData.date_of_birth}</Birthdate>
            <Age>${memberData.member_age}</Age>
            <AgeIndicater>Y</AgeIndicater>
            <Nationality>IND</Nationality>
            <OccupationCode>${memberData.occupation}</OccupationCode>
            <RelationshipWithApplicant>${memberData.relation}</RelationshipWithApplicant>
            <AppointeeName/>
            <AppointeeRelationshipwithNominee/>
            <PreExistingDisease>${memberData.preExistingDisease}</PreExistingDisease>
            <AnnualIncome>${memberData.annual_income}</AnnualIncome>
            <PrimaryCoverReq>Y</PrimaryCoverReq>
            <Exclusion/>
            <CumulativeBonus/>
            ${generateNomineeDeatilsXML(memberData, index)}
            <PrimaryCover>
                <Cover>
                    <CoverCode>AD</CoverCode>
                    <CoverName>Accidental death</CoverName>
                    <SumInsured>${memberData.ad_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>PP</CoverCode>
                    <CoverName>Permanent Partial Disabilement</CoverName>
                    <SumInsured>${memberData.pp_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                <Cover>
                    <CoverCode>PT</CoverCode>
                    <CoverName>Permanent Total Disablement</CoverName>
                    <SumInsured>${memberData.pt_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>
                ${memberData.tt_sum_insured > 0 ? `
                <Cover>
                    <CoverCode>TT</CoverCode>
                    <CoverName>Temporary Total Disablement</CoverName>
                    <SumInsured>${memberData.tt_sum_insured}</SumInsured>
                    <Premium />
                    <CoverType>M</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
            </PrimaryCover>
         <AdditionalCoverReq>Y</AdditionalCoverReq >
             <AdditionalCover>
               
                <Cover>
                    <CoverCode>RF</CoverCode>
                    <CoverName>Repatriation and Funeral Expenses</CoverName>
                    <SumInsured>${memberData.RF_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>
                ${memberData.AA_suminsured > 0 ? `
                <Cover>
                    <CoverCode>AA</CoverCode>
                    <CoverName>Adaptation Allowance</CoverName>
                    <SumInsured>${memberData.AA_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.CS_suminsured > 0 && (memberData.relation === 'SELF' || memberData.relation === 'SPOU') ? `
                <Cover>
                    <CoverCode>CS</CoverCode>
                    <CoverName>Child Education Support</CoverName>
                    <SumInsured>${memberData.CS_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.FT_suminsured > 0 ? `
                <Cover>
                    <CoverCode>FT</CoverCode>
                    <CoverName>Family Transportation Allowance</CoverName>
                    <SumInsured>${memberData.FT_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.HC_suminsured > 0 ? `
                <Cover>
                    <CoverCode>HC</CoverCode>
                    <CoverName>Hospital Cash Allowance</CoverName>
                    <SumInsured>${memberData.HC_suminsured}</SumInsured>
                    <Premium />
                    <CoverType>S</CoverType>
                    <Times>* </Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.ME_suminsured > 0 ? `
                <Cover>
                    <CoverCode>ME</CoverCode>
                    <CoverName>Accidental Hospitalisation</CoverName>
                    <SumInsured>${memberData.ME_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.LP_suminsured > 0 && (memberData.relation === 'SELF' || memberData.relation === 'SPOU') ? `
                <Cover>
                    <CoverCode>LP</CoverCode>
                    <CoverName>Loan Protecter</CoverName>
                    <SumInsured>${memberData.LP_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.LS_suminsured > 0 ? `
                <Cover>
                    <CoverCode>LS</CoverCode>
                    <CoverName>Life Support Benifits</CoverName>
                    <SumInsured>${memberData.LS_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.AM_suminsured > 0 ? `
                <Cover>
                    <CoverCode>AM</CoverCode>
                    <CoverName>Accidental Medical Expenses</CoverName>
                    <SumInsured>${memberData.AM_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
                ${memberData.BB_suminsured > 0 ? `
                <Cover>
                    <CoverCode>BB</CoverCode>
                    <CoverName>Broken Bones</CoverName>
                    <SumInsured>${memberData.BB_suminsured}</SumInsured>
                    <Premium/>
                    <CoverType>S</CoverType>
                    <Times>*</Times>
                    <Benefit>*</Benefit>
                </Cover>` : ''}
            </AdditionalCover>
        </Insured > `)
        .join('');
};
const SOAP_BODY = (uid, customerData, proposalData, insuredXML, coverageClass) => `
  <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
            <tem:Product>PA</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
    <Uid>${uid}</Uid>
   <VendorCode>${VENDOR_CODE}</VendorCode>
   <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
    <SentToOutSourcePrint>0</SentToOutSourcePrint>
    <WinNo/>
    <ApplicationNo/>
    <PolicyHeader>
        <PolicyStartDate>${proposalData.start_date}</PolicyStartDate>
        <PolicyEndDate>${proposalData.end_date}</PolicyEndDate>
        <AgentCode>${proposalData.imf_code}</AgentCode>
        <BranchCode>${proposalData.branch_code}</BranchCode>
        <MajorClass>PAC</MajorClass>
        <ContractType>PAL</ContractType>
        <METHOD>CRT</METHOD>
        <PolicyIssueType>I</PolicyIssueType>
        <PolicyNo/>
         <ClientID>${proposalData.client_id}</ClientID>
    <ReceiptNo>${proposalData.receipt_no}</ReceiptNo>
    </PolicyHeader>
    <POS_MISP>
        <Type></Type>
        <PanNo>${customerData.pan_number}</PanNo>
    </POS_MISP>
    <Client>
        <ClientType>I</ClientType>
        <CreationType>C</CreationType>
        <Salutation>${proposalData.salutation}</Salutation>
        <FirstName>${customerData.first_name}</FirstName>
        <LastName>${customerData.last_name}</LastName>
        <DOB>${customerData.date_of_birth}</DOB>
        <Gender>${customerData.gender}</Gender>
        <MaritalStatus>${customerData.marital_status}</MaritalStatus>
        <Occupation>${customerData.occupation}</Occupation>
        <PANNo>${customerData.pan_number}</PANNo>
        <GSTIN/>
        <AadharNo />
        <CKYCNo>${proposalData.ckyc_number}</CKYCNo>
        <CKYCRefNo>${proposalData.proposal_Id}</CKYCRefNo>
        <EIANo></EIANo>
        <Address1>
            <AddrLine1>${customerData.address_line1}</AddrLine1>
            <AddrLine2>${customerData.address_line2Q}</AddrLine2>
            <AddrLine3/>
            <Landmark/>
            <Pincode>${customerData.pincode}</Pincode>
            <City>${customerData.city}</City>
            <State>${customerData.state}</State>
            <Country>IND</Country>
            <AddressType>R</AddressType>
            <HomeTelNo/>
            <OfficeTelNo/>
            <FAXNO />
            <MobileNo>${customerData.mobile}</MobileNo>
            <EmailAddr>${customerData.email}</EmailAddr>
        </Address1>
        <Address2>
            <AddrLine1>${customerData.address_line1}</AddrLine1>
            <AddrLine2>${customerData.address_line2}</AddrLine2>
            <AddrLine3/>
            <Landmark/>
            <Pincode>${customerData.pincode}</Pincode>
            <City>${customerData.city}</City>
            <State>${customerData.state}</State>
            <Country>IND</Country>
            <AddressType>R</AddressType>
            <HomeTelNo/>
            <OfficeTelNo/>
            <FAXNO/>
            <MobileNo>${customerData.mobile}</MobileNo>
            <EmailAddr>${customerData.email}</EmailAddr>
        </Address2>
        <VIPFlag>N</VIPFlag>
        <VIPCategory/>
    </Client>
    <Receipt>
        <UniqueTranKey>${proposalData.WS_P_ID}</UniqueTranKey>
        <CheckType/>
        <BSBCode/>
        <TransactionDate>${proposalData.transaction_date}</TransactionDate>
        <ReceiptType>IVR</ReceiptType>
        <Amount>${proposalData.PremiumAmount}</Amount>
        <TCSAmount/>
        <TranRefNo>${proposalData.PGID}</TranRefNo>
        <TranRefNoDate>${proposalData.transaction_date}</TranRefNoDate>
    </Receipt>
    <Risk>
        <IsfgEmployee>N</IsfgEmployee>
        <Duration>${proposalData.duration}</Duration>
        <Installments>FULL</Installments>
        <PaymentType/>
        <Discount/>
        <CoverageClass>${coverageClass}</CoverageClass>
        <CoverageClassCode>PAL</CoverageClassCode>
        <Plan>Accident Suraksha</Plan>
        <Unit>1</Unit>
        <Claimfreeyears>0</Claimfreeyears>
        <OccupationClass>Y</OccupationClass>
        <CumulativeBonusAmount/>
        <AlreadyPAPolicy/>
        <AdditionalRemarks/>
        <PendingRemarks/>
        <BranchReferenceID/>
        <FGBankBranchStaffID/>
        <BankStaffID/>
        <BankCustomerID/>
        <BancaChannel/>
        <PartnerRefNo/>
        <PayorID/>
        <PayerName/>
         ${insuredXML}
          <GrossPremium></GrossPremium>
        <ServiceTax></ServiceTax>
     </Risk>
</Root>]]></tem:XML>
          </tem:CreatePolicy>
   </soapenv:Body>
</soapenv:Envelope>` ;



// Save SOAP response to database
const sendSOAPRequest = async (membersData, proposalData, proposalId, customerData) => {
    try {
        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };
        // Add logging statements
        // console.log("Members Data:", membersData);
        // Ensure membersData is an array
        const membersArray = Array.isArray(membersData) ? membersData : [membersData];

        // Generate unique UID and Member XML
        const uniqueUID = uuidv4();
        const membersXML = generateInsuredXML(membersArray);

        const coverageClass = membersArray.length === 1 && membersArray[0].relation === 'SELF' ? 'Individual' : 'Family';

        // Generate SOAP body dynamically
        const requestBody = SOAP_BODY(uniqueUID, customerData, proposalData, membersXML, coverageClass);
        console.log('SOAP Envelope:', requestBody);
        await db('policy_logs').insert({
            quotation_number: proposalData.quotation_number,
            policy_type: 'PA',
            request_payload: requestBody, // Raw XML request
            response_payload: response.data, // Raw XML response
            status: 'SUCCESS',
            error_message: null,
            created_at: db.fn.now()
        });
        // Send the SOAP request
        const response = await axios.post(SOAP_API_URL, requestBody, { headers });
        console.log('SOAP Response:', response.data);
        // Parse the XML response
        const jsonResponse = await parseStringPromise(response.data);
        const createPolicyResult = jsonResponse['s:Envelope']['s:Body'][0]['CreatePolicyResponse'][0]['CreatePolicyResult'][0];
        const innerXmlResult = await parseStringPromise(createPolicyResult);
        const root = innerXmlResult.Root;

        // Handle validation errors
        if (root.Status?.[0] === 'Fail') {
            // Check for duplicate policy error
            if (root.ValidationError?.[0]?.includes('already created')) {
                const errorResponse = {
                    success: false,
                    error: {
                        message: 'Policy already exists',
                        type: 'DuplicatePolicy',
                        details: root.ValidationError[0]
                    }
                };
                throw errorResponse;
            }

            // Check for CKYC validation error
            if (root.Message?.[0]?.includes('CKYC') || root.Message?.[0]?.includes('KYC not completed')) {
                await db('proposals_pa')
                    .where('ProposalNumber', proposalData.proposal_number)
                    .update({ status: 'CKYC Pending' });

                const errorResponse = {
                    success: false,
                    status: 'CKYC Pending',
                    error: {
                        message: 'KYC verification pending',
                        type: 'CKYCPending',
                        details: root.Message[0]
                    }
                };
                throw errorResponse;
            }

            throw new Error(root.ValidationError?.[0] || root.Message?.[0] || 'Validation failed');
        }

        // Handle receipt failure
        if (root.Receipt?.[0]?.Status?.[0] === 'Fail') {
            const errorResponse = {
                success: false,
                error: {
                    message: 'Receipt creation failed',
                    type: 'ReceiptError',
                    details: root.Receipt[0].ErrorMessage[0]
                }
            };
            throw errorResponse;
        }

        // Extract all relevant information
        const formattedResponse = {
            client: {
                status: root.Client[0].Status[0],
                clientId: root.Client[0].ClientId[0],
                errorMessage: root.Client[0].ErrorMessage[0]
            },
            receipt: {
                status: root.Receipt[0].Status[0],
                receiptNo: root.Receipt[0].ReceiptNo[0],
                errorMessage: root.Receipt[0].ErrorMessage[0]
            },
            policy: {
                status: root.Policy[0].Status[0],
                policyNo: root.Policy[0].PolicyNo[0],
                message: root.Policy[0].Message[0]
            },
            application: {
                winNo: root.Application[0].WinNo[0],
                applicationNo: root.Application[0].ApplicationNo[0]
            }
        };

        const clientId = root.Client?.[0]?.ClientId?.[0] || null;
        const receiptNo = root.Receipt?.[0]?.ReceiptNo?.[0] || null;

        // Check if the values already exist in the database
        const existingProposal = await db('proposals_pa')
            .where('ProposalNumber', proposalData.proposal_number)
            .select('client_id', 'receipt_no')
            .first();
        // Update the database only if the values are not already present and are not empty
        if (existingProposal) {
            const { client_id, receipt_no } = existingProposal;
            // Only update if the new values are not empty and different from existing values
            if ((clientId && client_id !== clientId) || (receiptNo && receipt_no !== receiptNo)) {
                await db('proposals_pa')
                    .where('ProposalNumber', proposalData.proposal_number)
                    .update({
                        client_id: clientId,
                        receipt_no: receiptNo
                    });
            }
        }
        // console.log('Formatted Response:', formattedResponse);

        // Update database with success information
        try {
            await db('proposals_pa')
                .where('ProposalNumber', proposalData.proposal_number)
                .update({
                    status: 'SUCCESS',
                    policy_number: formattedResponse.policy.policyNo,
                    win_no: formattedResponse.application.winNo,
                    application_no: formattedResponse.application.applicationNo,
                    // Only update client_id and receipt_no if they are not already present
                    client_id: existingProposal?.client_id || formattedResponse.client.clientId,
                    receipt_no: existingProposal?.receipt_no || formattedResponse.receipt.receiptNo,
                    policy_issue_date: new Date().toISOString()
                });
        } catch (dbError) {
            console.error("Database update error:", dbError);
        }

        return formattedResponse;

    } catch (error) {
        // If it's our custom error response, throw it directly
        if (error.success === false) {
            throw error;
        }

        // Handle unexpected errors
        const errorResponse = {
            success: false,
            error: {
                message: error.message,
                status: error.response?.status || "No status",
                data: error.response?.data || "No data",
                timestamp: new Date().toISOString(),
                type: error.name || "Error"
            }
        };

        await db('policy_logs').insert({
            quotation_number: proposalData.quotation_number,
            policy_type: 'PA',
            request_payload: requestBody, // Original XML request
            response_payload: error.response?.data || error.message,
            status: 'ERROR',
            error_message: error.message,
            created_at: db.fn.now()
        });

        // Update database based on error type
        try {
            let status = 'FAILED';
            if (error.status === 'CKYC Pending') {
                status = 'CKYC Pending';
            } else if (error.error?.type === 'DuplicatePolicy') {
                status = 'DUPLICATE';
            }

            await db('proposals_pa')
                .where('ProposalNumber', proposalData.proposal_number)
                .update({ status: status });
        } catch (dbError) {
            console.error("Database update error:", dbError);
        }

        console.error("SOAP Request Error Details:", errorResponse);
        throw errorResponse;
    }
};

module.exports = { sendSOAPRequest };
