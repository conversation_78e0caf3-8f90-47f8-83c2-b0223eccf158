import { createBranchAndAgencyCode } from "../../actions/action"; 
import { createSlice } from '@reduxjs/toolkit';

const branchSlice = createSlice({
    name: 'branch',
    initialState: {
      branchData: null,
      loading: false,
      error: null,
      success: null
    },
    reducers: {
      clearState: (state) => {
        state.error = null;
        state.success = null;
      }
    },
    extraReducers: (builder) => {
      builder
        .addCase(createBranchAndAgencyCode.pending, (state) => {
          state.loading = true;
        })
        .addCase(createBranchAndAgencyCode.fulfilled, (state, action) => {
          state.loading = false;
          state.success = action.payload.message;
        })
        .addCase(createBranchAndAgencyCode.rejected, (state, action) => {
          state.loading = false;
          state.error = action.payload.error;
        });
    }
  });
  
  export const { clearState } = branchSlice.actions;
  
  export default branchSlice.reducer;

