import { createSlice } from '@reduxjs/toolkit';

const initialSavedRows = JSON.parse(localStorage.getItem('savedRows')) || [];

const savedRowsSlice = createSlice({
  name: 'savedRows',
  initialState: {
    savedRows: initialSavedRows, // Load from local storage
    isAddButtonDisabled: false,
  },
  reducers: {
    markRowAsSaved: (state, action) => {
      const rowId = action.payload;
      if (!state.savedRows.includes(rowId)) {
        state.savedRows.push(rowId);
        localStorage.setItem('savedRows', JSON.stringify(state.savedRows)); // Save to local storage
      }
    },
    setAddButtonDisabled: (state, action) => {
      state.isAddButtonDisabled = action.payload; 
    },
    clearSavedRows: (state) => {
      state.savedRows = [];
      localStorage.removeItem('savedRows'); // Clear from local storage
    },
  },
});

export const { markRowAsSaved, setAddButtonDisabled, clearSavedRows } = savedRowsSlice.actions;

export default savedRowsSlice.reducer;
