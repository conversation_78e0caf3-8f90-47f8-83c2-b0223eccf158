import React, { useEffect, useState } from 'react';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import Dropdown from '../../../components/table/DropDown';
import CheckboxWithLabel from '../../../components/CheckboxWithLabel';
import CustomSection from '../../../components/CustomSection';
import DeleteIcon from '@mui/icons-material/Delete';
import { IconButton } from '@mui/material';
import { Typography } from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { createEmployeeAddress, getEmployeeAddressById, fetchEmployeeById, getAllLocations, getLocationByPincode, getAreasByPincode_City, getLocationDetailsByPincode, getLocationsWithSubAreas, getSubAreasByPincode, updatemployeeAddress, deleteAgentAddress, getLocationByPincodeAndCity, getAreasByPincodeAndCity, getEmployeeAddressByEmployeeId } from '../../../redux/actions/action';
// import { createEmployeeAddress, getAllEmployeeAddresses, getAllLocations, getAreasByPincodeAndCity, getCustomerById, getEmployeeAddressByEmployeeId, getEmployeeAddressById, getLocationByPincode, getLocationDetailsByPincode, updatemployeeAddress, updatemployeeAddress } from '../../../redux/actions/action';
import DeletePopup from '../../../components/DeletePopup';
import { clearCurrentAddress } from '../../../redux/slices/User/employeeAddressSlice';
import { toast } from 'react-toastify';

const EmployeeAddress = () => {
    const { id, view } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();


    const [formData, setFormData] = useState({
        employee_id: '',
        current_apartment_no: '',
        current_apartment_name: '',
        current_address_line1: '',
        current_address_line2: '',
        current_pincode: '',
        current_area: '',
        current_city: '',
        current_state: '',
        permanent_apartment_no: '',
        permanent_apartment_name: '',
        permanent_address_line1: '',
        permanent_address_line2: '',
        permanent_pincode: '',
        permanent_area: '',
        permanent_city: '',
        permanent_state: '',
        used_address: '',
        use_current_as_permanent: '',
        status: true
    });

    const [formErrors, setFormErrors] = useState({
        employee_id: '',
        current_apartment_no: '',
        current_apartment_name: '',
        current_address_line1: '',
        current_address_line2: '',
        current_pincode: '',
        current_area: '',
        current_city: '',
        current_state: '',
        permanent_apartment_no: '',
        permanent_apartment_name: '',
        permanent_address_line1: '',
        permanent_address_line2: '',
        permanent_pincode: '',
        permanent_area: '',
        permanent_city: '',
        permanent_state: '',
        used_address: '',
        use_current_as_permanent: '',
        status: true
    });

    const [isSameAddress, setIsSameAddress] = useState(false);
    const [currentSubArea, setCurrentSubArea] = useState([]);
    const [permanentSubArea, setPermanentSubArea] = useState([]);
    const employee = useSelector((state) => state.employeeInfoReducer.employeeDetail);
    const [employeeAddress, setEmployeeAddress] = useState(null);
    const locations = useSelector(state => state.areaManagementReducer.locations);
    const subAreas = useSelector(state => state.areaManagementReducer.subAreas);
    const location = useSelector(state => state.areaManagementReducer.location);

    const [isDeletePopupOpen, setIsDeletePopupOpen] = useState(false);
    const [addressToDelete, setAddressToDelete] = useState('');

    const [useCurrentAsPermanent, setUseCurrentAsPermanent] = useState(false);
    const [currentCityOptions, setCurrentCityOptions] = useState([]);
    const [permanentCityOptions, setPermanentCityOptions] = useState([]);
    const [currentAreaOptions, setCurrentAreaOptions] = useState([]);
    const [permanentAreaOptions, setPermanentAreaOptions] = useState([]);

    // Initialize isViewMode
    const isViewMode = view === 'true' || employee?.status === 0;
    const validateForm = () => {
        let errors = {};
        let isValid = true;

        // Validate current address fields
        if (!formData.current_apartment_no) {
            errors.current_apartment_no = "Building no is required";
            isValid = false;
        }

        if (!formData.current_address_line1) {
            errors.current_address_line1 = "Address line 1 is required";
            isValid = false;
        }

        if (!formData.current_pincode) {
            errors.current_pincode = "Pincode is required";
            isValid = false;
        } else if (!/^\d{6}$/.test(formData.current_pincode)) {
            errors.current_pincode = "Pincode must be 6 digits";
            isValid = false;
        }



        if (!formData.current_city) {
            errors.current_city = "City is required";
            isValid = false;
        }

        if (!formData.current_state) {
            errors.current_state = "State is required";
            isValid = false;
        }

        // Only validate permanent address if not same as current
        if (!isSameAddress) {
            if (!formData.permanent_apartment_no) {
                errors.permanent_apartment_no = "Building no is required";
                isValid = false;
            }

            if (!formData.permanent_address_line1) {
                errors.permanent_address_line1 = "Address line 1 is required";
                isValid = false;
            }

            if (!formData.permanent_pincode) {
                errors.permanent_pincode = "Pincode is required";
                isValid = false;
            } else if (!/^\d{6}$/.test(formData.permanent_pincode)) {
                errors.permanent_pincode = "Pincode must be 6 digits";
                isValid = false;
            }
            if (!formData.permanent_city) {
                errors.permanent_city = "City is required";
                isValid = false;
            }

            if (!formData.permanent_state) {
                errors.permanent_state = "State is required";
                isValid = false;
            }
        }

        // Validate used_address
        if (!formData.used_address) {
            errors.used_address = "Please select an address to use";
            isValid = false;
        }

        setFormErrors(errors);
        return isValid;
    }

    useEffect(() => {
        if (id) {
            dispatch(fetchEmployeeById(id));
            dispatch(getEmployeeAddressByEmployeeId(id)).then(res => {
                if (res.payload) {
                    setEmployeeAddress(res.payload);
                }
            });
        } else {
            // Clear form data and errors when creating a new employee
            setFormData({
                employee_id: '',
                current_apartment_no: '',
                current_apartment_name: '',
                current_address_line1: '',
                current_address_line2: '',
                current_pincode: '',
                current_area: '',
                current_city: '',
                current_state: '',
                permanent_apartment_no: '',
                permanent_apartment_name: '',
                permanent_address_line1: '',
                permanent_address_line2: '',
                permanent_pincode: '',
                permanent_area: '',
                permanent_city: '',
                permanent_state: '',
                used_address: '',
                use_current_as_permanent: '',
                status: true
            });
            setFormErrors({
                employee_id: '',
                current_apartment_no: '',
                current_apartment_name: '',
                current_address_line1: '',
                current_address_line2: '',
                current_pincode: '',
                current_area: '',
                current_city: '',
                current_state: '',
                permanent_apartment_no: '',
                permanent_apartment_name: '',
                permanent_address_line1: '',
                permanent_address_line2: '',
                permanent_pincode: '',
                permanent_area: '',
                permanent_city: '',
                permanent_state: '',
                used_address: '',
                use_current_as_permanent: '',
                status: true
            });
            setEmployeeAddress(null);
        }
    }, [id, dispatch]);

    useEffect(() => {
        if (id && employeeAddress) {
            setFormData(prev => ({
                ...prev,
                employee_id: id || '',
                current_apartment_no: employeeAddress.current_apartment_no || '',
                current_apartment_name: employeeAddress.current_apartment_name || '',
                current_address_line1: employeeAddress.current_address_line1 || '',
                current_address_line2: employeeAddress.current_address_line2 || '',
                current_pincode: employeeAddress.current_pincode || '',
                current_area: employeeAddress.current_area || '',
                current_city: employeeAddress.current_city || '',
                current_state: employeeAddress.current_state || '',
                permanent_apartment_no: employeeAddress.permanent_apartment_no || '',
                permanent_apartment_name: employeeAddress.permanent_apartment_name || '',
                permanent_address_line1: employeeAddress.permanent_address_line1 || '',
                permanent_address_line2: employeeAddress.permanent_address_line2 || '',
                permanent_pincode: employeeAddress.permanent_pincode || '',
                permanent_area: employeeAddress.permanent_area || '',
                permanent_city: employeeAddress.permanent_city || '',
                permanent_state: employeeAddress.permanent_state || '',
                used_address: employeeAddress.used_address || '',
                use_current_as_permanent: employeeAddress.use_current_as_permanent || '',
                status: employeeAddress.status
            }));
            setIsSameAddress(employeeAddress.use_current_as_permanent === 'Yes');
            // Fetch sub areas for both current and permanent addresses
            if (employeeAddress.current_pincode) {
                dispatch(getLocationDetailsByPincode(employeeAddress.current_pincode)).then((action) => {
                    if (action.payload) {
                        setCurrentSubArea(action.payload.sub_areas);
                    }
                });
            }

            if (employeeAddress.permanent_pincode) {
                dispatch(getLocationDetailsByPincode(employeeAddress.permanent_pincode)).then((action) => {
                    if (action.payload) {
                        setPermanentSubArea(action.payload.sub_areas);
                    }
                });
            }
        }
    }, [id, employeeAddress, dispatch])

    useEffect(() => {
    }, [])

    useEffect(() => {
        if (
            formData.permanent_apartment_no !== formData.current_apartment_no ||
            formData.permanent_apartment_name !== formData.current_apartment_name ||
            formData.permanent_address_line1 !== formData.current_address_line1 ||
            formData.permanent_address_line2 !== formData.current_address_line2 ||
            formData.permanent_pincode !== formData.current_pincode
            // formData.permanent_sub_area_id !== formData.current_sub_area_id
        ) {
            setIsSameAddress(false);
        }
    }, [formData])

    useEffect(() => {
        if (String(formData.current_pincode).length === 6) {
            dispatch(getLocationByPincode(formData.current_pincode)).then((action) => {
                if (action.payload.length > 0) {
                    setFormData(prev => ({
                        ...prev,
                        current_state: action.payload[0].state,
                    }));
                    setCurrentCityOptions(() => {
                        return action.payload.map(city => ({
                            label: city.city,
                            value: city.id
                        }))
                    })
                } else {
                    setFormErrors(prev => ({
                        ...prev,
                        current_pincode: 'Pincode not found'
                    }));
                }
            });
        }
        if (String(formData.permanent_pincode).length === 6) {
            dispatch(getLocationByPincode(formData.permanent_pincode)).then((action) => {
                if (action.payload.length > 0) {
                    setFormData(prev => ({
                        ...prev,
                        permanent_state: action.payload[0].state,
                    }));
                    setPermanentCityOptions(() => {
                        return action.payload.map(city => ({
                            label: city.city,
                            value: city.id
                        }))
                    })
                } else {
                    setFormErrors(prev => ({
                        ...prev,
                        permanent_pincode: 'Pincode not found'
                    }));
                }
            });
        }
    }, [formData.current_pincode, formData.permanent_pincode])

    useEffect(() => {
        if (formData.current_city) {
            const city = currentCityOptions.find(city => city.value === Number(formData.current_city));
            dispatch(getAreasByPincode_City(formData.current_pincode + city?.label?.replace(/\s+/g, ''))).then((action) => {
                if (action.payload) {
                    setCurrentAreaOptions(() => {
                        return action.payload.map(area => ({
                            label: area.area,
                            value: area.id
                        }))
                    })
                }
            });
        }

        if (formData.permanent_city) {
            const city = permanentCityOptions.find(city => city.value === Number(formData.permanent_city));
            dispatch(getAreasByPincode_City(formData.permanent_pincode + city?.label?.replace(/\s+/g, ''))).then((action) => {
                if (action.payload) {
                    setPermanentAreaOptions(() => {
                        return action.payload.map(area => ({
                            label: area.area,
                            value: area.id
                        }))
                    })
                }
            });
        }
    }, [formData.current_city, formData.permanent_city, currentCityOptions, permanentCityOptions])

    const toCamelCase = (str) => {
        return str.split(' ').map(word => {
            return word.charAt(0).toUpperCase() + word.slice(1);
        }).join(' ');
    }

    const copyData = () => {
        setFormData(prev => ({
            ...prev,
            permanent_apartment_no: prev.current_apartment_no,
            permanent_apartment_name: prev.current_apartment_name,
            permanent_address_line1: prev.current_address_line1,
            permanent_address_line2: prev.current_address_line2,
            permanent_pincode: prev.current_pincode,
            permanent_area: prev.current_area,
            permanent_city: prev.current_city,
            permanent_state: prev.current_state
        }));
        setFormErrors(prev => ({
            ...prev,
            permanent_apartment_no: '',
            permanent_apartment_name: '',
            permanent_address_line1: '',
            permanent_address_line2: '',
            permanent_pincode: '',
            permanent_area: '',
            permanent_city: '',
            permanent_state: ''
        }));
        setPermanentAreaOptions(currentAreaOptions);
        setPermanentCityOptions(currentCityOptions);
    }

    const handleSame = () => {
        if (!isSameAddress) {
            setIsSameAddress(true);
            copyData();
            setFormData(prev => ({
                ...prev,
                use_current_as_permanent: 'yes' // Save 'yes' when checked
            }));
        } else {
            setIsSameAddress(false);
            setFormData(prev => ({
                ...prev,
                use_current_as_permanent: 'no' // Save 'no' when unchecked
            }));
        }
    }

    const handleChange = (e) => {
        if (isViewMode) return;
        const { name, value } = e.target;

        if (name === 'current_pincode' || name === 'permanent_pincode') {
            // Only allow numbers
            if (!/^\d*$/.test(value)) {
                setFormErrors(prev => ({
                    ...prev,
                    [name]: 'Pincode can only contain numbers'
                }));
                return;
            }

            // Limit to 6 digits
            if (value.length > 6) return;

            // Clear related fields when pincode changes
            if (name === 'current_pincode') {
                setFormData(prev => ({
                    ...prev,
                    [name]: value,
                    current_area: '',
                    current_city: '',
                    current_state: ''
                }));
                setFormErrors(prev => ({
                    ...prev,
                    [name]: '',
                    current_area: '',
                    current_city: '',
                    current_state: ''
                }));
                setCurrentAreaOptions([]);
                setCurrentCityOptions([]);
            } else {
                setFormData(prev => ({
                    ...prev,
                    [name]: value,
                    permanent_area: '',
                    permanent_city: '',
                    permanent_state: ''
                }));
                setFormErrors(prev => ({
                    ...prev,
                    [name]: '',
                    permanent_area: '',
                    permanent_city: '',
                    permanent_state: ''
                }));
                setPermanentAreaOptions([]);
                setPermanentCityOptions([]);
            }
            return;
        }

        // Special handling for area selection
        if (name === 'current_area' || name === 'permanent_area') {
            setFormData(prevFormData => ({
                ...prevFormData,
                [name]: value,
            }));
            setFormErrors(prev => ({
                ...prev,
                [name]: ''
            }));
            return;
        }

        if (name === 'current_city' || name === 'permanent_city') {
            setFormData(prevFormData => ({
                ...prevFormData,
                [name]: value
            }));
            setFormErrors(prev => ({
                ...prev,
                [name]: ''
            }));
            return;
        }

        const data = typeof value === 'number' ? value : (name === 'permanent_address_line1' || name === 'permanent_address_line2' || name === 'current_address_line1' || name === 'current_address_line2') ? toCamelCase(value) : value.toUpperCase();
        setFormData(prevFormData => ({
            ...prevFormData,
            [name]: data
        }));
        setFormErrors(prev => ({
            ...prev,
            [name]: ''
        }));
    }

    const handleUsedAddressChange = (addressType) => {
        setFormData(prev => ({
            ...prev,
            used_address: addressType
        }));
    };

    const handleSaveAndUpdate = async () => {
        if (!validateForm()) {
            return;
        }

        const formDataToSend = {
            ...formData,
            employee_id: id,
            current_area: formData.current_area || null,
            permanent_area: formData.permanent_area || null,
            use_current_as_permanent: formData.use_current_as_permanent === 'yes' ? 'Yes' : 'No'
        };
        delete formDataToSend.same_as_current;

        try {
            let response;
            if (!employeeAddress) {
                response = await dispatch(createEmployeeAddress(formDataToSend)).unwrap();
            } else {
                response = await dispatch(updatemployeeAddress({
                    id: employeeAddress.id,
                    addressData: formDataToSend
                })).unwrap();
            }

            // Get the employee ID from the response or use the existing ID
            const employeeId = response?.employee_id || id;
            return employeeId;
        } catch (error) {
            console.error('Error saving address:', error);
            throw error;
        }
    };

    const handleSave = async () => {
        try {
            const employeeId = await handleSaveAndUpdate();
            if (employeeId) {
                navigate(`/dashboard/employee-master-overview/${employeeId}`);
            } else {
                toast.error("Could not get employee ID");
            }
        } catch (error) {
            toast.error("Failed to save employee address");
        }
    };

    const handleSaveAndNew = async () => {
        try {
            const employeeId = await handleSaveAndUpdate();
            if (employeeId) {
                navigate('/dashboard/employee-personal-information');
            } else {
                toast.error("Could not get employee ID");
            }
        } catch (error) {
            toast.error("Failed to save employee address");
        }
    };

    const handleCancel = () => {
        dispatch(clearCurrentAddress())
        navigate('/dashboard/employee-Master');
    };

    const handleDeleteClick = (addressType) => {
        setAddressToDelete(addressType);
        setIsDeletePopupOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (addressToDelete === 'current') {
            setFormData(prev => ({
                ...prev,
                current_apartment_no: '',
                current_apartment_name: '',
                current_address_line1: '',
                current_address_line2: '',
                current_pincode: '',
                current_area: '',
                current_city: '',
                current_state: '',
                used_address: prev.used_address === 'current' ? '' : prev.used_address
            }));
        } else if (addressToDelete === 'permanent') {
            setFormData(prev => ({
                ...prev,
                permanent_apartment_no: '',
                permanent_apartment_name: '',
                permanent_address_line1: '',
                permanent_address_line2: '',
                permanent_pincode: '',
                permanent_area: '',
                permanent_city: '',
                permanent_state: '',
            }));
        }

        setIsDeletePopupOpen(false);
    };

    const handleDeleteCancel = () => {
        setIsDeletePopupOpen(false);
    };

    return (
        <Box sx={{ paddingLeft: '40px', paddingRight: '40px', paddingBottom: '40px' }}>
            <form>
                <Grid container style={{ display: 'flex' }}>
                    {/* Header Row */}
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Employee" pageName={id ? (isViewMode ? "View" : "Edit") : "Create"} />
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        {!isViewMode && (
                            <>
                                <Button onClick={handleSaveAndNew} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}>
                                    Save & New
                                </Button>
                                <Button onClick={handleSave} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}>
                                    Save
                                </Button>
                            </>
                        )}
                        <Button onClick={handleCancel} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }} >
                            Cancel
                        </Button>
                    </Grid>
                    <Grid container spacing={2}>
                        <CustomSection titles={['Overview', 'Personal Details', 'Address']} page='employee' />
                    </Grid>
                </Grid>
                {/* Current Address */}
                <Grid sx={{ display: 'flex', width: '100%', }}>
                    <Box
                        sx={{
                            width: '100%',
                            backgroundColor: '#f0f0f0',
                            display: "flex",
                            alignItems: "center",
                            padding: "10px",
                            borderRadius: "4px",
                            height: "60px",
                            fontSize: "18px",
                            fontStyle: "normal",
                            fontWeight: "700",
                            lineHeight: "27px",
                            color: '#4C5157',
                            borderRadius: '.3rem'
                        }}
                    >
                        <h5>Current Address</h5>
                        <div
                            style={{
                                height: "100vh",
                                display: "flex",
                                alignItems: "center",
                            }}
                        ></div>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Building no / Apartment No 1"
                            name="current_apartment_no"
                            value={formData.current_apartment_no}
                            onChange={handleChange}
                            error={!!formErrors.current_apartment_no}
                            helperText={formErrors.current_apartment_no}
                            isRequired
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Building Name / Apartment Name 2"
                            name="current_apartment_name"
                            value={formData.current_apartment_name}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Address Line 1"
                            name="current_address_line1"
                            isRequired
                            value={formData.current_address_line1}
                            error={!!formErrors.current_address_line1}
                            helperText={formErrors.current_address_line1}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Address Line 2"
                            name="current_address_line2"
                            value={formData.current_address_line2}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Pincode"
                            name="current_pincode"
                            isRequired
                            value={formData.current_pincode}
                            error={!!formErrors.current_pincode}
                            helperText={formErrors.current_pincode}
                            disabled={isViewMode}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <Dropdown
                            label="City"
                            name="current_city"
                            options={currentCityOptions}
                            value={formData.current_city}
                            onChange={handleChange}
                            fullWidth
                            required
                            disabled={isViewMode}
                            error={!!formErrors.current_city}
                            helperText={formErrors.current_city}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Area"
                            name="current_area"
                            options={currentAreaOptions}
                            value={formData.current_area}
                            onChange={handleChange}
                            fullWidth
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='current_state'
                            label='State'
                            fullWidth
                            isRequired
                            disabled
                            value={formData.current_state}
                            error={!!formErrors.current_state}
                            helperText={formErrors.current_state}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ display: 'flex', gap: '1rem' }}>
                        {!isViewMode && (
                            <>
                                <CheckboxWithLabel
                                    label="Use This"
                                    disabled={false}
                                    checked={formData.used_address === 'current'}
                                    onChange={() => handleUsedAddressChange('current')}
                                />
                                <Box display="flex" flexDirection="column" alignItems="center" justifyContent='space-between'>
                                    <IconButton aria-label="delete" onClick={() => handleDeleteClick('current')}>
                                        <DeleteIcon sx={{ color: 'red' }} />
                                    </IconButton>
                                    <Typography variant="body2" sx={{ color: 'gray' }} >Delete</Typography>
                                </Box>
                                <CheckboxWithLabel
                                    label="Is permanent address Same as Current Address"
                                    disabled={false}
                                    direction='row'
                                    onChange={handleSame}
                                    checked={isSameAddress}
                                />
                            </>
                        )}
                    </Grid>
                </Grid>

                {/* Permanent Address */}
                <Grid sx={{ display: 'flex', width: '100%', }}>
                    <Box
                        sx={{
                            width: '100%',
                            backgroundColor: '#f0f0f0',
                            display: "flex",
                            alignItems: "center",
                            padding: "10px",
                            borderRadius: "4px",
                            height: "60px",
                            fontSize: "18px",
                            fontStyle: "normal",
                            fontWeight: "700",
                            lineHeight: "27px",
                            color: '#4C5157',
                            borderRadius: '.3rem'
                        }}
                    >
                        <h5>Permanent Address</h5>
                        <div
                            style={{
                                height: "100vh",
                                display: "flex",
                                alignItems: "center",
                            }}
                        ></div>
                    </Box>
                </Grid >
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Building no / Apartment No 1"
                            name="permanent_apartment_no"
                            isRequired
                            value={formData.permanent_apartment_no}
                            error={!!formErrors.permanent_apartment_no}
                            helperText={formErrors.permanent_apartment_no}
                            disabled={isViewMode || isSameAddress}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Building Name / Apartment Name 2"
                            name="permanent_apartment_name"
                            value={formData.permanent_apartment_name}
                            disabled={isViewMode || isSameAddress}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Address Line 1"
                            name="permanent_address_line1"
                            isRequired
                            value={formData.permanent_address_line1}
                            error={!!formErrors.permanent_address_line1}
                            helperText={formErrors.permanent_address_line1}
                            disabled={isViewMode || isSameAddress}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Address Line 2"
                            name="permanent_address_line2"
                            value={formData.permanent_address_line2}
                            disabled={isViewMode || isSameAddress}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            label="Pincode"
                            name="permanent_pincode"
                            isRequired
                            value={formData.permanent_pincode}
                            error={!!formErrors.permanent_pincode}
                            helperText={formErrors.permanent_pincode}
                            disabled={isViewMode || isSameAddress}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <Dropdown
                            label="City"
                            name="permanent_city"
                            options={permanentCityOptions}
                            value={formData.permanent_city}
                            onChange={handleChange}
                            fullWidth
                            required
                            disabled={isViewMode || isSameAddress}
                            error={!!formErrors.permanent_city}
                            helperText={formErrors.permanent_city}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Area"
                            name="permanent_area"
                            options={permanentAreaOptions}
                            value={formData.permanent_area}
                            onChange={handleChange}
                            fullWidth
                            disabled={isViewMode || isSameAddress}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            onChange={handleChange}
                            name='permanent_state'
                            label='State'
                            fullWidth
                            isRequired
                            disabled
                            value={formData.permanent_state}
                            error={!!formErrors.permanent_state}
                            helperText={formErrors.permanent_state}
                        />
                    </Grid>
                    <Grid item xs={2} sx={{ display: 'flex', gap: '1rem' }}>
                        {!isViewMode && (
                            <>
                                <CheckboxWithLabel
                                    label="Use This"
                                    disabled={false}
                                    checked={formData.used_address === 'permanent'}
                                    onChange={() => handleUsedAddressChange('permanent')}
                                />
                                <Box display="flex" flexDirection="column" alignItems="center" justifyContent='space-between'>
                                    <IconButton aria-label="delete" onClick={() => handleDeleteClick('permanent')}>
                                        <DeleteIcon sx={{ color: 'red' }} />
                                    </IconButton>
                                    <Typography variant="body2" sx={{ color: 'gray' }} >Delete</Typography>
                                </Box>
                            </>
                        )}
                    </Grid>
                    {formErrors.used_address && (
                        <Grid item xs={12}>
                            <Typography color="error" variant="caption">
                                {formErrors.used_address}
                            </Typography>
                        </Grid>
                    )}
                </Grid>
            </form >

            <DeletePopup
                open={isDeletePopupOpen}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                modulename={addressToDelete}
                message={`Are you sure you want to delete the ${addressToDelete} address?`}
            />
        </Box >
    );
};

export default EmployeeAddress;