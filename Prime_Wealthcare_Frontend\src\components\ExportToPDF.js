import React from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Button } from '@mui/material';
import { useSelector } from 'react-redux';

const ExportToPDF = ({ data, headNames, selectedRows, fileName, dataMapping, headerTitle, isLastColumnWide = false , disabled = false}) => {
    const user = useSelector((state) => state.auth.user);
    const username = user.userName || user.full_name || 'Guest';

    const handleExport = () => {
        const doc = new jsPDF({ orientation: 'landscape' });

        const selectedData = data.filter(item => selectedRows.includes(item.id));

        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const imageWidth = pageWidth * 0.3;
        const image = new Image();
        image.src = '/logo.png'; 

        const watermark = new Image();
        watermark.src = '/gray-logo.png';

        const padding = 3;

        image.onload = () => {
            const aspectRatio = image.width / image.height;
            const imageHeight = imageWidth / aspectRatio;

            doc.addImage('/logo.png', 'PNG', padding, padding, imageWidth, imageHeight);

            const currentDate = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = currentDate.toLocaleDateString('en-US', options);
            doc.setFontSize(12);
            const rightPadding = 10;
            const dateXPosition = pageWidth - padding - doc.getTextWidth(formattedDate) - rightPadding;
            const dateYPosition = padding + 20;
            doc.text(formattedDate, dateXPosition, dateYPosition);

            const header = headerTitle || (fileName.charAt(0).toUpperCase() + fileName.slice(1).replace('.pdf', '') + ' Report');
            doc.setFontSize(18);
            const headerY = imageHeight + padding + 10;

            doc.setFillColor(82, 138, 126);
            doc.rect(0, headerY - 8, pageWidth, 10, 'F');

            doc.setTextColor(255, 255, 255);
            doc.text(header, pageWidth / 2, headerY, { align: 'center' });

            watermark.onload = () => {
                addWatermark(doc, pageWidth, pageHeight, watermark);

                const tableData = selectedData.map(item => {
                    return headNames.map(header => {
                        const mappedValue = item[dataMapping[header]];
                        return mappedValue !== undefined && mappedValue !== null && mappedValue !== '' ? mappedValue : 'N/A';
                    });
                });

                const columnStyles = {};
                if (isLastColumnWide) {
                    columnStyles[headNames.length - 2] = { cellWidth: pageWidth * 0.3 };
                }

                autoTable(doc, {
                    head: [headNames],
                    body: tableData,
                    startY: imageHeight + padding + 20,
                    headStyles: {
                        fillColor: [82, 138, 126]
                    },
                    styles: {
                        lineWidth: 0.5,
                        lineColor: [200, 200, 200],
                        halign: 'center'
                    },
                    columnStyles: columnStyles,
                    didDrawPage: (data) => {
                        addWatermark(doc, pageWidth, pageHeight, watermark);

                        doc.setTextColor(0, 0, 0);
                        doc.setDrawColor(200, 200, 200);
                        doc.setLineWidth(0.5);
                        doc.line(10, pageHeight - 15, pageWidth - 10, pageHeight - 15);
                        doc.setFontSize(10); // Set a smaller font size for the user ID
                        const userId = `User ID: ${username}`;
                        doc.text(userId, 10, pageHeight - 10);

                        doc.text(
                            `Page ${data.pageNumber} of ${doc.internal.getNumberOfPages()}`,
                            pageWidth - 20,
                            pageHeight - 10,
                            { align: 'right' }
                        );
                    }
                });

                doc.save(fileName || 'document.pdf');
            };
        };
    };

    const addWatermark = (doc, pageWidth, pageHeight, watermark) => {
        const watermarkWidth = pageWidth * 0.3;
        const watermarkHeight = watermarkWidth / (watermark.width / watermark.height);
        const watermarkX = (pageWidth - watermarkWidth) / 2;
        const watermarkY = (pageHeight - watermarkHeight) / 2;

        if (watermarkWidth > 0 && watermarkHeight > 0) {
            doc.setGState(new doc.GState({ opacity: 0.1 }));
            doc.addImage(watermark, 'PNG', watermarkX, watermarkY, watermarkWidth, watermarkHeight, '', 'FAST');
            doc.setGState(new doc.GState({ opacity: 1 }));
        } else {
            console.error('Invalid watermark dimensions');
        }
    };

    return (
        <Button 
            onClick={handleExport} 
            sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
            disabled={disabled || !selectedRows || selectedRows.length === 0} // Disable button if no selected data
        >
            Export to PDF
        </Button>
    );
};

export default ExportToPDF;
