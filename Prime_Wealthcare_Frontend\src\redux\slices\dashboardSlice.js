import { createSlice } from '@reduxjs/toolkit';
import { fetchProposalsByCompany, fetchProposalsByAgent } from '../actions/action';

const initialState = {
  proposalData: null,
  agentData: null,
  loading: {
    proposals: false,
    agents: false
  },
  error: {
    proposals: null,
    agents: null
  }
};

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Insurance Company Proposals
      .addCase(fetchProposalsByCompany.pending, (state) => {
        state.loading.proposals = true;
        state.error.proposals = null;
      })
      .addCase(fetchProposalsByCompany.fulfilled, (state, action) => {
        state.loading.proposals = false;
        state.proposalData = action.payload.data;
      })
      .addCase(fetchProposalsByCompany.rejected, (state, action) => {
        state.loading.proposals = false;
        state.error.proposals = action.payload || { message: 'Failed to fetch proposal data' };
      })

      // Agent Performance
      .addCase(fetchProposalsByAgent.pending, (state) => {
        state.loading.agents = true;
        state.error.agents = null;
      })
      .addCase(fetchProposalsByAgent.fulfilled, (state, action) => {
        state.loading.agents = false;
        state.agentData = action.payload.data;
      })
      .addCase(fetchProposalsByAgent.rejected, (state, action) => {
        state.loading.agents = false;
        state.error.agents = action.payload || { message: 'Failed to fetch agent data' };
      });
  }
});

export default dashboardSlice.reducer;