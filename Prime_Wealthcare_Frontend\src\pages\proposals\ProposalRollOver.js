import React, { useState, useEffect } from "react";
import BarLoader from '../../components/BarLoader';
import { Box, Button, Divider, FormControl, FormControlLabel, Grid, IconButton, Radio, RadioGroup, Typography } from "@mui/material";
import ModuleName from "../../components/table/ModuleName";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import { Cancel, Save } from "@mui/icons-material";
import AutocompleteDropdown from "../../components/table/AutocompleteDropdown";
import CustomTextField from "../../components/CustomTextField";
import Dropdown from "../../components/table/DropDown";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CustomFileUpload from "../../components/CustomFileUpload";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { clearAllPaymentDetails } from "../../redux/slices/payment/paymentSlice";
import { createRolloverMigration, fetchAllBanks, fetchAllImfBranches, fetchImfAgencyCodes, fetchInsuranceCompanies, fetchInsuranceCompanyBranchByInsuranceCompanyId, fetchInsuranceCompanyBranches, getAllAgentDetails, getAllCustomer, getAllMasterProducts, getAllPickLists, getAllProducts, getAllSubProducts, getCustomerById, getMemberByCustomerId, getNomineeRelations, updateRolloverMigration } from "../../redux/actions/action";
import { toast } from 'react-toastify';
import { getProposalDetailsByPolicyNumber, updateRolloverMigrations } from '../../redux/actions/action'; // Adjust path as needed
import dayjs from "dayjs";
import DeleteIcon from "@mui/icons-material/Delete";
import { formatDate, generateProposalNumber, generateChecksum, formatToTwoDecimals, calculateAge } from "../../utils/Reusable";
import QuestionToggle from "../../components/QuestionToggle";
import { getMasterProductByMainProductAndInsuranceCompany, getSubProductByProductDetails, getRolloverMigrationById } from "../../redux/actions/action";
import MemberSelectionPopup from "../../components/MemberSelectionPopup";
import { Autocomplete, TextField } from '@mui/material';


function ProposalRollOver() {

    const minAgeDate = dayjs().subtract(18, 'year');
    const { id } = useParams();
    const [loading, setLoading] = useState(false);
    const [proposalType, setProposalType] = useState('Roll Over');
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [readyForProposal, setReadyForProposal] = useState(false);
    const [selectedPolicy, setSelectedPolicy] = useState(null);
    const [isSearching, setIsSearching] = useState(false);
    const [members, setMembers] = useState([]);
    const [areCustomerMembersFetched, setAreCustomerMembersFetched] = useState(false);
    const [relations, setRelations] = useState();
    const isEditMode = !!id;

    const [memberSelectionData, setMemberSelectionData] = useState({ customer: null, membersData: [] });
    const [onlySeniorCitizen, setOnlySeniorCitizen] = useState(false);
    const [onlySeniorCitizenAsMember, setOnlySeniorCitizenAsMember] = useState(false);
    const [reloadNeeded, setReloadNeeded] = useState(false);
    const [salutations, setSalutations] = useState([]);
    const user = useSelector((state) => state.auth.user); // For getting current logged in user details
    const fetchedProposal = useSelector(state => state.proposalReducer.proposal); // For getting proposal details for a single proposal
    const username = user?.userName || user?.full_name;

    const [questions, setQuestions] = useState([]);
    const [answers, setAnswers] = useState([]);
    const [memberAnswers, setMemberAnswers] = useState("");
    const [allowManualEntry, setAllowManualEntry] = useState(false);
    const [memberErrors, setMemberErrors] = useState([]);
    const [banks, setBanks] = useState([]);
    const [formErrors, setFormErrors] = useState({});

    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer?.insuranceCompanies || []); // For insurance company dropdown
    const mainProducts = useSelector(state => state.mainProductReducer.products); // For product type dropdown
    const masterProducts = useSelector(state => state.productMasterReducer.products); // For product name dropdown
    const subProducts = useSelector(state => state.subProductReducer.subProducts); // For sub product name dropdown
    const insuranceBranches = useSelector(state => state.insuranceBranchReducer.branches); // For insurance company branch dropdown
    const agents = useSelector(state => state.agentReducer.agents); // For agent dropdown
    const customer = useSelector(state => state.customerReducer.customerDetails); // For getting single customer details
    const membersData = useSelector(state => state.customerMemberReducer.customerMember); // For getting customer members for a single customer
    const customerDetails = useSelector(state => state.customerReducer.customer); // For customer dropdown
    // Picklist Data
    const relationOptions = useSelector(state => state.pickListReducer.relationOptions); // For relation dropdown
    const maritalStatusOptions = useSelector(state => state.pickListReducer.maritalStatusOptions); // For marital status dropdown
    const paymentTypes = useSelector(state => state.pickListReducer.paymentTypes); // For payment type dropdown
    const durationOptions = useSelector(state => state.pickListReducer.durationOptions); // For duration dropdown
    const genderOptions = useSelector(state => state.pickListReducer.genderOptions); // For gender dropdown
    const salutationOptions = useSelector(state => state.pickListReducer.salutationOptions); // For salutation dropdown
    const OccupationOptions = useSelector(state => state.pickListReducer.OccupationOptions); // For occupation dropdown
    const employeeBankState = useSelector((state) => state.employeeBank) || {};
    const [isPolicyCompleted, setIsPolicyCompleted] = useState(false);

    const [formData, setFormData] = useState({
        customer_id: '',

        policy_number: '',
        policy_pdf: '',
        new_policy_number: '',
        previous_insurance_company: '',
        previous_insurance_company_branch: '',
        previous_product_type: '',
        previous_product_name: '',
        previous_sub_product_name: '',
        previous_member_type: '',
        insurance_company: '',
        insurance_company_name: '',
        insurance_company_branch: '',
        insurance_branch: '',
        product_type: '',
        product_name: '',
        sub_product_name: '',
        member_type: '',
        agent_code: '',
        agent_name: '',
        branch_name: '',
        imf_code: '',
        bank_branch_name: '',
        salutation: '',
        first_name: '',
        last_name: '',
        date_of_birth: null,
        gender: '',
        relation: '',
        marital_status: '',
        email_id: '',
        occupation: '',
        mobile_number: '',
        mobile: '',
        aadhar_number: '',
        total_premium: '',
        start_date: null,
        end_date: null,
        issue_date: null,
        proposal_number: '',
        previous_proposal_number: '',
        customer_member_id: '',
        payment_type: '',
        cash_amount: '',
        cheque_amount: '',
        cheque_number: '',
        cheque_date: '',
        dd_amount: '',
        bank_branch_name: '',
        dd_number: '',
        dd_date: '',
        online_amount: '',
        transaction_date: '',
        transaction_type: '',
        receipt_number: '',
        received_date: '',
        tenure: '',
        created_by: user?.userId || user?.full_name,
        created_at: '',
        updated_by: '',

    });

    useEffect(() => {
        // Only generate proposal number when allowManualEntry is true (no policy found)
        if (user && !id) {
            const initializeProposal = async () => {
                const number = await generateProposalNumber(user.userId, dispatch);
                setFormData(prevData => ({
                    ...prevData,
                    created_by: username,
                    proposal_number: number
                }));
            }
            initializeProposal();
        }
    }, [user]);

    useEffect(() => {
        const masterProduct = masterProducts.find(product => product.id === formData.product_name);
        if (masterProduct?.product_name.toLowerCase().includes('accident suraksha')) {
            toast.error('Roll Over for Accident Suraksha is not allowed. Please contact the nearest branch for assistance.');
            // toast.info('Navigating to the proposal list page.');
            navigate('/dashboard/proposals');
        }
    }, [formData.product_name])

    useEffect(() => {
        const fetchData = async () => {
            try {

                await Promise.all([
                    dispatch(getAllCustomer()),
                    dispatch(fetchInsuranceCompanies()),
                    dispatch(getAllProducts()),
                    dispatch(fetchImfAgencyCodes()),
                    dispatch(getAllPickLists()),
                    dispatch(fetchAllImfBranches()),
                    dispatch(fetchInsuranceCompanyBranches()),
                    dispatch(getAllAgentDetails()),
                    dispatch(getAllMasterProducts()),
                    dispatch(getAllSubProducts()),
                ]);

                if (id && Number(id) !== 0) {
                    await Promise.all([
                        dispatch(fetchInsuranceCompanyBranches()),
                        dispatch(getAllAgentDetails()),
                        dispatch(getAllMasterProducts()),
                        dispatch(getAllSubProducts()),
                    ]);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                // toast.error('Failed to load initial data');
            } finally {
                setReadyForProposal(true);
            }
        };

        fetchData();
    }, []);

    // Add this useEffect after your existing useEffects
    useEffect(() => {

        if (formData.insurance_company_name && formData.product_type && !formData.product_name) {
            // Fetch master products when insurance company and product type are selected
            dispatch(getMasterProductByMainProductAndInsuranceCompany({
                insuranceCompanyId: formData.insurance_company_name,
                mainProductId: formData.product_type
            }));
        } else if (formData.insurance_company_name && formData.product_type && formData.product_name) {
            // Check if selected product is Varishtha Bima
            if (masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('varishtha bima')) {
                setOnlySeniorCitizen(true);
                setOnlySeniorCitizenAsMember(false);
            }
            // Fetch sub-products when product name is also selected
            dispatch(getSubProductByProductDetails({
                mainProductId: formData.product_type,
                insuranceCompanyId: formData.insurance_company_name,
                productMasterId: formData.product_name
            }));
        }

    }, [formData.insurance_company_name, formData.product_type, formData.product_name]);


    useEffect(() => {
        if (relationOptions) {
            setRelations(relationOptions);
        }
    }, [relationOptions])

    // To fetch the insurance company branches when the insurance company name changes
    useEffect(() => {
        if (formData.insurance_company_name === '') {
            setSalutations([]);
            setFormData(prevData => ({
                ...prevData,
                salutation: ''
            }));
        } else {
            dispatch(getNomineeRelations()).then(response => {
                if (response.payload) {
                    setRelations(response.payload);
                }
            })
            setSalutations(getSalutationOptions(formData.insurance_company_name));
        }
        if (formData.insurance_company_name && (!id || Number(id) === 0)) {
            dispatch(fetchInsuranceCompanyBranchByInsuranceCompanyId(formData.insurance_company_name));
        }
    }, [formData.insurance_company_name])

    // or when members are updated and the selected product is Varishtha Bima
    useEffect(() => {
        // Check if the selected product is Varishtha Bima
        const isVarishthaBima = masterProducts.find(
            product => product.id === formData.product_name
        )?.product_name.toLowerCase().includes('varishtha bima');

        if (isVarishthaBima && members.length > 0) {
            // Check if any member is below 60 years
            const ineligibleMembers = members.filter(member => {
                if (!member.dob) return false;
                const age = dayjs().diff(dayjs(member.dob, 'DD/MM/YYYY'), 'years');
                return age < 60;
            });

            if (ineligibleMembers.length > 0) {
                toast.error('Varishtha Bima is only available for members aged 60 years and above. Please select a different product.');
                // Reset product and sub-product selection
                setFormData(prev => ({
                    ...prev,
                    product_name: '',
                    sub_product_name: ''
                }));
            }
        }
    }, [formData.product_name, members]);


    // Add this function with your other helper functions
    const getSalutationOptions = (insuranceCompany) => {
        try {
            if (insuranceCompany === '') {
                return [];
            }

            // If insurance company is not Future Generali (ID: 1), return default options
            if (Number(insuranceCompany) !== 1) {
                return salutationOptions.map(option => ({
                    label: option.label_name,
                    value: option.id
                }));
            }

            // Get customer's gender
            const customerGender = genderOptions.find(g => g.id === customer?.gender_id)?.label_name?.toLowerCase();

            // Common salutations for both genders
            const bothGenderSalutations = ['ADM', 'ADV', 'BRIG', 'CDR', 'COL', 'DR', 'LT', 'MAJ', 'PROF'];
            // Male-only salutations
            const maleSalutations = ['MAST', 'MR', 'SHRI', 'SIR'];
            // Female-only salutations
            const femaleSalutations = ['MISS', 'MRS', 'MS', 'SMT'];

            // Filter salutations based on insurance company
            let filteredSalutations = salutationOptions.filter(option =>
                option.type_name === 'Future_genrali_Salutation'
            );

            // Further filter based on gender
            if (customerGender === 'male') {
                filteredSalutations = filteredSalutations.filter(option =>
                    bothGenderSalutations.includes(option.api_name) ||
                    maleSalutations.includes(option.api_name)
                );
            } else if (customerGender === 'female') {
                filteredSalutations = filteredSalutations.filter(option =>
                    bothGenderSalutations.includes(option.api_name) ||
                    femaleSalutations.includes(option.api_name)
                );
            }

            // Map the filtered salutations to dropdown format
            return filteredSalutations?.map(option => ({
                label: option.label_name,
                value: option.id
            })) || [];

        } catch (error) {
            console.error('Error in getSalutationOptions:', error);
            return [];
        }
    };
    // To fetch the insurance company branches when the insurance company name changes
    useEffect(() => {
        if (formData.insurance_company_name === '') {
            setSalutations([]);
            setFormData(prevData => ({
                ...prevData,
                salutation: ''
            }));
        } else {
            dispatch(getNomineeRelations()).then(response => {
                if (response.payload) {
                    setRelations(response.payload);
                }
            })
            setSalutations(getSalutationOptions(formData.insurance_company_name));
        }
        if (formData.insurance_company_name && (!id || Number(id) === 0)) {
            dispatch(fetchInsuranceCompanyBranchByInsuranceCompanyId(formData.insurance_company_name));
        }
    }, [formData.insurance_company_name])


    const getFilteredRelations = () => {
        try {

            // Get the current product name
            const currentProduct = masterProducts.find(product => product.id === formData.product_name)?.product_name;

            // Base excluded relations that should never be available
            const baseExcluded = ['self'];
            if (currentProduct?.toLowerCase()?.includes('fg')) {
                baseExcluded.push('child', 'husband', 'wife');
            }
            // Add spouse-related relations if product is FG VARISHTHA BIMA
            if (currentProduct?.toLowerCase()?.includes('fg varishta bima')) {
                baseExcluded.push('spouse', 'husband', 'wife');
            }

            // Combine all exclusions
            const excludedRelations = [...baseExcluded];

            const filteredRelations = relations.filter(relation =>
                !excludedRelations.includes(relation.label_name?.toLowerCase())
            );
            return filteredRelations;
        } catch (error) {
            console.error('Error in getFilteredRelations:', error);
            return relations; // Return all relations as fallback
        }
    };

    const validatePaymentDetails = () => {
        const errors = {};
        const totalPremium = Number(formData.total_premium);
        const selectedPaymentType = paymentTypes.find(type => type.id === Number(formData.payment_type))?.api_name;

        if (!selectedPaymentType) {
            errors.payment_type = 'Please select payment type';
            return errors;
        }

        switch (selectedPaymentType) {
            case 'PAY_CASH':
                if (!formData.cash_amount) {
                    errors.cash_amount = 'Please enter cash amount';
                } else if (Number(formData.cash_amount) !== totalPremium) {
                    errors.cash_amount = `Amount (₹${formData.cash_amount}) must equal total premium (₹${totalPremium})`;
                }
                break;

            case 'PAY_CHEQUE':
                if (!formData.cheque_amount) {
                    errors.cheque_amount = 'Please enter cheque amount';
                } else if (Number(formData.cheque_amount) !== totalPremium) {
                    errors.cheque_amount = `Amount (₹${formData.cheque_amount}) must equal total premium (₹${totalPremium})`;
                }
                break;

            case 'PAY_DD':
                if (!formData.dd_amount) {
                    errors.dd_amount = 'Please enter DD amount';
                } else if (Number(formData.dd_amount) !== totalPremium) {
                    errors.dd_amount = `Amount (₹${formData.dd_amount}) must equal total premium (₹${totalPremium})`;
                }
                break;

            case 'PAY_ONLINE':
                if (!formData.online_amount) {
                    errors.online_amount = 'Please enter online amount';
                } else if (Number(formData.online_amount) !== totalPremium) {
                    errors.online_amount = `Amount (₹${formData.online_amount}) must equal total premium (₹${totalPremium})`;
                }
                break;

            case 'PAY_CHEQUE_CASH':
                const chequeAndCashTotal = Number(formData.cheque_amount || 0) + Number(formData.cash_amount || 0);
                if (chequeAndCashTotal !== totalPremium) {
                    errors.cheque_amount = `Combined amount (₹${chequeAndCashTotal}) must equal total premium (₹${totalPremium})`;
                    errors.cash_amount = `Combined amount (₹${chequeAndCashTotal}) must equal total premium (₹${totalPremium})`;
                }
                break;

            case 'PAY_DD_CASH':
                const ddAndCashTotal = Number(formData.dd_amount || 0) + Number(formData.cash_amount || 0);
                if (ddAndCashTotal !== totalPremium) {
                    errors.dd_amount = `Combined amount (₹${ddAndCashTotal}) must equal total premium (₹${totalPremium})`;
                    errors.cash_amount = `Combined amount (₹${ddAndCashTotal}) must equal total premium (₹${totalPremium})`;
                }
                break;

            default:
                break;
        }

        return errors;
    };
    const validateForm = () => {
        const errors = {};
        const mErrors = [];
        let toastErrors = [];
        let isValid = true;
        // Validate Renewal Details
        // if (!formData.policy_number) {
        //     errors.policy_number = 'Please enter policy number';
        //     toastErrors.push('Policy Number');
        // }

        // Validate insurance company selection for roll over
        if (!allowManualEntry || allowManualEntry) {
            const selectedCompany = insuranceCompanies.find(company => company.id === formData.insurance_company_name);
            if (selectedCompany && formData.previous_insurance_company &&
                selectedCompany.insurance_company_name.toLowerCase() === formData.previous_insurance_company.toLowerCase()) {
                errors.insurance_company_name = 'For policy rollover, the new insurance company must differ from the previous one. Verify migration if retaining the company name.';
                isValid = false;
            }
        }

        // Validate start date if policy number exists
        if (formData.new_policy_number && !formData.start_date) {
            errors.start_date = 'Start Date is mandatory when Policy Number is entered';
            isValid = false;
        }
        if (formData.start_date && !formData.new_policy_number) {
            errors.new_policy_number = 'Policy number is mandatory when start date is entered';
            isValid = false;
        }

        if (allowManualEntry) {
            if (!formData.customer_id) {
                errors.customer_id = 'Please select customer';
                toastErrors.push('Customer');
            }
            if (!formData.previous_insurance_company) {
                errors.previous_insurance_company = 'Please enter previous insurance company';
            }
            if (!formData.previous_product_type) {
                errors.previous_product_type = 'Please enter previous product type';
            }
            if (!formData.previous_product_name) {
                errors.previous_product_name = 'Please enter previous product name';
            }
            if (!formData.previous_sub_product_name) {
                errors.previous_sub_product_name = 'Please enter previous sub product name';
            }
            if (!formData.previous_member_type) {
                errors.previous_member_type = 'Please enter previous member type';
            }

        }

        // Validate Portability Details
        if (!formData.insurance_company_name) {
            errors.insurance_company_name = 'Please select insurance company';
            toastErrors.push('Insurance Company');
        }
        if (!formData.product_type) {
            errors.product_type = 'Please select product type';
            toastErrors.push('Product Type');
        }
        if (!formData.product_name) {
            errors.product_name = 'Please select product name';
            toastErrors.push('Product Name');
        }
        if (!formData.sub_product_name) {
            errors.sub_product_name = 'Please select sub product';
            toastErrors.push('Sub Product');
        }
        if (!formData.member_type) {
            errors.member_type = 'Please select member type';
            toastErrors.push('Member Type');
        }

        // Validate PoSP Details
        if (!formData.agent_code) {
            errors.agent_code = 'Please select agent';
            toastErrors.push('Agent');
        }
        // if (!formData.branch_name) {
        //     errors.branch_name = 'Please select branch name';
        //     toastErrors.push('Branch Name');
        // }

        // Validate Members
        if (!members || members.length === 0) {
            toastErrors.push('At least one member is required');
        } else {
            members.forEach((member, index) => {
                const memberError = {};

                // Validate Sum Insured based on product type
                if (masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) {
                    if (!member.ad_sum_insured) {
                        memberError.ad_sum_insured = 'Required';
                    }
                    if (!member.pt_sum_insured) {
                        memberError.pt_sum_insured = 'Required';
                    }
                    if (!member.pp_sum_insured) {
                        memberError.pp_sum_insured = 'Required';
                    }
                    if (!member.tt_sum_insured) {
                        memberError.tt_sum_insured = 'Required';
                    }
                } else {
                    if (!member.sum_insured) {
                        memberError.sum_insured = 'Please enter sum insured';
                    }
                }

                // deductable is requeired if produt name is advantage top up 
                if (masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('advantage top up')) {
                    if (!member.deductible) {
                        memberError.deductible = 'Please enter deductible amount';
                    }
                }

                // Validate Nominee Details
                if (!member.nominee?.name) {
                    memberError.nominee_name = 'Please enter nominee name';
                }
                if (!member.nominee?.gender) {
                    memberError.nominee_gender = 'Please select nominee gender';
                }
                if (!member.nominee?.dob) {
                    memberError.nominee_dob = 'Please select nominee date of birth';
                }
                if (!member.nominee?.relation) {
                    memberError.nominee_relation = 'Please select nominee relation';
                }

                // Validate Appointee Details if nominee is minor
                if (member.nominee?.dob && dayjs().diff(dayjs(member.nominee.dob), 'years') < 18) {
                    if (!member.appointee?.name) {
                        memberError.appointee_name = 'Please enter appointee name';
                    }
                    if (!member.appointee?.gender) {
                        memberError.appointee_gender = 'Please select appointee gender';
                    }
                    if (!member.appointee?.dob) {
                        memberError.appointee_dob = 'Please select appointee date of birth';
                    }
                    if (!member.appointee?.relation) {
                        memberError.appointee_relation = 'Please select appointee relation';
                    }
                }

                if (Object.keys(memberError).length > 0) {
                    mErrors[index] = memberError;
                }
            });
        }

        // Validate Premium Details
        if (!formData.net_premium) {
            errors.net_premium = 'Please enter net premium';
            toastErrors.push('Net Premium');
        }
        if (!formData.tenure) {
            errors.tenure = 'Please select tenure';
            toastErrors.push('Tenure');
        }
        if (!formData.issue_date) {
            errors.issue_date = 'Please select issue date';
            toastErrors.push('Issue Date');
        }
        // if (!formData.start_date) {
        //     errors.start_date = 'Please select start date';
        //     toastErrors.push('Start Date');
        // }

        // Validate Payment Details only for new records
        if (!id && formData.net_premium && formData.tenure) {
            const paymentErrors = validatePaymentDetails();
            if (Object.keys(paymentErrors).length > 0) {
                Object.assign(errors, paymentErrors);
                toastErrors.push('Payment Details');
            }
        }

        setFormErrors(errors);
        setMemberErrors(mErrors);

        if (toastErrors.length > 0) {
            toast.error(`Please fill required fields: ${toastErrors.join(', ')}`);
            return false;
        }

        return Object.keys(errors).length === 0 && mErrors.length === 0;
    };



    // roll over
    useEffect(() => {
        const fetchRolloverData = async () => {
            if (!id) return;
            setLoading(true);
            try {
                const response = await dispatch(getRolloverMigrationById(id)).unwrap();
                if (!response) return;

                const rolloverData = response.data;
                // First fetch dependent dropdowns data
                if (rolloverData.insurance_company) {
                    await dispatch(fetchInsuranceCompanyBranchByInsuranceCompanyId(rolloverData.insurance_company));

                    if (rolloverData.product_type) {
                        // Fetch master products
                        await dispatch(getMasterProductByMainProductAndInsuranceCompany({
                            insuranceCompanyId: rolloverData.insurance_company,
                            mainProductId: rolloverData.product_type
                        }));

                        if (rolloverData.product_name) {
                            // Fetch sub-products
                            await dispatch(getSubProductByProductDetails({
                                mainProductId: rolloverData.product_type,
                                insuranceCompanyId: rolloverData.insurance_company,
                                productMasterId: rolloverData.product_name
                            }));
                        }
                    }
                }

                const paymentData = rolloverData.payment;
                if (rolloverData && rolloverData.policy_number === '') {
                    setIsPolicyCompleted(false);
                }
                else {
                    setIsPolicyCompleted(true);
                }
                // Set initial form data from rollover
                setFormData(prev => ({
                    ...prev,
                    customer_id: rolloverData.customer_id,
                    new_policy_number: rolloverData.policy_number,
                    previous_insurance_company: rolloverData.pinsurance_company,
                    previous_product_type: rolloverData.pproduct_type,
                    previous_product_name: rolloverData.pproduct_name,
                    previous_sub_product_name: rolloverData.psub_product,
                    previous_member_type: rolloverData.pmember_type,
                    insurance_company_name: rolloverData.insurance_company,
                    insurance_company_branch: rolloverData.insurance_branch,
                    product_type: rolloverData.product_type,
                    product_name: rolloverData.product_name,
                    sub_product_name: rolloverData.sub_product,
                    member_type: rolloverData.member_type,
                    agent_code: rolloverData.agent_code,
                    net_premium: rolloverData.net_premium,
                    gst_amount: rolloverData.gst_amount,
                    gst_percentage: rolloverData.gst_percentage,
                    total_premium: rolloverData.total_premium,
                    tenure: rolloverData.tenure,
                    co_pay: rolloverData.co_pay,
                    start_date: rolloverData.start_date ? dayjs(rolloverData.start_date) : null,
                    end_date: rolloverData.end_date ? dayjs(rolloverData.end_date) : null,
                    issue_date: rolloverData.policy_issue_date ? dayjs(rolloverData.policy_issue_date) : null,
                    //   proposal_number: rolloverData.proposal_Number,
                    previous_proposal_number: rolloverData.ProposalNumber,
                    remarks: rolloverData.remarks,
                    created_by: rolloverData.created_by,
                    salutation: rolloverData.customer_salutation,

                    // Payment Type
                    payment_type: paymentData?.PaymentType || '',

                    // Cash Payment Details
                    cash_amount: paymentData?.cash_amount || '',
                    received_date: paymentData?.received_date ? dayjs(paymentData.received_date) : null,

                    // Cheque Payment Details
                    cheque_amount: paymentData?.cheque_amount || '',
                    cheque_number: paymentData?.cheque_number || '',
                    cheque_date: paymentData?.cheque_date ? dayjs(paymentData.cheque_date) : null,
                    bank_name: paymentData?.bank_name || '',
                    bank_branch_name: paymentData?.branch_name || '',

                    // DD Payment Details
                    dd_amount: paymentData?.dd_amount || '',
                    dd_number: paymentData?.dd_number || '',
                    dd_date: paymentData?.dd_date ? dayjs(paymentData.dd_date) : null,

                    // Online Payment Details
                    online_amount: paymentData?.online_amount || '',
                    transaction_date: paymentData?.transaction_date ? dayjs(paymentData.transaction_date) : null,
                    transaction_type: paymentData?.transaction_type || '',
                    receipt_number: paymentData?.receipt_number || ''

                }));

                // Fetch customer
                const customerResponse = await dispatch(getCustomerById(rolloverData.customer_id)).unwrap();
                const membersResponse = await dispatch(getMemberByCustomerId(rolloverData.customer_id)).unwrap();
                const customer = customerResponse;
                const customerMembers = membersResponse || [];
                // Fill customer details into form
                setFormData(prev => ({
                    ...prev,
                    first_name: customer.first_name || '',
                    last_name: customer.last_name || '',
                    gender: customer.gender_id || '',
                    date_of_birth: customer.date_of_birth ? dayjs(customer.date_of_birth) : null,
                    marital_status: customer.marital_status_id || '',
                    occupation: customer.occupation || '',
                    mobile_number: customer.mobile || '',
                    email_id: customer.email || '',
                    aadhar_number: customer.aadhar_number || ''
                }));

                // Process and set member data
                const mappedMembers = rolloverData.members.map(member => {
                    // Determine if this is the primary customer
                    const isCustomer = member.relation === '39';

                    // Find the corresponding customer member from membersResponse
                    const customerMember = customerMembers.find(m => m.id === member.customer_member_id);

                    return {
                        id: member.id,
                        member_id: member.member_id || member.id,
                        // Set customer_member_id to null for primary customer, otherwise use the ID
                        customer_member_id: isCustomer ? null : member.customer_member_id,

                        // Personal Details
                        first_name: isCustomer ? customer.first_name : customerMember?.full_name?.split(' ')[0] || '',
                        middle_name: isCustomer ? customer.middle_name : '',
                        last_name: isCustomer ? customer.last_name : customerMember?.full_name?.split(' ').slice(-1)[0] || '',

                        // Member Details
                        relation: member.relation || '',
                        dob: isCustomer
                            ? formatDate(customer.date_of_birth)
                            : formatDate(customerMember?.date_of_birth) || '',
                        gender: isCustomer
                            ? genderOptions.find(option => option.id === customer.gender_id)?.api_name
                            : genderOptions.find(option => option.id === customerMember?.gender_id)?.api_name || '',
                        marital_status: isCustomer
                            ? customer.marital_status_id
                            : customerMember?.marital_status_id || '',
                        marriage_date: isCustomer
                            ? formatDate(customer.marriage_date)
                            : formatDate(customerMember?.marriage_date) || '',
                        mobile: isCustomer ? customer.mobile : customerMember?.mobile || '',
                        email_id: isCustomer ? customer.email : customerMember?.email || '',

                        // Insurance Details
                        sum_insured: member.sum_insured || '',
                        deductible: member.deductible || '',
                        occupation: isCustomer
                            ? customer.occupation
                            : customerMember?.member_occupation || '',
                        annual_income: member.annual_income || '',

                        // Nominee Details
                        nominee: {
                            name: member.nominee_name || '',
                            gender: member.nominee_gender || '',
                            dob: member.nominee_dob ? dayjs(member.nominee_dob) : null,
                            relation: member.nominee_relation || ''
                        },

                        // Appointee Details
                        appointee: {
                            name: member.appointee_name || '',
                            gender: member.appointee_gender || '',
                            dob: member.appointee_dob ? dayjs(member.appointee_dob) : null,
                            relation: member.appointee_relation || ''
                        }
                    };
                });

                // Set the members state
                setMembers(mappedMembers);

                // Dependent dropdown data
                if (rolloverData.insurance_company) {
                    await dispatch(fetchInsuranceCompanyBranchByInsuranceCompanyId(rolloverData.insurance_company));

                    if (rolloverData.product_type) {
                        await dispatch(getMasterProductByMainProductAndInsuranceCompany({
                            insuranceCompanyId: rolloverData.insurance_company,
                            mainProductId: rolloverData.product_type
                        }));

                        if (rolloverData.product_name) {
                            await dispatch(getSubProductByProductDetails({
                                insuranceCompanyId: rolloverData.insurance_company,
                                mainProductId: rolloverData.product_type,
                                productMasterId: rolloverData.product_name
                            }));
                        }
                    }
                }
            } catch (error) {
                console.error('Error fetching rollover data:', error);
                toast.error('Failed to fetch rollover data');
            } finally {
                setLoading(false);
            }
        };

        fetchRolloverData();
    }, [id, dispatch]);



    // useEffect to eliminate duplicates in members array
    useEffect(() => {
        if (members && members.length > 0) {
            const isDuplicatePresent = members.some((member, index, self) =>
                index !== self.findIndex((t) => (
                    t.first_name === member.first_name &&
                    t.last_name === member.last_name &&
                    t.dob === member.dob
                ))
            );
            if (isDuplicatePresent) {
                const uniqueMembers = members.filter((member, index, self) =>
                    index === self.findIndex((t) => (
                        t.first_name === member.first_name &&
                        t.last_name === member.last_name &&
                        t.dob === member.dob
                    ))
                );
                setMembers(uniqueMembers);
            }
            const isChildAbove25 = members.some((member) => {
                const relation = relationOptions.find(r => r.id === Number(member.relation_id));
                const dob = dayjs(member.dob, "DD-MM-YYYY");
                const age = dayjs().diff(dob, 'year');
                return ['son', 'daughter'].includes(relation?.label_name.toLowerCase()) && age >= 25;
            });
            if (isChildAbove25) {
                const filteredMembers = members.filter((member) => {
                    const relation = relationOptions.find(r => r.id === Number(member.relation_id));
                    if (['son', 'daughter'].includes(relation?.label_name.toLowerCase())) {
                        const dob = dayjs(member.dob, "DD-MM-YYYY");
                        const age = dayjs().diff(dob, 'year');
                        if (age < 25) {
                            return member;
                        }
                    } else {
                        return member;
                    }
                });
                toast.info('Removed children above 25 years of age.');
                setMembers(filteredMembers);
            }
        }
    }, [members]);

    const handleSave = async () => {
        try {
            setLoading(true);

            if (!validateForm()) {
                setLoading(false);
                return;
            }

            // Check if the product is Varishtha Bima and validate member ages
            const isVarishthaBima = masterProducts.find(
                product => product.id === formData.product_name
            )?.product_name.toLowerCase().includes('varishtha bima');

            if (isVarishthaBima) {
                // Check if any member is below 60 years
                const ineligibleMembers = members.filter(member => {
                    if (!member.dob) return false;
                    const age = dayjs().diff(dayjs(member.dob, 'DD/MM/YYYY'), 'years');
                    return age < 60;
                });

                if (ineligibleMembers.length > 0) {
                    toast.error('Varishtha Bima is only available for members aged 60 years and above. Cannot proceed with members below 60.');
                    setLoading(false);
                    return;
                }
            }
            // Prepare migration data from form
            const migrationData = {
                customer_id: formData.customer_id,
                customer_salutation: formData.salutation,
                policy_number: formData.new_policy_number,
                policy_pdf: formData.policy_pdf,

                // Previous policy details
                pinsurance_company: formData.previous_insurance_company || "",
                pproduct_type: formData.previous_product_type || "",
                pproduct_name: formData.previous_product_name || "",
                psub_product: formData.previous_sub_product_name || "",
                pmember_type: formData.previous_member_type || "",

                // New policy details
                insurance_company: formData.insurance_company_name,
                insurance_branch: formData.insurance_branch || null,
                agent_code: formData.agent_code,
                imf_branch: agents.find(agent => agent.id === formData.agent_code)?.branch_id || formData.imf_branch || null,
                imf_code: formData.imf_code || null,
                product_type: formData.product_type,
                product_name: formData.product_name,
                sub_product: formData.sub_product_name,
                member_type: formData.member_type,

                // Premium details
                net_premium: formData.net_premium || "0",
                gst_amount: formData.gst_amount || "0",
                gst_percentage: formData.gst_percentage || "18",
                total_premium: formData.total_premium || "0",
                tenure: formData.tenure || null,
                co_pay: formData.co_pay || false,

                // Dates
                start_date: formData.start_date ? dayjs(formData.start_date).format('YYYY-MM-DD') : null,
                end_date: formData.end_date ? dayjs(formData.end_date).format('YYYY-MM-DD') : null,
                policy_issue_date: formData.issue_date ? dayjs(formData.issue_date).format('YYYY-MM-DD') : null,

                // Additional fields
                remarks: formData.remarks || "",
                imf_branch: formData.imf_branch || null,
                proposal_type: proposalType,
                proposal_Number: formData.proposal_number,
                prev_proposal_number: formData.previous_proposal_number || null,

                created_by: formData.created_by,
                Updated_by: isEditMode ? username : null, // Add username when updating
                Updated_at: isEditMode ? dayjs().format('YYYY-MM-DD HH:mm:ss') : null // Add current timestamp when updating

            };
            // Prepare member data
            const memberData = members.map(member => {
                // Calculate whether this person is a customer (self) or a member
                const isSelf = member.relation === '39';

                return {
                    customer_member_id: isSelf ? null : member.customer_member_id,
                    member_id: member.member_id || null,
                    //customer_member_id: member.customer_member_id || null,
                    relation: member.relation,
                    sum_insured: member.sum_insured || 0,
                    deductible: member.deductible || null,
                    occupation: member.occupation || null,
                    annual_income: member.annual_income || 0,
                    created_by: formData.created_by,
                    // Add accident suraksha fields if they exist
                    ...(member.ad_sum_insured && {
                        ad_sum_insured: member.ad_sum_insured,
                        pp_sum_insured: member.pp_sum_insured,
                        pt_sum_insured: member.pt_sum_insured,
                        tt_sum_insured: member.tt_sum_insured,
                        lp_sum_insured: member.lp_sum_insured,
                        ls_sum_insured: member.ls_sum_insured,
                        me_sum_insured: member.me_sum_insured,
                        am_sum_insured: member.am_sum_insured,
                        bb_sum_insured: member.bb_sum_insured,
                        rf_sum_insured: member.rf_sum_insured,
                        aa_sum_insured: member.aa_sum_insured,
                        cs_sum_insured: member.cs_sum_insured,
                        hc_sum_insured: member.hc_sum_insured,
                        ft_sum_insured: member.ft_sum_insured
                    }),

                    // Add nominee details
                    nominee_name: member.nominee?.name || '',
                    nominee_gender: member.nominee?.gender || '',
                    nominee_dob: member.nominee?.dob ? dayjs(member.nominee.dob).format('YYYY-MM-DD') : null,
                    nominee_relation: member.nominee?.relation || null,

                    // Add appointee details if nominee is minor and appointee exists
                    ...(member.appointee && {
                        appointee_name: member.appointee.name || '',
                        appointee_gender: member.appointee.gender || '',
                        appointee_dob: member.appointee.dob ? dayjs(member.appointee.dob).format('YYYY-MM-DD') : null,
                        appointee_relation: member.appointee.relation || null
                    })
                };
            });

            // Get the payment type name
            const selectedPaymentType = paymentTypes.find(type => type.id === Number(formData.payment_type));
            const paymentTypeName = selectedPaymentType ? selectedPaymentType.api_name : '';

            // Prepare payment data based on payment type
            const paymentData = {
                TransactionID: '',
                PaymentType: formData.payment_type,
                PaymentOption: paymentTypeName,
                ProposalNumber: formData.proposal_number,
                UserIdentifier: '',
                UserId: '',
                FirstName: '',
                LastName: '',
                Mobile: '',
                Email: '',
                Vendor: '',
                CheckSum: '',
                Status: 'SUCCESS',
                ResponseURL: '',
                // transaction_date: '',
                // transaction_type: '',
                // online_amount: '',
            };

            // Add specific payment details based on the payment type selected
            if (hasPaymentType(['PAY_CASH'])) {
                paymentData.cash_amount = formData.cash_amount;
                paymentData.received_date = dayjs().format('YYYY-MM-DD');
            }
            else if (hasPaymentType(['PAY_CHEQUE'])) {
                paymentData.cheque_amount = formData.cheque_amount;
                paymentData.cheque_number = formData.cheque_number;
                paymentData.cheque_date = formData.cheque_date ? dayjs(formData.cheque_date).format('YYYY-MM-DD') : null;
                paymentData.bank_name = formData.bank_name;
                paymentData.branch_name = formData.bank_branch_name;
            }
            else if (hasPaymentType(['PAY_DD'])) {
                paymentData.dd_amount = formData.dd_amount;
                paymentData.dd_number = formData.dd_number;
                paymentData.dd_date = formData.dd_date ? dayjs(formData.dd_date).format('YYYY-MM-DD') : null;
                paymentData.branch_name = formData.bank_branch_name;
                paymentData.bank_name = formData.bank_name;
            }
            else if (hasPaymentType(['PAY_ONLINE', 'PAY_CHEQUE_CASH', 'PAY_DD_CASH'])) {
                paymentData.online_amount = formData.online_amount;
                paymentData.transaction_date = formData.transaction_date ? dayjs(formData.transaction_date).format('YYYY-MM-DD') : null;
                paymentData.transaction_type = formData.transaction_type || paymentTypeName;
            }

            // Add receipt number if provided
            if (formData.receipt_number) {
                paymentData.receipt_number = formData.receipt_number;
            }

            let result;

            const formPayload = new FormData();

            if (formData.policy_pdf?.image instanceof File) {
                // “policy_pdf” must match the name your multer.fields() is watching
                console.log('this is the policy number: ', migrationData.policy_number);
                formPayload.append(
                    'policy_pdf',
                    formData.policy_pdf.image,
                    migrationData.policy_number ? migrationData.policy_number : 'ROLLOVER' + formData.policy_number
                );
            }


            if (isEditMode) {
                // Structure main data
                formPayload.append("data", JSON.stringify({
                    migrationData
                }));
                // Update existing rollover migration
                result = await dispatch(updateRolloverMigration({
                    id,
                    formPayload
                })).unwrap();
            } else {
                // Structure main data
                formPayload.append("data", JSON.stringify({
                    migrationData,
                    memberData,
                    paymentData
                }));
                // Create new rollover migration
                result = await dispatch(createRolloverMigration(formPayload)).unwrap();
            }

            if (result.success) {
                toast.success(`Roll Over Data ${isEditMode ? 'updated' : 'saved'} successfully`);
                navigate('/dashboard/proposals');
            } else {
                toast.error(result.message || `Failed to ${isEditMode ? 'update' : 'save'} migration`);
            }

        } catch (error) {
            console.error("Error in rollover operation:", error);
            toast.error(error.message || `An error occurred while ${isEditMode ? 'updating' : 'saving'} the migration`);
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        const fetchData = async () => {
            try {
                await Promise.all([
                    dispatch(getAllCustomer()), // Add this line
                    dispatch(fetchInsuranceCompanies()),
                    // ... rest of your existing dispatch calls
                ]);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, []);


    const deductileOptions = [
        { value: 50000, label: '50 Thousand' },
        { value: 100000, label: '1 Lakh' },
        { value: 150000, label: '1.5 Lakh' },
        { value: 200000, label: '2 Lakh' },
        { value: 250000, label: '2.5 Lakh' },
        { value: 300000, label: '3 Lakh' },
        { value: 350000, label: '3.5 Lakh' },
        { value: 400000, label: '4 Lakh' },
        { value: 450000, label: '4.5 Lakh' },
        { value: 500000, label: '5 Lakh' },
        { value: 600000, label: '6 Lakh' },
        { value: 700000, label: '7 Lakh' },
        { value: 750000, label: '7.5 Lakh' },
        { value: 800000, label: '8 Lakh' },
        { value: 900000, label: '9 Lakh' },
        { value: 1000000, label: '10 Lakh' },
        { value: 1500000, label: '15 Lakh' },
        { value: 2000000, label: '20 Lakh' },
        { value: 2500000, label: '25 Lakh' },
        { value: 3000000, label: '30 Lakh' },
        { value: 3500000, label: '35 Lakh' },
        { value: 4000000, label: '40 Lakh' },
        { value: 4500000, label: '45 Lakh' },
        { value: 5000000, label: '50 Lakh' },
        { value: ********, label: '1 Crore' },
        { value: ********, label: '2 Crore' },
    ]
    const sumInsuredOptions = [
        { value: 50000, label: '50 Thousand' },
        { value: 100000, label: '1 Lakh' },
        { value: 150000, label: '1.5 Lakh' },
        { value: 200000, label: '2 Lakh' },
        { value: 250000, label: '2.5 Lakh' },
        { value: 300000, label: '3 Lakh' },
        { value: 350000, label: '3.5 Lakh' },
        { value: 400000, label: '4 Lakh' },
        { value: 450000, label: '4.5 Lakh' },
        { value: 500000, label: '5 Lakh' },
        { value: 600000, label: '6 Lakh' },
        { value: 700000, label: '7 Lakh' },
        { value: 750000, label: '7.5 Lakh' },
        { value: 800000, label: '8 Lakh' },
        { value: 900000, label: '9 Lakh' },
        { value: 1000000, label: '10 Lakh' },
        { value: 1500000, label: '15 Lakh' },
        { value: 2000000, label: '20 Lakh' },
        { value: 2500000, label: '25 Lakh' },
        { value: 3000000, label: '30 Lakh' },
        { value: 3500000, label: '35 Lakh' },
        { value: 4000000, label: '40 Lakh' },
        { value: 4500000, label: '45 Lakh' },
        { value: 5000000, label: '50 Lakh' },
        { value: ********, label: '1 Crore' },
        { value: ********, label: '2 Crore' },
    ]

    useEffect(() => {
        dispatch(fetchAllBanks()).then((action) => {
            if (action.payload) {
                setBanks(action.payload);
            }
        });
    }, [dispatch]);
    // Add this helper function to parse health declaration string
    const parseHealthDeclaration = (healthDeclarationString) => {
        if (!healthDeclarationString) return {};

        return healthDeclarationString.split('|').reduce((acc, pair) => {
            const [key, value] = pair.split(':');
            return { ...acc, [key]: value };
        }, {});
    };

    // To check if the payment type is present in the API names
    const hasPaymentType = (apiNames) => {
        return paymentTypes.some(type =>
            apiNames.includes(type.api_name) &&
            type.id === Number(formData.payment_type)
        );
    }
    // To handle the date change
    const handleDateChange = (name, date) => {
        if (name === 'start_date') {
            if (!date) {
                // If date is null/undefined, clear the start_date
                setFormData(prev => ({
                    ...prev,
                    start_date: null,
                    end_date: null
                }));
                return;
            }

            if (formData.tenure) {
                // Calculate end date based on tenure (1 year minus 1 day)
                const endDate = dayjs(date).add(Number(formData.tenure), 'year').subtract(1, 'day');
                setFormData(prev => ({
                    ...prev,
                    start_date: date,
                    end_date: endDate
                }));
                // setFormErrors(prev => ({
                //     ...prev,
                //     start_date: '',
                //     end_date: ''
                // }));
            } else {
                setFormData(prev => ({
                    ...prev,
                    start_date: date
                }));
                // setFormErrors(prev => ({
                //     ...prev,
                //     start_date: ''
                // }));
            }
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: date
            }));
            // setFormErrors(prev => ({
            //     ...prev,
            //     [name]: ''
            // }));
        }
    };
    const handleAutocompleteDropdownChange = async ({ name, value }) => {
        if (name === 'customer_id') {
            setFormData(prev => ({
                ...prev,
                customer_id: value
            }));

            if (value) {
                try {
                    // Fetch customer details
                    const customerResponse = await dispatch(getCustomerById(value)).unwrap();
                    await dispatch(getMemberByCustomerId(value)).unwrap();
                    setAreCustomerMembersFetched(true);
                    if (customerResponse) {
                        // Get customer's gender from gender options
                        const customerGender = genderOptions.find(g => g.id === customerResponse.gender_id)?.api_name?.toLowerCase();
                        // Determine default salutation based on gender and marital status
                        let defaultSalutation = '';
                        if (customerGender === 'm') {
                            // Find the salutation option with api_name 'MR' for males
                            defaultSalutation = salutationOptions.find(s => s.api_name === 'MR')?.id;
                        } else if (customerGender === 'f') {
                            // Check if customer is married
                            const isMarried = customerResponse.marital_status_id ===
                                maritalStatusOptions.find(m => m.api_name?.toLowerCase() === 'married')?.id;
                            // Set 'MRS' for married females, 'MISS' for unmarried
                            defaultSalutation = salutationOptions.find(s =>
                                s.api_name === (isMarried ? 'MRS' : 'MISS'))?.id;
                        }
                        // Update form data with customer details and calculated salutation
                        setFormData(prev => ({
                            ...prev,
                            customer_id: value,
                            salutation: defaultSalutation || '', // Use calculated salutation
                            first_name: customerResponse.first_name || '',
                            middle_name: customerResponse.middle_name || '',
                            last_name: customerResponse.last_name || '',
                            date_of_birth: customerResponse.date_of_birth ? dayjs(customerResponse.date_of_birth) : null,
                            gender: customerResponse.gender_id || '',
                            marital_status: customerResponse.marital_status_id || '',
                            marriage_date: customerResponse.marriage_date ? dayjs(customerResponse.marriage_date) : null,
                            occupation: customerResponse.occupation || '',
                            mobile_number: customerResponse.mobile || '',
                            email_id: customerResponse.email || '',
                            aadhar_number: customerResponse.aadhar_number || '',
                            pan_number: customerResponse.pan_number || '',
                            ckyc_number: customerResponse.ckyc_number || ''
                        }));

                        // Initialize the first member as the customer
                        const customerMember = {
                            first_name: customerResponse.first_name || '',
                            middle_name: customerResponse.middle_name || '',
                            last_name: customerResponse.last_name || '',
                            dob: customerResponse.date_of_birth ? dayjs(customerResponse.date_of_birth).format('DD/MM/YYYY') : null,
                            relation: relationOptions.find(r => r.api_name.toLowerCase() === 'self')?.id || '',
                            gender: customerResponse.gender_id || '',
                            marital_status: customerResponse.marital_status_id || '',
                            marriage_date: customerResponse.marriage_date ? dayjs(customerResponse.marriage_date).format('DD/MM/YYYY') : null,
                            mobile: customerResponse.mobile || '',
                            email_id: customerResponse.email || '',
                            occupation: customerResponse.occupation || '',
                            nominee: {
                                name: '',
                                gender: '',
                                dob: null,
                                relation: ''
                            },
                            appointee: {
                                name: '',
                                gender: '',
                                dob: null,
                                relation: ''
                            },
                            health_declaration: {}
                        };

                        setMembers([customerMember]);
                        toast.success('Customer details loaded successfully');
                    }
                } catch (error) {
                    console.error('Error fetching customer details:', error);
                    toast.error('Failed to load customer details');
                }
            } else {
                // Reset form data if no customer is selected
                setFormData(prev => ({
                    ...prev,
                    customer_id: '',
                    salutation: '',
                    first_name: '',
                    middle_name: '',
                    last_name: '',
                    date_of_birth: null,
                    gender: '',
                    marital_status: '',
                    marriage_date: null,
                    occupation: '',
                    mobile_number: '',
                    email_id: '',
                    aadhar_number: '',
                    pan_number: '',
                    ckyc_number: ''
                }));
                setMembers([]);
                setAreCustomerMembersFetched(false);
            }
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };
    // Add a new state to track first search
    // To fetch the proposal members data when the component is mounted
    useEffect(() => {
        if (formData.policy_number) {
            if (customer) {
                const proposalMembers = fetchedProposal?.members;
                if (proposalMembers) {
                    const memberDataArray = proposalMembers.map(member => {
                        const isCustomer = member.customer_member_id === null;
                        const customerMember = membersData.find(m => m.id === member.customer_member_id);
                        const customerMemberName = customerMember?.full_name?.split(' ');
                        const genderId = isCustomer ? customer.gender_id : customerMember?.gender_id || '';

                        return {
                            first_name: isCustomer ? customer.first_name : customerMemberName?.[0] || '',
                            middle_name: isCustomer
                                ? customer.middle_name
                                : customerMemberName?.length > 2
                                    ? customerMemberName?.[1]
                                    : '',
                            last_name: isCustomer
                                ? customer.last_name
                                : customerMemberName?.length > 1
                                    ? customerMemberName?.[customerMemberName?.length - 1]
                                    : '',
                            dob: isCustomer ? formatDate(customer.date_of_birth) : formatDate(customerMember?.date_of_birth) || '',
                            relation: member?.relation || '',
                            gender: genderOptions.find(option => option.id === genderId)?.api_name || '',
                            marital_status: isCustomer ? customer.marital_status_id : customerMember?.marital_status_id || '',
                            marriage_date: isCustomer ? formatDate(customer.marriage_date) : formatDate(customerMember?.marriage_date) || '',
                            mobile: isCustomer ? customer.mobile : customerMember?.mobile || '',
                            member_id: member.member_id,
                            customer_member_id: member.customer_member_id || '',
                            email_id: isCustomer ? customer.email : customerMember?.email || '',
                            sum_insured: member.sum_insured,
                            deductible: member.deductible || null,
                            ...(member.ad_sum_insured && {
                                occupation: member.occupation,
                                ad_sum_insured: member.ad_sum_insured || 0,
                                pp_sum_insured: member.pp_sum_insured || 0,
                                pt_sum_insured: member.pt_sum_insured || 0,
                                tt_sum_insured: member.tt_sum_insured || 0,
                                lp_sum_insured: member.lp_sum_insured || 0,
                                ls_sum_insured: member.ls_sum_insured || 0,
                                me_sum_insured: member.me_sum_insured || 0,
                                am_sum_insured: member.am_sum_insured || 0,
                                bb_sum_insured: member.bb_sum_insured || 0,
                                rf_sum_insured: member.rf_sum_insured || 0,
                                aa_sum_insured: member.aa_sum_insured || 0,
                                cs_sum_insured: member.cs_sum_insured || 0,
                                hc_sum_insured: member.hc_sum_insured || 0,
                                ft_sum_insured: member.ft_sum_insured || 0,
                                annual_income: member.annual_income || 0,
                            }),
                            id: member.id,
                            nominee: {
                                name: member.nominee_name || '',
                                gender: member.nominee_gender || '',
                                dob: member.nominee_dob ? member.nominee_dob : null,
                                relation: member.nominee_relation || null
                            },
                            appointee: {
                                name: member.appointee_name || '',
                                gender: member.appointee_gender || '',
                                dob: member.appointee_dob ? member.appointee_dob : null,
                                relation: member.appointee_relation || null
                            }
                        };
                    });

                    setMembers(memberDataArray);
                }
            } else {
                dispatch(getCustomerById(fetchedProposal?.customer_id)).then((res) =>
                    setFormData(prev => ({
                        ...prev,
                        customer_id: res.payload.id,
                    }))
                );
            }
        }
    }, [customer, fetchedProposal, membersData]);

    // Add with your other state variables
    const [openMemberDialog, setOpenMemberDialog] = useState(false);

    // Add these handler functions
    const handleMemberDialogOpen = () => {
        // Filter out members that are already added to the form
        const filteredMembersData = membersData.filter(potentialMember => {
            // Don't show members that are already added (check by customer_member_id)
            return !members.some(existingMember =>
                existingMember.customer_member_id === potentialMember.id
            );
        });

        // Set the filtered data
        setMemberSelectionData({
            customer,
            membersData: filteredMembersData
        });

        // Open the dialog
        setOpenMemberDialog(true);
    };

    const handleMemberDialogClose = () => {
        setOpenMemberDialog(false);
    };

    // To Add a new member
    const handleAddMember = (members) => {
        // Handle array of members
        const processedMembers = members.map(member => {
            if (member.first_name) {
                return handleAddCustomerAsMember();
            } else {
                // Extract first, middle and last name from full_name
                const name = member?.full_name?.split(' ') || [];
                const firstName = name[0] || member.first_name;
                const middleName = name.length > 2 ? name[1] : member.second_name;
                const lastName = name.length > 1 ? name[name.length - 1] : member.last_name;

                // Create base member object
                const memberObj = {
                    first_name: firstName,
                    middle_name: middleName,
                    last_name: lastName,
                    // customer_member_id: member.customer_member_id || '',
                    customer_member_id: Number(member.id.replace('member-', '')) || null,

                    dob: formatDate(member.date_of_birth) || '',
                    relation: member.relation_id || '',
                    gender: genderOptions.find(gender => gender.id === member.gender_id)?.api_name || '',
                    marital_status: member.marital_status_id || '',
                    marriage_date: formatDate(member.marriage_date) || '',
                    member_id: member.member_id || '',
                    mobile: member.mobile || '',
                    email_id: member.email || '',
                    // Initialize empty nominee and appointee
                    nominee: {
                        name: "",
                        gender: "",
                        dob: null,
                        relation: null
                    },
                    appointee: {
                        name: '',
                        gender: '',
                        dob: null,
                        relation: null
                    }
                };

                // Add product specific fields
                if (masterProducts.find(product => product.id === formData.product_name)
                    ?.product_name.toLowerCase() === 'fg accident suraksha') {
                    return {
                        ...memberObj,
                        occupation: member?.member_occupation || '',
                        ad_sum_insured: Math.round(Number(member?.ad_sum_insured)) || 0,
                        pp_sum_insured: Math.round(Number(member?.pp_sum_insured)) || 0,
                        pt_sum_insured: Math.round(Number(member?.pt_sum_insured)) || 0,
                        tt_sum_insured: Math.round(Number(member?.tt_sum_insured)) || 0,
                        lp_sum_insured: Math.round(Number(member?.lp_sum_insured)) || 0,
                        ls_sum_insured: Math.round(Number(member?.ls_sum_insured)) || 0,
                        me_sum_insured: Math.round(Number(member?.me_sum_insured)) || 0,
                        am_sum_insured: Math.round(Number(member?.am_sum_insured)) || 0,
                        bb_sum_insured: Math.round(Number(member?.bb_sum_insured)) || 0,
                        rf_sum_insured: Math.round(Number(member?.rf_sum_insured)) || 0,
                        aa_sum_insured: Math.round(Number(member?.aa_sum_insured)) || 0,
                        cs_sum_insured: Math.round(Number(member?.cs_sum_insured)) || 0,
                        hc_sum_insured: Math.round(Number(member?.hc_sum_insured)) || 0,
                        ft_sum_insured: Math.round(Number(member?.ft_sum_insured)) || 0,
                        annual_income: Math.round(Number(member?.annual_income)) || 0,
                    };
                } else {
                    return {
                        ...memberObj,
                        sum_insured: Math.round(Number(member?.sum_insured)) || null,
                        deductible: Math.round(Number(member?.deductible)) || null,
                    };
                }
            }
        });

        // Update members state
        setMembers(prevMembers => [...prevMembers, ...processedMembers]);
    };

    const handleAddCustomerAsMember = () => {
        const data = {
            first_name: customer.first_name || '',
            middle_name: customer.middle_name || '',
            last_name: customer.last_name || '',
            dob: formatDate(customer.date_of_birth) || '',
            gender: genderOptions.find(gender => gender.id === customer.gender_id)?.api_name || '',
            relation: relationOptions.find(relation => relation?.label_name?.toLowerCase() === 'self')?.id || '',
            marital_status: customer.marital_status_id || '',
            marriage_date: formatDate(customer.marriage_date) || '',
            member_id: '', // Add check
            mobile: customer.mobile || '',
            email_id: customer.email || '',
            ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase() === 'fg accident suraksha') ? {
                occupation: customer.occupation,
                ad_sum_insured: 0,
                pp_sum_insured: 0,
                pt_sum_insured: 0,
                tt_sum_insured: 0,
                lp_sum_insured: 0,
                ls_sum_insured: 0,
                me_sum_insured: 0,
                am_sum_insured: 0,
                bb_sum_insured: 0,
                rf_sum_insured: 0,
                aa_sum_insured: 0,
                cs_sum_insured: 0,
                hc_sum_insured: 0,
                ft_sum_insured: 0,
                annual_income: 0,
            } : {
                deductible: '', // Add check
                sum_insured: ''
            }),
            nominee: {
                name: "",
                gender: "",
                dob: null,
                relation: null
            },
            appointee: {
                name: '',
                gender: '',
                dob: null,
                relation: null
            }
        }
        return data;
    }


    // Modify the handlePolicySearch function
    const handlePolicySearch = async () => {
        if (!formData.policy_number) {
            toast.error("Please enter a policy number");
            return;
        }
        if (readyForProposal) {

            try {
                setIsSearching(true);
                const response = await dispatch(getProposalDetailsByPolicyNumber({ policy_number: formData.policy_number, policy_type: 'roll_over' })).unwrap();
                if (response) {
                    setAllowManualEntry(false);
                    setSelectedPolicy(response);
                    // First fetch customer details if customer_id exists

                    if (response.customer_id) {
                        try {
                            //setIsSearching(true);
                            await dispatch(getCustomerById(response.customer_id)).unwrap();
                            // Fetch customer members and wait for the response
                            await dispatch(getMemberByCustomerId(response.customer_id)).unwrap();
                            setAreCustomerMembersFetched(true);
                        } catch (error) {
                            console.error("Error fetching customer details:", error);
                        }
                    }

                    // Now process members after customer data is fetched
                    if (response.members && Array.isArray(response.members)) {
                        const processedMembers = response.members.map(member => {
                            const isCustomer = member.customer_member_id === null;
                            const customerMember = membersData.find(m => m.id === member.customer_member_id);
                            const customerMemberName = customerMember?.full_name?.split(' ');
                            const genderId = isCustomer ? customer?.gender_id : customerMember?.gender_id || '';
                            const healthDeclarationAnswers = parseHealthDeclaration(member.health_declaration);

                            return {  // Add curly braces here
                                first_name: isCustomer ? customer?.first_name : customerMemberName?.[0] || '',
                                middle_name: isCustomer ? customer?.middle_name : customerMemberName?.length > 2 ? customerMemberName?.[1] : '',
                                last_name: isCustomer ? customer?.last_name : customerMemberName?.length > 1 ? customerMemberName?.[customerMemberName?.length - 1] : '',
                                dob: isCustomer ? formatDate(customer?.date_of_birth) : formatDate(customerMember?.date_of_birth) || '',
                                relation: member?.relation || '',
                                sum_insured: member?.sum_insured || 0,
                                gender: genderOptions.find(option => option.id === genderId)?.api_name || '',
                                marital_status: isCustomer ? formData.marital_status : customerMember?.marital_status_id || '',
                                marriage_date: isCustomer ? formatDate(customer?.marriage_date) : formatDate(customerMember?.marriage_date) || '',
                                mobile: isCustomer ? customer?.mobile : customerMember?.mobile || '',
                                member_id: member.member_id,
                                customer_member_id: member.customer_member_id || '',
                                email_id: isCustomer ? customer?.email : customerMember?.email || '',
                                occupation: isCustomer ? formData?.occupation : customerMember?.member_occupation || '',
                                deductible: member.deductible || null,
                                health_declaration: healthDeclarationAnswers,
                                ...(member.ad_sum_insured && {
                                    occupation: member.occupation,
                                    ad_sum_insured: member.ad_sum_insured || 0,
                                    pp_sum_insured: member.pp_sum_insured || 0,
                                    pt_sum_insured: member.pt_sum_insured || 0,
                                    tt_sum_insured: member.tt_sum_insured || 0,
                                    lp_sum_insured: member.lp_sum_insured || 0,
                                    ls_sum_insured: member.ls_sum_insured || 0,
                                    me_sum_insured: member.me_sum_insured || 0,
                                    am_sum_insured: member.am_sum_insured || 0,
                                    bb_sum_insured: member.bb_sum_insured || 0,
                                    rf_sum_insured: member.rf_sum_insured || 0,
                                    aa_sum_insured: member.aa_sum_insured || 0,
                                    cs_sum_insured: member.cs_sum_insured || 0,
                                    hc_sum_insured: member.hc_sum_insured || 0,
                                    ft_sum_insured: member.ft_sum_insured || 0,
                                    annual_income: member.annual_income || 0,
                                }),
                                id: member.id,
                                nominee: {
                                    name: member.nominee_name || '',
                                    gender: member.nominee_gender || '',
                                    dob: member.nominee_dob ? member.nominee_dob : null,
                                    relation: member.nominee_relation || null
                                },
                                appointee: {
                                    name: member.appointee_name || '',
                                    gender: member.appointee_gender || '',
                                    dob: member.appointee_dob ? member.appointee_dob : null,
                                    relation: member.appointee_relation || null
                                }
                            };  // Close the return object
                        });
                        // Set members based on the number of members
                        if (processedMembers.length === 1) {
                            setMembers([processedMembers[0]]);
                        } else if (processedMembers.length > 1) {
                            setMembers(processedMembers);
                        }
                    }

                    // Fetch customer details if customer_id exists
                    let customerDetails = null;
                    if (response.customer_id) {
                        try {
                            customerDetails = await dispatch(getCustomerById(response.customer_id)).unwrap();
                        } catch (error) {
                            console.error("Error fetching customer details:", error);
                        }
                    }
                    // Find data from arrays with null checks
                    const insuranceCompany = Array.isArray(insuranceCompanies) ?
                        insuranceCompanies.find(company => company.id === response.insurance_company) : null;

                    const product = Array.isArray(mainProducts) ?
                        mainProducts.find(product => product.id === response.product_type) : null;

                    const Product_Master = Array.isArray(masterProducts) ?
                        masterProducts.find(masterProduct => masterProduct.id === response.product_name) : null;

                    const subProduct = Array.isArray(subProducts) ?
                        subProducts.find(subProduct => subProduct.id === response.sub_product) : null;

                    const foundAgent = Array.isArray(agents) ?
                        agents.find(agent => agent.id === parseInt(response.agent_code)) : null;

                    const foundInsurenceBranch = Array.isArray(insuranceBranches) ?
                        insuranceBranches.find(branch => branch.id === response.insurance_branch) : null;
                    // Get member details from the first member
                    const memberDetails = response.members?.[0] || {};

                    setFormData(prev => ({
                        ...prev,
                        // Previous sections remain the same
                        policy_number: response?.policy_number || '',
                        previous_insurance_company: insuranceCompany?.insurance_company_name || '',
                        previous_product_type: product?.main_product || '',
                        previous_product_name: Product_Master?.product_name || '',
                        previous_sub_product_name: subProduct?.sub_product_name || '',
                        previous_member_type: response?.member_type || '',
                        previous_insurance_company_branch: foundInsurenceBranch?.insurance_co_branch_name || '',
                        // Portability details
                        customer_id: response?.customer_id || '',
                        //  insurance_company: response?.insurance_company || '',
                        //  insurance_company_branch: response?.insurance_branch || '',
                        product_type: response?.product_type || '',
                        // product_name: response?.product_name || '',
                        // sub_product_name: response?.sub_product || '',
                        // member_type: response?.member_type || '',

                        // Agent details
                        agent_code: foundAgent?.id || '',
                        agent_name: foundAgent?.full_name || '',
                        branch_name: foundAgent?.branch_name || '',
                        imf_branch: foundAgent?.branch_id || null,
                        imf_code: response?.imf_code || null,
                        // Customer Details from API
                        salutation: response?.customer_salutation || '',
                        first_name: customerDetails?.first_name || response.members?.[0]?.first_name || '',
                        last_name: customerDetails?.last_name || response.members?.[0]?.last_name || '',
                        date_of_birth: customerDetails?.date_of_birth ?
                            dayjs(customerDetails.date_of_birth) :
                            response.members?.[0]?.date_of_birth ?
                                dayjs(response.members[0].date_of_birth) : null,
                        marriage_date: customerDetails?.marriage_date ?
                            dayjs(customerDetails.marriage_date) :
                            response.members?.[0]?.marriage_date ?
                                dayjs(response.members[0].marriage_date) : null,
                        gender: customerDetails?.gender_id || response.members?.[0]?.gender_id || '',
                        marital_status: customerDetails?.marital_status_id || response.members?.[0]?.marital_status_id || '',
                        occupation: customerDetails?.occupation || response.members?.[0]?.occupation || '',
                        aadhar_number: customerDetails?.aadhar_number || response.members?.[0]?.aadhar_number || '',
                        mobile_number: customerDetails?.mobile_number || '',
                        email: customerDetails?.email || '',
                        pan_number: customerDetails?.pan_number || '',
                        ckyc_number: response?.ckyc_number || '',
                        // Premium details
                        total_premium: response?.total_premium || '',
                        net_premium: response?.net_premium || '',
                        gst_amount: response?.gst_amount || '',
                        gst_percentage: response?.gst_percentage || '',
                        //   start_date: response?.start_date ? dayjs(response.start_date) : null,
                        //  end_date: response?.end_date ? dayjs(response.end_date) : null,
                        //  tenure: response?.tenure || '',
                        issue_date: response?.policy_issue_date ? dayjs(response.policy_issue_date) : null,
                        //  proposal_number: response?.ProposalNumber || '',
                        prev_proposal_number: response?.ProposalNumber || response?.proposal_Number || '',
                        // Additional details
                        remarks: response?.remarks || '',
                        created_by: response?.Created_by || '',
                        created_at: response?.Created_at || '',
                        updated_by: response?.Updated_by || '',
                        updated_at: response?.Updated_at || '',
                        quotation_number: response?.quotation_number || '',

                    }));


                    // toast.success("Policy data loaded successfully");
                } else {
                    toast.error("No policy found with this number");
                    setAllowManualEntry(true); // Enable manual entry if no policy is found
                    // Reset the renewal details fields
                    const newProposalNumber = await generateProposalNumber(user.userId, dispatch);
                    setFormData(prev => ({
                        ...prev,
                        previous_insurance_company: '',
                        previous_product_type: '',
                        previous_product_name: '',
                        previous_sub_product_name: '',
                        previous_member_type: '',
                        previous_proposal_number: null,
                        proposal_number: newProposalNumber // Set the newly generated number

                    }));

                }
            } catch (error) {
                console.error("Error fetching policy data:", error);
                //  toast.error(error.message || "Error fetching policy data");
                setAllowManualEntry(true); // Enable manual entry if there's an error

            } finally {
                setIsSearching(false);
            }
        };
    }


    // To handle the proposal type change
    const handleProposalTypeChange = (proposalType) => {
        if (proposalType === 'Renewal') {
            navigate(`/dashboard/proposal-renewal/`)
        }
        else if (proposalType === 'Roll Over') {
            navigate(`/dashboard/proposal-roll-over/`) // Add the route for roll over
        }
        else if (proposalType === 'Migration') {
            navigate(`/dashboard/proposal-migration/`) // Add the route for migration
        }
        else if (proposalType === 'New') {
            navigate(`/dashboard/create-proposal`)
        }
    }

    // Add this useEffect after your existing useEffects
    useEffect(() => {
        // Get discount amounts with null checks
        const family_discount_amount = formData?.family_discount_amount || 0;
        const long_term_discount_amount = formData?.long_term_discount_amount || 0;

        // Calculate net premium after discounts
        const netPremium = formData?.net_premium - family_discount_amount - long_term_discount_amount;

        // Calculate GST amount (18% of net premium after discounts)
        const gst_amount = Math.round(netPremium * 0.18);

        // Calculate total premium
        const total_premium = Math.round(netPremium + gst_amount);

        // Update form data with new calculations
        setFormData(prevData => ({
            ...prevData,
            gst_amount: gst_amount,
            total_premium: total_premium
        }));
    }, [formData.net_premium, formData.family_discount_amount, formData.long_term_discount_amount]);
    // To handle the member change
    // Modify the handleMemberChange function to convert text values to uppercase

    const handleMemberChange = (index, field, value) => {
        // Clear member error for the field being changed
        setMemberErrors(prev => {
            const updated = [...(prev || [])];
            if (updated[index]) {
                if (field === 'nominee' || field === 'appointee') {
                    updated[index] = {
                        ...updated[index],
                        [`${field}_${Object.keys(value)[0]}`]: ''
                    };
                } else {
                    updated[index] = {
                        ...updated[index],
                        [field]: ''
                    };
                }
            }
            return updated;
        });

        // Convert string values to uppercase
        let processedValue = value;

        // Handle nominee/appointee nested fields with name property
        if ((field === 'nominee' || field === 'appointee') && value.name !== undefined) {
            processedValue = {
                ...value,
                name: typeof value.name === 'string' ? value.name.toUpperCase() : value.name
            };
        }
        // Handle regular string fields
        else if (typeof value === 'string' && field !== 'dob' && field !== 'marriage_date') {
            processedValue = value.toUpperCase();
        }

        setMembers(prevMembers => {
            const updatedMembers = [...prevMembers];

            // Handle nominee/appointee nested fields
            if (field === 'nominee' || field === 'appointee') {
                updatedMembers[index] = {
                    ...updatedMembers[index],
                    [field]: {
                        ...updatedMembers[index]?.[field],
                        ...processedValue
                    }
                };
            } else {
                // Handle regular fields
                updatedMembers[index] = {
                    ...updatedMembers[index],
                    [field]: processedValue
                };
            }

            return updatedMembers;
        });
    };
    // To handle the member removal
    const handleRemoveMember = (index) => {
        setMembers(prevMembers => prevMembers.filter((_, i) => i !== index));
    }

    // Add handler for policy number change
    // Modify the handleChange function to convert text input values to uppercase

    useEffect(() => {
        if (formData.insurance_company_branch) {
            const selectedBranch = insuranceBranches?.find(
                branch => branch.id === Number(formData.insurance_company_branch)
            );
            if (selectedBranch?.imf_code) {
                setFormData(prev => ({
                    ...prev,
                    imf_code: selectedBranch.imf_code,
                    imf_branch: selectedBranch.imf_branch_id || null
                }));
            }
        }
    }, [formData.insurance_company_branch, insuranceBranches]);

    const handleChange = (e) => {
        const { name, value, type } = e.target;

        if (name === 'insurance_company_branch') {
            const selectedBranch = insuranceBranches?.find(
                branch => branch.id === Number(value)
            );
            setFormData(prev => ({
                ...prev,
                [name]: value,
                imf_code: selectedBranch?.imf_code || '',
                imf_branch: selectedBranch?.imf_branch_id || null
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }

        if (name === 'insurance_company_name') {
            const selectedCompany = insuranceCompanies.find(company => company.id === value);
            if (selectedCompany && formData.previous_insurance_company &&
                selectedCompany.insurance_company_name.toLowerCase() === formData.previous_insurance_company.toLowerCase()) {
                setFormErrors(prev => ({
                    ...prev,
                    insurance_company_name: 'For policy rollover, the new insurance company must differ from the previous one. Verify migration if retaining the company name.'
                }));
                return; // Prevent setting the same insurance company
            }
        }

        // Clear error for the field being changed
        setFormErrors(prev => ({
            ...prev,
            [name]: ''
        }));

        // Clear the error when a different company is selected
        if (name === 'insurance_company_name') {
            setFormErrors(prev => ({
                ...prev,
                insurance_company_name: ''
            }));
        }
        // Convert text input to uppercase if it's a string type field
        let processedValue = value;
        if (typeof value === 'string' && (type === 'text' || type === undefined)) {
            processedValue = value.toUpperCase();
        }

        // If policy number is entered, validate start date
        if (name === 'new_policy_number' && processedValue) {
            if (!formData.start_date) {
                setFormErrors(prev => ({
                    ...prev,
                    start_date: 'Start Date is mandatory when Policy Number is entered'
                }));
            }
        }

        // Reset dependent fields when parent field changes
        if (name === 'insurance_company_name') {
            setFormData(prev => ({
                ...prev,
                [name]: processedValue,
                //  product_type: '',
                product_name: '',
                sub_product_name: ''
            }));
        } else if (name === 'product_type') {
            setFormData(prev => ({
                ...prev,
                [name]: processedValue,
                product_name: '',
                sub_product_name: ''
            }));
        } else if (name === 'product_name') {
            setFormData(prev => ({
                ...prev,
                [name]: processedValue,
                sub_product_name: ''
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: processedValue
            }));
        }
    };

    const handleFileSelect = (file) => {
        setFormData(prev => ({
            ...prev,
            policy_pdf: file
        }))
        setFormErrors(prev => ({
            ...prev,
            policy_pdf: ''
        }))
    }

    // Add this helper function at the component level
    const formatDate = (date) => {
        if (!date) return null;
        return dayjs(date);
    };

    // Add this helper function if not already present
    const getHealthDeclarationToObject = (healthDeclarationString) => {
        if (!healthDeclarationString) return {};

        const result = {};
        const pairs = healthDeclarationString.split('|');

        pairs.forEach(pair => {
            const [key, value] = pair.split(':');
            if (key && value) {
                result[key] = value;
            }
        });

        return result;
    };

    // To handle the cancel event
    const handleCancel = () => {
        // dispatch(clearSelectedQuotation());
        navigate('/dashboard/proposals')
    }

    const isFieldEmpty = (value) => {
        return value === null || value === undefined || value === '';
    };

    return (
        <Box sx={{
            padding: { xs: '0 5px 5px', md: '0 40px 40px' },
            width: '100%'
        }}>
            <BarLoader loading={loading} />
            {!loading && (
                <form encType="multipart/form-data" style={{ width: '100%' }}>
                    <Grid
                        container
                        sx={{
                            position: 'sticky',
                            top: { xs: '140px', sm: '140px', md: '164px', lg: '164px' },
                            zIndex: 101,
                            backgroundColor: 'white',
                            borderBottom: '2px solid #E0E0E0',
                            padding: '10px 0',
                            display: "flex"
                        }}
                    >
                        {/* Header Row */}
                        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{
                                    width: '20px',
                                    marginLeft: '20px',
                                    backgroundColor: 'green'
                                }}
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <ModuleName moduleName="Proposal" pageName={"Roll Over"} />
                            </Box>
                        </Grid>

                        <Grid item xs={4} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                                {!isPolicyCompleted &&
                                    <IconButton
                                        onClick={handleSave}
                                        sx={{ color: 'green', mx: 0.5 }}
                                    >
                                        <Save />
                                    </IconButton>
                                }
                                <IconButton
                                    onClick={handleCancel}
                                    sx={{ color: 'red', mx: 0.5 }}
                                >
                                    <Cancel />
                                </IconButton>
                            </Box>
                            <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
                                {!isPolicyCompleted &&
                                    <Button
                                        onClick={handleSave}
                                        variant="outlined"
                                        size="large"
                                        sx={{
                                            width: '100%',
                                            mx: 0.5,
                                            color: 'green',
                                            borderColor: 'green',
                                            textTransform: 'none',
                                            textWrap: 'nowrap'
                                        }}
                                    >
                                        Save
                                    </Button>
                                }
                                <Button
                                    onClick={handleCancel}
                                    variant="outlined"
                                    size="small"
                                    sx={{
                                        maxWidth: '100px',
                                        width: '100%',
                                        mx: 1.5,
                                        color: 'red',
                                        borderColor: 'red',
                                        textTransform: 'none'
                                    }}
                                >
                                    Cancel
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>

                    {/* Proposal Type */}
                    {!id && (
                        <Box sx={{
                            width: '100%',
                            paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                            paddingInline: { xs: '0.5rem', sm: '1rem' },
                            marginTop: { xs: 2, sm: 3 }
                        }}>
                            {/* Section Header */}

                            <Box sx={{
                                width: '100%',
                                padding: { xs: "10px", sm: "15px" },
                                borderRadius: "4px",
                            }}>
                                <Typography
                                    sx={{
                                        fontSize: '18px',
                                        fontWeight: "700",
                                        color: '#4C5157',
                                    }}
                                >
                                    Proposal Type
                                </Typography>
                            </Box>
                            <Divider />

                            {/* Radio Buttons */}
                            <Box sx={{
                                width: '100%',
                                padding: { xs: '1rem', sm: '1.5rem' }
                            }}>
                                <FormControl component="fieldset" sx={{ width: '100%' }}>
                                    <RadioGroup
                                        row
                                        aria-label="proposal-type"
                                        name="proposal-type"
                                        value={proposalType}
                                        sx={{
                                            display: 'flex',
                                            flexDirection: { xs: 'column', sm: 'row' },
                                            gap: { xs: '0.5rem', sm: '1rem' },
                                            '& .MuiFormControlLabel-root': {
                                                flex: { xs: '1 1 100%', sm: '1 1 auto' }
                                            }
                                        }}
                                    >
                                        <FormControlLabel
                                            value="New"
                                            control={<Radio sx={{
                                                color: '#528A7E',
                                                '&.Mui-checked': { color: '#528A7E' }
                                            }} />}
                                            onClick={() => handleProposalTypeChange('New')}
                                            label="New"
                                        />
                                        <FormControlLabel
                                            value="Renewal"
                                            control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                            label="Renewal"
                                            onClick={() => handleProposalTypeChange('Renewal')}
                                        />
                                        <FormControlLabel
                                            value="Roll Over"
                                            control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                            label="Roll Over"
                                            onClick={() => handleProposalTypeChange('Roll Over')}

                                        />
                                        <FormControlLabel
                                            value="Migration"
                                            control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                            label="Migration"
                                            onClick={() => handleProposalTypeChange('Migration')}
                                        />
                                    </RadioGroup>
                                </FormControl>
                            </Box>
                        </Box>
                    )}

                    {/* Personal Details */}

                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Renewal Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        {!id && (
                            <Grid container spacing={2} sx={{
                                width: '100%',
                                padding: { xs: '1rem', sm: '1.5rem' },
                                '& .MuiGrid-item': {
                                    width: '100%'
                                }
                            }}>

                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        label="Policy Number"
                                        name="policy_number"
                                        value={formData.policy_number}
                                        onChange={handleChange}
                                        fullWidth
                                        error={Boolean(formErrors.policy_number)}
                                        helperText={formErrors.policy_number}
                                        isRequired
                                    />
                                </Grid>

                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <Button
                                        variant="contained"
                                        onClick={handlePolicySearch}
                                        disabled={isSearching}
                                        fullWidth

                                        sx={{
                                            height: '56px',
                                            backgroundColor: '#528A7E',
                                            '&:hover': {
                                                backgroundColor: '#397367'
                                            }
                                        }}
                                    >
                                        {isSearching ? "Searching..." : "Search Policy"}
                                    </Button>
                                </Grid>

                                {allowManualEntry && (
                                    <>

                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <AutocompleteDropdown
                                                label="Customer Name"
                                                name="customer_id"
                                                options={Array.isArray(customerDetails) ? customerDetails.map((customer) => ({
                                                    value: customer.id,
                                                    label: `${customer.first_name} ${customer.middle_name ? customer.middle_name + ' ' : ''}${customer.last_name} | Mobile: ${customer.mobile} | Aadhar: ${customer.aadhar_number}`
                                                })) : []}
                                                value={formData.customer_id || ''}
                                                onChange={handleAutocompleteDropdownChange}
                                                fullWidth
                                                helperText={formErrors.customer_id}
                                                error={Boolean(formErrors.customer_id)}

                                            />
                                            <Typography
                                                sx={{
                                                    color: '#528A7E',
                                                    mt: 2,
                                                    mb: 1,
                                                    fontSize: '0.875rem',
                                                    fontStyle: 'italic'
                                                }}
                                            >
                                                No policy found. Please select a customer and enter the renewal details below.
                                            </Typography>
                                        </Grid>
                                        {!formData.customer_id && (
                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                <Button
                                                    variant="contained"
                                                    onClick={() => {
                                                        navigate('/dashboard/customer-personal-information')
                                                    }}
                                                    fullWidth
                                                    sx={{
                                                        height: '56px',
                                                        backgroundColor: '#528A7E',
                                                        '&:hover': {
                                                            backgroundColor: '#397367'
                                                        }
                                                    }}
                                                >
                                                    Add Customer
                                                </Button>

                                            </Grid>
                                        )}
                                    </>
                                )}
                            </Grid>
                        )}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>

                            {/* <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label="Previous Insurance Company"
                                    name="previous_insurance_company"
                                    value={formData.previous_insurance_company}
                                    onChange={handleChange}
                                    fullWidth
                                    isRequired
                                    disabled={!allowManualEntry || id}
                                    helperText={formErrors.previous_insurance_company}
                                />
                            </Grid> */}
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Autocomplete
                                    options={insuranceCompanies.map(company => company.insurance_company_name)}
                                    value={formData.previous_insurance_company || ''}
                                    onChange={(event, newValue) => {
                                        setFormData(prev => ({
                                            ...prev,
                                            previous_insurance_company: newValue
                                        }));
                                        // Clear error when value is selected
                                        if (newValue) {
                                            setFormErrors(prev => ({
                                                ...prev,
                                                previous_insurance_company: ''
                                            }));
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label="Previous Insurance Company"
                                            error={Boolean(formErrors.previous_insurance_company)}
                                            helperText={formErrors.previous_insurance_company}
                                            required
                                            fullWidth
                                            sx={{
                                                '& input': {
                                                    textTransform: 'uppercase'
                                                }
                                            }}
                                        />
                                    )}
                                    freeSolo
                                    autoSelect
                                    disabled={!allowManualEntry || id}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label="Product Type"
                                    name="previous_product_type"
                                    value={formData.previous_product_type}
                                    onChange={handleChange}
                                    fullWidth
                                    isRequired
                                    disabled={!allowManualEntry}
                                    helperText={formErrors.previous_product_type}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label="Product Name"
                                    name="previous_product_name"
                                    value={formData.previous_product_name}
                                    onChange={handleChange}
                                    fullWidth
                                    isRequired
                                    disabled={!allowManualEntry}
                                    helperText={formErrors.previous_product_name}
                                />
                            </Grid>


                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label="Sub Product Name"
                                    name="previous_sub_product_name"
                                    value={formData.previous_sub_product_name}
                                    onChange={handleChange}
                                    fullWidth
                                    isRequired
                                    disabled={!allowManualEntry}
                                    helperText={formErrors.previous_sub_product_name}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label="Member Type"
                                    name="previous_member_type"
                                    value={formData.previous_member_type}
                                    onChange={handleChange}
                                    fullWidth
                                    isRequired
                                    disabled={!allowManualEntry}
                                    helperText={formErrors.previous_member_type}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Portability Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Insurance Company"
                                    name="insurance_company_name"
                                    value={formData.insurance_company_name}
                                    options={Array.isArray(insuranceCompanies) ? insuranceCompanies.map(company => ({
                                        value: company?.id,
                                        label: company?.insurance_company_name
                                    })) : []}
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    disabled={id}
                                    error={Boolean(formErrors.insurance_company_name)}
                                    helperText={formErrors.insurance_company_name}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Insurance Company Branch"
                                    name="insurance_company_branch"
                                    options={insuranceBranches?.map(branch => ({
                                        label: branch.insurance_co_branch_name,
                                        value: branch.id
                                    }))}
                                    onChange={({ name, value }) => {
                                        const selectedBranch = insuranceBranches.find(branch => branch.id === value);
                                        setFormData(prev => ({
                                            ...prev,
                                            [name]: value,
                                            insurance_branch: value, // Save the ID here
                                            previous_insurance_company_branch: selectedBranch?.insurance_co_branch_name || ''
                                        }));
                                        setFormErrors(prev => ({
                                            ...prev,
                                            [name]: ''
                                        }));
                                    }}
                                    value={formData.insurance_company_branch}
                                    fullWidth
                                    disabled={id}
                                    required
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Product Type"
                                    name="product_type"
                                    value={formData.product_type}
                                    options={Array.isArray(mainProducts) ? mainProducts.map(product => ({
                                        value: product?.id,
                                        label: product?.main_product
                                    })) : []}
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    disabled={!allowManualEntry}
                                    error={Boolean(formErrors.product_type)}
                                    helperText={formErrors.product_type}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Product Name"
                                    name="product_name"
                                    value={formData.product_name}
                                    // options={Array.isArray(masterProducts) ? masterProducts.map(product => ({
                                    //     value: product?.id,
                                    //     label: product?.product_name
                                    // })) : []}
                                    options={Array.isArray(masterProducts) ? masterProducts
                                        .filter(product => !product.product_name.includes('FG ACCIDENT SURAKSHA'))
                                        .map(product => ({
                                            value: product?.id,
                                            label: product?.product_name
                                        })) : []}
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    disabled={!formData.product_type || id}
                                    error={Boolean(formErrors.product_name)}
                                    helperText={formErrors.product_name}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Sub Product Name"
                                    name="sub_product_name"
                                    value={formData.sub_product_name}
                                    options={Array.isArray(subProducts) ? subProducts.map(product => ({
                                        value: product?.id,
                                        label: product?.sub_product_name
                                    })) : []}
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    disabled={!formData.product_name || id}
                                    error={Boolean(formErrors.sub_product_name)}
                                    helperText={formErrors.sub_product_name}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Member Type"
                                    name="member_type"
                                    value={formData.member_type}
                                    options={[
                                        { label: 'INDIVIDUAL', value: 'INDIVIDUAL' },
                                        { label: 'FAMILY FLOATER', value: 'FAMILY FLOATER' }
                                    ]}
                                    fullWidth
                                    required
                                    disabled={id}
                                    onChange={handleChange}
                                    error={Boolean(formErrors.member_type)}
                                    helperText={formErrors.member_type}
                                />
                            </Grid>


                        </Grid>
                    </Box>
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                PoSP Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label='Agent Code'
                                    name="agent_code"
                                    value={formData.agent_code}
                                    options={Array.isArray(agents) ? agents.map(agent => ({
                                        value: agent.id,
                                        label: `${agent.agent_id} (${agent.full_name})`
                                    })) : []}
                                    onChange={(e) => {
                                        const selectedAgent = agents.find(agent => agent.id === e.target.value);
                                        setFormData(prev => ({
                                            ...prev,
                                            agent_code: e.target.value,
                                            branch_name: selectedAgent?.branch_name || '',
                                            imf_branch: selectedAgent?.branch_id || null // Set branch_id regardless of manual entry mode
                                        }));
                                    }}
                                    fullWidth
                                    required
                                    disabled={!allowManualEntry}
                                    error={Boolean(formErrors.agent_code)}
                                    helperText={formErrors.agent_code}
                                />
                            </Grid>
                            {/* <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label='Agent Name'
                                    name="agent_name"
                                    onChange={handleChange}
                                    value={formData.agent_name}
                                    fullWidth
                                    disabled={!allowManualEntry}
                                />
                            </Grid> */}
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label='IMF Branch Name'
                                    name='branch_name'
                                    value={agents.find(agent => agent.id === formData.agent_code)?.branch_name || ''}
                                    fullWidth
                                    isRequired
                                    disabled={!allowManualEntry}
                                    error={Boolean(formErrors.branch_name)}
                                    helperText={formErrors.branch_name}
                                />
                            </Grid>
                            {/* <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label='Branch Name'
                                    name='branch_name'
                                    value={formData.branch_name}
                                    options={Array.isArray(agents) ?
                                        [...new Set(agents.map(agent => agent.branch_name))]
                                            .filter(Boolean)
                                            .map(branch => ({
                                                value: branch,
                                                label: branch.toUpperCase()
                                            }))
                                        : []
                                    }
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    disabled={!allowManualEntry}
                                    error={Boolean(formErrors.branch_name)}
                                    helperText={formErrors.branch_name}
                                />
                            </Grid> */}
                        </Grid>
                    </Box>

                    {/* Customer Details Section */}
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Proposer Details
                            </Typography>
                        </Box>
                        <Divider />
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Salutation"
                                    name="salutation"
                                    value={formData.salutation}
                                    options={Array.isArray(salutationOptions) ? salutationOptions.map(salutation => ({
                                        value: salutation.id,
                                        label: salutation.label_name
                                    })) : []}
                                    onChange={handleChange}
                                    disabled
                                    fullWidth
                                    required
                                    error={Boolean(formErrors.salutation)}
                                    helperText={formErrors.salutation}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="first_name"
                                    label="First Name"
                                    value={formData.first_name}
                                    fullWidth
                                    disabled
                                    isRequired
                                    error={Boolean(formErrors.first_name)}
                                    helperText={formErrors.first_name}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="last_name"
                                    label="Last Name"
                                    value={formData.last_name}
                                    fullWidth
                                    disabled
                                    isRequired
                                    error={Boolean(formErrors.last_name)}
                                    helperText={formErrors.last_name}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <FormControl fullWidth>
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            label="Date of Birth"
                                            value={formData.date_of_birth}
                                            format="DD/MM/YYYY"
                                            disabled
                                            error={Boolean(formErrors.date_of_birth)}
                                            helperText={formErrors.date_of_birth}
                                        />
                                    </LocalizationProvider>
                                </FormControl>
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Gender"
                                    name="gender"
                                    value={formData.gender}
                                    options={Array.isArray(genderOptions) ? genderOptions.map(gender => ({
                                        value: gender?.id,
                                        label: gender?.label_name
                                    })) : []}
                                    disabled
                                    fullWidth
                                    required
                                    error={Boolean(formErrors.gender)}
                                    helperText={formErrors.gender}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Marital Status"
                                    name="marital_status"
                                    value={formData.marital_status}
                                    options={maritalStatusOptions?.map(option => ({
                                        label: option.label_name,
                                        value: option.id
                                    }))}
                                    fullWidth
                                    disabled
                                    required
                                    error={Boolean(formErrors.marital_status)}
                                    helperText={formErrors.marital_status}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Occupation"
                                    name="occupation"
                                    value={formData.occupation}
                                    options={Array.isArray(OccupationOptions) ? OccupationOptions.map(occupation => ({
                                        value: occupation?.id,
                                        label: occupation?.label_name
                                    })) : []}
                                    fullWidth
                                    disabled
                                    required
                                    error={Boolean(formErrors.occupation)}
                                    helperText={formErrors.occupation}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="aadhar_number"
                                    label="Aadhar Number"
                                    value={formData.aadhar_number}
                                    fullWidth
                                    disabled
                                    isRequired
                                    error={Boolean(formErrors.aadhar_number)}
                                    helperText={formErrors.aadhar_number}
                                />
                            </Grid>
                        </Grid>
                    </Box>

                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Member Details
                            </Typography>
                            {!isPolicyCompleted &&
                                <Button
                                    variant="outlined"
                                    onClick={handleMemberDialogOpen}
                                    sx={{
                                        color: '#528A7E',
                                        borderColor: '#528A7E'
                                    }}
                                >
                                    Select Members
                                </Button>
                            }
                        </Box>
                        <Divider />
                        {members.map((member, index) => (
                            <Card
                                key={index}
                                sx={{
                                    mb: 2,
                                    width: '100%',
                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                    backgroundColor: '#f5f5f5', // Light gray background
                                }}>
                                <CardHeader
                                    title={`Member ${index + 1}`}
                                    action={
                                        !isPolicyCompleted && members.length > 1 &&
                                        <IconButton
                                            onClick={() => handleRemoveMember(index)}
                                        >
                                            <DeleteIcon sx={{ color: 'red' }} />
                                        </IconButton>

                                    }
                                    sx={{
                                        borderBottom: '2px solid #528A7E',
                                        backgroundColor: '#fff',
                                        '& .MuiCardHeader-title': {
                                            fontSize: '1rem',
                                            color: '#4C5157',
                                            fontWeight: 'normal'
                                        }
                                    }}
                                />
                                <CardContent>
                                    <Grid container spacing={2} sx={{
                                        width: '100%',
                                        '& .MuiGrid-item': {
                                            width: '100%'
                                        }
                                    }}>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <CustomTextField
                                                name="first_name"
                                                label="First Name"
                                                value={member?.first_name || ''}
                                                fullWidth
                                                disabled
                                                isRequired
                                                error={Boolean(memberErrors[index]?.first_name)}
                                                helperText={memberErrors[index]?.first_name}
                                            />

                                        </Grid>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <CustomTextField
                                                name="middle_name"
                                                label="Middle Name"
                                                value={member?.middle_name || ''}
                                                fullWidth
                                                isRequired
                                                disabled
                                                error={Boolean(memberErrors[index]?.middle_name)}
                                                helperText={memberErrors[index]?.middle_name}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <CustomTextField
                                                name="last_name"
                                                label="Last Name"
                                                value={member?.last_name || ''}
                                                fullWidth
                                                isRequired
                                                disabled
                                                error={Boolean(memberErrors[index]?.last_name)}
                                                helperText={memberErrors[index]?.last_name}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <FormControl fullWidth required>
                                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                    <DatePicker
                                                        label="DOB"
                                                        value={member?.dob ? dayjs(member.dob, 'DD/MM/YYYY') : null}
                                                        // value={member?.dob ? dayjs(member.dob, 'DD/MM/YYYY') : null}
                                                        //  onChange={(date) => handleMemberChange(index, 'dob', date)}
                                                        maxDate={minAgeDate}
                                                        format="DD/MM/YYYY"
                                                        disabled
                                                        helperText={memberErrors[index]?.dob}
                                                        slotProps={{
                                                            textField: {
                                                                fullWidth: true,
                                                                sx: {
                                                                    '& .MuiOutlinedInput-root': {
                                                                        '&::before': {
                                                                            content: '""',
                                                                            position: 'absolute',
                                                                            left: 0,
                                                                            top: 0,
                                                                            bottom: 0,
                                                                            width: '3px',
                                                                            backgroundColor: 'red',
                                                                            zIndex: 1,
                                                                        }
                                                                    },
                                                                }
                                                            }
                                                        }}
                                                    />
                                                </LocalizationProvider>
                                            </FormControl>
                                        </Grid>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <Dropdown
                                                label="Relation"
                                                name="relation"
                                                options={relationOptions?.map(option => ({
                                                    label: option.label_name,
                                                    value: option.id
                                                }))}
                                                value={member?.relation || ''}

                                                onChange={handleChange}
                                                fullWidth
                                                helperText={memberErrors[index]?.relation_id}
                                                required
                                                disabled
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <Dropdown
                                                label="Marital Status"
                                                name="marital_status"
                                                options={maritalStatusOptions?.map(option => ({
                                                    label: option.label_name,
                                                    value: option.id
                                                }))}
                                                value={member?.marital_status || ''}

                                                onChange={(e) => handleMemberChange(index, 'marital_status', e.target.value)}
                                                fullWidth
                                                helperText={memberErrors[index]?.marital_status}
                                                required
                                                disabled
                                            />
                                        </Grid>
                                        {
                                            maritalStatusOptions?.find(option => option.id === member.marital_status)?.label_name === 'Married' && (
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <FormControl fullWidth required>
                                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                            <DatePicker
                                                                label="Marriage Date"
                                                                value={member?.marriage_date ? dayjs(member.marriage_date, 'DD/MM/YYYY') : null}
                                                                //  onChange={(date) => handleMemberChange(index, 'marriage_date', date)}
                                                                disabled
                                                                format="DD/MM/YYYY"
                                                                helperText={memberErrors[index]?.marriage_date}
                                                                slotProps={{
                                                                    textField: {
                                                                        fullWidth: true,
                                                                        sx: {
                                                                            '& .MuiOutlinedInput-root': {
                                                                                '&::before': {
                                                                                    content: '""',
                                                                                    position: 'absolute',
                                                                                    left: 0,
                                                                                    top: 0,
                                                                                    bottom: 0,
                                                                                    width: '3px',
                                                                                    backgroundColor: 'red',
                                                                                    zIndex: 1,
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }}
                                                            />
                                                        </LocalizationProvider>
                                                    </FormControl>
                                                </Grid>
                                            )}
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <CustomTextField
                                                name="mobile"
                                                label="Mobile Number"
                                                value={member?.mobile || ''} // Add null check with default empty string
                                                onChange={handleChange}
                                                fullWidth
                                                helperText={memberErrors[index]?.mobile}
                                                disabled
                                                isRequired
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <CustomTextField
                                                name="email_id"
                                                label="Email Id"
                                                value={member.email_id || ''}
                                                onChange={handleChange}
                                                fullWidth
                                                helperText={memberErrors[index]?.email_id}
                                                disabled
                                                isRequired
                                            />
                                        </Grid>
                                        {/* Show sum insured only for individual member type */}
                                        {
                                            (formData.member_type === 'INDIVIDUAL') ?
                                                (
                                                    masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') ? (
                                                        <>
                                                            {/* <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <AutocompleteDropdown
                                                                    label='Occupation'
                                                                    value={member.occupation}
                                                                    options={Array.isArray(OccupationOptions) ? OccupationOptions.map(occupation => ({
                                                                        value: occupation?.id,
                                                                        label: occupation?.label_name
                                                                    })) : []}
                                                                    fullWidth
                                                                //    disabled
                                                                />
                                                            </Grid> */}
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <CustomTextField
                                                                    name="annual_income"
                                                                    label="Annual Income"
                                                                    value={member?.annual_income}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                                                        handleMemberChange(index, 'add_sum_insured', {
                                                                            name: 'annual_income',
                                                                            value: value
                                                                        });
                                                                    }}
                                                                    fullWidth
                                                                    isRequired
                                                                    //   helperText={memberErrors[index]?.annual_income}
                                                                    //  disabled={id}
                                                                    inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <CustomTextField
                                                                    name="ad_sum_insured"
                                                                    label="Sum Insured - Accidental Death"
                                                                    value={member?.ad_sum_insured}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                                                        handleMemberChange(index, 'add_sum_insured', {
                                                                            name: 'ad_sum_insured',
                                                                            value: value
                                                                        });
                                                                    }}
                                                                    fullWidth
                                                                    isRequired
                                                                    //      helperText={memberErrors[index]?.ad_sum_insured}
                                                                    //  disabled={id}
                                                                    inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <CustomTextField
                                                                    name="pt_sum_insured"
                                                                    label="Sum Insured - Permanent Total Disability"
                                                                    value={member?.pt_sum_insured}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                                                        handleMemberChange(index, 'add_sum_insured', {
                                                                            name: 'pt_sum_insured',
                                                                            value: value
                                                                        });
                                                                    }}
                                                                    fullWidth
                                                                    isRequired
                                                                    //    helperText={memberErrors[index]?.pt_sum_insured}
                                                                    // disabled={id}
                                                                    inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <CustomTextField
                                                                    name="pp_sum_insured"
                                                                    label="Sum Insured - Permanent Partial Disability"
                                                                    value={member?.pp_sum_insured}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                                                        handleMemberChange(index, 'add_sum_insured', {
                                                                            name: 'pp_sum_insured',
                                                                            value: value
                                                                        });
                                                                    }}
                                                                    fullWidth
                                                                    isRequired
                                                                    //    helperText={memberErrors[index]?.pp_sum_insured}
                                                                    // disabled={id}
                                                                    inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <CustomTextField
                                                                    name="tt_sum_insured"
                                                                    label="Sum Insured - Temporary Total Disability"
                                                                    value={member?.tt_sum_insured}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                                                        handleMemberChange(index, 'add_sum_insured', {
                                                                            name: 'tt_sum_insured',
                                                                            value: value
                                                                        });
                                                                    }}
                                                                    fullWidth
                                                                    isRequired
                                                                    //   helperText={memberErrors[index]?.tt_sum_insured}
                                                                    //  disabled={id}
                                                                    inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                />
                                                            </Grid>

                                                            {(Boolean(!id) || Boolean(member?.lp_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="lp_sum_insured"
                                                                        label="Sum Insured - Loan Protector"
                                                                        value={member?.lp_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'lp_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //   helperText={memberErrors[index]?.lp_sum_insured}
                                                                        //    disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.ls_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="ls_sum_insured"
                                                                        label="Sum Insured - Life Support Benefits"
                                                                        value={member?.ls_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'ls_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //   helperText={memberErrors[index]?.ls_sum_insured}
                                                                        // disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.me_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="me_sum_insured"
                                                                        label="Sum Insured - Accident Hospitalisation"
                                                                        value={member?.me_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'me_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        isRequired
                                                                        fullWidth
                                                                        //  helperText={memberErrors[index]?.me_sum_insured}
                                                                        //    disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.am_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="am_sum_insured"
                                                                        label="Sum Insured - Accident Medical Expenses"
                                                                        value={member?.am_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'am_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //   helperText={memberErrors[index]?.am_sum_insured}
                                                                        //    disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.bb_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="bb_sum_insured"
                                                                        label="Sum Insured - Broken Bones"
                                                                        value={member?.bb_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'bb_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //   helperText={memberErrors[index]?.bb_sum_insured}
                                                                        //   disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.rf_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="rf_sum_insured"
                                                                        label="Sum Insured - Repatration And Funeral Expenses"
                                                                        value={member?.rf_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'rf_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //  helperText={memberErrors[index]?.rf_sum_insured}
                                                                        //   disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.aa_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="aa_sum_insured"
                                                                        label="Sum Insured - Adaptation Allowance"
                                                                        value={member?.aa_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'aa_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //  helperText={memberErrors[index]?.aa_sum_insured}
                                                                        //  disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.cs_sum_insured)) &&

                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="cs_sum_insured"
                                                                        label="Sum Insured - Child Education Support"
                                                                        value={member?.cs_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'cs_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //  helperText={memberErrors[index]?.cs_sum_insured}
                                                                        //  disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.hc_sum_insured)) &&
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="hc_sum_insured"
                                                                        label="Sum Insured - Hospital Cash Allowance"
                                                                        value={member?.hc_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'hc_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //  helperText={memberErrors[index]?.hc_sum_insured}
                                                                        //  disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                            {(Boolean(!id) || Boolean(member?.ft_sum_insured)) &&

                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <CustomTextField
                                                                        name="ft_sum_insured"
                                                                        label="Sum Insured - Family Transport Allowance"
                                                                        value={member?.ft_sum_insured}
                                                                        onChange={(e) => {
                                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                                            handleMemberChange(index, 'add_sum_insured', {
                                                                                name: 'ft_sum_insured',
                                                                                value: value
                                                                            });
                                                                        }}
                                                                        fullWidth
                                                                        isRequired
                                                                        //   helperText={memberErrors[index]?.ft_sum_insured}
                                                                        // disabled={id}
                                                                        inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                    />
                                                                </Grid>}
                                                        </>
                                                    ) : (
                                                        <>
                                                            {masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('advantage top up') && (
                                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                    <Dropdown
                                                                        label="Deductible"
                                                                        name="deductible"
                                                                        options={deductileOptions}
                                                                        value={member?.deductible || ''}
                                                                        prefixSymbol={'₹'}
                                                                        onChange={(e) => handleMemberChange(index, 'deductible', e.target.value)}
                                                                        fullWidth
                                                                        helperText={memberErrors[index]?.deductible}

                                                                        required
                                                                    // disabled={id}
                                                                    />
                                                                </Grid>
                                                            )}
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <Dropdown
                                                                    name="sum_insured"
                                                                    label="Sum Insured"
                                                                    value={member?.sum_insured || ''}
                                                                    prefixSymbol={'₹'}
                                                                    options={sumInsuredOptions}
                                                                    // prefixSymbol={'₹'}
                                                                    // options={sumInsuredOptions}
                                                                    onChange={(e) => handleMemberChange(index, 'sum_insured', e.target.value)}
                                                                    fullWidth
                                                                    required
                                                                    disabled={id}
                                                                    error={Boolean(memberErrors[index]?.sum_insured)}
                                                                    helperText={memberErrors[index]?.sum_insured}
                                                                />
                                                            </Grid>
                                                        </>

                                                    )
                                                ) : null}
                                    </Grid>
                                    {/* Nominee Details Card */}
                                    <Card sx={{
                                        mt: 2,
                                        backgroundColor: '#f8f9fa',
                                        boxShadow: '0 4px 8px rgba(82, 138, 126, 0.15)',
                                        border: '1px solid #528A7E',
                                        borderRadius: '8px',
                                        position: 'relative',
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            height: '4px',
                                            backgroundColor: '#528A7E',
                                            borderRadius: '8px 8px 0 0'
                                        }
                                    }}>
                                        <CardHeader
                                            title="Nominee Details"
                                            sx={{
                                                backgroundColor: 'rgba(82, 138, 126, 0.08)',
                                                borderBottom: '1px solid #528A7E',
                                                '& .MuiCardHeader-title': {
                                                    fontSize: '1.1rem',
                                                    color: '#528A7E',
                                                    fontWeight: '600',
                                                    letterSpacing: '0.5px'
                                                }
                                            }}
                                        />
                                        <CardContent sx={{ backgroundColor: 'white' }}>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <CustomTextField
                                                        name="nominee_name"
                                                        label="Nominee Name"
                                                        value={member?.nominee?.name || ''}
                                                        onChange={(e) => handleMemberChange(index, 'nominee', {
                                                            name: e.target.value
                                                        })}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.nominee_name}
                                                        isRequired
                                                        disabled={id}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <Dropdown
                                                        label="Gender"
                                                        name="nominee_gender"
                                                        options={genderOptions?.map(option => ({
                                                            label: option.label_name,
                                                            value: option.id
                                                        }))}
                                                        value={member?.nominee?.gender || ''}
                                                        onChange={(e) => handleMemberChange(index, 'nominee', {
                                                            gender: e.target.value
                                                        })}
                                                        fullWidth
                                                        required
                                                        helperText={memberErrors[index]?.nominee_gender}
                                                        disabled={id}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <FormControl fullWidth required>
                                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                            <DatePicker
                                                                label="Date of Birth"
                                                                disabled={id}
                                                                value={member?.nominee?.dob ? dayjs(member.nominee.dob) : null}
                                                                onChange={(date) => {
                                                                    handleMemberChange(index, 'nominee', {
                                                                        dob: date
                                                                    });
                                                                }}
                                                                format="DD/MM/YYYY"
                                                                helperText={memberErrors[index]?.nominee_dob}
                                                                slotProps={{
                                                                    textField: {
                                                                        fullWidth: true,
                                                                        required: true,
                                                                        error: Boolean(memberErrors[index]?.nominee_dob),
                                                                        helperText: memberErrors[index]?.nominee_dob,

                                                                        sx: {
                                                                            '& .MuiOutlinedInput-root': {
                                                                                '&::before': {
                                                                                    content: '""',
                                                                                    position: 'absolute',
                                                                                    left: 0,
                                                                                    top: 0,
                                                                                    bottom: 0,
                                                                                    width: '3px',
                                                                                    backgroundColor: 'red',
                                                                                    zIndex: 1,
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                }}
                                                            />
                                                        </LocalizationProvider>
                                                    </FormControl>
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <Dropdown
                                                        label="Relationship"
                                                        name="nominee_relation"
                                                        options={getFilteredRelations().map(relation => ({
                                                            label: relation.label_name,
                                                            value: relation.id
                                                        }))}
                                                        value={member?.nominee?.relation || ''}
                                                        onChange={(e) => handleMemberChange(index, 'nominee', {
                                                            relation: e.target.value
                                                        })}
                                                        fullWidth
                                                        required
                                                        helperText={memberErrors[index]?.nominee_relation}
                                                        disabled={id}
                                                    />
                                                </Grid>
                                            </Grid>
                                        </CardContent>
                                    </Card>
                                    {/* Appointee Section - Only show if nominee is under 18 */}
                                    {member?.nominee?.dob && dayjs().diff(dayjs(member.nominee.dob), 'years') < 18 && (
                                        <Card sx={{
                                            mt: 2,
                                            backgroundColor: '#f8f9fa',
                                            boxShadow: '0 4px 8px rgba(82, 138, 126, 0.15)',
                                            border: '1px solid #528A7E',
                                            borderRadius: '8px',
                                            position: 'relative',
                                            '&::before': {
                                                content: '""',
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                right: 0,
                                                height: '4px',
                                                backgroundColor: '#528A7E',
                                                borderRadius: '8px 8px 0 0'
                                            }
                                        }}>
                                            <CardHeader
                                                title="Appointee Details"
                                                sx={{
                                                    backgroundColor: 'rgba(82, 138, 126, 0.08)',
                                                    borderBottom: '1px solid #528A7E',
                                                    '& .MuiCardHeader-title': {
                                                        fontSize: '1.1rem',
                                                        color: '#528A7E',
                                                        fontWeight: '600',
                                                        letterSpacing: '0.5px'
                                                    }
                                                }}
                                            />
                                            <CardContent sx={{ backgroundColor: 'white' }}>
                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                        <CustomTextField
                                                            name="appointee_name"
                                                            label="Appointee Name"
                                                            value={member?.appointee?.name || ''}
                                                            onChange={(e) => handleMemberChange(index, 'appointee', {
                                                                name: e.target.value
                                                            })}
                                                            fullWidth
                                                            helperText={memberErrors[index]?.appointee_name}
                                                            isRequired
                                                            disabled={id}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                        <Dropdown
                                                            label="Gender"
                                                            name="appointee_gender"
                                                            options={genderOptions?.map(option => ({
                                                                label: option.label_name,
                                                                value: option.id
                                                            }))}
                                                            value={member?.appointee?.gender || ''}
                                                            onChange={(e) => handleMemberChange(index, 'appointee', {
                                                                gender: e.target.value
                                                            })}
                                                            fullWidth
                                                            required
                                                            helperText={memberErrors[index]?.appointee_gender}
                                                            disabled={id}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                        <FormControl fullWidth required>
                                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                                <DatePicker
                                                                    label="Date of Birth"
                                                                    value={member?.appointee?.dob ? dayjs(member.appointee.dob) : null}
                                                                    onChange={(date) => {
                                                                        handleMemberChange(index, 'appointee', {
                                                                            dob: date
                                                                        });
                                                                    }}
                                                                    format="DD/MM/YYYY"
                                                                    helperText={memberErrors[index]?.appointee_dob}
                                                                    maxDate={dayjs().subtract(18, 'years')}
                                                                    disabled={id}
                                                                    slotProps={{
                                                                        textField: {
                                                                            fullWidth: true,
                                                                            required: true,
                                                                            error: Boolean(memberErrors[index]?.appointee_dob),
                                                                            helperText: memberErrors[index]?.appointee_dob,
                                                                            // disabled: id && Number(id) !== 0,
                                                                            sx: {
                                                                                '& .MuiOutlinedInput-root': {
                                                                                    '&::before': {
                                                                                        content: '""',
                                                                                        position: 'absolute',
                                                                                        left: 0,
                                                                                        top: 0,
                                                                                        bottom: 0,
                                                                                        width: '3px',
                                                                                        backgroundColor: 'red',
                                                                                        zIndex: 1,
                                                                                    }
                                                                                },
                                                                            }
                                                                        }
                                                                    }}
                                                                />
                                                            </LocalizationProvider>
                                                        </FormControl>
                                                    </Grid>
                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                        <Dropdown
                                                            label="Relationship"
                                                            name="appointee_relation"
                                                            options={getFilteredRelations().map(relation => ({
                                                                label: relation.label_name,
                                                                value: relation.id
                                                            }))}
                                                            value={member?.appointee?.relation || ''}
                                                            onChange={(e) => handleMemberChange(index, 'appointee', {
                                                                relation: e.target.value
                                                            })}
                                                            fullWidth
                                                            required
                                                            helperText={memberErrors[index]?.appointee_relation}
                                                            disabled={id}
                                                        />
                                                    </Grid>
                                                </Grid>
                                            </CardContent>
                                        </Card>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </Box>


                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Premium Details
                            </Typography>

                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            {formData.member_type === 'FAMILY FLOATER' && (
                                <>
                                    {masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('advantage top up') &&
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <Dropdown
                                                label="Deductible"
                                                name="deductible"
                                                options={deductileOptions}
                                                value={members?.[0]?.deductible || ''}
                                                onChange={(e) => {
                                                    setMembers(prevMembers =>
                                                        prevMembers?.map(member => ({
                                                            ...member,
                                                            deductible: e.target.value
                                                        }))
                                                    );
                                                }}
                                                prefixSymbol={'₹'}
                                                fullWidth
                                                helperText={memberErrors?.[0]?.deductible}
                                                required
                                                disabled={id}
                                            />
                                        </Grid>
                                    }
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <Dropdown
                                            name="sum_insured"
                                            label="Sum Insured"
                                            value={members?.[0]?.sum_insured || ''}
                                            prefixSymbol={'₹'}
                                            options={sumInsuredOptions}
                                            onChange={(e) => {
                                                setMembers(prevMembers =>
                                                    prevMembers?.map(member => ({
                                                        ...member,
                                                        sum_insured: e.target.value
                                                    }))
                                                );
                                            }}
                                            fullWidth
                                            helperText={memberErrors?.[0]?.sum_insured}
                                            required
                                            disabled={id}
                                        />
                                    </Grid>
                                </>
                            )}
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="net_premium"
                                    label="Net Premium"
                                    value={formatToTwoDecimals(formData.net_premium)}
                                    prefixSymbol={'₹'}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.net_premium}
                                    isRequired
                                    disabled={id}
                                //  isDisabled={id || selectedQuotation}
                                />
                            </Grid>
                            {/* Add Family Discount Fields */}
                            {(masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') || formData.family_discount_percentage) && (
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="family_discount_percentage"
                                        label="Family Discount %"
                                        value={formData.family_discount_percentage}
                                        prefixSymbol={'%'}
                                        onChange={handleChange}
                                        fullWidth
                                        //  helperText={formErrors.family_discount_percentage}
                                        isRequired
                                    //  isDisabled={id || selectedQuotation}
                                    />
                                </Grid>
                            )}
                            {(formData.family_discount_amount || masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) && (
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="family_discount_amount"
                                        label="Family Discount Amount"
                                        value={formData.family_discount_amount}
                                        prefixSymbol={'₹'}
                                        onChange={handleChange}
                                        fullWidth
                                        //   helperText={formErrors.family_discount_amount}
                                        isRequired
                                    //  isDisabled={id || selectedQuotation}
                                    />
                                </Grid>
                            )}
                            {(masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') || formData.long_term_discount_percentage) && (
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="long_term_discount_percentage"
                                        label="Long Term Discount %"
                                        value={formData.long_term_discount_percentage}
                                        prefixSymbol={'%'}
                                        onChange={handleChange}
                                        fullWidth
                                        //   helperText={formErrors.long_term_discount_percentage}
                                        isRequired
                                    //  isDisabled={id || selectedQuotation}
                                    />
                                </Grid>
                            )}
                            {(masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') || formData.long_term_discount_amount) && (
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="long_term_discount_amount"
                                        label="Long Term Discount Amount"
                                        value={formData.long_term_discount_amount}
                                        prefixSymbol={'₹'}
                                        onChange={handleChange}
                                        fullWidth
                                        //   helperText={formErrors.long_term_discount_amount}
                                        isRequired
                                    //   isDisabled={id || selectedQuotation}
                                    />
                                </Grid>
                            )}

                            {/* Existing GST fields */}
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="gst_percentage"
                                    label="GST Percentage"
                                    value={formData.gst_percentage || 18}
                                    prefixSymbol={'%'}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.gst_percentage}
                                    isRequired
                                    disabled={id}
                                //  isDisabled={id || selectedQuotation}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="gst_amount"
                                    label="GST Amount"
                                    value={formatToTwoDecimals(formData.gst_amount)}
                                    prefixSymbol={'₹'}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.gst_amount}
                                    isDisabled
                                    isRequired
                                />
                            </Grid>
                            {/* Show deductible and sum insured for floater member type */}
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="total_premium"
                                    label="Total Premium"
                                    value={formData.total_premium}
                                    prefixSymbol={'₹'}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.total_premium}
                                    isRequired
                                    disabled={id}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Tenure"
                                    name="tenure"
                                    value={formData.tenure}
                                    onChange={handleChange}
                                    options={Array.isArray(durationOptions) ? durationOptions.map(tenure => ({
                                        value: tenure?.api_name,
                                        label: tenure?.label_name
                                    })) : []}
                                    fullWidth
                                    helperText={formErrors.tenure}
                                    required
                                    disabled={id}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <FormControl fullWidth required >
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            label="Start Date"
                                            name="start_date"
                                            onChange={(date) => {
                                                handleDateChange('start_date', date);
                                                // Clear error when date is selected
                                                if (date) {
                                                    setFormErrors(prev => ({
                                                        ...prev,
                                                        start_date: ''
                                                    }));
                                                }
                                            }}
                                            value={formData.start_date}
                                            format="DD/MM/YYYY"
                                            helperText={formErrors.start_date}
                                            //  isDisabled={isPolicyCompleted === true}
                                            disabled={isPolicyCompleted === true}
                                            error={Boolean(formErrors.start_date)}

                                            slotProps={{
                                                textField: {
                                                    error: Boolean(formErrors.start_date),
                                                    helperText: formErrors.start_date,
                                                    required: Boolean(formData.new_policy_number)
                                                }
                                            }}
                                        />
                                    </LocalizationProvider>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <FormControl fullWidth required>
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            label="End Date"
                                            value={formData.end_date}
                                            format="DD/MM/YYYY"
                                            disabled
                                            slotProps={{
                                                textField: {
                                                    fullWidth: true,
                                                    sx: {
                                                        '& .MuiOutlinedInput-root': {
                                                            '&::before': {
                                                                content: '""',
                                                                position: 'absolute',
                                                                left: 0,
                                                                top: 0,
                                                                bottom: 0,
                                                                width: '3px',
                                                                backgroundColor: 'red',
                                                                zIndex: 1,
                                                            }
                                                        },
                                                    }
                                                }
                                            }}
                                        />
                                    </LocalizationProvider>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <FormControl fullWidth required>

                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            label="Issue Date"
                                            value={formData.issue_date ? dayjs(formData.issue_date) : null}
                                            onChange={(date) => handleDateChange('issue_date', date)}
                                            format="DD/MM/YYYY"


                                            slotProps={{
                                                textField: {
                                                    error: Boolean(formErrors.issue_date),
                                                    helperText: formErrors.issue_date,
                                                    required: Boolean(formData.new_policy_number),
                                                    fullWidth: true,
                                                    sx: {
                                                        '& .MuiOutlinedInput-root': {
                                                            '&::before': {
                                                                content: '""',
                                                                position: 'absolute',
                                                                left: 0,
                                                                top: 0,
                                                                bottom: 0,
                                                                width: '3px',
                                                                backgroundColor: 'red',
                                                                zIndex: 1,
                                                            }
                                                        },
                                                    }
                                                }
                                            }}

                                            // disabled={!allowManualEntry}
                                            disabled={id || !allowManualEntry}
                                            helperText={formErrors.issue_date}
                                            error={Boolean(formErrors.issue_date)}
                                        />
                                    </LocalizationProvider>
                                </FormControl>
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="new_policy_number"
                                    label="Policy Number"
                                    value={formData.new_policy_number}
                                    onChange={handleChange}
                                    fullWidth
                                    isRequired
                                    isDisabled={isPolicyCompleted}
                                    error={Boolean(formErrors.new_policy_number)}
                                    helperText={formErrors.new_policy_number}
                                />

                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomFileUpload
                                    name="policy_pdf"
                                    section_name="Upload Policy Pdf"
                                    accept=".pdf"
                                    label={"Upload Policy Pdf"}
                                    onFileSelect={handleFileSelect}
                                    insertedFile={null}
                                    helperText={formErrors.policy_pdf}
                                    error={Boolean(formErrors.policy_pdf)}
                                    disabled={isPolicyCompleted}
                                    sx={{
                                        '& .MuiFormHelperText-root': {
                                            display: 'block',
                                            visibility: 'visible'
                                        }
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </Box>

                    {/* Payment Details - Only show if proposal details section is complete */}
                    {(formData.net_premium && formData.tenure) && (<Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Payment Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Select Payment Type"
                                    name="payment_type"
                                    options={paymentTypes?.map(type => ({
                                        label: type.label_name,
                                        value: String(type.id)  // Convert id to string
                                    }))}
                                    value={String(formData.payment_type || '')}  // Convert to string and provide default
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    error={Boolean(formErrors.payment_type)}
                                    helperText={formErrors.payment_type}
                                    disabled={id}
                                />
                            </Grid>

                            {/* Cash Fields */}
                            {hasPaymentType(['PAY_CASH', 'PAY_CHEQUE_CASH', 'PAY_DD_CASH']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="cash_amount"
                                            label="Cash Amount"
                                            value={formData.cash_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            error={Boolean(formErrors.cash_amount)}
                                            helperText={formErrors.cash_amount}
                                            isDisabled={id}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="Received Date"
                                                    value={formData.received_date ? dayjs(formData.received_date) : null}
                                                    onChange={(date) => handleDateChange('received_date', date)}
                                                    // maxDate={dayjs(formData.start_date).subtract(1, 'day')}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(1, 'months')}

                                                    format="DD/MM/YYYY"
                                                    disabled={id}
                                                    slotProps={{
                                                        textField: {
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiInputBase-input.Mui-disabled': {
                                                                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                                                },
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                </>
                            )}

                            {/* Cheque Fields */}
                            {hasPaymentType(['PAY_CHEQUE', 'PAY_CHEQUE_CASH']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="cheque_amount"
                                            label="Cheque Amount"
                                            value={formData.cheque_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            isDisabled={id}
                                            error={Boolean(formErrors.cheque_amount)}
                                            helperText={formErrors.cheque_amount}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="cheque_number"
                                            label="Cheque Number"
                                            value={formData.cheque_number}
                                            onChange={handleChange}
                                            fullWidth
                                            isDisabled={id}
                                            isRequired
                                            error={Boolean(formErrors.cheque_number)}
                                            helperText={formErrors.cheque_number}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="Cheque Date"
                                                    value={formData.cheque_date ? dayjs(formData.cheque_date) : null}
                                                    onChange={(date) => handleDateChange('cheque_date', date)}
                                                    format="DD/MM/YYYY"
                                                    disabled={id}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(90, 'days')}
                                                    slotProps={{
                                                        textField: {
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <Dropdown
                                            label="Bank Name"
                                            name="bank_name"
                                            options={banks.map(bank => ({ label: bank.label_name, value: bank.id }))}
                                            value={formData.bank_name}
                                            onChange={handleChange}
                                            fullWidth
                                            required
                                            disabled={id}
                                            error={Boolean(formErrors.bank_name)}
                                            helperText={formErrors.bank_name}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="bank_branch_name"
                                            label="Branch Name"
                                            value={formData.bank_branch_name}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            isDisabled={id}
                                            error={Boolean(formErrors.bank_branch_name)}
                                            helperText={formErrors.bank_branch_name}
                                        />
                                    </Grid>
                                </>
                            )}

                            {/* DD Fields */}
                            {hasPaymentType(['PAY_DD', 'PAY_DD_CASH']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="dd_amount"
                                            label="DD Amount"
                                            value={formData.dd_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isDisabled={id}
                                            isRequired
                                            error={Boolean(formErrors.dd_amount)}
                                            helperText={formErrors.dd_amount}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="dd_number"
                                            label="DD Number"
                                            value={formData.dd_number}
                                            onChange={handleChange}
                                            fullWidth
                                            isDisabled={id}
                                            isRequired
                                            error={Boolean(formErrors.dd_number)}
                                            helperText={formErrors.dd_number}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="DD Date"
                                                    value={formData.dd_date ? dayjs(formData.dd_date) : null}
                                                    onChange={(date) => handleDateChange('dd_date', date)}
                                                    format="DD/MM/YYYY"
                                                    disabled={id}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(90, 'days')}
                                                    slotProps={{
                                                        textField: {
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <Dropdown
                                            label="Bank Name"
                                            name="bank_name"
                                            options={banks.map(bank => ({ label: bank.label_name, value: bank.id }))}
                                            value={formData.bank_name}
                                            onChange={handleChange}
                                            fullWidth
                                            required
                                            disabled={id}
                                            error={Boolean(formErrors.bank_name)}
                                            helperText={formErrors.bank_name}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="bank_branch_name"
                                            label="Branch Name"
                                            value={formData.bank_branch_name}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            isDisabled={id}
                                            error={Boolean(formErrors.bank_branch_name)}
                                            helperText={formErrors.bank_branch_name}
                                        />
                                    </Grid>
                                </>
                            )}

                            {/* Online Payment Fields */}
                            {hasPaymentType(['PAY_ONLINE']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="online_amount"
                                            label="Amount"
                                            value={formData.online_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isDisabled={id}
                                            isRequired
                                            error={Boolean(formErrors.online_amount)}
                                            helperText={formErrors.online_amount}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="Transaction Date"
                                                    value={formData.transaction_date ? dayjs(formData.transaction_date) : null}
                                                    onChange={(date) => handleDateChange('transaction_date', date)}
                                                    format="DD/MM/YYYY"
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(7, 'days')}

                                                    disabled={id}
                                                    slotProps={{
                                                        textField: {
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <Dropdown
                                            label="Transaction Type"
                                            name="transaction_type"
                                            options={[
                                                { label: 'UPI', value: 'UPI' },
                                                { label: 'Net Banking', value: 'Net Banking' },
                                                { label: 'Credit Card', value: 'Credit Card' },
                                                { label: 'Debit Card', value: 'Debit Card' },
                                                { label: 'Wallet', value: 'Wallet' },
                                                { label: 'Other', value: 'Other' }
                                            ]}
                                            value={formData.transaction_type}
                                            onChange={handleChange}
                                            fullWidth
                                            disabled={id}
                                            required
                                            error={Boolean(formErrors.transaction_type)}
                                            helperText={formErrors.transaction_type}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="receipt_number"
                                            label="Receipt Number"
                                            value={formData.receipt_number}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            error={Boolean(formErrors.receipt_number)}
                                            helperText={formErrors.receipt_number}
                                        />
                                    </Grid>
                                </>
                            )}
                        </Grid>
                    </Box>)}

                    {/* Remarks */}
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Remarks
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid item xs={12}
                            sx={{
                                width: '100%',
                                padding: { xs: '1rem', sm: '1.5rem' },
                                '& .MuiGrid-item': {
                                    width: '100%'
                                }
                            }}>
                            <CustomTextField
                                label="Remarks"
                                multiline
                                rows={3}
                                fullWidth
                                name="remarks"
                                onChange={handleChange}
                                value={formData.remarks}
                                isDisabled={isPolicyCompleted}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Box>
                    {/* User Info */}
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                User Info
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="created_by"
                                    label="Created By"
                                    value={formData.created_by}
                                    onChange={handleChange}
                                    fullWidth
                                    isDisabled={true}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="created_at"
                                    label="Created At"
                                    value={(() => {
                                        // If formData.created_at exists, try to parse it
                                        if (formData.created_at) {
                                            const date = dayjs(formData.created_at);
                                            if (date.isValid()) {
                                                return date.format('DD/MM/YYYY');
                                            }
                                        }
                                        // If no valid created_at or parsing failed, return current date
                                        return dayjs().format('DD/MM/YYYY');
                                    })()}
                                    fullWidth
                                    isDisabled={true}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                </form>
            )
            }
            <MemberSelectionPopup
                open={openMemberDialog}
                onClose={handleMemberDialogClose}
                existingMembers={members}
                potentialMembers={memberSelectionData}
                onSave={handleAddMember}
                relationOptions={relationOptions}
            />
        </Box >
    );
}
export default ProposalRollOver;