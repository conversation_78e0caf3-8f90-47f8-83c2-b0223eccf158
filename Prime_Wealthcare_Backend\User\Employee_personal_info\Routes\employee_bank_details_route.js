const express = require('express');
const EmployeeBankDetailsController = require('../Controllers/employee_bank_details_controller');
const router = express.Router();

// Route to create a new Employee info
router.post('/', EmployeeBankDetailsController.createEmployeeBankDetails);

router.get('/employee_id/:id', EmployeeBankDetailsController.getEmployeeBankDetailsByEmployeeId);

router.get('/', EmployeeBankDetailsController.getEmployeeBankDetails);

router.get('/:id', EmployeeBankDetailsController.getEmployeeBankDetailsById);

router.put('/:id', EmployeeBankDetailsController.updateEmployeeBankDetails);

router.delete('/first_bank/:id', EmployeeBankDetailsController.deleteFirstBankById);

router.delete('/second_bank/:id', EmployeeBankDetailsController.deleteSecondBankById);

router.delete('/:id', EmployeeBankDetailsController.deleteEmployeeBankDetails);

module.exports = router;