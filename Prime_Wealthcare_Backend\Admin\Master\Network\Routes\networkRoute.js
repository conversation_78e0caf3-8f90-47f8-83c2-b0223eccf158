const express = require('express')
const networkController = require('../Controllers/networkController')
const router = express.Router();

router.get('/', networkController.getAll);

router.get('/insurance-company/:insuranceCompanyId', networkController.getNetworkByInsuranceCompanyId);

router.get('/:id', networkController.getNetworkById);

router.get('/name/:name', networkController.getNetworkByName);

router.get('/criteria/:criteria', networkController.getNetworksByCriteria);

router.post('/', networkController.createNetwork);

router.put('/:id', networkController.updateNetwork);

router.delete('/:id', networkController.deleteNetwork);

router.put('/reinstate/:id', networkController.reinstateNetwork);

module.exports = router;