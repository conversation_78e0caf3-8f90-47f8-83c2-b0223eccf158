const express = require('express');
const router = express.Router();
const imfBranchController = require('../Controllers/imfBranchController');

router.get('/', imfBranchController.getAllIMFBranches);
router.get('/:id', imfBranchController.getIMFBranchById);
router.post('/', imfBranchController.createIMFBranch);
router.put('/:id', imfBranchController.updateIMFBranch);
router.delete('/:id', imfBranchController.softDeleteIMFBranch);
router.put('/reinstate/:id', imfBranchController.reinstateIMFBranch);
router.get('/criteria/:criteria', imfBranchController.getIMFBranchesByCriteria);
router.get('/name/:name', imfBranchController.searchIMFBranch);



module.exports = router;
