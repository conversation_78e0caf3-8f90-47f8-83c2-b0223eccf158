import { createSlice } from '@reduxjs/toolkit';
import { checkIfSuccess, createPayment } from '../../actions/action';
import { toast } from 'react-toastify';

const initialState = {
    paymentDetails: null,
    loading: false,
    error: null,
    transactionDetails: null,
    transactionStatus: null,
    currentProposalId: null
};

const paymentSlice = createSlice({
    name: 'payment',
    initialState,
    reducers: {
        setPaymentDetails: (state, action) => {
            state.paymentDetails = action.payload;
        },
        setTransactionDetails: (state, action) => {
            state.transactionDetails = action.payload;
        },
        setTransactionStatus: (state, action) => {
            state.transactionStatus = action.payload;
        },
        setCurrentProposalId: (state, action) => {
            state.currentProposalId = action.payload;
        },
        clearPaymentDetails: (state) => {
            state.transactionStatus = null;
            state.transactionDetails = null;
            state.error = null;
        },
        clearAllPaymentDetails: (state) => {
            state.transactionStatus = null;
            state.transactionDetails = null;
            state.error = null;
            state.paymentDetails = null;
            state.currentProposalId = null;
        },
        setError: (state, action) => {
            state.error = action.payload;
            state.loading = false;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(createPayment.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(createPayment.fulfilled, (state, action) => {
                state.transactionDetails = action.payload;
                toast.success('Payment created successfully');
                state.loading = false;
            })
            .addCase(createPayment.rejected, (state, action) => {
                toast.error('Payment creation failed');
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(checkIfSuccess.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(checkIfSuccess.fulfilled, (state, action) => {
                state.transactionStatus = action.payload;
                state.loading = false;
            })
            .addCase(checkIfSuccess.rejected, (state, action) => {
                toast.error('Payment status check failed');
                state.error = action.payload;
                state.loading = false;
            })
    }

});

export const {
    setPaymentDetails,
    setTransactionDetails,
    setTransactionStatus, 
    clearPaymentDetails,
    clearAllPaymentDetails,
    setCurrentProposalId,
    setError,
    setLoading
} = paymentSlice.actions;

export default paymentSlice.reducer;