exports.up = function (knex) {
    return knex.schema.hasTable('proposals_rollover_migration').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('proposals_rollover_migration', (table) => {
                table.increments('id').primary(); // Auto-increment primary key
                table.integer('customer_id').notNullable(); // Foreign key: customer ID
                table.string('customer_salutation', 10).notNullable();
                table.string('policy_number').nullable();

                table.string('pinsurance_company').nullable();
                table.string('pproduct_type').nullable();
                table.string('pproduct_name').nullable();
                table.string('psub_product').nullable();
                table.string('pmember_type').nullable();

                table.integer('insurance_company').unsigned().references('id').inTable('insurance_company').onDelete('CASCADE');
                table.integer('insurance_branch').unsigned().references('id').inTable('insurance_branch').onDelete('CASCADE');
                table.integer('agent_code').unsigned().references('id').inTable('agents').onDelete('CASCADE'); // Agent code
                table.integer('product_type').unsigned().references('id').inTable('main_product').onDelete('CASCADE');
                table.integer('product_name').unsigned().references('id').inTable('product_master').onDelete('CASCADE');
                table.integer('sub_product').unsigned().references('id').inTable('sub_product').onDelete('CASCADE');
                table.string('member_type').notNullable();

                table.string('net_premium').notNullable(); // Net premium
                table.string('gst_amount').notNullable(); // GST amount
                table.string('gst_percentage').notNullable().defaultTo(18); // GST percentage
                table.string('total_premium').notNullable(); // Total premium
                table.integer('tenure').notNullable(); // Tenure
                table.boolean('co_pay').nullable().defaultTo(false);
                table.string('start_date').nullable(); // Start date
                table.string('end_date').nullable(); // End date
                table.string('policy_issue_date').nullable(); // Policy issue date
                table.string('policy_pdf').nullable(); // Policy PDF
                table.string('proposal_Number').nullable(); // Proposal ID
                table.string('prev_proposal_number').references('ProposalNumber').inTable('payment_master').onDelete('CASCADE'); // Add foreign key reference
                table.string('receipt_no', 255).nullable(); // Receipt No
                table.integer('imf_code').unsigned();
                table.integer('imf_branch').unsigned().references('id').inTable('imf_branches').onDelete('CASCADE');
                table.string('remarks').nullable();
                table.string('proposal_type').notNullable();
                table.string('status').notNullable(); // Status
                table.string('Created_by').notNullable(); // Created by
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now()); // Created timestamp
                table.string('Updated_by').nullable(); // Updated by
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now()); // Updated timestamp
            });
        }
    })
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('proposals_rollover_migration');
};
