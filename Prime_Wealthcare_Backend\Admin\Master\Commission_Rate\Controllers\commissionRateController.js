const CommissionRate = require('../Models/commissionRate');

class CommissionRateController {
    // Get all commission rates
    static async getAll(req, res, next) {
        try {
            const rates = await CommissionRate.getAll();
            res.status(200).json(rates);
        } catch (error) {
            next(error);
        }
    }

    // Get a commission rate by ID
    static async getById(req, res, next) {
        const { id } = req.params;
        try {
            const rate = await CommissionRate.getById(id);
            if (!rate) return res.status(404).json({ message: 'Commission rate not found' });
            res.status(200).json(rate);
        } catch (error) {
            next(error);
        }
    }

    // Create a new commission rate
    static async create(req, res, next) {
        try {
            const rateId = await CommissionRate.create(req.body);
            ("dataaaaa", req.body)
            ("POST request to create commission rate received");
            ("Request Body:", req.body); // Log the incoming request body
            res.status(201).json({ message: 'Commission rate created', id: rateId });
        } catch (error) {
            next(error);
        }
    }

    // Update multiple commission rates
    static async updateMultiple(req, res, next) {
        const { commissionRates } = req.body;

        if (!Array.isArray(commissionRates)) {
            return res.status(400).json({ error: 'Invalid input: commissionRates must be an array.' });
        }

        // Validate each commission rate entry
        for (const rate of commissionRates) {
            if (typeof rate.id !== 'number' || typeof rate.fixed_percentage !== 'number') {
                return res.status(400).json({ error: 'Invalid input: each commission rate must have a valid id and fixed_percentage.' });
            }
        }

        try {
            await CommissionRate.updateCommissionRates(commissionRates);
            res.status(200).json({ message: 'Commission rates updated successfully' });
        } catch (error) {
            console.error('Failed to update commission rates:', error);
            next(error);
        }
    }


    // Soft delete a commission rate
    static async delete(req, res, next) {
        const { id } = req.params;
        try {
            await CommissionRate.delete(id);
            res.status(200).json({ message: 'Commission rate deleted' });
        } catch (error) {
            next(error);
        }
    }

    // Reinstate a commission rate
    static async reinstate(req, res, next) {
        const { id } = req.params;
        try {
            const message = await CommissionRate.reinstate(id);
            res.status(200).json(message);
        } catch (error) {
            next(error);    
        }    
    }



    static async getCommissionRatesByCompany(req, res, next) {
        const { insuranceCompanyId } = req.params;
        try {
            const commissionRates = await CommissionRate.getCommissionRatesByInsuranceCompanyId(insuranceCompanyId);

            return res.status(200).json(commissionRates);
        } catch (error) {
            //console.error('Error in fetching commission rates:', error);
            next(error);  
       }
    }



    static async updateCommissionRate(req, res, next) {
        try {
            (req.params); // Add this line to debug
            // Extract the ID from URL parameters and updated data from request body
            const { id } = req.params;
            const {
                range_from,
                range_to,
                extra_percentage,
                effective_from,
                effective_to,
                range_from2,
                range_to2,
                extra_percentage2,
                effective_from2,
                effective_to2,
                calculated_on
            } = req.body;

            // Prepare the data for updating
            const updateData = {

                range_from,
                range_to,
                extra_percentage,
                effective_from,
                effective_to,
                // Convert empty strings to null for integer fields
                range_from2: range_from2 === '' ? null : range_from2,
                range_to2: range_to2 === '' ? null : range_to2,
                extra_percentage2: extra_percentage2 === '' ? null : extra_percentage2,
                effective_from2,
                effective_to2,
                calculated_on
            };

            // Call the model function to update the commission rate
            const updated = await CommissionRate.updateCommissionRate(id, updateData);

            // Check if the update was successful
            if (updated) {
                return res.status(200).json({ message: 'Commission rate updated successfully' });
            } else {
                return res.status(404).json({ message: 'Commission rate not found' });
            }
        } catch (error) {
            console.error("Error updating commission rate:", error);
            next(error);
        }
    }


    // Fetch commission rates by date ranges for various condition
    static async getCommissionRatesByCriteria(req, res, next) {
        try {
            const { criteria } = req.params;  // Use 'criteria' from URL parameters

            let data;
            switch (criteria) {
                case 'none':
                    data = await CommissionRate.getAll();
                    break;
                case 'newLastWeek':
                    data = await CommissionRate.newLastWeek();
                    break;
                case 'newThisWeek':
                    data = await CommissionRate.newThisWeek();
                    break;
                case 'deactivatedThisWeek':
                    data = await CommissionRate.deactivatedThisWeek();
                    break;
                case 'deactivatedLastWeek':
                    data = await CommissionRate.deactivatedLastWeek();
                    break;
                case 'editedThisWeek':
                    data = await CommissionRate.editedThisWeek();
                    break;
                case 'editedLastWeek':
                    data = await CommissionRate.editedLastWeek();
                    break;
                /*  default:
                     return res.status(400).json({ error: 'Invalid criteria parameter' }); */
            }

            res.status(200).json(data);
        } catch (error) {
           // console.error('Error fetching commission rates by date range:', error);
            next(error);
        }
    }

    // Method to get commission rates by name
    static async getCommissionRatesByName(req, res, next) {
        const { name } = req.params; // Get name from route parameters

        if (!name) {
            return res.status(400).json({ message: 'Name parameter is required.' });
        }

        try {
            const commissionRates = await CommissionRate.getByName(name);
            return res.status(200).json(commissionRates);
        } catch (error) {
            //console.error('Error fetching commission rates:', error);
            next(error);
        }
    }
}


module.exports = CommissionRateController;
