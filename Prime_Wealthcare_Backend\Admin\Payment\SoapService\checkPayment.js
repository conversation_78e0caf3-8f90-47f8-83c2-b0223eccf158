const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const knex = require('knex');
const knexConfig = require('../../../knexfile.js');
const db = knex(knexConfig.development);

require('dotenv').config();

const paymentURL = process.env.CHECK_PAYMENT_URL; // Use the URL from the .env file
const paymentSource = process.env.CHECK_PAYMENT_SOURCE;

const SOAP_BODY = (transactionId) => `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
    <soap:Body>
        <FetchTRNDetails xmlns="http://tempuri.org/">
            <transactionId>${transactionId}</transactionId>
            <source>${paymentSource}</source>
        </FetchTRNDetails>
    </soap:Body>
</soap:Envelope>`

const sendSOAPRequest = async (transactionId) => {
    try {
        const headers = {
            "Content-Type": "text/xml; charset=utf-8"
        };
        const requestBody = SOAP_BODY(transactionId);

        const response = await axios.post(paymentURL, requestBody, { headers });

        const jsonResponse = await parseStringPromise(response.data);


        const quickPayFields = jsonResponse?.['soap:Envelope']?.['soap:Body']?.[0]
            ?.['FetchTRNDetailsResponse']?.[0]?.['FetchTRNDetailsResult']?.[0]
            ?.['listQuickPayFields']?.[0]?.['QuickPayField'] || [];

        const paymentRecords = quickPayFields
            .filter(field => field['TransactionStatus']?.[0] === process.env.PAYMENT_SUCCESS_CODE)
            .map(field => ({
                pgTransactionId: field['PGTransactionID']?.[0] || '',
                transactionDate: field['TransactionDate']?.[0] || '',
                transactionStatus: field['TransactionStatus']?.[0] || '',
                transactionId,
                clientName: field['ClientName']?.[0] || '',
                paymentAmount: parseFloat(field['PaymentAmount']?.[0]) || 0,
                paymentGateway: field['PaymentMode']?.[0] || '',
                emailId: field['EmailID']?.[0] || '',
                mobileNo: field['MobileNo']?.[0] || '',
                fgTransactionId: field['FG_Transaction_ID']?.[0] || '',
                srno: field['Srno']?.[0] || '0'
            }));

        if (quickPayFields.length === 0) {
            return {
                status: 'fail',
                message: 'No payment records found for the given transaction ID'
            };
        }

        await db('payment_recon_logs').insert({ transaction_id: transactionId, response_payload: response.data, request_payload: requestBody });

        return {
            status: 'success',
            ...paymentRecords?.[0],
            validationError: jsonResponse?.['soap:Envelope']?.['soap:Body']?.[0]
                ?.['FetchTRNDetailsResponse']?.[0]?.['FetchTRNDetailsResult']?.[0]
                ?.['validationError']?.[0] || '',
            exceptionError: jsonResponse?.['soap:Envelope']?.['soap:Body']?.[0]
                ?.['FetchTRNDetailsResponse']?.[0]?.['FetchTRNDetailsResult']?.[0]
                ?.['exceptionError']?.[0] || ''
        };

    } catch (error) {
        throw {
            error: {
                message: error.message || 'Failed to fetch payment details',
                timestamp: new Date().toISOString(),
                type: error.type || 'PAYMENT_DETAILS_FETCH_ERROR'
            }
        };
    }
}

module.exports = {
    sendSOAPRequest
};