const express = require('express');
const agentsDetailsController = require('../Controllers/agentDetailsController');
const upload = require('../Middleware/upload');
const router = express.Router();

const uploadFields = upload.fields([
    { name: 'aadhar_card_front', maxCount: 1 },
    { name: 'aadhar_card_back', maxCount: 1 },
    { name: 'driving_license_card', maxCount: 1 },
    { name: 'pan_card', maxCount: 1 },
    { name: 'photo', maxCount: 1 },
    { name: 'signed_offer_letter_card', maxCount: 1 },
])

// Route to get all agent details
router.get('/', agentsDetailsController.getAllAgents);

// Route to get agent details by ID
router.get('/:id', agentsDetailsController.getAgentById);

// Route to get agents by branch_id
router.get('/branch_id/:id', agentsDetailsController.getAgentByBranchName)

// Route to get agent details by ID for edit
router.get('/edit/:id', agentsDetailsController.getAgentByIdForEdit);

// Route to get agent details by search
router.get('/search/:search', agentsDetailsController.getAgentBySearch);

// Route to create a new agent detail
router.post('/', uploadFields, agentsDetailsController.createAgent);

// Route to update agent details by ID
router.put('/:id', uploadFields, agentsDetailsController.updateAgent)

// Route to deactivate agent details by ID
router.delete('/:id', agentsDetailsController.deleteAgent);

// Route to reinstate agent details by ID
router.put('/reinstate/:id', agentsDetailsController.reinstateAgent);

// Route to get agents by status (active or inactive)
router.get('/criteria/:criteria', agentsDetailsController.getAgentsByCriteria);

module.exports = router;