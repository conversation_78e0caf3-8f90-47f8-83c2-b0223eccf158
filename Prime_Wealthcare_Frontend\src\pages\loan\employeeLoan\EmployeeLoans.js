import React, { useEffect, useState, useMemo } from 'react';
import { Box, Container, Button, ButtonGroup, Grid, Typography } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTableNew from '../../../components/table/CustomTableNew';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { startLoading, stopLoading } from '../../../redux/slices/loadingSlice';
import { getAllEmployeeLoans } from '../../../redux/actions/action';
import dayjs from 'dayjs';
import { formatDate } from '../../../utils/Reusable';
import { usePermissions } from '../../../hooks/usePermissions';

function EmployeeLoans() {
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [activeFilter, setActiveFilter] = useState('all');

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const employeeLoans = useSelector(state => state.employeeLoanReducer.employeeLoans);
    const [sortedEmployeeLoans, setSortedEmployeeLoans] = useState(employeeLoans || []);
    const isLoading = true;

    // Get permissions for this page
    const permissions = usePermissions('Loan', 'Employee Loan');

    // Calculate counts for different approval statuses
    const approvedLoans = useMemo(() =>
        employeeLoans.filter(loan => loan.admin_approval === 'APPROVED').length
        , [employeeLoans]);

    const pendingLoans = useMemo(() =>
        employeeLoans.filter(loan => loan.admin_approval === 'PENDING').length
        , [employeeLoans]);

    const rejectedLoans = useMemo(() =>
        employeeLoans.filter(loan => loan.admin_approval === 'REJECTED').length
        , [employeeLoans]);

    useEffect(() => {
        if (isLoading === 'rejected') {
            dispatch(startLoading());
        } else {
            dispatch(stopLoading());
        }
    }, [isLoading, dispatch])

    useEffect(() => {
        dispatch(getAllEmployeeLoans())
    }, [dispatch])

    useEffect(() => {
        // Format the dates in the filtered loans
        const loansWithFormattedDates = getFilteredLoans().map(loan => ({
            ...loan,
            issue_date_formatted: formatDate(loan.issue_date),
            end_date_formatted: formatDate(loan.end_date)
        }));
        setSortedEmployeeLoans(loansWithFormattedDates);
    }, [employeeLoans, activeFilter]);

    // Function to filter loans based on activeFilter
    const getFilteredLoans = () => {
        switch (activeFilter) {
            case 'approved':
                return employeeLoans.filter(loan => loan.admin_approval === 'APPROVED');
            case 'pending':
                return employeeLoans.filter(loan => loan.admin_approval === 'PENDING');
            case 'rejected':
                return employeeLoans.filter(loan => loan.admin_approval === 'REJECTED');
            default:
                return employeeLoans;
        }
    };

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/employee-loan-form');
    };

    const handleEdit = (id) => {
        navigate(`/dashboard/employee-loan-form/${id}`);
    };

    const handleView = (id) => {
        navigate(`/dashboard/employee-loan-form/view/${id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedEmployeeLoans?.map(employeeLoan => employeeLoan.id) : []);
    };

    const columns = [
        { field: 'employee_name', headerName: 'Employee Name' },
        { field: 'loan_id', headerName: 'Loan ID' },
        { field: 'loan_type', headerName: 'Loan Type' },
        { field: 'loan_amount', headerName: 'Loan Amount' },
        { field: 'balance_amount', headerName: 'Balance Amount' },
        { field: 'paid_amount', headerName: 'Paid Amount' },
        { field: 'emi', headerName: 'EMI' },
        { field: 'issue_date_formatted', headerName: 'Issue Date' },
        { field: 'tenure', headerName: 'Tenure' },
        { field: 'end_date_formatted', headerName: 'End Date' },
        { field: 'admin_approval', headerName: 'Status' }
    ];

    const dataMapping = {
        id: 'id',
        Loan_Type: 'loan_type',
        Loan_Amount: 'loan_amount',
        Balance_Amount: 'balance_amount',
        Paid_Amount: 'paid_amount',
        EMI: 'emi',
        Issue_Date: 'issue_date',
        Tenure: 'tenure',
        End_Date: 'end_date',
        Status: 'status',
    };

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Employee Loan" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                            <Button onClick={handleAdd} sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}>
                                New
                            </Button>
                        )}
                        {permissions.can_view && (
                            <ExportToPDF
                                data={sortedEmployeeLoans?.map(employeeLoan => ({
                                    ...employeeLoan,
                                    status: employeeLoan.status === 1 ? 'Active' : 'Inactive'
                                }))}
                                headNames={['Area Name', 'City', 'Pincode', 'State', 'Status']}
                                selectedRows={selectedRows}
                                imageUrl="/logo.png"
                                watermarkUrl="/gray-logo.png"
                                fileName="employeeLoans.pdf"
                                dataMapping={dataMapping}
                                headerTitle="Employee Loans Report"
                            />
                        )}
                    </ButtonGroup>
                </Box>

                {/* Status filter boxes */}
                <Grid container spacing={3} padding={2}>
                    {/* All Loans */}
                    <Grid item xs={12} sm={6} md={3}>
                        <Box
                            onClick={() => setActiveFilter('all')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #16A085`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'all' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>All Loans</Typography>
                            <Typography variant='h6' sx={{ color: '#16A085', fontWeight: 'bold' }}>{employeeLoans.length}</Typography>
                        </Box>
                    </Grid>

                    {/* Approved Loans */}
                    <Grid item xs={12} sm={6} md={3}>
                        <Box
                            onClick={() => setActiveFilter('approved')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #2ecc71`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'approved' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Approved</Typography>
                            <Typography variant='h6' sx={{ color: '#2ecc71', fontWeight: 'bold' }}>{approvedLoans}</Typography>
                        </Box>
                    </Grid>

                    {/* Pending Loans */}
                    <Grid item xs={12} sm={6} md={3}>
                        <Box
                            onClick={() => setActiveFilter('pending')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #FFC300`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'pending' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Pending</Typography>
                            <Typography variant='h6' sx={{ color: '#FFC300', fontWeight: 'bold' }}>{pendingLoans}</Typography>
                        </Box>
                    </Grid>

                    {/* Rejected Loans */}
                    <Grid item xs={12} sm={6} md={3}>
                        <Box
                            onClick={() => setActiveFilter('rejected')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #e74c3c`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'rejected' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Rejected</Typography>
                            <Typography variant='h6' sx={{ color: '#e74c3c', fontWeight: 'bold' }}>{rejectedLoans}</Typography>
                        </Box>
                    </Grid>
                </Grid>

                <Box sx={{ mt: -1 }}>
                    <CustomTableNew
                        data={sortedEmployeeLoans}
                        columns={columns}
                        onEdit={permissions.can_edit ? handleEdit : null}
                        onView={permissions.can_view ? handleView : null}
                        selectedRows={selectedRows}
                        onSelectionChange={handleSelectionChange}
                        onSelectAll={handleSelectAll}
                        showViewButton={true}
                        showEditButton={permissions.can_edit}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                message={`Are you sure you want to delete this employee loan?`}
                onClose={handleCloseDeletePopup}
                // onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.city_name : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                deleted={'deleted'}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.city_name : ''}
            />
        </Container>
    )
}

export default EmployeeLoans