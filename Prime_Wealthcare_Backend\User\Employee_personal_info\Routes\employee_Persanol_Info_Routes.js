const express = require('express');
const router = express.Router();
const EmployeeInfoController = require('../Controllers/employee_Personal_Info_Controller');
const upload = require('../Midleware/upload');
const uploadFields = upload.fields([
  { name: 'emp_photo', maxCount: 1 },
  { name: 'emp_adhar_front_pdf', maxCount: 1 },
  { name: 'emp_adhar_back_pdf', maxCount: 1 },
  { name: 'emp_PAN_pdf', maxCount: 1 },
  { name: 'emp_signed_offer_letter_pdf', maxCount: 1 },
  { name: 'emp_driving_license_pdf', maxCount: 1 }
]);
// Create a new Employee
router.post('/', uploadFields, EmployeeInfoController.createEmployee_Info);

router.get('/emp_info/:user_id', EmployeeInfoController.getEmployeeByUserId);

router.get('/user_id/:user_id', EmployeeInfoController.getEmployeeByReportingManagerUserId);

router.get('/name', EmployeeInfoController.getEmployeeByName);

router.get('/criteria/:criteria', EmployeeInfoController.getEmployeesByCriteria);

router.get('/', EmployeeInfoController.getAllEmployeeInfo);

router.get('/:id', EmployeeInfoController.getEmployeeInfoById);

router.put('/:id', uploadFields, EmployeeInfoController.updateEmployeeInfo);

router.delete('/:id', EmployeeInfoController.softDeleteEmployeeInfo);

router.put('/reinstate/:id', EmployeeInfoController.reinstateEmployeeInfo);

module.exports = router;