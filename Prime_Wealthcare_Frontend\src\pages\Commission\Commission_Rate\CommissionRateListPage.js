import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import { useNavigate } from 'react-router-dom';
import ModuleName from '../../../components/table/ModuleName';
import DropDown from '../../../components/table/DropDown';
import { useDispatch, useSelector } from 'react-redux';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
  fetchCommissionRates, updateCommissionRate, deleteCommissionRate, reinstateCommissionRate,
  getCommissionRateByCriteria, searchCommissionRate
} from '../../../redux/actions/action';
import Popup from './PopUp'; // Popup component for extra percentage fields
import { resetIsSubProductAdded } from '../../../redux/slices/master/subProductSlice';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const CommissionRateListPage = () => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const [selectedOption, setSelectedOption] = useState('none');
  const [statusFilter, setStatusFilter] = useState('all');
  const [hasSynced, setHasSynced] = useState(false); // State to track sync status
  const [searchQuery, setSearchQuery] = useState('');

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isSubProductAdded = useSelector(state => state.subProductReducer.isSubProductAdded);
  const commissionRates = useSelector(state => state.commissionRateReducer.commissionRates);
  const [filteredRates, setFilteredRates] = useState([]);

  useEffect(() => {
    dispatch(fetchCommissionRates());
  }, [dispatch]);


  useEffect(() => {
    // Reset isSubProductAdded state when the component mounts or unmounts
    return () => {
      dispatch(resetIsSubProductAdded());
    };
  }, [dispatch]);


  // Filter commission rates by status
  useEffect(() => {
    const filterRatesByStatus = () => {
      let ratesToDisplay = commissionRates;

      if (statusFilter !== 'all') {
        ratesToDisplay = ratesToDisplay.filter(rate => rate.status === (statusFilter === 'active' ? 1 : 0));
      }

      // Filter out rates where fixed_percentage is less than or equal to 0
      ratesToDisplay = ratesToDisplay.filter(rate => rate.fixed_percentage > 0);
      setFilteredRates(ratesToDisplay);
    

     // Sort by 'created_at' in reverse order (newest first)
     ratesToDisplay = [...ratesToDisplay].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }
    filterRatesByStatus();
  }, [statusFilter, commissionRates]);

  useEffect(() => {
    const fetchCommissionRates = () => {
      dispatch(getCommissionRateByCriteria(selectedOption));
    };
    fetchCommissionRates();
  }, [selectedOption, dispatch]);

  const handleOpenDeletePopup = (item) => {
    setSelectedItem(item);
    setOpenDeletePopup(true);
  };

  const handleCloseDeletePopup = () => {
    setOpenDeletePopup(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    dispatch(deleteCommissionRate(selectedItem.id))
      .then(() => {
        dispatch(fetchCommissionRates());
        setOpenDeletePopup(false);
        setOpenSuccessPopup(true);
      })
      .catch(error => {
        console.error("Failed to delete commission rate:", error);
        setOpenDeletePopup(false);
      });
  };

  const handleCloseSuccessPopup = () => {
    setOpenSuccessPopup(false);
  };


  const handleDelete = (id) => {
    handleOpenDeletePopup(commissionRates.find(company => company.id === id));
  };

  const handleReinstate = (id) => {
    dispatch(reinstateCommissionRate(id))
      .then(() => {
        dispatch(fetchCommissionRates());
      })
      .catch(error => {
        console.error("Failed to reinstate role:", error);
      });
  };

  const handleEdit = (id) => {
    const selectedCommissionRate = commissionRates.find(rate => rate.id === id);
    navigate(`/dashboard/commission-rate-edit-form/${id}`, { state: { commissionRate: selectedCommissionRate } });
  };


  const handleSavePopup = (updatedData) => {
    // Dispatch update action
    dispatch(updateCommissionRate(updatedData))
      .then(() => {
        dispatch(fetchCommissionRates());
        setOpenPopup(false);
      })
      .catch(error => console.error('Failed to update commission rate:', error));
  };

  const handleSyncCommissionData = () => {
    dispatch(fetchCommissionRates()).then(() => {
      setHasSynced(true); // Mark as synced
      navigate('/dashboard/commission-rate'); // Navigate to the update page
    });
  };

  const handleSearch = (query) => {
    if (query) {
      dispatch(searchCommissionRate(query));
    } else {
      dispatch(fetchCommissionRates());
    }
  };
  
  const handleAdd = () => {
    navigate('/dashboard/commission-rate'); // Navigate to add new commission rate
  };

  const handleSelectionChange = (id) => {
    setSelectedRows(prevSelected =>
      prevSelected.includes(id)
        ? prevSelected.filter(rowId => rowId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSelectAll = (isSelected) => {
    if (isSelected) {
      setSelectedRows(filteredRates.map(rate => rate.id)); // Select all
    } else {
      setSelectedRows([]); // Deselect all
    }
  };


  const handleAllClick = () => setStatusFilter('all');
  const handleActiveClick = () => setStatusFilter('active');
  const handleInactiveClick = () => setStatusFilter('inactive');
  const handleRefreshClick = () => dispatch(fetchCommissionRates());

  const handleExportToPDF = () => {
    // Create a new PDF in landscape mode
    const doc = new jsPDF({ orientation: 'landscape' });

    // Filter selected rows based on IDs in filteredRates
    const selectedData = filteredRates.filter(rate => selectedRows.includes(rate.id));

    // Define table headers for the PDF
    const columns = [
      'ID',
      'Insurance Company',
      'Main Product',
      'Product Name',
      'Sub Product',
      'Commission Source',
      'Commission Type',
      'Policy Type',
      'Fixed %',
      'Extra %',

    ];

    // Map data from filteredRates to match the table structure
    const body = selectedData.map(rate => [
      rate.id,
      rate.insurance_company_name,    // Replace with the actual field names from your data
      rate.main_product_name,
      rate.product_master_name,
      rate.sub_product_name,
      rate.commission_source,
      rate.commission_type,
      rate.policy_type,
      rate.fixed_percentage,
      rate.extra_percentage,
      rate.effective_from,

    ]);

    // Generate the PDF with the headers and body
    autoTable(doc, {
      head: [columns],     // Table header
      body: body,          // Table body (data rows)
      startY: 10,          // Adjust the start position of the table
      theme: 'grid',       // You can change the table theme here
      headStyles: { fillColor: [22, 160, 133] }, // Custom styles for header (optional)
      margin: { top: 20 }  // Add margin at the top for better spacing
    });

    // Save the generated PDF file
    doc.save('commission_rates_landscape.pdf');
  };

  const columns = [
    { field: 'insurance_company_name', headerName: 'Insurance Company', width: 150 },
    { field: 'main_product_name', headerName: 'Main Product', width: 150 },
    { field: 'product_master_name', headerName: 'Product Name', width: 150 },
    { field: 'sub_product_name', headerName: 'Sub Product', width: 150 },
    { field: 'commission_type', headerName: 'Comm Type', width: 150 },
    { field: 'commission_source', headerName: 'Comm Source', width: 150 },
    { field: 'policy_type', headerName: 'Policy Type', width: 150 },
    { field: 'fixed_percentage', headerName: 'Fixed %', width: 100 },
    { field: 'extra_percentage', headerName: 'Extra %', width: 100 }, // Extra % from popup
  ];

  return (
    <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/image.png"
              alt="module icon"
              style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
            />
            <ModuleName moduleName="Commission" pageName="List" />
          </Box>
          <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
            <Button
              disabled={!isSubProductAdded}
              onClick={handleSyncCommissionData}
              sx={{
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
                backgroundColor: !isSubProductAdded ? 'gray' : '#528A7E',
                color: '#fff'
              }}
            >
              Sync Commission Data
            </Button>
            <Button
              onClick={handleAdd} // Navigate to add new commission rate
              sx={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
            >
              Add
            </Button>
            <Button
              onClick={handleExportToPDF}
              sx={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0, mr: '8px' }}
            >
              Export to PDF
            </Button>
          </ButtonGroup>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingBottom: '1rem', ml: 5 }}>
          <DropDown
            label=""
            value={selectedOption}
            onChange={(e) => setSelectedOption(e.target.value)}
            options={[
              { value: 'none', label: 'None' },
              { value: 'newLastWeek', label: 'New Last Week' },
              { value: 'newThisWeek', label: 'New this Week' },
              { value: 'deactivatedThisWeek', label: 'Deactivated this week' },
              { value: 'deactivatedLastWeek', label: 'Deactivated last week' },
              { value: 'editedLastWeek', label: 'Edited last week' },
              { value: 'editedThisWeek', label: 'Edited this week' },
            ]}
            sx={{ height: '30px', ml: '82px', mt: '-15px' }}
          />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchBar placeholder="Search......"
              onSearch={handleSearch} />
            <IconActions
              onAllClick={handleAllClick}
              onActiveClick={handleActiveClick}
              onInactiveClick={handleInactiveClick}
              onRefreshClick={handleRefreshClick}
            />
          </Box>
        </Box>

        <Box sx={{ mt: 2 }}>
          <CustomTable
            data={filteredRates}
            columns={columns}
            onDelete={handleDelete}
            onEdit={handleEdit}
            onReinstate={handleReinstate}
            onSelectionChange={handleSelectionChange}
            selectedRows={selectedRows}
            onSelectAll={handleSelectAll}

          />
        </Box>

        <DeletePopup
          open={openDeletePopup}
          onClose={handleCloseDeletePopup}
          onConfirm={handleConfirmDelete}
          modulename={selectedItem ? selectedItem.insurance_company_name : ''}

        />

        <SuccessPopup
          open={openSuccessPopup}
          onClose={handleCloseSuccessPopup}
          modulename={selectedItem ? selectedItem.insurance_company_name : ''}

        />

        {openPopup && (
          <Popup
            data={selectedItem} // Pass selected item to the popup for editing
            onSave={handleSavePopup}
            onClose={() => setOpenPopup(false)}
          />
        )}
      </Box>
    </Container>
  );
};

export default CommissionRateListPage;
