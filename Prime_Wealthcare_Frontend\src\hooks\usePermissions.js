import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { checkUserAccess } from '../redux/actions/action';

export const usePermissions = (moduleName, pageName) => {
    const [permissions, setPermissions] = useState({
        can_view: false,
        can_add: false,
        can_edit: false,
        can_delete: false
    });
    const user = useSelector(state => state.auth.user);
    const dispatch = useDispatch();

    useEffect(() => {
        const checkPermissions = async () => {
            if (!user?.userId) return;

            try {
                const response = await dispatch(checkUserAccess({
                    user_id: user.userId,
                    module_name: moduleName,
                    page_name: pageName
                })).unwrap();

                setPermissions({
                    can_view: <PERSON><PERSON><PERSON>(response.data?.can_view),
                    can_add: <PERSON><PERSON><PERSON>(response.data?.can_add),
                    can_edit: <PERSON><PERSON>an(response.data?.can_edit),
                    can_delete: <PERSON><PERSON><PERSON>(response.data?.can_delete)
                });
            } catch (error) {
                console.error('Permission check failed:', error);
            }
        };

        checkPermissions();
    }, [user, moduleName, pageName]);

    return permissions;
}; 