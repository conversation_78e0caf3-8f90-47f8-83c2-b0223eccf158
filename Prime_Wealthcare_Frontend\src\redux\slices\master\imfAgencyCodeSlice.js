import { createSlice } from '@reduxjs/toolkit';
import {
  fetchImfAgencyCodes,
  fetchImfAgencyCodeById,
  createImfAgencyCode,
  updateImfAgencyCode,
  softDeleteImfAgencyCode,
  reinstateImfAgencyCode,
  //getAgencyCodesByBranchName
} from '../../actions/action';



// Slice
const imfAgencyCodeSlice = createSlice({
  name: 'imfAgencyCode',
  initialState: {
    agencyCodes: [],
    selectedAgencyCode: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    // Fetch all agency codes
    builder.addCase(fetchImfAgencyCodes.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(fetchImfAgencyCodes.fulfilled, (state, action) => {
      state.agencyCodes = action.payload;
      state.loading = false;
    });
    builder.addCase(fetchImfAgencyCodes.rejected, (state, action) => {
      state.error = action.error.message;
      state.loading = false;
    });

    // Fetch agency code by ID
    builder.addCase(fetchImfAgencyCodeById.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(fetchImfAgencyCodeById.fulfilled, (state, action) => {
      state.selectedAgencyCode = action.payload;
      state.loading = false;
    });
    builder.addCase(fetchImfAgencyCodeById.rejected, (state, action) => {
      state.error = action.error.message;
      state.loading = false;
    });

    // Create agency code
    builder.addCase(createImfAgencyCode.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(createImfAgencyCode.fulfilled, (state, action) => {
      state.agencyCodes.push(action.payload);
      state.loading = false;
    });
    builder.addCase(createImfAgencyCode.rejected, (state, action) => {
      state.error = action.error.message;
      state.loading = false;
    });

    // Update agency code
    builder.addCase(updateImfAgencyCode.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(updateImfAgencyCode.fulfilled, (state, action) => {
      const index = state.agencyCodes.findIndex(agencyCode => agencyCode.id === action.payload.id);
      if (index !== -1) {
        state.agencyCodes[index] = action.payload;
      }
      state.loading = false;
    });
    builder.addCase(updateImfAgencyCode.rejected, (state, action) => {
      state.error = action.error.message;
      state.loading = false;
    });

    // Soft delete agency code
    builder.addCase(softDeleteImfAgencyCode.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(softDeleteImfAgencyCode.fulfilled, (state, action) => {
      const index = state.agencyCodes.findIndex(agencyCode => agencyCode.id === action.payload.id);
      if (index !== -1) {
        state.agencyCodes[index].status = 0; // Mark as soft-deleted
      }
      state.loading = false;
    });
    builder.addCase(softDeleteImfAgencyCode.rejected, (state, action) => {
      state.error = action.error.message;
      state.loading = false;
    });

    // Reinstate agency code
    builder.addCase(reinstateImfAgencyCode.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(reinstateImfAgencyCode.fulfilled, (state, action) => {
      const index = state.agencyCodes.findIndex(agencyCode => agencyCode.id === action.payload.id);
      if (index !== -1) {
        state.agencyCodes[index].status = 1; // Mark as active
      }
      state.loading = false;
    });
    builder.addCase(reinstateImfAgencyCode.rejected, (state, action) => {
      state.error = action.error.message;
      state.loading = false;
    });
    /* builder
      .addCase(getAgencyCodesByBranchName.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAgencyCodesByBranchName.fulfilled, (state, action) => {
        state.loading = false;
        state.agencyCodes = action.payload;
      })
      .addCase(getAgencyCodesByBranchName.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      }); */
  }

  
});

// Export the reducer
export default imfAgencyCodeSlice.reducer;