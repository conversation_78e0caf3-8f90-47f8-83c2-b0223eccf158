exports.up = function (knex) {
    return knex.schema.createTable('policy_logs', (table) => {
        table.increments('id').primary();
        table.string('quotation_number').notNullable();
        table.string('policy_type').notNullable(); // 'PA' or 'HEALTH'
        table.text('request_payload');
        table.text('response_payload');
        table.string('status');
        table.text('error_message');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        
        // Add foreign key constraints
        table.foreign('quotation_number')
            .references('quotation_number')
            .inTable('proposals')
            .onDelete('CASCADE');
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('policy_logs');
};