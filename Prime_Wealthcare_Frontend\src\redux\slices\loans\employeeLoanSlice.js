import { createSlice } from "@reduxjs/toolkit";
import { getAllEmployeeLoans, createEmployeeLoan, updateEmployeeLoan, deleteEmployeeLoan, getEmployeeLoanById, getEmployeeLoanEmis, updateEmployeeEmi, deleteEmployeeEmi } from "../../actions/action";
import { toast } from 'react-toastify'; // Import toast for notifications

const initialState = {
    employeeLoans: [],
    employeeLoanDetails: {},
    employeeLoanEmis: [],
    isLoading: false,
    error: null,
};

const employeeLoanSlice = createSlice({
    name: 'employeeLoan',
    initialState,
    reducers: {
        clearEmployeeLoanDetails: (state) => {
            state.employeeLoanDetails = {};
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(getAllEmployeeLoans.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(getAllEmployeeLoans.fulfilled, (state, action) => {
                state.isLoading = false;
                state.employeeLoans = action.payload;
            })
            .addCase(getAllEmployeeLoans.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee loans.'); // Error message
            })
            .addCase(createEmployeeLoan.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(createEmployeeLoan.fulfilled, (state, action) => {
                state.isLoading = false;
                state.employeeLoans.push(action.payload);
                //toast.success('Employee loan created successfully!'); // Success message
            })
            .addCase(createEmployeeLoan.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to create employee loan.'); // Error message
            })
            .addCase(updateEmployeeLoan.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(updateEmployeeLoan.fulfilled, (state, action) => {
                state.isLoading = false;
                const index = state.employeeLoans.findIndex(loan => loan.id === action.payload.id);
                if (index !== -1) {
                    state.employeeLoans[index] = action.payload;
                    //toast.success('Employee loan updated successfully!'); // Success message
                }
            })
            .addCase(updateEmployeeLoan.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to update employee loan.'); // Error message
            })
            .addCase(deleteEmployeeLoan.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(deleteEmployeeLoan.fulfilled, (state, action) => {
                state.isLoading = false;
                state.employeeLoans = state.employeeLoans.filter(loan => loan.id !== action.payload.id);
                //toast.success('Employee loan deleted successfully!'); // Success message
            })
            .addCase(deleteEmployeeLoan.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to delete employee loan.'); // Error message
            })
            .addCase(getEmployeeLoanById.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(getEmployeeLoanById.fulfilled, (state, action) => {
                state.isLoading = false;
                state.employeeLoanDetails = action.payload;
                //toast.success('Employee loan details fetched successfully!'); // Success message
            })
            .addCase(getEmployeeLoanById.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee loan details.'); // Error message
            })
            .addCase(getEmployeeLoanEmis.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(getEmployeeLoanEmis.fulfilled, (state, action) => {
                state.isLoading = false;
                state.employeeLoanEmis = action.payload;
                //toast.success('Loan EMIs fetched successfully!'); // Success message
            })
            .addCase(getEmployeeLoanEmis.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch loan EMIs.'); // Error message
            })
            .addCase(updateEmployeeEmi.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(updateEmployeeEmi.fulfilled, (state, action) => {
                state.isLoading = false;
                toast.success('EMI updated successfully!'); // Success message
            })
            .addCase(updateEmployeeEmi.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to update EMI.'); // Error message
            })
            .addCase(deleteEmployeeEmi.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(deleteEmployeeEmi.fulfilled, (state, action) => {
                state.isLoading = false;
                state.employeeLoanEmis = state.employeeLoanEmis.filter(emi => emi.id !== action.payload.id);
                //toast.success('EMI deleted successfully!'); // Success message
            })
            .addCase(deleteEmployeeEmi.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to delete EMI.'); // Error message
            });
    }
});

export const { clearEmployeeLoanDetails } = employeeLoanSlice.actions;
export default employeeLoanSlice.reducer;