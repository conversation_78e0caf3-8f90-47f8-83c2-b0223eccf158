const customerDocumentationModel = require('../Models/customer_documentation_model');
const uploadDir = process.env.UPLOAD_DIR;

// Create
exports.create = async (req, res, next) => {
    try {
        let data = req.body;
        const files = req.files;

        // Get file path from req.files
        let documentPath = '';
        if (files && files.document_path && files.document_path.length > 0) {
            // Get the relative path from the full path
            const path = files.document_path[0].path;
            documentPath = path.replace(uploadDir, '');
        }
        data = {
            customer_id: data.customer_id,
            member_id: data.member_id,
            document_type_id: data.document_type_id,
            document_id: data.document_id,
            document_path: documentPath, // Use the file path from req.files
        }

        const result = await customerDocumentationModel.create(data);
        res.status(200).json({ message: 'Customer Documentation created successfully', result });
    } catch (error) {
        console.error('Error creating customer documentation:', error);
        next(error);
    }
}

// Get All
exports.getAll = async (req, res, next) => {
    try {
        const result = await customerDocumentationModel.getAll();
        res.status(200).json(result);
    } catch (error) {
        next(error);
    }
}

// Get By Id
exports.getById = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await customerDocumentationModel.getById(id);
        res.status(200).json(result);
    } catch (error) {
        next(error);
    }
}

// Get By Customer Id
exports.getByCustomerId = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await customerDocumentationModel.getByCustomerId(id);
        res.status(200).json(result);
    } catch (error) {
        next(error);
    }
}

// Update
exports.update = async (req, res, next) => {
    try {
        const id = req.params.id;
        let data = req.body;
        const files = req.files

        // Get file path from req.files
        let documentPath = data.document_path; // Keep existing path if no new file
        if (files && files.document_path && files.document_path.length > 0) {
            // Get the relative path from the full path
            const path = files.document_path[0].path;
            documentPath = path.replace(uploadDir, ''); 
        } 

        data = {
            ...data,
            document_path: documentPath // Use the file path from req.files or keep existing
        }

        delete data.document_type;
        delete data.document_name;

        await customerDocumentationModel.update(id, data);
        res.status(200).json({ message: 'Customer Documentation updated successfully' });
    } catch (error) {
        next(error);
    }
}

//Update
exports.updateById = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = req.body;
        await customerDocumentationModel.updateById(id, data);
        res.status(200).json({ message: 'Customer Documentation updated successfully' });
    } catch (error) {
        next(error);
    }
}

// Delete
exports.delete = async (req, res, next) => {
    try {
        const id = req.params.id;
        await customerDocumentationModel.deleteById(id);
        res.status(200).json({ message: 'Customer Documentation deleted successfully' });
    } catch (error) {
        next(error);
    }
}


