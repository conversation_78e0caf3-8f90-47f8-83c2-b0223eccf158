import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Box, Grid, Typography, Divider, <PERSON><PERSON>, Card, CardContent, CircularProgress } from "@mui/material";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import AutocompleteDropdown from "../../components/table/AutocompleteDropdown";
import Dropdown from "../../components/table/DropDown";
import { FormGroup, FormControlLabel, Checkbox, Paper } from "@mui/material";
import {
    Table, TableBody, TableCell, TableContainer,
    TableHead, TableRow, TablePagination
} from "@mui/material";
import DownloadIcon from '@mui/icons-material/Download';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { Tab, Tabs } from '@mui/material';
import BarChartIcon from '@mui/icons-material/BarChart';
import TableChartIcon from '@mui/icons-material/TableChart';
import {
    fetchAllImfBranches, getAllAgentDetails, fetchInsuranceCompanyBranches,
    fetchInsuranceCompanies, getAllProducts, getAllSubProducts,
    fetchImfAgencyCodes, getAllMasterProducts, getMasterProductByMainProductAndInsuranceCompany,
    getSubProductByProductDetails, generateReport, getFinancialYears
} from "../../redux/actions/action";
import { clearReportData } from "../../redux/slices/reports/reportsSlice";
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import ReportCharts from '../../components/reports/ReportCharts';

const Reports = () => {
    const dispatch = useDispatch();
    const [formData, setFormData] = useState({
        imf_code: '',
        imf_branch: '',
        agent_id: '',
        insurance_company_name: '',
        product_type: '',
        product_name: '',  // This will be disabled initially
        sub_product_name: '', // This will be disabled initially
        member_type: '',
        policy_status: '',
        proposal_type: '',
        report_type: '',
        policy_start_date: null,
        policy_end_date: null,
        proposal_start_date: null,
        proposal_end_date: null
    });

    // Add a state to track disabled fields
    const [disabledFields, setDisabledFields] = useState({
        product_name: true,
        sub_product_name: true,
        product: true,  // For group by
        sub_product: true  // For group by
    });

    // Add pagination state
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [selectedFinancialYears, setSelectedFinancialYears] = useState(['2025-26']); // Default to current year
    const [groupBy, setGroupBy] = useState({
        /*  agent: true,
         imf_branch: true,
         insurance_company: false,
         product: false,
         sub_product: false */
    });

    // Redux States
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies || []);
    const imfBranches = useSelector(state => state.imfBranchReducer.data || []);
    const agents = useSelector(state => state.agentReducer.agents || []);
    const mainProducts = useSelector(state => state.mainProductReducer.products || []);
    const masterProducts = useSelector(state => state.productMasterReducer.products || []);
    const subProducts = useSelector(state => state.subProductReducer.subProducts || []);
    const insuranceBranches = useSelector(state => state.insuranceBranchReducer.branches); // For insurance company branch dropdown
    const { reportData, loading, error } = useSelector(state => state.reports);
    const availableFinancialYears = useSelector(state => state.reports.financialYears);

    const [chartType, setChartType] = useState('bar');
    // Add a tab state to toggle between table and chart views
    const [viewMode, setViewMode] = useState('table');  // 'table' or 'chart'

    // Handle pagination changes
    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    // First, add a reset function to clear all form fields:
    const resetFormState = () => {
        setFormData({
            imf_code: '',
            imf_branch: '',
            agent_id: '',
            insurance_company_name: '',
            product_type: '',
            product_name: '',
            sub_product_name: '',
            member_type: '',
            policy_status: '',
            proposal_type: '',
            report_type: '',
            policy_start_date: null,
            policy_end_date: null,
            proposal_start_date: null,
            proposal_end_date: null
        });

        setSelectedFinancialYears(['2025-26']); // Reset to default

        setGroupBy({});

        setDisabledFields({
            product_name: true,
            sub_product_name: true,
            product: true,
            sub_product: true
        });

        setPage(0);
        setRowsPerPage(10);
        setViewMode('table');
        setChartType('bar');
    };

    // Then modify your existing useEffect for form initialization:
    useEffect(() => {
        // Reset form on mount 
        resetFormState();

        // Fetch required data
        dispatch(getAllAgentDetails());
        dispatch(getAllSubProducts());
        dispatch(fetchInsuranceCompanies());
        dispatch(getAllProducts());
        dispatch(fetchImfAgencyCodes());
        dispatch(fetchAllImfBranches());
        dispatch(getAllMasterProducts());
        dispatch(fetchInsuranceCompanyBranches());
        dispatch(getFinancialYears());

        // Handle page refresh - this will clear the form when the page is refreshed
        const handlePageRefresh = (event) => {
            // This will be triggered when the page is refreshed
            event.preventDefault();
            resetFormState();
            dispatch(clearReportData());
        };

        // Add event listener for page refresh
        window.addEventListener('beforeunload', handlePageRefresh);

        // Return cleanup function
        return () => {
            // Clear report data when leaving the page
            dispatch(clearReportData());
            // Remove event listener
            window.removeEventListener('beforeunload', handlePageRefresh);
        };
    }, [dispatch]);

    // To fetch the master product data when the insurance company name, product type and product name changes
    useEffect(() => {
        if (formData && formData.insurance_company_name) {
            if (formData.insurance_company_name && formData.product_type && !formData.product_name) {
                dispatch(getMasterProductByMainProductAndInsuranceCompany({
                    insuranceCompanyId: formData.insurance_company_name,
                    mainProductId: formData.product_type
                }));
            } else if (formData.insurance_company_name && formData.product_type && formData.product_name) {
                dispatch(getSubProductByProductDetails({
                    mainProductId: formData.product_type,
                    insuranceCompanyId: formData.insurance_company_name,
                    productMasterId: formData.product_name
                }));
            }
        }
    }, [formData.insurance_company_name, formData.product_type, formData.product_name, dispatch]);

    // Add this effect to reset selections when dependencies change
    useEffect(() => {
        // If insurance company is cleared, disable and clear product and sub-product
        // ONLY if insurance_company is not selected in Group By
        if (!formData.insurance_company_name && !groupBy.insurance_company) {
            setDisabledFields(prev => ({
                ...prev,
                product_name: true,
                product: true,
                sub_product_name: true,
                sub_product: true
            }));

            setFormData(prev => ({
                ...prev,
                product_name: '',
                sub_product_name: ''
            }));

            setGroupBy(prev => ({
                ...prev,
                product: false,
                sub_product: false
            }));
        }

        // If product is cleared, disable and clear sub-product
        // ONLY if product is not selected in Group By
        if (!formData.product_name && !groupBy.product) {
            setDisabledFields(prev => ({
                ...prev,
                sub_product_name: true,
                sub_product: true
            }));

            setFormData(prev => ({
                ...prev,
                sub_product_name: ''
            }));

            setGroupBy(prev => ({
                ...prev,
                sub_product: false
            }));
        }
    }, [formData.insurance_company_name, formData.product_name, groupBy.insurance_company, groupBy.product]);

    // Handle changes in form fields
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prevData => ({
            ...prevData,
            [name]: value
        }));
    };

    // Handle changes in autocomplete fields
    const handleAutocompleteDropdownChange = ({ name, value }) => {
        setFormData(prevData => ({
            ...prevData,
            [name]: value
        }));

        // If insurance company field changes, update disabled states
        if (name === 'insurance_company_name') {
            setDisabledFields(prev => ({
                ...prev,
                product_name: !value, // Enable product_name if insurance_company has a value
                product: !value && !groupBy.insurance_company, // Only disable product if insurance_company is not selected in Group By
            }));

            // Clear product and sub-product selections when insurance company changes
            if (formData.product_name || formData.sub_product_name) {
                setFormData(prev => ({
                    ...prev,
                    product_name: '',
                    sub_product_name: ''
                }));
            }

            // Clear product and sub-product group by selections ONLY if they're not already needed
            if ((groupBy.product || groupBy.sub_product) && !value && !groupBy.insurance_company) {
                setGroupBy(prev => ({
                    ...prev,
                    product: false,
                    sub_product: false
                }));
            }
        }

        // If product name field changes, update sub-product disabled state
        if (name === 'product_name') {
            setDisabledFields(prev => ({
                ...prev,
                sub_product_name: !value, // Enable sub_product_name if product_name has a value
                sub_product: !value && !groupBy.product // Only disable sub-product if product is not selected in Group By
            }));

            // Clear sub-product selections when product changes
            if (formData.sub_product_name) {
                setFormData(prev => ({
                    ...prev,
                    sub_product_name: ''
                }));
            }

            // Clear sub-product group by selection when product changes ONLY if it's not already needed
            if (groupBy.sub_product && !value && !groupBy.product) {
                setGroupBy(prev => ({
                    ...prev,
                    sub_product: false
                }));
            }
        }
    };

    // Handle date changes
    const handleDateChange = (name, date) => {
        setFormData(prevData => ({
            ...prevData,
            [name]: date
        }));
    };

    // Handle checkbox changes for financial years
    const handleFinancialYearChange = (event) => {
        const { value, checked } = event.target;

        if (checked) {
            // Don't allow more than 2 selections
            if (selectedFinancialYears.length >= 2) {
                alert("You can select a maximum of 2 financial years");
                return;
            }

            setSelectedFinancialYears([...selectedFinancialYears, value]);
        } else {
            setSelectedFinancialYears(selectedFinancialYears.filter(year => year !== value));
        }
    };

    // Handle checkbox changes for grouping options
    const handleGroupByChange = (event) => {
        const { name, checked } = event.target;

        // Prevent enabling disabled options
        if (disabledFields[name]) {
            return;
        }

        // Update the checkbox state
        setGroupBy(prevGroupBy => ({
            ...prevGroupBy,
            [name]: checked
        }));

        // If insurance company is selected in Group By, enable product option
        if (name === 'insurance_company' && checked) {
            setDisabledFields(prev => ({
                ...prev,
                product: false // Enable product when insurance company is selected
            }));
        }

        // If product is selected in Group By, enable sub-product option
        if (name === 'product' && checked) {
            setDisabledFields(prev => ({
                ...prev,
                sub_product: false // Enable sub-product when product is selected
            }));
        }

        // If insurance company is unchecked and no insurance company is selected in filters,
        // disable product again
        if (name === 'insurance_company' && !checked && !formData.insurance_company_name) {
            setDisabledFields(prev => ({
                ...prev,
                product: true, // Disable product
                sub_product: true // Disable sub-product
            }));

            // Also uncheck product and sub-product if they were checked
            setGroupBy(prev => ({
                ...prev,
                product: false,
                sub_product: false
            }));
        }

        // If product is unchecked and no product is selected in filters,
        // disable sub-product again
        if (name === 'product' && !checked && !formData.product_name) {
            setDisabledFields(prev => ({
                ...prev,
                sub_product: true // Disable sub-product
            }));

            // Also uncheck sub-product if it was checked
            setGroupBy(prev => ({
                ...prev,
                sub_product: false
            }));
        }

        // Count how many would be selected after this change
        const updatedGroupBy = { ...groupBy, [name]: checked };
        const selectedCount = Object.entries(updatedGroupBy)
            .filter(([key, value]) => value)
            .length;

        // Don't allow deselecting if it would result in less than 2 selections
        if (!checked && selectedCount < 2) {
            alert("You must select at least 2 grouping options");
            return;
        }
    };

    // Generate report with validation
    const handleGenerateReport = () => {
        const selectedGroupByCount = Object.values(groupBy).filter(Boolean).length;

        if (selectedFinancialYears.length === 0) {
            alert("Please select at least one financial year");
            return;
        }

        if (selectedFinancialYears.length > 2) {
            alert("You can select a maximum of 2 financial years");
            return;
        }

        if (selectedGroupByCount < 2) {
            alert("Please select at least 2 grouping options");
            return;
        }

        // Check if at least one filter field is selected
        const hasAtLeastOneFilter =
            formData.imf_code ||
            formData.imf_branch ||
            formData.agent_id ||
            formData.insurance_company_name ||
            formData.product_type ||
            formData.product_name ||
            formData.sub_product_name ||
            formData.member_type ||
            formData.policy_status ||
            formData.proposal_type ||
            formData.report_type ||
            formData.policy_start_date ||
            formData.policy_end_date ||
            formData.proposal_start_date ||
            formData.proposal_end_date;

        if (!hasAtLeastOneFilter) {
            alert("Please select at least one filter");
            return;
        }

        // Format dates if present
        const formattedData = { ...formData };
        if (formData.policy_start_date) {
            formattedData.policy_start_date = formData.policy_start_date.format('YYYY-MM-DD');
        }
        if (formData.policy_end_date) {
            formattedData.policy_end_date = formData.policy_end_date.format('YYYY-MM-DD');
        }
        if (formData.proposal_start_date) {
            formattedData.proposal_start_date = formData.proposal_start_date.format('YYYY-MM-DD');
        }
        if (formData.proposal_end_date) {
            formattedData.proposal_end_date = formData.proposal_end_date.format('YYYY-MM-DD');
        }

        // Dispatch the generateReport action with all filters
        dispatch(generateReport({
            ...formattedData,
            financial_years: selectedFinancialYears,
            group_by: Object.keys(groupBy).filter(key => groupBy[key])
        }));
    };

    // Export to PDF function
    const handleExportToPDF = () => {
        if (!reportData || !reportData.rows || reportData.rows.length === 0) return;

        const doc = new jsPDF();

        // Add title and date
        doc.setFontSize(18);
        doc.text('Prime Wealthcare Report', 105, 15, { align: 'center' });

        doc.setFontSize(10);
        doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 22, { align: 'center' });

        // Add filter info to the PDF
        doc.setFontSize(10);
        doc.text('Filters:', 15, 30);

        let filterY = 35;
        if (reportData.appliedFilters) {
            Object.entries(reportData.appliedFilters).forEach(([key, val]) => {
                // Safely handle potentially null/undefined values
                const displayValue = val !== null && val !== undefined ?
                    getFilterDisplayValue(key, val) : 'Not specified';
                doc.text(`${key}: ${displayValue}`, 15, filterY);
                filterY += 5;
            });
        }

        // Get the ordered columns
        const orderedColumns = getBusinessOrderedColumns(reportData.columns);

        // Create table headers and data
        const tableHeaders = orderedColumns.map(col => col.headerName);

        // Safely format table data with null checks
        const tableData = reportData.rows.map(row =>
            orderedColumns.map(col => {
                // Special handling for null/undefined values
                if (row[col.field] === null || row[col.field] === undefined) {
                    return '';
                }

                // Special handling for insurance company column
                if (col.field === 'insurance_company_name' && row.insurance_company_short_name) {
                    return row.insurance_company_short_name;
                }

                // Format currency values
                if (col.type === 'currency') {
                    const value = parseFloat(row[col.field] || 0);
                    return `₹${value.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`;
                }

                // Convert other values to strings safely
                return String(row[col.field]);
            })
        );

        // Calculate starting y-position for the table
        const tableY = filterY + 5;

        // Create the table
        doc.autoTable({
            head: [tableHeaders],
            body: tableData,
            startY: tableY,
            theme: 'grid',
            headStyles: {
                fillColor: [82, 138, 126],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
            },
            columnStyles: {
                // Apply right-alignment to numeric columns
                // You may need to adjust indices based on your actual column order
                [tableHeaders.length - 1]: { halign: 'right' }, // Gross Premium
                [tableHeaders.length - 2]: { halign: 'right' }, // GST
                [tableHeaders.length - 3]: { halign: 'right' }, // Net Premium
                [tableHeaders.length - 4]: { halign: 'right' }, // Total Policies
            }
        });

        // Add summary totals at the bottom of the table
        const finalY = doc.lastAutoTable.finalY + 10;
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');

        doc.text(`Total Policies: ${reportData.summary.total_policies.toLocaleString('en-IN')}`, 15, finalY);
        doc.text(`Total Net Premium: ₹${reportData.summary.total_net_premium.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`, 15, finalY + 5);
        doc.text(`Total GST: ₹${reportData.summary.total_gst.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`, 15, finalY + 10);
        doc.text(`Total Gross Premium: ₹${reportData.summary.total_gross_premium.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`, 15, finalY + 15);

        // Add footer with page numbers
        const pageCount = doc.internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setFont(undefined, 'normal');
            doc.text(`Page ${i} of ${pageCount}`, 105, doc.internal.pageSize.height - 10, { align: 'center' });
        }

        // Save the PDF
        doc.save(`PrimeWealthcare_Report_${new Date().toISOString().split('T')[0]}.pdf`);
    };

    // Export to Excel function
    const handleExportToExcel = () => {
        if (!reportData || !reportData.rows || reportData.rows.length === 0) return;

        // Get the ordered columns
        const orderedColumns = getBusinessOrderedColumns(reportData.columns);

        // Format data for Excel
        const excelData = reportData.rows.map(row => {
            const formattedRow = {};
            orderedColumns.forEach(col => {
                // Special handling for insurance company column - show only short name
                if (col.field === 'insurance_company_name' && row.insurance_company_short_name) {
                    formattedRow[col.headerName] = row.insurance_company_short_name;
                } else {
                    formattedRow[col.headerName] = col.type === 'currency'
                        ? parseFloat(row[col.field] || 0)
                        : row[col.field];
                }
            });
            return formattedRow;
        });

        // Add summary row
        const summaryRow = {};
        orderedColumns.forEach(col => {
            if (col === orderedColumns[0]) {
                summaryRow[col.headerName] = 'Total';
            } else if (col.field === 'total_count') {
                summaryRow[col.headerName] = reportData.summary.total_policies;
            } else if (col.field === 'total_net_premium') {
                summaryRow[col.headerName] = reportData.summary.total_net_premium;
            } else if (col.field === 'total_gst') {
                summaryRow[col.headerName] = reportData.summary.total_gst;
            } else if (col.field === 'total_gross_premium') {
                summaryRow[col.headerName] = reportData.summary.total_gross_premium;
            } else {
                summaryRow[col.headerName] = '';
            }
        });
        excelData.push(summaryRow);

        // Create workbook and worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(excelData);

        // Add styling for header and totals (limited options in XLSX)
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Report');

        // Generate and download file
        XLSX.writeFile(workbook, 'PrimeWealthcare_Report.xlsx');
    };

    // Financial years data
    const financialYears = [
        { value: '2025-26', label: '2025-26' },
        { value: '2024-25', label: '2024-25' },
        { value: '2023-24', label: '2023-24' },
        { value: '2022-23', label: '2022-23' },
        { value: '2021-22', label: '2021-22' },
    ];

    // Policy types
    const policyTypes = [
        { value: 'individual', label: 'Individual' },
        { value: 'family floater', label: 'Family Floater' }
    ];

    // Proposal status
    const proposalStatuses = [
        { value: 'new', label: 'New' },
        { value: 'renewal', label: 'Renewal' },
        { value: 'rollover', label: 'Rollover' },
        { value: 'migration', label: 'Migration' },
    ];

    // Policy status
    const policyStatuses = [
        { value: 'success', label: 'Success' },
        { value: 'pending', label: 'Pending' },
        { value: 'cancelled', label: 'Cancelled' }
    ];

    // Report types
    const reportTypes = [
        { value: 'ytd', label: 'YTD' },
        { value: 'mtd', label: 'MTD' },
    ];

    // Add a handler for chart type change
    const handleChartTypeChange = (event) => {
        setChartType(event.target.value);
    };

    // Sort data to ensure related items are grouped together
    const sortTableData = (data, columns) => {
        if (!data || data.length === 0) return data;

        // Get non-metric columns for sorting
        const sortColumns = columns
            .filter(col => !['total_count', 'total_net_premium', 'total_gst', 'total_gross_premium'].includes(col.field))
            .map(col => col.field);

        // Sort by multiple columns
        return [...data].sort((a, b) => {
            for (let col of sortColumns) {
                if (a[col] < b[col]) return -1;
                if (a[col] > b[col]) return 1;
            }
            return 0;
        });
    };

    // Process data to calculate rowspans for duplicate values
    const processTableDataWithRowspans = (data, columns) => {
        if (!data || data.length === 0) return { rows: [] };

        const processed = [];
        const spans = {};

        // Initialize span counters for each column
        columns.forEach(col => {
            if (!['total_count', 'total_net_premium', 'total_gst', 'total_gross_premium'].includes(col.field)) {
                spans[col.field] = { value: null, rowspan: 1, index: -1 };
            }
        });

        // Process each row
        data.forEach((row, rowIndex) => {
            const processedRow = { ...row, _spans: {} };

            // Check each column for spans
            columns.forEach(col => {
                const field = col.field;

                // Skip calculation for metrics columns
                if (['total_count', 'total_net_premium', 'total_gst', 'total_gross_premium'].includes(field)) {
                    return;
                }

                if (rowIndex > 0 && spans[field].value === row[field]) {
                    // Current value is same as previous row
                    spans[field].rowspan++;
                    processedRow._spans[field] = 0; // Mark as "don't display"

                    // Update the rowspan value in the first row of this span
                    processed[spans[field].index]._spans[field] = spans[field].rowspan;
                } else {
                    // New value, reset span counter
                    spans[field] = { value: row[field], rowspan: 1, index: rowIndex };
                    processedRow._spans[field] = 1;
                }
            });

            processed.push(processedRow);
        });

        return { rows: processed };
    };

    // Add this function to your Reports component
    const getOrderedColumns = (columns, groupBySelections) => {
        if (!columns || !columns.length || !groupBySelections) return columns;

        // Create a mapping of group by fields to their display priority
        // The lower the number, the higher the priority (appears more to the left)
        const groupByPriorities = {};
        const groupByFields = Object.keys(groupBySelections).filter(key => groupBySelections[key]);

        // Assign priorities based on user's group by selections
        groupByFields.forEach((field, index) => {
            // Map the group by field names to their corresponding column field names
            let columnField;
            switch (field) {
                case 'insurance_company':
                    columnField = 'insurance_company_name';
                    break;
                case 'product':
                    columnField = 'product_name';
                    break;
                case 'sub_product':
                    columnField = 'sub_product_name';
                    break;
                case 'agent':
                    columnField = 'agent_id';
                    break;
                case 'imf_branch':
                    columnField = 'imf_branch_name';
                    break;
                default:
                    columnField = field;
            }
            groupByPriorities[columnField] = index;
        });

        // Make a copy of the columns to sort
        const orderedColumns = [...columns];

        // Sort columns based on the group by priorities
        orderedColumns.sort((a, b) => {
            // If both columns are part of group by, sort by priority
            if (groupByPriorities[a.field] !== undefined && groupByPriorities[b.field] !== undefined) {
                return groupByPriorities[a.field] - groupByPriorities[b.field];
            }

            // If only column a is part of group by, it should come first
            if (groupByPriorities[a.field] !== undefined) {
                return -1;
            }

            // If only column b is part of group by, it should come first
            if (groupByPriorities[b.field] !== undefined) {
                return 1;
            }

            // For metric columns (ones not in group by), keep their original order
            // These are typically the last columns (count, premiums, etc.)
            return columns.indexOf(a) - columns.indexOf(b);
        });

        return orderedColumns;
    };

    // Add this function to reorder columns based on fixed business logic
    const getBusinessOrderedColumns = (columns) => {
        if (!columns || columns.length === 0) return columns;

        // Define the business order priority (lower number = higher priority = appears first)
        const businessOrderPriority = {
            'insurance_company_name': 1,
            'product_name': 2,
            'sub_product_name': 3,
            'agent_id': 4,
            'agent_name': 5,
            'imf_branch_name': 6,
            // Metrics always go last
            'total_count': 90,
            'total_net_premium': 91,
            'total_gst': 92,
            'total_gross_premium': 93
        };

        // Create a copy of the columns array
        return [...columns].sort((a, b) => {
            // Get priority for column a and b, default to 100 if not found
            const aPriority = businessOrderPriority[a.field] !== undefined ?
                businessOrderPriority[a.field] : 100;
            const bPriority = businessOrderPriority[b.field] !== undefined ?
                businessOrderPriority[b.field] : 100;

            // Sort based on priority
            return aPriority - bPriority;
        });
    };

    // Add this helper function to your component:

    const getFilterDisplayValue = (key, value) => {
        if (!value) return value;

        // Try to parse the value as an integer if it's a string number
        const numericValue = !isNaN(parseInt(value)) ? parseInt(value) : value;

        switch (key) {
            case 'IMF Code':
                // For IMF Code, just return the value as is (it's already the display value)
                return value;
            case 'IMF Branch':
                if (imfBranches && imfBranches.length > 0) {
                    const branch = imfBranches.find(b => b.id === numericValue);
                    return branch ? branch.branch_name : value;
                }
                return value;

            case 'Insurance Company':
                if (insuranceCompanies && insuranceCompanies.length > 0) {
                    const company = insuranceCompanies.find(c => c.id === numericValue);
                    if (company) {
                        // Show only short name if available, otherwise show full name
                        return company.short_name || company.insurance_company_name;
                    }
                }
                return value;

            case 'Agent':
                if (agents && agents.length > 0) {
                    const agent = agents.find(a => a.id === numericValue);
                    return agent ? `${agent.agent_id} - ${agent.full_name}` : value;
                }
                return value;

            case 'Product':
                if (masterProducts && masterProducts.length > 0) {
                    const product = masterProducts.find(p => p.id === numericValue);
                    return product ? product.product_name : value;
                }
                return value;

            case 'Sub Product':
                if (subProducts && subProducts.length > 0) {
                    const subProduct = subProducts.find(p => p.id === numericValue);
                    return subProduct ? subProduct.sub_product_name : value;
                }
                return value;

            case 'Product Type':
                if (mainProducts && mainProducts.length > 0) {
                    const productType = mainProducts.find(p => p.id === numericValue);
                    return productType ? productType.main_product : value;
                }
                return value;

            default:
                return value;
        }
    };

    // Convert your groupBy object to an array of field names
    const getSelectedGroupByFields = () => {
        const fields = [];
        if (groupBy.agent) fields.push('agent_id');
        if (groupBy.imf_branch) fields.push('imf_branch_name');
        if (groupBy.insurance_company) fields.push('insurance_company_name');
        if (groupBy.product) fields.push('product_name');
        if (groupBy.sub_product) fields.push('sub_product_name');
        return fields;
    };

    return (
        <Box sx={{ padding: { xs: '0 5px 5px', md: '0 40px 40px' }, width: '100%' }}>
            {/* Header */}
            <Box sx={{
                position: 'sticky',
                top: { xs: '140px', sm: '140px', md: '164px', lg: '164px' },
                zIndex: 101,
                backgroundColor: 'white',
                borderBottom: '2px solid #E0E0E0',
                padding: '10px 0',
                display: "flex"
            }}>
                <Grid container>
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{
                                width: '20px',
                                marginLeft: '20px',
                                backgroundColor: 'green'
                            }}
                        />
                        <Box sx={{ display: 'flex', alignItems: 'center', marginLeft: 2 }}>
                            <Typography variant="h6">Reports</Typography>
                        </Box>
                    </Grid>
                    <Grid item xs={4} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', paddingRight: 2 }}>
                        <Button
                            variant="contained"
                            onClick={handleGenerateReport}
                            disabled={loading}
                            sx={{
                                backgroundColor: '#528A7E',
                                '&:hover': {
                                    backgroundColor: '#3a6259',
                                }
                            }}
                        >
                            {loading ? 'Generating...' : 'Generate Report'}
                        </Button>
                    </Grid>
                </Grid>
            </Box>

            {/* Filters Card */}
            <Card sx={{ mt: 3, boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                        Filter Options
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                    <Grid container spacing={3}>
                        {/* IMF Code */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="IMF Code"
                                name="imf_code"
                                options={insuranceBranches?.map(code => ({
                                    label: code.imf_code,
                                    value: code.imf_code
                                })) || []}
                                value={formData.imf_code}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                            />
                        </Grid>

                        {/* IMF Branch */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="IMF Branch"
                                name="imf_branch"
                                options={imfBranches?.map(branch => ({
                                    label: branch.branch_name,
                                    value: branch.id
                                })) || []}
                                value={formData.imf_branch}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Agent Code */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="Agent Code"
                                name="agent_id"
                                options={agents?.filter(agent => agent.status === 1).map(agent => {
                                    return {
                                        label: `${agent.agent_id}(${agent.full_name})`,
                                        value: agent.id
                                    }
                                })}
                                value={formData.agent_id}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Insurance Company */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="Insurance Company"
                                name="insurance_company_name"
                                options={insuranceCompanies?.map(company => ({
                                    label: company.insurance_company_name,
                                    value: company.id
                                })) || []}
                                value={formData.insurance_company_name}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Main Product */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="Product Type"
                                name="product_type"
                                options={mainProducts?.map(product => ({
                                    label: product.main_product,
                                    value: product.id
                                })) || []}
                                value={formData.product_type}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Product */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="Product Name"
                                name="product_name"
                                options={masterProducts?.map(product => ({
                                    label: product.product_name,
                                    value: product.id
                                })) || []}
                                value={formData.product_name}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                                disabled={disabledFields.product_name}
                                helperText={disabledFields.product_name ? "Select Insurance Company first" : ""}
                            />
                        </Grid>

                        {/* Sub Product */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <AutocompleteDropdown
                                label="Sub Product"
                                name="sub_product_name"
                                options={subProducts?.map(product => ({
                                    label: product.sub_product_name,
                                    value: product.id
                                })) || []}
                                value={formData.sub_product_name}
                                onChange={handleAutocompleteDropdownChange}
                                fullWidth
                                disabled={disabledFields.sub_product_name}
                                helperText={disabledFields.sub_product_name ? "Select Product Name first" : ""}
                            />
                        </Grid>

                        {/* Policy Type */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Member Type"
                                name="member_type"
                                options={policyTypes}
                                value={formData.member_type}
                                onChange={handleChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Proposal Status */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Proposal Type"
                                name="proposal_type"
                                options={proposalStatuses}
                                value={formData.proposal_type}
                                onChange={handleChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Policy Status */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Policy Status"
                                name="policy_status"
                                options={policyStatuses}
                                value={formData.policy_status}
                                onChange={handleChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Report Type */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Report Type"
                                name="report_type"
                                options={reportTypes}
                                value={formData.report_type}
                                onChange={handleChange}
                                fullWidth
                            />
                        </Grid>

                        {/* Policy Start Date */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Policy Start From Date"
                                    value={formData.policy_start_date}
                                    onChange={(date) => handleDateChange('policy_start_date', date)}
                                    format="DD/MM/YYYY"
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            variant: "outlined"
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </Grid>

                        {/* Policy End Date */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Policy End To Date"
                                    value={formData.policy_end_date}
                                    onChange={(date) => handleDateChange('policy_end_date', date)}
                                    format="DD/MM/YYYY"
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            variant: "outlined"
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </Grid>

                        {/* Proposal Start Date */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Proposal Start From "
                                    value={formData.proposal_start_date}
                                    onChange={(date) => handleDateChange('proposal_start_date', date)}
                                    format="DD/MM/YYYY"
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            variant: "outlined"
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </Grid>

                        {/* Proposal End Date */}
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Proposal End To"
                                    value={formData.proposal_end_date}
                                    onChange={(date) => handleDateChange('proposal_end_date', date)}
                                    format="DD/MM/YYYY"
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            variant: "outlined"
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </Grid>

                        {/* Financial Years - Horizontal Layout with selection hint */}
                        <Grid item xs={12}>
                            <Paper elevation={1} sx={{ padding: 2, mb: 2 }}>
                                <Box display="flex" justifyContent="space-between" alignItems="center">
                                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                        Financial Years
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        (Select max 2years)
                                    </Typography>
                                </Box>
                                <Divider sx={{ mb: 2, mt: 1 }} />

                                <FormGroup row>
                                    {financialYears.map((year) => (
                                        <FormControlLabel
                                            key={year.value}
                                            control={
                                                <Checkbox
                                                    checked={selectedFinancialYears.includes(year.value)}
                                                    onChange={handleFinancialYearChange}
                                                    value={year.value}
                                                />
                                            }
                                            label={year.label}
                                            sx={{ mr: 4 }}
                                        />
                                    ))}
                                </FormGroup>
                            </Paper>
                        </Grid>

                        {/* Group By Section - Horizontal Layout with selection hint */}
                        <Grid item xs={12}>
                            <Paper elevation={1} sx={{ padding: 2, mb: 2 }}>
                                <Box display="flex" justifyContent="space-between" alignItems="center">
                                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                        Group By
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        (Select atleast 2options)
                                    </Typography>
                                </Box>
                                <Divider sx={{ mb: 2, mt: 1 }} />

                                <FormGroup row>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={groupBy.agent}
                                                onChange={handleGroupByChange}
                                                name="agent"
                                            />
                                        }
                                        label="Agent"
                                        sx={{ mr: 4 }}
                                    />
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={groupBy.imf_branch}
                                                onChange={handleGroupByChange}
                                                name="imf_branch"
                                            />
                                        }
                                        label="IMF Branch"
                                        sx={{ mr: 4 }}
                                    />
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={groupBy.insurance_company}
                                                onChange={handleGroupByChange}
                                                name="insurance_company"
                                            />
                                        }
                                        label="Insurance Company"
                                        sx={{ mr: 4 }}
                                    />
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={groupBy.product}
                                                onChange={handleGroupByChange}
                                                name="product"
                                                disabled={disabledFields.product}
                                            />
                                        }
                                        label="Product"
                                        sx={{ mr: 4 }}
                                    />
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={groupBy.sub_product}
                                                onChange={handleGroupByChange}
                                                name="sub_product"
                                                disabled={disabledFields.sub_product}
                                            />
                                        }
                                        label="Sub Product"
                                        sx={{ mr: 4 }}
                                    />
                                </FormGroup>
                            </Paper>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            {/* Report Results */}
            {loading ? (
                <Box sx={{ mt: 3, p: 5, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <CircularProgress color="primary" />
                    <Typography variant="body1" sx={{ ml: 2 }}>Generating report...</Typography>
                </Box>
            ) : reportData ? (
                <>
                    {/* View Mode Toggle */}
                    <Box sx={{ mb: { xs: 2, sm: 0 } }}>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>Report Results</Typography>
                        <Tabs
                            value={viewMode}
                            onChange={(e, newValue) => setViewMode(newValue)}
                            aria-label="report view mode"
                        >
                            <Tab
                                icon={<TableChartIcon />}
                                label="Table"
                                value="table"
                                iconPosition="start"
                            />
                            <Tab
                                icon={<BarChartIcon />}
                                label="Chart"
                                value="chart"
                                iconPosition="start"
                            />
                        </Tabs>
                    </Box>

                    {/* Table View */}
                    {viewMode === 'table' && (
                        <Card sx={{ mt: 2, boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                                    {/* Tab View Toggle and Report Header */}
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: { xs: 'column', sm: 'row' },
                                        justifyContent: 'space-between',
                                        alignItems: { xs: 'flex-start', sm: 'center' },
                                        mb: 2
                                    }}>

                                        <Box>
                                            <Button
                                                variant="outlined"
                                                startIcon={<DownloadIcon />}
                                                onClick={handleExportToPDF}
                                                sx={{ mr: 2 }}
                                            >
                                                Export to PDF
                                            </Button>
                                            <Button
                                                variant="outlined"
                                                startIcon={<FileDownloadIcon />}
                                                onClick={handleExportToExcel}
                                            >
                                                Export to Excel
                                            </Button>
                                        </Box>
                                    </Box>

                                    {/* Table Content */}
                                    <TableContainer component={Paper} sx={{ maxHeight: 500 }}>
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                p: 2,
                                                backgroundColor: '#f9f9f9',
                                                borderBottom: '1px solid #e0e0e0'
                                            }}
                                        >
                                            <strong>Filters:</strong> {' '}
                                            {reportData.appliedFilters ?
                                                Object.entries(reportData.appliedFilters)
                                                    .map(([key, val]) => `${key}: ${getFilterDisplayValue(key, val)}`)
                                                    .join(' | ')
                                                : 'None'
                                            }
                                        </Typography>
                                        <Table stickyHeader aria-label="report results table">
                                            <TableHead>
                                                {(() => {
                                                    // Apply the business ordering to columns
                                                    const orderedColumns = getBusinessOrderedColumns(reportData.columns);

                                                    return orderedColumns.map((column) => (
                                                        <TableCell
                                                            key={column.field}
                                                            align={column.type === 'number' || column.type === 'currency' ? 'right' : 'left'}
                                                            sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}
                                                        >
                                                            {column.headerName}
                                                        </TableCell>
                                                    ));
                                                })()}
                                            </TableHead>
                                            <TableBody>
                                                {(() => {
                                                    // Get the ordered columns
                                                    const orderedColumns = getBusinessOrderedColumns(reportData.columns);

                                                    // Process the data for rowspans - first sort, then calculate spans
                                                    const currentPageData = reportData.rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

                                                    // Sort data to match the column order
                                                    const sortedData = sortTableData(currentPageData, orderedColumns);
                                                    const processedData = processTableDataWithRowspans(sortedData, orderedColumns);

                                                    return processedData.rows.map((row, index) => (
                                                        <TableRow key={index} hover>
                                                            {orderedColumns.map((column, colIndex) => {
                                                                // Check if this cell should be rendered
                                                                if (row._spans[column.field] === 0) {
                                                                    // Don't render this cell (it's part of a rowspan)
                                                                    return null;
                                                                }

                                                                // Special handling for insurance company column to show ONLY short name
                                                                if (column.field === 'insurance_company_name' && row.insurance_company_short_name) {
                                                                    return (
                                                                        <TableCell
                                                                            key={`${index}-${column.field}`}
                                                                            align={column.type === 'number' || column.type === 'currency' ? 'right' : 'left'}
                                                                            rowSpan={row._spans[column.field] || 1}
                                                                            sx={row._spans[column.field] > 1 ? {
                                                                                backgroundColor: '#fafafa',
                                                                                verticalAlign: 'top',
                                                                                borderRight: '1px solid #f0f0f0'
                                                                            } : {}}
                                                                        >
                                                                            {row.insurance_company_short_name}
                                                                        </TableCell>
                                                                    );
                                                                }

                                                                return (
                                                                    <TableCell
                                                                        key={`${index}-${column.field}`}
                                                                        align={column.type === 'number' || column.type === 'currency' ? 'right' : 'left'}
                                                                        rowSpan={row._spans[column.field] || 1}
                                                                        sx={row._spans[column.field] > 1 ? {
                                                                            backgroundColor: '#fafafa',
                                                                            verticalAlign: 'top',
                                                                            borderRight: '1px solid #f0f0f0'
                                                                        } : {}}
                                                                    >
                                                                        {column.type === 'currency'
                                                                            ? `₹${parseFloat(row[column.field] || 0).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
                                                                            : row[column.field]}
                                                                    </TableCell>
                                                                );
                                                            })}
                                                        </TableRow>
                                                    ));
                                                })()}

                                                {/* Summary Row */}
                                                <TableRow sx={{ backgroundColor: '#f0f0f0', fontWeight: 'bold' }}>
                                                    {(() => {
                                                        const orderedColumns = getBusinessOrderedColumns(reportData.columns);

                                                        return orderedColumns.map((column, colIndex) => {
                                                            // First column shows "Total"
                                                            if (colIndex === 0) {
                                                                const nonMetricColumnsCount = orderedColumns.filter(col =>
                                                                    !['total_count', 'total_net_premium', 'total_gst', 'total_gross_premium'].includes(col.field)
                                                                ).length;

                                                                return (
                                                                    <TableCell
                                                                        key={`summary-${column.field}`}
                                                                        colSpan={nonMetricColumnsCount}
                                                                        sx={{ fontWeight: 'bold' }}
                                                                    >
                                                                        Total
                                                                    </TableCell>
                                                                );
                                                            }
                                                            // Skip cells that are part of the first column's colSpan
                                                            else if (colIndex > 0 && !['total_count', 'total_net_premium', 'total_gst', 'total_gross_premium'].includes(column.field)) {
                                                                return null;
                                                            }
                                                            // Show total policies count
                                                            else if (column.field === 'total_count') {
                                                                return (
                                                                    <TableCell
                                                                        key={`summary-${column.field}`}
                                                                        align="right"
                                                                        sx={{ fontWeight: 'bold' }}
                                                                    >
                                                                        {reportData.summary.total_policies}
                                                                    </TableCell>
                                                                );
                                                            }
                                                            // Other metric columns
                                                            else if (['total_net_premium', 'total_gst', 'total_gross_premium'].includes(column.field)) {
                                                                const value = reportData.summary[column.field];
                                                                return (
                                                                    <TableCell
                                                                        key={`summary-${column.field}`}
                                                                        align="right"
                                                                        sx={{ fontWeight: 'bold' }}
                                                                    >
                                                                        ₹{value.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                                                    </TableCell>
                                                                );
                                                            }
                                                            return null;
                                                        }).filter(Boolean);
                                                    })()}
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </TableContainer>

                                    <TablePagination
                                        rowsPerPageOptions={[10, 25, 50, 100]}
                                        component="div"
                                        count={reportData.rows.length}
                                        rowsPerPage={rowsPerPage}
                                        page={page}
                                        onPageChange={handleChangePage}
                                        onRowsPerPageChange={handleChangeRowsPerPage}
                                    />
                                </Box>
                            </CardContent>
                        </Card>
                    )}

                    {/* Chart View */}
                    {viewMode === 'chart' && (
                        <Card sx={{ mt: 2, boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                            <CardContent>
                                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                                    {/* Tab View Toggle and Report Header */}
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: { xs: 'column', sm: 'row' },
                                        justifyContent: 'space-between',
                                        alignItems: { xs: 'flex-start', sm: 'center' },
                                        mb: 2
                                    }}>

                                        {/* Chart Type Selection */}
                                        {/*  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                                            <Typography variant="body2" sx={{ mr: 1 }}>Chart Type:</Typography>
                                            <Button
                                                variant={chartType === 'bar' ? 'contained' : 'outlined'}
                                                onClick={() => setChartType('bar')}
                                                size="small"
                                            >
                                                Bar
                                            </Button>
                                            <Button
                                                variant={chartType === 'line' ? 'contained' : 'outlined'}
                                                onClick={() => setChartType('line')}
                                                size="small"
                                            >
                                                Line
                                            </Button>
                                            <Button
                                                variant={chartType === 'pie' ? 'contained' : 'outlined'}
                                                onClick={() => setChartType('pie')}
                                                size="small"
                                            >
                                                Pie
                                            </Button>
                                        </Box> */}
                                    </Box>

                                    {/* Chart Component */}
                                    <ReportCharts
                                        reportData={{
                                            ...reportData,
                                            columns: getBusinessOrderedColumns(reportData.columns),
                                            groupByFields: getSelectedGroupByFields() // Add this line
                                        }}
                                        //selectedChartType={chartType}
                                        page={page}
                                        rowsPerPage={rowsPerPage}
                                        agents={agents} // Pass agents data for mapping agent_id to agent_id string
                                    />
                                </Box>
                            </CardContent>
                        </Card>
                    )}
                </>
            ) : (
                <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="body1" color="textSecondary">
                        Report results will be displayed here after generating a report
                    </Typography>
                </Box>
            )}

            {/* Applied Filters Display */}
            {/*  {reportData && reportData.appliedFilters && Object.keys(reportData.appliedFilters).length > 0 && (
                <Card sx={{ mt: 3, boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                    <CardContent>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                            Applied Filters
                        </Typography>
                        <Divider sx={{ mb: 2 }} />
                        <Grid container spacing={2}>
                            {Object.entries(reportData.appliedFilters).map(([key, value]) => (
                                <Grid item xs={6} sm={4} md={3} lg={2} key={key}>
                                    <Paper 
                                        elevation={0} 
                                        sx={{ 
                                            p: 1, 
                                            bgcolor: '#f5f5f5',
                                            borderRadius: 1,
                                            height: '100%'
                                        }}
                                    >
                                        <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                                            {key}:
                                        </Typography>
                                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                            {value}
                                        </Typography>
                                    </Paper>
                                </Grid>
                            ))}
                            
                            {/* Always show financial years */}
            {/*   <Grid item xs={6} sm={4} md={3} lg={2}>
                                <Paper 
                                    elevation={0} 
                                    sx={{ 
                                        p: 1, 
                                        bgcolor: '#f5f5f5',
                                        borderRadius: 1,
                                        height: '100%'
                                    }}
                                >
                                    <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                                        Financial Years:
                                    </Typography>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        {selectedFinancialYears.join(', ')}
                                    </Typography> 
                                </Paper>
                            </Grid> */}

            {/* Always show grouping */}
            {/*  <Grid item xs={6} sm={4} md={3} lg={2}>
                                <Paper 
                                    elevation={0} 
                                    sx={{ 
                                        p: 1, 
                                        bgcolor: '#f5f5f5',
                                        borderRadius: 1,
                                        height: '100%'
                                    }}
                                >
                                    <Typography variant="caption" color="textSecondary" sx={{ display: 'block' }}>
                                        Grouped By:
                                    </Typography>
                                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                        {Object.keys(groupBy)
                                            .filter(key => groupBy[key])
                                            .map(key => {
                                                // Convert camelCase to Title Case with spaces
                                                return key.replace(/_/g, ' ')
                                                    .replace(/\b\w/g, letter => letter.toUpperCase());
                                            })
                                            .join(', ')}
                                    </Typography>
                                </Paper>
                            </Grid>
                        </Grid>
                    </CardContent>
                </Card>
            )}
 */}
            {/* Error Message */}
            {error && (
                <Box sx={{ mt: 2, p: 2, bgcolor: '#ffebee', borderRadius: 1 }}>
                    <Typography variant="body2" color="error">
                        {error.message || "An error occurred while generating the report"}
                    </Typography>
                </Box>
            )}
        </Box>
    );
};

export default Reports;