const express = require('express');
const router = express.Router();
const locationsController = require('../Controllers/locationsController');

// Route to get all locations
router.get('/', locationsController.getAllLocations);

// Route to create a new location (POST)
router.post('/', locationsController.createLocation);

// Route to update a location by pincode (PUT)
router.put('/:id', locationsController.updateLocation);

// Route to delete a location by pincode (DELETE)
router.delete('/:pincode/:city', locationsController.deleteLocation);

router.get('/:id', locationsController.getLocationById);

// Route to get location details by pincode (GET)
router.get('/pincode/:pincode', locationsController.getLocationByPincode); 

// Route to get location details by pincode and city (GET)
router.get('/pincode/:pincode/:city', locationsController.getLocationByPincodeAndCity);

// Route to get location and sub area details by pincode
router.get('/details/:pincode', locationsController.getLocationAndSubAreaByPincode);

router.get('/search/:query', locationsController.getLocationBySearch);

// Route to get locations by criteria 
router.get('/criteria/:criteria', locationsController.getLocationByCriteria)

module.exports = router;
