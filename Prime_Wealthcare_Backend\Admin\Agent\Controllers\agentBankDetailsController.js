const AgentMaster = require('../Models/agentBankDetailsModel'); // Assuming a similar model exists

// Create new Agent
exports.createAgentBankDetails = async (req, res, next) => {
    try {
        const AgentData = req.body;
        await AgentMaster.create(AgentData);
        res.status(200).json({ message: 'Agent BankDetails created successfully' });
    } catch (error) {
        next(error);
    }
};

// Get all Agents
exports.getAgentBankDetails = async (req, res, next) => {
    try {
        const data = await AgentMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get Agent by ID
exports.getAgentBankDetailsById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const Agent = await AgentMaster.findById(id);
        if (Agent) {
            res.status(200).json(Agent);
        } else {
            res.status(404).json({ message: 'Agent BankDetails not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Get Agent by Employee ID
exports.getAgentBankDetailsByAgentId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const Agent = await AgentMaster.findByAgentId(id);
        res.status(200).json(Agent);
    } catch (error) {
        next(error);
    }
};


// Update Agent by ID
exports.updateAgentBankDetails = async (req, res, next) => {
    try {
        const { id } = req.params;
        const AgentData = req.body;
        const data = await AgentMaster.update(id, AgentData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Delete an Agent by ID
exports.deleteAgentBankDetails = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await AgentMaster.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'BankDetails not found' });
        }
        res.status(204).json({ message: 'Agent deleted successfully' });
    } catch (error) {
        next(error);
    }
};

// Delete First Bank Details by ID
exports.deleteFirstBankById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await AgentMaster.deleteFirstBankById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'First Bank Details not found' });
        }
        res.status(204).json({ message: 'First Bank Details deleted successfully' });
    } catch (error) {
        next(error);
    }
};

// Delete Second Bank Details by ID
exports.deleteSecondBankById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await AgentMaster.deleteSecondBankById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Second Bank Details not found' });
        }
        res.status(204).json({ message: 'Second Bank Details deleted successfully' });
    } catch (error) {
        next(error);
    }
};

