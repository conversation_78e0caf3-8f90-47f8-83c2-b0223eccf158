// database.js
const knex = require('knex');
const config = require('./knexfile.js'); // Import your Knex configuration
const { exec } = require('child_process');

async function checkAndCreateDatabase() {
    const db = knex({
        client: 'mysql2', // Use the appropriate client
        connection: {
            host: config.development.connection.host,
            user: config.development.connection.user,
            password: config.development.connection.password,
        },
    });

    const dbName = config.development.connection.database;

    try {
        // Check if the database exists
        const result = await db.raw(`SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?`, [dbName]);

        if (result[0].length === 0) {
            // If the database does not exist, create it
            await db.raw(`CREATE DATABASE ??`, [dbName]);
            console.log(`Database ${dbName} created successfully.`);

            // Run migrations and seeds after creating the database
            runMigrationsAndSeeds();
        } else {
            console.log(`Database ${dbName} already exists.`);

            // Check if the database has tables
            const tablesResult = await db.raw(`SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ?`, [dbName]);
            if (tablesResult[0].length === 0) {
                console.log(`Database ${dbName} has no tables.`);

                // Run migrations and seeds if the database has no tables
                runMigrationsAndSeeds();
            } else {
                console.log(`Database ${dbName} has tables.`);
            }
        }
    } catch (error) {
        console.error('Error checking or creating the database:', error);
    } finally {
        // Destroy the connection to prevent memory leaks
        await db.destroy();
    }
}

function runMigrationsAndSeeds() {
    exec('npx knex migrate:latest', (error, stdout, stderr) => {
        if (error) {
            console.error(`Error running migrations: ${error.message}`);
            return;
        }
        console.log(`Migration output: ${stdout}`);
        console.log('Migrations completed successfully.');

        exec('npx knex seed:run', (error, stdout, stderr) => {
            if (error) {
                console.error(`Error running seeds: ${error.message}`);
                return;
            }
            console.log(`Seed output: ${stdout}`);
            console.log('Seeding completed successfully.');
        });
    });
}

module.exports = { checkAndCreateDatabase }; // Export the function
