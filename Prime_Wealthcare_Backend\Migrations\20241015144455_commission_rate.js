exports.up = function (knex) {
  return knex.schema.createTable('commission_rate', (table) => {
    table.increments('id').primary();

    // Foreign keys
    table.integer('insurance_company_id').unsigned().notNullable();
    table.foreign('insurance_company_id').references('id').inTable('insurance_company').onDelete('CASCADE');

    table.integer('main_product_id').unsigned().notNullable();
    table.foreign('main_product_id').references('id').inTable('main_product').onDelete('CASCADE');

    table.integer('product_master_id').unsigned().notNullable();
    table.foreign('product_master_id').references('id').inTable('product_master').onDelete('CASCADE');

    table.integer('sub_product_id').unsigned().notNullable();
    table.foreign('sub_product_id').references('id').inTable('sub_product').onDelete('CASCADE');

    table.string('commission_source', 255).notNullable(); // IRDA, ORC etc
    table.string('commission_type', 255).notNullable(); // Receivable, Payable, etc
    table.string('policy_type', 255).notNullable(); // New, Renewal, etc
    table.decimal('fixed_percentage', 5, 2).notNullable(); // New field for fixed percentage

    // Extra % and other details
    table.integer('range_from').unsigned().nullable();
    table.integer('range_to').unsigned().nullable();
    table.decimal('extra_percentage', 5, 2).nullable();
    table.date('effective_from').nullable();
    table.date('effective_to').nullable();
    table.string('calculated_on').nullable();
    table.integer('range_from2').unsigned().nullable();
    table.integer('range_to2').unsigned().nullable();
    table.decimal('extra_percentage2', 5, 2).nullable();
    table.date('effective_from2').nullable();
    table.date('effective_to2').nullable();

    table.boolean('status').notNullable().defaultTo(true);
    table.timestamps(true, true); // Created at & Updated at
  });
};

exports.down = function (knex) {
  return knex.schema.dropTableIfExists('commission_rate');
};