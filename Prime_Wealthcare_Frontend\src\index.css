* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* scrollbar-width: none; */
    font-family: 'Poppins', sans-serif;
}

/* Flex column box */
.flex-column-box {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Flex row box */
.flex-row-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Module icon */
.module-icon {
    width: 20px;
    padding: 10px 30px;
    margin-left: 20px;
    background-color: green;
}

/* Button group */
.button-group {
    border-radius: 1px;
}

.button-new {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.button-export {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-right: 8px;
}

/* Drop down and search */
.dropdown-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -1rem;
    padding-inline: 1rem;
    margin-left: 5rem;
}

/* Custom table */
.custom-table {
    margin-top: -1rem;
    max-height: 400px;
}