const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');

// Constants
const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTION; 
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;
const DEFAULT_CONFIG = {
    VENDOR_CODE: VENDOR_CODE,
    AGENT_CODE: 600113084,
    BR<PERSON>CH_CODE: '24',
    MAJOR_CLASS: 'FAT',
    CONTRACT_TYPE: 'FAT',
    POLICY_TYPE: 'HTI'
};


const createSoapEnvelope = (data) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
        <soapenv:Body>
            <tem:CreatePolicy>
            <!--Optional:-->
            <tem:Product>AdvantageTopup</tem:Product>
            <!--Optional:-->
            <tem:XML>
                <![CDATA[<Root>
                    <Uid>${data.Uid}</Uid>
  <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
                    <SentToOutSourcePrint></SentToOutSourcePrint>
                    <WinNo />
                    <ApplicationNo />
                    <PolicyHeader>
                        <PolicyStartDate>${data.PolicyHeader.PolicyStartDate}</PolicyStartDate>
                        <PolicyEndDate>${data.PolicyHeader.PolicyEndDate}</PolicyEndDate>
                        <AgentCode>60048599</AgentCode>
                        <BranchCode>51</BranchCode>
                        <MajorClass>FAT</MajorClass>
                        <ContractType>FAT</ContractType>
                        <METHOD>ENQ</METHOD>
                        <PolicyIssueType>I</PolicyIssueType>
                        <PolicyNo />
                        <ClientID></ClientID>
                        <ReceiptNo />
                    </PolicyHeader>
                    <POS_MISP>
                        <Type />
                        <PanNo />
                    </POS_MISP>
                    <Client>
                        <ClientCategory />
                        <ClientType>I</ClientType>
                        <CreationType>C</CreationType>
                        <Salutation>MR</Salutation>
                        <FirstName></FirstName>
                        <LastName></LastName>
                        <DOB>16/10/1997</DOB>
                        <Gender>M</Gender>
                        <MaritalStatus>S</MaritalStatus>
                        <Occupation>BUSM</Occupation>
                        <PANNo />
                        <GSTIN />
                        <AadharNo />
                        <CKYCNo></CKYCNo>
                        <CKYCRefNo />
                        <EIANo />
                        <Address1>
                            <AddrLine1>Thane </AddrLine1>
                            <AddrLine2>West </AddrLine2>
                            <AddrLine3>Mumbai </AddrLine3>
                            <Landmark />
                            <Pincode>${data.Client.Address1.Pincode}</Pincode>
                            <City>Thane</City>
                            <State>Maharashtra</State>
                            <Country>IND</Country>
                            <AddressType>R</AddressType>
                            <HomeTelNo />
                            <OfficeTelNo />
                            <FAXNO />
                            <MobileNo>9145328306</MobileNo>
                            <EmailAddr><EMAIL></EmailAddr>
                        </Address1>
                        <Address2>
                            <AddrLine1> </AddrLine1>
                            <AddrLine2></AddrLine2>
                            <AddrLine3></AddrLine3>
                            <Landmark />
                            <Pincode>400607</Pincode>
                            <City></City>
                            <State></State>
                            <Country></Country>
                            <AddressType>P</AddressType>
                            <HomeTelNo />
                            <OfficeTelNo />
                            <FAXNO />
                            <MobileNo></MobileNo>
                            <EmailAddr></EmailAddr>
                        </Address2>
                        <VIPFlag>N</VIPFlag>
                        <VIPCategory />
                    </Client>
                    <Receipt>
                        <UniqueTranKey></UniqueTranKey>
                        <CheckType />
                        <BSBCode />
                        <TransactionDate></TransactionDate>
                        <ReceiptType>IVR</ReceiptType>
                        <Amount></Amount>
                        <TCSAmount />
                        <TranRefNo></TranRefNo>
                        <TranRefNoDate></TranRefNoDate>
                    </Receipt>
                    <Risk>
                        <PolicyType>${data.Risk.PolicyType}</PolicyType>
                        <Duration>${data.Risk.Duration}</Duration>
                        <Installments>FULL</Installments>
                        <PaymentType>Cash</PaymentType>
                        <IsFgEmployee>N</IsFgEmployee>
                        <BranchReferenceID />
                        <FGBankBranchStaffID />
                        <BankStaffID />
                        <BankCustomerID />
                        <BancaChannel />
                        <PartnerRefNo />
                        <PayorID />
                        <PayerName />
                        <BeneficiaryDetails>
                            ${data.BeneficiaryDetails.Member.map(member => `
                                <Member>
                                    <MemberId>${member.MemberId}</MemberId>
                                    <AbhaNo />
                                    <InsuredName>Chetan Balwant Kamble</InsuredName>
                                    <InsuredDob>${member.InsuredDob}</InsuredDob>
                                    <InsuredGender>M</InsuredGender>
                                    <InsuredOccpn>BUSM</InsuredOccpn>
                                    <SumInsured>${member.SumInsured}</SumInsured>
                                    <Deductible>${member.Deductible}</Deductible>
                                    <Plantype>${member.PlanType}</Plantype>
                                    <DeductibleDiscount>50000</DeductibleDiscount>
                                    <Relation>${member.Relation}</Relation>
                                    <NomineeName>Pm</NomineeName>
                                    <NomineeRelation>Brother</NomineeRelation>
                                    <NomineeAge>27</NomineeAge>
                                    <Height>154.94</Height>
                                    <Weight>70</Weight>
                                    <AppointeeName>0</AppointeeName>
                                    <AptRelWithNominee>0</AptRelWithNominee>
                                    <MedicalLoading />
                                    ${generateNomineeDetailsXML(member.NomineeDetails)}
                                </Member>
                            `).join('')}
                        </BeneficiaryDetails>
                    </Risk>
                </Root>]]>
            </tem:XML>
        </tem:CreatePolicy>
    </soapenv:Body>
</soapenv:Envelope>`

async function sendAdvTopupSOAPRequest(data) {
    try {
        // Prepare request data
        const modifiedData = prepareRequestData(data);
        const soapEnvelope = createSoapEnvelope(modifiedData);

        // Send request
        const response = await sendRequest(soapEnvelope);

        // Process response
        return await processResponse(response.data);

    } catch (error) {
        console.error('SOAP Request Error:', {
            message: error.message,
            response: error.response?.data,
            status: error.response?.status
        });
        throw new Error(`SOAP Request Failed: ${error.message}`);
    }
}

// Helper Functions
function prepareRequestData(data) {
    return {
        ...data,
        Product: 'AdvantageTopup',
        VendorCode: DEFAULT_CONFIG.VENDOR_CODE,
        VendorUserId: DEFAULT_CONFIG.VENDOR_CODE,
        PolicyHeader: {
            ...data.PolicyHeader,
            AgentCode: DEFAULT_CONFIG.AGENT_CODE,
            BranchCode: DEFAULT_CONFIG.BRANCH_CODE,
            MajorClass: DEFAULT_CONFIG.MAJOR_CLASS,
            ContractType: DEFAULT_CONFIG.CONTRACT_TYPE
        },
        Risk: {
            ...data.Risk,
            PolicyType: DEFAULT_CONFIG.POLICY_TYPE
        }
    };
}

async function sendRequest(soapEnvelope) {
    const headers = {
        "Content-Type": "text/xml; charset=utf-8",
        SOAPAction: SOAP_ACTION,
    };
    return await axios.post(SOAP_API_URL, soapEnvelope, { headers });
}

async function processResponse(responseData) {
    const parsedResponse = await parseStringPromise(responseData);

    const createPolicyResult = parsedResponse['s:Envelope']['s:Body'][0]['CreatePolicyResponse'][0]['CreatePolicyResult'][0];

    let policyDetails = null;
    try {
        if (createPolicyResult) {
            policyDetails = await parseStringPromise(createPolicyResult);
        }
    } catch (parseError) {
        console.error('Error parsing policy details:', parseError);
    }

    return {
        status: 'success',
        rawResponse: parsedResponse,
        policyDetails,
        timestamp: new Date().toISOString()
    };
}

module.exports = { sendAdvTopupSOAPRequest };