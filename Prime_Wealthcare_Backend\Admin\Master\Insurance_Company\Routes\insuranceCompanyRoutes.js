const express = require('express');
const router = express.Router();
const InsuranceCompanyController = require('../Controllers/insuranceCompanyController');

router.post('/', InsuranceCompanyController.createInsuranceCompany);
router.get('/', InsuranceCompanyController.getAllInsuranceCompanies);
router.get('/:id', InsuranceCompanyController.getInsuranceCompanyById);
router.put('/:id', InsuranceCompanyController.updateInsuranceCompanyById);
router.delete('/:id', InsuranceCompanyController.deleteInsuranceCompanyById);
router.patch('/reinstate/:id', InsuranceCompanyController.reinstateInsuranceCompany);
router.get('/name/:name', InsuranceCompanyController.getInsuranceCompanyByName);
router.get('/criteria/:criteria', InsuranceCompanyController.getInsuranceCompaniesByCriteria);


module.exports = router;
