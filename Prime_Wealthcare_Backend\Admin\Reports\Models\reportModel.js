const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);

// Update the generateReport function to handle union properly
const generateReport = async (filters) => {
    try {
        // Create base queries for different proposal tables
        let baseQueries = getBaseQueries();
        
        // Apply filters to all base queries
        baseQueries = baseQueries.map(query => {
            return applyFilters(query, filters);
        });
        
        // Create the union query properly
        let unionQuery;
        if (baseQueries.length === 1) {
            unionQuery = baseQueries[0];
        } else {
            // This creates a proper UNION query in Knex
            unionQuery = baseQueries[0];
            for (let i = 1; i < baseQueries.length; i++) {
                unionQuery = unionQuery.union(baseQueries[i]);
            }
        }
                
        // Apply grouping based on group_by selections
        let groupedQuery = applyGrouping(unionQuery, filters.group_by);        
        // Execute the final query
        try {
            const results = await groupedQuery;
            
            // Create a lookup object to store any additional info about filters
            const filterLabels = {};
            
            // If you have the reference data already loaded, you can use it directly
            // (from your Redux state in the frontend)
            
            return formatResults(results, filters.group_by, filters, filterLabels);
        } catch (error) {
            // More descriptive error message for common errors
            if (error.code === 'ER_INVALID_GROUP_FUNC_USE') {
                throw new Error('Invalid use of group function. Check your GROUP BY clause.');
            } else if (error.code === 'ER_WRONG_FIELD_WITH_GROUP') {
                throw new Error('Non-aggregated column used with GROUP BY. Check your select and group by fields.');
            } else {
                throw error;
            }
        }
    } catch (error) {
        console.error('Error in generateReport model:', error);
        throw error;
    }
};

// Get available financial years from policy data
const getFinancialYears = async () => {
    try {
        const currentYear = new Date().getFullYear();
        // Typically return the current and previous 4 financial years
        const financialYears = [];
        
        for (let i = 0; i < 5; i++) {
            const startYear = currentYear - i;
            const endYear = startYear + 1;
            // Format as "2023-24"
            financialYears.push({
                value: `${startYear}-${endYear.toString().slice(-2)}`,
                label: `${startYear}-${endYear.toString().slice(-2)}`
            });
        }
        
        return financialYears;
    } catch (error) {
        console.error('Error in getFinancialYears model:', error);
        throw error;
    }
};

// Helper function to create base queries for different proposal tables
const getBaseQueries = () => {
    // Query for regular proposals
    const regularProposalsQuery = db('proposals as p')
        .select([
            'p.id as proposal_id',  // Changed from p.id to p.id as proposal_id
            'p.ProposalNumber as proposal_number',
            'a.agent_id as agent_id',     // Changed from a.id to a.id as agent_id
            'a.full_name as agent_name',
            'ib.id as imf_branch_id', // Added alias for branch id
            'ib.branch_name as imf_branch_name',
            'ic.id as insurance_company_id', // Added alias for insurance company id
            'ic.insurance_company_name',
            'ic.short_name as insurance_company_short_name', // Add this line
            'mp.id as product_type_id', // Added alias for product type id
            'mp.main_product as product_type',
            'pm.id as product_id', // Added alias for product id
            'pm.product_name',
            'sp.id as sub_product_id', // Added alias for sub product id
            'sp.sub_product_name',
            'p.member_type',
            'p.status as policy_status',
            'p.proposal_type as proposal_type',
            'p.policy_number',
            'p.policy_issue_date',
            'p.start_date',
            'p.end_date',
            'p.net_premium',
            'p.gst_amount',
            'p.total_premium',
            'p.Created_at as created_at'
        ])
        .leftJoin('agents as a', 'p.agent_code', 'a.id')
        .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
        .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
        .leftJoin('main_product as mp', 'p.product_type', 'mp.id')
        .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
        .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id');

    // Query for PA proposals
    const paProposalsQuery = db('proposals_pa as p')
        .select([
            'p.id as proposal_id',  // Changed from p.id to p.id as proposal_id
            'p.ProposalNumber as proposal_number',
            'a.agent_id as agent_id',     // Changed from agent_code to a.id as agent_id
            'a.full_name as agent_name',
            'ib.id as imf_branch_id',
            'ib.branch_name as imf_branch_name',
            'ic.id as insurance_company_id',
            'ic.insurance_company_name',
            'ic.short_name as insurance_company_short_name', // Add this line
            'mp.id as product_type_id',
            'mp.main_product as product_type',
            'pm.id as product_id',
            'pm.product_name',
            'sp.id as sub_product_id',
            'sp.sub_product_name',
            'p.member_type',
            'p.status as policy_status',
            'p.proposal_type as proposal_type',
            'p.policy_number',
            'p.policy_issue_date',
            'p.start_date',
            'p.end_date',
            'p.net_premium',
            'p.gst_amount',
            'p.total_premium',
            'p.Created_at as created_at'
        ])
        .leftJoin('agents as a', 'p.agent_code', 'a.id')
        .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
        .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
        .leftJoin('main_product as mp', 'p.product_type', 'mp.id')
        .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
        .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id');

    // Query for rollover/migration proposals
    const rolloverProposalsQuery = db('proposals_rollover_migration as p')
        .select([
            'p.id as proposal_id',
            'p.proposal_Number as proposal_number',
            'a.agent_id as agent_id',
            'a.full_name as agent_name',
            'ib.id as imf_branch_id',
            'ib.branch_name as imf_branch_name',
            'ic.id as insurance_company_id',
            'ic.insurance_company_name',
            'ic.short_name as insurance_company_short_name',
            'mp.id as product_type_id',
            'mp.main_product as product_type',
            'pm.id as product_id',
            'pm.product_name',
            'sp.id as sub_product_id',
            'sp.sub_product_name',
            'p.member_type',
            'p.status as policy_status',
            'p.proposal_type as proposal_type',
            'p.policy_number',
            'p.policy_issue_date',
            'p.start_date',
            'p.end_date',
            'p.net_premium',
            'p.gst_amount',
            'p.total_premium',
            'p.Created_at as created_at'
        ])
        .leftJoin('agents as a', 'p.agent_code', 'a.id')
        .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
        .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
        .leftJoin('main_product as mp', 'p.product_type', 'mp.id')
        .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
        .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id');
    
    return [regularProposalsQuery, paProposalsQuery, rolloverProposalsQuery];
};

// Helper function to apply filters to a query
const applyFilters = (query, filters) => {
    const {
        imf_code,
        imf_branch,
        agent_id,
        insurance_company_name,
        product_type,
        product_name,
        sub_product_name,
        member_type,
        policy_status,
        proposal_type,
        policy_start_date,
        policy_end_date,
        proposal_start_date,
        proposal_end_date,
        financial_years,
        report_type
    } = filters;

    // Get the table name to properly detect which table we're working with
    const queryString = query.toString().toLowerCase();
    const isRolloverTable = queryString.includes('proposals_rollover_migration');

    // Apply each filter if it exists
     if (imf_code) {
        query = query.where('p.imf_code', imf_code);
    } 
    
    if (imf_branch) {
        query = query.where('p.imf_branch', imf_branch);
    }
    
    // Fix the agent filter - different columns in different tables
    if (agent_id) {
            query = query.where('p.agent_code', agent_id);
    }
    
    // Fix insurance company filter - different column names in different tables
    if (insurance_company_name) {
            query = query.where('p.insurance_company', insurance_company_name);
        }
    
    // Common filters for all tables
    if (product_type) {
        query = query.where('p.product_type', product_type);
    }
    
    if (product_name) {
        query = query.where('p.product_name', product_name);
    }
    
    // Fix sub product filter - different column names in different tables
    if (sub_product_name) {
            query = query.where('p.sub_product', sub_product_name);
        }
    
    if (member_type) {
        query = query.where('p.member_type', member_type);
    }
    
    if (policy_status) {
        query = query.where('p.status', policy_status);
    }
    
    if (proposal_type) {
        query = query.where('p.proposal_type', proposal_type);
    }
    
    // Date filters
    if (policy_start_date) {
        query = query.where('p.start_date', '>=', policy_start_date);
    }
    
    if (policy_end_date) {
        query = query.where('p.start_date', '<=', policy_end_date);
    }
    
    if (proposal_start_date) {
        query = query.where('p.Created_at', '>=', proposal_start_date);
    }
    
    if (proposal_end_date) {
        query = query.where('p.Created_at', '<=', proposal_end_date);
    }
    
    // Financial years filter
    if (financial_years && financial_years.length > 0) {
        query = query.where(function() {
            for (const fyYear of financial_years) {
                const [startYear, endYearSuffix] = fyYear.split('-');
                const endYear = parseInt(`20${endYearSuffix}`);
                
                const fyStartDate = `${startYear}-04-01`;
                const fyEndDate = `${endYear}-03-31`;
                
                this.orWhereBetween('p.Created_at', [fyStartDate, fyEndDate]);
            }
        });
    }
    
    // Apply Report Type filter (YTD or MTD) if no other date filters are specified
    if (report_type && !policy_start_date && !policy_end_date && 
        !proposal_start_date && !proposal_end_date && 
        (!financial_years || financial_years.length === 0)) {
        
        const today = new Date();
        
        if (report_type === 'ytd') {
            // YTD implementation
        } 
        else if (report_type === 'mtd') {
            // MTD implementation
        }
    }
    
    // Apply Report Type filter (YTD or MTD)
    if (report_type) {
        const today = new Date();
        
        if (report_type === 'ytd') {
            // For YTD, get the current financial year start date
            // In India, financial year starts on April 1st
            const currentYear = today.getMonth() >= 3 ? today.getFullYear() : today.getFullYear() - 1;
            const financialYearStart = `${currentYear}-04-01`;
            
            // Format today's date as YYYY-MM-DD
            const todayFormatted = today.toISOString().split('T')[0];
            
            // Apply the date range filter
            query = query.whereBetween('p.Created_at', [financialYearStart, todayFormatted]);
        } 
        else if (report_type === 'mtd') {
            // For MTD, get the current month's first day
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
                .toISOString().split('T')[0];
            
            // Format today's date as YYYY-MM-DD
            const todayFormatted = today.toISOString().split('T')[0];
            
            // Apply the date range filter
            query = query.whereBetween('p.Created_at', [firstDayOfMonth, todayFormatted]);
        }
    }
     return query;
};

// Fix the applyGrouping function
const applyGrouping = (query, groupByOptions) => {
    // Map group by options to their respective columns
    const groupByMappings = {
        agent: ['agent_id', 'agent_name'],
        imf_branch: ['imf_branch_id', 'imf_branch_name'],
        insurance_company: ['insurance_company_id', 'insurance_company_name','insurance_company_short_name'],
        product: ['product_id', 'product_name'],
        sub_product: ['sub_product_id', 'sub_product_name']
    };
    
    // Build the select and group by clauses
    let selectClauses = [];
    let groupByClauses = [];
    
    // Add columns based on selected groupings
    for (const option of groupByOptions) {
        if (groupByMappings[option]) {
            // Add columns to select with proper aliasing
            if (option === 'agent') {
                selectClauses.push('agent_id', 'agent_name');
                groupByClauses.push('agent_id', 'agent_name'); 
            } 
            else if (option === 'imf_branch') {
                selectClauses.push('imf_branch_id', 'imf_branch_name');
                groupByClauses.push('imf_branch_id', 'imf_branch_name');
            }
            else if (option === 'insurance_company') {
                selectClauses.push('insurance_company_id', 'insurance_company_name', 'insurance_company_short_name');
                groupByClauses.push('insurance_company_id', 'insurance_company_name','insurance_company_short_name');
            }
            else if (option === 'product') {
                selectClauses.push('product_id', 'product_name');
                groupByClauses.push('product_id', 'product_name');
            }
            else if (option === 'sub_product') {
                selectClauses.push('sub_product_id', 'sub_product_name');
                groupByClauses.push('sub_product_id', 'sub_product_name');
            }
        }
    }
    
    // Always include these aggregate columns
    selectClauses.push(
        db.raw('COUNT(*) as total_count'),
        db.raw('SUM(net_premium) as total_net_premium'),
        db.raw('SUM(gst_amount) as total_gst'),
        db.raw('SUM(total_premium) as total_gross_premium')
    );
    
    // IMPORTANT CHANGE: Create a new query from the derived table 
    // but ONLY select specific columns (not '*')
    const finalQuery = db.from(function() {
        this.select(db.raw('*')).from(db.raw(`(${query.toString()}) as derived`)).as('temp');
    })
    .select(selectClauses)
    .groupBy(groupByClauses);
        return finalQuery;
};

// Format results for frontend consumption with filter information
const formatResults = (results, groupByOptions, filters) => {
    // Create column headers based on group by options
    const columns = [];
    
    // First add columns for the group by selections
    groupByOptions.forEach(option => {
        switch(option) {
            case 'agent':
                columns.push({ field: 'agent_id', headerName: 'Agent Code' });
                columns.push({ field: 'agent_name', headerName: 'Agent Name' });
                break;
            case 'imf_branch':
                columns.push({ field: 'imf_branch_name', headerName: 'IMF Branch' });
                break;
            case 'insurance_company':
                columns.push({ field: 'insurance_company_name', headerName: 'Insurance Company' });
                //columns.push({ field: 'insurance_company_short_name', headerName: 'Short Name' });
                break;
            case 'product':
                columns.push({ field: 'product_name', headerName: 'Product' });
                break;
            case 'sub_product':
                columns.push({ field: 'sub_product_name', headerName: 'Sub Product' });
                break;
        }
    });
    
    // Add metric columns
    columns.push({ field: 'total_count', headerName: 'Total Policies', type: 'number' });
    columns.push({ field: 'total_net_premium', headerName: 'Net Premium', type: 'currency' });
    columns.push({ field: 'total_gst', headerName: 'GST', type: 'currency' });
    columns.push({ field: 'total_gross_premium', headerName: 'Gross Premium', type: 'currency' });
    
    // Calculate summary totals
    const summary = {
        total_policies: results.reduce((sum, row) => sum + parseInt(row.total_count || 0), 0),
        total_net_premium: results.reduce((sum, row) => sum + parseFloat(row.total_net_premium || 0), 0),
        total_gst: results.reduce((sum, row) => sum + parseFloat(row.total_gst || 0), 0),
        total_gross_premium: results.reduce((sum, row) => sum + parseFloat(row.total_gross_premium || 0), 0)
    };
    
    // Include filter information in the response for display
    const appliedFilters = {};
    
    // Add date ranges
    if (filters.policy_start_date) {
        appliedFilters["Policy Date Range"] = `${filters.policy_start_date} to ${filters.policy_end_date || 'Present'}`;
    }
    
    if (filters.proposal_start_date) {
        appliedFilters["Proposal Date Range"] = `${filters.proposal_start_date} to ${filters.proposal_end_date || 'Present'}`;
    }
    
    // Add financial years
    if (filters.financial_years && filters.financial_years.length > 0) {
        appliedFilters["Financial Years"] = filters.financial_years.join(', ');
    }
    
    // Get the label values for various filters from reference data
    // This requires us to have access to this data or fetch it
    async function getFilterLabelValues() {
        try {
            // Example: If filtering by agent_id, get the agent name
            if (filters.agent_id) {
                const agent = await db('agents').where('id', filters.agent_id).first();
                if (agent) {
                    appliedFilters["Agent"] = `${agent.agent_id} - ${agent.full_name}`;
                }
            }
            
            // Example: If filtering by imf_branch, get the branch name
            if (filters.imf_branch) {
                const branch = await db('imf_branches').where('id', filters.imf_branch).first();
                if (branch) {
                    appliedFilters["IMF Branch"] = branch.branch_name;
                }
            }
            
            // Similar lookups for other foreign keys...
            
            // For dropdown values, we can use a mapping
            if (filters.member_type) {
                const memberTypeMap = {
                    'individual': 'Individual',
                    'family floater': 'Family Floater'
                };
                appliedFilters["Member Type"] = memberTypeMap[filters.member_type] || filters.member_type;
            }
            
            if (filters.policy_status) {
                const statusMap = {
                    'success': 'Success',
                    'pending': 'Pending',
                    'cancelled': 'Cancelled'
                };
                appliedFilters["Policy Status"] = statusMap[filters.policy_status] || filters.policy_status;
            }
            
            if (filters.proposal_type) {
                const proposalTypeMap = {
                    'new': 'New', 
                    'renewal': 'Renew',
                    'rollover': 'Roll Over',
                    'migration': 'Migration'
                };
                appliedFilters["Proposal Type"] = proposalTypeMap[filters.proposal_type] || filters.proposal_type;
            }
            
            return appliedFilters;
        } catch (error) {
            console.error("Error getting filter label values:", error);
            return appliedFilters;
        }
    }
    
    // For synchronous version, use this approach instead:
    // Add other filters if they were applied
    const filterMappings = {
        imf_code: 'IMF Code',
        member_type: 'Member Type',
        policy_status: 'Policy Status',
        proposal_type: 'Proposal Type',
        imf_branch: 'IMF Branch',
        agent_id: 'Agent',
        insurance_company_name: 'Insurance Company',
        product_type: 'Product Type',
        product_name: 'Product',
        sub_product_name: 'Sub Product'
    };
    
    // Add each applied filter to the response
    Object.entries(filters).forEach(([key, value]) => {
        if (value && filterMappings[key]) {
            appliedFilters[filterMappings[key]] = value;
        }
    });
    
    // Add report type to applied filters
    if (filters.report_type) {
        if (filters.report_type === 'ytd') {
            const today = new Date();
            const currentYear = today.getMonth() >= 3 ? today.getFullYear() : today.getFullYear() - 1;
            const nextYear = currentYear + 1;
            appliedFilters["Report Type"] = `Year-to-Date (${currentYear}-${nextYear})`;
        } else if (filters.report_type === 'mtd') {
            const today = new Date();
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];
            appliedFilters["Report Type"] = `Month-to-Date (${monthNames[today.getMonth()]})`;
        }
    }
    return {
        columns,
        rows: results,
        summary,
        appliedFilters
    }; 
};

// Helper function to get financial year information
const getFinancialYearInfo = () => {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0-11 (Jan-Dec)
    
    // If current month is January to March, we're in the previous year's financial year
    const financialYearStartYear = currentMonth >= 3 ? today.getFullYear() : today.getFullYear() - 1;
    const financialYearEndYear = financialYearStartYear + 1;
    
    const financialYearStartDate = `${financialYearStartYear}-04-01`;
    const financialYearEndDate = `${financialYearEndYear}-03-31`;
    
    return {
        startYear: financialYearStartYear,
        endYear: financialYearEndYear,
        startDate: financialYearStartDate,
        endDate: financialYearEndDate,
        currentFinancialYear: `${financialYearStartYear}-${financialYearEndYear.toString().slice(-2)}`
    };
};

module.exports = {
    generateReport,
    getFinancialYears
};