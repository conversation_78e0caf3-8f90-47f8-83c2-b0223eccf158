import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import { toast } from 'react-toastify';
import ModuleName from '../../../components/table/ModuleName';
import DropDown from '../../../components/table/DropDown';
import FileUploadButton from '../../../components/table/FileUploadButton';
import { fetchInsuranceTypes } from '../../../redux/actions/action';
import { createInsuranceCompany, updateInsuranceCompany, fetchInsuranceCompanies, fetchInsuranceCompanyById, getLocationByPincode, getAreasByPincodeAndCity } from '../../../redux/actions/action';
import BarLoader from '../../../components/BarLoader'; // Import BarLoader

const InsuranceForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    insurance_company_name: '',
    short_name: '',
    ado_code: '',
    help_line_no: '',
    email_id: '',
    zone_head_number: '',
    zone_head_name: '',
    insurance_type: '',
    address_line1: '',
    address_line2: '',
    area: '',
    city: '',
    pincode: '',
    state: '',
    company_logo: null,
  });

  const insuranceTypes = useSelector((state) => state.insuranceTypeReducer.insuranceTypes);
  const insuranceCompany = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
  const locationData = useSelector(state => state.areaManagementReducer.locations);
  const companyData = useSelector(state => state.insuranceCompanyReducer.currentCompany)
  const isEditMode = !!id;

  const [cityOptions, setCityOptions] = useState([]);
  const [areaOptions, setAreaOptions] = useState([]);
  const [loading, setLoading] = useState(false); // Add loading state

  useEffect(() => {
    setLoading(true); // Set loading to true when fetching data
    if (id) {
      dispatch(fetchInsuranceCompanyById(id));
    }
    else {
      resetFormData();
    }
  }, [dispatch, id]);

  useEffect(() => {
    dispatch(fetchInsuranceTypes()).finally(() => setLoading(false)); // Set loading to false after fetching
  }, [dispatch]);

  useEffect(() => {
    if (isEditMode && companyData) {

      setFormData({
        insurance_company_name: companyData.insurance_company_name || '',
        short_name: companyData.short_name || '',
        ado_code: companyData.ado_code || '',
        help_line_no: companyData.help_line_no || '',
        email_id: companyData.email_id || '',
        zone_head_number: companyData.zone_head_number || '',
        zone_head_name: companyData.zone_head_name || '',
        insurance_type: companyData.insurance_type || '',
        address_line1: companyData.address_line1 || '',
        address_line2: companyData.address_line2 || '',
        area: companyData.area || '',
        city: companyData.city || '',
        pincode: companyData.pincode || '',
        state: companyData.state || '',
        company_logo: companyData.company_logo || null,
      });
    }
  }, [isEditMode, companyData]);

  useEffect(() => {
    if (String(formData.pincode).length === 6) {
      dispatch(getLocationByPincode(formData.pincode)).then((action) => {
        if (!action.payload || action.payload.length === 0) {
          setErrors(prevErrors => ({ ...prevErrors, pincode: 'This pincode does not exist' }));
          setFormData(prevFormData => ({
            ...prevFormData,
            area: '',
            city: '',
            state: ''
          }));
        } else {
          const uniqueCities = [...new Set(action.payload.map(location => location.city))];
          const data = uniqueCities.map(city => ({ label: city, value: city }));
          setCityOptions(data);
          setFormData(prevFormData => ({
            ...prevFormData,
            state: companyData?.state || action.payload[0].state,
            city: companyData?.city
          }));
        }
      });
    }
  }, [formData.pincode, companyData?.city, companyData?.state, dispatch]);

  useEffect(() => {
    if (formData.city && formData.pincode) {
      dispatch(getAreasByPincodeAndCity({ pincode: formData.pincode, city: formData.city })).then((action) => {
        if (action.payload.length > 0) {
          const data = action.payload.map(area => ({ label: area.area, value: area.id }));
          setAreaOptions(data);
          setFormData(prevFormData => ({
            ...prevFormData,
            area: companyData?.area || (data.length === 1 ? data[0].value : '')
          }));
        } else {
          setAreaOptions([]);
          setFormData(prevFormData => ({
            ...prevFormData,
            area: ''
          }));
        }
      });
    }
    if (formData.city) {
      setErrors(prevErrors => ({
        ...prevErrors,
        city: ''
      }));
    }
  }, [formData.city, formData.pincode, companyData?.area, dispatch]);

  useEffect(() => {
    if (locationData && formData.pincode) {
      setFormData(prevFormData => ({
        ...prevFormData,
        area: locationData.area || '',
        city: locationData.city || '',
        state: locationData.state || ''
      }));
    }
  }, [locationData, formData.pincode]);


  const handleChange = (e) => {
    const { name, value } = e.target;
    // Handle email to lowercase
    if (name === 'email_id') {
        setFormData({
            ...formData,
            [name]: value.toLowerCase(),
        });
    } else if (name === 'address_line1' || name === 'address_line2') {
        // Use regex to validate address
        const regex = /^[a-zA-Z0-9,.\-\s#/&]+$/;
        if (!regex.test(value) && value !== '') {
            setErrors(prevErrors => ({
                ...prevErrors,
                [name]: 'Address must only contain letters, numbers, spaces, and special characters (,.-/#& )'
            }));
            return;
        }
        // Convert address to title case
        const formattedValue = value.split(' ').map(word => {
            return word.charAt(0).toUpperCase() + word.slice(1);
        }).join(' ').replace(/\s{2,}$/, ' ');
        setFormData({
            ...formData,
            [name]: formattedValue,
        });
    } else if (name === 'insurance_type' || name === 'area') {
        // Directly use the value for dropdowns without modification
        setFormData({
            ...formData,
            [name]: value,
        });
    } else if (name === 'pincode') {
        // Allow only numbers and restrict to 6 digits
        const formattedPincode = value.replace(/\D/g, '').slice(0, 6);
        setFormData({
            ...formData,
            [name]: formattedPincode,
        });
    } else {
        // For all other fields, capitalize the value
        setFormData({
            ...formData,
            [name]: value.toUpperCase(),
        });
    }
    setErrors({
        ...errors,
        [name]: false,
    });
  };


  const handleLogoChange = (event) => {
    const file = event.target.files[0];
    setFormData((prevData) => ({
      ...prevData,
      company_logo: file,
    }));
  };

  const resetFormData = () => {
    setFormData({
      insurance_company_name: '',
      short_name: '',
      ado_code: '',
      help_line_no: '',
      email_id: '',
      zone_head_number: '',
      zone_head_name: '',
      insurance_type: '',
      address_line1: '',
      address_line2: '',
      area: '',
      city: '',
      pincode: '',
      state: '',
      company_logo: null,
    });
    setErrors({}); // Reset errors as well
  };


  const validateForm = () => {
    const validationErrors = {};

    const existingEmails = insuranceCompany.map(company => company.email_id?.trim() || '');
    const existingHelpLineNumbers = insuranceCompany.map(company => company.help_line_no?.trim() || '');
    const existingCompanyNames = insuranceCompany.map(company => {
      const name = company.insurance_company_name?.trim() || '';
      const shortName = company.short_name?.trim() || '';
      return `${name}-${shortName}`;
    });
    const existingInsuranceCompanyNames = insuranceCompany.map(company => company.insurance_company_name?.trim() || '');
    const existingShortNames = insuranceCompany.map(company => company.short_name?.trim() || '');

    const currentCompanyName = formData.insurance_company_name.trim();
    const currentShortName = formData.short_name.trim();
    const currentEmail = formData.email_id.trim();
    const currentHelpLineNo = formData.help_line_no.trim();
    const currentCompanyCombination = `${currentCompanyName}-${currentShortName}`;

    // Validate uniqueness of the combination of insurance company name and short name
    if (!isEditMode) {
      if (
        existingCompanyNames.includes(currentCompanyCombination)) {
        validationErrors.company_combination = 'The combination of Company Name and Short Name must be unique.';
      }
    }

    // Validate insurance company name uniqueness
    if (!isEditMode) {
      if (existingInsuranceCompanyNames.includes(currentCompanyName)) {
        validationErrors.insurance_company_name = 'Insurance Company Name already exists.';
      }
    }

    // Validate short name uniqueness
    if (!isEditMode) {
      if (existingShortNames.includes(currentShortName)) {
        validationErrors.short_name = 'Short Name must be unique.';
      }
    }

    /* // For edit mode: allow current values
    if (isEditMode) {
      if (currentCompanyName !== companyData.insurance_company_name && existingInsuranceCompanyNames.includes(currentCompanyName)) {
          validationErrors.insurance_company_name = 'Insurance Company Name already exists.';
      }
      if (currentShortName !== companyData.short_name && existingShortNames.includes(currentShortName)) {
          validationErrors.short_name = 'Short Name must be unique.';
      }
    } */
    // Check for duplicates
    if (existingEmails.includes(currentEmail) && currentEmail !== (companyData?.email_id?.trim() || '')) {
      validationErrors.email_id = 'Email ID already exists.';
    }
    if (existingHelpLineNumbers.includes(currentHelpLineNo) && currentHelpLineNo !== (companyData?.help_line_no?.trim() || '')) {
      validationErrors.help_line_no = 'Help Line No. already exists.';
    }


    if (!formData.insurance_company_name) {
      validationErrors.insurance_company_name = 'Company Name is required';
    }
    if (!formData.short_name) {
      validationErrors.short_name = 'Short Name is required';
    }
    if (!formData.help_line_no || !/^\d{10,12}$/.test(formData.help_line_no)) {
      validationErrors.help_line_no = 'Help Line No. must be between 10 to 12 digits';
    }
    if (!formData.email_id || !/\S+@\S+\.\S+/.test(formData.email_id)) {
      validationErrors.email_id = 'Email ID is invalid';
    }
    // Validate Pincode
    if (!/^[0-9]{6}$/.test(formData.pincode)) {
      validationErrors.pincode = 'Please enter a valid 6-digit Pincode.';
    }

    if(!formData.city){
      validationErrors.city = 'Please select a City.';
    }

    if (!formData.insurance_type) {
      validationErrors.insurance_type = 'Insurance type is required';
    }
    if (!formData.zone_head_name) {
      validationErrors.zone_head_name = 'Zone head name is required';
    }

    if (!formData.zone_head_number || !/^[6-9]\d{9,11}$/.test(formData.zone_head_number)) {
      validationErrors.zone_head_number = 'Zone head number must be a valid 10 digit number';
    }

    // Validate address lines to allow numbers and specific special characters
    const addressFields = ['address_line1', 'address_line2'];
    addressFields.forEach(field => {
      if (formData[field] && !/^[A-Za-z0-9 ,.#/&-]+$/.test(formData[field])) {
        validationErrors[field] = 'Address must only contain letters, numbers, spaces, and special characters ( ,.-&).';
      }
    });
    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0;
  };

  const handleInsuranceCompanyCreationAndUpdate = async () => {
    const isValid = validateForm(); // Ensure the validation is called
    if (!isValid) return; // Stop if the form is invalid
    const { area, ...companyData } = formData; // Spread the form data
    const dataToSend = {
      ...companyData,
      area: formData?.area || null,
    };
    let response;
    try {
      if (isEditMode) {
        response = await dispatch(updateInsuranceCompany({ id, companyData: dataToSend }));
      } else {
        response = await dispatch(createInsuranceCompany(dataToSend));
      }
      if (response.error) {
        console.error("Detailed error response:", response.error); // Log the full error response
        throw new Error(response.error.message); // Throw an error if response.error exists
      }
      toast.success(isEditMode ? 'Company updated successfully!' : 'Company created successfully!');
      return true; // Indicate success
    } catch (error) {
      console.error("Error saving company:", error); // Log the error
      toast.error('Failed to save company: ' + (error.message || 'An unknown error occurred.')); // Show the toast error message
      return false; // Indicate failure
    }
  };

  const handleSave = async () => {
    setLoading(true); // Start loading
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate a 2 seconds delay
    const isSuccess = await handleInsuranceCompanyCreationAndUpdate();
    setLoading(false); // Stop loading
    if (isSuccess) {
      handleCancel(); // Navigate if the save was successful
    }
  };

  const handleSaveAndNew = async () => {
    setLoading(true); // Start loading
    const isValid = validateForm();
    if (!isValid) return;
    await handleInsuranceCompanyCreationAndUpdate().then(() => {
      dispatch(fetchInsuranceCompanies());
    })
    setLoading(false); // Stop loading
    if (isValid) {
      resetFormData();
      navigate('/dashboard/insurance-form');
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/insurance-company');
  };

  return (
    <>
      <BarLoader loading={loading} /> {/* Render BarLoader */}
      <form>
        <Grid container spacing={2}>
          <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
            <img src="/image.png" alt="module icon" style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }} />
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ModuleName moduleName="Insurance Company" pageName={id ? companyData?.status === 0 ? "View" : "Edit" : "Create"} />          </Box>
          </Grid>
          <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
            {id ? (
              <>
                {(companyData?.status === 1 || !id) && <Button variant="outlined" size="small" sx={{ maxWidth: '120px', width: '120px', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }} onClick={handleSaveAndNew}>
                  Update & New
                </Button>}
                {(companyData?.status === 1 || !id) && <Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }} onClick={handleSave}>
                  Update
                </Button>}
              </>
            ) : (
              <>
                {(companyData?.status === 1 || !id) && (<Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }} onClick={handleSaveAndNew}>
                  Save & New
                </Button>)}
                {(companyData?.status === 1 || !id) && <Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }} onClick={handleSave}>
                  Save
                </Button>}
              </>
            )}
            <Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }} onClick={handleCancel}>
              Cancel
            </Button>
          </Grid>
          {/* Form Fields */}
          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Company Name"
              name="insurance_company_name"
              value={formData.insurance_company_name}
              onChange={handleChange}
              error={!!errors.insurance_company_name}
              helperText={errors.insurance_company_name}
              disabled={isEditMode}
              style={isEditMode ? { backgroundColor: 'lightgray' } : {}}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px', // Width of the red line
                    backgroundColor: 'red', // Color of the line
                  },
                  backgroundColor: id ? '#e0e0e0' : 'inherit', // Grey out in edit mode

                },
              }}
            />
          </Grid>
          <Grid item xs={3}>
            <CustomTextField
              label="Short Name"
              name="short_name"
              value={formData.short_name}
              onChange={handleChange}
              error={!!errors.short_name}
              helperText={errors.short_name}
              disabled={isEditMode}
              style={isEditMode ? { backgroundColor: 'lightgray' } : {}}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px', // Width of the red line
                    backgroundColor: 'red', // Color of the line
                  },
                  backgroundColor: id ? '#e0e0e0' : 'inherit', // Grey out in edit mode

                },
              }}
            />
          </Grid>
          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <CustomTextField
              label="ADO Code"
              name="ado_code"
              value={formData.ado_code}
              onChange={handleChange}
              error={!!errors.ado_code}
              helperText={errors.ado_code}
              InputProps={{
                readOnly: isEditMode, // Read-only if in edit mode
                style: isEditMode ? { backgroundColor: '#e0e0e0' } : {},
              }}
              disabled={isEditMode} // Disable only if in edit mode

              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px', // Width of the red line
                    // backgroundColor: 'red', // Color of the line
                  },
                  backgroundColor: id ? '#e0e0e0' : 'inherit', // Grey out in edit mode

                },
              }}
            />
          </Grid>
          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Help Line No."
              name="help_line_no"
              value={formData.help_line_no}
              onChange={handleChange}
              type="tel"
              error={!!errors.help_line_no}
              helperText={errors.help_line_no}
              disabled={id && companyData?.status === 0}
              inputProps={{
                maxLength: 12, // Updated to 12
                pattern: "[0-9]*",
                onInput: (e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 12);
                }
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />

          </Grid>
          <Grid item xs={3}>
            <CustomTextField
              label="Email Id"
              name="email_id"
              value={formData.email_id}
              onChange={handleChange}
              type="email"
              error={!!errors.email_id}
              helperText={errors.email_id}
              disabled={id && companyData?.status === 0}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />

          </Grid>
          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <CustomTextField
              label="Zone Head Name"
              name="zone_head_name"
              value={formData.zone_head_name}
              onChange={handleChange}
              error={!!errors.zone_head_name}
              helperText={errors.zone_head_name}
              disabled={id && companyData?.status === 0}
              type="text"
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }
              }
            />


          </Grid>
          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Zone Head Number"
              name="zone_head_number"
              value={formData.zone_head_number}
              onChange={handleChange}
              type="tel"
              error={!!errors.zone_head_number}
              helperText={errors.zone_head_number}
              disabled={id && companyData?.status === 0}
              applyPrefix // This prop triggers the prefix
              inputProps={{
                maxLength: 10, // Maximum 10 digits
                pattern: "[0-9]*", // Only allow digits
                onInput: (e) => {
                  // Remove non-numeric characters
                  e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 10);
                }
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />

          </Grid>
          <Grid item xs={3}>
            <FileUploadButton
              label="Upload Company Logo"
              InputProps={{
                readOnly: isEditMode,
                style: isEditMode ? { backgroundColor: '#e0e0e0' } : {},
              }}
              disabled={isEditMode}
              onChange={handleLogoChange}  // Capture the file on change
            />
          </Grid>
          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <DropDown
              label="Insurance Type"
              name="insurance_type"
              required
              helperText={errors.insurance_type}
              options={[
                { label: 'None', value: '' },
                ...(Array.isArray(insuranceTypes) ? insuranceTypes.map(type => ({ label: type.type_name, value: type.type_name })) : []),
              ]}

              value={formData.insurance_type}
              disabled={id && companyData?.status === 0}
              onChange={handleChange}  // Use handleChange here
              fullWidth
            />
          </Grid>
          <Grid item xs={12}>
            <Box
              sx={{
                backgroundColor: '#f0f0f0',
                padding: '15px',
                borderRadius: '4px',
                height: '60px'
              }}
            >
              <h2>Address Information</h2>
            </Box>
          </Grid>
          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Address Line 1"
              name="address_line1"
              value={formData.address_line1}
              onChange={handleChange}
              error={!!errors.address_line1}
              helperText={errors.address_line1}
              disabled={id && companyData?.status === 0}
            />
          </Grid>
          <Grid item xs={3}>
            <CustomTextField
              label="Address Line 2"
              name="address_line2"
              value={formData.address_line2}
              onChange={handleChange}
              error={!!errors.address_line2}
              helperText={errors.address_line2}
              disabled={id && companyData?.status === 0}

            />
          </Grid>
          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <CustomTextField
              label="Pincode"
              name="pincode"
              value={formData.pincode}
              disabled={id && companyData?.status === 0}

              onChange={handleChange}
              error={!!errors.pincode}
              helperText={errors.pincode}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />

          </Grid>

          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="State"
              name="state"
              value={formData.state}
              disabled // Disable editing
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />

          </Grid>
          <Grid item xs={3} >
            <DropDown
              label="City"
              name="city"
              options={cityOptions}
              value={formData.city}
              onChange={handleChange}
              fullWidth
              helperText={errors.city}
              disabled={id && companyData?.status === 0}
              required
            />

          </Grid>
          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <DropDown
              label="Area"
              name="area"
              options={areaOptions}
              value={formData.area}
              onChange={handleChange}
              fullWidth
              disabled={id && companyData?.status === 0}
            />
          </Grid>
        </Grid>
      </form>
    </>
  );
};

export default InsuranceForm;