import React, { useEffect, useMemo, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Avatar, IconButton } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import CustomTableNew from '../../components/table/CustomTableNew';
import SearchBar from '../../components/table/SearchBar';
import IconActions from '../../components/table/IconActions';
import DropDown from '../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../components/DeletePopup';
import SuccessPopup from '../../components/SuccessPopUp';
import { deleteProposal, getAllCustomer, getAllProposals, getAllProposalsByUserId, getAllQuotationResponse, getCustomerAndAddress, getCustomerById } from '../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../components/ExportToPDF';
import { Grid, Typography } from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { usePermissions } from '../../hooks/usePermissions';
import { Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions } from '@mui/material';
import TextField from '@mui/material/TextField';
import FilePreviewModal from '../../components/FilePreviewModal';
import { toast } from 'react-toastify';
const ProposalListPage = () => {

    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { user } = useSelector(state => state.auth);
    const [selectedOption, setSelectedOption] = useState('none');
    const [selectedRows, setSelectedRows] = useState([]);
    const [deletedIds, setDeletedIds] = useState([]);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);
    const proposals = useSelector(state => state.proposalReducer.proposalList);
    const [activeFilter, setActiveFilter] = useState('all');
    const [openDialog, setOpenDialog] = useState(false);
    const [deleteRemarks, setDeleteRemarks] = useState('');
    const [selectedProposal, setSelectedProposal] = useState(null);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState({
        url: '',
        name: '',
        isLocal: false
    });

    const pendingProposals = useMemo(() =>
        proposals.filter(proposal => proposal.policy_number === null || proposal.policy_number === '').length
        , [proposals]);

    const convertedProposals = useMemo(() =>
        proposals.filter(proposal => proposal.policy_number !== null && proposal.policy_number !== '').length
        , [proposals]);

    const linkedProposals = useMemo(() =>
        proposals.filter(proposal => proposal.policy_pdf !== null).length
        , [proposals]);

    const cancelledProposals = useMemo(() =>
        proposals.filter(proposal => proposal.status === 0).length
        , [proposals]);

    const successfulPayments = useMemo(() =>
        proposals.filter(proposal => proposal.payment_status === 'SUCCESS').length
        , [proposals]);

    useEffect(() => {
        dispatch(getAllProposalsByUserId(user.userId));
    }, [dispatch]);

    const isLocalFile = (url) => {
        return url && (
            url.startsWith('http')
        );
    };

    const columns = [
        { field: 'policy_number', headerName: 'Policy Number' },
        {
            field: 'policy_pdf',
            headerName: 'Policy Pdf',
            renderCell: (params) => {
                if (params.row.policy_pdf) {
                    return (
                        <IconButton
                            onClick={() => {
                                setSelectedFile({
                                    url: params.row.policy_pdf,
                                    name: params.row.policy_number || 'policy.pdf',
                                    isLocal: isLocalFile(params.row.policy_pdf)
                                });
                                setPreviewOpen(true);
                            }}
                            size="small"
                            sx={{ padding: '4px' }}
                        >
                            <PictureAsPdfIcon
                                color="primary"
                                sx={{ fontSize: '20px' }}
                            />
                        </IconButton>
                    );
                }
                return '—';
            }
        },
        { field: 'proposal_number', headerName: 'Proposal Number' },
        { field: 'proposal_type', headerName: 'Proposal Type' },
        { field: 'quotation_number', headerName: 'Quotation Number' },
        { field: 'proposal_date', headerName: 'Proposal Date', renderCell: (params) => { return new Date(params.row.proposal_date).toLocaleDateString(); } },
        { field: 'customer_full_name', headerName: 'Contact Name' },
        { field: 'insurance_company_name', headerName: 'Insurance Company' },
        { field: 'agent_code', headerName: 'Agent Code' },
        { field: 'master_product_name', headerName: 'Product' },
        { field: 'sub_product_name', headerName: 'Sub Product' },
        { field: 'ckyc_status', headerName: 'CKYC Status' },
        { field: 'payment_status', headerName: 'Payment Status' },
    ];
    const permissions = usePermissions('Proposal', 'Proposal Create');
    const getFilteredProposals = () => {
        switch (activeFilter) {
            case 'pending':
                return proposals.filter(proposal => proposal.policy_number === null || proposal.policy_number === '');
            case 'converted':
                return proposals.filter(proposal => proposal.policy_number !== null && proposal.policy_number !== '');
            case 'linked':
                return proposals.filter(proposal => proposal.policy_pdf !== null);
            case 'cancelled':
                return proposals.filter(proposal => proposal.status === 0);
            case 'payment_success':
                return proposals.filter(proposal => proposal.payment_status === 'SUCCESS');
            default:
                return proposals;
        }
    };

    const handleEdit = (id, proposal_type) => {
        if (!permissions?.can_edit) {
            return;
        }

        // Check proposal type and redirect accordingly
        if (proposal_type?.toLowerCase() === 'roll over') {
            navigate(`/dashboard/proposal-roll-over/${id}`);
        }
        else if (proposal_type?.toLowerCase() === 'migration') {
            navigate(`/dashboard/proposal-migration/${id}`);
        } else {
            navigate(`/dashboard/edit-proposal/${id}`);
        }
    };

    const handleDelete = (row) => {
        if (!row.policy_number) {
            toast.error('Deletion only allowed after policy creation.');
            return;
        }
        const alreadyCancelled = proposals.find(proposal => proposal.ProposalNumber === row.ProposalNumber && proposal.status === 0);
        if(alreadyCancelled){
            toast.error('Policy already cancelled.');
            return;
        }
        setSelectedProposal(row);
        setOpenDialog(true);
    };

    const handleConfirmDelete = () => {
        if (!deleteRemarks.trim()) {
            return; // Don't proceed if remarks are empty
        }

        dispatch(deleteProposal({
            id: selectedProposal.id,
            proposal_type: selectedProposal.proposal_type,
            proposal_number: selectedProposal.ProposalNumber,
            remarks: deleteRemarks
        })).then(res => {
            dispatch(getAllProposalsByUserId(user.userId));
            handleDialogClose();
        });
    };

    const handleDialogClose = () => {
        setOpenDialog(false);
        setDeleteRemarks('');
        setSelectedProposal(null);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };


    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelectedRows => {
            if (prevSelectedRows.includes(id)) {
                return prevSelectedRows.filter(selectedId => selectedId !== id);
            } else {
                return [...prevSelectedRows, id];
            }
        });
    };

    const handleSelectAll = (isSelected) => {
        if (isSelected) {
            const allIds = proposals.map(proposal => proposal.id);
            setSelectedRows(allIds);
        } else {
            setSelectedRows([]);
        }
    };

    const dataMapping = {
        'Policy Number': 'policy_number',
        'Proposal Type': 'proposal_type',
        'Proposal Number': 'proposal_number',
        'Proposal Date': 'proposal_date',
        'Contact Name': 'customer_full_name',
        'Insurance Company': 'insurance_company_name',
        'Agent Code': 'agent_code',
        'Product': 'master_product_name',
        'Sub Product': 'sub_product_name',
        'CKYC Status': 'ckyc_status',
        'Payment Status': 'payment_status',
    };

    // Add this new handler function after your other handlers
    const handlePreviewClose = () => {
        setPreviewOpen(false);
        setSelectedFile({
            url: '',
            name: '',
            isLocal: false
        });
    };

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {/* Header Section */}
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 2, sm: 0 }
                }}>
                    <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: { xs: '100%', sm: 'auto' }
                    }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Proposal" pageName="List" />
                    </Box>
                    <ButtonGroup
                        variant="outlined"
                        sx={{
                            borderRadius: 1,
                            width: { xs: '50%', sm: 'auto' },
                            '& .MuiButton-root': {
                                flex: { xs: 1, sm: 'initial' }
                            }
                        }}
                    >
                        {!Permissions.can_add && (
                            <Button
                                onClick={() => navigate('/dashboard/create-proposal')}
                                sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
                            >
                                New
                            </Button>
                        )}
                        <ExportToPDF
                            data={proposals}
                            headNames={['Policy Number', 'Proposal Number', 'Proposal Date', 'Contact Name', 'Insurance Company', 'Agent Code', 'Product', 'Sub Product', 'CKYC Status', 'Payment Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="customerData.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Proposal Report"
                            disabled
                        />
                    </ButtonGroup>
                </Box>

                <Grid container spacing={3} padding={2}>
                    {/* All Proposals */}
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setActiveFilter('all')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #16A085`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'all' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>All [Proposal Stage]</Typography>
                            <Typography variant='h6' sx={{ color: '#16A085', fontWeight: 'bold' }}>{proposals.length}</Typography>
                        </Box>
                    </Grid>

                    {/* Pending Proposals */}
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setActiveFilter('pending')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #FFC300`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'pending' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Policy Pending</Typography>
                            <Typography variant='h6' sx={{ color: '#FFC300', fontWeight: 'bold' }}>{pendingProposals}</Typography>
                        </Box>
                    </Grid>

                    {/* Converted Proposals */}
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setActiveFilter('converted')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #7d3c98`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'converted' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Policy Converted</Typography>
                            <Typography variant='h6' sx={{ color: '#7d3c98', fontWeight: 'bold' }}>{convertedProposals}</Typography>
                        </Box>
                    </Grid>

                    {/* Second Row */}
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setActiveFilter('cancelled')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #d35400`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'cancelled' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Cancelled</Typography>
                            <Typography variant='h6' sx={{ color: '#d35400', fontWeight: 'bold' }}>{cancelledProposals}</Typography>
                        </Box>
                    </Grid>

                    {/* Policy Linked */}
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setActiveFilter('linked')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #7d3c98`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'linked' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Policy Linked</Typography>
                            <Typography variant='h6' sx={{ color: '#7d3c98', fontWeight: 'bold' }}>{linkedProposals}</Typography>
                        </Box>
                    </Grid>

                    {/* New Payment Success Box */}
                    <Grid item xs={12} sm={6} md={2}>
                        <Box
                            onClick={() => setActiveFilter('payment_success')}
                            sx={{
                                border: '1px solid #ddd',
                                padding: 2,
                                borderRadius: '8px',
                                boxShadow: `-5px 0 0px 0px #2ecc71`,
                                cursor: 'pointer',
                                bgcolor: activeFilter === 'payment_success' ? '#f5f5f5' : 'transparent',
                                '&:hover': { bgcolor: '#f5f5f5' }
                            }}
                        >
                            <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Payment Success</Typography>
                            <Typography variant='h6' sx={{ color: '#2ecc71', fontWeight: 'bold' }}>{successfulPayments}</Typography>
                        </Box>
                    </Grid>

                    {/* Add more boxes as needed */}
                </Grid>
                {/* Table Section */}
                <Box sx={{
                    overflowX: 'auto',
                    width: '100%'
                }}>
                    <CustomTableNew
                        data={getFilteredProposals()}
                        columns={columns}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        onView={handleEdit}
                        selectedRows={selectedRows}
                        onSelectionChange={handleSelectionChange}
                        onSelectAll={handleSelectAll}
                        showEditButton={true}
                        showDeleteButton={true}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.customer_full_name : ''}
            />

            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.customer_full_name : ''}
            />

            <Dialog
                open={openDialog}
                onClose={handleDialogClose}
                aria-labelledby="delete-dialog-title"
                aria-describedby="delete-dialog-description"
            >
                <DialogTitle id="delete-dialog-title">
                    Confirm Deletion
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="delete-dialog-description" sx={{ mb: 2 }}>
                        Are you sure you want to delete this proposal? Please provide a reason for deletion.
                    </DialogContentText>
                    <TextField
                        label="Remarks"
                        multiline
                        rows={3}
                        fullWidth
                        required
                        value={deleteRemarks}
                        onChange={(e) => setDeleteRemarks(e.target.value)}
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                width: '100%'
                            }
                        }}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleDialogClose} color="error">
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmDelete}
                        color="primary"
                        variant="contained"
                        disabled={!deleteRemarks.trim()}
                    >
                        Delete
                    </Button>
                </DialogActions>
            </Dialog>

            <FilePreviewModal
                isOpen={previewOpen}
                onClose={handlePreviewClose}
                fileUrl={selectedFile.url}
                fileName={selectedFile.name}
                isLocalFile={selectedFile.isLocal}
            />
        </Container>
    );
}

export default ProposalListPage;
