import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { getAllDiseases, getDiseaseById, updateDisease, createDisease, searchDisease } from '../../../redux/actions/action';
import { trimFormData } from '../../../utils/Reusable';

function DiseaseTypeForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const disease = useSelector(state => state.diseaseMasterReducer.disease);
  const diseases = useSelector(state => state.diseaseMasterReducer.diseases);


  const [formData, setFormData] = useState({
    disease_name: '',
    disease_description: ''
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (id) {
      dispatch(getDiseaseById(id));
    } else {
      setFormData({
        disease_name: '',
        disease_description: ''
      });
    }
  }, [id, dispatch]);

  useEffect(() => {
    if (disease && id) {
      setFormData({
        disease_name: disease.disease_name || '',
        disease_description: disease.disease_description || '',
      });
    }
  }, [disease, id]);

  // Validation Function
  const validate = () => {
    let tempErrors = {};
    if (!formData.disease_name) {
      tempErrors.disease_name = "Disease Name is required.";
    } else {
      const isDuplicate = diseases.find(disease => disease.disease_name === formData.disease_name && disease.id !== Number(id))
      if (isDuplicate) {
        tempErrors.disease_name = "Disease Name already exists.";
      }
    }
    if (!formData.disease_description) tempErrors.disease_description = "Disease Description is required.";
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (value === ' ') {
      setErrors({
        ...errors,
        [name]: 'Do not start with a whitespace character'
      })
      return;
    }
    const data = value.toUpperCase().replace(/\s{2,}$/, ' ')
    setFormData({
      ...formData,
      [name]: data,
    });
    setErrors({
      ...errors,
      [name]: false,
    })
    if (name === 'disease_name' && value !== '') {
      dispatch(searchDisease(value));
    }
  };

  const handleDiseaseCreationAndUpdate = () => {
    const isValid = validate();
    if (!isValid) return;

    const { disease_name, disease_description } = formData;
    const data =  { disease_name, disease_description };
    const filteredData = trimFormData(data);

    if (id) {
      dispatch(updateDisease({ id, filteredData }));
    } else {
      dispatch(createDisease(filteredData));
    }
    setTimeout(() => {
      dispatch(getAllDiseases());
  }, 500);
  };

  const handleSaveAndNew = () => {
    handleDiseaseCreationAndUpdate();
    if (validate()) {
      setFormData({
        disease_name: '',
        disease_description: ''
      });
    }
  };

  const handleSave = () => {
    handleDiseaseCreationAndUpdate();
    if (validate()) {
      navigate('/dashboard/disease-master');
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/disease-master');
  };

  return (
    <form>
      <Grid container spacing={2}>
        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
          <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
          <Box sx={{ width: '100%', margin: '0 20px' }}>
            <ModuleName moduleName="Disease Type" pageName={id ? disease?.status === 0 ? "View" : "Edit" : "Create"} />
          </Box>
        </Grid>
        <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
          {!id && <Button
            variant="outlined"
            size="small"
            sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
            onClick={handleSaveAndNew}
            disabled={id && disease?.status === 0}
          >
            Save & New
          </Button>}

          {(disease?.status === 1 || !id) && (
            <Button
              variant="outlined"
              size="small"
              sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
              onClick={handleSave}
            >
              Save
            </Button>
          )}
          <Button
            variant="outlined"
            size="small"
            sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </Grid>

        {/* Disease Information */}
        <Grid item xs={12}>
          <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
            <h2>Disease Master Information</h2>
          </Box>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
          {/* Disease Name Custom TextField */}
          <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
            <CustomTextField
              name="disease_name"
              label="Disease Name"
              value={formData.disease_name}
              onChange={handleChange}
              fullWidth
              disabled={id && disease?.status === 0}
              error={!!errors.disease_name}
              helperText={errors.disease_name}
              isRequired
            />
          </Grid>

          <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
            <CustomTextField
              name="disease_description"
              label="Disease Description"
              value={formData.disease_description}
              onChange={handleChange}
              fullWidth
              disabled={id && disease?.status === 0}
              error={!!errors.disease_description}
              helperText={errors.disease_description}
              isRequired
            />
          </Grid>
        </Box>
      </Grid>
    </form>
  );
}

export default DiseaseTypeForm;