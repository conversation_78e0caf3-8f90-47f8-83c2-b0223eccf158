// emailService.js
const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Configure nodemailer transporter
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Define constants for logo path and email signature
const LOGO_PATH = path.join(__dirname, '../public/Images/logo.png');
const EMAIL_SIGNATURE = `
  <br><br>
  <div>
    <p style="margin: 0;">Best Regards,</p>
    <p style="margin: 0;">PRIME WEALTHCARE SOLUTION IMF PVT LTD</p>
    <p style="margin: 0;">+91 91041 05104</p>
  </div>
  <br><br><br>
 <div style="display: flex; align-items: flex-start; justify-content: flex-start;">
    <div style="width: 30%; text-align: left; float: left; margin-right: 20px;"> <!-- Increased logo size and adjusted margin -->
      <img src="cid:primewealthcareLogo" alt="Prime Wealthcare Logo" width="200" height="60"> <!-- Larger logo size -->

      </div>
    <div style="border-left: 0.5px solid #ccc; height: auto; margin: 0 20px;"></div>
    <div style="width: 45%; font-size: 14px;">
      <p style="margin: 2px 0;">PRIME WEALTHCARE SOLUTION IMF PVT LTD</p>
      <p style="margin: 2px 0;">510, Shivalik Satyamev, Nr. Vakil Saheb bridge,</p>
      <p style="margin: 2px 0;">Ambli Bopal Junction, Ahmedabad, 380058</p>
      <p style="margin: 2px 0;"><a href="https://www.primewealthcare.com" style="text-decoration: none;">www.primewealthcare.com</a></p>
    </div>
  </div>
  <br>
  <div style="background-color: #528a7e; color: white; padding: 10px; margin-top: 25px; text-align: center;">
    <p style="margin: 5px 0;">FOLLOW US ON:</p>
    <p style="margin: 5px 0;">
      <a href="https://twitter.com/primewealthcare" style="text-decoration: none; margin-right: 8px;">
        <img src="https://cdn-icons-png.flaticon.com/32/733/733579.png" alt="Twitter" width="24" height="24">
      </a>
      <a href="https://instagram.com/primewealthcare" style="text-decoration: none; margin-right: 8px;">
        <img src="https://cdn-icons-png.flaticon.com/32/2111/2111463.png" alt="Instagram" width="24" height="24">
      </a>
      <a href="https://facebook.com/primewealthcare" style="text-decoration: none;">
        <img src="https://cdn-icons-png.flaticon.com/32/733/733547.png" alt="Facebook" width="24" height="24">
      </a>
    </p>
    <p style="margin: 5px 0;"> +91 9104105104</p>
    <a href="mailto:<EMAIL>" style="text-decoration: none; color: white !important;"><EMAIL></a>
    <p style="margin: 5px 0; font-size: 12px;color: #D3D3D3;">&copy; Prime Wealthcare Solution. All Rights Reserved</p>
  </div>
`;

// Function to send an email
const sendEmail = async (to, subject, text, html) => {
  try {
    // Verify if logo file exists
    if (!fs.existsSync(LOGO_PATH)) {
      console.warn('Logo file not found:', LOGO_PATH);
      // You might want to use a fallback signature without logo
    }

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to,
      subject,
      text,
      html: html + EMAIL_SIGNATURE,
      attachments: [
        {
          filename: 'logo.png',
          path: LOGO_PATH,
          cid: 'primewealthcareLogo',
        },
      ],
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);
    console.log(`Email sent: ${info.response}`);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

// Function to send error notification email
const sendErrorEmail = async (error) => {
  const subject = 'Error in Application';
  const text = `An error occurred: \n\n${error.message || 'Unknown Error'}\n\nStack: \n${error.stack || 'No stack trace available'}`;
  const html = `<h2>An error occurred in your application</h2><p>${error.message || 'Unknown Error'}</p><pre>${error.stack || 'No stack trace available'}</pre>`;

  // Send the error email to a predefined recipient
  await sendEmail(process.env.EMAIL_USER, subject, text, html);
};

module.exports = { sendEmail, sendErrorEmail };