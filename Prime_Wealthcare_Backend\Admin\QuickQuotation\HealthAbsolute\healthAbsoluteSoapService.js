const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');

const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTION; // Use the SOAP Action from the .env file
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

const createSoapEnvelope = (data) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
            <!--Optional:-->
            <tem:Product>HealthAbsolute</tem:Product>
            <!--Optional:-->
            <tem:XML>
                <![CDATA[<Root>
  <Uid>${data.Uid}</Uid>
  <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
  <SentToOutSourcePrint>0</SentToOutSourcePrint>
  <WinNo />
  <ApplicationNo />
  <PolicyHeader>
    <PolicyStartDate>${data.PolicyHeader.PolicyStartDate}</PolicyStartDate>
    <PolicyEndDate>${data.PolicyHeader.PolicyEndDate}</PolicyEndDate>
    <AgentCode>${process.env.AGENT_CODE}</AgentCode>
    <BranchCode>${process.env.BRANCH_CODE}</BranchCode>
    <MajorClass>FHA</MajorClass>
    <ContractType>FHA</ContractType>
    <METHOD>ENQ</METHOD>
    <PolicyIssueType>C</PolicyIssueType>
    <PolicyNo />
    <ClientID></ClientID>
    <ReceiptNo />
  </PolicyHeader>
  <POS_MISP>
    <Type />
    <PanNo />
  </POS_MISP>
  <Client>
    <ClientCategory />
    <ClientType>I</ClientType>
    <CreationType>C</CreationType>
    <Salutation>MR</Salutation>
    <FirstName></FirstName>
    <LastName></LastName>
    <DOB></DOB>
    <Gender>M</Gender>
    <MaritalStatus>M</MaritalStatus>
    <Occupation>SVCM</Occupation>
    <PANNo>**********</PANNo>
    <GSTIN />
    <AadharNo />
    <CKYCNo></CKYCNo>
    <CKYCRefNo></CKYCRefNo>
    <EIANo />
    <Address1>
      <AddrLine1>42 narmda apartmnt</AddrLine1>
      <AddrLine2>KALWAR ROAD</AddrLine2>
      <AddrLine3 />
      <Landmark />
      <Pincode>626138</Pincode>
      <City>Virudhunagar</City>
      <State>Tamil Nadu</State>
      <Country>IND</Country>
      <AddressType>R</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo>9829876493</MobileNo>
      <EmailAddr><EMAIL></EmailAddr>
    </Address1>
    <Address2>
      <AddrLine1>S/O: Laxman Kumawat Jaipur Phulera (HQ Sambhar) Kalwad</AddrLine1>
      <AddrLine2> cholai ki dhani Bassinaga Jaipur Pincode-303706 Rajasthan</AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${data.Client.Address1.Pincode}</Pincode>
      <City></City>
      <State></State>
      <Country>IND</Country>
      <AddressType>K</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo />
      <EmailAddr />
    </Address2>
    <VIPFlag>N</VIPFlag>
    <VIPCategory />
  </Client>
  <Receipt>
    <UniqueTranKey></UniqueTranKey>
    <CheckType />
    <BSBCode />
    <TransactionDate></TransactionDate>
    <ReceiptType>IVR</ReceiptType>
    <Amount></Amount>
    <TCSAmount />
    <TranRefNo></TranRefNo>
    <TranRefNoDate></TranRefNoDate>
  </Receipt>
  <Risk>
    <eNach>N</eNach>
    <PolicyType>${data.Risk.PolicyType}</PolicyType>
    <Duration>${data.Risk.Duration}</Duration>
    <Installments>FULL</Installments>
    <PaymentType>CC</PaymentType>
    <IsFgEmployee>N</IsFgEmployee>
    <BranchReferenceID />
    <FGBankBranchStaffID />
    <BankStaffID />
    <BankCustomerID />
    <BancaChannel />
    <PartnerRefNo />
    <PayorID />
    <PayerName />
    <BeneficiaryDetails>
    ${data.BeneficiaryDetails.Member.map(member => `
      <Member>
        <MemberId>${member.MemberId}</MemberId>
        <AbhaNo />
        <InsuredName></InsuredName>
        <InsuredDob>${member.InsuredDob}</InsuredDob>
        <InsuredGender>M</InsuredGender>
        <InsuredOccpn>SVCM</InsuredOccpn>
        <CoverType>${member.CoverType}</CoverType>
        <SumInsured>${member.SumInsured}</SumInsured>
        <DeductibleDiscount />
        <Relation>${member.Relation}</Relation>
        <NomineeName>Papa </NomineeName>
        <NomineeRelation>FATH</NomineeRelation>
        <AnualIncome />
        <Height>170</Height>
        <Weight>55</Weight>
        <NomineeAge>51</NomineeAge>
        <AppointeeName />
        <AptRelWithNominee />
        <Smoking>N</Smoking>
        <Tobacco>N</Tobacco>
        <IsGoodHealth>Y</IsGoodHealth>
        <IsExistingAbsolutePolicy>N</IsExistingAbsolutePolicy>
        <AdditionalInformation />
        ${generateNomineeDetailsXML({}, member.MemberId - 1, true)}
      </Member>`).join('')}
         </BeneficiaryDetails>
  </Risk>
</Root>]]></tem:XML>
        </tem:CreatePolicy>
    </soapenv:Body>
</soapenv:Envelope>
`;

const sendSOAPRequest = async (data) => {
  try {

    const modifiedData = {
      ...data,
      Product: data.Product,
      VendorUserId: 'webagg',
      PolicyHeader: {
        ...data.PolicyHeader,
        AgentCode: '60048599',
        BranchCode: '51',
        MajorClass: 'HTO',
        ContractType: 'HTO'
      },
      Risk: {
        ...data.Risk,
        PolicyType: data.Risk.PolicyType // Ensure PolicyType is passed
      }
    };

    const soapEnvelope = createSoapEnvelope(modifiedData);
    const headers = {
      "Content-Type": "text/xml; charset=utf-8",
      SOAPAction: SOAP_ACTION,
    };

    const response = await axios.post(SOAP_API_URL, soapEnvelope, { headers });

    const parsedResponse = await parseStringPromise(response.data);

    const createPolicyResult = parsedResponse['s:Envelope']['s:Body'][0]['CreatePolicyResponse'][0]['CreatePolicyResult'][0];

    // Parse the CDATA section if it exists
    let policyDetails;
    try {
      if (createPolicyResult) {
        policyDetails = await parseStringPromise(createPolicyResult);
      }
    } catch (parseError) {
      console.error('Error parsing policy details:', parseError);
    }

     // Extract error message if present
     if (policyDetails && policyDetails.Root && policyDetails.Root.Status && policyDetails.Root.Status[0] === "Fail") {
      const errorMessage = policyDetails.Root.ValidationError ? policyDetails.Root.ValidationError[0] : "Unknown error";
      return {
        status: 'error',
        message: errorMessage,
        timestamp: new Date().toISOString()
      };
    }

    return {
      status: 'success',
      //rawResponse: parsedResponse,
      policyDetails: policyDetails || null,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('SOAP Request Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });

   // throw new Error(`SOAP Request Failed: ${error.message}`);
   return {
    status: 'error',
    message: `SOAP Request Failed: ${error.message}`,
    timestamp: new Date().toISOString()
  };
  }
};

module.exports = { sendSOAPRequest };
