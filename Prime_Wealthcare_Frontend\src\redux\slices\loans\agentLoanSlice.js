import { createSlice } from "@reduxjs/toolkit";
import { getAllAgentLoans, createAgentLoan, updateAgentLoan, deleteAgentLoan, getAgentLoanById, getAgentLoanEmis, updateAgentEmi, deleteAgentEmi } from "../../actions/action";
import { toast } from 'react-toastify'; // Import toast for notifications

const initialState = {
    agentLoans: [],
    agentLoanDetails: {},
    agentLoanEmis: [],
    isLoading: false,
    error: null,
};

const agentLoanSlice = createSlice({
    name: 'agentLoan',
    initialState,
    reducers: {
        clearAgentLoanDetails: (state) => {
            state.agentLoanDetails = {};
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(getAllAgentLoans.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(getAllAgentLoans.fulfilled, (state, action) => {
                state.isLoading = false;
                state.agentLoans = action.payload;
            })
            .addCase(getAllAgentLoans.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent loans.'); // Error message
            })
            .addCase(createAgentLoan.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(createAgentLoan.fulfilled, (state, action) => {
                state.isLoading = false;
                state.agentLoans.push(action.payload);
                //toast.success('Agent loan created successfully!'); // Success message
            })
            .addCase(createAgentLoan.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to create agent loan.'); // Error message
            })
            .addCase(updateAgentLoan.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(updateAgentLoan.fulfilled, (state, action) => {
                state.isLoading = false;
                const index = state.agentLoans.findIndex(loan => loan.id === action.payload.id);
                if (index !== -1) {
                    state.agentLoans[index] = action.payload;
                    //toast.success('Agent loan updated successfully!'); // Success message
                }
            })
            .addCase(updateAgentLoan.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to update agent loan.'); // Error message
            })
            .addCase(deleteAgentLoan.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(deleteAgentLoan.fulfilled, (state, action) => {
                state.isLoading = false;
                state.agentLoans = state.agentLoans.filter(loan => loan.id !== action.payload.id);
                //toast.success('Agent loan deleted successfully!'); // Success message
            })
            .addCase(deleteAgentLoan.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to delete agent loan.'); // Error message
            })
            .addCase(getAgentLoanById.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(getAgentLoanById.fulfilled, (state, action) => {
                state.isLoading = false;
                state.agentLoanDetails = action.payload;
                //toast.success('Agent loan details fetched successfully!'); // Success message
            })
            .addCase(getAgentLoanById.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent loan details.'); // Error message
            })
            .addCase(getAgentLoanEmis.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(getAgentLoanEmis.fulfilled, (state, action) => {
                state.isLoading = false;
                state.agentLoanEmis = action.payload;
                //toast.success('Loan EMIs fetched successfully!'); // Success message
            })
            .addCase(getAgentLoanEmis.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch loan EMIs.'); // Error message
            })
            .addCase(updateAgentEmi.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(updateAgentEmi.fulfilled, (state, action) => {
                state.isLoading = false;
                toast.success('EMI updated successfully!'); // Success message
            })
            .addCase(updateAgentEmi.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to update EMI.'); // Error message
            })
            .addCase(deleteAgentEmi.pending, (state) => {
                state.isLoading = true;
            })
            .addCase(deleteAgentEmi.fulfilled, (state, action) => {
                state.isLoading = false;
                state.agentLoanEmis = state.agentLoanEmis.filter(emi => emi.id !== action.payload.id);
                //toast.success('EMI deleted successfully!'); // Success message
            })
            .addCase(deleteAgentEmi.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to delete EMI.'); // Error message
            });
    }
});

export const { clearAgentLoanDetails } = agentLoanSlice.actions;
export default agentLoanSlice.reducer;