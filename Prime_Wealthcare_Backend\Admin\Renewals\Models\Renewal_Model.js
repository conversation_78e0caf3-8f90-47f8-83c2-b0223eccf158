const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');

class RenewalModel {
    static async saveRenewalsMapping(renewals, insurance_company_id, user_id) {
        const client = await db.transaction();
        try {
            await client.raw('BEGIN');

            const savedRenewals = [];
            for (const renewal of renewals) {
                const query = `
                    INSERT INTO renewals_mapping (
                        customer_name,
                        customer_number,
                        imf_branch_name,
                        old_policy_number,
                        expiry_date,
                        month,
                        insurance_company_id,
                        insurance_company,
                        main_product,
                        product,
                        sub_product,
                        number_of_members,
                        premium,
                        agent_code,
                        agent_name,
                        renewal_status,
                        created_by,
                        updated_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $17)
                    RETURNING *
                `;

                const values = [
                    renewal.customer_name,
                    renewal.customer_number,
                    renewal.imf_branch_name,
                    renewal.old_policy_number,
                    renewal.expiry_date,
                    renewal.month,
                    insurance_company_id,
                    renewal.insurance_company,
                    renewal.main_product,
                    renewal.product,
                    renewal.sub_product,
                    renewal.number_of_members,
                    renewal.premium,
                    renewal.agent_code,
                    renewal.agent_name,
                    renewal.renewal_status,
                    user_id
                ];

                const result = await client.query(query, values);
                savedRenewals.push(result.rows[0]);
            }

            await client.query('COMMIT');
            return savedRenewals;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }
}

module.exports = RenewalModel;