import { createSlice } from '@reduxjs/toolkit';
import {
  fetchInsuranceCompanyBranches,
  fetchInsuranceCompanyBranchById,
  createInsuranceCompanyBranch,
  updateInsuranceCompanyBranch,
  softDeleteInsuranceCompanyBranch,
  reinstateInsuranceCompanyBranch,createBranchAndAgencyCode,
  searchInsuranceBranches,fetchInsuranceBranchesByCriteria,
  fetchInsuranceCompanyBranchByInsuranceCompanyId,
  //fetchInsuranceCompanies,fetchBranchAndAgencyCode
} from '../../actions/action';

const insuranceBranchSlice = createSlice({
  name: 'insuranceBranch',
  initialState: {
    branches: [],
    selectedBranch: null,
    loading: false,
    error: null,
    searchResults: [],
    createdBranch: null,
  },
  reducers: {
    clearInsuranceBranch: (state) => {
      state.branches = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchInsuranceCompanyBranches.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchInsuranceCompanyBranches.fulfilled, (state, action) => {
        state.branches = action.payload;
        state.loading = false;
      })
      .addCase(fetchInsuranceCompanyBranches.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to fetch branches';
        state.loading = false;
      })
      .addCase(fetchInsuranceCompanyBranchById.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchInsuranceCompanyBranchById.fulfilled, (state, action) => {
        state.selectedBranch = action.payload;
        state.loading = false;
      })
      .addCase(fetchInsuranceCompanyBranchById.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to fetch branch by ID';
        state.loading = false;
      })
      .addCase(fetchInsuranceCompanyBranchByInsuranceCompanyId.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchInsuranceCompanyBranchByInsuranceCompanyId.fulfilled, (state, action) => {
        state.branches = action.payload;
        state.loading = false;
      })
      .addCase(fetchInsuranceCompanyBranchByInsuranceCompanyId.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to fetch branches';
        state.loading = false;
      })
      .addCase(createInsuranceCompanyBranch.pending, (state) => {
        state.loading = true;
      })
      .addCase(createInsuranceCompanyBranch.fulfilled, (state, action) => {
        state.branches.push(action.payload);
        state.loading = false;
      })
      .addCase(createInsuranceCompanyBranch.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to create branch';
        state.loading = false;
      })
      .addCase(updateInsuranceCompanyBranch.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateInsuranceCompanyBranch.fulfilled, (state, action) => {
        const index = state.branches.findIndex(branch => branch.id === action.payload.id);
        if (index !== -1) {
          // Replace the existing branch with the updated branch
          state.branches[index] = { ...state.branches[index], ...action.payload };
        }
        state.loading = false;
      })
      .addCase(updateInsuranceCompanyBranch.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to update branch';
        state.loading = false;
      })
      .addCase(softDeleteInsuranceCompanyBranch.pending, (state) => {
        state.loading = true;
      })
      .addCase(softDeleteInsuranceCompanyBranch.fulfilled, (state, action) => {
        const index = state.branches.findIndex(branch => branch.id === action.meta.arg);
        if (index !== -1) {
          // Mark as soft deleted in the frontend
          state.branches[index].status = 0;
        }
        state.loading = false;
      })
      
      .addCase(softDeleteInsuranceCompanyBranch.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to delete branch';
        state.loading = false;
      })
      .addCase(reinstateInsuranceCompanyBranch.pending, (state) => {
        state.loading = true;
      })
      .addCase(reinstateInsuranceCompanyBranch.fulfilled, (state, action) => {
        const index = state.branches.findIndex(branch => branch.id === action.meta.arg);
        if (index !== -1) {
          // Mark as reinstated in the frontend
          state.branches[index].status = 1;
        }
        state.loading = false;
      })
      
      .addCase(reinstateInsuranceCompanyBranch.rejected, (state, action) => {
        state.error = action.error?.message || 'Failed to reinstate branch';
        state.loading = false;
      })

      .addCase(fetchInsuranceBranchesByCriteria.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInsuranceBranchesByCriteria.fulfilled, (state, action) => {
        state.loading = false;
        state.branches = action.payload;
      })
      .addCase(fetchInsuranceBranchesByCriteria.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      })
      // Search branches
      .addCase(searchInsuranceBranches.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchInsuranceBranches.fulfilled, (state, action) => {
        state.loading = false;
        state.branches = action.payload;
      })
      .addCase(searchInsuranceBranches.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
  
      // Create branch and agency code together
      .addCase(createBranchAndAgencyCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createBranchAndAgencyCode.fulfilled, (state, action) => {
        state.loading = false;
        state.createdBranch = action.payload;
      })
      .addCase(createBranchAndAgencyCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
   
    /*  .addCase(fetchBranchAndAgencyCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBranchAndAgencyCode.fulfilled, (state, action) => {
        state.branches = action.payload; // Ensure this is an array
        state.loading = false;
      })
      .addCase(fetchBranchAndAgencyCode.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch data';
        state.loading = false;
      });
       */
  },
});

export const { clearInsuranceBranch } = insuranceBranchSlice.actions;
export default insuranceBranchSlice.reducer;