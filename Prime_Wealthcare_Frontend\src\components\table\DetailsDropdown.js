import React, { useState } from 'react';
import {
    Box,
    Typography,
    IconButton,
    Collapse
} from '@mui/material';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import DetailsTable from './DetailsTable'; // Import the DetailsTable component
import SearchBar from './SearchBar';

const DetailsDropdown = ({ headerText, tableHeadings, tableData = [], onEdit, onView, handleCreate, showSearch = false, optionsColumn }) => {
    const [page, setPage] = useState(0);
    const [open, setOpen] = useState(false);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [searchQuery, setSearchQuery] = useState('');

    const handleToggleOpen = (event) => {
        if (event.target.closest('.no-toggle')) {
            return;
        }
        setOpen((prev) => !prev);
    };

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleSearch = (query) => {
        setSearchQuery(query);
    };

    const filteredData = (tableData || []).filter((row) =>
        Object.values(row).some((value) =>
            value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    return (
        <Box
            sx={{
                borderRadius: '1rem',
                border: '1px solid black',
                overflow: 'hidden',
                margin: '0 1rem',
                padding: '0.5rem 1rem'
            }}
        >
            {/* Header section with arrow */}
            <Box
                display="flex"
                alignItems="center"
                mb={2}
                sx={{
                    cursor: 'pointer',
                    marginTop: '0.5rem',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}
                onClick={handleToggleOpen}
            >
                <Box display='flex'>
                    <ArrowForwardIosIcon
                        sx={{
                            width: '1rem',
                            transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                            transition: 'transform 0.3s',
                        }}
                    />
                    <Typography sx={{ marginLeft: '0.5rem' }}>
                        <strong>{headerText}</strong>
                    </Typography>
                </Box>
                <Box display='flex' alignItems='center' gap='1rem' className="no-toggle">
                    {showSearch && <SearchBar height='2rem' placeholder="Search..." onSearch={handleSearch} />}
                    <Typography onClick={handleCreate} sx={{ marginLeft: 'auto', color: '#528a7e' }}>{'Create'}</Typography>
                </Box>
            </Box>

            <DetailsTable
                open={open}
                tableHeadings={tableHeadings}
                tableData={filteredData}
                page={page}
                rowsPerPage={rowsPerPage}
                onEdit={onEdit}
                onView={onView}
                handleChangePage={handleChangePage}
                handleChangeRowsPerPage={handleChangeRowsPerPage}
                optionsColumn={optionsColumn}
                noDataMessage="No data present"
            />
        </Box>
    );
};

export default DetailsDropdown;
