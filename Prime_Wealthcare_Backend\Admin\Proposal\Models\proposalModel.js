const knexConfig = require('../../../knexfile');
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

exports.createProposal = async (proposalData, memberData) => {
    // Use transaction to ensure both operations succeed or fail together
    return db.transaction(async trx => {
        try {
            const subProduct = await trx('sub_product').where('id', proposalData.sub_product).first();
            const proposalTableName = subProduct?.sub_product_name?.toLowerCase()?.includes('accident suraksha') ? 'proposals_pa' : 'proposals';
            const proposalMembersTableName = subProduct?.sub_product_name?.toLowerCase()?.includes('accident suraksha') ? 'proposal_members_pa' : 'proposal_members';
            // Insert proposal and get the id
            const [proposalId] = await trx(proposalTableName)
                .insert(proposalData)
                .returning('id');

            // Add proposal_id to each member record
            const membersToInsert = memberData.map(member => ({
                ...member,
                proposal_id: proposalId
            }));

            // Insert all members
            await trx(proposalMembersTableName)
                .insert(membersToInsert);


            // Update quotations table status to 'submitted'
            // if (proposalData.quotation_number) {
            //     await trx('quotations')
            //         .where('quotation_number', proposalData.quotation_number)
            //         .update({ status: 'SUBMITTED' });
            // }
            // Update quotations table status to 'submitted'
            if (proposalData.quotation_number) {
                // Check and update in quotations table
                const updatedQuotations = await trx('quotations')
                    .where('quotation_number', proposalData.quotation_number)
                    .update({ status: 'SUBMITTED', updated_at: getCurrentTimestamp() });

                // If no rows were updated, check in pa_quotations table
                if (updatedQuotations === 0) {
                    await trx('pa_quotations')
                        .where('quotation_number', proposalData.quotation_number)
                        .update({ status: 'SUBMITTED', updated_at: getCurrentTimestamp() });
                }
            }

            const createdProposalId = proposalTableName === 'proposals_pa' ? (proposalId + "PA") : proposalId;

            return { proposalId: createdProposalId, success: true };
        } catch (error) {
            throw error;
        }
    });
};

exports.updateProposal = async (id, proposalData, memberData) => {
    return db.transaction(async trx => {
        try {
            let fetchedId = id;
            let proposalTableName = 'proposals';
            let proposalMembersTableName = 'proposal_members';
            if (fetchedId.includes("PA")) {
                fetchedId = Number(fetchedId.replace("PA", ""));
                proposalTableName = 'proposals_pa';
                proposalMembersTableName = 'proposal_members_pa';
            }
            // Update proposal
            const proposal = await trx(proposalTableName)
                .where('id', fetchedId)
                .update(proposalData);

            if (proposal && memberData && memberData.length > 0) {
                // Wait for all member updates to complete
                await Promise.all(memberData.map(member => {
                    // Only update if member.id exists
                    if (!member.id) {
                        return Promise.resolve();
                    }

                    // Remove any undefined values from the member object
                    const cleanMemberData = Object.fromEntries(
                        Object.entries(member).filter(([_, v]) => v !== undefined)
                    );

                    return trx(proposalMembersTableName)
                        .where('id', member.id)
                        .update(cleanMemberData);
                }));
            }

            return proposal;
        } catch (error) {
            throw error;
        }
    });
};

exports.getProposalById = async (proposalId) => {
    try {
        let fetchedId = proposalId;
        let proposalTableName = 'proposals';
        let proposalMembersTableName = 'proposal_members';

        if (proposalId.includes("PA")) {
            fetchedId = Number(proposalId.replace("PA", ""));
            proposalTableName = 'proposals_pa';
            proposalMembersTableName = 'proposal_members_pa';
        }

        // Get the proposal
        const proposal = await db(proposalTableName)
            .where('id', fetchedId)
            .first();

        if (!proposal) {
            return null;
        }

        // Get associated members
        const members = await db(proposalMembersTableName)
            .where('proposal_id', fetchedId);

        // Get quotation creation date if quotation_id exists
        let quotationCreatedAt = null;
        if (proposal.quotation_id) {
            const quotation = await db('quotations')
                .where('quotation_id', proposal.quotation_id)
                .select('Created_at')
                .first();
            quotationCreatedAt = quotation?.Created_at;
        }

        return {
            ...proposal,
            id: proposalId.includes("PA") ? `${proposal.id}PA` : proposal.id,
            quotation_created_at: quotationCreatedAt,
            members
        };
    } catch (error) {
        throw error;
    }
};

exports.getProposalByQuotationNumber = async (quotation_number) => {
    try {
        // Get the proposal
        let proposal = await db('proposals')
            .where('quotation_number', quotation_number)
            .first();

        let proposalMembersTableName = 'proposal_members';

        if (!proposal) {
            proposal = await db('proposals_pa')
                .where('quotation_number', quotation_number)
                .first();
            proposalMembersTableName = 'proposal_members_pa';
        }

        if (!proposal) {
            return null;
        }

        const members = await db(`${proposalMembersTableName} as pm`)
            .select([
                'pm.*',
                db.raw(`
            CASE 
                WHEN pm.relation = '39' THEN c.first_name
                ELSE cm.full_name
            END as first_name,
            CASE 
                WHEN pm.relation = '39' THEN c.middle_name
                ELSE NULL
            END as middle_name,
            CASE 
                WHEN pm.relation = '39' THEN c.last_name
                ELSE NULL
            END as last_name
        `)
            ])
            .where('pm.proposal_id', proposal.id)
            .leftJoin('customer_personal_info as c', 'pm.member_id', 'c.id')
            .leftJoin('customer_member_info as cm', 'pm.member_id', 'cm.id');

        return {
            ...proposal,
            id: proposalMembersTableName === 'proposal_members_pa' ? `${proposal.id}PA` : proposal.id,
            members
        };
    } catch (error) {
        throw error;
    }
};

exports.getProposalByPolicyNumber = async ({ policy_number, policy_type }) => {
    try {
        // Get the proposal from multiple tables
        let proposal;
        let proposalMembersTableName;
        let isRollover = false;

        // First check in regular proposals
        proposal = await db('proposals')
            .where('policy_number', policy_number)
            .where('status', '!=', 'CANCELLED')
            .first();
        proposalMembersTableName = 'proposal_members';

        // If not found, check in PA proposals
        if (!proposal) {
            proposal = await db('proposals_pa')
                .where('policy_number', policy_number)
                .where('status', '!=', 'CANCELLED')
                .first();
            if (proposal) {
                proposalMembersTableName = 'proposal_members_pa';
            }
        }

        // If not found in either, check in rollover migrations
        if (!proposal) {
            proposal = await db('proposals_rollover_migration')
                .where('policy_number', policy_number)
                .where('status', '!=', 'CANCELLED')
                .first();
            if (proposal) {
                proposalMembersTableName = 'proposals_rollover_migration_members';
                isRollover = true;
            }
        }

        // If still not found, return error
        if (!proposal) {
            return {
                error: 'Policy not found.',
            };
        }

        const proposalNumber = isRollover ? proposal?.proposal_Number : proposal?.ProposalNumber || '';

        // Check for duplicate proposals

        let duplicateCheck = await db('proposals')
            .where('prev_proposal_number', proposalNumber);
        if (!duplicateCheck) {
            duplicateCheck = await db('proposals_pa')
                .where('prev_proposal_number', proposalNumber);
            if (!duplicateCheck) {
                duplicateCheck = await db('proposals_rollover_migration')
                    .where('prev_proposal_number', proposalNumber);
            }
        }

        if (duplicateCheck && duplicateCheck.length > 0) {
            const tempPolicyType = policy_type.toLowerCase();
            return {
                error: tempPolicyType === 'renew' ? 'Policy cannot be renewed.' : tempPolicyType === "roll_over" ? 'Policy cannot be ported' : 'Policy cannot be migrated.',
            };
        }

        // Date validation
        if (proposal?.end_date) {
            const endDate = new Date(proposal.end_date);
            const currentDate = new Date();
            const before30 = new Date(endDate);
            before30.setDate(endDate.getDate() - 30);
            const after30 = new Date(endDate);
            after30.setDate(endDate.getDate() + 30);

            let isInTimeLimit = false;
            const policyType = policy_type.toLowerCase();

            if (policyType === 'renew') {
                isInTimeLimit = currentDate >= before30 && currentDate <= after30;
            } else if (policyType === 'roll_over' || policyType === 'migration') {
                isInTimeLimit = currentDate >= before30 && currentDate <= endDate;
            }

            if (!isInTimeLimit) {
                if (currentDate < before30) {
                    return {
                        error: 'You cannot access this policy before 30 days of expiry.',
                    };
                } else {
                    return {
                        error: 'Policy expired.',
                    };
                }
            }
        }

        // Get associated members
        const members = await db(proposalMembersTableName)
            .where(isRollover ? 'prm_id' : 'proposal_id', proposal.id);

        // Get payment details if applicable
        let paymentData = null;
        if (proposalNumber) {
            paymentData = await db('payment_master')
                .where('ProposalNumber', proposalNumber)
                .first();
        }

        // Return combined data
        return {
            ...proposal,
            id: proposalMembersTableName === 'proposal_members_pa' ? `${proposal.id}PA` : proposal.id,
            members,
            payment: paymentData,
            isRollover
        };

    } catch (error) {
        throw error;
    }
};

exports.getAllProposals = async () => {
    try {
        // Query for regular proposals
        const regularProposals = db('proposals as p')
            .select([
                'p.*',
                'p.ProposalNumber as proposal_number',
                'p.Created_at as proposal_date',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL AND c.company_name != '' THEN c.company_name 
                    ELSE CONCAT(
                        COALESCE(c.first_name, ''), 
                        CASE WHEN c.middle_name IS NOT NULL AND c.middle_name != '' THEN CONCAT(' ', c.middle_name) ELSE '' END,
                        CASE WHEN c.last_name IS NOT NULL AND c.last_name != '' THEN CONCAT(' ', c.last_name) ELSE '' END
                    )
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name',
                db.raw('MIN(spa.sum_insured) as sum_insured'),
                db.raw('COALESCE(p.ckyc_number, p.proposal_id) as ckyc_status'),
                'pay.Status as payment_status'
            ])
            .leftJoin('customer_personal_info as c', 'p.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'p.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id')
            .leftJoin('sub_product_age_sum as spa', 'sp.id', 'spa.sub_product_id')
            // Get latest payment status
            .leftJoin(
                db('payment_master')
                    .select('ProposalNumber',
                        db.raw('MAX(created_at) as latest_payment_date'))
                    .groupBy('ProposalNumber')
                    .as('latest_payments'),
                'p.ProposalNumber', 'latest_payments.ProposalNumber'
            )
            .leftJoin('payment_master as pay', function () {
                this.on('p.ProposalNumber', '=', 'pay.ProposalNumber')
                    .andOn('pay.created_at', '=', 'latest_payments.latest_payment_date')
            })
            .groupBy([
                'p.id',
                'p.policy_number',
                'p.ProposalNumber',
                'p.Created_at',
                'c.company_name',
                'c.first_name',
                'c.middle_name',
                'c.last_name',
                'ic.insurance_company_name',
                'a.agent_id',
                'pm.product_name',
                'sp.sub_product_name',
                'p.proposal_id',
                'p.proposal_type',
                'pay.Status',
            ])
            .orderBy('p.Created_at', 'desc');


        // Query for PA proposals
        const paProposals = db('proposals_pa as p')
            .select([
                'p.*',
                'p.ProposalNumber as proposal_number',
                'p.Created_at as proposal_date',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL AND c.company_name != '' THEN c.company_name 
                    ELSE CONCAT(
                        COALESCE(c.first_name, ''), 
                        CASE WHEN c.middle_name IS NOT NULL AND c.middle_name != '' THEN CONCAT(' ', c.middle_name) ELSE '' END,
                        CASE WHEN c.last_name IS NOT NULL AND c.last_name != '' THEN CONCAT(' ', c.last_name) ELSE '' END
                    )
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name',
                db.raw('MIN(spa.sum_insured) as sum_insured'),
                db.raw('COALESCE(p.ckyc_number, p.proposal_id) as ckyc_status'),
                'pay.Status as payment_status'
            ])
            .leftJoin('customer_personal_info as c', 'p.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'p.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id')
            .leftJoin('sub_product_age_sum as spa', 'sp.id', 'spa.sub_product_id')
            .leftJoin(
                db('payment_master')
                    .select('ProposalNumber', db.raw('MAX(created_at) as latest_payment_date'))
                    .groupBy('ProposalNumber')
                    .as('latest_payments'),
                'p.ProposalNumber',
                'latest_payments.ProposalNumber'
            )
            .leftJoin('payment_master as pay', function () {
                this.on('p.ProposalNumber', '=', 'pay.ProposalNumber')
                    .andOn('pay.created_at', '=', 'latest_payments.latest_payment_date');
            })
            .groupBy([
                'p.id',
                'p.policy_number',
                'p.ProposalNumber',
                'p.Created_at',
                'c.company_name',
                'c.first_name',
                'c.middle_name',
                'c.last_name',
                'ic.insurance_company_name',
                'a.agent_id',
                'pm.product_name',
                'sp.sub_product_name',
                'p.proposal_id',
                'p.proposal_type',
                'pay.Status'
            ]);


        // Combine and execute both queries
        const [regularResults, paResults] = await Promise.all([regularProposals, paProposals]);

        // Update PA proposal IDs to include "PA" suffix
        const modifiedPaResults = paResults.map(proposal => ({
            ...proposal,
            id: proposal.id.toString().includes('PA') ? proposal.id : proposal.id + 'PA'
        }));

        // Combine and sort results by creation date
        const allProposals = [...regularResults, ...modifiedPaResults]
            .sort((a, b) => new Date(b.Created_at) - new Date(a.Created_at))
            .map(proposal => ({
                ...proposal,
                status: proposal.status?.toLowerCase() === 'cancelled' ? 0 : 1
            }));
        return allProposals;
    } catch (error) {
        console.error('Error in getAllProposals:', error);
        throw error;
    }
};

exports.getAllProposalsByUserId = async (userId) => {
    try {
        // Function to get agent IDs for employee
        const getAgentIdsForEmployee = async (userId) => {
            const employee = await db('employee_personal_info')
                .where('user_id', userId)
                .first();

            if (!employee || !employee.branch_id) {
                return [];
            }

            const branchIds = employee.branch_id.split(',').map(id => id.trim());
            const agents = await db('agents')
                .whereIn('branch_id', branchIds)
                .select('id');

            return agents.map(agent => agent.id);
        };

        // Regular proposals query
        let regularProposalsQuery = db('proposals as p')
            .select([
                'p.*',
                'p.ProposalNumber as proposal_number',
                'p.Created_at as proposal_date',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL AND c.company_name != '' THEN c.company_name 
                    ELSE CONCAT(
                        COALESCE(c.first_name, ''), 
                        CASE WHEN c.middle_name IS NOT NULL AND c.middle_name != '' THEN CONCAT(' ', c.middle_name) ELSE '' END,
                        CASE WHEN c.last_name IS NOT NULL AND c.last_name != '' THEN CONCAT(' ', c.last_name) ELSE '' END
                    )
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name',
                db.raw('MIN(spa.sum_insured) as sum_insured'),
                db.raw('COALESCE(p.ckyc_number, p.proposal_id) as ckyc_status'),
                'pay.Status as payment_status',
            ])
            .leftJoin('customer_personal_info as c', 'p.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'p.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id')
            .leftJoin('sub_product_age_sum as spa', 'sp.id', 'spa.sub_product_id')
            .leftJoin(
                db('payment_master')
                    .select('ProposalNumber', db.raw('MAX(created_at) as latest_payment_date'))
                    .groupBy('ProposalNumber')
                    .as('latest_payments'),
                'p.ProposalNumber', 'latest_payments.ProposalNumber'
            )
            .leftJoin('payment_master as pay', function () {
                this.on('p.ProposalNumber', '=', 'pay.ProposalNumber')
                    .andOn('pay.created_at', '=', 'latest_payments.latest_payment_date')
            });

        // PA proposals query
        let paProposalsQuery = db('proposals_pa as p')
            .select([
                'p.*',
                'p.ProposalNumber as proposal_number',
                'p.Created_at as proposal_date',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL AND c.company_name != '' THEN c.company_name 
                    ELSE CONCAT(
                        COALESCE(c.first_name, ''), 
                        CASE WHEN c.middle_name IS NOT NULL AND c.middle_name != '' THEN CONCAT(' ', c.middle_name) ELSE '' END,
                        CASE WHEN c.last_name IS NOT NULL AND c.last_name != '' THEN CONCAT(' ', c.last_name) ELSE '' END
                    )
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name',
                db.raw('MIN(spa.sum_insured) as sum_insured'),
                db.raw('COALESCE(p.ckyc_number, p.proposal_id) as ckyc_status'),
                'pay.Status as payment_status',
                db.raw("'pa' as proposal_category")
            ])
            // ... existing joins ...
            .leftJoin('customer_personal_info as c', 'p.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'p.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'p.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'p.sub_product', 'sp.id')
            .leftJoin('sub_product_age_sum as spa', 'sp.id', 'spa.sub_product_id')
            .leftJoin(
                db('payment_master')
                    .select('ProposalNumber', db.raw('MAX(created_at) as latest_payment_date'))
                    .groupBy('ProposalNumber')
                    .as('latest_payments'),
                'p.ProposalNumber', 'latest_payments.ProposalNumber'
            )
            .leftJoin('payment_master as pay', function () {
                this.on('p.ProposalNumber', '=', 'pay.ProposalNumber')
                    .andOn('pay.created_at', '=', 'latest_payments.latest_payment_date')
            });

        // NEW: Rollover/Migration proposals query
        let rolloverMigrationQuery = db('proposals_rollover_migration as rm')
            .select([
                'rm.*',
                'rm.proposal_Number as proposal_number',
                'rm.Created_at as proposal_date',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL AND c.company_name != '' THEN c.company_name 
                    ELSE CONCAT(
                        COALESCE(c.first_name, ''), 
                        CASE WHEN c.middle_name IS NOT NULL AND c.middle_name != '' THEN CONCAT(' ', c.middle_name) ELSE '' END,
                        CASE WHEN c.last_name IS NOT NULL AND c.last_name != '' THEN CONCAT(' ', c.last_name) ELSE '' END
                    )
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name',
                db.raw('NULL as sum_insured'),
                db.raw('NULL as ckyc_status'),
                'pay.Status as payment_status',
            ])
            .leftJoin('customer_personal_info as c', 'rm.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'rm.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'rm.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'rm.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'rm.sub_product', 'sp.id')
            // Get latest payment status
            .leftJoin(
                db('payment_master')
                    .select('ProposalNumber',
                        db.raw('MAX(created_at) as latest_payment_date'))
                    .groupBy('ProposalNumber')
                    .as('latest_payments'),
                'rm.proposal_Number', 'latest_payments.ProposalNumber'
            )
            .leftJoin('payment_master as pay', function () {
                this.on('rm.proposal_Number', '=', 'pay.ProposalNumber')
                    .andOn('pay.created_at', '=', 'latest_payments.latest_payment_date')
            });

        // Apply filters based on user role
        if (userId.includes('ADM')) {
            // Admin sees all proposals, no additional filters needed
        } else if (userId.includes('RM')) {
            // RM sees only their proposals
            const agent = await db('agents').where('agent_id', userId).first();
            if (!agent) {
                return [];
            }
            regularProposalsQuery = regularProposalsQuery.where('p.agent_code', agent.id);
            paProposalsQuery = paProposalsQuery.where('p.agent_code', agent.id);
            rolloverMigrationQuery = rolloverMigrationQuery.where('rm.agent_code', agent.id);
        } else {
            // Other employees see proposals for agents in their branches
            const agentIds = await getAgentIdsForEmployee(userId);
            if (agentIds.length === 0) {
                return [];
            }
            regularProposalsQuery = regularProposalsQuery.whereIn('p.agent_code', agentIds);
            paProposalsQuery = paProposalsQuery.whereIn('p.agent_code', agentIds);
            rolloverMigrationQuery = rolloverMigrationQuery.whereIn('rm.agent_code', agentIds);
        }

        // Add grouping and ordering
        regularProposalsQuery = regularProposalsQuery
            .groupBy([
                'p.id', 'p.policy_number', 'p.ProposalNumber', 'p.Created_at',
                'c.company_name', 'c.first_name', 'c.middle_name', 'c.last_name',
                'ic.insurance_company_name', 'a.agent_id', 'pm.product_name',
                'sp.sub_product_name', 'p.proposal_id', 'pay.Status'
            ])
            .orderBy('p.Created_at', 'desc');

        paProposalsQuery = paProposalsQuery
            .groupBy([
                'p.id', 'p.policy_number', 'p.ProposalNumber', 'p.Created_at',
                'c.company_name', 'c.first_name', 'c.middle_name', 'c.last_name',
                'ic.insurance_company_name', 'a.agent_id', 'pm.product_name',
                'sp.sub_product_name', 'p.proposal_id', 'pay.Status'
            ])
            .orderBy('p.Created_at', 'desc');

        rolloverMigrationQuery = rolloverMigrationQuery
            .groupBy([
                'rm.id', 'rm.policy_number', 'rm.proposal_Number', 'rm.Created_at',
                'c.company_name', 'c.first_name', 'c.middle_name', 'c.last_name',
                'ic.insurance_company_name', 'a.agent_id', 'pm.product_name',
                'sp.sub_product_name', 'pay.Status'
            ])
            .orderBy('rm.Created_at', 'desc');

        // Execute queries
        const [regularResults, paResults, rolloverMigrationResults] = await Promise.all([
            regularProposalsQuery,
            paProposalsQuery,
            rolloverMigrationQuery
        ]);

        // Update PA proposal IDs to include "PA" suffix
        const modifiedPaResults = paResults.map(proposal => ({
            ...proposal,
            id: proposal.id.toString().includes('PA') ? proposal.id : proposal.id + 'PA'
        }));

        // Format rollover migration results to match proposal structure
        const formattedRolloverResults = rolloverMigrationResults.map(migration => ({
            ...migration,
            proposal_type: migration.proposal_type || 'Migration',
            ProposalNumber: migration.proposal_Number, // Ensure consistent field naming
            policy_status: migration.status || 'ACTIVE'
        }));

        // Combine and sort results by creation date
        const allProposals = [...regularResults, ...modifiedPaResults, ...formattedRolloverResults]
            .sort((a, b) => new Date(b.Created_at) - new Date(a.Created_at))
            .map(proposal => ({
                ...proposal,
                status: proposal.status?.toLowerCase() === 'cancelled' ? 0 : 1
            }));
        return allProposals;
    } catch (error) {
        console.error('Error in getAllProposalsByUserId:', error);
        throw error;
    }
};

exports.getProposalByNumber = async (proposalNumber) => {
    try {
        // Get the proposal
        const proposal = await db('proposals')
            .where('proposal_number', proposalNumber)
            .first();

        if (!proposal) {
            const proposal_pa = await db('proposals_pa')
                .where('proposal_number', proposalNumber)
                .first();
            const members_pa = await db('proposal_members_pa')
                .where('proposal_id', proposal_pa.id);
            const returning_proposal = {
                ...proposal_pa,
                id: (proposal_pa.id + "PA"),
                members: members_pa
            }
            if (!proposal_pa) {
                return null;
            }
            return returning_proposal;
        } else {
            const members = await db('proposal_members')
                .where('proposal_id', proposal.id);
            return {
                ...proposal,
                members
            };
        }

        // Get associated members
    } catch (error) {
        throw error;
    }
};

exports.getProposalCount = async (prefix) => {
    try {
        const count = await db('proposals')
            .where('ProposalNumber', 'like', `${prefix}%`)
            .count('* as count')
        const count2 = await db('proposals_pa')
            .where('ProposalNumber', 'like', `${prefix}%`)
            .count('* as count')
        const count3 = await db('proposals_rollover_migration')
            .where('proposal_Number', 'like', `${prefix}%`)
            .count('* as count')
        return count[0].count + count2[0].count + count3[0].count;
    } catch (error) {
        console.error("Error getting proposal count:", error);
        throw error;
    }
};

exports.deleteProposal = async (id, policy_type, proposal_number, remarks) => {
    try {
        let fetchedId = id;
        let proposalTableName;

        // Determine which tables to use based on id and policy_type
        if (fetchedId.includes("PA")) {
            fetchedId = Number(fetchedId.replace("PA", ""));
            proposalTableName = 'proposals_pa';
        } else if (policy_type?.toLowerCase() === 'new' || policy_type?.toLowerCase() === 'renew') {
            proposalTableName = 'proposals';
        } else {
            proposalTableName = 'proposals_rollover_migration';
        }

        // Get the original proposal record
        const originalProposal = await db(proposalTableName)
            .where('id', fetchedId)
            .first();

        if (!originalProposal) {
            throw new Error('Proposal not found');
        }


        // Create new cancellation proposal record
        const cancellationProposal = {
            ...originalProposal,
            id: undefined, // Let DB auto-generate new ID
            quotation_number: null,

            // Make amounts negative
            net_premium: originalProposal.net_premium ? -Math.abs(originalProposal.net_premium) : null,
            total_premium: originalProposal.total_premium ? -Math.abs(originalProposal.total_premium) : null,
            gst_amount: originalProposal.gst_amount ? -Math.abs(originalProposal.gst_amount) : null,

            // Update status and timestamps
            status: 'CANCELLED',
            remarks: remarks,
            Created_at: undefined,
            Updated_at: undefined
        };

        // Insert the cancellation proposal
        const response = await db(proposalTableName).insert(cancellationProposal);
        return response;
        // return 1;
        // Update original proposal status
        // return await db(proposalTableName)
        //     .where('id', fetchedId)
        //     .update({
        //         status: 'CANCELLED',
        //         remarks: remarks,
        //         updated_at: getCurrentTimestamp()
        //     });

    } catch (error) {
        console.error("Error deleting/cancelling proposal:", error);
        throw error;
    }
}

exports.transferBusiness = async (id, agent_id) => {
    try {
        // get agent_id from agents table by id
        const agent = await db('agents')
            .where('id', agent_id)
            .first();
        if (!agent) {
            return {
                success: false,
                message: 'Agent not found'
            };
        }
        return db.transaction(async trx => {
            // Update agent_code in all three tables
            const regularUpdate = await trx('proposals')
                .where('agent_code', id)
                .update({
                    agent_code: agent_id,
                    updated_at: getCurrentTimestamp()
                });

            const paUpdate = await trx('proposals_pa')
                .where('agent_code', id)
                .update({
                    agent_code: agent_id,
                    updated_at: getCurrentTimestamp()
                });

            const rolloverUpdate = await trx('proposals_rollover_migration')
                .where('agent_code', id)
                .update({
                    agent_code: agent_id,
                    updated_at: getCurrentTimestamp()
                });

            const quotationUpdate = await trx('quotations')
                .where('agent_id', id)
                .update({
                    agent_id: agent_id,
                    updated_at: getCurrentTimestamp()
                });
            const paQuotationUpdate = await trx('pa_quotations')
                .where('agent_id', id)
                .update({
                    agent_id: agent_id,
                    updated_at: getCurrentTimestamp()
                });
            const customerUpdate = await trx('customer_personal_info')
                .where('agent_id', id)
                .update({
                    agent_id: agent_id,
                    assigned_to: agent.agent_id,
                    updated_at: getCurrentTimestamp()
                });

            // Check if any update was successful
            if (regularUpdate || paUpdate || rolloverUpdate || quotationUpdate || paQuotationUpdate || customerUpdate) {
                // Get updated data from all tables
                const updatedProposal = await trx('proposals')
                    .where('agent_code', id)
                    .first() ||
                    await trx('proposals_pa')
                        .where('agent_code', id)
                        .first() ||
                    await trx('proposals_rollover_migration')
                        .where('agent_code', id)
                        .first() ||
                    await trx('quotations')
                        .where('agent_id', id)
                        .first() ||
                    await trx('pa_quotations')
                        .where('agent_id', id)
                        .first() ||
                    await trx('customer_personal_info')
                        .where('agent_id', id)
                        .first();

                return {
                    success: true,
                    data: updatedProposal,
                    message: 'Business transferred successfully'
                };
            }

            return {
                success: false,
                message: 'No proposal found with the given ID'
            };
        });
    } catch (error) {
        console.error('Error transferring business:', error);
        throw error;
    }
};