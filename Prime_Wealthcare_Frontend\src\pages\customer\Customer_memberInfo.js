import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { TextField, FormControl, MenuItem, Checkbox, ListItemText, FormControlLabel, RadioGroup, Radio } from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import CustomSection from '../../components/CustomSection';
import { toast } from 'react-toastify';
import CustomTextField from '../../components/CustomTextField';
import Dropdown from '../../components/table/DropDown';
import ModuleName from '../../components/table/ModuleName';
import { FormHelperText } from '@mui/material';
import { Autocomplete } from '@mui/material';
import {

    createCustomerMemberInfo,
    getAllPickLists,
    getCustomerById,
    getMemberById,
    saveCustomerData,
    saveMemberData,
    updateCustomer,
    updateMember,
    deleteMember,
    getMemberByCustomerId,
    getAllCustomer,
    deleteDisease
} from '../../redux/actions/action';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import SaveIcon from '@mui/icons-material/Save';
import { Today } from '@mui/icons-material';
import CustomFileUpload from '../../components/CustomFileUpload';

const Customer_memberInfo = () => {

    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const { id } = useParams();
    const [errors, setErrors] = useState({}); // State for error messages
    const minAgeDate = dayjs().subtract(18, 'years'); // Calculate date 18 years ago
    const today = dayjs(); // Get today's date
    const [isEditing, setIsEditing] = useState(false); // State to manage edit mode

    const isDisabled = location.state?.isDisabled || false;
    const [editingMemberIds, setEditingMemberIds] = useState(new Set());
    const [formData, setFormData] = useState({
        full_name: '',
        relation: '',
        gender: '',
        date_of_birth: null,
        marital_status: 1,
        marriage_date: null,

        members: [
            //     {
            //    // id: 0,
            //     member_full_name: '',
            //     member_relation: '',
            //     member_gender: '',
            //     member_date_of_birth: null,
            //     member_adhar_number: '',
            //     member_PAN_number: '',
            // }
        ]
    });
    const handleEditToggle = () => {
        setIsEditing(prev => !prev); // Toggle edit mode
    };
    const GenderData = useSelector((state) => state.pickListReducer.genderOptions);
    const maritalStatusData = useSelector((state) => state.pickListReducer.maritalStatusOptions);
    const relationData = useSelector((state) => state.pickListReducer.relationOptions);
    const memberData = useSelector((state) => state.customerReducer.customer);
    const dataByCustomerId = useSelector((state) => state.customerMemberReducer.customerMember);
    const [customerData, setCustomerData] = useState(null); // State for customer data
    const EducationData = useSelector((state) => state.pickListReducer.OccupationOptions);

    const educationOptions = EducationData.map((education) => ({
        value: education.id,
        label: education.label_name
    }))

    const genderOptions = GenderData.map((gender) => ({
        value: gender.id,
        label: gender.label_name

    }));
    const maritalOptions = maritalStatusData.map((maritalStatus) => ({
        value: maritalStatus.id,
        label: maritalStatus.label_name
    }))

    const getMemberRelationOptions = (maritalStatus) => {
        if (!relationData) return [];

        return relationData
            .map((relation) => ({
                value: relation.id,
                label: relation.label_name
            }))
            .filter(option => {
                const label = option.label.toUpperCase();

                // If marital status is not 5 (assuming 5 is married)
                if (maritalStatus !== 5) {
                    // Filter out SPOUSE, SON, and DAUGHTER options
                    return !['SPOUSE', 'SON', 'DAUGHTER', 'HUSBAND', 'CHILD', 'WIFE', 'SELF'].includes(label);
                }

                // If married, filter out only SELF
                return label !== 'SELF';
            });
    };

    const member_relationOptions = getMemberRelationOptions(formData.marital_status);

    const relationOptions = relationData.map((relation) => ({
        value: relation.id,
        label: relation.label_name,

    }))
    //.filter(option => option.label == 'SELF');
    const defaultRelation = relationOptions.find(rel => rel.label === 'SELF')?.value || '';
    useEffect(() => {
        const fetchData = async () => {
            try {
                await dispatch(getAllPickLists());
                const customerAction = await dispatch(getCustomerById(id)); // Fetch customer data
                const customer = customerAction.payload;
                setCustomerData(customer);

                //const defaultRelation = relationData.find(rel => rel.label_name === 'SELF')?.id || '';
                // Initialize formData with customer data
                setFormData(prevData => ({
                    ...prevData,
                    full_name: `${customer.first_name} ${customer.last_name}` || '',
                    relation: customer.relation_id || defaultRelation, // Set to customer relation or default to 'SELF'
                    gender: customer.gender_id || '',
                    date_of_birth: customer.date_of_birth ? dayjs(customer.date_of_birth) : null,
                    marital_status: customer.marital_status_id || '',
                    marriage_date: customer.marriage_date ? dayjs(customer.marriage_date) : null,

                }));



                // Fetch members for the customer
                const action = await dispatch(getMemberByCustomerId(id));
                const members = action.payload;

                if (Array.isArray(members) && members.length > 0) {
                    const activeMembers = members.filter(member => member.status === 1);
                    setFormData(prevData => ({
                        ...prevData,
                        members: activeMembers.map(member => ({
                            id: member.id, // Keep original ID to distinguish existing members
                            member_full_name: member.full_name || '',
                            member_relation: member.relation_id || '',
                            member_gender: member.gender_id || '',
                            member_date_of_birth: dayjs(member.date_of_birth) || null,
                            original_pan: member.pan_number, // Store original PAN
                            original_aadhar: member.aadhar_number, // Store original Aadhaar
                            member_adhar_number: member.aadhar_number || '',
                            member_PAN_number: member.pan_number || '',
                            member_mobile: member.mobile || '',
                            member_email: member.email || '',
                            marital_status_id: member.marital_status_id || '',
                            member_marriage_date: dayjs(member.marriage_date) || null,
                            member_occupation: member.member_occupation || '',
                            member_weight: member.member_weight || '',
                            member_height: member.member_height || '',
                            isSmoking: member.isSmoking || '',
                            isTobacco: member.isTobacco || '',
                            preExistingDisease: member.preExistingDisease || '',
                            smokingPerDay: member.smokingPerDay || '',
                            tobaccoPerDay: member.tobaccoPerDay || '',
                            diseaseDetails: member.diseaseDetails || '',

                        }))
                    }));
                } else {
                    console.error('No members found or invalid structure:', members);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, [dispatch, id]);

    useEffect(() => {
        // When marital status changes, filter out any invalid relations
        if (formData.marital_status !== 5) {
            setFormData(prevData => ({
                ...prevData,
                members: prevData.members.filter(member => {
                    const relation = relationData.find(r => r.id === member.member_relation);
                    return relation && !['SPOUSE', 'SON', 'DAUGHTER'].includes(relation.label_name.toUpperCase());
                })
            }));
        }
    }, [formData.marital_status]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;

        // Clear the error for the specific field
        setErrors(prevErrors => ({
            ...prevErrors,
            [name]: undefined
        }));
        // Transform Aadhaar and PAN fields
        let transformedValue = value;

        if (name === 'marital_status') {
            // If changing to single (not 5), remove spouse/children relations
            if (value !== 5) {
                setFormData(prevData => ({
                    ...prevData,
                    [name]: value,
                    members: prevData.members.filter(member => {
                        const relation = relationData.find(r => r.id === member.member_relation);
                        return relation && !['SPOUSE', 'SON', 'DAUGHTER'].includes(relation.label_name.toUpperCase());
                    })
                }));
            } else {
                setFormData(prevData => ({
                    ...prevData,
                    [name]: value
                }));
            }
        } else {
            setFormData(prevData => ({
                ...prevData,
                [name]: transformedValue
            }));
        }
    };
    const handleDateChange = (name, value) => {

        // Clear the error for the specific date field
        setErrors(prevErrors => ({
            ...prevErrors,
            [name]: undefined
        }));

        setFormData(prevData => ({
            ...prevData,
            [name]: value
        }));
    };
    const handleCheckboxChange = (e) => {
        const { name, checked } = e.target;
        setFormData(prevData => ({
            ...prevData,
            [name]: checked
        }));
    };
    const validateData = (data) => {
        const errors = {};

        // Validate customer data
        if (!data.full_name) {
            errors.full_name = "Full Name is required.";
        }
        // if (!data.relation) {
        //     errors.relation = "Relation is required.";
        // }
        if (!data.gender) {
            errors.gender = "Gender is required.";
        }
        if (!data.date_of_birth) {
            errors.date_of_birth = "Date of Birth is required.";
        }
        if (!data.marital_status) {
            errors.marital_status = "Marital Status is required.";
        }
        // if (data.marital_status == 5 && !data.marriage_date) { // Assuming '4' is for single
        //     errors.marriage_date = "Marriage Date is required if not single.";
        // }





        return errors;
    };
    const formatAadharNumber = (input) => {
        // Remove non-numeric characters
        const numericValue = input.replace(/[^0-9]/g, "");
        // Limit to 12 digits
        const limitedValue = numericValue.slice(0, 12);
        // Add hyphens after every 4 digits
        return limitedValue.replace(/(\d{4})(?=\d)/g, "$1-");
    };
    const validateMember = (data) => {
        const errors = {};
        data.members.forEach((member, index) => {
            // if (member.marital_status_id === 5 && !member.member_marriage_date) {
            //     errors[`member_marriage_date_${index}`] = "Marriage Date is required if not single.";
            // }
            if (!member.member_full_name) {
                errors[`member_full_name_${index}`] = `Full Name is required.`;
            }
            if (!member.member_relation) {
                errors[`member_relation_${index}`] = `Relation is required.`;
            }
            if (!member.member_gender) {
                errors[`member_gender_${index}`] = `Gender is required.`;
            }
            if (!member.member_height) {
                errors[`member_height_${index}`] = `Height is required.`;
            }
            if (!member.member_weight) {
                errors[`member_weight_${index}`] = `Weight is required.`;
            }
            if (!member.member_occupation) {
                errors[`member_occupation_${index}`] = `Education is required.`;
            }
            // if (!member_occupation) {
            //     errors[`member_occupation_${index}`] = `Education is required.`;
            // }
            if (!member.member_date_of_birth) {
                errors[`member_date_of_birth_${index}`] = `Date of Birth is required.`;
            }
            // Aadhaar Validation (Optional)
            if (member.member_adhar_number && !/^\d{4}-\d{4}-\d{4}$/.test(member.member_adhar_number)) {
                errors[`member_adhar_number_${index}`] = `Invalid Aadhaar Number. It should be a 12-digit numeric value.`;
            }

            if (member.member_PAN_number && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(member.member_PAN_number)) {
                errors[`member_PAN_number_${index}`] = `Invalid PAN Number. Format should be 5 letters, 4 digits, 1 letter (e.g., **********).`;
            }
            // Email Validation (Optional)
            if (member.member_email && !/^[a-z0-9._]+@[a-z0-9-]+(\.[a-z]{2,})+$/.test(member.member_email)) {
                errors[`member_email_${index}`] = `Invalid Email Address.`;
            }

            // Mobile Validation (Optional)
            if (member.member_mobile) {
                if (!/^[789]\d{9}$/.test(member.member_mobile)) {
                    errors[`member_mobile_${index}`] = `Invalid Mobile Number. It should start with 7, 8, or 9 and be 10 digits long.`;
                }
            }

            // Add validation for smoking
            if (!member.isSmoking) {
                errors[`isSmoking_${index}`] = "Please select smoking status.";
            }

            // Add validation for tobacco
            if (!member.isTobacco) {
                errors[`isTobacco_${index}`] = "Please select tobacco status.";
            }

            // Add validation for preExistingDisease
            if (!member.preExistingDisease) {
                errors[`preExistingDisease_${index}`] = "Please select pre-existing disease status.";
            }
            // Add validation for smoking details
            if (member.isSmoking === 'Y' && !member.smokingPerDay) {
                errors[`smokingPerDay_${index}`] = 'Please specify smoking consumption per day';
            }
            if (member.isTobacco === 'Y' && !member.tobaccoPerDay) {
                errors[`tobaccoPerDay_${index}`] = 'Please specify tobacco consumption per day';
            }
            if (member.preExistingDisease === 'Y' && !member.diseaseDetails) {
                errors[`diseaseDetails_${index}`] = 'Please specify disease details';
            }
        });
        return errors;
    }
    const handleSubmit = async (e) => {
        e.preventDefault();

        // New validation to check for duplicate Aadhaar and PAN numbers
        const aadhaarSet = new Set();
        const panSet = new Set();
        const duplicateErrors = {};

        formData.members.forEach((member, index) => {
            if (member.member_adhar_number) {
                if (aadhaarSet.has(member.member_adhar_number)) {
                    duplicateErrors[`member_adhar_number_${index}`] = `Duplicate Aadhaar Number found.`;
                } else {
                    aadhaarSet.add(member.member_adhar_number);
                }
            }
            if (member.member_PAN_number) {
                if (panSet.has(member.member_PAN_number)) {
                    duplicateErrors[`member_PAN_number_${index}`] = `Duplicate PAN Number found.`;
                } else {
                    panSet.add(member.member_PAN_number);
                }
            }
        });


        // Combine validation errors
        const memberValidationErrors = validateMember(formData);
        const validationErrors = validateData(formData);
        const combinedErrors = { ...validationErrors, ...duplicateErrors, ...memberValidationErrors };
        setErrors(combinedErrors); // Set errors in state

        if (Object.keys(combinedErrors).length > 0) {
            return; // Stop submission if there are errors
        }

        const customerData = {
            relation_id: defaultRelation,
            gender_id: formData.gender,
            date_of_birth: formData.date_of_birth ? dayjs(formData.date_of_birth).format('YYYY-MM-DD') : null,
            marital_status_id: formData.marital_status,
            marriage_date: formData.marriage_date ? dayjs(formData.marriage_date).format('YYYY-MM-DD') : null,

        };

        dispatch(updateCustomer({ id, data: customerData }))
            .then(() => {
                const memberPromises = formData.members.map((member) => {
                    // Prepare member data for creation or update
                    const memberData = {
                        full_name: member.member_full_name,
                        relation_id: member.member_relation,
                        gender_id: member.member_gender,
                        date_of_birth: member.member_date_of_birth ? dayjs(member.member_date_of_birth).format('YYYY-MM-DD') : null,
                        aadhar_number: member.member_adhar_number,
                        pan_number: member.member_PAN_number,
                        mobile: member.member_mobile,
                        email: member.member_email,
                        marital_status_id: member.marital_status_id,
                        marriage_date: (member.marital_status_id === 5 && member.member_marriage_date &&
                            dayjs(member.member_marriage_date).isValid()) ?
                            dayjs(member.member_marriage_date).format('YYYY-MM-DD') : null,
                        customer_id: id,  // Ensure customer_id is added for new members
                        member_occupation: member.member_occupation,
                        member_height: member.member_height,
                        member_weight: member.member_weight,
                        isSmoking: member.isSmoking,
                        isTobacco: member.isTobacco,
                        preExistingDisease: member.preExistingDisease,
                        smokingPerDay: member.smokingPerDay,
                        tobaccoPerDay: member.tobaccoPerDay,
                        diseaseDetails: member.diseaseDetails,

                    };

                    if (!member.id) {
                        // If `member.id` is missing, treat it as a new member and create it
                        return dispatch(createCustomerMemberInfo(memberData))
                            .catch(error => {
                                console.error(`Error creating member ${member.member_full_name}:`, error);
                                toast.error(`Failed to create member: ${member.member_full_name}`);
                            });
                    } else {
                        // If `member.id` exists, update the member
                        return dispatch(updateMember({ id: member.id, data: memberData }))
                            .catch(error => {
                                console.error(`Error updating member ${member.member_full_name}:`, error);
                                toast.error(`Failed to update member: ${member.member_full_name}`);
                            });
                    }
                });

                return Promise.all(memberPromises);
            })
            .then(() => {
                toast.success('Customer data updated successfully!'); // Show success message

                navigate(`/dashboard/customer-address/${id}`);
            })
            .catch(error => {
                console.error('Error saving data:', error);
                toast.error('Failed to save data.');
            });
    };



    const handleAddMember = () => {
        const newMember = {
            // id: newMemberId, // Uncomment if you want to assign an ID
            member_full_name: '',
            member_relation: '',
            member_gender: '',
            member_date_of_birth: null,
            member_adhar_number: '',
            member_PAN_number: '',
            member_mobile: '',
            member_email: '',
            marital_status_id: '',
            member_marriage_date: null,
            member_occupation: '',
            member_weight: '',
            member_height: '',
            isSmoking: '',
            isTobacco: '',
            preExistingDisease: '',
            smokingPerDay: '',
            tobaccoPerDay: '',
            diseaseDetails: '',
        };

        setFormData(prevData => ({
            ...prevData,
            members: [newMember, ...prevData.members],
        }));
    };



    const handleEditMember = (memberId) => {
        setEditingMemberIds((prevIds) => {
            const updatedIds = new Set(prevIds);
            if (updatedIds.has(memberId)) {
                updatedIds.delete(memberId);
            } else {
                updatedIds.add(memberId);
            }
            return updatedIds;
        });
    };


    const handleDeleteMember = (memberId) => {
        // const confirmDelete = window.confirm("Are you sure you want to delete this member?");
        if (!window.confirm("Are you sure you want to delete this member?")) {
            return;
        }

        if (memberId) {
            // If member has an ID, delete from backend
            dispatch(deleteMember(memberId))
                .then(() => {
                    setFormData(prevData => ({
                        ...prevData,
                        members: prevData.members.filter(member => member.id !== memberId)
                    }));
                    toast.success('Member deleted successfully!');
                })
                .catch(error => {
                    console.error('Error deleting member:', error);
                    toast.error('Failed to delete member.');
                });
        } else {
            // If member has no ID (new empty section), just remove from state
            setFormData(prevData => ({
                ...prevData,
                members: prevData.members.filter((_, index) =>
                    // Find the specific index of the member we want to delete
                    index !== prevData.members.findIndex(m =>
                        !m.id &&
                        !m.member_full_name &&
                        !m.member_relation &&
                        !m.member_gender &&
                        !m.member_date_of_birth
                    )
                )
            }));
        }

    };

    const handleMemberInputChange = (index, e, autocompleteValue = null) => {
        let name, value;

        // Determine the source of the input (either from an input field or Autocomplete)
        if (e && e.target) {
            // Handling normal input fields
            name = e.target.name;
            value = e.target.value;
        } else {
            // Handling Autocomplete fields
            name = "member_occupation"; // Specific field name for education
            value = autocompleteValue?.value || ""; // Use selected value or empty string
        }

        // Add validation for smoking and tobacco consumption to allow only alphanumeric characters
        if (name === 'smokingPerDay' || name === 'tobaccoPerDay') {
            // Allow only letters and numbers, remove any other characters
            const alphanumericValue = value.replace(/[^a-zA-Z0-9\s]/g, '');
            if (alphanumericValue !== value) {
                return; // Prevent input of symbols
            }
        }
        // Add validation for diseaseDetails
        if (name === 'diseaseDetails') {
            // Allow letters, numbers, spaces, parentheses, hyphens, dots, and common separators
            const validValue = value.replace(/[^a-zA-Z0-9\s,.\-()]/g, '');

            // Define a regular expression for valid disease detail formats
            const isValidInput = /^(\d+\s*-\s*[a-zA-Z0-9\s,.\-()]+|[a-zA-Z0-9\s,.\-()]+)$/;

            if (value && !isValidInput.test(validValue)) {
                setErrors(prevErrors => ({
                    ...prevErrors,
                    [`diseaseDetails_${index}`]: 'Please enter a valid medical condition format like "1-Sugar" or "Diabetes Type-2"'
                }));
                return;
            }

            value = validValue;
        }
        // Clear the error for the specific field
        setErrors(prevErrors => ({
            ...prevErrors,
            [`${name}_${index}`]: undefined, // Clear the error for the specific field
        }));

        // Calculate age if we have date of birth
        const member = formData.members[index];
        const dob = member.member_date_of_birth;
        let age;
        if (dob) {
            age = dayjs().diff(dayjs(dob), 'year');
        }

        // If setting occupation and age is 19 or below, force "Student" occupation
        if (name === "member_occupation" && age !== undefined && age <= 19) {
            // Find the "Student" option value from educationOptions
            const studentOption = educationOptions.find(option => option.label.toLowerCase() === "student");
            if (studentOption) {
                value = studentOption.value;
            }
        }
        // Transform input based on field type
        let transformedValue = value;

        switch (name) {
            case "member_mobile":
                // Ensure the mobile number is numeric and limited to 10 digits
                transformedValue = value.replace(/[^0-9]/g, "").slice(0, 10);
                break;
            case "member_email":
                // Convert email to lowercase
                transformedValue = value.toLowerCase();
                break;
            case "member_PAN_number":
                // Format PAN to uppercase and limit to 10 characters
                transformedValue = value.toUpperCase().replace(/[^A-Z0-9]/g, "").slice(0, 10);
                break;
            case "member_full_name":
                // Convert full name to uppercase
                transformedValue = value.toUpperCase();
                break;
            case "member_adhar_number":
                // Format Aadhaar number using the custom formatting function
                transformedValue = formatAadharNumber(value);
                break;
            case "member_height":
                // Only allow whole numbers for height
                transformedValue = value.replace(/[^0-9]/g, "");
                break;
            case "member_weight":
                // Only allow whole numbers for weight
                transformedValue = value.replace(/[^0-9]/g, "");
                break;
            default:
                // If no transformation is needed, use the original value
                transformedValue = value;
                break;
        }

        // Update form data for the specific member
        setFormData(prevData => ({
            ...prevData,
            members: prevData.members.map((member, i) =>
                i === index ? {
                    ...member,
                    [name]: transformedValue, // Update the specific member field
                } : member
            )
        }));
    };





    const handleMemberDateChange = (index, name, value) => {
        // Clear the error for the specific date field
        setErrors(prevErrors => ({
            ...prevErrors,
            [`${name}_${index}`]: undefined
        }));

        // If the date is valid, update the form data
        setFormData(prevData => ({
            ...prevData,
            members: prevData.members.map((member, i) =>
                i === index ? { ...member, [name]: value } : member
            )
        }));
    };






    const handleCancel = () => {
        navigate('/dashboard/customer-Master'); // Navigate to the desired route
    };

    return (
        <Box sx={{
            paddingLeft: { xs: '20px', md: '40px' },
            paddingRight: { xs: '20px', md: '40px' },
            paddingBottom: '40px'
        }}>
            <form encType="multipart/form-data" >
                <Grid container spacing={2} style={{ display: 'flex', alignItems: 'center' }}>
                    {/* Header Row */}
                    <Grid item xs={12} md={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Customer" pageName={"Create"} />
                        </Box>
                    </Grid>


                    <Grid item xs={12} md={4} sx={{
                        display: 'flex',
                        justifyContent: { xs: 'center', md: 'flex-end' },
                        flexWrap: { xs: 'wrap', md: 'nowrap' },
                        gap: { xs: 1, md: 0 }
                    }}>
                        {!id && (
                            <Button variant="outlined" size="small"
                                sx={{
                                    minWidth: { xs: '20%', md: '100px' },
                                    mx: { xs: 0.5, md: 0.5 },
                                    color: 'green',
                                    borderColor: 'green',
                                    mt: 3,
                                    textTransform: 'none'
                                }}>
                                Save & New
                            </Button>
                        )}
                        <Button variant="outlined" size="small" sx={{
                            minWidth: { xs: '20%', md: '100px' },
                            mx: { xs: 0.5, md: 0.5 },
                            color: 'green',
                            borderColor: 'green',
                            mt: 3,
                            textTransform: 'none'
                        }}
                            onClick={handleSubmit}
                        >
                            Save & Next
                        </Button>
                        <Button variant="outlined" size="small" sx={{
                            minWidth: { xs: '20%', md: '100px' },
                            mx: { xs: 0.5, md: 0.5 },
                            color: 'red',
                            borderColor: 'red',
                            mt: 3,
                            textTransform: 'none'
                        }}
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                    </Grid>
                    <Grid container>
                        {customerData && (
                            <CustomSection
                                titles={['Overview', 'Personal Details', 'Member Information', 'Address', 'Grouping']}
                                page='customer'
                                customerType={customerData?.customer_category}
                            />
                        )}
                    </Grid>

                    {/* Form Fields */}
                    {/* Row 1 */}
                    <Grid item xs={12} >
                        <Box
                            sx={{
                                //backgroundColor: '#f0f0f0',
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',
                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Personal Details</h5>
                            {/* {{ edit_1 }} // Add the pencil icon here */}
                            <IconButton sx={{ marginLeft: '8px', color: 'green' }} onClick={handleEditToggle}>
                                <EditIcon />
                            </IconButton>
                            <div style={{ height: '100vh', display: 'flex', alignItems: 'center' }}>

                            </div>
                        </Box>
                        <hr></hr></Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="Full Name"
                            name="full_name"
                            value={formData.full_name} // Use formData for editing
                            onChange={handleInputChange}
                            error={Boolean(errors.full_name)} // Set error state
                            helperText={errors.full_name} // Display helper text
                            disabled
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>

                        <Dropdown
                            label="Relation (Self)"
                            name="relation"
                            value={defaultRelation}  // Use formData for editing
                            onChange={handleInputChange}
                            options={relationOptions}
                            // error={Boolean(errors.relation)} // Set error state
                            // helperText={errors.relation} // Display helper text
                            fullWidth
                            required
                            disabled
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>

                        <Dropdown
                            label="Select Gender"
                            name="gender"
                            value={formData.gender} // Use formData for editing
                            onChange={handleInputChange}
                            options={genderOptions}
                            error={Boolean(errors.gender)} // Set error state
                            helperText={errors.gender} // Display helper text
                            fullWidth
                            disabled={!isEditing} // Disable based on edit mode

                            required
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <FormControl fullWidth required
                        // error={Boolean(error.date_of_birth)}
                        >
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    required
                                    maxDate={minAgeDate}
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            //  required: true,
                                            error: Boolean(errors.date_of_birth),
                                            helperText: errors.date_of_birth,
                                            sx: {
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }
                                        }
                                    }}
                                    label="Date of Birth"
                                    name="date_of_birth"
                                    //  disabled={!isEditing} // Disable based on edit mode
                                    disabled={!isEditing || id && customerData?.date_of_birth ? true : false}
                                    // disableMaskedInput={false}
                                    error={Boolean(errors.date_of_birth)} // Set error state
                                    helperText={errors.date_of_birth} // Display helper text
                                    value={formData.date_of_birth} // Use formData for editing
                                    onChange={(value) => handleDateChange('date_of_birth', value)}
                                    renderInput={(params) => (
                                        <TextField
                                            //   {...params}
                                            fullWidth
                                            // error={Boolean(error.date_of_birth)}
                                            //  helperText={error.date_of_birth}
                                            sx={{
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px', // Width of the red line
                                                        backgroundColor: 'red', // Color of the line
                                                        zIndex: 1,
                                                    }
                                                },
                                            }}
                                        />
                                    )}
                                    format="DD/MM/YYYY"
                                    disableMaskedInput={false} // Enables input masking
                                />
                            </LocalizationProvider>
                            {/* {error.date_of_birth && (
                    <FormHelperText>{error.date_of_birth}</FormHelperText>
                )} */}
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <Dropdown
                            label="Marital Status "
                            name="marital_status"
                            options={maritalOptions}
                            error={Boolean(errors.marital_status)} // Set error state
                            helperText={errors.marital_status} // Display helper text
                            value={formData.marital_status} // Use formData for editing
                            onChange={handleInputChange}
                            fullWidth
                            disabled={!isEditing} // Disable based on edit mode

                            required
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    {formData.marital_status === 5 && (
                        <Grid item xs={12} sm={6} md={3}

                        >
                            <FormControl fullWidth required
                            // error={Boolean(error.date_of_birth)}
                            >
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker

                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                //  required: true,
                                                error: Boolean(errors.marriage_date),
                                                helperText: errors.marriage_date,
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            //  backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                        label="Marriage Date"
                                        name="marriage_date"
                                        error={Boolean(errors.marriage_date)} // Set error state
                                        helperText={errors.marriage_date} // Display helper text
                                        maxDate={dayjs()}
                                        // value={member.member_date_of_birth ?dayjs(member.member_date_of_birth) : null}
                                        //onChange={(date) => handleMemberDateChange( 'member_date_of_birth', date)}
                                        value={formData.marriage_date ? dayjs(formData.marriage_date) : null} // Use formData for editing
                                        onChange={(date) => handleDateChange('marriage_date', date)}
                                        disabled={!isEditing || formData.marital_status !== 5} // Disable based on edit mode and marital status
                                        renderInput={(params) => (
                                            <TextField
                                                fullWidth
                                                error={Boolean(errors.marriage_date)} // Set error state
                                                helperText={errors.marriage_date} // Display helper text
                                                sx={{
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px', // Width of the red line
                                                            //   backgroundColor: 'red', // Color of the line
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }}
                                            />
                                        )}
                                        format="DD/MM/YYYY"
                                        disableMaskedInput={false} // Enables input masking
                                    />
                                </LocalizationProvider>
                                {/* {error.date_of_birth && (
                    <FormHelperText>{error.date_of_birth}</FormHelperText>
                )} */}
                            </FormControl>
                        </Grid>
                    )}



                    <Grid item xs={12} >
                        <Box
                            sx={{
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',
                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Member Details</h5>
                            <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                                <IconButton

                                    onClick={handleAddMember}
                                    sx={{
                                        color: 'green',
                                        '&:hover': { color: 'darkgreen' }
                                    }}
                                >
                                    <AddCircleOutlineIcon />
                                </IconButton>
                            </Box>
                        </Box>
                        <hr></hr>
                    </Grid>

                    {/* Render member details sections dynamically only if members exist */}
                    {

                        formData.members.length > 0 ? (

                            formData.members.map((member, index) => (
                                <React.Fragment key={member.id}>
                                    <Grid item xs={12}>
                                        <Box sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            padding: '10px',
                                            borderRadius: '4px',
                                            height: '40px',
                                            borderBottom: '1px solid #e0e0e0'
                                        }}>
                                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                                Member {formData.members.length - index}
                                            </Typography>
                                            <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                                                {/* <IconButton 
                                                    onClick={() => editingMemberIds.has(member.id) || !member.id ? handleSaveMember(member.id) : handleEditMember(member.id)}
                                                    sx={{ color: 'green', '&:hover': { color: 'darkgreen' }}}
                                                >
                                                    {editingMemberIds.has(member.id) || !member.id  ? <SaveIcon /> : <EditIcon />}
                                                </IconButton> */}
                                                <IconButton
                                                    onClick={() => handleEditMember(member.id)} // Only handle edit
                                                    sx={{ color: 'green', '&:hover': { color: 'darkgreen' } }}
                                                >
                                                    <EditIcon /> {/* Only show the edit icon */}
                                                </IconButton>
                                                <IconButton
                                                    onClick={() => handleDeleteMember(member.id)}
                                                    sx={{ color: 'red', '&:hover': { color: 'darkred' } }}
                                                // disabled={formData.members.length === 1}
                                                >
                                                    <DeleteIcon />
                                                </IconButton>
                                            </Box>
                                        </Box>
                                    </Grid>

                                    {/* Member Detail Fields */}
                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField
                                            label="Full Name"
                                            name="member_full_name"
                                            value={member.member_full_name || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            disabled={!editingMemberIds.has(member.id)}
                                            // disabled={!editingMemberIds.has(member.id) && member.id} // Enable if editing or new member
                                            error={Boolean(errors[`member_full_name_${index}`])} // Set error state
                                            helperText={errors[`member_full_name_${index}`]} // Display helper text
                                            isRequired
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <Dropdown
                                            label="Select Relation"
                                            name="member_relation"
                                            value={member.member_relation || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            options={member_relationOptions}
                                            fullWidth
                                            required
                                            disabled={!editingMemberIds.has(member.id)}
                                            // disabled={!editingMemberIds.has(member.id) && member.id} // Enable if editing or new member
                                            error={Boolean(errors[`member_relation_${index}`])} // Set error state
                                            helperText={errors[`member_relation_${index}`]} // Display helper text
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <Dropdown
                                            label="Select Gender"
                                            name="member_gender"
                                            value={member.member_gender || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            options={genderOptions}
                                            fullWidth
                                            required
                                            disabled={!editingMemberIds.has(member.id)}
                                            // disabled={!editingMemberIds.has(member.id) && member.id} // Enable if editing or new member
                                            error={Boolean(errors[`member_gender_${index}`])} // Set error state
                                            helperText={errors[`member_gender_${index}`]} // Display helper text
                                        />
                                    </Grid>

                                    <Grid item xs={12} sm={6} md={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    slotProps={{
                                                        textField: {
                                                            fullWidth: true,
                                                            error: Boolean(errors[`member_date_of_birth_${index}`]), // Set error state
                                                            helperText: errors[`member_date_of_birth_${index}`], // Display helper text
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                },
                                                            },
                                                        },
                                                    }}
                                                    // error={Boolean(errors[`member_date_of_birth_${index}`])} // Set error state
                                                    // helperText={errors[`member_date_of_birth_${index}`]} // Display helper text
                                                    // disabled={!editingMemberIds.has(member.id)} // Disable if not editing
                                                    disabled={!editingMemberIds.has(member.id) || member.id ? true : false}
                                                    label="Date of Birth"
                                                    value={member.member_date_of_birth ? dayjs(member.member_date_of_birth) : null}
                                                    onChange={(date) => handleMemberDateChange(index, 'member_date_of_birth', date)} // Handle date change
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            fullWidth
                                                            // error={Boolean(errors[`member_date_of_birth_${index}`])} // Set error state
                                                            // helperText={errors[`member_date_of_birth_${index}`]} // Display helper text
                                                            disabled={!editingMemberIds.has(member.id)} // Disable if not editing
                                                        />
                                                    )}
                                                    format="DD/MM/YYYY"
                                                    // Set minDate and maxDate for Son/Daughter
                                                    minDate={formData.members[index].member_relation === 48 || formData.members[index].member_relation === 49
                                                        ? dayjs().subtract(25, 'years').startOf('day') // Minimum date: 25 years ago for Son/Daughter
                                                        : undefined} // No minDate for other relations
                                                    maxDate={formData.members[index].member_relation === 48 || formData.members[index].member_relation === 49
                                                        ? dayjs().endOf('day') // Maximum date: today for Son/Daughter
                                                        : dayjs().endOf('day')} // No future dates for other relations (set maxDate to today)
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>

                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField
                                            label='Mobile Number'
                                            name='member_mobile'
                                            value={member.member_mobile || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            disabled={!editingMemberIds.has(member.id)}
                                            error={Boolean(errors[`member_mobile_${index}`])} // Set error state
                                            helperText={errors[`member_mobile_${index}`]} // Display helper text
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField
                                            label='Email'
                                            name='member_email'
                                            value={member.member_email || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            disabled={!editingMemberIds.has(member.id)}
                                            error={Boolean(errors[`member_email_${index}`])} // Set error state
                                            helperText={errors[`member_email_${index}`]} // Display helper text
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <Dropdown
                                            label='Marital Status'
                                            name='marital_status_id'
                                            value={member.marital_status_id || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            disabled={!editingMemberIds.has(member.id)}
                                            options={maritalOptions}
                                            required
                                            fullWidth
                                        />
                                    </Grid>
                                    {member.marital_status_id === 5 && (
                                        <Grid item xs={12} sm={6} md={3}>
                                            <FormControl fullWidth required>
                                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                    <DatePicker
                                                        slotProps={{
                                                            textField: {
                                                                fullWidth: true,
                                                                error: Boolean(errors[`member_marriage_date_${index}`]), // Set error state
                                                                helperText: errors[`member_marriage_date_${index}`], // Display helper text
                                                                sx: {
                                                                    '& .MuiOutlinedInput-root': {
                                                                        '&::before': {
                                                                            content: '""',
                                                                            position: 'absolute',
                                                                            left: 0,
                                                                            top: 0,
                                                                            bottom: 0,
                                                                            width: '3px',
                                                                            // backgroundColor: 'red',
                                                                            zIndex: 1,
                                                                        }
                                                                    },
                                                                }
                                                            }
                                                        }}
                                                        label="Marriage Date"
                                                        name="member_marriage_date"
                                                        error={Boolean(errors[`member_marriage_date_${index}`])} // Set error state
                                                        helperText={errors[`member_marriage_date_${index}`]} // Display helper text
                                                        maxDate={dayjs()}
                                                        value={member.member_marriage_date ? dayjs(member.member_marriage_date) : null} // Use formData for editing
                                                        onChange={(date) => handleMemberDateChange(index, 'member_marriage_date', date)}
                                                        disabled={!editingMemberIds.has(member.id) || member.marital_status_id !== 5} // Disable based on edit mode and marital status
                                                        renderInput={(params) => (
                                                            <TextField
                                                                fullWidth
                                                                error={Boolean(errors[`member_marriage_date_${index}`])} // Set error state
                                                                helperText={errors[`member_marriage_date_${index}`]} // Display helper text
                                                                sx={{
                                                                    '& .MuiOutlinedInput-root': {
                                                                        '&::before': {
                                                                            content: '""',
                                                                            position: 'absolute',
                                                                            left: 0,
                                                                            top: 0,
                                                                            bottom: 0,
                                                                            width: '3px', // Width of the red line
                                                                            //  backgroundColor: 'red', // Color of the line
                                                                            zIndex: 1,
                                                                        }
                                                                    },
                                                                }}
                                                            />
                                                        )}
                                                        format="DD/MM/YYYY"
                                                    />
                                                </LocalizationProvider>
                                            </FormControl>
                                        </Grid>
                                    )}
                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField
                                            label="Aadhaar Number"
                                            name="member_adhar_number"
                                            value={member.member_adhar_number || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            //disabled={!editingMemberIds.has(member.id)}
                                            disabled={!editingMemberIds.has(member.id) || (member.id && member.original_aadhar)} // Check original value
                                            //disabled={!editingMemberIds.has(member.id) && member.id} // Enable if editing or new member
                                            error={Boolean(errors[`member_adhar_number_${index}`])} // Set error state
                                            helperText={errors[`member_adhar_number_${index}`]} // Display helper text
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField
                                            label="PAN Number"
                                            name="member_PAN_number"
                                            value={member.member_PAN_number || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            // disabled={!editingMemberIds.has(member.id)}
                                            //disabled={!editingMemberIds.has(member.id) && member.id} // Enable if editing or new member
                                            disabled={!editingMemberIds.has(member.id) || (member.id && member.original_pan)} // Check original value
                                            error={Boolean(errors[`member_PAN_number_${index}`])} // Set error state
                                            helperText={errors[`member_PAN_number_${index}`]} // Display helper text
                                        />
                                    </Grid>
                                    {/* add education dropdowh */}
                                    <Grid item xs={12} sm={6} md={3}>
                                        <Autocomplete
                                            options={educationOptions}
                                            getOptionLabel={(option) => typeof option === 'string' ? option : option.label || ''}
                                            onChange={(e, value) => handleMemberInputChange(index, null, value)}
                                            value={educationOptions.find(option => option.value === member.member_occupation) || null}
                                            disableClearable={!editingMemberIds.has(member.id)}
                                            disabled={!editingMemberIds.has(member.id)}

                                            isOptionEqualToValue={(option, value) => option.value === value.value}
                                            renderInput={(params) => (
                                                <CustomTextField
                                                    {...params}
                                                    label="Occupation"
                                                    name="member_occupation"
                                                    error={Boolean(errors[`member_occupation_${index}`])}
                                                    helperText={
                                                        errors[`member_occupation_${index}`] ||
                                                        (dayjs().diff(dayjs(member.member_date_of_birth), 'year') <= 19 ? (
                                                            <span style={{ color: 'green' }}>
                                                                Age is 19 or below - occupation set to Student
                                                            </span>
                                                        ) : "")
                                                    }
                                                    fullWidth
                                                    isRequired
                                                />
                                            )}
                                            // disabled={
                                            //     !editingMemberIds.has(member.id) || 
                                            //     (member.member_date_of_birth && dayjs().diff(dayjs(member.member_date_of_birth), 'year') <= 19)
                                            // }
                                            sx={{
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }}
                                        />
                                    </Grid>




                                    {/* height field */}
                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField

                                            label="Height (in cm)"
                                            name="member_height"
                                            value={member.member_height || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            disabled={!editingMemberIds.has(member.id)}
                                            error={Boolean(errors[`member_height_${index}`])} // Set error state
                                            helperText={errors[`member_height_${index}`]} // Display helper text
                                            sx={{
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                }
                                            }}
                                        />
                                    </Grid>
                                    {/* weight field */}
                                    <Grid item xs={12} sm={6} md={3}>
                                        <CustomTextField
                                            label="Weight (in kg)"
                                            name="member_weight"
                                            value={member.member_weight || ''}
                                            onChange={(e) => handleMemberInputChange(index, e)}
                                            disabled={!editingMemberIds.has(member.id)}
                                            error={Boolean(errors[`member_weight_${index}`])} // Set error state
                                            helperText={errors[`member_weight_${index}`]} // Display helper text
                                            sx={{
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                }
                                            }
                                            }
                                        />


                                    </Grid>
                                    {/*  */}
                                    <Grid item xs={12} container spacing={2} alignItems="center">

                                        <Grid item xs={12} sm={6} md={3}>
                                            <Typography variant="h6" color="textSecondary">
                                                Is Smoking?
                                            </Typography>
                                            <FormControl
                                                component="fieldset"
                                                error={Boolean(errors[`isSmoking_${index}`])}
                                                disabled={!editingMemberIds.has(member.id)}
                                                required
                                            >
                                                <RadioGroup
                                                    row
                                                    name="isSmoking"
                                                    value={member.isSmoking || ''}
                                                    onChange={(e) => handleMemberInputChange(index, e)}
                                                >
                                                    <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                                    <FormControlLabel value="N" control={<Radio />} label="No" />
                                                </RadioGroup>
                                                {errors[`isSmoking_${index}`] && (
                                                    <FormHelperText>{errors[`isSmoking_${index}`]}</FormHelperText>
                                                )}
                                            </FormControl>
                                        </Grid>
                                        {member.isSmoking === "Y" && (
                                            <Grid item xs={12} md={4} sm={3}>
                                                <CustomTextField
                                                    label="How many cigarettes consumed per day?"
                                                    name="smokingPerDay"
                                                    value={member.smokingPerDay || ""}
                                                    onChange={(e) => handleMemberInputChange(index, e)}
                                                    fullWidth
                                                    error={Boolean(errors.smokingPerDay)}
                                                    helperText={errors[`smokingPerDay_${index}`]}
                                                    disabled={!editingMemberIds.has(member.id)}  // Fixed: Use editingMemberIds instead of isEditing
                                                    isRequired
                                                    type="text"
                                                // inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }} // Add numeric input mode

                                                />
                                            </Grid>
                                        )}
                                    </Grid>
                                    {/* is tobacco radio group */}
                                    <Grid item xs={12} container spacing={2} alignItems="center">

                                        <Grid item xs={12} sm={6} md={3}>
                                            <Typography variant="h6" color="textSecondary">
                                                Is Tobacco?
                                            </Typography>
                                            <FormControl
                                                component="fieldset"
                                                error={Boolean(errors[`isTobacco_${index}`])}
                                                disabled={!editingMemberIds.has(member.id)}
                                                required
                                            >
                                                <RadioGroup
                                                    row
                                                    name="isTobacco"
                                                    value={member.isTobacco || ''}
                                                    onChange={(e) => handleMemberInputChange(index, e)}
                                                >
                                                    <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                                    <FormControlLabel value="N" control={<Radio />} label="No" />
                                                </RadioGroup>
                                                {errors[`isTobacco_${index}`] && (
                                                    <FormHelperText>{errors[`isTobacco_${index}`]}</FormHelperText>
                                                )}
                                            </FormControl>
                                        </Grid>
                                        {member.isTobacco === "Y" && (
                                            <Grid item xs={12} sm={3} md={4}>
                                                <CustomTextField
                                                    label="How much tobacco consumed per day?"
                                                    name="tobaccoPerDay"
                                                    value={member.tobaccoPerDay || ""}
                                                    onChange={(e) => handleMemberInputChange(index, e)}  // Add index parameter here
                                                    fullWidth
                                                    error={Boolean(errors.tobaccoPerDay)}
                                                    helperText={errors[`tobaccoPerDay_${index}`]}
                                                    disabled={!editingMemberIds.has(member.id)}  // Fixed: Use editingMemberIds instead of isEditing
                                                    isRequired
                                                    type="text"
                                                //  inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }} // Add numeric input mode

                                                />
                                            </Grid>
                                        )}
                                    </Grid>
                                    <Grid item xs={12} container spacing={2} alignItems="center">

                                        <Grid item xs={12} sm={6} md={3}>
                                            <Typography variant="h6" color="textSecondary">
                                                preExistingDisease?
                                            </Typography>
                                            <FormControl
                                                component="fieldset"
                                                error={Boolean(errors[`preExistingDisease_${index}`])}
                                                disabled={!editingMemberIds.has(member.id)}
                                                required
                                            >
                                                <RadioGroup
                                                    row
                                                    name="preExistingDisease"
                                                    value={member.preExistingDisease || ''}
                                                    onChange={(e) => handleMemberInputChange(index, e)}  // Add index parameter here
                                                >
                                                    <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                                    <FormControlLabel value="N" control={<Radio />} label="No" />
                                                </RadioGroup>
                                                {errors[`preExistingDisease_${index}`] && (
                                                    <FormHelperText>{errors[`preExistingDisease_${index}`]}</FormHelperText>
                                                )}
                                            </FormControl>
                                        </Grid>
                                        {member.preExistingDisease === "Y" && (
                                            <>
                                                <Grid item xs={12} sm={3} md={4}>
                                                    <CustomTextField
                                                        label="Pre-Existing Diseases"
                                                        name="diseaseDetails"
                                                        fullWidth
                                                        multiline
                                                        rows={3}
                                                        value={member.diseaseDetails || ""}
                                                        onChange={(e) => handleMemberInputChange(index, e)}  // Add index parameter here
                                                        isRequired
                                                        error={Boolean(errors.diseaseDetails)}
                                                        helperText={errors[`diseaseDetails_${index}`]}
                                                        disabled={!editingMemberIds.has(member.id)}  // Fixed: Use editingMemberIds instead of isEditing
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={3}>
                                                    <CustomFileUpload

                                                        accept=".pdf,.jpg,.png"
                                                        type="file"
                                                        label="Upload Discharge Summary"
                                                        name="prescriptionFile"
                                                        onChange={(e) => {
                                                            const file = e.target.files[0];
                                                            setFormData((prev) => ({
                                                                ...prev,
                                                                prescriptionFile: file,
                                                            }));
                                                        }}
                                                        disabled={!editingMemberIds.has(member.id)}  // Fixed: Use editingMemberIds instead of isEditing
                                                    />
                                                    {errors.prescriptionFile && (
                                                        <FormHelperText error>{errors.prescriptionFile}</FormHelperText>
                                                    )}
                                                </Grid>
                                            </>
                                        )}
                                    </Grid>
                                </React.Fragment>
                            ))
                        ) : (
                            <Typography> </Typography>
                        )}

                </Grid>

            </form>
        </Box>
    );
};

export default Customer_memberInfo;
