// controllers/EmployeeInfoController.js
const employee_Persanol_Info_Model = require('../Models/employee_Persanol_Info_Model');
const uploadDir = process.env.UPLOAD_DIR;

const createEmployee_Info = async (req, res) => {
    try {
        // Access uploaded files from req.files
        const files = req.files;
        const data = req.body;

        // Create the employee data object from request body
        if (files.emp_photo) {
            const path = files.emp_photo[0].path;
            data.emp_photo = path.replace(uploadDir, ''); 
        }
        if (files.emp_adhar_front_pdf) {
            const path = files.emp_adhar_front_pdf[0].path;
            data.emp_adhar_front_pdf = path.replace(uploadDir, '');
        }
        if (files.emp_adhar_back_pdf) {
            const path = files.emp_adhar_back_pdf[0].path;
            data.emp_adhar_back_pdf = path.replace(uploadDir, '');
        }
        if (files.emp_PAN_pdf) {
            const path = files.emp_PAN_pdf[0].path;
            data.emp_PAN_pdf = path.replace(uploadDir, ''); 
        }
        if (files.emp_signed_offer_letter_pdf) {
            
            const path = files.emp_signed_offer_letter_pdf[0].path;
            data.emp_signed_offer_letter_pdf = path.replace(uploadDir, '');
        }
        if (files.emp_driving_license_pdf) {
            const path = files.emp_driving_license_pdf[0].path;
            data.emp_driving_license_pdf = path.replace(uploadDir, '');
        }
        // Log the employee data for debugging

        // Save the employee data to the database
        const result = await employee_Persanol_Info_Model.create(data);
        res.status(201).json(result);
    } catch (error) {
        console.error("Error creating EmployeeInfo:", error);
        res.status(500).json({ message: "Error creating EmployeeInfo", error });
    }
};


const getAllEmployeeInfo = async (req, res) => {
    try {
        const employee = await employee_Persanol_Info_Model.findAll();
        res.status(200).json(employee);
    } catch (error) {
        res.status(500).json({ message: "Error fetching EmployeeInfo", error });
    }
};

const getEmployeeInfoById = async (req, res) => {
    const { id } = req.params;
    try {
        const employee = await employee_Persanol_Info_Model.findById(id);
        if (!employee) {
            return res.status(404).json({ message: "Employee not found" });
        }
        res.status(200).json(employee);
    } catch (error) {
        res.status(500).json({ message: "Error fetching EmployeeInfo", error });
    }
};


const updateEmployeeInfo = async (req, res) => {
    const { id } = req.params;
    const { files } = req;
    const data = req.body;

    try {

        if (files.emp_photo) {
            const path = files.emp_photo[0].path;
            data.emp_photo = path.replace(uploadDir, ''); 
        }
        if (files.emp_adhar_front_pdf) {
            const path = files.emp_adhar_front_pdf[0].path;
            data.emp_adhar_front_pdf = path.replace(uploadDir, '');
        }
        if (files.emp_adhar_back_pdf) {
            const path = files.emp_adhar_back_pdf[0].path;
            data.emp_adhar_back_pdf = path.replace(uploadDir, '');
        }
        if (files.emp_PAN_pdf) {
            const path = files.emp_PAN_pdf[0].path;
            data.emp_PAN_pdf = path.replace(uploadDir, ''); 
        }
        if (files.emp_signed_offer_letter_pdf) {
            
            const path = files.emp_signed_offer_letter_pdf[0].path;
            data.emp_signed_offer_letter_pdf = path.replace(uploadDir, '');
        }
        if (files.emp_driving_license_pdf) {
            const path = files.emp_driving_license_pdf[0].path;
            data.emp_driving_license_pdf = path.replace(uploadDir, '');
        }
       
        data.updated_at = new Date();

        const result = await employee_Persanol_Info_Model.updateById(id, data);
        if (result) {
            res.status(200).json({ message: "EmployeeInfo updated successfully" });
        } else {
            res.status(404).json({ message: "EmployeeInfo not found" });
        }
    } catch (error) {
        console.error("Error updating EmployeeInfo:", error);
        res.status(500).json({ message: "Error updating EmployeeInfo", error: error.message });
    }
};


const softDeleteEmployeeInfo = async (req, res) => {
    const { id } = req.params;
    try {
        const result = await employee_Persanol_Info_Model.softDeleteById(id);
        if (result) {
            res.status(200).json({ message: "EmployeeInfo soft deleted successfully" });
        } else {
            res.status(404).json({ message: "EmployeeInfo not found" });
        }
    } catch (error) {
        res.status(500).json({ message: "Error deleting EmployeeInfo", error });
    }
};

const reinstateEmployeeInfo = async (req, res) => {
    const { id } = req.params;
    try {
        const result = await employee_Persanol_Info_Model.reinstateById(id);
        if (result) {
            res.status(200).json({ message: "EmployeeInfo reinstated successfully" });
        } else {
            res.status(404).json({ message: "EmployeeInfo not found" });
        }
    } catch (error) {
        res.status(500).json({ message: "Error reinstating EmployeeInfo", error });
    }
};
const getEmployeeByName = async (req, res) => {
    try {
        const { name } = req.params;
        const employee = await employee_Persanol_Info_Model.getEmployeeByName(name);


        res.status(200).json(employee);

    } catch (error) {
        console.error('Error fetching employee by name:', error);  // Log the actual error
        res.status(500).json({ message: 'Error fetching employee by name', error: error.message });
    }
};
// Get insurance companies by criteria
const getEmployeesByCriteria = async (req, res) => {
    try {
        const criteria = req.params.criteria.trim();

        let data;
        switch (criteria) {
            case 'none':
                data = await employee_Persanol_Info_Model.findAll();
                break;
            case 'newLastWeek':
                data = await employee_Persanol_Info_Model.newEmployeeThisWeek();
                break;
            case 'newThisWeek':
                data = await employee_Persanol_Info_Model.newEmployeeLastWeek();
                break;
            case 'deactivatedThisWeek':
                data = await employee_Persanol_Info_Model.deactivatedEmployeeThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await employee_Persanol_Info_Model.deactivatedEmployeeLastWeek();
                break;
            case 'editedThisWeek':
                data = await employee_Persanol_Info_Model.editedEmployeeThisWeek();
                break;
            case 'editedLastWeek':
                data = await employee_Persanol_Info_Model.editedEmployeeLastWeek();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }

        res.status(200).json(data);
    } catch (error) {
        console.error('Error fetching employees:', error); // Added error logging for better debugging
        res.status(500).json({ message: 'Error fetching employees', error: error.message });
    }
};

const getEmployeeByReportingManagerUserId = async (req, res) => {
    const { user_id } = req.params;
    try {
        const employee = await employee_Persanol_Info_Model.getEmployeeByReportingManagerUserId(user_id);
        res.status(200).json(employee);
    } catch (error) {
        res.status(500).json({ message: "Error fetching EmployeeInfo", error });
    }
}

const getEmployeeByUserId = async (req, res) => {
    const { user_id } = req.params;
    try {
        const employee = await employee_Persanol_Info_Model.getEmployeeByUserId(user_id);
        res.status(200).json(employee);
    } catch (error) {
        res.status(500).json({ message: "Error fetching EmployeeInfo", error });
    }
}

module.exports = {
    createEmployee_Info,
    getAllEmployeeInfo,
    getEmployeeInfoById,
    updateEmployeeInfo,
    softDeleteEmployeeInfo,
    reinstateEmployeeInfo,
    getEmployeeByName,
    getEmployeesByCriteria,
    getEmployeeByReportingManagerUserId,
    getEmployeeByUserId
};
