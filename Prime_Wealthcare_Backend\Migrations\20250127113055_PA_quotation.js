exports.up = function (knex) {
    return knex.schema.hasTable('pa_quotations').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('pa_quotations', (table) => {
                table.increments('pa_quotation_id').primary();
                table.string('quotation_number').notNullable().unique();
                table.integer('product').unsigned().notNullable().references('id').inTable('product_master');
                table.integer('insurance_company').unsigned().nullable().references('id').inTable('insurance_company');
                table.integer('main_product').unsigned().nullable().references('id').inTable('main_product');
                table.integer('sub_product_id').unsigned().nullable().references('id').inTable('sub_product');
                table.integer('customer_id').unsigned().nullable().references('id').inTable('customer_personal_info')
                table.string('agent_id').nullable();
                table.integer('duration').nullable();
                table.string('status').notNullable();
                table.string('Created_by').notNullable();
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now());
                table.string('Updated_by').nullable();
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now());
            });
        }
    })
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('pa_quotations');
};
