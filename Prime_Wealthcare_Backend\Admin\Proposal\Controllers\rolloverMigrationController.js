const rolloverMigrationModel = require('../Models/rolloverMigrationModel');
const uploadDir = process.env.UPLOAD_DIR;

exports.createRolloverMigration = async (req, res) => {
    try {
        let parsedBody = req.body;
        if (parsedBody.data && typeof parsedBody.data === 'string') {
            try {
                parsedBody = JSON.parse(parsedBody.data);
            } catch (error) {
                console.error('Error parsing req.body.data:', error);
            }
        }
        const { migrationData, memberData, paymentData } = parsedBody;

        if (req.files && req.files.policy_pdf && req.files.policy_pdf.length > 0) {
            const path = req.files.policy_pdf[0].path;
            migrationData.policy_pdf = path.replace(uploadDir, '');
        }

        migrationData.status = 'SUCCESS';
        migrationData.Created_at = new Date();

        // Set created_by for each member
        const membersWithCreator = memberData.map(member => ({
            ...member,
            // Created_by: req.user ? req.user.id : 'system',
            status: 'SUCCESS',
            Created_at: new Date()
        }));

        const result = await rolloverMigrationModel.createRolloverMigration(migrationData, membersWithCreator, paymentData);

        if (result && result.success) {
            return res.status(201).json({
                success: true,
                message: 'Rollover migration created successfully',
                data: result.data
            });
        } else {
            return res.status(400).json({
                success: false,
                message: 'Failed to create rollover migration'
            });
        }
    } catch (error) {
        console.error('Error creating rollover migration:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

exports.getRolloverMigrationById = async (req, res) => {
    try {
        const { id } = req.params;
        const migration = await rolloverMigrationModel.getRolloverMigrationById(id);

        if (migration) {
            return res.status(200).json({
                success: true,
                data: migration
            });
        } else {
            return res.status(404).json({
                success: false,
                message: 'Rollover migration not found'
            });
        }
    } catch (error) {
        console.error('Error retrieving rollover migration:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

exports.getRolloverMigrationByPolicyNumber = async (req, res) => {
    try {
        const { policyNumber } = req.params;
        const migration = await rolloverMigrationModel.getRolloverMigrationByPolicyNumber(policyNumber);

        if (migration) {
            return res.status(200).json({
                success: true,
                data: migration
            });
        } else {
            return res.status(404).json({
                success: false,
                message: 'Rollover migration not found'
            });
        }
    } catch (error) {
        console.error('Error retrieving rollover migration by policy number:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

exports.getAllRolloverMigrations = async (req, res) => {
    try {
        const migrations = await rolloverMigrationModel.getAllRolloverMigrations();

        return res.status(200).json({
            success: true,
            count: migrations.length,
            data: migrations
        });
    } catch (error) {
        console.error('Error retrieving rollover migrations:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

exports.getAllRolloverMigrationsByUserId = async (req, res) => {
    try {
        const userId = req.user.id;
        const migrations = await rolloverMigrationModel.getAllRolloverMigrationsByUserId(userId);

        return res.status(200).json({
            success: true,
            count: migrations.length,
            data: migrations
        });
    } catch (error) {
        console.error('Error retrieving rollover migrations by user ID:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};

// Update the controller method to handle updates with updated_by
exports.updateRolloverMigration = async (req, res) => {
    try {
        const { id } = req.params;
        let parsedBody = req.body;

        if (parsedBody.data && typeof parsedBody.data === 'string') {
            try {
                parsedBody = JSON.parse(parsedBody.data);
            } catch (error) {
                console.error('Error parsing req.body.data:', error);
            }
        }
        const { migrationData } = parsedBody;

        if (req.files && req.files.policy_pdf && req.files.policy_pdf.length > 0) {
            const path = req.files.policy_pdf[0].path;
            migrationData.policy_pdf = path.replace(uploadDir, '');
        }

        // Check if required data exists
        if (!migrationData) {
            return res.status(400).json({
                success: false,
                message: 'Migration data is required'
            });
        }

        // Set the current timestamp for Updated_at if not provided
        migrationData.Updated_at = migrationData.Updated_at || new Date();

        // Get Updated_by from migrationData, fallback to user from request if available
        //    migrationData.Updated_by = migrationData.Updated_by || (req.user ? req.user.id : 'system');

        const result = await rolloverMigrationModel.updateRolloverMigration(id, migrationData);

        if (result && result.success) {
            return res.status(200).json({
                success: true,
                message: 'Rollover migration updated successfully',
                data: result.data
            });
        } else {
            return res.status(400).json({
                success: false,
                message: 'Failed to update rollover migration'
            });
        }
    } catch (error) {
        console.error('Error updating rollover migration:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};