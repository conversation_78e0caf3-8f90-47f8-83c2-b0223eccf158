exports.up = function (knex) {
    return knex.schema.hasTable('pa_quotation_member').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('pa_quotation_member', (table) => {
                table.increments('id').primary(); // Auto-increment primary key
                table.integer('pa_quotation_id').unsigned().nullable();
                table.foreign('pa_quotation_id').references('pa_quotation_id').inTable('pa_quotations');
                table.integer('member_id').nullable();
                table.string('insuredName', 50).notNullable(); // Insured name
                table.string('relation').notNullable(); // Relation
                table.bigint('annual_income').notNullable();
                table.bigint('AD_suminsured').notNullable();
                table.bigint('PP_suminsured').notNullable();
                table.bigint('PT_suminsured').notNullable();
                table.bigint('TT_suminsured').notNullable();
                table.bigint('RF_suminsured').notNullable();
                table.bigint('AA_suminsured').notNullable();
                table.bigint('CS_suminsured').notNullable();
                table.bigint('FT_suminsured').notNullable();
                table.bigint('HC_suminsured').notNullable();
                table.bigint('LP_suminsured').nullable();
                table.bigint('LS_suminsured').nullable();
                table.bigint('ME_suminsured').nullable();
                table.bigint('AM_suminsured').nullable();
                table.bigint('BB_suminsured').nullable();
                table.string('status', 50).notNullable(); // Status
                table.string('Created_by').notNullable(); // Created by
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now()); // Created timestamp
                table.integer('Updated_by').nullable(); // Updated by
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now()); // Updated timestamp
            });
        }
    })

};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('pa_quotation_member');
};
