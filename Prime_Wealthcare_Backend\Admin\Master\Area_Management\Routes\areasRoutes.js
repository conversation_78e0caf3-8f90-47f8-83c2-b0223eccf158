const express = require('express');
const router = express.Router();
const AreasController = require('../Controllers/areasController');

router.get('/', AreasController.getAreas);
router.get('/:pincode/:city', AreasController.getAreasWithPincodeAndCity);
router.get('/:pincode_city', AreasController.getAreasByPincode_City);
router.get('/:id', AreasController.getAreaById);
router.post('/', AreasController.createArea);
router.put('/:id', AreasController.updateArea);
router.delete('/:id', AreasController.deleteArea);

module.exports = router;
