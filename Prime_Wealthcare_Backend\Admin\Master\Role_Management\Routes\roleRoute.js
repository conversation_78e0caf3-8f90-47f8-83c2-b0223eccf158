const express = require('express');
const roleManagementController = require('../Controllers/roleManagementController');
const router = express.Router();

// Route to get all roles
router.get('/', roleManagementController.getAllRoles);

// Route to get a role by ID
router.get('/:id', roleManagementController.getRoleById);

// Route to get a role by name
router.get('/name/:name', roleManagementController.getRoleByName);

// Route to create a new role
router.post('/', roleManagementController.createRole);

// Route to update a role by ID
router.put('/:id', roleManagementController.updateRole);

// Route to delete a role by ID
router.delete('/:id', roleManagementController.deleteRole);

// Route to reinstate a role by ID
router.put('/reinstate/:id', roleManagementController.reinstateRole);

// Route to get a role by criteria
router.get('/criteria/:criteria', roleManagementController.getRoleByCriteria);

module.exports = router;
