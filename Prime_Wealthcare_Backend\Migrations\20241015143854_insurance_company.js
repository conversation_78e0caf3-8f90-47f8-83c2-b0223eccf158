exports.up = function (knex) {
  return knex.schema.hasTable('insurance_company').then(function (exists) {
    if (!exists) {
      return knex.schema.createTable('insurance_company', function (table) {
        table.increments('id').primary();

        // Insurance company fields
        table.string('insurance_company_name', 500).notNullable();
        table.string('short_name', 255).notNullable();
        table.string('ado_code', 255).nullable();
        table.string('help_line_no', 48).notNullable();
        table.string('email_id', 500).notNullable().unique();
        table.string('zone_head_name', 500).notNullable();
        table.string('zone_head_number', 48).nullable();
        table.binary('company_logo').nullable();
        table.string('insurance_type').notNullable();

        table.string('address_line1', 255).nullable();  // VARCHAR(255)
        table.string('address_line2', 255).nullable();  // VARCHAR(255)
        table.integer('pincode', 255).notNullable();
        table.string('city', 255).notNullable();
        table.string('state', 255).notNullable();
        table.string('pincode_city').references('pincode_city').inTable('locations').onDelete('CASCADE');
        table.integer('area').unsigned().nullable()
          .references('id').inTable('areas') // Foreign key to `areas`
          .onDelete('CASCADE');

        // Audit fields
        table.integer('created_by').notNullable().defaultTo(1);
        table.integer('updated_by').notNullable().defaultTo(1);
        table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
        table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();

        // Status
        table.boolean('status').notNullable().defaultTo(true);
      });
    }
  });
};

exports.down = function (knex) {
  return knex.schema.dropTableIfExists('insurance_company');
};