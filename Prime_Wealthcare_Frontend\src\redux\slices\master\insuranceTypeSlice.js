import { createSlice } from '@reduxjs/toolkit';
import {fetchInsuranceTypes} from '../../actions/action'

const insuranceTypesSlice = createSlice({
  name: 'insuranceTypes',
  initialState: {
    insuranceTypes: [],
    loading: false,
    error: null,
  },
  reducers: {
    // You can define any synchronous reducers here if needed
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchInsuranceTypes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInsuranceTypes.fulfilled, (state, action) => {
        state.insuranceTypes = action.payload;
        state.loading = false;
      })
      .addCase(fetchInsuranceTypes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Something went wrong';
      });
  },
});

export default insuranceTypesSlice.reducer;
