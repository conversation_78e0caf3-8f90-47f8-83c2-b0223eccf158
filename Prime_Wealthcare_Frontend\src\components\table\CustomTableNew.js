import React, { useState } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  Paper, TablePagination, Checkbox, IconButton, Typography,
  Box, TextField, InputAdornment
} from '@mui/material';
import { styled } from '@mui/material/styles';
import EditIcon from '@mui/icons-material/Edit';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import DeleteIcon from '@mui/icons-material/Delete';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';

function CustomTableWithEditOnly({
  data = [], columns = [], onEdit,
  onSelectionChange, selectedRows, onSelectAll,
  onView, showViewButton = false, showEditButton = false,
  showDeleteButton = false, onDelete,
  editDisabled = false,showActionColumn = true
}) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({});

  // Ensure data is always an array
  const safeData = Array.isArray(data) ? data : [];

  // Filtered data based on search inputs
  const filteredData = safeData.filter((item) =>
    columns.every((column) => {
      const filterValue = filters[column.field];
      if (!filterValue) return true;
      return item[column.field]?.toString().toLowerCase().includes(filterValue.toLowerCase());
    })
  );

  const StyledTableCell = styled(TableCell)(({ theme }) => ({
    backgroundColor: "#528A7E",
    color: theme.palette.common.white,
    fontSize: 14,
    padding: '8px',
    width: `${100 / (columns.length + 2)}%`,
    textAlign: 'center',
  }));

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectAll = (event) => {
    onSelectAll(event.target.checked);
  };

  const handleFilterChange = (field, value) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [field]: value,
    }));
  };

  // Add new responsive styles
  const tableContainerStyles = {
    width: '100%',
    marginInline: 'auto',
    paddingTop: '1rem',
    overflowX: 'auto',
    '& .MuiTable-root': {
      minWidth: {
        xs: '800px',
        sm: '1000px',
        md: '100%'
      }
    },
    // Add position relative for sticky columns to work
    position: 'relative'
  };

  // Update sticky column styles
  const stickyColumnStyles = {
    position: 'sticky',
    backgroundColor: 'white',
    zIndex: 2,
    right: 0,  // Changed from left to right
  };

  // Remove checkbox styles since it won't be sticky anymore
  const checkboxColumnStyles = {
    backgroundColor: 'white',
  };

  const actionColumnStyles = {
    ...stickyColumnStyles,
  };

  const cellStyles = {
    padding: {
      xs: '8px 4px', // Reduced padding for mobile
      sm: '8px 8px', // Slightly larger for tablet
      md: '16px'     // Default padding for desktop
    }
  };

  return (
    <Box sx={{ position: 'relative', width: '100%' }}>
      <TableContainer component={Paper} sx={tableContainerStyles}>
        <Table sx={{ tableLayout: 'auto' }} aria-label="customized table with edit only">
          <TableHead>
            <TableRow>
              <StyledTableCell
                padding="checkbox"
                sx={{
                  textAlign: 'center',
                  backgroundColor: "#528A7E"
                }}
              >
                <Checkbox
                  onChange={handleSelectAll}
                  checked={data.length > 0 && selectedRows?.length === data.length}
                />
              </StyledTableCell>
              {columns.map((column) => (
                <StyledTableCell key={column.field}>
                  {column.headerName}
                </StyledTableCell>
              ))}
               {showActionColumn && ( 
              <StyledTableCell
                sx={{
                  position: 'sticky',
                  right: 0,
                  zIndex: 3,
                  backgroundColor: "#528A7E"
                }}
              >
                Action
              </StyledTableCell>
               )}
            </TableRow>
          </TableHead>
          <TableBody>
            {/* Search row */}
            <TableRow sx={{
              height: {
                xs: '32px',
                sm: '36px',
                md: '40px'
              }
            }}>
              <TableCell
                padding="checkbox"
                sx={{
                  textAlign: 'center',
                  ...checkboxColumnStyles,
                  backgroundColor: 'white'
                }}
              />
              {columns.map((column, index) => (
                <TableCell key={`${column.field}-filter`} sx={{ ...cellStyles, padding: '4px' }}>
                  <TextField
                    variant="outlined"
                    size="small"
                    placeholder={`Search`}
                    value={filters[column.field] || ''}
                    onChange={(e) => handleFilterChange(column.field, e.target.value)}
                    fullWidth
                    sx={{ height: 40 }}
                  />
                </TableCell>
              ))}
               {showActionColumn && (  
              <TableCell sx={{ ...actionColumnStyles, backgroundColor: 'white' }} />
               )}
              </TableRow>
            {/* Data rows */}
            {filteredData.length > 0 ? (
              filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((item) => (
                <TableRow key={item.id}>
                  <TableCell
                    padding="checkbox"
                    sx={{
                      ...cellStyles,
                      textAlign: 'center',
                      ...checkboxColumnStyles,
                      color: item.status === 0 ? 'red' : 'inherit'
                    }}
                  >
                    <Checkbox
                      checked={selectedRows.includes(item.id)}
                      onChange={() => onSelectionChange(item.id)}
                    />
                  </TableCell>
                
                  {columns.map((column) => (
                    <TableCell
                      key={column.field}
                      sx={{
                        ...cellStyles,
                        color: item.status === 0 ? 'red' : 'inherit'
                      }}
                    >
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        fontSize: {
                          xs: '12px',
                          sm: '13px',
                          md: '14px'
                        }
                      }}>
                        {column.renderCell ? column.renderCell({ row: item }) : item[column.field] || "N/A"}
                      </Box>
                    </TableCell>
                  ))}
                  {showActionColumn && (
                  <TableCell sx={{ ...cellStyles, ...actionColumnStyles }}>
                    {item.status === 1 ? (
                      <>
                        {showEditButton && (
                          <IconButton
                            onClick={() => onEdit(item.id, item?.proposal_type)}
                            disabled={editDisabled}
                            sx={{
                              color: editDisabled ? 'grey.400' : 'green',
                              padding: '8px',
                              '&.Mui-disabled': {
                                color: 'rgba(0, 0, 0, 0.26)'
                              },
                              '&:hover': {
                                backgroundColor: editDisabled ? 'transparent' : 'rgba(0, 128, 0, 0.04)'
                              }
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                        )}
                        {showDeleteButton && (
                          <IconButton
                            onClick={() => onDelete?.(item)}
                            sx={{
                              color: 'red',
                              padding: '8px',
                              '&:hover': {
                                backgroundColor: 'rgba(255, 0, 0, 0.04)'
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        )}
                      </>
                    ) : (
                      <IconButton
                        onClick={() => {
                          onView?.(item.id, item?.proposal_type ? item?.proposal_type : item?.source_table);
                        }}
                        sx={{
                          color: '#528a7e',
                          padding: '8px'
                        }}
                      >
                        <RemoveRedEyeOutlinedIcon />
                      </IconButton>
                    )}
                    {showViewButton && item.status === 1 && (
                      <IconButton
                        onClick={() => {
                          onView?.(item.id, item?.proposal_type ? item?.proposal_type : item?.source_table);
                        }}
                        sx={{ color: 'blue', padding: '8px' }}
                      >
                        <RemoveRedEyeOutlinedIcon />
                      </IconButton>
                    )}
                  </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length + 2}>
                  <Typography variant="body1" align="center" sx={{ padding: '1rem' }}>
                    No data present
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Box sx={{
        position: 'sticky',
        right: 0,
        bottom: 0,
        width: '100%',
        backgroundColor: 'white',
        borderTop: '1px solid rgba(224, 224, 224, 1)',
        display: 'flex',
        justifyContent: 'flex-end'
      }}>
        <TablePagination
          rowsPerPageOptions={[
            10,
            20,
            { label: 'All', value: data.length }
          ]}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{
            '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
              fontSize: {
                xs: '12px',
                sm: '14px',
                md: '16px'
              }
            }
          }}
        />
      </Box>
    </Box>
  );
}

export default CustomTableWithEditOnly;