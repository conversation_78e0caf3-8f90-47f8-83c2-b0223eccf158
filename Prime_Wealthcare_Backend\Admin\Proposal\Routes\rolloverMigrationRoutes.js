const express = require('express');
const router = express.Router();
const rolloverMigrationController = require('../Controllers/rolloverMigrationController');
const upload = require('../Middleware/upload');

const uploadFields = upload.fields([
    { name: 'policy_pdf', maxCount: 1 }
]);

// Get all rollover migrations
router.get('/', rolloverMigrationController.getAllRolloverMigrations);

// Get a rollover migration by ID
router.get('/:id', rolloverMigrationController.getRolloverMigrationById);

// Create a new rollover migration
router.post('/', uploadFields, rolloverMigrationController.createRolloverMigration);

// Add the update route
router.put('/:id', uploadFields, rolloverMigrationController.updateRolloverMigration);

module.exports = router;