import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { createRole, getAllRoles, getRoleById, getRolesByName, updateRole } from '../../../redux/actions/action';
import { trimFormData } from '../../../utils/Reusable';

function RoleListForm() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const role = useSelector(state => state.roleManagementReducer.role);
    const roles = useSelector(state => state.roleManagementReducer.roles);

    const [formData, setFormData] = useState({
        role_name: '',
        department_name: '',
        created_at: new Date().toISOString(),
    });

    const [errors, setErrors] = useState({
        role_name: false,
        department_name: false,
    })

    useEffect(() => {
        if (id) {
            dispatch(getRoleById(id));
        } else {
            resetFormData();
        }
    }, [id, dispatch]);

    useEffect(() => {
        if (role && id) {
            setFormData({
                role_name: role.role_name || '',
                department_name: role.department_name || '',
                created_at: role.created_at || '',
            });
        }
    }, [role, id]);

    const resetFormData = () => {
        setFormData({
            role_name: '',
            department_name: '',
            created_at: new Date().toISOString(),
        });
    };

    const validate = () => {
        let tempErrors = {};

        // Check if role_name is empty
        if (!formData.role_name) {
            tempErrors.role_name = "Role name is required";
        } else {
            // Check for duplicate role names in the existing roles
            const isDuplicate = roles.find(role => role.role_name === formData.role_name && role.id !== Number(id)); // Exclude the current role being edited by checking id

            if (isDuplicate) {
                tempErrors.role_name = "Role name already exists";
            }
        }

        // Check if department_name is empty
        if (!formData.department_name) {
            tempErrors.department_name = "Department name is required";
        }

        setErrors(tempErrors);

        // Return true if there are no errors
        return Object.keys(tempErrors).length === 0;
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        
        if (value === ' ') {
            setErrors({
                ...errors,
                [name]: 'Do not start with a whitespace character'
            })
            return;
        }
        const data = value.toUpperCase().replace(/\s{2,}$/, ' ')
        setFormData({
            ...formData,
            [name]: data,
        });
        setErrors({
            ...errors,
            [name]: false
        })
        if (name === 'role_name' && value !== '') {
            dispatch(getRolesByName(value));
        }
    };

    const handleRoleCreationAndUpdate = () => {
        const isValid = validate();
        if (!isValid) return; // Stop if the form is invalid

        const { role_name, department_name } = formData;
        const data = { role_name, department_name };
        const filteredData = trimFormData(data);

        if (id) {
            dispatch(updateRole({ id, data: filteredData }));
        } else {
            dispatch(createRole(filteredData));
        }
        setTimeout(() => {
            dispatch(getAllRoles());
        }, 500);
    };


    const formatDate = (date) => {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    const handleSave = () => {
        handleRoleCreationAndUpdate();
        if (validate()) {
            handleCancel();
        }
    };

    const handleSaveAndNew = () => {
        handleRoleCreationAndUpdate();
        if (validate()) {
            resetFormData();
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/role-list');
    };

    return (
        <form>
            <Grid container spacing={2}>
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ModuleName moduleName="Role" pageName={id ? role?.status === 0 ? "View" : "Edit" : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!id && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '120px', width: '120px', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleSaveAndNew}
                        >
                            Save & New
                        </Button>
                    )}
                    {(role?.status === 1 || !id) && <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                        onClick={handleSave}
                    >
                        Save
                    </Button>}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>
                <Grid item xs={12}>
                    <Box
                        sx={{
                            backgroundColor: '#f0f0f0',
                            padding: '1rem',
                            borderRadius: '4px',
                            mb: 2,
                        }}
                    >
                        <h2>Role Information</h2>
                    </Box>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            name="role_name"
                            label="Role Name"
                            value={formData.role_name}
                            onChange={handleChange}
                            disabled={id && role?.status === 0}
                            error={!!errors.role_name}
                            helperText={errors.role_name}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    position: 'relative',
                                    '&:before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        borderTopLeftRadius: '20px',
                                        borderBottomLeftRadius: '20px',
                                    },
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            name="department_name"
                            label="Department Name"
                            value={formData.department_name}
                            onChange={handleChange}
                            disabled={id && role?.status === 0}
                            error={!!errors.department_name}
                            helperText={errors.department_name}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    position: 'relative',
                                    '&:before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                    },
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Created At"
                            name="created_at"
                            value={formatDate(formData.created_at)}
                            onChange={handleChange}
                            type="datetime-local"
                            disabled
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    position: 'relative',
                                    '&:before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                    },
                                },
                                '& .MuiOutlinedInput-input': {
                                    outline: 0,
                                },
                            }}
                        />
                    </Grid>
                </Box>
            </Grid>
        </form>
    );
}

export default RoleListForm;