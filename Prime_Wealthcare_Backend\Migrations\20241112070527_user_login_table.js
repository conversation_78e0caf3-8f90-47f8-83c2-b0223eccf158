exports.up = function (knex) {
    return knex.schema.createTable('user_login', function (table) {
        table.increments('id').primary();
        table.string('user_id').notNullable();
        table.string('user_type').nullable();
        table.text('token').notNullable();
        table.timestamp('login_time').defaultTo(knex.fn.now());
        table.timestamp('expires_at');
        table.boolean('is_active').defaultTo(true);
        table.timestamps(true, true);
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('user_login');
};
