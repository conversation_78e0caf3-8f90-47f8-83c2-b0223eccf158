exports.up = function (knex) {
    return knex.schema.hasTable('proposal_members').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('proposal_members', (table) => {
                table.increments('id').primary(); // Auto-increment primary key
                table.integer('proposal_id').unsigned().references('id').inTable('proposals');
                table.integer('customer_member_id').unsigned().references('id').inTable('customer_member_info').onDelete('CASCADE').nullable();
                table.integer('member_id').nullable();
                table.string('relation').notNullable(); // Relation
                table.integer('sum_insured').notNullable();
                table.integer('deductible').nullable();
                table.string('health_declaration').nullable();
                table.string('nominee_name').nullable(); // Nominee name
                table.string('nominee_gender').nullable(); // Nominee gender
                table.string('nominee_dob').nullable(); // Nominee date of birth
                table.integer('nominee_relation').nullable(); // Nominee relation
                table.string('appointee_name').nullable(); // Appointee name
                table.string('appointee_gender').nullable(); // Appointee gender
                table.string('appointee_dob').nullable(); // Appointee date of birth
                table.integer('appointee_relation').nullable(); // Appointee relation
                table.string('status').notNullable(); // Status
                table.string('Created_by').notNullable(); // Created by
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now()); // Created timestamp
                table.integer('Updated_by').nullable(); // Updated by
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now()); // Updated timestamp
            });
        }
    })
};
exports.down = function (knex) {
    return knex.schema.dropTableIfExists('proposal_members');
};
