exports.up = function (knex) {
  return knex.schema.createTable('agents', function (table) {
    table.increments('id').primary();
    table.string('full_name').notNullable();
    // Foreign key from pick_list for gender, education, marital status
    table.integer('gender_id').unsigned().references('id').inTable('pick_list');
    table.integer('education_id').unsigned().references('id').inTable('pick_list');
    table.integer('marital_status_id').unsigned().references('id').inTable('pick_list');
    table.date('marriage_date').nullable();
    // Personal information
    table.string('aadhar_number').notNullable();
    table.string('pan_number').notNullable();
    table.string('personal_email').notNullable();
    table.string('personal_mobile').notNullable();
    table.date('dob').notNullable();
    table.string('blood_group');
    table.string('driving_license_no');
    // Authentication information
    table.string('agent_id').notNullable().unique();
    table.string('password').notNullable();
    // Foreign key for role
    table.integer('role_id').unsigned().references('id').inTable('role_management').notNullable();
    // Foreign key for branch (insurance company table)
    table.integer('branch_id').unsigned().references('id').inTable('imf_branches').notNullable();
    // Reporting managers (store as string to accommodate different ID formats)
    table.string('first_reporting_manager_id').notNullable();
    table.string('second_reporting_manager_id').notNullable();
    // Official details
    table.string('official_email').notNullable();
    table.string('official_mobile').notNullable();
    // Employment info
    table.date('date_of_joining').notNullable();
    // Fields to store file paths
    table.string('photo').notNullable(); // File path for photo
    table.string('aadhar_card_front').notNullable(); // File path for Aadhar front
    table.string('aadhar_card_back').notNullable(); // File path for Aadhar back
    table.string('pan_card').notNullable(); // File path for PAN
    table.string('signed_offer_letter_card'); // File path for signed offer letter
    table.string('driving_license_card'); // File path for driving license (nullable)
    // Common Fields
    table.boolean('status').notNullable().defaultTo(true); // Status field (Active or Inactive)
    table.integer('created_by').notNullable().defaultTo(1);
    table.integer('updated_by').notNullable().defaultTo(1);
    // Timestamps
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable('agents');
};
