const MainProduct = require('../Models/mainProduct');

// Get all products
exports.getAllProducts = async (req, res, next) => {
    try {
        const data = await MainProduct.findAll();

        res.status(200).json(data);
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Get product by ID
exports.getProductById = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = await MainProduct.findById(id);
        if (!data) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get product by name
exports.getProductByName = async (req, res, next) => {
    try {
        const name = req.params.name;
        const data = await MainProduct.findByName(name);
        if (!data) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Create a new product
exports.createProduct = async (req, res, next) => {
    try {
        const data = req.body;
        const result = await MainProduct.create(data);
        res.status(201).json(result);
    } catch (error) {
        next(error);
    }
};

// Update product by ID
exports.updateProduct = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const result = await MainProduct.updateById(id, data);
        if (!result) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json({ message: 'Product updated successfully' });
    } catch (error) {
        next(error);
    }
};

// Delete (deactivate) product by ID
exports.deleteProduct = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await MainProduct.deleteById(id);
        if (!result) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json({ message: 'Product deactivated successfully' });
    } catch (error) {
        next(error);
    }
};

// Reinstate a product by ID
exports.reinstateProduct = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await MainProduct.reinstate(id);
        if (!result) {
            return res.status(404).json({ message: 'Product not found' });
        }
        res.status(200).json({ message: 'Product reinstated successfully' });
    } catch (error) {
        next(error);
    }
};

// Get products by specific criteria
exports.getProductsByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await MainProduct.findAll();
                break;
            case 'newLastWeek':
                data = await MainProduct.newLastWeek();
                break;
            case 'newThisWeek':
                data = await MainProduct.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await MainProduct.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await MainProduct.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await MainProduct.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await MainProduct.editedLastWeek();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }

        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};