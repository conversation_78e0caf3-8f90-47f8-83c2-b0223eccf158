const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new endorsment
const create = async (data) => {
    try {
        const { result } = await db('endorsment_type').insert(data);

        return result;
    } catch (error) {
        console.error("Error creating endorsment type:", error);
        throw error;
    }
};

// Find all endorsments
const findAll = async () => {
    try {
        return await db('endorsment_type').select('*');
    } catch (error) {
        throw error;
    }
};

// Find endorsment by ID
const findById = async (id) => {
    try {
        const endorsment = await db('endorsment_type').where({ id }).first();
        return endorsment;
    } catch (error) {
        throw error;
    }
};

// Find endorsments by endorsment name (partial match)
const findByName = async (query) => {
    try {
        const endorsments = await db('endorsment_type')
            .where(function () {
                this.where('endorsment_name', 'like', `%${query}%`)
                    .orWhere('endorsment_type', 'like', `%${query}%`)
            })
        return endorsments;
    } catch (error) {
        console.error("Error finding endorsments by name:", error);
        throw error;
    }
};

const updateById = async (id, data) => {
    try {
        // Add the formatted updated_at field to the data object
        data.updated_at = getCurrentTimestamp();

        const result = await db('endorsment_type')
            .where({ id })
            .update(data);  // Use the passed data object for the update
        return result;
    } catch (error) {
        throw error;
    }
};



// Delete endorsment by ID (soft delete by updating status)
const deleteById = async (id) => {
    try {
        const result = await db('endorsment_type').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

// Reinstate endorsment by ID
const reinstate = async (id) => {
    try {
        const result = await db('endorsment_type').where({ id }).update({ status: 1, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

const newLastWeek = async () => {
    try {
        return await db('endorsment_type')
            .where('created_at', '<', db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'));
    } catch (error) {
        throw error;
    }
};


// New endorsments created this week
const newThisWeek = async () => {
    try {
        const query = db('endorsment_type')
            .whereBetween('created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ]);



        return await query;
    } catch (error) {
        throw error;
    }
};


// Deactivated endorsments updated this week
const deactivatedThisWeek = async () => {
    try {
        return await db('endorsment_type')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);
    } catch (error) {
        throw error;
    }
};

// Deactivated endorsments updated last week
const deactivatedLastWeek = async () => {
    try {
        const query = db('endorsment_type')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited endorsments updated this week
const editedThisWeek = async () => {
    try {
        const query = db('endorsment_type')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited endorsments updated last week
const editedLastWeek = async () => {
    try {
        return await db('endorsment_type')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findAll,
    findById,
    findByName,
    updateById,
    deleteById,
    reinstate,
    newLastWeek,
    newThisWeek,
    deactivatedThisWeek,
    deactivatedLastWeek,
    editedThisWeek,
    editedLastWeek
};
