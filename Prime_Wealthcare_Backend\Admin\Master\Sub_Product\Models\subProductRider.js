const knexConfig = require('../../../../knexfile');
const db = require('knex')(knexConfig.development);

class SubProductRider {
    // Create a new sub-product
    static async create(productData) {
        try {
            const [data] = await db('sub_product_rider').insert(productData);

            return data;
        } catch (error) {
            console.error('Error inserting sub-product:', error);
            throw error;
        }
    }

    // Update an existing sub-product by ID
    static async update(id, productData) {
        if (!id) throw new Error("Sub-product ID is required");

        try {
            const result = await db('sub_product_rider').where('id', id).update(productData);
            return result;
        } catch (error) {
            console.error(`Error updating sub-product with ID: ${id}`, error);
            throw error;
        }
    }

    // Soft delete a sub-product by ID (set status to 0)
    static async delete(id) {
        try {
            await db('sub_product_rider').where('id', id).del();
        } catch (error) {
            console.error(`Error deleting sub-product with ID: ${id}`, error);
            throw error;
        }
    }
}

module.exports = SubProductRider;
