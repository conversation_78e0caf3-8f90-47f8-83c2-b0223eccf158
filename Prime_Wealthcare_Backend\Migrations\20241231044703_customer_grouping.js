// migration file for creating customer_grouping table
exports.up = function (knex) {
    return knex.schema.hasTable('customer_grouping').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('customer_grouping', function (table) {
                table.increments('id').primary(); // Auto incrementing ID field
                table.integer('customer_id').unsigned().nullable();
                table.integer('relation_id').unsigned().nullable();
                table.boolean('head_name', 50).nullable();
                table.string('group_code', 500).nullable();

                table.string('assigned_to', 50).nullable();
                table.integer('agent_id').unsigned().nullable();
                // Common Fields
                table.boolean('status').notNullable().defaultTo(true); // Status field (Active or Inactive)
                table.integer('created_by').notNullable().defaultTo(1);
                table.integer('updated_by').notNullable().defaultTo(1);
                // Timestamps
                table.timestamp('created_at').defaultTo(knex.fn.now());
                table.timestamp('updated_at').defaultTo(knex.fn.now());
                // Adding foreign keys (adjust table names as per your actual schema)
                table.foreign('customer_id').references('id').inTable('customer_personal_info');
                table.foreign('relation_id').references('id').inTable('pick_list');
                table.foreign('agent_id').references('id').inTable('agents');
            });
        };
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('customer_grouping');
};
