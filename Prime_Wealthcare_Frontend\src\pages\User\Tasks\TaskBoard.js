import React, { useState, useEffect } from "react";
import { DragDrop<PERSON>ontext, Droppable, Draggable } from "@hello-pangea/dnd";
import {
  Box, Typography, Card, CardContent, Grid, Button,
  TextField, Dialog, DialogTitle, DialogContent,
  DialogActions, InputAdornment, IconButton
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import ModuleName from "../../../components/table/ModuleName";
import { useDispatch, useSelector } from 'react-redux';
import { getEmployeeByReportingManagerUserId, fetchEmployeeByUserId, getTaskDetails, addCommentReply } from '../../../redux/actions/action';
import DropDown from '../../../components/table/DropDown';
import {
  getAllTasks,
  createTask,
  updateTask,
  deleteTask,
  createTaskComment, updateCommentNotification,
  getTasksByAssignedTo,getTaskNotifications,
  updateTaskNotification
} from '../../../redux/actions/action';
import ReplyIcon from '@mui/icons-material/Reply';
import CommentIcon from '@mui/icons-material/Comment';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PersonIcon from '@mui/icons-material/Person';
import { toast } from 'react-toastify';
import { keyframes } from '@mui/system';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import BarLoader from '../../../components/BarLoader';

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
`;

const generateTaskCode = (existingTasks) => {
  const prefix = 'TM-';

  if (!existingTasks || !Array.isArray(existingTasks) || existingTasks.length === 0) {
    return `${prefix}01`;
  }

  const numbers = existingTasks
    .map(task => task.task_code)
    .filter(code => code && code.startsWith(prefix))
    .map(code => parseInt(code.replace(prefix, ''), 10))
    .filter(num => !isNaN(num));

  const highestNumber = Math.max(...numbers, 0);
  const nextNumber = highestNumber + 1;

  return `${prefix}${nextNumber.toString().padStart(2, '0')}`;
};

const TASK_STATUSES = {
  TODO: 'To-Do',
  IN_PROGRESS: 'In Progress',
  DONE: 'Completed'
};

const organizeTasksByStatus = (tasks) => {
  return {
    'To-Do': tasks.filter(task => task.status === 'To-Do'),
    'In Progress': tasks.filter(task => task.status === 'In Progress'),
    'Completed': tasks.filter(task => task.status === 'Completed')
  };
};

const KanbanBoard = () => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.auth.user);
  const { tasks: tasksList} = useSelector((state) => state.tasks);
  const [organizedTasks, setOrganizedTasks] = useState({
    'To-Do': [],
    'In Progress': [],
    'Completed': []
  });
  const [assignees, setAssignees] = useState([]);
  const [open, setOpen] = useState(false);
  const [taskDetailsOpen, setTaskDetailsOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [newComment, setNewComment] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [currentEmployeeId, setCurrentEmployeeId] = useState(null);
  const [newReply, setNewReply] = useState("");
  const [replyingTo, setReplyingTo] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTasks, setFilteredTasks] = useState({
    'To-Do': [],
    'In Progress': [],
    'Completed': []
  });
  const [loading, setLoading] = useState(false);

  const [newTask, setNewTask] = useState({
    title: "",
    task_code: "",
    description: "",
    assigned_to: "",
    assigned_by: "",
    created_by: "",
    status: TASK_STATUSES.TODO
  });

  useEffect(() => {
    dispatch(getTaskDetails(currentEmployeeId));
  
  }, [dispatch, currentEmployeeId]);

  useEffect(() => {
    if (Array.isArray(tasksList)) {
      setOrganizedTasks(organizeTasksByStatus(tasksList));
    }
  }, [tasksList]);

  const handleSearch = (query) => {
    setSearchQuery(query);
    
    if (!Array.isArray(tasksList)) return;
  
    const searchResults = tasksList.filter(task => {
      const assigneeName = assignees.find(a => a.id === task.assigned_to)?.label || '';
      
      return (
        task.task_code?.toLowerCase().includes(query.toLowerCase()) ||
        task.title?.toLowerCase().includes(query.toLowerCase()) ||
        assigneeName.toLowerCase().includes(query.toLowerCase())
      );
    });
  
    setFilteredTasks(organizeTasksByStatus(searchResults));
  };
  const handleCreateTask = async () => {
    if (newTask.title && newTask.task_code) {
      const taskData = {
        title: newTask.title,
        task_code: newTask.task_code,
        description: newTask.description,
        assigned_to: parseInt(newTask.assigned_to),
        assigned_by: currentEmployeeId,
        created_by: currentEmployeeId,
        status: TASK_STATUSES.TODO
      };
      setLoading(true);
      try {
        await dispatch(createTask(taskData)).unwrap();
        toast.success('Task created successfully');
        if (currentEmployeeId) {
          dispatch(getTaskDetails(currentEmployeeId));
        }
        handleClose();
        setLoading(false);
      } catch (error) {
        console.error('Failed to create task:', error);
        setLoading(false);
      }
    } else {
      console.error('Missing required fields or employee ID not available');
      setLoading(false);
    }
  };

  const handleCloseDialog = () => {
    setOpen(false);
    setNewTask({
      title: "",
      code: "",
      description: "",
      assigned_to: "",
      status: "todo",
      created_by: "",
      assigned_by: ""
    });
  };

  const handleOpen = (status) => {
    const nextTaskCode = generateTaskCode(tasksList);
    setSelectedStatus(status);
    setOpen(true);
    setNewTask(prev => ({
      ...prev,
      task_code: nextTaskCode
    }));
  };

  const handleClose = () => {
    setOpen(false);
    setNewTask({
      title: "",
      code: "",
      description: "",
      assignee: "",
      comments: []
    });
  };

  const handleTaskClick = async (task, status) => {
    if (task && status) {
      setSelectedTask(task);
      setSelectedStatus(status);
      setTaskDetailsOpen(true);

      try {
        // Update task notification only if current user is the intended recipient
        const isTaskNotificationRecipient = task.assigned_to === currentEmployeeId ? 
          task.assigned_by === task.created_by : 
          task.assigned_to === task.created_by;

        if (isTaskNotificationRecipient) {
          await dispatch(updateTaskNotification({
            taskId: task.id,
            notificationData: { 
              updated_by: currentEmployeeId
            }
          })).unwrap();
        }

        // Update comment notifications only if current user is the comment recipient
        if (task.comments && task.comments.length > 0) {
          const updatePromises = task.comments.map(comment => {
            if (comment.user_id === currentEmployeeId) {
              return dispatch(updateCommentNotification({
                userId: currentEmployeeId,
                taskId: task.id,
                commentId: comment.comment_id
              }));
            }
            return Promise.resolve(); // Skip if not the recipient
          });
          await Promise.all(updatePromises);
        }

        // Refresh task details
        await dispatch(getTaskDetails(currentEmployeeId));
      } catch (error) {
        console.error('Error updating notifications:', error);
      }
    }
  };

  const handleTaskDetailsClose = () => {
    setTaskDetailsOpen(false);
    setSelectedTask(null);
    setNewComment("");
  };

  useEffect(() => {
    const fetchCurrentEmployee = async () => {
      if (user?.userId) {
        setLoading(true);
        try {
          const currentEmployeeResponse = await dispatch(fetchEmployeeByUserId(user.userId)).unwrap();

          if (currentEmployeeResponse) {
            setCurrentEmployeeId(currentEmployeeResponse.id);
          }
        } catch (error) {
          console.error('Error fetching current employee:', error);
        }
        setLoading(false);
      }
    };

    fetchCurrentEmployee();
  }, [dispatch, user]);

  useEffect(() => {
    const fetchReportingEmployees = async () => {
      if (user?.userId) {
        try {
          const reportingEmployeesResponse = await dispatch(getEmployeeByReportingManagerUserId(user.userId)).unwrap();

          if (reportingEmployeesResponse && Array.isArray(reportingEmployeesResponse)) {
            const employeeNames = reportingEmployeesResponse.map(employee => ({
              id: employee.id,
              value: employee.id,
              label: employee.employee_full_name || `${employee.first_name} ${employee.last_name}`
            }));
            setAssignees(employeeNames);
          }
        } catch (error) {
          console.error('Error fetching reporting employees:', error);
          setAssignees([]);
        }
      }
    };

    fetchReportingEmployees();
  }, [dispatch, user]);

  useEffect(() => {
    if (user?.user_id) {
      dispatch(getTasksByAssignedTo(user.user_id));
    }
  }, [dispatch, user]);

  useEffect(() => {
    if (Array.isArray(tasksList)) {
      const organized = organizeTasksByStatus(tasksList);
      setOrganizedTasks(organized);
      setFilteredTasks(organized);
    }
  }, [tasksList]);

  const determineNotificationUserId = (task) => {
    if (!task || !currentEmployeeId) {
      console.warn('Missing task or currentEmployeeId');
      return null;
    }
    return task.assigned_to === currentEmployeeId ? task.assigned_by : task.assigned_to;
  };

  // Remove the inline logic and update the handlers to use the helper function
  const handleAddComment = async () => {
    if (newComment.trim() && selectedTask) {
      setLoading(true);
      try {
        const notificationUserId = determineNotificationUserId(selectedTask);
        if (!notificationUserId) {
          setLoading(false);
          return;
        }

        await dispatch(createTaskComment({
          taskId: selectedTask.id,
          commentData: {
            task_id: selectedTask.id,
            user_id: notificationUserId,
            comment: newComment,
            created_at: new Date().toISOString(),
            created_by: currentEmployeeId,
          }
        })).unwrap();

        // Rest of the code remains the same
        if (currentEmployeeId) {
          await dispatch(getTaskDetails(currentEmployeeId));
        }
        setNewComment("");
        handleTaskDetailsClose();

      } catch (error) {
        console.error('Failed to add comment:', error);
      }
      setLoading(false);
    }
  };

  const handleAddReply = async (commentId) => {
    if (newReply.trim() && selectedTask) {
      setLoading(true);
      try {
        const notificationUserId = determineNotificationUserId(selectedTask);
        if (!notificationUserId) return;

        await dispatch(addCommentReply({
          commentId: commentId,
          replyData: {
            task_id: selectedTask.id,
            user_id: notificationUserId,
            comment: newReply,
            created_at: new Date().toISOString(),
            created_by: currentEmployeeId,
            parent_comment_id: commentId
          }
        })).unwrap();

        // Rest of the code remains the same
        if (currentEmployeeId) {
          await dispatch(getTaskDetails(currentEmployeeId));
        }
        setNewReply("");
        setReplyingTo(null);
        handleTaskDetailsClose();
        toast.success('Reply added successfully');
      } catch (error) {
        console.error('Failed to add reply:', error);
      }
      setLoading(false);
    }
  };

  const onDragEnd = async (result) => {
    const { source, destination, draggableId } = result;

    // Return if dropped outside or in same position
    if (!destination ||
      (destination.droppableId === source.droppableId &&
        destination.index === source.index)
    ) {
      return;
    }

    setLoading(true);
    try {
      const taskId = parseInt(draggableId);
      const newStatus = destination.droppableId;
      const taskToUpdate = tasksList.find(task => task.id === taskId);
      
      if (!taskToUpdate) {
        console.error('Task not found');
        return;
      }

      // Determine who should receive the notification
      const notificationUserId = determineNotificationUserId(taskToUpdate);
      
      // Generate appropriate message based on status change
      const statusChangeMessage = `Task ${taskToUpdate.task_code} moved to ${newStatus}`;

      // Update task status
      await dispatch(updateTask({
        id: taskId,
        taskData: {
          status: newStatus,
          updated_by: currentEmployeeId,
         // user_id: notificationUserId
        }
      })).unwrap();

      // Create notification for status change
      await dispatch(updateTaskNotification({
        taskId: taskId,
        notificationData: {
          task_id: taskId,
          user_id: notificationUserId,
          message: statusChangeMessage,
          created_by: currentEmployeeId,
          created_at: new Date().toISOString(),
          is_read: 0
        }
      })).unwrap();

      // Refresh tasks and notifications
      if (currentEmployeeId) {
        await Promise.all([
          dispatch(getTaskDetails(currentEmployeeId)),
          dispatch(getTaskNotifications(notificationUserId)),
        ]);
      }

    } catch (error) {
      console.error('Failed to update task status:', error);
      toast.error('Failed to update task status');
    }
    setLoading(false);
  };
  const filterTasksByPermission = (tasksList, userId, userEmployees) => {
    return Object.keys(tasksList).reduce((acc, status) => {
      acc[status] = tasksList[status].filter(task =>
        task.createdBy === userId ||
        task.assignee_id === userId ||
        (userEmployees && userEmployees.some(emp => emp.id === task.assignee_id))
      );
      return acc;
    }, {
      [TASK_STATUSES.TODO]: [],
      [TASK_STATUSES.IN_PROGRESS]: [],
      [TASK_STATUSES.DONE]: []
    });
  };

  return (
    <div style={{ padding: '10px', position: 'relative', minHeight: '100vh' }}>
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        display: loading ? 'flex' : 'none',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999 // High z-index to appear above dialogs
      }}>
        <BarLoader loading={loading} />
      </div>

      <Grid container spacing={2}>
        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
          <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
          <Box sx={{ width: '100%', margin: '0' }}>
            <ModuleName moduleName="Task Management" pageName="Create" />
          </Box>
        </Grid>

      </Grid>
      <TextField 
        placeholder="Search by task code, title or assignee" 
        variant="outlined" 
        size="small" 
        value={searchQuery}
        onChange={(e) => handleSearch(e.target.value)}
        sx={{ 
          mb: 2, 
          ml: 2, 
          mt: 1, 
          width: "25%"
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
          endAdornment: searchQuery && (
            <InputAdornment position="end">
              <IconButton size="small" onClick={() => handleSearch('')}>
                <ClearIcon />
              </IconButton>
            </InputAdornment>
          )
        }}
      />

      <DragDropContext onDragEnd={onDragEnd}>
        <Grid container spacing={2}>
          {Object.keys(TASK_STATUSES).map((statusKey) => {
            const status = TASK_STATUSES[statusKey];
            const tasksToDisplay = searchQuery ? filteredTasks[status] : organizedTasks[status];
            
            return (
              <Grid item xs={4} key={status}>
                <Box sx={{ backgroundColor: "#D9F2F0", padding: 2, borderRadius: 2 }}>
                  <Typography variant="h6" sx={{ 
                    fontWeight: 600, 
                    backgroundColor: "#B0BEC5", 
                    padding: 1, 
                    borderRadius: 1, 
                    textAlign: "center",
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <span>{status}</span>
                    <span style={{ fontSize: '0.8em' }}>({tasksToDisplay?.length || 0})</span>
                  </Typography>
                  <Droppable droppableId={status}>
                    {(provided, snapshot) => (
                      <Box
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        sx={{
                          minHeight: 200,
                          padding: 2,
                          backgroundColor: snapshot.isDraggingOver ? "#C8E6C9" : "transparent",
                          transition: 'background-color 0.2s ease'
                        }}
                      >
                        {tasksToDisplay?.map((task, index) => (
                          <Draggable
                            key={task.id}
                            draggableId={task.id.toString()}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <Card
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                sx={{
                                  mb: 2,
                                  backgroundColor: snapshot.isDragging ? "#E3F2FD" : "white",
                                  transform: snapshot.isDragging ? "rotate(3deg)" : "none",
                                  position: 'relative'
                                }}
                                onClick={() => handleTaskClick(task, status)}
                              >
                                {task.comments && (
                                  task.comments.some(comment => 
                                    (!comment.is_read && comment.user_id === currentEmployeeId) ||
                                    (comment.replies && comment.replies.some(reply => 
                                      !reply.is_read && reply.user_id === currentEmployeeId
                                    ))
                                  ) && (
                                    <Box sx={{
                                      position: 'absolute',
                                      top: 8,
                                      right: 8,
                                      display: 'flex',
                                      gap: 1,
                                      alignItems: 'center'
                                    }}>
                                      <CommentIcon 
                                        color="primary"
                                        sx={{ 
                                          fontSize: 20,
                                          animation: `${pulse} 1.5s infinite`
                                        }} 
                                      />
                                      <Typography
                                        variant="caption"
                                        color="primary"
                                      >
                                        {task.comments.reduce((count, comment) => {
                                          const unreadComments = !comment.is_read && comment.user_id === currentEmployeeId ? 1 : 0;
                                          const unreadReplies = comment.replies ? 
                                            comment.replies.filter(reply => !reply.is_read && reply.user_id === currentEmployeeId).length : 0;
                                          return count + unreadComments + unreadReplies;
                                        }, 0)}
                                      </Typography>
                                    </Box>
                                  )
                                )}

                                <CardContent>
                                  <Box sx={{ mb: 5 }}>
                                    <Typography component="div" sx={{ fontWeight: 400 }}>
                                      {task.title}
                                    </Typography>
                                  </Box>

                                  <Box sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                  }}>
                                    <Typography variant="body2" color="text.secondary">
                                      Task Code: {task.task_code}
                                    </Typography>

                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <PersonIcon color="action" sx={{ fontSize: 18 }} />
                                      <Typography variant="body2" color="text.secondary">
                                        {task.assigned_to_name}
                                      </Typography>
                                    </Box>
                                  </Box>

                                  {task.comments && task.comments.length > 0 && (
                                    <Box sx={{
                                      mt: 2,
                                      p: 1,
                                      bgcolor: '#f5f5f5',
                                      borderRadius: 1,
                                      borderLeft: '3px solid #1976d2'
                                    }}>
                                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                                        Latest comment:
                                      </Typography>
                                      <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                                        {[...task.comments]
                                          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0]
                                          .comment}
                                      </Typography>
                                    </Box>
                                  )}
                                </CardContent>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </Box>
                    )}
                  </Droppable>
                  <Button
                    startIcon={<AddIcon />}
                    fullWidth
                    sx={{ mt: 1 }}
                    onClick={() => handleOpen(status)}
                    style={{ display: status === 'To-Do' ? 'flex' : 'none' }}
                    disabled={assignees.length === 0}  // Add this line
                  >
                    Create a Task
                  </Button>
                </Box>
              </Grid>
            );
          })}
        </Grid>
      </DragDropContext>

      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Create New Task</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Task Title"
            fullWidth
            value={newTask.title}
            onChange={(e) => setNewTask({ 
              ...newTask, 
              title: e.target.value.toUpperCase() 
            })}
            inputProps={{
              style: { textTransform: 'uppercase' }
            }}
          />
          <TextField
            margin="dense"
            label="Task Code"
            fullWidth
            value={newTask.task_code}
            InputProps={{
              readOnly: true,
            }}
            helperText="Auto-generated task code"
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={4}
            value={newTask.description}
            onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
          />
          <DropDown
            label="Assign To"
            name="assigned_to"
            required
            value={newTask.assigned_to}
            onChange={(e) => setNewTask({
              ...newTask,
              assigned_to: e.target.value
            })}
            options={assignees.length > 0 ? assignees : [{ id: '', value: '', label: 'No employees found' }]}
            helperText={assignees.length === 0 ? "No reporting employees found" : ""}
            fullWidth
            InputLabelProps={{ shrink: true }}
            disabled={assignees.length === 0}
          />

        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleCreateTask} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {selectedTask && (
        <Dialog
          open={taskDetailsOpen}
          onClose={handleTaskDetailsClose}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle
            sx={{
              borderBottom: '1px solid #e0e0e0',
              bgcolor: '#f8f9fa'
            }}
          >
            Task Details
          </DialogTitle>
          <DialogContent sx={{ p: 3 }}>
            <Box sx={{
              mb: 3,
              p: 2,
              bgcolor: '#fff',
              borderRadius: 1,
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}>
              <Box sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Task Title :
                </Typography>
                <Typography variant="subtitle2" sx={{ pl: 2 }}>
                  {selectedTask.title}
                </Typography>
              </Box>

              <Box sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                  Task Code:
                </Typography>
                <Typography variant="body1" sx={{ pl: 2 }}>
                  {selectedTask.task_code}
                </Typography>
              </Box>

              <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                  Assigned To:
                </Typography>
                <Typography variant="body1" sx={{ pl: 2 }}>
                  {selectedTask.assigned_to_name}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Description:
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    mt: 1,
                    p: 2,
                    bgcolor: '#f5f5f5',
                    borderRadius: 1,
                    minHeight: '60px'
                  }}
                >
                  {selectedTask.description || 'No description provided'}
                </Typography>
              </Box>
            </Box>

            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>Comments:</Typography>
            {selectedTask.comments && selectedTask.comments.length > 0 ? (
              <Box sx={{ mb: 2 }}>
                {selectedTask.comments.map((comment, index) => (
                  <Card key={index} sx={{
                    mb: 1,
                    p: 1,
                    bgcolor: '#f8f9fa',
                    borderLeft: '3px solid #1976d2'
                  }}>
                    <Box>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        {comment.comment}
                      </Typography>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        borderTop: '1px solid #e0e0e0',
                        pt: 1,
                        mt: 1
                      }}>
                        <Typography variant="caption" color="text.secondary">
                          Posted on: {new Date(comment.created_at).toLocaleString()}
                        </Typography>
                        <Button
                          size="small"
                          startIcon={<ReplyIcon />}
                          onClick={() => setReplyingTo(comment.comment_id)}
                          sx={{
                            minWidth: 'auto',
                            color: '#1976d2'
                          }}
                        >
                          Reply
                        </Button>
                      </Box>

                      {replyingTo === comment.comment_id && (
                        <Box sx={{ mt: 1 }}>
                          <TextField
                            size="small"
                            fullWidth
                            value={newReply}
                            onChange={(e) => setNewReply(e.target.value)}
                            placeholder="Type your reply..."
                            sx={{ mb: 1 }}
                          />
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                            <Button
                              size="small"
                              onClick={() => {
                                setReplyingTo(null);
                                setNewReply('');
                              }}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="small"
                              variant="contained"
                              onClick={() => handleAddReply(comment.comment_id)}
                            >
                              Submit
                            </Button>
                          </Box>
                        </Box>
                      )}

                      {comment.replies && comment.replies.length > 0 && (
                        <Box sx={{ ml: 3, mt: 1 }}>
                          {comment.replies.map((reply, replyIndex) => (
                            <Card key={replyIndex} sx={{
                              mb: 1,
                              p: 1,
                              bgcolor: '#ffffff',
                              borderLeft: '2px solid #90caf9'
                            }}>
                              <Typography variant="body2">{reply.comment}</Typography>
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                                Posted on: {new Date(reply.created_at).toLocaleString()}
                              </Typography>
                            </Card>
                          ))}
                        </Box>
                      )}
                    </Box>
                  </Card>
                ))}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">No comments yet</Typography>
            )}
            <TextField
              margin="dense"
              label="Add Comment"
              fullWidth
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
          </DialogContent>
          <DialogActions sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
            <Button onClick={handleTaskDetailsClose}>Close</Button>
            <Button onClick={handleAddComment} variant="contained">Add Comment</Button>
          </DialogActions>
        </Dialog>
      )}
    </div>
  );
};

export default KanbanBoard;
