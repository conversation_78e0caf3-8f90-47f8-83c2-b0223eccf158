const InsuranceType = require('../Models/InusranceType');

exports.getAllInsuranceTypes = async (req, res) => {
  try {
    const insuranceTypes = await InsuranceType.getAll();
    res.status(200).json(insuranceTypes);
  } catch (error) {
    console.error('Error fetching insurance types:', error); // Log the actual error for debugging
    res.status(500).json({ success: false, error: 'Error fetching insurance types' });
  }
};

exports.getInsuranceTypeById = async (req, res) => {
  const { id } = req.params;
  try {
    const insuranceType = await InsuranceType.getById(id);
    if (insuranceType) {
      res.status(200).json(insuranceType);
    } else {
      res.status(404).json({ success: false, message: 'Insurance type not found' });
    }
  } catch (error) {
    console.error(`Error fetching insurance type with ID ${id}:`, error); // Log the error with more details
    res.status(500).json({ success: false, error: 'Error fetching insurance type' });
  }
};
