const express = require('express');
const router = express.Router();
const reusableController = require('../Controllers/reusableController');
const path = require('path');

router.get('/getEmployeeOrAgentById/:id', reusableController.getEmployeeOrAgentById);

router.get('/getMasterProductsByQuotationId/:quotation_id', reusableController.getMasterProductsByQuotationId);

router.get('/getNomineeRelations', reusableController.getNomineeRelations);

router.get('/getAllUsers', reusableController.getAllUsers);

// Serve uploaded files statically
router.get('/view/:filename', (req, res) => {
    // Decode the filename parameter to get the original file path
    const filePath = decodeURIComponent(req.params.filename);
    console.log('Serving file from:', filePath);

    res.sendFile(filePath, (err) => {
        if (err) {
            console.error('Error serving file:', err);
            res.status(404).json({ message: 'File not found' });
        }
    });
});

module.exports = router;

