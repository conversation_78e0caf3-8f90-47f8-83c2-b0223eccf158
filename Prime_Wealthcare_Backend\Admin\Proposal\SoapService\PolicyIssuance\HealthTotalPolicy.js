const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../../knexfile');
const db = knex(knexConfig.development);
const { generateNomineeDetailsXML } = require('../../../../Reusable/xmlComponents')

// Environment variables
require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTION; // Use the SOAP action from the .env file
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

// Generate Member XML dynamically
const generateMemberXML = (membersData) => {
  return membersData
    .map(
      (membersData, index) => `
        <Member>
            <MemberId>${index + 1}</MemberId>
            <AbhaNo />
            <InsuredName>${`${membersData.first_name}${membersData.middle_name ? ` ${membersData.middle_name}` : ''}${membersData.last_name ? ` ${membersData.last_name}` : ''}`}</InsuredName>
            <InsuredDob>${membersData.date_of_birth}</InsuredDob>  
            <InsuredGender>${membersData.gender}</InsuredGender>
            <InsuredOccpn>${membersData.occupation}</InsuredOccpn>
            <CoverType>${membersData.coverType}</CoverType>
            <SumInsured>${membersData.sumInsured}</SumInsured>
            <DeductibleDiscount>0</DeductibleDiscount>
            <Relation>${membersData.relation}</Relation>
            <NomineeName>${membersData.nominee_name}</NomineeName>
            <NomineeRelation>${membersData.nominee_relation}</NomineeRelation>
            <AnualIncome />
            <Height>${membersData.height}</Height>
            <Weight>${membersData.weight}</Weight>
            <NomineeAge>${membersData.nominee_age}</NomineeAge>
            <AppointeeName />
            <AptRelWithominee />
          <MedicalLoading>${membersData.medicalLoading}</MedicalLoading>
                <PreExstDisease>${membersData.preExistingDisease}</PreExstDisease>
                <DiseaseMedicalHistoryList>
                  <DiseaseMedicalHistory>
                    <PreExistingDiseaseCode />
                    <MedicalHistoryDetail />
                  </DiseaseMedicalHistory>
                </DiseaseMedicalHistoryList>
               ${generateNomineeDetailsXML(membersData, index)}
        </Member>`
    )
    .join("");
};

// Generate SOAP body dynamically
const SOAP_BODY = (uid, customerData, proposalData, membersXML) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
         <!--Optional:-->
            <tem:Product>HealthTotal</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
  <Uid>${uid}</Uid>
 <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
  <SentToOutSourcePrint>0</SentToOutSourcePrint>
  <WinNo />
  <ApplicationNo />
  <PolicyHeader>
    <PolicyStartDate>${proposalData.start_date}</PolicyStartDate>
    <PolicyEndDate>${proposalData.end_date}</PolicyEndDate>
    <AgentCode>${proposalData.imf_code}</AgentCode>
    <BranchCode>${proposalData.branch_code}</BranchCode>
    <MajorClass>HTO</MajorClass>
    <ContractType>HTO</ContractType>
    <METHOD>CRT</METHOD>
    <PolicyIssueType>I</PolicyIssueType>
    <PolicyNo />
    <ClientID>${proposalData.client_id}</ClientID>
    <ReceiptNo>${proposalData.receipt_no}</ReceiptNo>
  </PolicyHeader>
  <POS_MISP>
    <Type />
    <PanNo />
  </POS_MISP>
  <Client>
    <ClientCategory />
    <ClientType>I</ClientType>
    <CreationType>C</CreationType>
    <Salutation>${proposalData.salutation}</Salutation>
    <FirstName>${customerData.first_name}</FirstName>
    <LastName>${customerData.last_name}</LastName>
    <DOB>${customerData.date_of_birth}</DOB>
    <Gender>${customerData.gender}</Gender>
    <MaritalStatus>${customerData.marital_status}</MaritalStatus>
    <Occupation>${customerData.occupation}</Occupation>
    <PANNo/>
    <GSTIN />
    <AadharNo />
    <CKYCNo>${proposalData.ckyc_number}</CKYCNo>
    <CKYCRefNo>${proposalData.proposal_Id}</CKYCRefNo>
    <EIANo />
    <Address1>
      <AddrLine1>${customerData.address_line1}</AddrLine1>
      <AddrLine2>${customerData.address_line2}D</AddrLine2>
      <AddrLine3 />
      <Landmark />
      <Pincode>${customerData.pincode}</Pincode>
      <City>${customerData.city}</City>
      <State>${customerData.state}</State>
      <Country>IND</Country>
      <AddressType>R</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo>${customerData.mobile}</MobileNo>
      <EmailAddr>${customerData.email}</EmailAddr>
    </Address1>
    <Address2>
      <AddrLine1>${customerData.address_line1}</AddrLine1>
      <AddrLine2>${customerData.address_line2}</AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${customerData.pincode}</Pincode>
      <City>${customerData.city}</City>
      <State>${customerData.state}</State>
      <Country>IND</Country>
      <AddressType>K</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo />
      <EmailAddr />
    </Address2>
    <VIPFlag>N</VIPFlag>
    <VIPCategory />
  </Client>
   <Receipt>
    <UniqueTranKey>${proposalData.WS_P_ID}</UniqueTranKey>
    <CheckType />
    <BSBCode />
    <TransactionDate>${proposalData.transaction_date}</TransactionDate>
    <ReceiptType>IVR</ReceiptType>
    <Amount>${proposalData.PremiumAmount}</Amount>
    <TCSAmount />
    <TranRefNo>${proposalData.PGID}</TranRefNo>
    <TranRefNoDate>${proposalData.transaction_date}</TranRefNoDate>
  </Receipt>
    <Risk>
    <PolicyType>${proposalData.policyType}</PolicyType>
    <Duration>${proposalData.duration}</Duration>
    <Installments>FULL</Installments>
    <PaymentType>CC</PaymentType>
    <IsFgEmployee>N</IsFgEmployee>
    <BranchReferenceID>76768</BranchReferenceID>
    <FGBankBranchStaffID>890422</FGBankBranchStaffID>
    <BankStaffID>********</BankStaffID>
    <BankCustomerID>87675</BankCustomerID>
    <BancaChannel>Retail sales to Saving Account Customers</BancaChannel>
    <PartnerRefNo />
    <PayorID />
    <PayerName />
    <BeneficiaryDetails>
        ${membersXML}
    </BeneficiaryDetails>
  </Risk>
</Root>]]></tem:XML>
        </tem:CreatePolicy>
    </soapenv:Body>
</soapenv:Envelope>`;

// Modified sendSOAPRequestHealthTotal function
const sendSOAPRequest = async (membersData, proposalData, proposalId, customerData) => {
  let requestBody;
  try {
    const headers = {
      "Content-Type": "text/xml; charset=utf-8",
      SOAPAction: SOAP_ACTION,
    };
    // Add logging statements
    // console.log("Customer Data:", customerData);
    // console.log("Proposal Data:", proposalData);
    // console.log("Members Data:", membersData);
    // Ensure membersData is an array
    const membersArray = Array.isArray(membersData) ? membersData : [membersData];

    // Generate unique UID and Member XML
    const uniqueUID = uuidv4();
    const membersXML = generateMemberXML(membersArray);

    // Generate SOAP body dynamically
    requestBody = SOAP_BODY(uniqueUID, customerData, proposalData, membersXML);
    console.log('SOAP Envelope:', requestBody);
    // Send the SOAP request
    const response = await axios.post(SOAP_API_URL, requestBody, { headers });
    console.log('SOAP Response:', response.data);

    // Save both request and response in single log entry
    await db('policy_logs').insert({
      quotation_number: proposalData.quotation_number,
      policy_type: 'HEALTH',
      request_payload: requestBody, // Raw XML request
      response_payload: response.data, // Raw XML response
      status: 'SUCCESS',
      error_message: null,
      created_at: db.fn.now()
    });

    // Parse the XML response
    const jsonResponse = await parseStringPromise(response.data);
    // console.log('Parsed JSON Response:', JSON.stringify(jsonResponse, null, 2));

    // Extract the CreatePolicyResult and parse the inner XML
    const createPolicyResult = jsonResponse['s:Envelope']['s:Body'][0]
    ['CreatePolicyResponse'][0]['CreatePolicyResult'][0];

    // Parse the inner XML string (remove XML entities before parsing)
    const decodedXml = createPolicyResult
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>');

    const innerXmlResult = await parseStringPromise(decodedXml);
    // console.log('Inner XML Result:', JSON.stringify(innerXmlResult, null, 2));

    const root = innerXmlResult.Root;

    // Extract all relevant information with null checks
    const formattedResponse = {
      client: {
        status: root?.Client?.[0]?.Status?.[0] || '',
        clientId: root?.Client?.[0]?.ClientId?.[0] || '',
        errorMessage: root?.Client?.[0]?.ErrorMessage?.[0] || ''
      },
      receipt: {
        status: root?.Receipt?.[0]?.Status?.[0] || '',
        receiptNo: root?.Receipt?.[0]?.ReceiptNo?.[0] || '',
        errorMessage: root?.Receipt?.[0]?.ErrorMessage?.[0] || ''
      },
      policy: {
        status: root?.Policy?.[0]?.Status?.[0] || '',
        policyNo: root?.Policy?.[0]?.PolicyNo?.[0] || '',
        message: root?.Policy?.[0]?.Message?.[0] || ''
      },
      application: {
        winNo: root?.Application?.[0]?.WinNo?.[0] || '',
        applicationNo: root?.Application?.[0]?.ApplicationNo?.[0] || ''
      }
    };
    const clientId = root.Client?.[0]?.ClientId?.[0] || null;
    const receiptNo = root.Receipt?.[0]?.ReceiptNo?.[0] || null;

    // Check if the values already exist in the database
    const existingProposal = await db('proposals')
      .where('ProposalNumber', proposalData.proposal_number)
      .select('client_id', 'receipt_no')
      .first();
    // Update the database only if the values are not already present and are not empty
    if (existingProposal) {
      const { client_id, receipt_no } = existingProposal;
      // Only update if the new values are not empty and different from existing values
      if ((clientId && client_id !== clientId) || (receiptNo && receipt_no !== receiptNo)) {
        await db('proposals')
          .where('ProposalNumber', proposalData.proposal_number)
          .update({
            client_id: clientId,
            receipt_no: receiptNo
          });
      } 
    }
    // console.log('Formatted Response:', formattedResponse);

    // Check for receipt failure
    if (formattedResponse.receipt.status === 'Fail') {
      throw new Error(`Receipt creation failed: ${formattedResponse.receipt.errorMessage}`);
    }

    // Update database with success information
    try {
      await db('proposals')
        .where('ProposalNumber', proposalData.proposal_number)
        .update({
          status: 'SUCCESS',
          policy_number: formattedResponse.policy.policyNo,
          win_no: formattedResponse.application.winNo,
          application_no: formattedResponse.application.applicationNo,
          // Only update client_id and receipt_no if they are not already present
          client_id: existingProposal?.client_id || formattedResponse.client.clientId,
          receipt_no: existingProposal?.receipt_no || formattedResponse.receipt.receiptNo,
          policy_issue_date: new Date().toISOString()
        });
    } catch (dbError) {
      console.error("Database update error:", dbError);
    }

    return formattedResponse;
  } catch (error) {
    // Save error log with request and error response
    await db('policy_logs').insert({
      quotation_number: proposalData.quotation_number,
      policy_type: 'HEALTH',
      request_payload: requestBody, // Original XML request
      response_payload: error.response?.data || error.message,
      status: 'ERROR',
      error_message: error.message,
      created_at: db.fn.now()
    });

    const errorResponse = {
      success: false,
      error: {
        message: error.message,
        status: error.response?.status || "No status",
        data: error.response?.data || "No data",
        timestamp: new Date().toISOString(),
        type: error.name || "Error"
      },
      client: {
        message: ''
      }
    };

    // Add the actual response data if available
    if (error.response?.data) {
      errorResponse.rawResponse = error.response.data;
    }

    console.error("SOAP Request Error Details:", errorResponse);
    throw errorResponse;
  }
};

module.exports = { sendSOAPRequest };
