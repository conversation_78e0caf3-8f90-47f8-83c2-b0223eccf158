const knexConfig = require('../../../knexfile');
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

const create = async (data) => {
    try {
        const result = await db('page_rights').insert({
            user_id: data.user_id,
            module_name: data.module_name, 
            page_name: data.page_name,
            can_add: data.can_add,
            can_edit: data.can_edit,
            can_view: data.can_view,
            can_delete: data.can_delete,
            created_by: data.created_by,
            updated_by: data.updated_by
        });
        return result; 
    } catch (error) {
        throw error;
    }
};

const findByRoleId = async (roleId) => {
    try {
        return await db('page_rights')
            .where('role_id', roleId)
            .where('status', true)
            .select('*');
    } catch (error) {
        throw error;
    }
};

const updateRights = async (userId, moduleData, createdBy) => {
    try {
        
        // Check in employee_personal_info and agents tables
        const userExists = await db('employee_personal_info')
            .where('user_id', userId)
            .first()
            .union([
                db('agents')
                .where('agent_id', userId)
            ]);

        if (!userExists || userExists.length === 0) {
            throw new Error('User not found');
        }
        // Delete existing rights for this user
        await db('page_rights')
            .where('user_id', userId)
            .del();

        // Prepare the data for batch insert
        const rightsToInsert = [];
        
        moduleData.forEach(module => {
            module.pages.forEach(page => {
                rightsToInsert.push({
                    user_id: userId,
                    module_name: module.title,
                    page_name: page.page_name,
                    can_add: page.permissions.add,
                    can_edit: page.permissions.edit,
                    can_view: page.permissions.view,
                    can_delete: page.permissions.delete,
                    created_by: createdBy,
                    updated_by: createdBy,
                    status: true
                });
            });
        });

        return await db('page_rights').insert(rightsToInsert);
    } catch (error) {
        console.error('Error in updateRights:', error);
        throw error;
    }
};

const checkUserAccess = async (userId, moduleName, pageName) => {
    try {
        return await db('page_rights')
            .where({
                user_id: userId,
                module_name: moduleName,
                page_name: pageName,
                status: true
            })
            .first();
    } catch (error) {
        throw error;
    }
};

const findByUserId = async (userId) => {
    try {
        // Check in both employee_personal_info and agents tables
        const userExists = await db('employee_personal_info')
            .where('user_id', userId)
            .first()
            .union([
                db('agents')
                .where('agent_id', userId)
            ]);

        if (!userExists || userExists.length === 0) {
            throw new Error('User not found');
        }

        return await db('page_rights')
        .where('user_id', userId)
        .where('status', true)
        .select('*');
} catch (error) {
    throw error;
}
};

// Get all active users with their permissions
const getAllUserPermissions = async () => {
    try {
        return await db('page_rights')
            .join('user_login', 'page_rights.user_id', 'user_login.user_id')
            .where('page_rights.status', true)
            .where('user_login.is_active', true)
            .select('page_rights.*', 'user_login.user_type');
    } catch (error) {
        throw error;
    }
};

const getAllUsers = async () => {
    try {
        const users = await db('user_login')
            .where('is_active', true)
            .select('user_id', 'user_type');
        
        return users;
    } catch (error) {
        console.error('Error in getAllUsers model:', error);
        throw error;
    }
};

const getUserById = async (userId) => {
    try {
        const user = await db('user_login')
            .where('user_id', userId)
            .where('is_active', true)
            .first();
        return user;
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findByRoleId,
    updateRights,
    checkUserAccess,
    findByUserId,
    getAllUserPermissions,
    getAllUsers,
    getUserById
};