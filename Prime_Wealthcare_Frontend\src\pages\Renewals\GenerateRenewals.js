import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Grid, Box, Button, IconButton } from '@mui/material';
import { Save, Cancel } from '@mui/icons-material';
import Dropdown from '../../components/table/DropDown';
import BarLoader from '../../components/BarLoader';
import ModuleName from '../../components/table/ModuleName';
import { fetchInsuranceCompanies } from '../../redux/actions/action';
import CustomFileUpload from "../../components/CustomFileUpload";
import * as XLSX from 'xlsx';
import { toast } from 'react-toastify';
import {
    selectRenewalsLoading,
    selectRenewalsError,
    selectRenewalsSuccess,
    selectRenewalsTotalRecords
} from '../../redux/slices/Renewals/Renewals_mappingSlice';
import { saveRenewalsMapping } from '../../redux/actions/action';

const GenerateRenewals = () => {
    const navigate = useNavigate();
    const [isPolicyCompleted, setIsPolicyCompleted] = useState(false);
    const [formData, setFormData] = useState({
        insurance_company: ''
    });
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer?.insuranceCompanies || []); // For insurance company dropdown
    const [formErrors, setFormErrors] = useState({});
    const [excelData, setExcelData] = useState([]);
    const [uploadError, setUploadError] = useState('');
    const dispatch = useDispatch();
    const loading = useSelector(selectRenewalsLoading);
    const error = useSelector(selectRenewalsError);
    const success = useSelector(selectRenewalsSuccess);
    const totalRecords = useSelector(selectRenewalsTotalRecords);

    const requiredColumns = [
        'customer_name',
        'customer_number',
        'imf_branch_name',
        'old_policy_number',
        'expiry_date',
        'month',
        'insurance_company',
        'main_product',
        'product',
        'sub_product',
        'number_of_members',
        'premium',
        'agent_code',
        'agent_name',
        'renewal_status'
    ];

    useEffect(() => {
        dispatch(fetchInsuranceCompanies());
    }, [dispatch]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const validateExcelData = (data) => {
        if (!Array.isArray(data) || data.length === 0) {
            throw new Error('Excel file is empty');
        }

        const headerRow = Object.keys(data[0]);
        const missingColumns = requiredColumns.filter(
            col => !headerRow.includes(col)
        );

        if (missingColumns.length > 0) {
            throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
        }

        return true;
    };

    const handleFileChange = async (event) => {
        try {
            setUploadError('');

            // Get the file from event
            const file = event.target.files[0];

            if (!file) {
                throw new Error('Please select a file');
            }

            // Validate file type
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];
            if (!validTypes.includes(file.type)) {
                throw new Error('Please upload an Excel file (.xlsx or .xls)');
            }

            if (!formData.insurance_company) {
                throw new Error('Please select an insurance company first');
            }

            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];

                    // Convert headers to match your column names
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                        header: [
                            'customer_name',
                            'customer_number',
                            'imf_branch_name',
                            'old_policy_number',
                            'expiry_date',
                            'month',
                            'insurance_company',
                            'main_product',
                            'product',
                            'sub_product',
                            'number_of_members',
                            'premium',
                            'agent_code',
                            'agent_name',
                            'renewal_status'
                        ],
                        range: 1  // Skip header row
                    });

                    validateExcelData(jsonData);

                    // Map the data to match backend expectations
                    const mappedData = {
                        insurance_company_id: formData.insurance_company,
                        renewals: jsonData.map(row => ({
                            customer_name: row.customer_name?.trim(),
                            customer_number: row.customer_number?.trim(),
                            imf_branch_name: row.imf_branch_name?.trim(),
                            old_policy_number: row.old_policy_number?.trim(),
                            expiry_date: row.expiry_date,
                            month: row.month?.trim(),
                            insurance_company: row.insurance_company?.trim(),
                            main_product: row.main_product?.trim(),
                            product: row.product?.trim(),
                            sub_product: row.sub_product?.trim(),
                            number_of_members: Number(row.number_of_members) || 0,
                            premium: Number(row.premium) || 0,
                            agent_code: row.agent_code?.trim(),
                            agent_name: row.agent_name?.trim(),
                            renewal_status: row.renewal_status?.trim()
                        }))
                    };
                    // Validate mapped data
                    if (mappedData.renewals.some(row => !row.customer_name || !row.customer_number)) {
                        throw new Error('Invalid data: Customer name and number are required');
                    }

                    setExcelData(mappedData.renewals);
                    toast.success(`Successfully processed ${mappedData.renewals.length} records`);

                } catch (error) {
                    console.error('Excel processing error:', error);
                    setUploadError(error.message);
                    toast.error(error.message);
                }
            };

            reader.readAsArrayBuffer(file);

        } catch (error) {
            console.error('File upload error:', error);
            setUploadError(error.message);
            toast.error(error.message);
        }
    };

    const handleSave = async () => {
        try {
            if (!formData.insurance_company) {
                throw new Error('Please select an insurance company');
            }
            if (excelData.length === 0) {
                throw new Error('Please upload an Excel file first');
            }

            // Create submit data object
            const submitData = {
                insurance_company_id: formData.insurance_company,
                renewals: excelData
            };

            const response = await dispatch(saveRenewalsMapping(submitData)).unwrap();

            if (response.success) {
                toast.success(`Successfully saved ${response.data.total_records} records`);
                navigate(-1);
            }

        } catch (error) {
            console.error('Save error:', error);
            toast.error(error.message);
        }
    };

    const handleCancel = () => {
        navigate(-1);
    };

    return (
        <Box sx={{
            padding: { xs: '0 5px 5px', md: '0 40px 40px' },
            width: '100%'
        }}>
            <BarLoader loading={loading} />
            {!loading && (
                <form encType="multipart/form-data" style={{ width: '100%' }}>
                    <Grid
                        container
                        sx={{
                            position: 'sticky',
                            top: { xs: '140px', sm: '140px', md: '164px', lg: '164px' },
                            zIndex: 101,
                            backgroundColor: 'white',
                            borderBottom: '2px solid #E0E0E0',
                            padding: '10px 0',
                            display: "flex"
                        }}
                    >
                        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{
                                    width: '20px',
                                    marginLeft: '20px',
                                    backgroundColor: 'green'
                                }}
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <ModuleName moduleName="Renewals" pageName="Renewal Mapping" />
                            </Box>
                        </Grid>

                        <Grid item xs={4} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                                {!isPolicyCompleted &&
                                    <IconButton
                                        onClick={handleSave}
                                        sx={{ color: 'green', mx: 0.5 }}
                                    >
                                        <Save />
                                    </IconButton>
                                }
                                <IconButton
                                    onClick={handleCancel}
                                    sx={{ color: 'red', mx: 0.5 }}
                                >
                                    <Cancel />
                                </IconButton>
                            </Box>
                            <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
                                {!isPolicyCompleted &&
                                    <Button
                                        onClick={handleSave}
                                        variant="outlined"
                                        size="large"
                                        sx={{
                                            width: '100%',
                                            mx: 0.5,
                                            color: 'green',
                                            borderColor: 'green',
                                            textTransform: 'none',
                                            textWrap: 'nowrap'
                                        }}
                                    >
                                        Save
                                    </Button>
                                }
                                <Button
                                    onClick={handleCancel}
                                    variant="outlined"
                                    size="small"
                                    sx={{
                                        maxWidth: '100px',
                                        width: '100%',
                                        mx: 1.5,
                                        color: 'red',
                                        borderColor: 'red',
                                        textTransform: 'none'
                                    }}
                                >
                                    Cancel
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>

                    <Grid container spacing={2} sx={{ mt: 2, px: 2 }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Insurance Company"
                                name="insurance_company"
                                value={formData.insurance_company}
                                options={Array.isArray(insuranceCompanies) ? insuranceCompanies.map(company => ({
                                    value: company?.id,
                                    label: company?.insurance_company_name
                                })) : []}
                                onChange={handleChange}
                                fullWidth
                                required
                                error={Boolean(formErrors.insurance_company)}
                                helperText={formErrors.insurance_company}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomFileUpload
                                label="Upload Excel File"
                                name="upload_file"
                                accept=".xlsx,.xls"
                                onChange={handleFileChange}
                                isRequired
                                error={Boolean(uploadError)}
                                helperText={uploadError || 'Please upload an Excel file'}
                            />
                        </Grid>
                    </Grid>
                </form>
            )}
        </Box>
    );
};

export default GenerateRenewals;