import React, { useState, useEffect } from 'react';
import { <PERSON>rid, Box, Button, Typography, Checkbox, FormControlLabel, AppBar, Toolbar } from '@mui/material';
import AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';
import VpnKeyOutlinedIcon from '@mui/icons-material/VpnKeyOutlined';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import CustomTextField from '../../components/CustomTextField';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { login, validateToken } from '../../redux/actions/action';
import InputAdornment from '@mui/material/InputAdornment';
import { toast } from 'react-toastify';
import { setUserSession ,clearUserSession} from '../../utils/storage';


const LoginPage = () => {

  const [userId, setUserId] = useState('');
  const [password, setPassword] = useState('');
  const [keepLoggedIn, setKeepLoggedIn] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
 

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [errors, setErrors] = useState({
    userId: '',
    password: ''
  });

  useEffect(() => {
    const checkToken = async () => {
        // First check sessionStorage for current tab
        let token = sessionStorage.getItem('token');
        
        // If no token in sessionStorage, check localStorage
        if (!token) {
            token = localStorage.getItem('token');
            // If found in localStorage, also set in sessionStorage
            if (token) {
                sessionStorage.setItem('token', token);
            }
        }

        if (token) {
            try {
                const resultAction = await dispatch(validateToken(token));
                if (validateToken.fulfilled.match(resultAction)) {
                    navigate('/dashboard/crm-dashboard');
                }
            } catch (error) {
                // Clear storage if validation fails
                clearUserSession();
            }
        }
    };

    checkToken();
  }, [dispatch, navigate]);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      userId: '',
      password: ''
    };

    if (!userId.trim()) {
      newErrors.userId = 'Username is required';
      isValid = false;
    }

    if (!password.trim()) {
      newErrors.password = 'Password is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }
    try {
      const resultAction = await dispatch(login({ userId, password, keepLoggedIn }));
      
      if (login.fulfilled.match(resultAction)) {
        const { firstLogin, message, token, status } = resultAction.payload;
        
        // Check if user status is active (status = 1)
        if (status === 0) {
          toast.error('Your account is inactive. Please contact the administrator.');
          return;
        }
        
        if (firstLogin) {
          toast.info(message || "First-time login detected. Please change your password.");
          navigate('/new-password');
          return;
        }
        
        if (!token) {
          toast.error('Authentication failed: No token received');
          return;
        }
  
        document.dispatchEvent(new MouseEvent('mousemove'));
        
        // Always store in both storages for cross-tab functionality
        sessionStorage.setItem('token', token);
        localStorage.setItem('token', token);
        
        // Store the keepLoggedIn preference
        localStorage.setItem('keepLoggedIn', keepLoggedIn.toString());

        toast.success('Login successful! Redirecting to dashboard...');
        navigate('/dashboard/crm-dashboard');
      }
    } catch (error) {
      // Handle specific server responses
      if (error.response?.status === 403) {
        toast.error('Your account is inactive. Please contact the administrator.');
      } else {
        console.error('Login error:', error);
        toast.error('Invalid username or password. Please try again.');
      }
    }
  };

  // Add window unload listener to handle browser close
  useEffect(() => {
    const handleUnload = () => {
        const keepLoggedIn = localStorage.getItem('keepLoggedIn') === 'true';
        if (!keepLoggedIn) {
            localStorage.removeItem('token');
            localStorage.removeItem('keepLoggedIn');
        }
    };

    window.addEventListener('unload', handleUnload);
    return () => window.removeEventListener('unload', handleUnload);
  }, []);

  const handleForgotPasswordClick = () => {
    navigate('/forgot-password');
  };

  const toggleShowPassword = () => {
    setShowPassword(prev => !prev);
  };

  return (
    <Grid container style={{ height: '100vh', overflow: 'hidden', position: 'relative' }}>
      <AppBar 
        position="absolute" 
        color="transparent" 
        elevation={0}
        sx={{ zIndex: 1 }}
      >
        <Toolbar sx={{ justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            onClick={() => navigate('/quick-quotation')}
            sx={{
              backgroundColor: '#528A7E',
              color: 'white',
              '&:hover': {
                backgroundColor: '#397c63',
              },
              textTransform: 'none',
              fontWeight: 'bold',
              px: 3,
            }}
          >
            Quick Quotation
          </Button>
        </Toolbar>
      </AppBar>

      <Grid item xs={12} md={6} style={{ backgroundImage: `url(/background.png)`, backgroundSize: 'cover', backgroundPosition: 'left' }}>
      </Grid>

      <Grid
        item
        xs={12}
        md={6}
        display="flex"
        alignItems="center"
        justifyContent="center"
        style={{ backgroundColor: '#DDF2ED', padding: '12px', height: '100%' }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: 800,
            padding: 4,
            borderRadius: 2,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'center', pt: { xs: 2, md: 0 } }}>
            <img src="/logo1.png" alt="Logo" style={{ width: 250 }} />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <img src="/illustration.png" alt="Illustration" style={{ width: '50%' }} />
          </Box>

          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom>Welcome Back!</Typography>
            <Typography variant="body2" gutterBottom>Please login to view your dashboard</Typography>
          </Box>

          {/* Username and Password Fields */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
            <CustomTextField
              label="Enter User Name"
              type="text"
              value={userId}
              // onChange={(e) => setUserId(e.target.value)}
              onChange={(e) => {
                setUserId(e.target.value);
                if (e.target.value.trim()) {  // Clear error if field has value
                  setErrors(prev => ({ ...prev, userId: '' }));
                }
              }}
              margin="normal"
              icon={AccountCircleOutlinedIcon}
              sx={{ width: '100%', maxWidth: '400px' }}
              error={!!errors.userId}
              helperText={errors.userId}

            />

            <CustomTextField
              label="Enter Password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onFocus={() => setPasswordFocused(true)}
              onBlur={() => setPasswordFocused(false)}
              icon={passwordFocused || password ? (showPassword ? Visibility : VpnKeyOutlinedIcon) : VpnKeyOutlinedIcon}
              error={!!errors.password}
              helperText={errors.password}
              onChange={(e) => {
                setPassword(e.target.value);
                if (e.target.value.trim()) {  // Clear error if field has value
                  setErrors(prev => ({ ...prev, password: '' }));
                }
              }}
              margin="normal"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" onClick={toggleShowPassword} style={{ cursor: 'pointer' }}>
                    {showPassword ? <VisibilityOff /> : <Visibility />} {/* Eye icon toggling */}
                  </InputAdornment>
                ),
              }}
              sx={{ width: '100%', maxWidth: '400px' }}
            />
          </Box>

          {/* Keep Logged In Option */}
          <Box sx={{ display: 'flex', justifyContent: 'space-evenly', width: '100%', alignItems: 'center' }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={keepLoggedIn}
                  onChange={(e) => setKeepLoggedIn(e.target.checked)}
                />
              }
              label="Keep me logged in"
              sx={{ mr: 1, color: 'green', fontSize: '0.875rem' }}
            />
            <Typography
              variant="body2"
              color="primary"
              sx={{ cursor: 'pointer' }}
              onClick={handleForgotPasswordClick}
            >
              Forgot Password?
            </Typography>
          </Box>

          {/* Submit Button */}
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', pt: 3 }}>
            <Button
              variant="contained"
              sx={{
                backgroundColor: '#528A7E',
                color: 'white',
                width: '100%',
                maxWidth: '400px',
                '&:hover': {
                  backgroundColor: '#397c63',
                },
              }}
              onClick={handleLogin}
            >
              Login
            </Button>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default LoginPage;
