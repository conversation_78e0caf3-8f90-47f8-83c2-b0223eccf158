const express = require('express');
const subProductAgeSumController = require('../Controllers/subProductAgeSumControllers');
const router = express.Router();

// Route to create a new sub products
router.post('/', subProductAgeSumController.create);

// Route to update a sub products by ID
router.put('/:id', subProductAgeSumController.update);

// Route to delete a sub products by ID
router.delete('/:id', subProductAgeSumController.delete);

module.exports = router;
