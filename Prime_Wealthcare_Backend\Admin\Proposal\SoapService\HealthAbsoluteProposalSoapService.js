const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../knexfile');
const db = knex(knexConfig.development);
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');


// Environment variables
require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTIONPROPOSAL; // Use the SOAP Action from the .env fileconst VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;



// Generate Member XML dynamically
const generateMemberXML = (membersData) => {
  return membersData
    .map(
      (membersData, index) => `
        <Member>
            <MemberId>${index + 1}</MemberId>
            <AbhaNo />
            <InsuredName>${`${membersData.first_name}${membersData.middle_name ? ` ${membersData.middle_name}` : ''}${membersData.last_name ? ` ${membersData.last_name}` : ''}`}</InsuredName>
            <InsuredDob>${membersData.date_of_birth}</InsuredDob>  
            <InsuredGender>${membersData.gender}</InsuredGender>
            <InsuredOccpn>${membersData.occupation}</InsuredOccpn>
            <CoverType>${membersData.coverType}</CoverType>
            <SumInsured>${membersData.sumInsured}</SumInsured>
            <DeductibleDiscount />
            <Relation>${membersData.relation}</Relation>
            <NomineeName>${membersData.nominee_name}</NomineeName>
            <NomineeRelation>${membersData.nominee_relation}</NomineeRelation>
            <AnualIncome />
            <Height>${membersData.height}</Height>
            <Weight>${membersData.weight}</Weight>
            <NomineeAge>${membersData.nominee_age}</NomineeAge>
            <AppointeeName />
        <AptRelWithNominee />
        <Smoking>${membersData.isSmoking}</Smoking>
        <Tobacco>${membersData.isTobacco}</Tobacco>
        <IsGoodHealth>Y</IsGoodHealth>
        <IsExistingAbsolutePolicy>N</IsExistingAbsolutePolicy>
        <AdditionalInformation /> 
         ${generateNomineeDetailsXML(membersData, index)}    
        </Member>`
    )
    .join("");
};

// Generate SOAP body dynamically
const SOAP_BODY = (uid, customerData, proposalData, membersXML) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:HealthPreCRTValidate>
         <!--Optional:-->
            <tem:Product>HealthAbsolute</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
  <Uid>${uid}</Uid>
 <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
  <SentToOutSourcePrint>0</SentToOutSourcePrint>
  <WinNo />
  <ApplicationNo />
  <PolicyHeader>
    <PolicyStartDate>${proposalData.start_date}</PolicyStartDate>
    <PolicyEndDate>${proposalData.end_date}</PolicyEndDate>
    <AgentCode>>${proposalData.imf_code}</AgentCode>
    <BranchCode>>${proposalData.branch_code}</BranchCode>
    <MajorClass>FHA</MajorClass>
    <ContractType>FHA</ContractType>
    <METHOD>CRT</METHOD>
    <PolicyIssueType>C</PolicyIssueType>
    <PolicyNo />
     <ClientID>${proposalData.client_id}</ClientID>
    <ReceiptNo>${proposalData.receipt_no}</ReceiptNo>
  </PolicyHeader>
  <POS_MISP>
    <Type />
    <PanNo />
  </POS_MISP>
  <Client>
    <ClientCategory />
    <ClientType>I</ClientType>
    <CreationType>C</CreationType>
    <Salutation>${proposalData.salutation}</Salutation>
    <FirstName>${customerData.first_name}</FirstName>
    <LastName>${customerData.last_name}</LastName>
    <DOB>${customerData.date_of_birth}</DOB>
    <Gender>${customerData.gender}</Gender>
    <MaritalStatus>${customerData.marital_status}</MaritalStatus>
    <Occupation>${customerData.occupation}</Occupation>
    <PANNo>${customerData.pan_number}</PANNo>
    <GSTIN />
    <AadharNo />
    <CKYCNo>${proposalData.ckyc_number}</CKYCNo>
    <CKYCRefNo>${proposalData.proposal_Id}</CKYCRefNo>
    <EIANo />
    <Address1>
      <AddrLine1>${customerData.address_line1}</AddrLine1>
      <AddrLine2>${customerData.address_line2}</AddrLine2>
      <AddrLine3 />
      <Landmark />
      <Pincode>${customerData.pincode}</Pincode>
      <City>${customerData.city}</City>
      <State>${customerData.state}</State>
      <Country>IND</Country>
      <AddressType>R</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo>${customerData.mobile}</MobileNo>
      <EmailAddr>${customerData.email}</EmailAddr>
    </Address1>
    <Address2>
      <AddrLine1>${customerData.address_line1}</AddrLine1>
      <AddrLine2>${customerData.address_line2}</AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${customerData.pincode}</Pincode>
      <City>${customerData.city}</City>
      <State>${customerData.state}</State>
      <Country>IND</Country>
      <AddressType>K</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo />
      <EmailAddr />
    </Address2>
    <VIPFlag>N</VIPFlag>
    <VIPCategory />
  </Client>
  <Receipt>
    <UniqueTranKey>${proposalData.WS_P_ID}</UniqueTranKey>
    <CheckType />
    <BSBCode />
    <TransactionDate>${proposalData.transaction_date}</TransactionDate>
    <ReceiptType>IVR</ReceiptType>
    <Amount>${proposalData.PremiumAmount}</Amount>
    <TCSAmount />
    <TranRefNo>${proposalData.PGID}</TranRefNo>
    <TranRefNoDate>${proposalData.transaction_date}</TranRefNoDate>
  </Receipt>
    <Risk>
    <eNach>N</eNach>
    <PolicyType>${proposalData.policyType}</PolicyType>
    <Duration>${proposalData.duration}</Duration>
    <Installments>FULL</Installments>
    <PaymentType>CC</PaymentType>
    <IsFgEmployee>N</IsFgEmployee>
    <BranchReferenceID />
    <FGBankBranchStaffID />
    <BankStaffID />
    <BankCustomerID />
    <BancaChannel />
    <PartnerRefNo />
    <PayorID />
    <PayerName />
    <BeneficiaryDetails>
        ${membersXML}
    </BeneficiaryDetails>
  </Risk>
</Root>]]></tem:XML>
        </tem:HealthPreCRTValidate>
    </soapenv:Body>
</soapenv:Envelope>
`;


// Modified sendSOAPRequestHealthTotal function
const sendSOAPRequest = async (membersData, proposalData, proposalId, customerData) => {
  try {
    const headers = {
      "Content-Type": "text/xml; charset=utf-8",
      SOAPAction: SOAP_ACTION,
    };
    // Add logging statements
    // console.log("Customer Data:", customerData);
    // console.log("Proposal Data:", proposalData);
    // console.log("Members Data:", membersData);
    // Ensure membersData is an array
    const membersArray = Array.isArray(membersData) ? membersData : [membersData];

    // Generate unique UID and Member XML
    const uniqueUID = uuidv4();
    const membersXML = generateMemberXML(membersArray);

    // Generate SOAP body dynamically
    const requestBody = SOAP_BODY(uniqueUID, customerData, proposalData, membersXML);
    console.log('SOAP Envelope:', requestBody);
    // Send the SOAP request
    const response = await axios.post(SOAP_API_URL, requestBody, { headers });

    // Parse the XML response
    const jsonResponse = await parseStringPromise(response.data);

    // Extract the specific response data
    const healthPreCRTValidateResult = jsonResponse['s:Envelope']['s:Body'][0]
    ['HealthPreCRTValidateResponse'][0]['HealthPreCRTValidateResult'][0];

    // Parse the nested XML string in the result
    const innerXmlResult = await parseStringPromise(healthPreCRTValidateResult);
    const root = innerXmlResult.Root;

    // Check for validation error response
    if (root.Status && root.Status[0] === 'Fail') {
      throw new Error(root.ValidationError[0] || 'Validation failed');
    }
    // Check for CKYC validation error
    if (root.Status?.[0] === 'Fail' && root.ValidationError?.[0]?.includes('CKYC')) {
      // Update database with CKYC Pending status
      await db('proposals')
        .where('ProposalNumber', proposalData.proposal_number)
        .update({
          status: 'CKYC Pending'
        });

      throw new Error('CKYC validation failed: KYC not completed');
    }
    // Extract all relevant information
    const formattedResponse = {
      client: {
        status: root.Client[0].Status[0],
        clientId: root.Client[0].ClientId[0],
        errorMessage: root.Client[0].ErrorMessage[0]
      },
      receipt: {
        status: root.Receipt[0].Status[0],
        receiptNo: root.Receipt[0].ReceiptNo[0],
        errorMessage: root.Receipt[0].ErrorMessage[0]
      },
      policy: {
        status: root.Policy[0].Status[0],
        policyNo: root.Policy[0].PolicyNo[0],
        message: root.Policy[0].Message[0]
      },
      application: {
        winNo: root.Application[0].WinNo[0],
        applicationNo: root.Application[0].ApplicationNo[0]
      }
    };

    console.log('Formatted Response:', formattedResponse);

    // Update database with success information
    try {
      await db('proposals')
        .where('ProposalNumber', proposalData.proposal_number)
        .update({
          status: 'SUCCESS',
          policy_number: formattedResponse.policy.policyNo,
          win_no: formattedResponse.application.winNo,
          application_no: formattedResponse.application.applicationNo,
          client_id: formattedResponse.client.clientId,
          receipt_no: formattedResponse.receipt.receiptNo
        });
    } catch (dbError) {
      console.error("Database update error:", dbError);
    }

    return formattedResponse;
  } catch (error) {
    const errorResponse = {
      success: false,
      error: {
        message: error.message,
        status: error.response?.status || "No status",
        data: error.response?.data || "No data",
        timestamp: new Date().toISOString(),
        type: error.name || "Error"
      },
      client: {
        message: '' // Initialize client object with empty message
      }
    };
    // Always update status to 'CKYC Pending' for CKYC-related errors
    // Always update status to 'CKYC Pending' for CKYC-related errors
    if (error.message?.includes('CKYC') ||
      error.response?.data?.includes('CKYC') ||
      error.response?.data?.includes('KYC not completed')) {
      errorResponse.status = 'CKYC Pending';
      errorResponse.client.message = 'KYC verification pending. Please complete your KYC process.';
      try {
        await db('proposals')
          .where('ProposalNumber', proposalData.proposal_number)
          .update({
            status: 'CKYC Pending'
          });
      } catch (dbError) {
        console.error("Database update error:", dbError);
      }

    }
    console.error("SOAP Request Error Details:", errorResponse);
    throw errorResponse;
  }
};

module.exports = { sendSOAPRequest };
