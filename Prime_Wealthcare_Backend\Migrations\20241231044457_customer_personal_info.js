exports.up = function (knex) {
    return knex.schema.createTable('customer_personal_info', function (table) {
        table.increments('id').primary();
        table.string('customer_category').notNullable();
        table.string('first_name').nullable();
        table.string('middle_name').nullable();
        table.string('last_name').nullable();
        table.string('company_name').nullable();
        table.string('mobile').notNullable();
        table.string('whatsApp_number').notNullable();
        table.string('email').notNullable();
        // Foreign key from pick_list for gender, education, marital status
        table.integer('relation_id').unsigned().references('id').inTable('pick_list').nullable();
        table.integer('gender_id').unsigned().references('id').inTable('pick_list').nullable();
        table.date('date_of_birth').nullable();
        table.integer('marital_status_id').unsigned().references('id').inTable('pick_list').nullable();
        table.timestamp('marriage_date').nullable();
        // Personal information
        table.string('aadhar_number').nullable();
        table.string('pan_number').nullable();
        table.string('group_code').nullable();
        table.boolean('head_name').notNullable();
        //table.string('group_code').notNullable();
        table.string('comment_history').nullable();
        table.string('assigned_to').notNullable();
        table.string('agent_id').nullable();
        table.string('customer_type').nullable();
        table.integer('occupation').unsigned().references('id').inTable('pick_list').nullable();;
        table.string('height').nullable(); // Assuming height in meters or similar
        table.string('weight').nullable(); // Assuming weight in kilograms or similar
        table.string('isSmoking').nullable(); // Boolean for smoking status
        table.string('isTobacco').nullable(); // Boolean for tobacco usage
        table.string('smokingPerDay', 255).nullable(); // Number of cigarettes per day
        table.string('tobaccoPerDay', 255).nullable(); // Amount of tobacco per day
        table.string('diseaseDetails', 255).nullable(); // Details of diseases
        table.string('discarge_summaryFile', 255).nullable(); // Path to prescription file
        table.string('preExistingDisease').nullable(); // Description of pre-existing disease
        // Common Fields
        table.boolean('status').notNullable().defaultTo(true); // Status field (Active or Inactive)
        table.integer('created_by').nullable();
        table.integer('updated_by').nullable();
        // Timestamps
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};
exports.down = function (knex) {
    return knex.schema.dropTable('customer_personal_info');
};
