import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import CustomTextField from "../../../components/CustomTextField";
import Box from "@mui/material/Box";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import ModuleName from "../../../components/table/ModuleName";
import Dropdown from "../../../components/table/DropDown";
import { CircularProgress, TextField, Typography } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CustomFileUpload from "../../../components/CustomFileUpload";
import CustomSection from "../../../components/CustomSection";
import dayjs from "dayjs";
import { createAgentDetails, fetchAllImfBranches, getAllRoles, updateAgentDetails, getAgentByIdForEdit, getAllAgentDetails, fetchEmployeeData } from "../../../redux/actions/action";
import { FormControl } from "@mui/material";

const AgentPersonalInformation = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const location = useLocation();
    const [loading, setLoading] = useState(false); // Add loading state

    const [isSubmitting, setIsSubmitting] = useState(false);

    const initialFormData = {
        full_name: '',
        gender_id: null,
        education_id: '',
        aadhar_number: '',
        pan_number: '',
        personal_email: '',
        personal_mobile: '',
        dob: '',
        blood_group: '',
        marital_status_id: '',
        marriage_date: '',
        driving_license_no: '',
        agent_id: '',
        password: '',
        role_id: '',
        branch_id: '',
        first_reporting_manager_id: '',
        second_reporting_manager_id: '',
        official_email: '',
        official_mobile: '',
        date_of_joining: '',
        photo: null,
        aadhar_card_front: null,
        aadhar_card_back: null,
        pan_card: null,
        signed_offer_letter_card: null,
        driving_license_card: null
    };

    const [formData, setFormData] = useState(initialFormData);
    const [formErrors, setFormErrors] = useState({ ...initialFormData });
    const [managerOptions, setManagerOptions] = useState([]);

    const roles = useSelector(state => state.roleManagementReducer.roles);
    const branches = useSelector(state => state.imfBranchReducer.data);
    const genderOptions = useSelector(state => state.pickListReducer.genderOptions);
    const maritalOptions = useSelector(state => state.pickListReducer.maritalStatusOptions);
    const bloodGroupOptions = useSelector(state => state.pickListReducer.bloodGroupOptions);
    const educationOptions = useSelector(state => state.pickListReducer.educationOptions);
    const agentDetails = useSelector(state => state.agentReducer.agent);
    const agents = useSelector(state => state.agentReducer.agents);
    const employees = useSelector(state => state.employeeInfoReducer.employees);

    const minAgeDate = dayjs().subtract(18, "year");
    const [isViewMode, setIsViewMode] = useState(false);
    // const isViewMode = agentDetails?.status === 0;
    // const isEditMode = id && agentDetails?.status === 1;
    const [isEditMode, setIsEditMode] = useState(false);

    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const validateForm = () => {
        let errors = {};

        // Check for duplicates only for specific fields
        const checkDuplicate = (field, value, displayName) => {
            const fieldsToCheck = {
                'aadhar_number': 'Aadhar number',
                'pan_number': 'PAN number',
                'personal_email': 'email',
                'personal_mobile': 'mobile number'
            };

            if (!fieldsToCheck[field]) return;

            const isDuplicate = [...agents, ...employees].some(item => {
                // For edit mode, exclude current record from check using both agent_id and user_id
                if (id) {
                    return item[field] === value &&
                        item.agent_id !== formData.agent_id &&
                        item.user_id !== formData.agent_id;
                }
                // For create mode, check all records
                return item[field] === value;
            });

            if (isDuplicate) {
                errors[field] = `This ${displayName} is already registered`;
                toast.error(`This ${displayName} is already registered`);
            }
        };

        // Check duplicates only for specific fields
        if (formData.personal_email) {
            checkDuplicate('personal_email', formData.personal_email, 'email');
        }
        if (formData.personal_mobile) {
            checkDuplicate('personal_mobile', formData.personal_mobile, 'mobile number');
        }
        if (formData.aadhar_number) {
            checkDuplicate('aadhar_number', formData.aadhar_number, 'Aadhar number');
        }
        if (formData.pan_number) {
            checkDuplicate('pan_number', formData.pan_number, 'PAN number');
        }

        // Validate full name
        if (!formData?.full_name) {
            errors.full_name = "Full name is required";
        } else if (!/^[a-zA-Z\s]*$/.test(formData.full_name)) {
            errors.full_name = "Name should only contain letters";
        }

        // Validate Aadhar number
        if (!formData?.aadhar_number) {
            errors.aadhar_number = "Aadhar number is required";
        } else if (!/^\d{4}-\d{4}-\d{4}$/.test(formData.aadhar_number)) {
            errors.aadhar_number = "Invalid Aadhar number format";
        }

        // Validate PAN number
        if (!formData?.pan_number) {
            errors.pan_number = "PAN number is required";
        } else if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(formData.pan_number)) {
            errors.pan_number = "Invalid PAN number format";
        }

        // Validate personal email
        if (!formData?.personal_email) {
            errors.personal_email = "Personal email is required";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.personal_email)) {
            errors.personal_email = "Invalid email format";
        }

        // Validate personal mobile
        if (!formData?.personal_mobile) {
            errors.personal_mobile = "Personal mobile is required";
        } else if (!/^[6-9]\d{9}$/.test(formData.personal_mobile)) {
            errors.personal_mobile = "Invalid mobile number";
        }

        // Remove mandatory validation for official email and mobile
        if (formData?.official_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.official_email)) {
            errors.official_email = "Invalid email format";
        }

        if (formData?.official_mobile && !/^[6-9]\d{9}$/.test(formData.official_mobile)) {
            errors.official_mobile = "Invalid mobile number";
        }

        // Validate driving license
        if (formData?.driving_license_no && !/^[A-Z]{2}-\d{13}$/.test(formData.driving_license_no)) {
            errors.driving_license_no = "Driving license must be in the format: DL-0420190000000";
        }

        // Validate required dropdowns
        const requiredDropdowns = ['gender_id', 'education_id', 'blood_group', 'marital_status_id', 'role_id', 'branch_id'];
        requiredDropdowns.forEach(field => {
            if (!formData[field]) {
                errors[field] = `${field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} is required`;
            }
        });

        // Validate reporting managers
        ['first_reporting_manager_id', 'second_reporting_manager_id'].forEach(field => {
            if (!formData[field] || formData[field] === 'none') {
                errors[field] = `${field.includes('first') ? 'First' : 'Second'} reporting manager is required`;
            }
        });

        // Validate dates
        if (!formData?.dob) {
            errors.dob = "Date of birth is required";
        } else {
            const dobDate = dayjs(formData.dob);
            if (dobDate.isAfter(minAgeDate)) {
                errors.dob = "Employee must be at least 18 years old";
            }
        }

        if (!formData.blood_group) {
            errors.blood_group = "Blood group is required";
        }

        if (!formData?.date_of_joining) {
            errors.date_of_joining = "Joining date is required";
        } else {
            const joiningDate = dayjs(formData.date_of_joining);
            if (joiningDate.isAfter(dayjs())) {
                errors.date_of_joining = "Joining date cannot be in the future";
            }
        }
        if (formData.marriage_date) { // Assuming 5 is the ID for "Married"
            const marriageDate = dayjs(formData.marriage_date);
            if (marriageDate.isAfter(dayjs())) {
                errors.marriage_date = "Marriage date cannot be in the future";
            }
            if (formData.dob && marriageDate.isBefore(dayjs(formData.dob).add(18, 'year'))) {
                errors.marriage_date = "Marriage date must be after person turns 18";
            }
        }

        // Validate required documents
        const requiredDocs = ['photo', 'aadhar_card_front', 'aadhar_card_back', 'pan_card'];
        requiredDocs.forEach(doc => {
            if (!formData[doc]) {
                errors[doc] = `${doc.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} is required`;
            }
        });

        if (formData?.photo && formData?.photo.size > 10 * 1024 * 1024) {
            errors.photo = 'File size must not exceed 10MB';
        }
        if (formData?.aadhar_card_front && formData?.aadhar_card_front.size > 10 * 1024 * 1024) {
            errors.aadhar_card_front = 'File size must not exceed 10MB';
        }
        if (formData?.aadhar_card_back && formData?.aadhar_card_back.size > 10 * 1024 * 1024) {
            errors.aadhar_card_back = 'File size must not exceed 10MB';
        }
        if (formData?.pan_card && formData?.pan_card.size > 10 * 1024 * 1024) {
            errors.pan_card = 'File size must not exceed 10MB';
        }
        if (formData?.signed_offer_letter_card && formData?.signed_offer_letter_card.size > 10 * 1024 * 1024) {
            errors.signed_offer_letter_card = 'File size must not exceed 10MB';
        }
        if (formData?.driving_license_card && formData?.driving_license_card.size > 10 * 1024 * 1024) {
            errors.driving_license_card = 'File size must not exceed 10MB';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // useEffect(() => {
    //     setFormData({
    //         full_name: 'John Doe',
    //         gender_id: 1,
    //         education_id: 9,
    //         aadhar_number: '1234-5678-9012',
    //         pan_number: '**********',
    //         personal_email: '<EMAIL>',
    //         personal_mobile: '**********',
    //         dob: '1990-01-01',
    //         blood_group: 62,
    //         marital_status_id: 5,
    //         marriage_date: '2015-06-15',
    //         driving_license_no: 'DL-1234567898768',
    //         agent_id: 'AG001',
    //         password: 'password123',
    //         role_id: 1,
    //         branch_id: 1,
    //         first_reporting_manager_id: 'PWS-ADM002',
    //         second_reporting_manager_id: 'PWS-ADM001',
    //         official_email: '<EMAIL>',
    //         official_mobile: '**********',
    //         date_of_joining: '2023-01-15',
    //         photo: null,
    //         aadhar_card_front: null,
    //         aadhar_card_back: null,
    //         pan_card: null,
    //         signed_offer_letter_card: null,
    //         driving_license_card: null
    //     })
    // }, [])

    useEffect(() => {
        setLoading(true);
        dispatch(getAllRoles());
        dispatch(fetchAllImfBranches());
        dispatch(fetchEmployeeData());
        dispatch(getAllAgentDetails());
        if (id) {
            dispatch(getAgentByIdForEdit(id));
        }
        setLoading(false);
    }, [id, dispatch]);

    useEffect(() => {
        if (agents && employees) {
            const managerOptions = [...agents, ...employees].filter(item => item.status !== 0);
            setManagerOptions(managerOptions);
        }
    }, [id, agents, employees])

    useEffect(() => {
        if (id && agentDetails) {
            if (agentDetails?.status === 1) {
                setIsEditMode(true);
                setIsViewMode(false);
            }
            if (agentDetails?.status === 0) {
                setIsViewMode(true);
                setIsEditMode(false);
            }
            const formattedDob = agentDetails.dob ? dayjs(agentDetails.dob).format('YYYY-MM-DD') : '';
            let marriage_date = null;
            if (agentDetails.marriage_date) {
                marriage_date = agentDetails.marriage_date ? dayjs(agentDetails.marriage_date).format('YYYY-MM-DD') : '';
            }
            const formattedJoiningDate = agentDetails.date_of_joining ? dayjs(agentDetails.date_of_joining).format('YYYY-MM-DD') : '';

            // Function to extract filename and create new path
            const processFilePath = (originalPath) => {
                if (!originalPath) return null;
                const filename = originalPath.split('\\').pop(); // Extract filename
                return `D:\\Prime_WealthCare\\Prime_Wealthcare_Frontend\\public\\uploads\\${filename}`;
            };

            setFormData({
                ...agentDetails,
                dob: formattedDob,
                date_of_joining: formattedJoiningDate,
                marriage_date: marriage_date,
                aadhar_card_front: processFilePath(agentDetails.aadhar_card_front),
                aadhar_card_back: processFilePath(agentDetails.aadhar_card_back),
                pan_card: processFilePath(agentDetails.pan_card),
                signed_offer_letter_card: processFilePath(agentDetails.signed_offer_letter_card),
                driving_license_card: processFilePath(agentDetails.driving_license_card)
            });
        }
    }, [id, agentDetails]);

    const generateUserId = () => {
        const lastAgent = agents[agents.length - 1];
        let userId = lastAgent ? lastAgent.agent_id : 'RM-0000';

        let numericPart = parseInt(userId.slice(3), 10);
        numericPart++;

        return `RM-${String(numericPart).padStart(4, '0')}`;
    };

    const handleFileSelect = (file) => {
        if (file.image.size > 10 * 1024 * 1024) {
            setFormErrors({
                ...formErrors,
                [file.section_name]: 'File size must not exceed 10MB'
            });
        }
        try {
            setFormData(prevData => ({
                ...prevData,
                [file.section_name]: file.image
            }));
            if (file.image.size <= 10 * 1024 * 1024) {
                setFormErrors({
                    ...formErrors,
                    [file.section_name]: ''
                });
            }

            const formData = new FormData();
            formData.append('file', file.image);
            formData.append('section_name', file.section_name);
        } catch (error) {
            console.error('File upload error:', error);
            toast.error('Failed to upload file');
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        let data = value;

        // Clear marriage date if marital status is not "Married" (5)
        if (name === 'marital_status_id' && value !== 5) {
            setFormData(prevData => ({
                ...prevData,
                [name]: data,
                marriage_date: null // Clear marriage date
            }));
            return;
        }

        if (value === ' ') {
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [name]: 'Do not start with a whitespace character'
            }));
            return;
        }

        // Field-specific validations
        switch (name) {
            case 'full_name':
                if (/\d/.test(value)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Name cannot contain numbers'
                    }));
                    return;
                }
                data = value.toUpperCase().replace(/\s{2,}$/, ' ');
                break;

            case 'aadhar_number':
                // Remove any existing hyphens first
                const digitsOnly = value.replace(/-/g, '');

                // Check if input contains only digits
                if (!/^\d*$/.test(digitsOnly)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Aadhar number should only contain numbers'
                    }));
                    return;
                }

                if (digitsOnly.length > 12) return;

                // Format with hyphens
                if (digitsOnly.length > 0) {
                    data = digitsOnly.match(/.{1,4}/g).join('-');
                }
                break;

            case 'pan_number':
                if (value.length > 10) return;
                const firstFive = value.slice(0, 5);
                const middleFour = value.slice(5, 9);
                const lastChar = value.slice(9, 10);

                if (!/^[A-Za-z]*$/.test(firstFive)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'First 5 characters must be letters'
                    }));
                    return;
                }

                if (middleFour && !/^\d*$/.test(middleFour)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Characters 6-9 must be numbers'
                    }));
                    return;
                }

                if (lastChar && !/^[A-Za-z]$/.test(lastChar)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Last character must be a letter'
                    }));
                    return;
                }

                data = value.toUpperCase();
                break;

            case 'driving_license_no':
                if (value.length > 16) return;
                if (value.length <= 2) {
                    if (!/^[A-Z]*$/.test(value.toUpperCase())) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'First two characters must be letters'
                        }));
                        return;
                    }
                    data = value.toUpperCase();
                } else {
                    const letters = value.slice(0, 2).toUpperCase();
                    const rest = value.slice(2).replace(/[^0-9]/g, '');
                    if (!/^[A-Z]{2}$/.test(letters)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'First two characters must be letters'
                        }));
                        return;
                    }
                    if (!/^\d*$/.test(rest)) {
                        setFormErrors(prevErrors => ({
                            ...prevErrors,
                            [name]: 'Characters after hyphen must be numbers'
                        }));
                        return;
                    }
                    data = `${letters}-${rest}`;
                }
                break;

            case 'personal_mobile':
            case 'official_mobile':
                if (!/^\d*$/.test(value)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Mobile number can only contain numbers'
                    }));
                    return;
                }
                if (value.length > 10) return;
                break;

            case 'personal_email':
            case 'official_email':
                data = value.toLowerCase();
                if (data !== '' && !/^[a-zA-Z0-9@._-]+$/.test(data)) {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Email can only contain letters, numbers, and (@ . - _)'
                    }));
                    return;
                }
                break;

            case 'address':
                const addressRegex = /^[a-zA-Z0-9,.\-\s#/]+$/;
                if (!addressRegex.test(data) && data !== '') {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Address must only contain letters, numbers, spaces, and special characters (,.-/# )'
                    }));
                    return;
                }
                data = value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ').replace(/\s{2,}$/, ' ');
                break;

            default:
                if (typeof value === 'string') {
                    data = value.toUpperCase().replace(/\s{2,}$/, ' ');
                }
                break;
        }

        setFormData(prevFormData => ({
            ...prevFormData,
            [name]: data
        }));

        if (formErrors[name]) {
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [name]: ''
            }));
        }
    };

    const handleDateChange = (name, value) => {
        if (value) {
            setFormData({
                ...formData,
                [name]: formatDate(value.$d),
            });
            if (formErrors[name]) {
                setFormErrors({
                    ...formErrors,
                    [name]: ''
                });
            }
        }
    };

    const handleSaveAndUpdate = (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        setLoading(true);
        if (validateForm()) {
            const formDataToSend = new FormData();
            if (!id) {
                formDataToSend.append('agent_id', generateUserId());
            }
            Object.keys(formData).forEach(key => {
                if (key === 'dob' || key === 'date_of_joining') {
                    const dateValue = formData[key] ? dayjs(formData[key]).format('YYYY-MM-DD') : '';
                    formDataToSend.append(key, dateValue);
                } else if (key !== 'photo' && key !== 'aadhar_card_front' && key !== 'aadhar_card_back' && key !== 'pan_card' &&
                    key !== 'signed_offer_letter_card' && key !== 'driving_license_card') {
                    formDataToSend.append(key, formData[key]);
                }
            });

            const fileFields = ['photo', 'aadhar_card_front', 'aadhar_card_back', 'pan_card', 'signed_offer_letter_card', 'driving_license_card'];
            fileFields.forEach(field => {
                if (formData[field]) {
                    formDataToSend.append(field, formData[field]);
                }
            });

            if (id) {
                // Update existing agent
                dispatch(updateAgentDetails({ id, formData: formDataToSend }))
                    .then(res => {
                        if (res.payload) {
                            dispatch(getAllAgentDetails());
                            toast.success("Agent updated successfully!");
                            navigate(`/dashboard/agent-master-overview/${id}`);
                        }
                    })
                    .catch(error => {
                        toast.error("Failed to update agent");
                    })
                    .finally(() => {
                        setLoading(false);
                        setIsSubmitting(false);
                    });
            } else {
                // Create new agent
                dispatch(createAgentDetails(formDataToSend))
                    .then(res => {
                        if (res.payload) {
                            dispatch(getAllAgentDetails());
                            toast.success("Agent created successfully!");
                            // Navigate to address page with new agent ID
                            const newAgentId = res.payload;
                            navigate(`/dashboard/agent-address/${newAgentId}`);
                        }
                    })
                    .catch(error => {
                        toast.error("Failed to create agent");
                    })
                    .finally(() => {
                        setLoading(false);
                        setIsSubmitting(false);
                    });
            }
        } else {
            toast.error('Please fix the validation errors before submitting');
            setLoading(false);
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        navigate("/dashboard/agent-master");
    };

    const handleSearchDuplicate = async (e) => {
        const { name, value } = e.target;

        if (value) {
            const isDuplicate = [...agents, ...employees].some(item => {
                // For edit mode, exclude current record from check using both agent_id and user_id
                if (id) {
                    return item.personal_email === value &&
                        item.agent_id !== formData.agent_id &&
                        item.user_id !== formData.agent_id;
                }
                // For create mode, check all records
                return item.personal_email === value;
            });

            if (isDuplicate) {
                setFormErrors(prev => ({
                    ...prev,
                    [name]: 'This email is already registered'
                }));
                toast.error('This email is already registered');
            }
        }
    };

    return (

        <Box sx={{ padding: "0 40px 40px 40px" }}>

            {(loading || isSubmitting) && (
                <Box
                    sx={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)', // Semi-transparent background
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1000 // Ensure it is above other content
                    }}
                    aria-live="polite" // Accessibility feature
                >
                    <Box sx={{ textAlign: 'center' }}>
                        <CircularProgress /> {/* Show loading spinner */}
                        <Typography variant="h6" sx={{ marginTop: 2 }}>
                            {isSubmitting ? "Submitting..." : "Loading..."} {/* Custom message */}
                        </Typography>
                    </Box>
                </Box>
            )}
            <form encType="multipart/form-data">
                <Grid container style={{ display: "flex" }}>
                    {/* Header Row */}
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Agent" pageName={id ? (isViewMode ? "View" : "Edit") : "Create"} />
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        {!isViewMode && (
                            <Button onClick={handleSaveAndUpdate} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}>
                                Save
                            </Button>
                        )}
                        <Button onClick={handleCancel} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }} >
                            Cancel
                        </Button>
                    </Grid>
                    <Grid container >
                        <CustomSection titles={['Overview', 'Personal Details', 'Address']} page='agent' />
                    </Grid>
                </Grid>

                {/* Personal Details */}
                <Grid sx={{ display: 'flex', width: '100%' }}>
                    <Box sx={{
                        width: '100%',
                        backgroundColor: '#f0f0f0',
                        display: "flex",
                        alignItems: "center",
                        padding: "10px",
                        borderRadius: "4px",
                        height: "60px",

                        fontSize: "18px",
                        fontStyle: "normal",
                        fontWeight: "700",
                        lineHeight: "27px",
                        color: '#4C5157',
                    }}>
                        <h5>Personal Details</h5>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={3}>
                        <CustomTextField
                            name="full_name"
                            label="Agent Full Name"
                            value={formData.full_name}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.full_name || ''}
                            isRequired
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Gender"
                            name="gender_id"
                            options={genderOptions.map(option => ({ label: option.label_name, value: option.id }))}
                            value={formData.gender_id || ''}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.gender_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Education"
                            name="education_id"
                            options={educationOptions?.map(option => ({ label: option.label_name, value: option.id }))}
                            value={formData.education_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.education_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            name="aadhar_number"
                            label="Aadhar Number"
                            value={isEditMode ? '****-****-' + formData.aadhar_number.slice(-4) : formData.aadhar_number}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.aadhar_number || ''}
                            info='Example: 1234-5678-9012'
                            isRequired
                            isDisabled={isEditMode || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="PAN Number"
                            name="pan_number"
                            value={isEditMode ? '*****' + formData.pan_number.slice(5, 9) + formData.pan_number.slice(-1) : formData.pan_number}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.pan_number || ''}
                            info="Example: **********"
                            isRequired
                            isDisabled={isEditMode || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            name="personal_email"
                            label="Personal Email Id"
                            value={formData.personal_email}
                            onChange={handleChange}
                            onBlur={handleSearchDuplicate}
                            fullWidth
                            helperText={formErrors.personal_email || ''}
                            isRequired
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            name="personal_mobile"
                            label="Personal Mobile"
                            value={formData.personal_mobile}
                            onChange={handleChange}
                            fullWidth
                            type='tel'
                            applyPrefix
                            helperText={formErrors.personal_mobile || ''}
                            isRequired
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <FormControl fullWidth required error={Boolean(formErrors.dob)}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                {isEditMode || isViewMode ? (
                                    <TextField
                                        label="Date Of Birth"
                                        value={formData.dob ? `**/**/${dayjs(formData.dob).format('YYYY')}` : ''}
                                        disabled
                                        fullWidth
                                        error={Boolean(formErrors.dob)}
                                        helperText={formErrors.dob || 'Must be at least 18 years old'}
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            },
                                        }}
                                    />
                                ) : (
                                    <DatePicker
                                        label="Date Of Birth"
                                        value={formData.dob ? dayjs(formData.dob) : null}
                                        onChange={(date) => handleDateChange('dob', date)}
                                        maxDate={minAgeDate}
                                        disabled={isViewMode}
                                        format="DD/MM/YYYY"
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                required: true,
                                                error: Boolean(formErrors.dob),
                                                helperText: formErrors.dob || 'Must be at least 18 years old',
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                )}
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Blood Group"
                            name="blood_group"
                            options={bloodGroupOptions.map(option => ({ label: option.label_name, value: option.id }))}
                            value={formData.blood_group || ''}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.blood_group || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Marital Status"
                            name="marital_status_id"
                            options={maritalOptions.map(option => ({ label: option.label_name, value: option.id }))}
                            value={formData.marital_status_id || ''}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.marital_status_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    {maritalOptions.find(option => option.label_name === 'Married')?.id === formData.marital_status_id && (
                        <Grid item xs={3}>
                            <FormControl fullWidth required error={Boolean(formErrors.marriage_date)}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="Marriage Date"
                                        value={formData.marriage_date ? dayjs(formData.marriage_date) : null}
                                        onChange={(date) => handleDateChange('marriage_date', date)}
                                        maxDate={dayjs()}
                                        disabled={isViewMode}
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                error: Boolean(formErrors.marriage_date),
                                                helperText: formErrors.marriage_date,
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                    )}
                    <Grid item xs={3}>
                        <CustomTextField
                            name="driving_license_no"
                            label="Driving License No."
                            value={formData.driving_license_no}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.driving_license_no || ''}
                            info="Example: DL-0420190000000"
                            disabled={isViewMode}
                        />
                    </Grid>
                </Grid>

                {/* Agent Details */}
                <Grid sx={{ display: 'flex', width: '100%' }}>
                    <Box sx={{
                        width: '100%',
                        backgroundColor: '#f0f0f0',
                        display: "flex",
                        alignItems: "center",
                        padding: "10px",
                        borderRadius: "4px",
                        height: "60px",

                        fontSize: "18px",
                        fontStyle: "normal",
                        fontWeight: "700",
                        lineHeight: "27px",
                        color: '#4C5157',
                    }}>
                        <h5>Agent Details</h5>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    {id && <Grid item xs={3}>
                        <CustomTextField
                            name="agent_id"
                            label="User Id"
                            value={formData.agent_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.agent_id || ''}
                            isRequired
                            disabled
                        />
                    </Grid>}
                    <Grid item xs={3}>
                        <Dropdown
                            label="Role"
                            name="role_id"
                            options={[
                                { label: 'None', value: '' },
                                ...roles.map(role => ({ label: role.role_name, value: role.id })),
                            ]}
                            value={formData.role_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.role_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Branch Name"
                            name="branch_id"
                            options={[
                                { label: 'None', value: '' },
                                ...branches.map(branch => ({ label: branch.branch_name, value: branch.id })),
                            ]}
                            value={formData.branch_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.branch_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="First Reporting Manager"
                            name="first_reporting_manager_id"
                            options={[
                                { label: 'None', value: '' },
                                ...managerOptions
                                    .filter(manager =>
                                        manager.user_id !== formData.second_reporting_manager_id && manager.agent_id !== formData.second_reporting_manager_id
                                        && (isEditMode ? (manager.agent_id !== formData.agent_id && manager.user_id !== formData.agent_id) : true)
                                    )
                                    .map(manager => ({
                                        label: manager.full_name || manager.employee_full_name,
                                        value: manager.user_id || manager.agent_id
                                    }))
                            ]}
                            value={formData.first_reporting_manager_id || 'none'}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.first_reporting_manager_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Second Reporting Manager"
                            name="second_reporting_manager_id"
                            options={[
                                { label: 'None', value: '' },
                                ...managerOptions
                                    .filter(manager =>
                                        manager.user_id !== formData.first_reporting_manager_id && manager.agent_id !== formData.first_reporting_manager_id
                                        && (isEditMode ? (manager.agent_id !== formData.agent_id && manager.user_id !== formData.agent_id) : true)
                                    )
                                    .map(manager => ({
                                        label: manager.full_name || manager.employee_full_name,
                                        value: manager.user_id || manager.agent_id
                                    }))
                            ]}
                            value={formData.second_reporting_manager_id || 'none'}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.second_reporting_manager_id || ''}
                            required
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Official Email"
                            name="official_email"
                            value={formData?.official_email}
                            onChange={handleChange}
                            fullWidth
                            helperText={formErrors.official_email || ''}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            name="official_mobile"
                            label="Official Mobile"
                            value={formData?.official_mobile}
                            onChange={handleChange}
                            fullWidth
                            type='tel'
                            applyPrefix
                            helperText={formErrors.official_mobile || ''}
                            disabled={isViewMode}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <FormControl fullWidth required error={Boolean(formErrors.date_of_joining)}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Date Of Joining"
                                    value={formData.date_of_joining ? dayjs(formData.date_of_joining) : null}
                                    onChange={(date) => handleDateChange('date_of_joining', date)}
                                    maxDate={dayjs()}
                                    disabled={isViewMode}
                                    format="DD/MM/YYYY"
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            required: true,
                                            error: Boolean(formErrors.date_of_joining),
                                            helperText: formErrors.date_of_joining,
                                            sx: {
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>
                </Grid>

                {/* Documents */}
                <Grid sx={{ display: 'flex', width: '100%' }}>
                    <Box
                        sx={{
                            width: '100%',
                            backgroundColor: '#f0f0f0',
                            display: "flex",
                            alignItems: "center",
                            padding: "10px",
                            borderRadius: "4px",
                            height: "60px",

                            fontSize: "18px",
                            fontStyle: "normal",
                            fontWeight: "700",
                            lineHeight: "27px",
                            color: '#4C5157',
                        }}
                    >
                        <h5>Documents</h5>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ width: "100%", padding: '1rem' }}>
                    <Grid item xs={3}>
                        <CustomFileUpload
                            name="photo"
                            section_name="photo"
                            accept=".jpeg,.png,.jpg"
                            label={"Upload Photo"}
                            onFileSelect={handleFileSelect}
                            insertedFile={!agentDetails?.photo ? null : {
                                section_name: 'photo',
                                name: 'Photo',
                                url: agentDetails?.photo
                            }}
                            helperText={formErrors.photo || "Upload in Jpeg, Png, Jpg format"}
                            error={formErrors.photo}
                            isRequired
                            disabled={isViewMode}
                            sx={{
                                '& .MuiFormHelperText-root': {
                                    display: 'block',
                                    visibility: 'visible'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomFileUpload
                            name="aadhar_card_front"
                            section_name="aadhar_card_front"
                            accept=".pdf,.jpeg,.png,.jpg"
                            label={"Upload Aadhaar Front"}
                            onFileSelect={handleFileSelect}
                            insertedFile={!agentDetails?.aadhar_card_front ? null : {
                                section_name: 'aadhar_card_front',
                                name: 'Aadhaar Front',
                                url: agentDetails?.aadhar_card_front
                            }}
                            error={!!formErrors.aadhar_card_front}
                            helperText={formErrors.aadhar_card_front || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            isRequired
                            disabled={isViewMode}
                            sx={{
                                '& .MuiFormHelperText-root': {
                                    display: 'block',
                                    visibility: 'visible'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomFileUpload
                            name="aadhar_card_back"
                            section_name="aadhar_card_back"
                            accept=".pdf,.jpeg,.png,.jpg"
                            label={"Upload Aadhaar Back"}
                            onFileSelect={handleFileSelect}
                            insertedFile={!agentDetails?.aadhar_card_back ? null : {
                                section_name: 'aadhar_card_back',
                                name: 'Aadhaar Back',
                                url: agentDetails?.aadhar_card_back
                            }}
                            error={!!formErrors.aadhar_card_back}
                            helperText={formErrors.aadhar_card_back || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            isRequired
                            disabled={isViewMode}
                            sx={{
                                '& .MuiFormHelperText-root': {
                                    display: 'block',
                                    visibility: 'visible'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomFileUpload
                            name="pan_card"
                            section_name="pan_card"
                            accept=".pdf,.jpeg,.png,.jpg"
                            label={"Upload PAN"}
                            onFileSelect={handleFileSelect}
                            insertedFile={!agentDetails?.pan_card ? null : {
                                section_name: 'pan_card',
                                name: 'PAN',
                                url: agentDetails?.pan_card
                            }}
                            error={!!formErrors.pan_card}
                            helperText={formErrors.pan_card || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            isRequired
                            disabled={isViewMode}
                            sx={{
                                '& .MuiFormHelperText-root': {
                                    display: 'block',
                                    visibility: 'visible'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomFileUpload
                            name="signed_offer_letter_card"
                            section_name="signed_offer_letter_card"
                            accept=".pdf,.jpeg,.png,.jpg"
                            label={"Upload Signed Offer Letter"}
                            onFileSelect={handleFileSelect}
                            insertedFile={!agentDetails?.signed_offer_letter_card ? null : {
                                section_name: 'signed_offer_letter_card',
                                name: 'Signed Offer Letter',
                                url: agentDetails?.signed_offer_letter_card
                            }}
                            error={!!formErrors.signed_offer_letter_card}
                            helperText={formErrors.signed_offer_letter_card || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            disabled={isViewMode}
                            sx={{
                                '& .MuiFormHelperText-root': {
                                    display: 'block',
                                    visibility: 'visible'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomFileUpload
                            name="driving_license_card"
                            section_name="driving_license_card"
                            accept=".pdf,.jpeg,.png,.jpg"
                            label={"Upload Driving License"}
                            onFileSelect={handleFileSelect}
                            insertedFile={!agentDetails?.driving_license_card ? null : {
                                section_name: 'driving_license_card',
                                name: 'Driving License',
                                url: agentDetails?.driving_license_card
                            }}
                            error={formErrors.driving_license_card}
                            helperText={formErrors.driving_license_card || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            disabled={isViewMode}
                        />
                    </Grid>
                </Grid>
            </form>
        </Box >
    );
};

export default AgentPersonalInformation; 