import React from 'react';
import { Dialog, DialogActions, DialogContent, DialogTitle, But<PERSON> } from '@mui/material';

const SuccessPopup = ({ open, onClose, modulename, deleted }) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>{modulename} {deleted || 'deactivated'} Successfully!</DialogTitle>
      <DialogContent>
        <p>{modulename} has been {deleted || 'deactivated'} successfully.</p>
      </DialogContent>
      <DialogActions
        style={{
          justifyContent: 'center',
          height: '100px',
        }}
      >
        <Button
          onClick={onClose}
          color="primary"
          sx={{ backgroundColor: '#528A7E', color: 'white', '&:hover': { backgroundColor: '#3f6e5e' } }}
        >
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SuccessPopup;
