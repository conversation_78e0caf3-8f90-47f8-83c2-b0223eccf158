const QuotationModel = require('../Models/quotations_model'); // Adjust path as per your folder structure


// Fetch all quotations
const getQuotationResponse = async (req, res) => {
  try {
    const quotations = await QuotationModel.getQuotationWithResponseAndMembers();
    res.status(200).json(quotations);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching quotations', error });
  }
};
// Fetch a single quotation by ID
const getQuotationResponseById = async (req, res) => {
  const { id } = req.params;
  // console.log(id, "quotations")
  try {
    const quotation = await QuotationModel.getQuotationWithResponseAndMembersById(id);
    if (!quotation) {
      return res.status(404).json({ message: 'Quotation not found' });
    }
    res.status(200).json(quotation);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching quotation', error });
  }
};
// Fetch all quotations
const getAllQuotations = async (req, res) => {
  try {
    const quotations = await QuotationModel.getAllQuotations();
    res.status(200).json(quotations);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching quotations', error });
  }
};

// Fetch all quotations based on user_id
const getAllQuotationsByUserId = async (req, res) => {
  const { userId } = req.params;
  try {
    const quotations = await QuotationModel.getAllQuotationsByUserId(userId);
    res.status(200).json(quotations);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching quotations', error });
  }
};



// Fetch a single quotation by ID
const getQuotationById = async (req, res) => {
  const { id } = req.params;
  try {
    const quotation = await QuotationModel.getQuotationById(id);
    if (!quotation) {
      return res.status(404).json({ message: 'Quotation not found' });
    }
    res.status(200).json(quotation);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching quotation', error });
  }
};
// Fetch a single quotation by ID
const getQuotationByQuotationNumber = async (req, res) => {
  const { quotationNumber } = req.params;
  try {
    // console.log('Getting quotation by Quotation Number:', quotationNumber);
    const quotation = await QuotationModel.getQuotationByQuotationNumber(quotationNumber);
    if (!quotation) {
      return res.status(404).json({ message: 'Quotation not found' });
    }
    res.status(200).json(quotation);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching quotation', error });
  }
};

// Create a new quotation
const createQuotation = async (req, res) => {
  try {
    const quotationData = req.body;
    const [newQuotation] = await QuotationModel.createQuotation(quotationData); // Extract first object from returning array
    res.status(201).json(newQuotation);
  } catch (error) {
    res.status(500).json({ message: 'Error creating quotation', error });
  }
};

// Update a quotation
const updateQuotation = async (req, res) => {
  const { id } = req.params;
  const { quotationData } = req.body;

  try {
    const result = await QuotationModel.updateQuotation(
      id,
      quotationData,
      membersData
    );

    if (!result.success) {
      return res.status(404).json({ message: 'Quotation not found' });
    }

    res.status(200).json(result);
  } catch (error) {
    console.error('Error updating quotation:', error);
    res.status(500).json({
      message: 'Error updating quotation',
      error: error.message
    });
  }
};



module.exports = {
  getAllQuotations,
  getQuotationById,
  createQuotation,
  updateQuotation,
  getQuotationResponse,
  getQuotationResponseById,
  getQuotationByQuotationNumber,
  getAllQuotationsByUserId
};
