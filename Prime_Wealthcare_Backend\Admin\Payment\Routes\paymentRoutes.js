const express = require('express');
const { submitPayment, createPaymentDetails, getPaymentCounts, updatePaymentDetails, checkSuccess, getPaymentByProposalNumber, updatePaymentByTransactionId, updatePaymentStatus } = require('../Controllers/paymentController');
const { sendEmail } = require('../../../services/emailService');
const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const router = express.Router();

// POST route to handle payment submission
router.post('/', createPaymentDetails);
router.put('/:id', updatePaymentDetails);
router.get('/check-success/:proposal_number', checkSuccess);
router.get('/getPaymentByProposalNumber/:proposal_number', getPaymentByProposalNumber);

router.post('/pay-later', async (req, res) => {
    try {
        const paymentData = req.body;

        // Get the payment URL from environment variable or config
        let proposal = await db('proposals')
            .where({
                ProposalNumber: paymentData.ProposalNumber
            })
            .first();

        if (!proposal) {
            // If not found in proposals, check pa_proposals
            proposal = await db('proposals_pa')
                .where({
                    ProposalNumber: paymentData.ProposalNumber
                })
                .first();
        }

        if (!proposal) {
            return res.status(404).json({
                success: false,
                message: 'Proposal not found',
                data: null
            });
        }

        // Get agent details
        const agent = await db('agents')
            .where({
                id: proposal.agent_code
            })
            .first();

        // Create customer HTML form for email
        const customerEmailHtml = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #528A7E; text-align: center;">Complete Your Insurance Payment</h2>
                <p>Dear ${paymentData.FirstName},</p>
                <p>Thank you for choosing Prime Wealthcare for your insurance needs.</p>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="color: #528A7E; margin-top: 0;">Transaction Details</h3>
                    <ul style="list-style: none; padding-left: 0;">
                        <li>Transaction ID: ${paymentData.TransactionID}</li>
                        <li>Proposal Number: ${paymentData.ProposalNumber}</li>
                        <li>Premium Amount: ₹${paymentData.PremiumAmount}</li>
                    </ul>
                </div>

                <form action="${paymentData.paymentUrl}" method="POST" style="text-align: center;">
                    ${Object.entries(paymentData)
                .map(([key, value]) =>
                    `<input type="hidden" name="${key}" value="${value}">`
                )
                .join('')
            }
                    <button type="submit" 
                            style="background-color: #528A7E; 
                                   color: white; 
                                   padding: 12px 25px; 
                                   border: none; 
                                   border-radius: 5px; 
                                   font-size: 16px; 
                                   cursor: pointer;
                                   margin: 20px 0;">
                        Make Payment Now
                    </button>
                </form>

                <p style="color: #666; font-size: 14px;">This payment link will be valid for the next 24 hours.</p>
                <p style="color: #666; font-size: 14px;">If you have any questions, please contact our customer support.</p>
                
                <div style="margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px;">
                    <p style="color: #666; font-size: 12px; text-align: center;">
                        This is an automated email. Please do not reply directly to this message.
                    </p>
                </div>
            </div>
        `;

        // Create agent HTML notification
        const agentEmailHtml = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #528A7E; text-align: center;">Payment Link Sent to Customer</h2>
                <p>Dear Agent,</p>
                <p>A payment link has been sent to your customer. If they face any issues, you can complete the payment on their behalf using the link below:</p>
                
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="color: #528A7E; margin-top: 0;">Customer & Transaction Details</h3>
                    <ul style="list-style: none; padding-left: 0;">
                        <li><strong>Customer Name:</strong> ${paymentData.FirstName}</li>
                        <li><strong>Customer Email:</strong> ${paymentData.Email}</li>
                        <li><strong>Transaction ID:</strong> ${paymentData.TransactionID}</li>
                        <li><strong>Proposal Number:</strong> ${paymentData.ProposalNumber}</li>
                        <li><strong>Premium Amount:</strong> ₹${paymentData.PremiumAmount}</li>
                    </ul>
                </div>

                <form action="${paymentData.paymentUrl}" method="POST" style="text-align: center;">
                    ${Object.entries(paymentData)
                .map(([key, value]) =>
                    `<input type="hidden" name="${key}" value="${value}">`
                )
                .join('')
            }
                    <button type="submit" 
                            style="background-color: #528A7E; 
                                color: white; 
                                padding: 12px 25px; 
                                border: none; 
                                border-radius: 5px; 
                                font-size: 16px; 
                                cursor: pointer;
                                margin: 20px 0;">
                        Make Payment Now
                    </button>
                </form>

                <p style="color: #666; font-size: 14px;">The payment link will be valid for 24 hours.</p>
                
                <div style="margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px;">
                    <p style="color: #666; font-size: 12px; text-align: center;">
                        This is an automated notification. Please do not reply directly to this message.
                    </p>
                </div>
            </div>
        `;


        // Send emails to both customer and agent
        const emailPromises = [
            sendEmail(
                paymentData.Email,
                'Complete Your Insurance Payment - Prime Wealthcare',
                'Please use an HTML-enabled email client to view this message.',
                customerEmailHtml
            )
        ];

        if (agent && agent.official_email) {
            emailPromises.push(
                sendEmail(
                    agent.official_email,
                    'Payment Link Sent to Customer - Prime Wealthcare',
                    'Please use an HTML-enabled email client to view this message.',
                    agentEmailHtml
                )
            );
        }

        await Promise.all(emailPromises);

        const result = await db('payment_master')
            .where('TransactionID', paymentData?.TransactionID)
            .update({
                Status: 'EMAIL_SENT',
                updated_at: getCurrentTimestamp()
            });
        if (!result) {
            return res.status(500).json({
                success: false,
                message: 'Failed to update payment status in the database',
                data: null
            });
        }
        // Send success response
        res.json({
            success: true,
            message: 'Payment link sent successfully',
        });
    } catch (error) {
        console.error('Pay Later Error:', error);
        res.status(500).json({
            success: false,
            error: {
                message: error.message || 'Failed to process pay later request',
                timestamp: new Date().toISOString(),
                type: 'PAY_LATER_ERROR'
            }
        });
    }
});

router.post('/check-payment/:transactionId', async (req, res) => {
    try {
        const { transactionId } = req.params;
        const paymentDetails = await require('../SoapService/checkPayment').sendSOAPRequest(
            transactionId
        );


        if (paymentDetails && paymentDetails.transactionStatus === process.env.PAYMENT_SUCCESS_CODE) {
            const updatedPaymentDetails = {
                transactionId: paymentDetails.transactionId,
                status: "SUCCESS",
                transaction_date: paymentDetails.transactionDate,
                WS_P_ID: paymentDetails.fgTransactionId,
                PGID: paymentDetails.pgTransactionId,
            }
            const updatedData = {
                ...updatedPaymentDetails,
                updated_at: getCurrentTimestamp()
            }
            const result = await db('payment_master').where('transactionId', transactionId).update(updatedData);
            if (result) {
                res.json({
                    success: true,
                    message: 'Payment details retrieved successfully',
                    data: paymentDetails
                });
            } else {
                res.status(500).json({
                    success: false,
                    message: 'Failed to update payment details',
                    data: null
                });
            }
        } else {
            res.status(404).json({
                success: false,
                message: 'Payment details not found',
                data: null
            });
        }
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: {
                message: error.message ? error.message : 'Failed to retrieve payment details',
                timestamp: new Date().toISOString(),
                type: error.type ? error.type : 'CHECK_PAYMENT_ERROR'
            }
        });
    }
});
//router.post('/submit-payment', submitPayment);

module.exports = router;
