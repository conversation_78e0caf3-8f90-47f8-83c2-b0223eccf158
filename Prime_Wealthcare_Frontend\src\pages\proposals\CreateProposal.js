import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import CustomTextField from "../../components/CustomTextField";
import Box from "@mui/material/Box";
import { useDispatch, useSelector } from "react-redux";
import ModuleName from "../../components/table/ModuleName";
import Dropdown from "../../components/table/DropDown";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CustomFileUpload from "../../components/CustomFileUpload";
import dayjs from "dayjs";
import { fetchAllImfBranches, getAllAgentDetails, getAllCustomer, fetchInsuranceCompanies, getAllProducts, getAllPickLists, getAllSubProducts, fetchInsuranceCompanyBranches, fetchImfAgencyCodes, getAllMasterProducts, getCustomerById, fetchInsuranceCompanyBranchByInsuranceCompanyId, getAgentByBranchName, getMasterProductByMainProductAndInsuranceCompany, getSubProductByProductDetails, getMemberByCustomerId, checkForCkyc, createProposal, createPayment, checkIfSuccess, submitProposal, updateProposal, getProposalById, checkCKYCStatus, submitPolicy, updateMember, getNomineeRelations, getPolicyPdf, getPaymentByProposalNumber, checkPaymentStatus, updatePayment } from "../../redux/actions/action";
import { Radio, RadioGroup, FormControlLabel, FormControl, Typography, Divider, IconButton, Switch, FormHelperText, InputLabel, Select, MenuItem, CircularProgress, Backdrop } from "@mui/material";
import { Cancel, Save } from "@mui/icons-material";
import AutocompleteDropdown from "../../components/table/AutocompleteDropdown";
import { formatDate, generateProposalNumber, generateChecksum, formatToTwoDecimals, calculateAge } from "../../utils/Reusable";
import { toast } from "react-toastify";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import DeleteIcon from "@mui/icons-material/Delete";
import BarLoader from '../../components/BarLoader';
import { Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import { clearAllPaymentDetails, clearPaymentDetails, setPaymentDetails, setCurrentProposalId } from "../../redux/slices/payment/paymentSlice";
import { clearProposalDetails, clearSelectedQuotation } from "../../redux/slices/proposal/proposalSlice";
import { clearCustomerMemberDetails } from "../../redux/slices/customer/customer_member_info_slice";
import { clearSubProduct } from "../../redux/slices/master/subProductSlice";
import { clearInsuranceBranch } from "../../redux/slices/master/insuranceCompanyBranchSlice";
import { clearProductMaster } from "../../redux/slices/master/productMasterSlice";
import { clearImfBranch } from "../../redux/slices/master/imfBranchSlice";
import { clearCustomerDetails } from "../../redux/slices/customer/customer_info_slice";
import QuestionToggle from "../../components/QuestionToggle";

function CreateProposal() {
    const { id } = useParams();
    const host = process.env.REACT_APP_INTERNAL_HOST;
    const port = process.env.REACT_APP_INTERNAL_PORT;
    const baseUrl = port ? `${host}:${port}` : host;

    const navigate = useNavigate();
    const dispatch = useDispatch();

    // Required Data
    const [formData, setFormData] = useState({
        customer_id: '',
        ckyc_number: null,
        proposal_id: '',
        proposal_number: '',
        ckyc_link: '',
        salutation: '',

        quotation_number: '',
        quotation_created_at: '',

        insurance_company_name: '',
        insurance_company_branch: '',
        imf_code: '',
        imf_branch: '',
        agent_id: '',
        product_type: '',
        product_name: '',
        sub_product_name: '',
        member_type: '',
        co_pay: false,

        net_premium: '',
        gst_amount: '',
        gst_percentage: 18,
        total_premium: '',
        policy_issue_date: '',
        policy_number: '',
        policy_pdf: '',
        start_date: '',
        tenure: '',
        end_date: '',


        payment_type: '',

        remarks: '',

        created_by: '',
        created_at: '',

        family_discount_percentage: '',
        family_discount_amount: '',
        long_term_discount_percentage: '',
        long_term_discount_amount: '',
    });
    const [members, setMembers] = useState([]);
    const [formErrors, setFormErrors] = useState({ ...formData });
    const [memberErrors, setMemberErrors] = useState([]);
    const [loading, setLoading] = useState(false);
    const [proposalType, setProposalType] = useState('New');
    const [isPaymentSuccess, setIsPaymentSuccess] = useState(0);
    const [transactionId, setTransactionId] = useState('');
    const [openDialog, setOpenDialog] = useState(false);
    const [isCustomerMember, setIsCustomerMember] = useState(false);
    const [dialogConfig, setDialogConfig] = useState({
        title: '',
        message: '',
        type: '',
        callback: null
    });
    const [readyForProposal, setReadyForProposal] = useState(false);
    const [areCustomerMembersFetched, setAreCustomerMembersFetched] = useState(false);
    const [onlySeniorCitizen, setOnlySeniorCitizen] = useState(false);
    const [onlySeniorCitizenAsMember, setOnlySeniorCitizenAsMember] = useState(false);
    const [reloadNeeded, setReloadNeeded] = useState(false);
    const [salutations, setSalutations] = useState([]);
    const [isCancelled, setIsCancelled] = useState(false);

    // Redux States
    const customerDetails = useSelector(state => state.customerReducer.customer); // For customer dropdown
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies); // For insurance company dropdown
    const insuranceBranches = useSelector(state => state.insuranceBranchReducer.branches); // For insurance company branch dropdown
    const agencyCodes = useSelector(state => state.imfAgencyCodeReducer.agencyCodes); // For imf agency code dropdown
    const imfBranches = useSelector(state => state.imfBranchReducer.data); // For imf branch dropdown
    const agents = useSelector(state => state.agentReducer.agents); // For agent dropdown
    const mainProducts = useSelector(state => state.mainProductReducer.products); // For product type dropdown
    const masterProducts = useSelector(state => state.productMasterReducer.products); // For product name dropdown
    const subProducts = useSelector(state => state.subProductReducer.subProducts); // For sub product name dropdown
    const customer = useSelector(state => state.customerReducer.customerDetails); // For getting single customer details
    const membersData = useSelector(state => state.customerMemberReducer.customerMember); // For getting customer members for a single customer
    const companyData = useSelector(state => state.insuranceCompanyReducer.currentCompany); // For getting current insurance company details
    const user = useSelector((state) => state.auth.user); // For getting current logged in user details
    const fetchedProposal = useSelector(state => state.proposalReducer.proposal); // For getting proposal details for a single proposal
    const selectedQuotation = useSelector(state => state.proposalReducer.selectedQuotation); // For getting selected quotation details

    // Picklist Data
    const relationOptions = useSelector(state => state.pickListReducer.relationOptions); // For relation dropdown
    const maritalStatusOptions = useSelector(state => state.pickListReducer.maritalStatusOptions); // For marital status dropdown
    const paymentTypes = useSelector(state => state.pickListReducer.paymentTypes); // For payment type dropdown
    const durationOptions = useSelector(state => state.pickListReducer.durationOptions); // For duration dropdown
    const genderOptions = useSelector(state => state.pickListReducer.genderOptions); // For gender dropdown
    const salutationOptions = useSelector(state => state.pickListReducer.salutationOptions); // For salutation dropdown
    const OccupationOptions = useSelector(state => state.pickListReducer.OccupationOptions); // For occupation dropdown

    // Helper variables
    const [relations, setRelations] = useState();
    const username = user?.userName || user?.full_name;
    const minAgeDate = dayjs().subtract(18, 'year');
    const { quotationsData, customerInfo, totalPremium } = selectedQuotation ? selectedQuotation : {
        quotationsData: [],
        customerInfo: {},
        totalPremium: {}
    };
    const [questions, setQuestions] = useState([]);
    const [answers, setAnswers] = useState([]);
    const [transactionStatus, setTransactionStatus] = useState(false); // Transaction details for payment

    const deductileOptions = [
        { value: 50000, label: '50 Thousand' },
        { value: 100000, label: '1 Lakh' },
        { value: 150000, label: '1.5 Lakh' },
        { value: 200000, label: '2 Lakh' },
        { value: 250000, label: '2.5 Lakh' },
        { value: 300000, label: '3 Lakh' },
        { value: 350000, label: '3.5 Lakh' },
        { value: 400000, label: '4 Lakh' },
        { value: 450000, label: '4.5 Lakh' },
        { value: 500000, label: '5 Lakh' },
        { value: 600000, label: '6 Lakh' },
        { value: 700000, label: '7 Lakh' },
        { value: 750000, label: '7.5 Lakh' },
        { value: 800000, label: '8 Lakh' },
        { value: 900000, label: '9 Lakh' },
        { value: 1000000, label: '10 Lakh' },
        { value: 1500000, label: '15 Lakh' },
        { value: 2000000, label: '20 Lakh' },
        { value: 2500000, label: '25 Lakh' },
        { value: 3000000, label: '30 Lakh' },
        { value: 3500000, label: '35 Lakh' },
        { value: 4000000, label: '40 Lakh' },
        { value: 4500000, label: '45 Lakh' },
        { value: 5000000, label: '50 Lakh' },
        { value: 10000000, label: '1 Crore' },
        { value: 20000000, label: '2 Crore' },
    ]
    const sumInsuredOptions = [
        { value: 50000, label: '50 Thousand' },
        { value: 100000, label: '1 Lakh' },
        { value: 150000, label: '1.5 Lakh' },
        { value: 200000, label: '2 Lakh' },
        { value: 250000, label: '2.5 Lakh' },
        { value: 300000, label: '3 Lakh' },
        { value: 350000, label: '3.5 Lakh' },
        { value: 400000, label: '4 Lakh' },
        { value: 450000, label: '4.5 Lakh' },
        { value: 500000, label: '5 Lakh' },
        { value: 600000, label: '6 Lakh' },
        { value: 700000, label: '7 Lakh' },
        { value: 750000, label: '7.5 Lakh' },
        { value: 800000, label: '8 Lakh' },
        { value: 900000, label: '9 Lakh' },
        { value: 1000000, label: '10 Lakh' },
        { value: 1500000, label: '15 Lakh' },
        { value: 2000000, label: '20 Lakh' },
        { value: 2500000, label: '25 Lakh' },
        { value: 3000000, label: '30 Lakh' },
        { value: 3500000, label: '35 Lakh' },
        { value: 4000000, label: '40 Lakh' },
        { value: 4500000, label: '45 Lakh' },
        { value: 5000000, label: '50 Lakh' },
        { value: 10000000, label: '1 Crore' },
        { value: 20000000, label: '2 Crore' },
    ]

    // To handle the cleanup of the related data when unmounting the component
    useEffect(() => {
        const handleBeforeUnload = (e) => {
            e.preventDefault();
        };
        if (!reloadNeeded) {
            window.addEventListener('beforeunload', handleBeforeUnload);
        }

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
            // Only cleanup if not refreshing and not reloading
            if (window.performance.navigation.type !== window.performance.navigation.TYPE_RELOAD && !reloadNeeded) {
                handleCleanup();
                dispatch(clearSelectedQuotation());
            }
        }
    }, [navigate, reloadNeeded]);

    // To fetch the required data when the component is mounted
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                handleCleanup();
                dispatch(clearAllPaymentDetails());
                await Promise.all([
                    dispatch(getAllCustomer()),
                    dispatch(fetchInsuranceCompanies()),
                    dispatch(getAllProducts()),
                    dispatch(fetchImfAgencyCodes()),
                    dispatch(getAllPickLists()),
                    dispatch(fetchAllImfBranches()),
                ]);

                if (id && Number(id) !== 0) {
                    await Promise.all([
                        dispatch(fetchInsuranceCompanyBranches()),
                        dispatch(getAllAgentDetails()),
                        dispatch(getAllMasterProducts()),
                        dispatch(getAllSubProducts()),
                    ]);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                toast.error('Failed to load initial data');
            } finally {
                setReadyForProposal(true);
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    useEffect(() => {
        if (relationOptions) {
            setRelations(relationOptions);
        }
    }, [relationOptions])

    // To initialize the proposal number when the component is mounted
    useEffect(() => {
        if (user && formData.proposal_number === '') {
            const initializeProposal = async () => {
                const number = await generateProposalNumber(user.userId, dispatch);
                setFormData(prevData => ({
                    ...prevData,
                    created_by: username,
                    proposal_number: number
                }));
            }
            initializeProposal();
        }
    }, [user])

    // To fetch the proposal data when the component is mounted
    useEffect(() => {
        if (id && id !== '0' && readyForProposal) {
            setLoading(true);
            dispatch(getProposalById(id))
                .then(res => {
                    const proposalData = res.payload;
                    const formattedData = {
                        customer_id: proposalData.customer_id,
                        salutation: proposalData.customer_salutation,
                        proposal_id: proposalData.proposal_Id,
                        ckyc_number: proposalData.ckyc_number,
                        ckyc_link: proposalData.ckyc_link,
                        ckyc_expiry_date: proposalData.ckyc_expiry_date,
                        proposal_number: proposalData.ProposalNumber,

                        quotation_number: proposalData.quotation_number,
                        quotation_created_at: proposalData.quotation_created_at,

                        insurance_company_name: proposalData.insurance_company,
                        insurance_company_branch: proposalData.insurance_branch,
                        imf_code: proposalData.imf_code,
                        imf_branch: proposalData.imf_branch,
                        agent_id: Number(proposalData.agent_code),
                        product_type: proposalData.product_type,
                        product_name: proposalData.product_name,
                        sub_product_name: proposalData.sub_product,
                        member_type: proposalData.member_type,
                        co_pay: proposalData.co_pay,

                        net_premium: proposalData.net_premium,
                        gst_amount: proposalData.gst_amount,
                        ...(id.includes('PA') && {
                            long_term_discount_percentage: proposalData.long_term_discount_percentage,
                            long_term_discount_amount: proposalData.long_term_discount_amount,
                            family_discount_percentage: proposalData.family_discount_percentage,
                            family_discount_amount: proposalData.family_discount_amount,
                        }),
                        total_premium: proposalData.total_premium,
                        policy_issue_date: proposalData.policy_issue_date,
                        policy_number: proposalData.policy_number,
                        policy_pdf: proposalData.policy_pdf,
                        start_date: proposalData.start_date,
                        tenure: proposalData.tenure,
                        end_date: proposalData.end_date,

                        remarks: proposalData.remarks,

                        created_by: proposalData.Created_by,
                        created_at: proposalData.Created_at,
                    };
                    setFormData(prev => ({
                        ...prev,
                        ...formattedData
                    }));

                    if (proposalData.policy_number && !proposalData.policy_pdf) {
                        handleGetPolicyPdf(proposalData.policy_number);
                    }
                })
                .catch(error => {
                    console.error('Error fetching proposal:', error);
                    toast.error('Failed to load proposal details');
                })
                .finally(() => {
                    setLoading(false);
                })
        }
    }, [id, dispatch, readyForProposal]);

    // To fetch the proposal members data when the component is mounted
    useEffect(() => {
        if (id && Number(id) !== 0 && customer && fetchedProposal?.members?.length > 0 && areCustomerMembersFetched) {
            handlePaymentDetails(false);
            const proposalMembers = fetchedProposal?.members;
            proposalMembers?.forEach(member => {
                const isCustomer = member.customer_member_id === null;
                const customerMember = membersData.find(m => m.id === member.customer_member_id);
                const customerMemberName = customerMember?.full_name?.split(' ');
                const genderId = isCustomer ? customer.gender_id : customerMember?.gender_id || '';
                const memberData = {
                    first_name: isCustomer ? customer.first_name : customerMemberName?.[0] || '',
                    middle_name: isCustomer ? customer.middle_name : customerMemberName?.length > 2 ? customerMemberName?.[1] : '',
                    last_name: isCustomer ? customer.last_name : customerMemberName?.length > 1 ? customerMemberName?.[customerMemberName?.length - 1] : '',
                    dob: isCustomer ? formatDate(customer.date_of_birth) : formatDate(customerMember?.date_of_birth) || '',
                    relation_id: member?.relation || '',
                    gender: genderOptions.find(option => option.id === genderId)?.api_name || '',
                    marital_status: isCustomer ? customer.marital_status_id : customerMember?.marital_status_id || '',
                    marriage_date: isCustomer ? formatDate(customer.marriage_date) : formatDate(customerMember?.marriage_date) || '',
                    mobile_number: isCustomer ? customer.mobile : customerMember?.mobile || '',
                    member_id: member.member_id,
                    email_id: isCustomer ? customer.email : customerMember?.email || '',
                    sum_insured: member.sum_insured,
                    deductible: member.deductible || null,
                    ...(member.health_declaration && { health_declaration: getHealthDeclarationToObject(member.health_declaration) }),
                    ...(member.ad_sum_insured && {
                        occupation: member.occupation,
                        ad_sum_insured: member.ad_sum_insured || 0,
                        pp_sum_insured: member.pp_sum_insured || 0,
                        pt_sum_insured: member.pt_sum_insured || 0,
                        tt_sum_insured: member.tt_sum_insured || 0,
                        lp_sum_insured: member.lp_sum_insured || 0,
                        ls_sum_insured: member.ls_sum_insured || 0,
                        me_sum_insured: member.me_sum_insured || 0,
                        am_sum_insured: member.am_sum_insured || 0,
                        bb_sum_insured: member.bb_sum_insured || 0,
                        rf_sum_insured: member.rf_sum_insured || 0,
                        aa_sum_insured: member.aa_sum_insured || 0,
                        cs_sum_insured: member.cs_sum_insured || 0,
                        hc_sum_insured: member.hc_sum_insured || 0,
                        ft_sum_insured: member.ft_sum_insured || 0,
                        annual_income: member.annual_income || 0,
                    }),
                    id: member.id,
                    nominee: {
                        name: member.nominee_name || '',
                        gender: member.nominee_gender || '',
                        dob: member.nominee_dob ? member.nominee_dob : null,
                        relation: member.nominee_relation || null
                    },
                    appointee: {
                        name: member.appointee_name || '',
                        gender: member.appointee_gender || '',
                        dob: member.appointee_dob ? member.appointee_dob : null,
                        relation: member.appointee_relation || null
                    }
                }
                if (proposalMembers.length === 1) {
                    setMembers([memberData]);
                } else if (proposalMembers.length > 1) {
                    setMembers(prevMembers => [...prevMembers, memberData]);
                }
            });
        }
    }, [id, customer, areCustomerMembersFetched, fetchedProposal?.members]);

    // useEffect to eliminate duplicates in members array
    useEffect(() => {
        if (members && members.length > 0) {
            const isDuplicatePresent = members.some((member, index, self) =>
                index !== self.findIndex((t) => (
                    t.first_name === member.first_name &&
                    t.last_name === member.last_name &&
                    t.dob === member.dob
                ))
            );
            if (isDuplicatePresent) {
                const uniqueMembers = members.filter((member, index, self) =>
                    index === self.findIndex((t) => (
                        t.first_name === member.first_name &&
                        t.last_name === member.last_name &&
                        t.dob === member.dob
                    ))
                );
                setMembers(uniqueMembers);
            }
        }
    }, [members]);

    // To fetch the payment data when the component is mounted
    useEffect(() => {
        if (id && Number(id) !== 0 && transactionStatus) {
            setIsPaymentSuccess(1);
            const paymentType = Number(transactionStatus.PaymentType);
            const basePaymentData = {
                payment_type: paymentType
            };

            // Get payment type label from paymentTypes array
            const selectedPaymentType = paymentTypes.find(type => type.id === paymentType)?.api_name;

            switch (selectedPaymentType) {
                case 'PAY_CASH':
                    setFormData(prevData => ({
                        ...prevData,
                        ...basePaymentData,
                        cash_amount: transactionStatus.cash_amount,
                        received_date: transactionStatus.received_date,
                        ...!formData.start_date && {
                            start_date: transactionStatus.received_date,
                            end_date: dayjs(transactionStatus.received_date).add(formData.tenure, 'year').subtract(1, 'day')
                        }
                    }));
                case 'PAY_CHEQUE':
                    setFormData(prevData => ({
                        ...prevData,
                        ...basePaymentData,
                        cheque_amount: transactionStatus.cheque_amount,
                        cheque_number: transactionStatus.cheque_number,
                        cheque_date: transactionStatus.cheque_date,
                        bank_name: transactionStatus.bank_name || '',
                        branch_name: transactionStatus.branch_name || '',
                        ...!formData.start_date && {
                            start_date: transactionStatus.cheque_date,
                            end_date: dayjs(transactionStatus.cheque_date).add(formData.tenure, 'year').subtract(1, 'day')
                        }
                    }));
                    break;
                case 'PAY_DD':
                    setFormData(prevData => ({
                        ...prevData,
                        ...basePaymentData,
                        dd_amount: transactionStatus.dd_amount,
                        dd_number: transactionStatus.dd_number,
                        dd_date: transactionStatus.dd_date,
                        bank_name: transactionStatus.bank_name || '',
                        branch_name: transactionStatus.branch_name || '',
                        ...!formData.start_date && {
                            start_date: transactionStatus.dd_date,
                            end_date: dayjs(transactionStatus.dd_date).add(formData.tenure, 'year').subtract(1, 'day')
                        }
                    }));
                    break;
                case 'PAY_ONLINE':
                    setFormData(prevData => ({
                        ...prevData,
                        ...basePaymentData,
                        online_amount: transactionStatus.online_amount,
                        transaction_date: transactionStatus.transaction_date,
                        transaction_type: 'Credit Card',
                        receipt_number: transactionStatus.receipt_number || transactionStatus.TransactionID,
                        ...!formData.start_date && {
                            start_date: transactionStatus.transaction_date,
                            end_date: dayjs(transactionStatus.transaction_date).add(formData.tenure, 'year').subtract(1, 'day')
                        }
                    }));
                    break;

                case 'PAY_CHEQUE_CASH':
                    setFormData(prevData => ({
                        ...prevData,
                        ...basePaymentData,
                        cash_amount: transactionStatus.cash_amount || '',
                        received_date: transactionStatus.received_date || '',
                        cheque_amount: transactionStatus.cheque_amount || '',
                        cheque_number: transactionStatus.cheque_number,
                        cheque_date: transactionStatus.cheque_date,
                        bank_name: transactionStatus.bank_name || '',
                        branch_name: transactionStatus.branch_name || '',
                        ...!formData.start_date && {
                            start_date: transactionStatus.cheque_date,
                            end_date: dayjs(transactionStatus.cheque_date).add(formData.tenure, 'year').subtract(1, 'day')
                        }
                    }));
                    break;

                case 'PAY_DD_CASH':
                    setFormData(prevData => ({
                        ...prevData,
                        ...basePaymentData,
                        cash_amount: transactionStatus.cash_amount || '',
                        received_date: transactionStatus.received_date || '',
                        dd_amount: transactionStatus.dd_amount || '',
                        dd_number: transactionStatus.dd_number,
                        dd_date: transactionStatus.dd_date,
                        bank_name: transactionStatus.bank_name || '',
                        branch_name: transactionStatus.branch_name || '',
                        ...!formData.start_date && {
                            start_date: transactionStatus.dd_date,
                            end_date: dayjs(transactionStatus.dd_date).add(formData.tenure, 'year').subtract(1, 'day')
                        }
                    }));
                    break;

                default:
                    console.warn('Unknown payment type:', selectedPaymentType);
                    break;
            }
        }
    }, [transactionStatus]);

    useEffect(() => {
        const family_discount_amount = formData?.family_discount_amount || 0;
        const long_term_discount_amount = formData?.long_term_discount_amount || 0;
        const netPremium = formData?.net_premium - family_discount_amount - long_term_discount_amount;
        const gst_amount = netPremium * 0.18;
        const total_premium = Math.round(netPremium + gst_amount);
        setFormData(prevData => ({
            ...prevData,
            gst_amount: gst_amount,
            total_premium: total_premium
        }));
    }, [formData.net_premium, formData.family_discount_amount, formData.long_term_discount_amount])

    // To fetch the customer data when the customer id changes
    useEffect(() => {
        dispatch(clearCustomerDetails());
        setMembers([]); // Clearing the members array when the customer id changes
        if (formData.customer_id) {
            dispatch(getCustomerById(formData.customer_id)).then(res => {
                const data = res.payload;
                if (data.group_code === '') {
                    toast.error('Please create a group or link to a group for this customer')
                    navigate(`/dashboard/customer-personal-information/${data.id}`)
                    setDialogConfig({
                        title: 'No Group Found',
                        message: 'Please create a group or link to a group for this customer',
                        type: 'group_code',
                        callback: () => {
                            navigate(`/dashboard/customer-grouping/${data.id}`)
                        }
                    });
                }
            });
            dispatch(getMemberByCustomerId(formData.customer_id)).then(res => {
                setAreCustomerMembersFetched(true);
            });
        } else if (formData.customer_id === '') {
            setMembers([]);
            dispatch(clearCustomerMemberDetails());
        }
    }, [formData.customer_id])

    // To add the customer as a member when the customer is present
    useEffect(() => {
        if (customer !== null && questions.length !== 0) {
            if (!id && formData.product_name) {
                handleAddCustomerAsMember();
            } else if (Number(id) === 0 && formData.product_name) {
                if (isCustomerMember) {
                    handleAddCustomerAsMember();
                }
            }
        }
    }, [customer, questions, formData.product_name])

    useEffect(() => {
        if (formData.insurance_company_name && customer) {
            const isMale = genderOptions.find(g => g.id === customer?.gender_id)
                ?.label_name?.toLowerCase() === 'male';

            const defaultSalutation = salutationOptions.find(s =>
                s.api_name === (isMale ? 'MR' : 'MS')
            );

            setFormData({ ...formData, salutation: defaultSalutation?.id || '' });
        }
    }, [customer, formData.insurance_company_name])

    // To add the members from the quotation data when the component is mounted
    useEffect(() => {
        if (membersData.length > 0) {
            if (Number(id) === 0) {
                const data = quotationsData?.[0]?.members || quotationsData;
                data?.forEach(quotation => {
                    const isAlreadyMember = members.find(member => member.member_id === quotation?.member_id);
                    if (!isAlreadyMember) {
                        const memberName = quotation?.member_name?.toLowerCase() || quotation?.insuredName?.toLowerCase();
                        const member = membersData.find(member => member?.full_name?.toLowerCase() === memberName);
                        if (member) {
                            const memberWithQuotation = {
                                ...member,
                                ...((masterProducts.find(product => product.id === Number(formData.product_name))?.product_name.toLowerCase().includes('fg acci')) && {
                                    ad_sum_insured: quotation?.AD_suminsured || 0,
                                    pp_sum_insured: quotation?.PP_suminsured || 0,
                                    pt_sum_insured: quotation?.PT_suminsured || 0,
                                    tt_sum_insured: quotation?.TT_suminsured || 0,
                                    lp_sum_insured: quotation?.LP_suminsured || 0,
                                    ls_sum_insured: quotation?.LS_suminsured || 0,
                                    me_sum_insured: quotation?.ME_suminsured || 0,
                                    am_sum_insured: quotation?.AM_suminsured || 0,
                                    bb_sum_insured: quotation?.BB_suminsured || 0,
                                    rf_sum_insured: quotation?.RF_suminsured || 0,
                                    aa_sum_insured: quotation?.AA_suminsured || 0,
                                    cs_sum_insured: quotation?.CS_suminsured || 0,
                                    hc_sum_insured: quotation?.HC_suminsured || 0,
                                    ft_sum_insured: quotation?.FT_suminsured || 0,
                                    annual_income: quotation?.annual_income || 0,
                                }),
                                customer_member_id: member.id,
                                sum_insured: quotation?.soap_sum_insured || quotation?.member_sum_insured,
                                deductible: quotation?.soap_deductable_discount || quotation?.member_deductable_discount,
                                member_id: quotation?.member_id,
                            }
                            handleAddMember(memberWithQuotation);
                        }
                    }
                });
            } else if (!id && formData.product_name && questions.length !== 0) {
                membersData.forEach(member => {
                    const data = {
                        ...member,
                        customer_member_id: member.id
                    }
                    handleAddMember(data);
                });
            }
        }
    }, [membersData, questions, formData.product_name])

    useEffect(() => {
        if (!id && onlySeniorCitizen && members.length > 0 && !onlySeniorCitizenAsMember) {
            handleRemoveMemberByAge();
        }
    }, [onlySeniorCitizen, members, onlySeniorCitizenAsMember])


    // To fetch the insurance company branches when the insurance company name changes
    useEffect(() => {
        if (formData.insurance_company_name === '') {
            setSalutations([]);
            setFormData(prevData => ({
                ...prevData,
                salutation: ''
            }));
        } else {
            dispatch(getNomineeRelations()).then(response => {
                if (response.payload) {
                    setRelations(response.payload);
                }
            })
            setSalutations(getSalutationOptions(formData.insurance_company_name));
        }
        if (formData.insurance_company_name && (!id || Number(id) === 0)) {
            dispatch(fetchInsuranceCompanyBranchByInsuranceCompanyId(formData.insurance_company_name));
        }
    }, [formData.insurance_company_name])

    // To fetch the agent data when the imf branch changes
    useEffect(() => {
        if ((!id || Number(id) === 0)) {
            if (formData.imf_branch) {
                dispatch(getAgentByBranchName(formData.imf_branch));
            }
        }
    }, [formData.imf_branch])

    // To fetch the master product data when the insurance company name, product type and product name changes
    useEffect(() => {
        if ((!id || Number(id) === 0)) {
            if (formData.insurance_company_name && formData.product_type && !formData.product_name) {
                dispatch(getMasterProductByMainProductAndInsuranceCompany({ insuranceCompanyId: formData.insurance_company_name, mainProductId: formData.product_type }));
            } else if (formData.insurance_company_name && formData.product_type && formData.product_name) {
                if (masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('varishtha bima')) {
                    setOnlySeniorCitizen(true);
                    setOnlySeniorCitizenAsMember(false);
                }
                dispatch(getSubProductByProductDetails({ mainProductId: formData.product_type, insuranceCompanyId: formData.insurance_company_name, productMasterId: formData.product_name }))
            }
        }
    }, [formData.insurance_company_name, formData.product_type, formData.product_name])

    // Use Effect for new proposal from quotation overview
    useEffect(() => {
        if (selectedQuotation && Number(id) === 0 && quotationsData?.length > 0 && agents && !members.length > 0) {
            const yearPlanData = selectedQuotation?.yearPlanData;
            const agent = agents.find(agent => agent.id === Number(quotationsData[0].agent_id));
            dispatch(getMasterProductByMainProductAndInsuranceCompany({
                insuranceCompanyId: Number(quotationsData[0].insurance_company),
                mainProductId: Number(quotationsData[0].main_product)
            })).then(() => {
                dispatch(getAllAgentDetails()).then(() => {
                    dispatch(getSubProductByProductDetails({
                        mainProductId: Number(quotationsData[0].main_product),
                        insuranceCompanyId: Number(quotationsData[0].insurance_company),
                        productMasterId: quotationsData[0].product
                    })).then(() => {
                        setFormData(prevData => ({
                            ...prevData,
                            customer_id: quotationsData[0].customer_id,
                            quotation_number: quotationsData[0].quotation_number,
                            quotation_created_at: quotationsData[0].quotation_created_at,
                            insurance_company_name: Number(quotationsData[0].insurance_company),
                            agent_id: Number(quotationsData[0].agent_id),
                            imf_branch: agent?.branch_id,
                            product_type: Number(quotationsData[0].main_product),
                            member_type: customerInfo?.familyType?.toLowerCase() || '',
                            product_name: quotationsData[0].product,
                            sub_product_name: quotationsData[0].member_cover_type || quotationsData[0].sub_product_id,
                            ...(!yearPlanData && {
                                co_pay: quotationsData[0].coPay === "N" ? false : true,
                            }),
                            net_premium: yearPlanData ? Number(yearPlanData?.full_payment_premium) : Number(totalPremium?.premiumAmount),
                            gst_amount: yearPlanData ? Number(yearPlanData?.full_payment_tax) : Number(totalPremium?.serviceTax),
                            ...(yearPlanData && {
                                family_discount_amount: Number(yearPlanData?.family_discount_amt) || 0,
                                family_discount_percentage: Number(yearPlanData?.family_discount_amt) || 0,
                                long_term_discount_amount: Number(yearPlanData?.long_term_discount_amount) || 0,
                                long_term_discount_percentage: Number(yearPlanData?.long_term_discount_percent) || 0,
                            }),
                            total_premium: yearPlanData ? Number(yearPlanData?.total_full_payment) : Number(totalPremium?.totalPremiumWithServiceTax),
                            tenure: yearPlanData ? yearPlanData.duration : quotationsData[0].duration,
                        }));
                    });
                });
            });
            const relation = quotationsData[0].member_relation || quotationsData[0].members?.[0]?.relation;
            setIsCustomerMember(relation?.toLowerCase() === 'self');
        }
    }, [selectedQuotation, agents])

    // To fetch the imf branch data when the agent id changes    
    useEffect(() => {
        if ((!id || Number(id) === 0) && imfBranches?.length > 0) {
            const agent = agents?.find(agent => agent.id === Number(formData.agent_id)); // Add Number()
            if (agent?.branch_id) { // Add null-safe check
                const branchId = agent.branch_id;
                // Verify branch exists before setting
                if (imfBranches?.some(b => b.id === branchId)) {
                    setFormData(prevData => ({
                        ...prevData,
                        imf_branch: branchId,
                    }));
                }
            }
        }
    }, [formData.agent_id, imfBranches, agents, id]);

    useEffect(() => {
        if (fetchedProposal && !fetchedProposal?.proposal_type === 'RENEW') {
            setQuestions([]);
            setAnswers([]);
            return;
        }
        const fetchedMasterProduct = masterProducts.find(product => product.id === Number(formData.product_name));

        if (fetchedMasterProduct && fetchedMasterProduct.product_name !== 'FG ACCIDENT SURAKSHA') {
            const productName = fetchedMasterProduct.product_name.toUpperCase();

            // Reset questions and answers when product changes
            setQuestions([]);
            setAnswers([]);

            // Common questions for all products
            const commonQuestions = [
                { id: 'nationality', question: 'Are you an Indian resident?' },
                { id: 'generalHealth', question: 'Are you in good health and free from physical or mental diseases, infirmities, or any medical complaints?' },
                { id: 'hospitalization', question: 'Have you ever been hospitalized or undergone any ongoing treatment?' },
                { id: 'surgery', question: 'Have you undergone any surgery in the past, or do you have any planned surgery?' },
                { id: 'accident', question: 'Have you ever suffered an accident or injury that required hospitalization or treatment?' },
                { id: 'declined', question: 'Has any health insurance policy been declined for you in the past?' }
            ];

            // Product specific questions
            const productSpecificQuestions = {
                'FG HEALTH TOTAL': [
                    { id: 'existingHealthTotal', question: 'Do you already have a Health Total policy with Future Generali?' }
                ],
                'FG HEALTH SURAKSHA': [
                    { id: 'pregnancy', question: 'Are you currently pregnant?', onlyForFemale: true, minAge: 25 },
                    { id: 'maternityComplications', question: 'Have you had any past maternity-related complications?', onlyForFemale: true, minAge: 25 },
                    { id: 'existingHealthSuraksha', question: 'Do you already have a Health Suraksha policy with Future Generali?' }
                ],
                'FG HEALTH ABSOLUTE': [
                    { id: 'existingHealthAbsolute', question: 'Do you already have a Health Absolute policy with Future Generali?' }
                ],
                'FG ADVANTAGE TOP UP': [
                    { id: 'existingHealthTotal', question: 'Do you already have a Advantage Top Up policy with Future Generali?' }
                ]
            };

            // Combine common questions with product-specific questions
            const allQuestions = [
                ...commonQuestions,
                ...(productSpecificQuestions[productName] || [])
            ];

            setQuestions(allQuestions);

            // Set default answers
            const defaultAnswers = allQuestions.map(q => ({
                id: q.id,
                answer: (q.id === 'generalHealth' || q.id === 'nationality') ? true : false
            }));

            setAnswers(defaultAnswers);
        }
    }, [formData.product_name, fetchedProposal]);

    useEffect(() => {
        if (fetchedProposal && fetchedProposal?.status === 'CANCELLED') {
            setIsCancelled(true);
        }
    }, [fetchedProposal])

    {/* Helper functions */ }

    // To handle the form validation
    const handleValidateForm = (type) => {
        const errors = {};
        const memberErrors = [];
        let flag = false;
        let toastErrors = [];
        let proposalDecline = false;

        // Define validatePaymentFields inside handleValidateForm to access errors object
        const validatePaymentFields = (paymentType) => {

            const validateBankDetails = () => {
                if (!formData.bank_name) {
                    errors.bank_name = 'Please enter bank name';
                }
                if (!formData.branch_name) {
                    errors.branch_name = 'Please enter branch name';
                }
            };

            // Payment type specific validations
            const validations = {
                PAY_CASH: () => {
                    if (!formData.cash_amount) {
                        errors.cash_amount = 'Please enter cash amount';
                    }
                    if (Number(formData.total_premium) !== Number(formData.cash_amount)) {
                        errors.cash_amount = 'Cash amount must equal total premium';
                    }
                    if (!formData.received_date) {
                        errors.received_date = 'Please select received date';
                    }
                },

                PAY_CHEQUE: () => {
                    if (!formData.cheque_amount) {
                        errors.cheque_amount = 'Please enter cheque amount';
                    }
                    if (Number(formData.total_premium) !== Number(formData.cheque_amount)) {
                        errors.cheque_amount = 'Cheque amount must equal total premium';
                    }
                    if (!formData.cheque_number) {
                        errors.cheque_number = 'Please enter cheque number';
                    }
                    if (!formData.cheque_date) {
                        errors.cheque_date = 'Please select cheque date';
                    }
                    validateBankDetails();
                },

                PAY_DD: () => {
                    if (!formData.dd_amount) {
                        errors.dd_amount = 'Please enter DD amount';
                    }
                    if (Number(formData.total_premium) !== Number(formData.dd_amount)) {
                        errors.dd_amount = 'DD amount must equal total premium';
                    }
                    if (!formData.dd_number) {
                        errors.dd_number = 'Please enter DD number';
                    }
                    if (!formData.dd_date) {
                        errors.dd_date = 'Please select DD date';
                    }
                    validateBankDetails();
                },

                PAY_ONLINE: () => {
                    if (!formData.online_amount) {
                        errors.online_amount = 'Please enter amount';
                    }
                    if (Number(formData.total_premium) !== Number(formData.online_amount)) {
                        errors.online_amount = 'Amount must equal total premium';
                    }
                    if (!formData.transaction_date) {
                        errors.transaction_date = 'Please select transaction date';
                    }
                    if (!formData.transaction_type) {
                        errors.transaction_type = 'Please select transaction type';
                    }
                    if (!formData.receipt_number) {
                        errors.receipt_number = 'Please enter receipt number';
                    }
                },

                PAY_CHEQUE_CASH: () => {
                    if (!formData.cheque_amount) {
                        errors.cheque_amount = 'Please enter cheque amount';
                    }
                    if (!formData.cash_amount) {
                        errors.cash_amount = 'Please enter cash amount';
                    }
                    if (Number(formData.total_premium) !== (Number(formData.cheque_amount) + Number(formData.cash_amount))) {
                        errors.cheque_amount = 'Combined amount must equal total premium';
                        errors.cash_amount = 'Combined amount must equal total premium';
                    }
                    if (!formData.cheque_number) {
                        errors.cheque_number = 'Please enter cheque number';
                    }
                    if (!formData.cheque_date) {
                        errors.cheque_date = 'Please select cheque date';
                    }
                    if (!formData.received_date) {
                        errors.received_date = 'Please select received date';
                    }
                    validateBankDetails();
                },

                PAY_DD_CASH: () => {
                    if (!formData.dd_amount) {
                        errors.dd_amount = 'Please enter DD amount';
                    }
                    if (!formData.cash_amount) {
                        errors.cash_amount = 'Please enter cash amount';
                    }
                    if (Number(formData.total_premium) !== (Number(formData.dd_amount) + Number(formData.cash_amount))) {
                        errors.dd_amount = 'Combined amount must equal total premium';
                        errors.cash_amount = 'Combined amount must equal total premium';
                    }
                    if (!formData.dd_number) {
                        errors.dd_number = 'Please enter DD number';
                    }
                    if (!formData.dd_date) {
                        errors.dd_date = 'Please select DD date';
                    }
                    if (!formData.received_date) {
                        errors.received_date = 'Please select received date';
                    }
                    validateBankDetails();
                }
            };

            // Execute validation for selected payment type
            if (!formData.payment_type) {
                errors.payment_type = 'Please select a payment type';
            } else if (validations[paymentType]) {
                validations[paymentType]();
            }
        };

        const checkPayment = () => {
            if (!formData.net_premium) {
                errors.net_premium = 'Please enter a valid net premium';
            }
            if (!id) {
                const selectedPaymentType = paymentTypes.find(type =>
                    type.id === Number(formData.payment_type)
                )?.api_name;
                validatePaymentFields(selectedPaymentType);
            }
        };

        const checkProposal = () => {
            if (!formData.customer_id) {
                errors.customer_id = 'Please select a customer';
            }
            if (!formData.salutation) {
                errors.salutation = 'Please select a salutation';
            }
            if (!formData.proposal_number) {
                toast.error('Proposal Number Not Found');
                flag = true;
            }
            if (!formData.insurance_company_name) {
                errors.insurance_company_name = 'Please select an insurance company';
            }
            if (!formData.insurance_company_branch) {
                errors.insurance_company_branch = 'Please select an insurance company branch';
            }
            if (!formData.imf_code) {
                errors.imf_code = 'Please reselect the insurance company branch';
            }
            if (!formData.imf_branch) {
                errors.imf_branch = 'Please select an imf branch';
            }
            if (!formData.agent_id) {
                errors.agent_id = 'Please select an agent';
            }
            if (!formData.product_type) {
                errors.product_type = 'Please select a product type';
            }
            if (!formData.product_name) {
                errors.product_name = 'Please select a product';
            }
            if (!formData.sub_product_name) {
                errors.sub_product_name = 'Please select a sub product';
            }
            if (!formData.member_type) {
                errors.member_type = 'Please select a member type';
            }
            if (!formData.net_premium) {
                errors.net_premium = 'Please enter a valid net premium';
            }
            if (!formData.tenure) {
                errors.tenure = 'Please enter a valid tenure';
            }
            if (fetchedProposal && fetchedProposal.proposal_type === 'RENEW') {
                if (!formData.start_date) {
                    errors.start_date = "Please select a start date";
                }
                if (!formData.policy_number) {
                    errors.policy_number = "Please select a policy number";
                }
            }
            if (!id) {
                if (formData.start_date) {
                    if (!formData.policy_issue_date) {
                        errors.policy_issue_date = 'Please select an issue date';
                    }
                    if (!formData.policy_number) {
                        errors.policy_number = 'Please enter a valid policy number';
                    }
                    if (!formData.policy_pdf) {
                        errors.policy_pdf = 'Please upload the policy document';
                    }
                }
            }
            if (id) {
                if (Number(id) !== 0 && Boolean(isPaymentSuccess == 1)) {
                    if (!formData.start_date) {
                        errors.start_date = 'Please select a start date';
                    }
                    if (formData.start_date && !formData.quotation_number) {
                        if (formData.policy_number && !formData.policy_issue_date) {
                            errors.policy_issue_date = 'Please select an issue date';
                        }
                        if (formData.policy_issue_date && !formData.policy_number) {
                            errors.policy_number = 'Please enter a valid policy number';
                        }
                        if (formData.policy_issue_date && !formData.policy_pdf) {
                            errors.policy_pdf = 'Please enter valid policy document';
                        }
                    }
                }
            }
        }
        const checkMember = () => {
            members.forEach((member, index) => {
                const errors = {};

                // Required field validations
                const masterProduct = masterProducts.find(product => product.id === formData.product_name);
                if (masterProduct?.product_name?.toLowerCase()?.includes('fg accident suraksha')) {
                    if (!member.occupation) {
                        errors.occupation = 'Please enter occupation';
                    }
                    if (!member.ad_sum_insured && member.ad_sum_insured !== 0) {
                        errors.ad_sum_insured = 'Please enter sum insured';
                    }
                    if (!member.pp_sum_insured && member.pp_sum_insured !== 0) {
                        errors.pp_sum_insured = 'Please enter sum insured';
                    }
                    if (!member.pt_sum_insured && member.pt_sum_insured !== 0) {
                        errors.pt_sum_insured = 'Please enter sum insured';
                    }
                    if (!member.tt_sum_insured && member.tt_sum_insured !== 0) {
                        errors.tt_sum_insured = 'Please enter sum insured';
                    }
                } else {
                    if (!member.sum_insured) {
                        errors.sum_insured = 'Please enter sum insured';
                    }
                }
                if (!member.health_declaration) {
                    if (fetchedProposal && fetchedProposal.proposal_type === 'RENEW') {
                        return;
                    }
                    const masterProduct = masterProducts.find(product => product.id === formData.product_name)?.product_name;
                    if (!masterProduct?.toLowerCase()?.includes('fg accident suraksha')) {
                        toastErrors.push('Health Declaration');
                    }
                }
                if (member.health_declaration) {
                    const data = member.health_declaration;
                    const incompleteSection = false;
                    Object.keys(data).forEach(element => {
                        const ans = answers.find(item => item.id === element);
                        if (data[element] !== ans?.answer) {
                            proposalDecline = true;
                            return;
                        }
                        if (data[element] === null) {
                            incompleteSection = true;
                        }
                    });
                    const masterProduct = masterProducts.find(product => product.id === formData.product_name)?.product_name;
                    const length = masterProduct?.toLowerCase().includes('fg health suraksha') ? (member.gender === 'F' && calculateAge(member.dob) >= 25) ? questions.length : questions.length - 2 : questions.length;
                    if (incompleteSection || length !== Object.keys(data).length) {
                        toastErrors.push('Health Declaration');
                    }
                }
                // Nominee validations
                if (!member.nominee.name) {
                    errors.nominee_name = 'Please enter nominee name';
                }
                if (!member.nominee.gender) {
                    errors.nominee_gender = 'Please select nominee gender';
                }
                if (!member.nominee.dob) {
                    errors.nominee_dob = 'Please select nominee date of birth';
                }
                if (!member.nominee.relation) {
                    errors.nominee_relation = 'Please select nominee relation';
                }
                if (member.nominee.dob && dayjs().diff(dayjs(member.nominee.dob), 'year') < 18) {
                    if (!member.appointee.name) {
                        errors.appointee_name = 'Please enter appointee name';
                    }
                    if (!member.appointee.gender) {
                        errors.appointee_gender = 'Please select appointee gender';
                    }
                    if (!member.appointee.dob) {
                        errors.appointee_dob = 'Please select appointee date of birth';
                    }
                    if (!member.appointee.relation) {
                        errors.appointee_relation = 'Please select appointee relation';
                    }
                }
                if (Object.keys(errors).length > 0) {
                    memberErrors[index] = errors;
                }
            });

        };

        if (type === 'Payment') {
            checkPayment();
        } else if (type === 'Proposal') {
            checkProposal();
            checkMember();
        } else if (type === 'Both') {
            checkPayment();
            checkProposal();
            if (members.length === 0) {
                toast.error('Please add at least one member');
            } else {
                checkMember();
            }
        }

        if (proposalDecline || flag) {
            toast.error(proposalDecline ? "Regret, policy cannot be issued online. Please contact our nearest branch" : 'Please refresh the page and try again')
            return false;
        } else if (toastErrors.length > 0) {
            toast.error(`Please fill the ${toastErrors.join(', ')}`);
        } else if (Object.keys(errors).length > 0 || memberErrors.length > 0) {
            if (memberErrors.length > 0) {
                toast.error('Please fill the member details');
            }
            if (Object.keys(errors).length > 0) {
                toast.error('Please check the form for errors');
            }
            setFormErrors(errors);
            setMemberErrors(memberErrors);
        }
        return toastErrors.length === 0 && memberErrors.length === 0 && Object.keys(errors).length === 0;
    };

    // To get the payment details
    const handlePaymentDetails = (checkingPayment) => {
        dispatch(checkIfSuccess({ proposal_number: fetchedProposal.ProposalNumber }))
            .then(res => {
                const data = res.payload;
                setTransactionId(data?.TransactionID);
                if (data.Status === 'EMAIL_SENT') {
                    if (checkingPayment) {
                        const createdAt = dayjs(data.created_at);
                        const updatedAt = dayjs(data.updated_at);

                        // Get time difference in SECONDS
                        const timeDifference = updatedAt.diff(createdAt, 'hour');

                        if (timeDifference >= 24) {
                            setIsPaymentSuccess(0);
                            toast.error('Payment session has expired. Please try again.');
                        }
                    } else {
                        setIsPaymentSuccess(2);
                    }
                } else if (data.Status === 'SUCCESS') {
                    setTransactionStatus(data);
                }
            })
            .catch(error => {
                console.error('Error fetching payment details:', error);
            });
    };

    const handleAddCustomerAsMember = () => {
        const qData = quotationsData?.[0]?.members || quotationsData;

        // Create default health declaration answers
        const defaultHealthDeclaration = questions.reduce((acc, question) => {
            // Set all answers to match default answers pattern
            const defaultValue = answers.find(answer => answer.id === question.id)?.answer || false;

            // Only add questions applicable to the member
            if (question.onlyForFemale && genderOptions.find(gender => gender.id === customer.gender_id)?.api_name !== 'F') {
                return acc;
            }
            if (question.minAge && calculateAge(customer.date_of_birth) < question.minAge) {
                return acc;
            }

            return {
                ...acc,
                [question.id]: defaultValue
            };
        }, {});

        const data = {
            first_name: customer.first_name || '',
            middle_name: customer.middle_name || '',
            last_name: customer.last_name || '',
            dob: formatDate(customer.date_of_birth) || '',
            gender: genderOptions.find(gender => gender.id === customer.gender_id)?.api_name || '',
            relation_id: relationOptions.find(relation => relation?.label_name?.toLowerCase() === 'self')?.id || '',
            marital_status: customer.marital_status_id || '',
            marriage_date: formatDate(customer.marriage_date) || '',
            member_id: qData[0]?.member_id || '',
            mobile_number: customer.mobile || '',
            email_id: customer.email || '',
            health_declaration: defaultHealthDeclaration, // Add default health declaration matching answers pattern
            ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase() === 'fg accident suraksha') ? {
                occupation: customer.occupation,
                ad_sum_insured: qData[0]?.AD_suminsured || 0,
                pp_sum_insured: qData[0]?.PP_suminsured || 0,
                pt_sum_insured: qData[0]?.PT_suminsured || 0,
                tt_sum_insured: qData[0]?.TT_suminsured || 0,
                lp_sum_insured: qData[0]?.LP_suminsured || 0,
                ls_sum_insured: qData[0]?.LS_suminsured || 0,
                me_sum_insured: qData[0]?.ME_suminsured || 0,
                am_sum_insured: qData[0]?.AM_suminsured || 0,
                bb_sum_insured: qData[0]?.BB_suminsured || 0,
                rf_sum_insured: qData[0]?.RF_suminsured || 0,
                aa_sum_insured: qData[0]?.AA_suminsured || 0,
                cs_sum_insured: qData[0]?.CS_suminsured || 0,
                hc_sum_insured: qData[0]?.HC_suminsured || 0,
                ft_sum_insured: qData[0]?.FT_suminsured || 0,
                annual_income: qData[0]?.annual_income || 0,
            } : {
                deductible: Math.round(Number(qData?.[0]?.soap_deductable_discount)) || Math.round(Number(qData?.[0]?.member_deductable_discount)),
                sum_insured: Math.round(Number(qData?.[0]?.soap_sum_insured)) || Math.round(Number(qData?.[0]?.member_sum_insured)),
            }),
            nominee: {
                name: "",
                gender: "",
                dob: null,
                relation: null
            },
            appointee: {
                name: '',
                gender: '',
                dob: null,
                relation: null
            }
        }
        setMembers(prevMembers => [...prevMembers, data]);
    }

    // To Add a new member
    const handleAddMember = (member) => {
        const name = member?.full_name?.split(' ');
        const lastname = name?.length > 1 ? name[name.length - 1] : '';

        // Create default health declaration answers
        const defaultHealthDeclaration = questions.reduce((acc, question) => {
            // Set all answers as true except hospitalization, surgery, accident and declined
            const defaultValue = ['nationality', 'generalHealth'].includes(question.id)
                ? true
                : false;

            // Only add questions applicable to the member
            if (question.onlyForFemale && genderOptions.find(gender => gender.id === member.gender_id)?.api_name !== 'F') {
                return acc;
            }
            if (question.minAge && calculateAge(member.date_of_birth) < question.minAge) {
                return acc;
            }

            return {
                ...acc,
                [question.id]: defaultValue
            };
        }, {});

        setMembers(prevMembers => [
            ...prevMembers,
            {
                first_name: name?.[0],
                middle_name: name?.length > 2 ? name?.[1] : '',
                last_name: lastname,
                customer_member_id: member.customer_member_id,
                dob: formatDate(member.date_of_birth) || '',
                relation_id: member.relation_id || '',
                gender: genderOptions.find(gender => gender.id === member.gender_id)?.api_name || '',
                marital_status: member.marital_status_id || '',
                marriage_date: formatDate(member.marriage_date) || '',
                member_id: member.member_id || '',
                mobile_number: member.mobile || '',
                email_id: member.email || '',
                health_declaration: defaultHealthDeclaration, // Add default health declaration
                ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase() === 'fg accident suraksha') ? {
                    occupation: member?.member_occupation,
                    ad_sum_insured: Math.round(Number(member?.ad_sum_insured)) || 0,
                    pp_sum_insured: Math.round(Number(member?.pp_sum_insured)) || 0,
                    pt_sum_insured: Math.round(Number(member?.pt_sum_insured)) || 0,
                    tt_sum_insured: Math.round(Number(member?.tt_sum_insured)) || 0,
                    lp_sum_insured: Math.round(Number(member?.lp_sum_insured)) || 0,
                    ls_sum_insured: Math.round(Number(member?.ls_sum_insured)) || 0,
                    me_sum_insured: Math.round(Number(member?.me_sum_insured)) || 0,
                    am_sum_insured: Math.round(Number(member?.am_sum_insured)) || 0,
                    bb_sum_insured: Math.round(Number(member?.bb_sum_insured)) || 0,
                    rf_sum_insured: Math.round(Number(member?.rf_sum_insured)) || 0,
                    aa_sum_insured: Math.round(Number(member?.aa_sum_insured)) || 0,
                    cs_sum_insured: Math.round(Number(member?.cs_sum_insured)) || 0,
                    hc_sum_insured: Math.round(Number(member?.hc_sum_insured)) || 0,
                    ft_sum_insured: Math.round(Number(member?.ft_sum_insured)) || 0,
                    annual_income: Math.round(Number(member?.annual_income)) || 0,
                } : {
                    sum_insured: Math.round(Number(member?.sum_insured)) || null,
                    deductible: Math.round(Number(member?.deductible)) || null,
                }),
                nominee: {
                    name: "",
                    gender: "",
                    dob: null,
                    relation: null
                },
                appointee: {
                    name: '',
                    gender: '',
                    dob: null,
                    relation: null
                }
            }
        ]);
    }

    // To handle the save event
    const handleSave = () => {
        const validate = handleValidateForm('Both');
        if (!validate) {
            return;
        }
        try {
            const membersDataToSend = members?.map(member => {
                return {
                    ...(!id && {
                        customer_member_id: member.customer_member_id,
                        relation: member.relation_id,
                        sum_insured: member.sum_insured,
                        deductible: member.deductible,
                        status: 'PENDING',
                        created_by: formData.created_by
                    }),
                    ...(id && {
                        id: member.id,
                    }),
                    ...(member.health_declaration && {
                        health_declaration: getHealthDeclarationString(member.health_declaration)
                    }),
                    nominee_name: member.nominee?.name,
                    nominee_gender: member.nominee?.gender,
                    nominee_dob: member.nominee?.dob,
                    nominee_relation: member.nominee?.relation,
                    appointee_name: member.appointee?.name,
                    appointee_gender: member.appointee?.gender,
                    appointee_dob: member.appointee?.dob,
                    appointee_relation: member.appointee?.relation
                }
            });
            const subProductForSubmit = subProducts.find(subProduct => subProduct.id === formData.sub_product_name);
            const subProductLabel = subProductForSubmit.sub_product_name;
            const coverTypeForSubmit = subProductLabel ? (['SUPREME', 'ELITE'].includes(subProductLabel.toUpperCase()) ?
                subProductLabel.charAt(0).toUpperCase() + subProductLabel.slice(1) :
                subProductLabel.toUpperCase()) : '';
            const membersDataToSubmit = members?.map(member => ({
                member_id: member.member_id,
                coverType: coverTypeForSubmit,
                ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase() === 'fg accident suraksha') ? {
                    ad_sum_insured: member?.ad_sum_insured,
                    pp_sum_insured: member?.pp_sum_insured,
                    pt_sum_insured: member?.pt_sum_insured,
                    tt_sum_insured: member?.tt_sum_insured,
                    lp_sum_insured: member?.lp_sum_insured,
                    ls_sum_insured: member?.ls_sum_insured,
                    me_sum_insured: member?.me_sum_insured,
                    am_sum_insured: member?.am_sum_insured,
                    bb_sum_insured: member?.bb_sum_insured,
                    rf_sum_insured: member?.rf_sum_insured,
                    aa_sum_insured: member?.aa_sum_insured,
                    cs_sum_insured: member?.cs_sum_insured,
                    hc_sum_insured: member?.hc_sum_insured,
                    ft_sum_insured: member?.ft_sum_insured,
                    annual_income: member?.annual_income,
                } : {
                    sum_insured: member?.sum_insured,
                    deductible: member?.deductible,
                }),
                relation: Number(member.relation_id),
                nominee_name: member.nominee.name,
                nominee_gender: Number(member.nominee.gender),
                nominee_dob: member.nominee.dob,
                nominee_relation: Number(member.nominee.relation),
                appointee_name: member.appointee.name,
                appointee_gender: Number(member.appointee.gender),
                appointee_dob: member.appointee.dob,
                appointee_relation: Number(member.appointee.relation)
            }))
            const proposalDataToSend = {
                ...(!id && {
                    customer_id: formData.customer_id,
                    customer_salutation: formData.salutation,
                    ProposalNumber: formData.proposal_number,
                    insurance_company: formData.insurance_company_name,
                    insurance_branch: formData.insurance_company_branch,
                    imf_code: formData.imf_code,
                    imf_branch: formData.imf_branch,
                    agent_code: formData.agent_id,
                    product_type: formData.product_type,
                    product_name: formData.product_name,
                    sub_product: formData.sub_product_name,
                    member_type: formData.member_type,
                    co_pay: formData.co_pay,
                    net_premium: formData.net_premium,
                    gst_amount: formData.gst_amount,
                    total_premium: formData.total_premium,
                    tenure: formData.tenure,
                    start_date: formData.start_date,
                    end_date: formData.end_date,
                    policy_issue_date: formData.issue_date,
                    policy_number: formData.policy_number,
                    policy_pdf: formData.policy_pdf,
                    remarks: formData.remarks || "",
                    status: 'SUCCESS',
                    proposal_type: 'NEW',
                    Created_by: formData.created_by,
                }),
                ...(id && {
                    policy_issue_date: formData.policy_issue_date,
                    customer_salutation: formData.salutation,
                    policy_number: formData.policy_number,
                    policy_pdf: formData.policy_pdf,
                    start_date: formData.start_date,
                    end_date: formData.end_date,
                    ...(formData.family_discount_amount && {
                        family_discount_amount: formData.family_discount_amount,
                        family_discount_percentage: formData.family_discount_percentage,
                    }),
                    ...(formData.long_term_discount_amount && {
                        long_term_discount_amount: formData.long_term_discount_amount,
                        long_term_discount_percentage: formData.long_term_discount_percentage,
                    })
                })
            }
            const selectedBranch = insuranceBranches.find(branch => branch.id === formData.insurance_company_branch);
            const proposalDataForSubmit = {
                customer_id: formData.customer_id,
                salutation: formData.salutation,
                imf_code: formData.imf_code,
                branch_code: selectedBranch?.branch_code,
                quotation_number: formData.quotation_number,
                proposal_number: formData.proposal_number,
                members: membersDataToSubmit
            }
            const formPayload = new FormData();

            if (formData.policy_pdf?.image instanceof File) {
                // “policy_pdf” must match the name your multer.fields() is watching
                formPayload.append(
                    'policy_pdf',
                    formData.policy_pdf.image,
                    formData.policy_number
                );
            }


            // Structure main data
            formPayload.append("data", JSON.stringify({
                proposalData: proposalDataToSend,
                memberData: membersDataToSend
            }));

            if (id) {
                dispatch(updateProposal({ id: id, formData: formPayload })).then((res) => {
                    if (formData.quotation_number) {
                        setLoading(true);
                        setReloadNeeded(true);
                        const masterProduct = masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase();
                        if (fetchedProposal?.start_date || masterProduct === 'fg varishtha bima' || masterProduct === 'fg accident suraksha') {
                            dispatch(submitPolicy(proposalDataForSubmit)).then((response) => {
                                if (response.payload) {
                                    const responseData = response.payload?.data || response.payload;
                                    const error = responseData?.error?.message || responseData?.policy?.message || responseData?.client?.errorMessage || responseData?.receipt?.errorMessage;
                                    const policyNumber = responseData?.policy.policyNo;
                                    if (policyNumber) {
                                        handleGetPolicyPdf(policyNumber);
                                        toast.success('Policy synced successfully this is your policy number: ' + policyNumber);
                                    } else {
                                        toast.error(error);
                                    }
                                    navigate('/dashboard/proposals');
                                }
                            }).finally(() => setLoading(false));
                        }
                        else {
                            dispatch(submitProposal(proposalDataForSubmit)).finally(() => {
                                window.location.reload();
                            }).finally(setLoading(false));
                        }
                    } else {
                        navigate('/dashboard/proposals');
                    }
                })
            } else {
                setLoading(true);
                dispatch(createProposal(formPayload)).then((res) => {
                    if (res.payload) {
                        navigate('/dashboard/proposals');
                    }
                }).finally(() => setLoading(false))
            }

        } catch (error) {
            console.error('Error in handleSave:', error);
            toast.error('An unexpected error occurred');
        }
    }

    const handlePaymentCheck = async () => {
        const checkPayment = await dispatch(checkPaymentStatus(transactionId));
        const checkPaymentData = checkPayment?.payload;
        if (checkPaymentData.success === false) {
            toast.error(checkPaymentData.message || 'Payment not completed yet please check you email.');
            handlePaymentDetails(true);
        }
        if (checkPaymentData.success === true) {
            handlePaymentDetails(false);
            return;
        }
    }

    const handleCkycRedo = async () => {
        // Call checkCKYC API again to get new details
        const gender = genderOptions.find(gender => gender.id === customer.gender_id)?.label_name;
        const newCkycData = {
            req_id: "",
            proposal_no: "",
            customer_type: customerInfo?.familyType?.toLowerCase() === 'individual' ? 'I' : 'F',
            id_type: "PAN",
            id_num: customer.pan_number,
            dob: formatDate(customer.date_of_birth),
            full_name: customer.first_name + (customer.middle_name ? " " + customer.middle_name + " " : " ") + customer.last_name,
            gender: gender === 'Male' ? 'M' : gender === 'Female' ? 'F' : 'O',
            url_type: "URL",
            system_name: process.env.REACT_APP_SYSTEM_NAME,
            user_id: ""
        };
        setLoading(true);
        const newCkycResponse = await dispatch(checkForCkyc(newCkycData));
        const newData = newCkycResponse.payload;

        // Update form data with new CKYC details
        setFormData(prev => ({
            ...prev,
            ckyc_number: newData?.result?.ckyc_number,
            proposal_id: newData?.proposal_id,
            ckyc_link: newData?.url,
            ckyc_expiry_date: newData?.url_expiry
        }));

        // Update proposal with new CKYC details if we have an existing proposal
        if (id) {
            const updateData = {
                ckyc_number: newData?.result?.ckyc_number,
                proposal_id: newData?.proposal_id,
                ckyc_link: newData?.url,
                ckyc_expiry_date: newData?.url_expiry
            };
            setLoading(true);
            const formPayload = new FormData();
            // Structure main data
            formPayload.append("data", JSON.stringify({
                proposalData: updateData,
                memberData: []
            }));
            await dispatch(updateProposal({
                id: id,
                formData: formPayload
            })).finally(() => setLoading(false));
        }

        if (!newData?.result?.ckyc_number) {
            setLoading(false);
            setDialogConfig({
                title: 'Please complete your KYC verification before proceeding.',
                message: 'Keep your Photo ID Proof, Address Proof and PAN Card/Form 16A ready to upload in the link provided.',
                type: 'ckyc',
                callback: () => {
                    const ckycData = {
                        kycReqNo: newData?.proposal_id,
                        icKycNo: newData?.proposal_id,
                        returnUrl: `${baseUrl}/dashboard/ckyc-form`,
                        kycUrl: newData?.url
                    }
                    handleCKYCForm(ckycData);
                }
            });
            setOpenDialog(true);
        }
    }


    // To handle the payment
    const handlePayment = async () => {
        const validate = handleValidateForm('Both');
        if (!validate) {
            return;
        }

        try {
            // Only perform CKYC status check if no CKYC number exists
            if (id && !formData.ckyc_number) {
                if ((!formData.proposal_id || (!formData.proposal_id && !formData.ckyc_number)) && (id && Number(id) !== 0)) {
                    handleCkycRedo();
                    return;
                }
                // Add CKYC status check before proceeding
                const ckycStatusData = {
                    proposal_id: formData.proposal_id
                };
                const response = await dispatch(checkCKYCStatus(ckycStatusData));

                const final_status = response.payload?.Final_Status;

                // Only proceed if final_status is 1 or 3
                if ((!final_status || final_status === '0') && id) {
                    toast.error('Please complete your KYC verification before proceeding with payment');
                    return;
                }

                // Verify customer details if status is 1 or 3
                if (final_status === '1' || final_status === '3') {
                    const ckycData = response.payload?.result;

                    const customerDob = dayjs(customer.date_of_birth).format('DD-MM-YYYY');
                    const ckycDob = ckycData?.dob;

                    // Split names into words and filter out empty strings
                    const customerNameWords = `${customer.first_name}${customer.middle_name ? ' ' + customer.middle_name + ' ' : ' '}${customer.last_name}`
                        .toUpperCase()
                        .split(/\s+/)
                        .filter(word => word.length > 0);

                    // Define all possible titles and their variations
                    const titles = [
                        // Military ranks
                        'ADM', 'ADMIRAL',
                        'ADV', 'ADVOCATE',
                        'BRIG', 'BRIGADIER',
                        'CDR', 'COMMANDER',
                        'COL', 'COLONEL',
                        'LT', 'LIEUTENANT',
                        'MAJ', 'MAJOR',

                        // Professional titles
                        'DR', 'DOCTOR',
                        'PROF', 'PROFESSOR',

                        // Traditional titles
                        'SHRI', 'SH',
                        'SMT', 'SHRIMATI', 'SHREEMATI',

                        // Common titles
                        'MAST', 'MASTER',
                        'MR', 'MISTER',
                        'MRS', 'MISTRESS',
                        'MISS',
                        'MS',
                        'SIR'
                    ];

                    const titleRegex = new RegExp(`^(${titles.join('|')})\\s+`, 'i');

                    const ckycNameWords = ckycData?.customer_name
                        .replace(titleRegex, '')
                        .toUpperCase()
                        .split(/\s+/)
                        .filter(word => word.length > 0);

                    // Compare arrays of words
                    const namesMatch = customerNameWords?.length === ckycNameWords?.length &&
                        customerNameWords?.every((word, index) => word === ckycNameWords[index]);

                    if (!namesMatch && !ckycDob) {
                        toast.error('Please complete your ckyc verification before proceeding with payment');
                        return;
                    }
                    if (customerDob !== ckycDob || !namesMatch) {
                        let mismatchData = !namesMatch ? 'Customer name' : 'Customer dob';
                        toast.error(`${mismatchData} do not match with CKYC records. Initiating new CKYC verification.`);

                        // Call checkCKYC API again to get new details
                        handleCkycRedo();
                        return;
                    }
                }
            }

            const fetchedPayment = await dispatch(getPaymentByProposalNumber(formData.proposal_number));
            const fetchedPaymentData = fetchedPayment?.payload;

            // Prepare payment data
            let paymentData;

            const todaysDate = dayjs().format('DDMMYYYYhhmm');
            const transactionId = Math.floor(Math.random() * 100000000).toString() + todaysDate.toString();
            const userId = Math.floor(Math.random() * 90000000 + 10000000).toString();

            paymentData = {
                amount: formData.total_premium,
                firstName: customer.first_name,
                lastName: customer.last_name,
                mobile: customer.mobile,
                email: customer.email,
                proposal_number: formData.proposal_number,
                TransactionID: transactionId,
                PaymentOption: "3",
                ResponseURL: `${baseUrl}/dashboard/payment-form`,
                UserIdentifier: "webagg",
                UserId: userId,
                Vendor: "1",
                Status: !id ? "SUCCESS" : "PENDING",
            };

            // Generate checksum
            const checksum = generateChecksum({
                TransactionID: paymentData.TransactionID,
                PaymentOption: paymentData.PaymentOption,
                ResponseURL: paymentData.ResponseURL,
                ProposalNumber: paymentData.proposal_number,
                PremiumAmount: paymentData.amount,
                UserIdentifier: paymentData.UserIdentifier,
                UserId: paymentData.UserId,
                FirstName: paymentData.firstName.trim(),
                LastName: paymentData.lastName.trim(),
                Mobile: paymentData.mobile.trim(),
                Email: paymentData.email.trim(),
                Vendor: paymentData.Vendor
            });

            // Add checksum to payment data
            paymentData.CheckSum = checksum;

            const tempPaymentCreationData = getPaymentTypeData(
                formData.payment_type ?
                    paymentTypes.find(paymentType => paymentType.id === Number(formData.payment_type)) :
                    paymentTypes.find(paymentType => paymentType?.api_name?.toUpperCase() === 'PAY_ONLINE')
            );

            const paymentType = paymentTypes.find(paymentType => tempPaymentCreationData.PaymentType === paymentType.id);
            const paymentCreationData = {
                ...tempPaymentCreationData,
                ...(paymentType?.label_name?.toUpperCase() === 'ONLINE' && {
                    CheckSum: checksum,
                    PaymentOption: "3",
                    ResponseURL: `${baseUrl}/dashboard/payment-form`,
                    TransactionID: transactionId,
                    UserIdentifier: "webagg",
                    UserId: userId,
                    Vendor: "1",
                    Status: !id ? "SUCCESS" : "PENDING",
                }),
            }
            setLoading(true);
            if (id) {
                paymentData.id = fetchedPaymentData?.id;
                const recreatedPaymentData = {
                    ...paymentData,
                    CheckSum: fetchedPaymentData?.CheckSum,
                    Status: fetchedPaymentData?.Status,
                    TransactionID: fetchedPaymentData?.TransactionID,
                }
                dispatch(setPaymentDetails(recreatedPaymentData));
                if (Number(id) !== 0) {
                    dispatch(setCurrentProposalId(id));
                }
                setLoading(false);
            } else {
                await dispatch(createPayment(paymentCreationData)).then((response) => {
                    paymentData.id = response.payload.data;
                    dispatch(setPaymentDetails(paymentData));
                    if (Number(id) !== 0) {
                        dispatch(setCurrentProposalId(id));
                    }
                }).finally(() => setLoading(false))
            }
            if (paymentData.id) {
                // Generate random transaction ID and user ID
                if (fetchedProposal === null) {
                    // Prepare proposal data
                    const proposalData = {
                        ...((Number(id) === 0 || !id) && {
                            ...((Number(id) === 0) && {
                                customer_member_id: formData.customer_member_id,
                                quotation_id: quotationsData[0]?.quotation_id || quotationsData[0]?.pa_quotation_id,
                                quotation_number: quotationsData[0]?.quotation_number || '',
                                proposal_Id: formData.proposal_id,
                                ckyc_number: formData.ckyc_number,
                                ckyc_link: formData.ckyc_link,
                                ckyc_expiry_date: formData.ckyc_expiry_date,
                            }),
                            customer_id: formData.customer_id,
                            customer_salutation: formData.salutation,
                            ProposalNumber: formData.proposal_number,
                            insurance_company: formData.insurance_company_name,
                            insurance_branch: formData.insurance_company_branch,
                            imf_code: formData.imf_code,
                            imf_branch: formData.imf_branch,
                            agent_code: formData.agent_id,
                            product_type: formData.product_type,
                            product_name: formData.product_name,
                            member_type: formData.member_type,
                            co_pay: formData.co_pay,
                            sub_product: formData.sub_product_name,
                            start_date: formData.start_date,
                            end_date: formData.end_date,
                            net_premium: formData.net_premium,
                            gst_amount: formData.gst_amount,
                            ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) && {
                                family_discount_amount: formData.family_discount_amount || 0,
                                family_discount_percentage: formData.family_discount_percentage || 0,
                            }),
                            ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) && {
                                long_term_discount_amount: formData.long_term_discount_amount || 0,
                                long_term_discount_percentage: formData.long_term_discount_percentage || 0,
                            }),
                            total_premium: formData.total_premium,
                            tenure: formData.tenure,
                            policy_issue_date: formData.policy_issue_date,
                            policy_number: formData.policy_number,
                            remarks: formData.remarks,
                            status: id ? 'PENDING' : formData.policy_number ? 'SUCCESS' : 'PENDING',
                            proposal_type: 'NEW',
                            Created_by: formData.created_by,
                        }),
                    }
                    const memberData = members?.map(member => ({
                        ...((Number(id) === 0 || !id) && {
                            ...(Number(id) === 0 && {
                                member_id: member.member_id,
                            }),
                            customer_member_id: member.customer_member_id,
                            relation: member.relation_id,
                            ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) ? {
                                occupation: member?.occupation,
                                ad_sum_insured: member?.ad_sum_insured,
                                pp_sum_insured: member?.pp_sum_insured,
                                pt_sum_insured: member?.pt_sum_insured,
                                tt_sum_insured: member?.tt_sum_insured,
                                lp_sum_insured: member?.lp_sum_insured,
                                ls_sum_insured: member?.ls_sum_insured,
                                me_sum_insured: member?.me_sum_insured,
                                am_sum_insured: member?.am_sum_insured,
                                bb_sum_insured: member?.bb_sum_insured,
                                rf_sum_insured: member?.rf_sum_insured,
                                aa_sum_insured: member?.aa_sum_insured,
                                cs_sum_insured: member?.cs_sum_insured,
                                hc_sum_insured: member?.hc_sum_insured,
                                ft_sum_insured: member?.ft_sum_insured,
                                annual_income: member?.annual_income,
                            } : {
                                sum_insured: member.sum_insured || null,
                                deductible: member.deductible || null,
                            }),
                            status: 'PENDING',
                            Created_by: formData.created_by,
                        }),
                        ...(member.health_declaration && {
                            health_declaration: getHealthDeclarationString(member.health_declaration)
                        }),

                        nominee_name: member.nominee?.name,
                        nominee_gender: member.nominee?.gender,
                        nominee_dob: member.nominee?.dob || '',
                        nominee_relation: member.nominee?.relation,
                        appointee_name: member.appointee?.name,
                        appointee_gender: member.appointee?.gender || '',
                        appointee_dob: member.appointee?.dob || '',
                        appointee_relation: member.appointee?.relation
                    }))
                    const formPayload = new FormData();

                    if (formData.policy_pdf?.image instanceof File) {
                        // “policy_pdf” must match the name your multer.fields() is watching
                        formPayload.append(
                            'policy_pdf',
                            formData.policy_pdf.image,
                            formData.policy_number
                        );
                    }


                    // Structure main data
                    formPayload.append("data", JSON.stringify({
                        proposalData: proposalData,
                        memberData: memberData
                    }));
                    if (Number(id) === 0 || !id) {
                        setLoading(true);
                        dispatch(createProposal(formPayload)).then((response) => {
                            if (Number(id) === 0) {
                                dispatch(setCurrentProposalId(response.payload.proposalId));
                                navigate('/dashboard/payment-form');
                            } else {
                                navigate('/dashboard/proposals');
                            }
                        }).finally(() => setLoading(false));
                    }
                    else {
                        setLoading(true);
                        dispatch(updateProposal({ id: fetchedProposal.id, formData: formPayload })).then(() => {
                            if (fetchedProposal === null) {
                                navigate('/dashboard/payment-form');
                            }
                        }).finally(() => setLoading(false));
                    }
                } else {
                    navigate('/dashboard/payment-form');
                }
            } else {
                // navigate('/dashboard/payment-form');
                toast.error('Failed to create proposal. Please try again.');
            }
        } catch (error) {
            console.error('Payment processing error:', error);

            toast.error(error.message || 'Failed to process payment. Please try again.');

            // Additional error logging for development
            if (process.env.NODE_ENV === 'development') {
                console.debug('Payment error details:', {
                    formData,
                    formErrors,
                    memberErrors,
                    customer,
                    members,
                });
            }
            return false; // Indicate payment processing failed
        }
        return true; // Indicate payment processing succeeded
    }

    // To handle the cleanup of the related data
    const handleCleanup = () => {
        dispatch(clearProposalDetails());
        dispatch(clearCustomerMemberDetails());
        dispatch(clearSubProduct());
        dispatch(clearInsuranceBranch());
        dispatch(clearProductMaster());
        dispatch(clearImfBranch());
        dispatch(clearPaymentDetails());
        setFormData({});
        setMembers([]);
        setMemberErrors([]);
        setFormErrors({});
    }

    // To handle the cancel event
    const handleCancel = () => {
        dispatch(clearSelectedQuotation());
        navigate('/dashboard/proposals')
    }

    // To handle changes in the normal dropdowns and text fields
    const handleChange = (e) => {
        const { name, value } = e.target;
        if (name === 'tenure') {
            if (formData.start_date) {
                // Calculate end date based on new tenure
                const endDate = dayjs(formData.start_date).add(Number(value), 'year');
                setFormData(prev => ({
                    ...prev,
                    tenure: value,
                    end_date: endDate,
                }));
            } else {
                setFormData(prev => ({
                    ...prev,
                    tenure: value
                }));
            }
            setFormErrors(prevErrors => ({ ...prevErrors, [name]: '' }));
            return;
        }

        // Check if field name contains 'amount' or specific premium/number fields
        if (name.toLowerCase().includes('amount') ||
            ['net_premium', 'gst_amount', 'gst_percentage', 'sum_insured', 'family_discount_amount', 'long_term_discount_amount', 'family_discount_percentage', 'long_term_discount_percentage', 'deductible'].includes(name)) {
            // Validate decimal numbers
            if (!/^\d*?\d*$/.test(value) && value !== '') {
                setFormErrors(prevErrors => ({
                    ...prevErrors,
                    [name]: 'Only numbers are allowed'
                }));
                return;
            } else {
                setFormErrors(prevErrors => ({
                    ...prevErrors,
                    [name]: ''
                }));
            }
        }
        // Check if field name contains 'number' or specific number-only fields
        else if ((name.toLowerCase().includes('number') && name !== 'policy_number') ||
            ['mobile', 'aadhar', 'pincode'].includes(name)) {
            // Validate integers only
            if (!/^\d*$/.test(value) && value !== '') {
                setFormErrors(prevErrors => ({
                    ...prevErrors,
                    [name]: 'Only numbers are allowed'
                }));
                return;
            }
        }
        else if (name === 'policy_number') {
            // Don't allow spaces at start
            if (value.startsWith(' ')) {
                setFormErrors(prevErrors => ({
                    ...prevErrors,
                    [name]: 'Policy number cannot start with a space'
                }));
                return;
            }


            // Format the name: convert to uppercase and ensure only one space after each name part
            const formattedValue = value.toUpperCase().replace(/\s{2,}$/, ' ');

            // Allow only alphanumeric characters, hyphens and spaces
            if (!/^[A-Z0-9\s-]*$/.test(formattedValue)) {
                setFormErrors(prevErrors => ({
                    ...prevErrors,
                    [name]: 'Only letters, numbers, spaces and hyphens are allowed'
                }));
                return;
            }

            setFormData(prevData => ({
                ...prevData,
                [name]: formattedValue
            }));
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [name]: ''
            }));
            return;
        }

        if (name === 'payment_type') {
            setFormData(prevData => {
                const updatedData = { ...prevData, [name]: value };

                // Fields to remove based on payment types
                const fieldsToRemove = [
                    'cash_amount',
                    'received_date',
                    'cheque_amount',
                    'cheque_number',
                    'cheque_date',
                    'bank_name',
                    'branch_name',
                    'dd_amount',
                    'dd_number',
                    'dd_date',
                    'online_amount',
                    'transaction_date',
                    'transaction_type',
                    'receipt_number',
                ];

                // Remove all payment-related fields that exist in the current formData
                fieldsToRemove.forEach(field => {
                    if (field in updatedData) {
                        delete updatedData[field];
                    }
                });

                return updatedData;
            });
            setFormErrors(prevErrors => ({ ...prevErrors, [name]: '' }));
        }

        // Update form data and clear errors
        setFormData(prevData => ({ ...prevData, [name]: value }));
        setFormErrors(prevErrors => ({ ...prevErrors, [name]: '' }));
    }

    // To handle changes in the autocomplete dropdown
    const handleAutocompleteDropdownChange = ({ name, value }) => {
        let additionalChanges = {};
        if (name === 'customer_id') {
            setOnlySeniorCitizenAsMember(false);
            setMembers([]);
            dispatch(clearCustomerMemberDetails());
        }

        if (name === 'insurance_company_branch') {
            const selectedBranch = insuranceBranches.find(branch => branch.id === value);
            additionalChanges = {
                ...additionalChanges,
                imf_code: selectedBranch?.imf_code || '',
            }
        }

        if (name === 'product_type') {
            additionalChanges = {
                ...additionalChanges,
                product_name: '',
                sub_product_name: ''
            }
        }

        if (name === 'product_name') {
            const fetchedMasterProduct = masterProducts.find(product => product.id === Number(value));
            setQuestions([]);
            setAnswers([]);
            if (fetchedMasterProduct?.product_name.toLowerCase().includes('varishtha bima')) {
                setDialogConfig({
                    title: 'Age Restriction Warning',
                    message: 'This product is only for senior citizens. If you continue, all members below 60 years of age will be removed. Do you want to proceed?',
                    type: 'warning',
                    callback: () => {
                        setOnlySeniorCitizen(true);
                        setFormData(prevData => ({
                            ...prevData,
                            [name]: value,
                            sub_product_name: ''
                        }));
                        setFormErrors(prevErrors => ({ ...prevErrors, [name]: '' }));
                    }
                });
                setOpenDialog(true);
                return;
            }
            additionalChanges = {
                ...additionalChanges,
                sub_product_name: '',
            }
        }
        setFormData(prevData => ({
            ...prevData,
            ...additionalChanges,
            [name]: value
        }));
        setFormErrors(prevErrors => ({ ...prevErrors, [name]: '' }));
    }

    // To handle the proposal type change
    const handleProposalTypeChange = (proposalType) => {
        if (proposalType === 'Renewal') {
            navigate(`/dashboard/proposal-renewal/`)
        }
        else if (proposalType === 'Roll Over') {
            navigate(`/dashboard/proposal-roll-over/`) // Add the route for roll over
        }
        else if (proposalType === 'Migration') {
            navigate(`/dashboard/proposal-migration/`) // Add the route for migration
        }
    }

    // To handle the member change
    const handleMemberChange = (index, field, value) => {
        setMembers(prevMembers => {
            const updatedMembers = [...prevMembers];

            // Handle nominee/appointee nested fields
            if (field === 'nominee' || field === 'appointee') {
                // Handle date fields separately
                if ('dob' in value) {
                    updatedMembers[index] = {
                        ...updatedMembers[index],
                        [field]: {
                            ...updatedMembers[index][field],
                            dob: value.dob // Store date object directly
                        }
                    };
                }
                // Special handling for name field
                if ('name' in value) {
                    // Check if value starts with whitespace
                    if (value.name === ' ') {
                        setMemberErrors(prevErrors => {
                            const updatedErrors = { ...prevErrors };
                            if (!updatedErrors[index]) updatedErrors[index] = {};
                            const prefix = field === 'nominee' ? 'nominee_' : 'appointee_';
                            updatedErrors[index][`${prefix}name`] = 'Do not start with a whitespace character';
                            return updatedErrors;
                        });
                        return updatedMembers;
                    }

                    // Format the name: convert to uppercase and ensure only one space after each name part
                    const formattedName = value.name.toUpperCase().replace(/\s{2,}$/, ' ');

                    updatedMembers[index] = {
                        ...updatedMembers[index],
                        [field]: {
                            ...updatedMembers[index][field],
                            ...value,
                            name: formattedName
                        }
                    };
                } else {
                    // Handle other nominee/appointee fields normally
                    updatedMembers[index] = {
                        ...updatedMembers[index],
                        [field]: {
                            ...updatedMembers[index][field],
                            ...value
                        }
                    };
                }

                // Clear related error fields
                setMemberErrors(prevErrors => {
                    const updatedErrors = { ...prevErrors };
                    if (!updatedErrors[index]) updatedErrors[index] = {};
                    const changedField = Object.keys(value)[0];
                    const prefix = field === 'nominee' ? 'nominee_' : 'appointee_';
                    updatedErrors[index][`${prefix}${changedField}`] = '';
                    return updatedErrors;
                });
            }
            // Handle deductible special case
            else if (field === 'deductible') {
                updatedMembers[index] = {
                    ...updatedMembers[index],
                    deductible: value,
                    sum_insured: ''
                };

                // Clear both deductible and sum_insured errors
                setMemberErrors(prevErrors => {
                    const updatedErrors = { ...prevErrors };
                    if (!updatedErrors[index]) {
                        updatedErrors[index] = {};
                    }
                    updatedErrors[index].deductible = null;
                    updatedErrors[index].sum_insured = null;
                    return updatedErrors;
                });
            } else if (field === 'add_sum_insured') {
                const name = value.name;
                // Convert value to string and remove any non-numeric characters
                const fetchedValue = value.value.toString().replace(/[^0-9]/g, '');

                updatedMembers[index] = {
                    ...updatedMembers[index],
                    [name]: fetchedValue
                };

                // Clear error when valid value is entered 
                setMemberErrors(prevErrors => {
                    const updatedErrors = { ...prevErrors };
                    if (!updatedErrors[index]) {
                        updatedErrors[index] = {};
                    }
                    updatedErrors[index][name] = '';
                    return updatedErrors;
                });
            }
            else if (field === 'health_declaration') {
                // Ensure value has the expected structure and properties
                const id = value.id;
                const fetchedValue = value.value;
                updatedMembers[index] = {
                    ...updatedMembers[index],
                    health_declaration: {
                        ...(updatedMembers[index].health_declaration || {}),
                        [id]: fetchedValue
                    }
                };
            }
            // Handle all other direct member fields
            else {
                updatedMembers[index] = {
                    ...updatedMembers[index],
                    [field]: value
                };

                // Clear direct field error
                setMemberErrors(prevErrors => {
                    const updatedErrors = { ...prevErrors };
                    if (!updatedErrors[index]) {
                        updatedErrors[index] = {};
                    }
                    updatedErrors[index][field] = '';
                    return updatedErrors;
                });
            }

            return updatedMembers;
        });
    };

    // To handle the member removal
    const handleRemoveMember = (index) => {
        setMembers(prevMembers => prevMembers.filter((_, i) => i !== index));
    }

    // To handle the member removal by age
    const handleRemoveMemberByAge = () => {
        const currentDate = dayjs();


        setMembers(prevMembers => {
            const filteredMembers = members.filter(member => {
                // Parse the date string with the correct format (DD-MM-YYYY)
                const birthDate = dayjs(member.dob, "DD-MM-YYYY");
                if (!birthDate.isValid()) {
                    return false;
                }
                const memberAge = currentDate.diff(birthDate, 'year');
                const keepMember = memberAge >= 60;
                return keepMember;
            });
            return filteredMembers;
        });
        setOnlySeniorCitizenAsMember(true);
        toast.info('Members below 60 years have been removed');
    }

    // To handle the date change
    const handleDateChange = (name, date) => {
        if (name === 'start_date') {
            if (!date) {
                // If date is null/undefined, clear the start_date
                setFormData(prev => ({
                    ...prev,
                    start_date: null,
                    end_date: null
                }));
                return;
            }

            if (formData.tenure) {
                // Calculate end date based on tenure (1 year minus 1 day)
                const endDate = dayjs(date).add(Number(formData.tenure), 'year').subtract(1, 'day');
                setFormData(prev => ({
                    ...prev,
                    start_date: date,
                    end_date: endDate
                }));
                setFormErrors(prev => ({
                    ...prev,
                    start_date: '',
                    end_date: ''
                }));
            } else {
                setFormData(prev => ({
                    ...prev,
                    start_date: date
                }));
                setFormErrors(prev => ({
                    ...prev,
                    start_date: ''
                }));
            }
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: date
            }));
            setFormErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // To handle the file selection
    const handleFileSelect = (file) => {
        setFormData(prev => ({
            ...prev,
            policy_pdf: file
        }))
        setFormErrors(prev => ({
            ...prev,
            policy_pdf: ''
        }))
    }

    const handleGetPolicyPdf = (policy_number) => {
        // Check if formData and fetchedProposal are defined
        const policyNumber = policy_number || formData?.policy_number || fetchedProposal?.policy_number;
        if (!policyNumber) {
            toast.error('Policy number is not available. Please check your data.');
            return; // Exit the function if policy number is not available
        }
        setLoading(true);
        dispatch(getPolicyPdf(policyNumber))
            .finally(() => setLoading(false));
    };

    // To check if the payment type is present in the API names
    const hasPaymentType = (apiNames) => {
        return paymentTypes.some(type =>
            apiNames.includes(type.api_name) &&
            type.id === Number(formData.payment_type)
        );
    };

    // To handle the dialog close event
    const handleDialogClose = (proceed) => {
        setOpenDialog(false);

        if (!proceed) {
            return;
        }

        if (dialogConfig.callback) {
            dialogConfig.callback();
        }
    };

    // To check for CKYC
    const handleCkycCheck = async () => {
        const validate = handleValidateForm('Proposal');
        if (!validate) {
            return;
        }
        try {
            const handleCreation = (data) => {
                const ckyc_number = data?.result?.ckyc_number;
                const { proposal_id, url, url_expiry } = data;

                const todaysDate = dayjs().format('DDMMYYYYhhmm');
                const transactionId = Math.floor(Math.random() * 100000000).toString() + todaysDate.toString();
                const userId = Math.floor(Math.random() * 90000000 + 10000000).toString();

                // Prepare payment data
                const paymentData = {
                    amount: formData.total_premium,
                    firstName: customer.first_name,
                    lastName: customer.last_name,
                    mobile: customer.mobile,
                    email: customer.email,
                    proposal_number: formData.proposal_number,
                    TransactionID: transactionId,
                    PaymentOption: "3",
                    ResponseURL: `${baseUrl}/dashboard/payment-form`,
                    UserIdentifier: "webagg",
                    UserId: userId,
                    Vendor: "1",
                    Status: !id ? "SUCCESS" : "PENDING",
                };

                // Generate checksum
                const checksum = generateChecksum({
                    TransactionID: paymentData.TransactionID,
                    PaymentOption: paymentData.PaymentOption,
                    ResponseURL: paymentData.ResponseURL,
                    ProposalNumber: paymentData.proposal_number,
                    PremiumAmount: paymentData.amount,
                    UserIdentifier: paymentData.UserIdentifier,
                    UserId: paymentData.UserId,
                    FirstName: paymentData.firstName.trim(),
                    LastName: paymentData.lastName.trim(),
                    Mobile: paymentData.mobile.trim(),
                    Email: paymentData.email.trim(),
                    Vendor: paymentData.Vendor
                });

                // Add checksum to payment data
                paymentData.CheckSum = checksum;
                const paymentType = paymentTypes.find(paymentType => paymentType.label_name.toLowerCase() === 'online');

                const paymentCreationData = {
                    online_amount: formData.online_amount || formData.total_premium,
                    receipt_number: formData.receipt_number || '',
                    FirstName: customer.first_name || '',
                    LastName: customer.last_name || '',
                    Mobile: customer.mobile || '',
                    Email: customer.email || '',
                    transaction_date: formatDate(formData.transaction_date) || dayjs().format('YYYY-MM-DD'),
                    PaymentType: paymentType.id,
                    CheckSum: checksum,
                    PaymentOption: "3",
                    ResponseURL: `${baseUrl}/dashboard/payment-form`,
                    ProposalNumber: formData.proposal_number,
                    TransactionID: transactionId,
                    UserIdentifier: "webagg",
                    UserId: userId,
                    Vendor: "1",
                    Status: "PENDING",
                }
                // Create payment
                setLoading(true);
                dispatch(createPayment(paymentCreationData)).then((res) => {
                    const proposalCreationData = {
                        customer_member_id: formData.customer_member_id,
                        quotation_id: quotationsData[0]?.quotation_id || quotationsData[0]?.pa_quotation_id,
                        quotation_number: quotationsData[0]?.quotation_number || '',
                        proposal_Id: proposal_id,
                        ckyc_number: ckyc_number,
                        ckyc_link: url,
                        ckyc_expiry_date: url_expiry,
                        customer_id: formData.customer_id,
                        customer_salutation: formData.salutation,
                        ProposalNumber: formData.proposal_number,
                        insurance_company: formData.insurance_company_name,
                        insurance_branch: formData.insurance_company_branch,
                        imf_code: formData.imf_code,
                        imf_branch: formData.imf_branch,
                        agent_code: formData.agent_id,
                        product_type: formData.product_type,
                        product_name: formData.product_name,
                        member_type: formData.member_type,
                        co_pay: formData.co_pay,
                        sub_product: formData.sub_product_name,
                        start_date: formData.start_date,
                        end_date: formData.end_date,
                        net_premium: formData.net_premium,
                        gst_amount: formData.gst_amount,
                        ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) && {
                            family_discount_amount: formData.family_discount_amount || 0,
                            family_discount_percentage: formData.family_discount_percentage || 0,
                            long_term_discount_amount: formData.long_term_discount_amount || 0,
                            long_term_discount_percentage: formData.long_term_discount_percentage || 0,
                        }),
                        total_premium: formData.total_premium,
                        tenure: formData.tenure,
                        policy_issue_date: formData.issue_date,
                        policy_number: formData.policy_number,
                        remarks: formData.remarks,
                        status: 'PENDING',
                        proposal_type: 'NEW',
                        Created_by: formData.created_by,
                    }
                    const memberCreationData = members?.map(member => ({
                        member_id: member.member_id,
                        customer_member_id: member.customer_member_id,
                        relation: member.relation_id,
                        status: 'PENDING',
                        Created_by: formData.created_by,
                        ...(member.health_declaration && {
                            health_declaration: getHealthDeclarationString(member.health_declaration)
                        }),
                        ...((masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) ? {
                            occupation: member?.occupation,
                            ad_sum_insured: member?.ad_sum_insured,
                            pp_sum_insured: member?.pp_sum_insured,
                            pt_sum_insured: member?.pt_sum_insured,
                            tt_sum_insured: member?.tt_sum_insured,
                            lp_sum_insured: member?.lp_sum_insured,
                            ls_sum_insured: member?.ls_sum_insured,
                            me_sum_insured: member?.me_sum_insured,
                            am_sum_insured: member?.am_sum_insured,
                            bb_sum_insured: member?.bb_sum_insured,
                            rf_sum_insured: member?.rf_sum_insured,
                            aa_sum_insured: member?.aa_sum_insured,
                            cs_sum_insured: member?.cs_sum_insured,
                            hc_sum_insured: member?.hc_sum_insured,
                            ft_sum_insured: member?.ft_sum_insured,
                            annual_income: member?.annual_income,
                        } : {
                            sum_insured: member.sum_insured || null,
                            deductible: member.deductible || null,
                        }),
                        nominee_name: member.nominee?.name,
                        nominee_gender: member.nominee?.gender,
                        nominee_dob: member.nominee?.dob || '',
                        nominee_relation: member.nominee?.relation,
                        appointee_name: member.appointee?.name,
                        appointee_gender: member.appointee?.gender || '',
                        appointee_dob: member.appointee?.dob || '',
                        appointee_relation: member.appointee?.relation
                    }))
                    const formPayload = new FormData();

                    if (formData.policy_pdf?.image instanceof File) {
                        // “policy_pdf” must match the name your multer.fields() is watching
                        formPayload.append(
                            'policy_pdf',
                            formData.policy_pdf.image,
                            formData.policy_number
                        );
                    }


                    // Structure main data
                    formPayload.append("data", JSON.stringify({
                        proposalData: proposalCreationData,
                        memberData: memberCreationData
                    }));
                    dispatch(createProposal(formPayload)).then((response) => {
                        dispatch(setCurrentProposalId(response.payload.proposalId));
                        navigate(`/dashboard/edit-proposal/${response.payload.proposalId}`);
                        window.location.reload(); // Add reload after navigation
                    });
                }).finally(() => setLoading(false));
            }
            const gender = genderOptions.find(gender => gender.id === customer.gender_id)?.label_name;
            const ckycData = {
                req_id: "",
                proposal_no: "",
                customer_type: customerInfo?.familyType?.toLowerCase() === 'individual' ? 'I' : 'F',
                id_type: "PAN",
                id_num: customer.pan_number,
                dob: formatDate(customer.date_of_birth),
                full_name: customer.first_name + (customer.middle_name ? " " + customer.middle_name + " " : " ") + customer.last_name,
                gender: gender === 'Male' ? 'M' : gender === 'Female' ? 'F' : 'O',
                url_type: "URL",
                system_name: process.env.REACT_APP_SYSTEM_NAME,
                user_id: ""
            }
            setLoading(true);
            setReloadNeeded(true);
            await dispatch(checkForCkyc(ckycData))
                .then((res) => {
                    const data = res.payload;
                    setLoading(false);
                    setFormData(prev => ({
                        ...prev,
                        ckyc_number: data?.result?.ckyc_number,
                        proposal_id: data?.proposal_id,
                        ckyc_expiry_date: data?.result?.url_expiry
                    }))
                    if (data?.result?.ckyc_number) {
                        handleCreation(data);
                        toast.success('CKYC verification successful');
                    } else {
                        setDialogConfig({
                            title: 'Please complete your KYC verification before proceeding.',
                            message: 'Keep your Photo ID Proof, Address Proof and PAN Card/Form 16A ready to upload in the link provided.',
                            type: 'ckyc',
                            callback: () => {
                                handleCreation(data);
                                const ckycData = {
                                    kycReqNo: data?.proposal_id,
                                    icKycNo: data?.proposal_id,
                                    returnUrl: `${baseUrl}/dashboard/ckyc-form`,
                                    kycUrl: data?.url
                                }
                                handleCKYCForm(ckycData);
                            }
                        });
                        setOpenDialog(true);
                    }

                })
                .catch((error) => {
                    setReloadNeeded(false);
                    console.error('CKYC verification error:', error);
                    toast.error('CKYC verification failed');
                })

        } catch (error) {
            console.error('CKYC verification error:', error);
            toast.error('CKYC verification failed');

        }
    }

    // To handle the status check for ckyc
    const handleCkycStatusCheck = async () => {
        if (!formData.proposal_id) {
            toast.error('Proposal ID is required for CKYC status check');
            return;
        }

        try {
            if (formData.ckyc_number) return;
            const ckycStatusData = {
                proposal_id: formData.proposal_id
            }
            await dispatch(checkCKYCStatus(ckycStatusData)).then(res => {
                const final_status = Number(res.payload?.Final_Status);

                // Check if CKYC has expired
                const ckyc_expiry_date = formData.ckyc_expiry_date;
                const hasExpired = ckyc_expiry_date ? dayjs().isAfter(dayjs(ckyc_expiry_date)) : false;

                if (final_status === 0) {
                    toast.error('Please complete your KYC verification');
                    if (hasExpired) {
                        toast.warning('CKYC has expired. Initiating new CKYC verification.');
                        handleCkycCheck();
                    }
                    return;
                } else {
                    const ckyc_number = res.payload?.result?.ckyc_number;
                    if (ckyc_number === '') {
                        toast.error('KYC verification is still in progress');
                        return;
                    }
                    setFormData(prev => ({
                        ...prev,
                        ckyc_number: ckyc_number,
                    }));
                    toast.success('CKYC verification successful');
                }
            });
        } catch (error) {
            console.error('CKYC status check error:', error);
            toast.error('Failed to check CKYC status');
        }
    }

    // Add this effect to check CKYC status when proposal ID is available
    useEffect(() => {
        if (formData.proposal_id && Number(id) !== 0) {
            handleCkycStatusCheck();
        }
    }, [formData.proposal_id]);

    const getPaymentTypeData = (paymentType) => {
        // Helper function to format date to YYYY-MM-DD
        const formatDate = (dateString) => {
            if (!dateString) return '';
            return new Date(dateString).toISOString().split('T')[0];
        };

        const baseData = {
            PaymentType: paymentType.id,
            ProposalNumber: formData.proposal_number || '',
            Status: !id ? "SUCCESS" : "PENDING",
            TransactionID: '',
            PaymentOption: '',
            UserIdentifier: '',
            UserId: '',
            FirstName: '',
            LastName: '',
            Mobile: '',
            Email: '',
            Vendor: '',
            CheckSum: '',
            ResponseURL: ''
        };

        switch (paymentType.api_name) {
            case 'PAY_CASH':
                return {
                    ...baseData,
                    cash_amount: formData.cash_amount || '',
                    received_date: formatDate(formData.received_date)
                };

            case 'PAY_CHEQUE':
                return {
                    ...baseData,
                    cheque_amount: formData.cheque_amount || '',
                    cheque_number: formData.cheque_number || '',
                    cheque_date: formatDate(formData.cheque_date),
                    bank_name: formData.bank_name || '',
                    branch_name: formData.branch_name || ''
                };

            case 'PAY_DD':
                return {
                    ...baseData,
                    dd_amount: formData.dd_amount || '',
                    dd_number: formData.dd_number || '',
                    dd_date: formatDate(formData.dd_date),
                    bank_name: formData.bank_name || '',
                    branch_name: formData.branch_name || ''
                };

            case 'PAY_ONLINE':
                return {
                    ...baseData,
                    TransactionID: formData.TransactionID || '',
                    PaymentOption: formData.PaymentOption || '',
                    ResponseURL: formData.ResponseURL || '',
                    online_amount: formData.online_amount || formData.total_premium,
                    receipt_number: formData.receipt_number || '',
                    UserIdentifier: formData.UserIdentifier || '',
                    UserId: formData.UserId || '',
                    FirstName: customer.first_name || '',
                    LastName: customer.last_name || '',
                    Mobile: customer.mobile || '',
                    Email: customer.email || '',
                    Vendor: formData.Vendor || '',
                    transaction_date: formatDate(formData.transaction_date) || dayjs().format('YYYY-MM-DD')
                };

            case 'PAY_CHEQUE_CASH':
                return {
                    ...baseData,
                    cash_amount: formData.cash_amount || '',
                    received_date: formatDate(formData.received_date),
                    cheque_amount: formData.cheque_amount || '',
                    cheque_number: formData.cheque_number || '',
                    cheque_date: formatDate(formData.cheque_date),
                    bank_name: formData.bank_name || '',
                    branch_name: formData.branch_name || ''
                };

            case 'PAY_DD_CASH':
                return {
                    ...baseData,
                    cash_amount: formData.cash_amount || '',
                    received_date: formatDate(formData.received_date),
                    dd_amount: formData.dd_amount || '',
                    dd_number: formData.dd_number || '',
                    dd_date: formatDate(formData.dd_date),
                    bank_name: formData.bank_name || '',
                    branch_name: formData.branch_name || ''
                };

            default:
                return baseData;
        }
    };

    // To filter relations for nominee and appointee
    const getFilteredRelations = () => {
        try {

            // Get the current product name
            const currentProduct = masterProducts.find(product => product.id === formData.product_name)?.product_name;

            // Base excluded relations that should never be available
            const baseExcluded = ['self'];
            if (currentProduct?.toLowerCase()?.includes('fg')) {
                baseExcluded.push('child', 'husband', 'wife');
            }
            // Add spouse-related relations if product is FG VARISHTHA BIMA
            if (currentProduct?.toLowerCase()?.includes('fg varishta bima')) {
                baseExcluded.push('spouse', 'husband', 'wife');
            }

            // Combine all exclusions
            const excludedRelations = [...baseExcluded];

            const filteredRelations = relations.filter(relation =>
                !excludedRelations.includes(relation.label_name?.toLowerCase())
            );
            return filteredRelations;
        } catch (error) {
            console.error('Error in getFilteredRelations:', error);
            return relations; // Return all relations as fallback
        }
    };

    // Add this function with your other helper functions
    const getSalutationOptions = (insuranceCompany) => {
        try {
            if (insuranceCompany === '') {
                return [];
            }

            // If insurance company is not Future Generali (ID: 1), return default options
            if (Number(insuranceCompany) !== 1) {
                return salutationOptions.map(option => ({
                    label: option.label_name,
                    value: option.id
                }));
            }

            // Get customer's gender
            const customerGender = genderOptions.find(g => g.id === customer?.gender_id)?.label_name?.toLowerCase();

            // Common salutations for both genders
            const bothGenderSalutations = ['ADM', 'ADV', 'BRIG', 'CDR', 'COL', 'DR', 'LT', 'MAJ', 'PROF'];
            // Male-only salutations
            const maleSalutations = ['MAST', 'MR', 'SHRI', 'SIR'];
            // Female-only salutations
            const femaleSalutations = ['MISS', 'MRS', 'MS', 'SMT'];

            // Filter salutations based on insurance company
            let filteredSalutations = salutationOptions.filter(option =>
                option.type_name === 'Future_genrali_Salutation'
            );

            // Further filter based on gender
            if (customerGender === 'male') {
                filteredSalutations = filteredSalutations.filter(option =>
                    bothGenderSalutations.includes(option.api_name) ||
                    maleSalutations.includes(option.api_name)
                );
            } else if (customerGender === 'female') {
                filteredSalutations = filteredSalutations.filter(option =>
                    bothGenderSalutations.includes(option.api_name) ||
                    femaleSalutations.includes(option.api_name)
                );
            }

            // Map the filtered salutations to dropdown format
            return filteredSalutations?.map(option => ({
                label: option.label_name,
                value: option.id
            })) || [];

        } catch (error) {
            console.error('Error in getSalutationOptions:', error);
            return [];
        }
    };

    const getHealthDeclarationString = (healthDeclaration) => {
        if (!healthDeclaration) return '';

        return Object.entries(healthDeclaration)
            .map(([key, value]) => `${key}:${value ? 'Y' : 'N'}`)
            .join('|');
    };

    const getHealthDeclarationToObject = (healthDeclarationString) => {
        if (!healthDeclarationString) return {};

        return healthDeclarationString.split('|').reduce((acc, item) => {
            const [key, value] = item.split(':');
            return {
                ...acc,
                [key]: value === 'Y'
            };
        }, {});
    };

    const handleCKYCForm = (data) => {
        const ckycParams = {
            kycReqNo: data?.proposal_id || data?.kycReqNo,
            icKycNo: data?.proposal_id || data?.kycReqNo,
            returnUrl: `${baseUrl}/dashboard/proposals`,
            kycUrl: data?.ckyc_link || data?.kycUrl
        };
        window.open(`${baseUrl}/dashboard/ckyc-form?state=${JSON.stringify(ckycParams)}`, '_blank');
    }

    return (
        <Box sx={{
            padding: { xs: '0 5px 5px', md: '0 40px 40px' },
            width: '100%'
        }}>
            <Backdrop
                sx={{
                    color: '#fff',
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)'
                }}
                open={loading}
            >
                <CircularProgress color="inherit" size={60} />
            </Backdrop>
            {!loading && (
                <form encType="multipart/form-data" style={{ width: '100%' }}>
                    <Grid
                        container
                        sx={{
                            position: 'sticky',
                            top: { xs: '140px', sm: '140px', md: '164px', lg: '164px' },
                            zIndex: 101,
                            backgroundColor: 'white',
                            borderBottom: '2px solid #E0E0E0',
                            padding: '10px 0',
                            display: "flex"
                        }}
                    >
                        {/* Header Row */}
                        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{
                                    width: '20px',
                                    marginLeft: '20px',
                                    backgroundColor: 'green'
                                }}
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <ModuleName moduleName="Proposal" pageName={(!id || (id && Number(id) === 0)) ? "Create" : "Edit"} />
                            </Box>
                        </Grid>

                        <Grid item xs={4} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                                {(!id || (id && Number(id) !== 0) && Boolean(isPaymentSuccess === 1)) &&
                                    <IconButton
                                        onClick={!id ? handlePayment : handleSave}
                                        sx={{ color: 'green', mx: 0.5 }}
                                    >
                                        <Save />
                                    </IconButton>
                                }
                                <IconButton
                                    onClick={handleCancel}
                                    sx={{ color: 'red', mx: 0.5 }}
                                >
                                    <Cancel />
                                </IconButton>
                            </Box>
                            <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
                                {(!id || (id && Number(id) !== 0) && Boolean(isPaymentSuccess === 1) && !fetchedProposal?.policy_number) &&
                                    <Button
                                        onClick={!id ? handlePayment : handleSave}
                                        variant="outlined"
                                        size="large"
                                        sx={{
                                            width: '100%',
                                            mx: 0.5,
                                            color: 'green',
                                            borderColor: 'green',
                                            textTransform: 'none',
                                            textWrap: 'nowrap'
                                        }}
                                    >
                                        {(fetchedProposal?.start_date ||
                                            masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase() === 'fg varishtha bima' ||
                                            masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase() === 'fg accident suraksha') ?
                                            'Submit Policy' : 'Create Proposal'}
                                    </Button>
                                }
                                <Button
                                    onClick={handleCancel}
                                    variant="outlined"
                                    size="small"
                                    sx={{
                                        maxWidth: '100px',
                                        width: '100%',
                                        mx: 1.5,
                                        color: 'red',
                                        borderColor: 'red',
                                        textTransform: 'none'
                                    }}
                                >
                                    Cancel
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>

                    {/* Proposal Type */}
                    {!id &&
                        <Box sx={{
                            width: '100%',
                            paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                            paddingInline: { xs: '0.5rem', sm: '1rem' },
                            marginTop: { xs: 2, sm: 3 }
                        }}>
                            {/* Section Header */}
                            <Box sx={{
                                width: '100%',
                                padding: { xs: "10px", sm: "15px" },
                                borderRadius: "4px",
                            }}>
                                <Typography
                                    sx={{
                                        fontSize: '18px',
                                        fontWeight: "700",
                                        color: '#4C5157',
                                    }}
                                >
                                    Proposal Type
                                </Typography>
                            </Box>
                            <Divider />

                            {/* Radio Buttons */}
                            <Box sx={{
                                width: '100%',
                                padding: { xs: '1rem', sm: '1.5rem' }
                            }}>
                                <FormControl component="fieldset" sx={{ width: '100%' }}>
                                    <RadioGroup
                                        row
                                        aria-label="proposal-type"
                                        name="proposal-type"
                                        value={proposalType}
                                        sx={{
                                            display: 'flex',
                                            flexDirection: { xs: 'column', sm: 'row' },
                                            gap: { xs: '0.5rem', sm: '1rem' },
                                            '& .MuiFormControlLabel-root': {
                                                flex: { xs: '1 1 100%', sm: '1 1 auto' }
                                            }
                                        }}
                                    >
                                        <FormControlLabel
                                            value="New"
                                            control={<Radio sx={{
                                                color: '#528A7E',
                                                '&.Mui-checked': { color: '#528A7E' }
                                            }} />}
                                            label="New"
                                        />
                                        <FormControlLabel
                                            value="Renewal"
                                            control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                            label="Renewal"
                                            onClick={() => handleProposalTypeChange('Renewal')}
                                        />
                                        <FormControlLabel
                                            value="Roll Over"
                                            control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                            label="Roll Over"
                                            onClick={() => handleProposalTypeChange('Roll Over')}

                                        />
                                        <FormControlLabel
                                            value="Migration"
                                            control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                            label="Migration"
                                            onClick={() => handleProposalTypeChange('Migration')}
                                        />
                                    </RadioGroup>
                                </FormControl>
                            </Box>
                        </Box>
                    }
                    {/* Personal Details */}
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Personal Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={7} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Customer Name"
                                    name="customer_id"
                                    options={customerDetails?.map((customer) => {
                                        const customerInfo = customer.first_name +
                                            (customer.middle_name ? ' ' + customer.middle_name + ' ' : ' ') +
                                            customer.last_name +
                                            ', Mobile : ' + customer.mobile +
                                            ', Aadhar : ' + customer.aadhar_number;

                                        // Add CKYC or Proposal ID info
                                        let additionalInfo = '';
                                        if (formData.ckyc_number) {
                                            additionalInfo += `, CKYC : ${formData.ckyc_number}`;
                                        }
                                        if (formData.proposal_id) {
                                            additionalInfo += `, Proposal ID : ${formData.proposal_id}`;
                                        }

                                        return {
                                            label: customerInfo + additionalInfo,
                                            value: customer.id
                                        }
                                    })}
                                    info={(id || selectedQuotation) ? formData.ckyc_number ? `CKYC Number : ${formData.ckyc_number}` : formData.proposal_id ? `Proposal ID : ${formData.proposal_id}` : '' : ''}
                                    value={formData.customer_id}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    disabled={Boolean(id || selectedQuotation)}
                                    helperText={formErrors.customer_id}
                                    required
                                />
                            </Grid>
                            {!formData.customer_id &&
                                <Grid item xs={5} sm={4} md={3} lg={2} style={{ display: 'flex', alignItems: 'center' }}>
                                    <Button
                                        onClick={() => {
                                            navigate('/dashboard/customer-personal-information')
                                        }}
                                        variant="outlined"
                                        size="small"
                                        sx={{
                                            minWidth: 0,
                                            padding: { xs: '8px', sm: '6px 16px' },
                                            color: 'green',
                                            borderColor: 'green',
                                            '& .MuiButton-startIcon': {
                                                margin: 0
                                            }
                                        }}
                                    >
                                        Add Customer
                                    </Button>
                                </Grid>}
                            {/* Add KYC Verification button if no CKYC number */}
                            {(formData.customer_id && !formData.ckyc_number && id && formData.proposal_id) &&
                                <Grid item xs={5} sm={4} md={3} lg={2} style={{ display: 'flex', alignItems: 'center' }}>
                                    <Button
                                        onClick={() => handleCKYCForm(formData)}
                                        variant="outlined"
                                        size="small"
                                        sx={{
                                            minWidth: 0,
                                            padding: { xs: '8px', sm: '6px 16px' },
                                            color: 'green',
                                            borderColor: 'green',
                                            '& .MuiButton-startIcon': {
                                                margin: 0
                                            }
                                        }}
                                    >
                                        KYC VERIFICATION
                                    </Button>
                                </Grid>
                            }
                        </Grid>
                    </Box>

                    {/* Quotation Details - Only show if customer section is complete */}
                    {(id && formData.quotation_number) && <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Quotation Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="quotation_number"
                                    label="Quotation Number"
                                    value={formData.quotation_number || ''}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.quotation_number}
                                    isRequired
                                    isDisabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="quotation_created_at"
                                    label="Created At"
                                    value={(() => {
                                        if (!formData?.quotation_created_at) {
                                            return dayjs().format('DD-MM-YYYY');
                                        }
                                        try {
                                            // Try parsing the date in multiple formats
                                            const date = dayjs(formData.quotation_created_at);
                                            if (date.isValid()) {
                                                return date.format('DD-MM-YYYY');
                                            }
                                            // If direct parsing fails, try parsing with specific format
                                            const altDate = dayjs(formData.quotation_created_at, 'DD/MM/YYYY');
                                            if (altDate.isValid()) {
                                                return altDate.format('DD-MM-YYYY');
                                            }
                                            // If all parsing fails, return current date
                                            return dayjs().format('DD-MM-YYYY');
                                        } catch (error) {
                                            return dayjs().format('DD-MM-YYYY');
                                        }
                                    })()}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.quotation_created_at}
                                    isDisabled
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    label='Proposal Number'
                                    value={formData.proposal_number}
                                    fullWidth
                                    isDisabled
                                />
                            </Grid>
                        </Grid>
                    </Box>}

                    {/* Insurance Company Details - Only show if customer section is complete */}
                    {(formData.customer_id) && (<Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Insurance Company Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'

                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Insurance Company Name"
                                    name="insurance_company_name"
                                    options={insuranceCompanies?.map(company => {
                                        return {
                                            label: company.insurance_company_name,
                                            value: company.id,
                                        }
                                    })}
                                    value={Number(formData.insurance_company_name) || ''}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                    helperText={formErrors.insurance_company_name}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Insurance Company Branch"
                                    name="insurance_company_branch"
                                    options={insuranceBranches?.map(branch => {
                                        return {
                                            label: branch.insurance_co_branch_name,
                                            value: branch.id
                                        }
                                    })}
                                    value={formData.insurance_company_branch}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    helperText={formErrors.insurance_company_branch}
                                    required
                                    disabled={Boolean((id && id !== '0') || !formData.imf_branch)}
                                />
                            </Grid>
                            {/* <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="imf_code"
                                    label="IMF Code"
                                    value={formData.imf_code}
                                    fullWidth
                                    isDisabled
                                />
                            </Grid> */}
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="IMF Branch"
                                    name="imf_branch"
                                    options={imfBranches?.map(branch => {
                                        return {
                                            label: branch.branch_name,
                                            value: branch.id
                                        }
                                    })}
                                    value={formData.imf_branch}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    helperText={formErrors.imf_branch}
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Agent Code"
                                    name="agent_id"
                                    options={agents?.filter(agent => agent.status === 1).map(agent => {
                                        return {
                                            label: `${agent.agent_id}(${agent.full_name})`,
                                            value: agent.id
                                        }
                                    })}
                                    value={formData.agent_id}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    helperText={formErrors.agent_id}
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Product Type"
                                    name="product_type"
                                    options={mainProducts?.map(product => {
                                        return {
                                            label: product.main_product,
                                            value: product.id
                                        }
                                    })}
                                    value={formData.product_type}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    helperText={formErrors.product_type}
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Product Name"
                                    name="product_name"
                                    options={masterProducts?.map(product => {
                                        return {
                                            label: product.product_name,
                                            value: product.id
                                        }
                                    })}
                                    value={formData.product_name}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    helperText={formErrors.product_name}
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <AutocompleteDropdown
                                    label="Sub Product Name"
                                    name="sub_product_name"
                                    options={subProducts?.map(product => {
                                        return {
                                            label: product.sub_product_name,
                                            value: product.id
                                        }
                                    })}
                                    value={formData.sub_product_name}
                                    onChange={handleAutocompleteDropdownChange}
                                    fullWidth
                                    helperText={formErrors.sub_product_name}
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Member Type"
                                    name="member_type"
                                    options={
                                        masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') ? [
                                            {
                                                label: 'Individual',
                                                value: 'individual'
                                            }
                                        ] : [
                                            {
                                                label: 'Individual',
                                                value: 'individual'
                                            },
                                            {
                                                label: 'FamilyFloater',
                                                value: 'family floater'
                                            }
                                        ]
                                    }
                                    value={formData.member_type}
                                    onChange={handleChange}
                                    fullWidth
                                    helperText={formErrors.member_type}
                                    required
                                    disabled={Boolean(id || selectedQuotation)}
                                />
                            </Grid>
                            {masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('varishtha bima') && (
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <FormControl fullWidth sx={{ display: 'flex', justifyContent: 'center' }}>
                                        <FormControlLabel
                                            control={
                                                <Switch
                                                    checked={Boolean(formData.co_pay)}
                                                    onChange={(e) => {
                                                        setFormData(prev => ({
                                                            ...prev,
                                                            co_pay: e.target.checked
                                                        }));
                                                    }}
                                                    disabled={Boolean(id || selectedQuotation)}
                                                    sx={{
                                                        '& .MuiSwitch-switchBase': {
                                                            color: 'red', // Default color when unchecked
                                                            '&.Mui-checked': {
                                                                color: 'green',
                                                                '&:hover': {
                                                                    backgroundColor: 'rgba(82, 138, 126, 0.08)'
                                                                }
                                                            }
                                                        },
                                                        '& .MuiSwitch-track': {
                                                            backgroundColor: 'red !important', // Track color when unchecked
                                                        },
                                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                            backgroundColor: 'green !important' // Track color when checked
                                                        }
                                                    }}
                                                />
                                            }
                                            label={
                                                <Typography sx={{
                                                    fontSize: '1.1rem', // Increased font size
                                                    fontWeight: 500, // Added medium weight for better visibility
                                                    color: formErrors.co_pay ? 'error.main' :
                                                        (id || selectedQuotation) ? 'text.disabled' : 'inherit'
                                                }}>
                                                    Co-Pay {formData.co_pay ? 'Active' : 'Inactive'}
                                                </Typography>
                                            }
                                        />
                                        {formErrors.co_pay && (
                                            <FormHelperText error>{formErrors.co_pay}</FormHelperText>
                                        )}
                                    </FormControl>
                                </Grid>
                            )}
                        </Grid>
                    </Box>)}

                    {/* Customer Details Section */}
                    {(formData.customer_id) && (<Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Proposer Details
                            </Typography>
                        </Box>
                        <Divider />
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Salutation"
                                    name="salutation"
                                    value={formData.salutation}
                                    options={salutations}
                                    onChange={handleChange}
                                    disabled={Boolean(fetchedProposal?.customer_salutation || formData.insurance_company_name !== 1)}
                                    helperText={formErrors.salutation}
                                    fullWidth
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="first_name"
                                    label="First Name"
                                    value={customer?.first_name || ''}
                                    fullWidth
                                    isDisabled
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="last_name"
                                    label="Last Name"
                                    value={customer?.last_name || ''}
                                    fullWidth
                                    isDisabled
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <FormControl fullWidth>
                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                        <DatePicker
                                            label="Date of Birth"
                                            value={customer?.date_of_birth ? dayjs(customer.date_of_birth) : null}
                                            format="DD/MM/YYYY"
                                            disabled
                                        />
                                    </LocalizationProvider>
                                </FormControl>
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Gender"
                                    name="gender"
                                    value={customer?.gender_id || ''}
                                    options={genderOptions?.map(option => ({
                                        label: option.label_name,
                                        value: option.id
                                    }))}
                                    disabled
                                    fullWidth
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Marital Status"
                                    name="marital_status"
                                    value={customer?.marital_status_id || ''}
                                    options={maritalStatusOptions?.map(option => ({
                                        label: option.label_name,
                                        value: option.id
                                    }))}
                                    disabled
                                    fullWidth
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Occupation"
                                    name="occupation"
                                    value={customer?.occupation || ''}
                                    options={OccupationOptions?.map(option => ({
                                        label: option.label_name,
                                        value: option.id
                                    }))}
                                    fullWidth
                                    disabled
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="aadhar_number"
                                    label="Aadhar Number"
                                    value={customer?.aadhar_number || ''}
                                    fullWidth
                                    isDisabled
                                />
                            </Grid>
                        </Grid>
                    </Box>)}

                    {/* Member Details - Only show if insurance company section is complete */}
                    {(formData.insurance_company_name && formData.insurance_company_branch && formData.imf_branch && formData.agent_id && formData.product_type && formData.product_name && formData.sub_product_name && formData.member_type && !isCancelled) && (
                        <Box sx={{
                            width: '100%',
                            borderTop: '2px solid #E0E0E0',
                            paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                            paddingInline: { xs: '0.5rem', sm: '1rem' },

                        }}>
                            {/* Section Header */}
                            <Box sx={{
                                width: '100%',
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: { xs: "10px", sm: "15px" },
                                borderRadius: "4px",
                            }}>
                                <Typography
                                    variant="h5"
                                    sx={{
                                        fontSize: '18px',
                                        fontWeight: "700",
                                        color: '#4C5157',
                                    }}
                                >
                                    Member Details
                                </Typography>
                            </Box>
                            <Divider />

                            {/* Form Fields */}
                            {
                                members?.map((member, index) => (
                                    <Card key={index} sx={{
                                        mb: 2,
                                        width: '100%',
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                        backgroundColor: '#f5f5f5', // Light gray background
                                    }}>
                                        <CardHeader
                                            title={`Member ${index + 1}`}
                                            action={
                                                !id && !selectedQuotation && (
                                                    <IconButton onClick={() => handleRemoveMember(index)}>
                                                        <DeleteIcon sx={{ color: 'red' }} />
                                                    </IconButton>
                                                )
                                            }
                                            sx={{
                                                borderBottom: '2px solid #528A7E',
                                                backgroundColor: '#fff',
                                                '& .MuiCardHeader-title': {
                                                    fontSize: '1rem',
                                                    color: '#4C5157',
                                                    fontWeight: 'normal'
                                                }
                                            }}
                                        />
                                        <CardContent>
                                            <Grid container spacing={2} sx={{
                                                width: '100%',
                                                '& .MuiGrid-item': {
                                                    width: '100%'
                                                }
                                            }}>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <CustomTextField
                                                        name="first_name"
                                                        label="First Name"
                                                        value={member?.first_name || ''}
                                                        onChange={(e) => handleMemberChange(index, 'first_name', e.target.value)}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.first_name}
                                                        isRequired
                                                        isDisabled
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <CustomTextField
                                                        name="middle_name"
                                                        label="Middle Name"
                                                        value={member?.middle_name || ''}
                                                        onChange={(e) => handleMemberChange(index, 'middle_name', e.target.value)}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.middle_name}
                                                        isDisabled
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <CustomTextField
                                                        name="last_name"
                                                        label="Last Name"
                                                        value={member?.last_name || ''}
                                                        onChange={(e) => handleMemberChange(index, 'last_name', e.target.value)}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.last_name}
                                                        isDisabled
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <FormControl fullWidth required>
                                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                            <DatePicker
                                                                label="DOB"
                                                                value={member?.dob ? dayjs(member.dob, 'DD/MM/YYYY') : null}
                                                                onChange={(date) => handleMemberChange(index, 'dob', date)}
                                                                maxDate={minAgeDate}
                                                                format="DD/MM/YYYY"
                                                                disabled
                                                                helperText={memberErrors[index]?.dob}
                                                                slotProps={{
                                                                    textField: {
                                                                        fullWidth: true,
                                                                        sx: {
                                                                            '& .MuiOutlinedInput-root': {
                                                                                '&::before': {
                                                                                    content: '""',
                                                                                    position: 'absolute',
                                                                                    left: 0,
                                                                                    top: 0,
                                                                                    bottom: 0,
                                                                                    width: '3px',
                                                                                    backgroundColor: 'red',
                                                                                    zIndex: 1,
                                                                                }
                                                                            },
                                                                        }
                                                                    }
                                                                }}
                                                            />
                                                        </LocalizationProvider>
                                                    </FormControl>
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <Dropdown
                                                        label="Relation"
                                                        name="relation_id"
                                                        options={relationOptions?.map(option => ({
                                                            label: option.label_name,
                                                            value: option.id
                                                        }))}
                                                        value={member?.relation_id || ''}
                                                        onChange={handleChange}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.relation_id}
                                                        required
                                                        disabled
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <Dropdown
                                                        label="Marital Status"
                                                        name="marital_status"
                                                        options={maritalStatusOptions?.map(option => ({
                                                            label: option.label_name,
                                                            value: option.id
                                                        }))}
                                                        value={member?.marital_status || ''}
                                                        onChange={(e) => handleMemberChange(index, 'marital_status', e.target.value)}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.marital_status}
                                                        required
                                                        disabled
                                                    />
                                                </Grid>
                                                {
                                                    maritalStatusOptions?.find(option => option.id === member.marital_status)?.label_name === 'Married' && (
                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                            <FormControl fullWidth required>
                                                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                                    <DatePicker
                                                                        label="Marriage Date"
                                                                        value={member?.marriage_date ? dayjs(member.marriage_date, 'DD/MM/YYYY') : null}
                                                                        onChange={(date) => handleMemberChange(index, 'marriage_date', date)}
                                                                        disabled
                                                                        format="DD/MM/YYYY"
                                                                        helperText={memberErrors[index]?.marriage_date}
                                                                        slotProps={{
                                                                            textField: {
                                                                                fullWidth: true,
                                                                                sx: {
                                                                                    '& .MuiOutlinedInput-root': {
                                                                                        '&::before': {
                                                                                            content: '""',
                                                                                            position: 'absolute',
                                                                                            left: 0,
                                                                                            top: 0,
                                                                                            bottom: 0,
                                                                                            width: '3px',
                                                                                            backgroundColor: 'red',
                                                                                            zIndex: 1,
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }}
                                                                    />
                                                                </LocalizationProvider>
                                                            </FormControl>
                                                        </Grid>
                                                    )}
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <CustomTextField
                                                        name="mobile_number"
                                                        label="Mobile Number"
                                                        value={member?.mobile_number || ''}
                                                        onChange={handleChange}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.mobile_number}
                                                        isDisabled
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                                    <CustomTextField
                                                        name="email_id"
                                                        label="Email Id"
                                                        value={member?.email_id || ''}
                                                        onChange={handleChange}
                                                        fullWidth
                                                        helperText={memberErrors[index]?.email_id}
                                                        isDisabled
                                                    />
                                                </Grid>
                                                {/* Show sum insured only for individual member type */}



                                                {
                                                    (formData.member_type === 'individual') ?
                                                        (
                                                            masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') ? (
                                                                <>
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <AutocompleteDropdown
                                                                            label='Occupation'
                                                                            value={member?.occupation || ''}
                                                                            options={OccupationOptions?.map(option => ({
                                                                                label: option.label_name,
                                                                                value: option.id
                                                                            }))}
                                                                            fullWidth
                                                                            disabled
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="annual_income"
                                                                            label="Annual Income"
                                                                            value={member?.annual_income}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'annual_income',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.annual_income}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="ad_sum_insured"
                                                                            label="Sum Insured - Accidental Death"
                                                                            value={member?.ad_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'ad_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.ad_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="pt_sum_insured"
                                                                            label="Sum Insured - Permanent Total Disability"
                                                                            value={member?.pt_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'pt_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.pt_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="pp_sum_insured"
                                                                            label="Sum Insured - Permanent Partial Disability"
                                                                            value={member?.pp_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'pp_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.pp_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="tt_sum_insured"
                                                                            label="Sum Insured - Temporary Total Disability"
                                                                            value={member?.tt_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'tt_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.tt_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>
                                                                    {(Boolean(!id) || Boolean(member?.lp_sum_insured)) &&
                                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                            <CustomTextField
                                                                                name="lp_sum_insured"
                                                                                label="Sum Insured - Loan Protector"
                                                                                value={member?.lp_sum_insured}
                                                                                onChange={(e) => {
                                                                                    const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                    handleMemberChange(index, 'add_sum_insured', {
                                                                                        name: 'lp_sum_insured',
                                                                                        value: value
                                                                                    });
                                                                                }}
                                                                                fullWidth
                                                                                helperText={memberErrors[index]?.lp_sum_insured}
                                                                                disabled={Boolean(id)}
                                                                                inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                            />
                                                                        </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.ls_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="ls_sum_insured"
                                                                            label="Sum Insured - Life Support Benefits"
                                                                            value={member?.ls_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'ls_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.ls_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.me_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="me_sum_insured"
                                                                            label="Sum Insured - Accident Hospitalisation"
                                                                            value={member?.me_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'me_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.me_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.am_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="am_sum_insured"
                                                                            label="Sum Insured - Accident Medical Expenses"
                                                                            value={member?.am_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'am_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.am_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.bb_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="bb_sum_insured"
                                                                            label="Sum Insured - Broken Bones"
                                                                            value={member?.bb_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'bb_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.bb_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.rf_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="rf_sum_insured"
                                                                            label="Sum Insured - Repatration And Funeral Expenses"
                                                                            value={member?.rf_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'rf_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.rf_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.aa_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="aa_sum_insured"
                                                                            label="Sum Insured - Adaptation Allowance"
                                                                            value={member?.aa_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'aa_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.aa_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.cs_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="cs_sum_insured"
                                                                            label="Sum Insured - Child Education Support"
                                                                            value={member?.cs_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'cs_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.cs_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.hc_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="hc_sum_insured"
                                                                            label="Sum Insured - Hospital Cash Allowance"
                                                                            value={member?.hc_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'hc_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.hc_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                    {(Boolean(!id) || Boolean(member?.ft_sum_insured)) && <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <CustomTextField
                                                                            name="ft_sum_insured"
                                                                            label="Sum Insured - Family Transport Allowance"
                                                                            value={member?.ft_sum_insured}
                                                                            onChange={(e) => {
                                                                                const value = e.target.value.replace(/[^0-9]/g, '');
                                                                                handleMemberChange(index, 'add_sum_insured', {
                                                                                    name: 'ft_sum_insured',
                                                                                    value: value
                                                                                });
                                                                            }}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.ft_sum_insured}
                                                                            disabled={Boolean(id)}
                                                                            inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }}
                                                                        />
                                                                    </Grid>}
                                                                </>
                                                            ) : (
                                                                <>
                                                                    {masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('advantage top up') && (
                                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                            <Dropdown
                                                                                label="Deductible"
                                                                                name="deductible"
                                                                                options={deductileOptions}
                                                                                value={member?.deductible || ''}
                                                                                prefixSymbol={'₹'}
                                                                                onChange={(e) => handleMemberChange(index, 'deductible', e.target.value)}
                                                                                fullWidth
                                                                                helperText={memberErrors[index]?.deductible}
                                                                                required
                                                                                disabled={Boolean(id)}
                                                                            />
                                                                        </Grid>
                                                                    )}
                                                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                        <Dropdown
                                                                            name="sum_insured"
                                                                            label="Sum Insured"
                                                                            value={member?.sum_insured || ''}
                                                                            prefixSymbol={'₹'}
                                                                            options={sumInsuredOptions}
                                                                            onChange={(e) => handleMemberChange(index, 'sum_insured', e.target.value)}
                                                                            fullWidth
                                                                            helperText={memberErrors[index]?.sum_insured}
                                                                            isRequired
                                                                            disabled={Boolean(id)}
                                                                        />
                                                                    </Grid>
                                                                </>
                                                            )
                                                        ) : null}
                                            </Grid>
                                            {/*Questions to be asked */}

                                            {((questions && questions.length > 0) && (!fetchedProposal || fetchedProposal?.proposal_type !== 'RENEW')) && (
                                                <Card sx={{
                                                    mt: 2,
                                                    backgroundColor: '#f8f9fa',
                                                    boxShadow: '0 4px 8px rgba(82, 138, 126, 0.15)',
                                                    border: '1px solid #528A7E',
                                                    borderRadius: '8px',
                                                    position: 'relative',
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        top: 0,
                                                        left: 0,
                                                        right: 0,
                                                        height: '4px',
                                                        backgroundColor: '#528A7E',
                                                        borderRadius: '8px 8px 0 0'
                                                    }
                                                }}>
                                                    <CardHeader
                                                        title="Health Declaration"
                                                        sx={{
                                                            backgroundColor: 'rgba(82, 138, 126, 0.08)',
                                                            borderBottom: '1px solid #528A7E',
                                                            '& .MuiCardHeader-title': {
                                                                fontSize: '1.1rem',
                                                                color: '#528A7E',
                                                                fontWeight: '600',
                                                                letterSpacing: '0.5px'
                                                            }
                                                        }}
                                                    />
                                                    <CardContent sx={{ backgroundColor: 'white' }}>
                                                        {questions.map((question) => (
                                                            <Grid item xs={12} key={question.id}>
                                                                {
                                                                    ((question.onlyForFemale && member.gender !== 'F') ||
                                                                        (question.minAge && calculateAge(member.dob) < question.minAge)) ?
                                                                        null :
                                                                        (
                                                                            <QuestionToggle
                                                                                question={question.question}
                                                                                value={member?.health_declaration?.[question.id]}
                                                                                onChange={(value) => handleMemberChange(index, 'health_declaration', {
                                                                                    id: question.id,
                                                                                    value: value
                                                                                })}
                                                                                required
                                                                                disabled={Boolean(id && Number(id) !== 0)}
                                                                            />
                                                                        )
                                                                }
                                                            </Grid>
                                                        ))}
                                                    </CardContent>
                                                </Card>
                                            )}

                                            {/* Nominee Details Card */}
                                            <Card sx={{
                                                mt: 2,
                                                backgroundColor: '#f8f9fa',
                                                boxShadow: '0 4px 8px rgba(82, 138, 126, 0.15)',
                                                border: '1px solid #528A7E',
                                                borderRadius: '8px',
                                                position: 'relative',
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    right: 0,
                                                    height: '4px',
                                                    backgroundColor: '#528A7E',
                                                    borderRadius: '8px 8px 0 0'
                                                }
                                            }}>
                                                <CardHeader
                                                    title="Nominee Details"
                                                    sx={{
                                                        backgroundColor: 'rgba(82, 138, 126, 0.08)',
                                                        borderBottom: '1px solid #528A7E',
                                                        '& .MuiCardHeader-title': {
                                                            fontSize: '1.1rem',
                                                            color: '#528A7E',
                                                            fontWeight: '600',
                                                            letterSpacing: '0.5px'
                                                        }
                                                    }}
                                                />
                                                <CardContent sx={{ backgroundColor: 'white' }}>
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                            <CustomTextField
                                                                name="nominee_name"
                                                                label="Nominee Name"
                                                                value={member?.nominee?.name || ''}
                                                                onChange={(e) => handleMemberChange(index, 'nominee', {
                                                                    name: e.target.value
                                                                })}
                                                                fullWidth
                                                                helperText={memberErrors[index]?.nominee_name}
                                                                isRequired
                                                                disabled={Boolean(id && Number(id) !== 0)}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                            <Dropdown
                                                                label="Gender"
                                                                name="nominee_gender"
                                                                options={genderOptions?.map(option => ({
                                                                    label: option.label_name,
                                                                    value: option.id
                                                                }))}
                                                                value={member?.nominee?.gender || ''}
                                                                onChange={(e) => handleMemberChange(index, 'nominee', {
                                                                    gender: e.target.value
                                                                })}
                                                                fullWidth
                                                                required
                                                                helperText={memberErrors[index]?.nominee_gender}
                                                                disabled={Boolean(id && Number(id) !== 0)}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                            <FormControl fullWidth required>
                                                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                                    <DatePicker
                                                                        label="Date of Birth"
                                                                        value={member?.nominee?.dob ? dayjs(member.nominee.dob) : null}
                                                                        onChange={(date) => {
                                                                            handleMemberChange(index, 'nominee', {
                                                                                dob: date
                                                                            });
                                                                        }}
                                                                        format="DD/MM/YYYY"
                                                                        helperText={memberErrors[index]?.nominee_dob}
                                                                        slotProps={{
                                                                            textField: {
                                                                                fullWidth: true,
                                                                                required: true,
                                                                                error: Boolean(memberErrors[index]?.nominee_dob),
                                                                                helperText: memberErrors[index]?.nominee_dob,
                                                                                disabled: id && Number(id) !== 0,
                                                                                sx: {
                                                                                    '& .MuiOutlinedInput-root': {
                                                                                        '&::before': {
                                                                                            content: '""',
                                                                                            position: 'absolute',
                                                                                            left: 0,
                                                                                            top: 0,
                                                                                            bottom: 0,
                                                                                            width: '3px',
                                                                                            backgroundColor: 'red',
                                                                                            zIndex: 1,
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }}
                                                                    />
                                                                </LocalizationProvider>
                                                            </FormControl>
                                                        </Grid>
                                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                                            <Dropdown
                                                                label="Relationship"
                                                                name="nominee_relation"
                                                                options={getFilteredRelations().map(relation => ({
                                                                    label: relation.label_name,
                                                                    value: relation.id
                                                                }))}
                                                                value={member?.nominee?.relation || ''}
                                                                onChange={(e) => handleMemberChange(index, 'nominee', {
                                                                    relation: e.target.value
                                                                })}
                                                                fullWidth
                                                                required
                                                                helperText={memberErrors[index]?.nominee_relation}
                                                                disabled={Boolean(id && Number(id) !== 0)}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                </CardContent>
                                            </Card>
                                            {/* Appointee Section - Only show if nominee is under 18 */}
                                            {member?.nominee?.dob && dayjs().diff(dayjs(member.nominee.dob), 'years') < 18 && (
                                                <Card sx={{
                                                    mt: 2,
                                                    backgroundColor: '#f8f9fa',
                                                    boxShadow: '0 4px 8px rgba(82, 138, 126, 0.15)',
                                                    border: '1px solid #528A7E',
                                                    borderRadius: '8px',
                                                    position: 'relative',
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        top: 0,
                                                        left: 0,
                                                        right: 0,
                                                        height: '4px',
                                                        backgroundColor: '#528A7E',
                                                        borderRadius: '8px 8px 0 0'
                                                    }
                                                }}>
                                                    <CardHeader
                                                        title="Appointee Details"
                                                        sx={{
                                                            backgroundColor: 'rgba(82, 138, 126, 0.08)',
                                                            borderBottom: '1px solid #528A7E',
                                                            '& .MuiCardHeader-title': {
                                                                fontSize: '1.1rem',
                                                                color: '#528A7E',
                                                                fontWeight: '600',
                                                                letterSpacing: '0.5px'
                                                            }
                                                        }}
                                                    />
                                                    <CardContent sx={{ backgroundColor: 'white' }}>
                                                        <Grid container spacing={2}>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <CustomTextField
                                                                    name="appointee_name"
                                                                    label="Appointee Name"
                                                                    value={member?.appointee?.name || ''}
                                                                    onChange={(e) => handleMemberChange(index, 'appointee', {
                                                                        name: e.target.value
                                                                    })}
                                                                    fullWidth
                                                                    helperText={memberErrors[index]?.appointee_name}
                                                                    isRequired
                                                                    disabled={Boolean(id && Number(id) !== 0)}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <Dropdown
                                                                    label="Gender"
                                                                    name="appointee_gender"
                                                                    options={genderOptions?.map(option => ({
                                                                        label: option.label_name,
                                                                        value: option.id
                                                                    }))}
                                                                    value={member?.appointee?.gender || ''}
                                                                    onChange={(e) => handleMemberChange(index, 'appointee', {
                                                                        gender: e.target.value
                                                                    })}
                                                                    fullWidth
                                                                    required
                                                                    helperText={memberErrors[index]?.appointee_gender}
                                                                    disabled={Boolean(id && Number(id) !== 0)}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <FormControl fullWidth required>
                                                                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                                        <DatePicker
                                                                            label="Date of Birth"
                                                                            value={member?.appointee?.dob ? dayjs(member.appointee.dob) : null}
                                                                            onChange={(date) => {
                                                                                handleMemberChange(index, 'appointee', {
                                                                                    dob: date
                                                                                });
                                                                            }}
                                                                            format="DD/MM/YYYY"
                                                                            helperText={memberErrors[index]?.appointee_dob}
                                                                            maxDate={dayjs().subtract(18, 'years')}
                                                                            slotProps={{
                                                                                textField: {
                                                                                    fullWidth: true,
                                                                                    required: true,
                                                                                    error: Boolean(memberErrors[index]?.appointee_dob),
                                                                                    helperText: memberErrors[index]?.appointee_dob,
                                                                                    disabled: id && Number(id) !== 0,
                                                                                    sx: {
                                                                                        '& .MuiOutlinedInput-root': {
                                                                                            '&::before': {
                                                                                                content: '""',
                                                                                                position: 'absolute',
                                                                                                left: 0,
                                                                                                top: 0,
                                                                                                bottom: 0,
                                                                                                width: '3px',
                                                                                                backgroundColor: 'red',
                                                                                                zIndex: 1,
                                                                                            }
                                                                                        },
                                                                                    }
                                                                                }
                                                                            }}
                                                                        />
                                                                    </LocalizationProvider>
                                                                </FormControl>
                                                            </Grid>
                                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                                <Dropdown
                                                                    label="Relationship"
                                                                    name="appointee_relation"
                                                                    options={getFilteredRelations().map(relation => ({
                                                                        label: relation.label_name,
                                                                        value: relation.id
                                                                    }))}
                                                                    value={member?.appointee?.relation || ''}
                                                                    onChange={(e) => handleMemberChange(index, 'appointee', {
                                                                        relation: e.target.value
                                                                    })}
                                                                    fullWidth
                                                                    required
                                                                    helperText={memberErrors[index]?.appointee_relation}
                                                                    disabled={Boolean(id && Number(id) !== 0)}
                                                                />
                                                            </Grid>
                                                        </Grid>
                                                    </CardContent>
                                                </Card>
                                            )}
                                        </CardContent>
                                    </Card>
                                ))
                            }
                        </Box>
                    )}

                    {/* Premium Details - Only show if member section is complete */}
                    {(formData.insurance_company_name && formData.insurance_company_branch && formData.imf_branch && formData.agent_id && formData.product_type && formData.product_name && formData.sub_product_name && formData.member_type) && (
                        <Box sx={{
                            width: '100%',
                            borderTop: '2px solid #E0E0E0',
                            paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                            paddingInline: { xs: '0.5rem', sm: '1rem' },

                        }}>
                            {/* Section Header */}
                            <Box sx={{
                                width: '100%',
                                padding: { xs: "10px", sm: "15px" },
                                borderRadius: "4px",
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            }}>
                                <Typography
                                    variant="h5"
                                    sx={{
                                        fontSize: '18px',
                                        fontWeight: "700",
                                        color: '#4C5157',
                                    }}
                                >
                                    Premium Details
                                </Typography>
                                {(id && transactionStatus.payment_type || Boolean(isPaymentSuccess === 2)) &&
                                    <Button
                                        onClick={() => handlePaymentCheck()}
                                        variant="outlined"
                                        size="small"
                                        sx={{
                                            maxWidth: '150px',
                                            width: '100%',
                                            mx: 0.5,
                                            color: 'green',
                                            borderColor: 'green',
                                            textTransform: 'none'
                                        }}
                                    >
                                        Payment Check
                                    </Button>
                                }
                                {(id && (Number(id) === 0 || Boolean(isPaymentSuccess === 0))) && !isCancelled && <Button
                                    onClick={() => {
                                        if (Number(id) === 0 && !formData.ckyc_number) {
                                            handleCkycCheck();
                                        } else {
                                            handlePayment();
                                        }
                                    }}
                                    variant="outlined"
                                    size="small"
                                    sx={{
                                        maxWidth: '150px',
                                        width: '100%',
                                        mx: 0.5,
                                        color: 'green',
                                        borderColor: 'green',
                                        textTransform: 'none'
                                    }}
                                >
                                    {(Number(id) === 0 && !formData.ckyc_number) ? 'CHECK FOR CKYC' : 'PROCEED TO PAYMENT'}
                                </Button>}
                            </Box>
                            <Divider />

                            {/* Form Fields */}
                            <Grid container spacing={2} sx={{
                                width: '100%',
                                padding: { xs: '1rem', sm: '1.5rem' },
                                '& .MuiGrid-item': {
                                    width: '100%'
                                }
                            }}>
                                {/* Show deductible and sum insured for floater member type */}
                                {(formData.member_type === 'family floater' && !isCancelled) && (
                                    <>
                                        {masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('advantage top up') &&
                                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                                <Dropdown
                                                    label="Deductible"
                                                    name="deductible"
                                                    options={deductileOptions}
                                                    value={members?.[0]?.deductible || ''}
                                                    onChange={(e) => {
                                                        setMembers(prevMembers =>
                                                            prevMembers?.map(member => ({
                                                                ...member,
                                                                deductible: e.target.value
                                                            }))
                                                        );
                                                    }}
                                                    prefixSymbol={'₹'}
                                                    fullWidth
                                                    helperText={formErrors.deductible}
                                                    required
                                                    disabled={Boolean(id)}
                                                />
                                            </Grid>
                                        }
                                        <Grid item xs={12} sm={6} md={4} lg={3}>
                                            <Dropdown
                                                name="sum_insured"
                                                label="Sum Insured"
                                                value={members?.[0]?.sum_insured || ''}
                                                prefixSymbol={'₹'}
                                                options={sumInsuredOptions}
                                                onChange={(e) => {
                                                    setMembers(prevMembers =>
                                                        prevMembers?.map(member => ({
                                                            ...member,
                                                            sum_insured: e.target.value
                                                        }))
                                                    );
                                                }}
                                                fullWidth
                                                helperText={memberErrors?.[0]?.sum_insured}
                                                isRequired
                                                disabled={Boolean(id)}
                                            />
                                        </Grid>
                                    </>
                                )}

                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="net_premium"
                                        label="Net Premium"
                                        value={formatToTwoDecimals(formData.net_premium)}
                                        prefixSymbol={'₹'}
                                        onChange={handleChange}
                                        fullWidth
                                        helperText={formErrors.net_premium}
                                        isRequired
                                        isDisabled={Boolean(id || selectedQuotation)}
                                    />
                                </Grid>
                                {/* Add Family Discount Fields */}
                                {(id && masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') || formData.family_discount_percentage) && (
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="family_discount_percentage"
                                            label="Family Discount %"
                                            value={formData.family_discount_percentage}
                                            prefixSymbol={'%'}
                                            onChange={handleChange}
                                            fullWidth
                                            helperText={formErrors.family_discount_percentage}
                                            isRequired
                                            isDisabled={Boolean(id || selectedQuotation)}
                                        />
                                    </Grid>
                                )}
                                {(formData.family_discount_amount || masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha')) && (
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="family_discount_amount"
                                            label="Family Discount Amount"
                                            value={formData.family_discount_amount}
                                            prefixSymbol={'₹'}
                                            onChange={handleChange}
                                            fullWidth
                                            helperText={formErrors.family_discount_amount}
                                            isRequired
                                            isDisabled={Boolean(id || selectedQuotation)}
                                        />
                                    </Grid>
                                )}
                                {(id && masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') || formData.long_term_discount_percentage) && (
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="long_term_discount_percentage"
                                            label="Long Term Discount %"
                                            value={formData.long_term_discount_percentage}
                                            prefixSymbol={'%'}
                                            onChange={handleChange}
                                            fullWidth
                                            helperText={formErrors.long_term_discount_percentage}
                                            isRequired
                                            isDisabled={Boolean(id || selectedQuotation)}
                                        />
                                    </Grid>
                                )}
                                {(masterProducts.find(product => product.id === formData.product_name)?.product_name.toLowerCase().includes('fg accident suraksha') || formData.long_term_discount_amount) && (
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="long_term_discount_amount"
                                            label="Long Term Discount Amount"
                                            value={formData.long_term_discount_amount}
                                            prefixSymbol={'₹'}
                                            onChange={handleChange}
                                            fullWidth
                                            helperText={formErrors.long_term_discount_amount}
                                            isRequired
                                            isDisabled={Boolean(id || selectedQuotation)}
                                        />
                                    </Grid>
                                )}

                                {/* Existing GST fields */}
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="gst_percentage"
                                        label="GST Percentage"
                                        value={formData.gst_percentage || 18}
                                        prefixSymbol={'%'}
                                        onChange={handleChange}
                                        fullWidth
                                        helperText={formErrors.gst_percentage}
                                        isRequired
                                        isDisabled={Boolean(id || selectedQuotation)}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="gst_amount"
                                        label="GST Amount"
                                        value={formatToTwoDecimals(formData.gst_amount)}
                                        prefixSymbol={'₹'}
                                        onChange={handleChange}
                                        fullWidth
                                        helperText={formErrors.gst_amount}
                                        isDisabled
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="total_premium"
                                        label="Total Premium"
                                        value={(formData.total_premium)}
                                        prefixSymbol={'₹'}
                                        onChange={handleChange}
                                        fullWidth
                                        helperText={formErrors.total_premium}
                                        isRequired
                                        isDisabled={Boolean(id)}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <FormControl fullWidth required >
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <DatePicker
                                                label="Start Date"
                                                value={formData.start_date ? dayjs(formData.start_date) : null}
                                                onChange={(newDate) => handleDateChange('start_date', newDate)}
                                                minDate={dayjs(formData?.received_date || formData?.cheque_date || formData?.dd_date || formData?.online_date || formData?.transaction_date)}
                                                maxDate={dayjs(formData?.received_date || formData?.cheque_date || formData?.dd_date || formData?.online_date || formData?.transaction_date).add(90, 'day')}
                                                format="DD/MM/YYYY"
                                                disabled={Boolean(id && (Boolean(fetchedProposal?.start_date) || !Boolean(isPaymentSuccess === 1)))}
                                                slotProps={{
                                                    textField: {
                                                        fullWidth: true,
                                                        error: Boolean(formErrors.start_date),
                                                        helperText: formErrors.start_date,
                                                        inputProps: {
                                                            readOnly: true // This makes the input read-only
                                                        },
                                                        sx: {
                                                            '& .MuiOutlinedInput-root': {
                                                                ...(id && {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }),
                                                            }
                                                        }
                                                    }
                                                }}
                                            />
                                        </LocalizationProvider>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <Dropdown
                                        label="Tenure"
                                        name="tenure"
                                        options={durationOptions?.map(option => ({
                                            label: option.label_name,
                                            value: option.api_name
                                        }))}
                                        value={formData.tenure}
                                        onChange={handleChange}
                                        fullWidth
                                        helperText={formErrors.tenure}
                                        required
                                        disabled={Boolean(id)}
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                width: '100%'
                                            }
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <FormControl fullWidth required>
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <DatePicker
                                                label="End Date"
                                                value={formData.end_date ? dayjs(formData.end_date) : null}
                                                onChange={(date) => handleDateChange('end_date', date)}
                                                disabled
                                                format="DD/MM/YYYY"
                                                helperText={formErrors.end_date}
                                                slotProps={{
                                                    textField: {
                                                        fullWidth: true,
                                                        sx: {
                                                            '& .MuiOutlinedInput-root': {
                                                                '&::before': {
                                                                    content: '""',
                                                                    position: 'absolute',
                                                                    left: 0,
                                                                    top: 0,
                                                                    bottom: 0,
                                                                    width: '3px',
                                                                    backgroundColor: 'red',
                                                                    zIndex: 1,
                                                                }
                                                            },
                                                        }
                                                    }
                                                }}
                                            />
                                        </LocalizationProvider>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <FormControl fullWidth required >
                                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                                            <DatePicker
                                                label="Issue Date"
                                                value={formData.policy_issue_date ? dayjs(formData.policy_issue_date) : null}
                                                onChange={(date) => handleDateChange('policy_issue_date', date)}
                                                maxDate={dayjs(formData.start_date).subtract(1, 'day')}
                                                minDate={dayjs(formData.start_date).subtract(90, 'day')}
                                                format="DD/MM/YYYY"
                                                helperText={formErrors.policy_issue_date}
                                                disabled={Boolean(
                                                    !formData?.start_date
                                                    || (id
                                                        && (fetchedProposal?.quotation_number
                                                            || fetchedProposal?.policy_issue_date
                                                            || Number(id) === 0
                                                            || !Boolean(isPaymentSuccess === 1))))}
                                                slotProps={{
                                                    textField: {
                                                        inputProps: {
                                                            readOnly: true // This makes the input read-only
                                                        },
                                                        fullWidth: true,
                                                        error: Boolean(formErrors.policy_issue_date),
                                                        helperText: formErrors.policy_issue_date,
                                                        ...((id && formData.start_date) ? {
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                },
                                                            }
                                                        } : {}),
                                                    }
                                                }}
                                            />
                                        </LocalizationProvider>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomTextField
                                        name="policy_number"
                                        label="Policy Number"
                                        value={formData.policy_number}
                                        onChange={handleChange}
                                        fullWidth
                                        helperText={formErrors.policy_number}
                                        isRequired={Boolean(Number(id))}
                                        isDisabled={Boolean(
                                            !formData.start_date ||
                                            (id
                                                && (fetchedProposal?.quotation_number
                                                    || fetchedProposal?.policy_number
                                                    || Number(id) === 0
                                                    || !Boolean(isPaymentSuccess === 1))))}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4} lg={3}>
                                    <CustomFileUpload
                                        name="policy_pdf"
                                        section_name="Upload Policy Pdf"
                                        accept=".pdf"
                                        label={"Upload Policy Pdf"}
                                        onFileSelect={handleFileSelect}
                                        insertedFile={!formData.policy_pdf ? null : {
                                            section_name: 'document',
                                            name: 'File',
                                            url: formData.policy_pdf
                                        }}
                                        helperText={formErrors.policy_pdf}
                                        error={Boolean(formErrors.policy_pdf)}
                                        required={Boolean(formData.policy_number)}
                                        disabled={Boolean(
                                            !formData.start_date ||
                                            (id
                                                && (fetchedProposal?.quotation_number
                                                    || fetchedProposal?.policy_number
                                                    || Number(id) === 0
                                                    || !Boolean(isPaymentSuccess === 1))))}
                                    />
                                </Grid>
                            </Grid>
                        </Box>
                    )}

                    {/* Payment Details - Only show if proposal details section is complete */}
                    {(formData.net_premium && formData.tenure && !isCancelled) && (<Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Payment Details
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Dropdown
                                    label="Select Payment Type"
                                    name="payment_type"
                                    options={paymentTypes?.map(type => ({
                                        label: type.label_name,
                                        value: String(type.id)  // Convert id to string
                                    }))}
                                    value={String(formData.payment_type || '')}  // Convert to string and provide default
                                    onChange={handleChange}
                                    fullWidth
                                    required
                                    helperText={formErrors.payment_type}
                                    disabled={Boolean(id)}
                                />
                            </Grid>

                            {/* Cash Fields */}
                            {hasPaymentType(['PAY_CASH', 'PAY_CHEQUE_CASH', 'PAY_DD_CASH']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="cash_amount"
                                            label="Cash Amount"
                                            value={formData.cash_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.cash_amount}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="Received Date"
                                                    value={formData.received_date ? dayjs(formData.received_date) : null}
                                                    onChange={(date) => handleDateChange('received_date', date)}
                                                    disabled={Boolean(id || Boolean(isPaymentSuccess === 1))}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(1, 'months')}
                                                    helperText={formErrors.received_date}
                                                    format="DD/MM/YYYY"
                                                    slotProps={{
                                                        textField: {
                                                            inputProps: {
                                                                readOnly: true // This makes the input read-only
                                                            },
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiInputBase-input.Mui-disabled': {
                                                                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                                                                },
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                </>
                            )}

                            {/* Cheque Fields */}
                            {hasPaymentType(['PAY_CHEQUE', 'PAY_CHEQUE_CASH']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="cheque_amount"
                                            label="Cheque Amount"
                                            value={formData.cheque_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.cheque_amount}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="cheque_number"
                                            label="Cheque Number"
                                            value={formData.cheque_number}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.cheque_number}
                                            disabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="Cheque Date"
                                                    value={formData.cheque_date ? dayjs(formData.cheque_date) : null}
                                                    onChange={(date) => handleDateChange('cheque_date', date)}
                                                    format="DD/MM/YYYY"
                                                    disabled={Boolean(id || Boolean(isPaymentSuccess === 1))}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(90, 'days')}
                                                    helperText={formErrors.cheque_date}
                                                    slotProps={{
                                                        textField: {
                                                            inputProps: {
                                                                readOnly: true // This makes the input read-only
                                                            },
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="bank_name"
                                            label="Bank Name"
                                            value={formData.bank_name}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.bank_name}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="branch_name"
                                            label="Branch Name"
                                            value={formData.branch_name}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.branch_name}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                </>
                            )}

                            {/* DD Fields */}
                            {hasPaymentType(['PAY_DD', 'PAY_DD_CASH']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="dd_amount"
                                            label="DD Amount"
                                            value={formData.dd_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.dd_amount}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="dd_number"
                                            label="DD Number"
                                            value={formData.dd_number}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.dd_number}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="DD Date"
                                                    value={formData.dd_date ? dayjs(formData.dd_date) : null}
                                                    onChange={(date) => handleDateChange('dd_date', date)}
                                                    format="DD/MM/YYYY"
                                                    disabled={Boolean(id || Boolean(isPaymentSuccess === 1))}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(90, 'days')}
                                                    helperText={formErrors.dd_date}
                                                    slotProps={{
                                                        textField: {
                                                            inputProps: {
                                                                readOnly: true // This makes the input read-only
                                                            },
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="bank_name"
                                            label="Bank Name"
                                            value={formData.bank_name}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.bank_name}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="branch_name"
                                            label="Branch Name"
                                            value={formData.branch_name}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            helperText={formErrors.branch_name}
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                        />
                                    </Grid>
                                </>
                            )}

                            {/* Online Payment Fields */}
                            {hasPaymentType(['PAY_ONLINE']) && (
                                <>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="online_amount"
                                            label="Amount"
                                            value={formData.online_amount}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                            helperText={formErrors.online_amount}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <FormControl fullWidth required>
                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                <DatePicker
                                                    label="Transaction Date"
                                                    value={formData.transaction_date ? dayjs(formData.transaction_date) : null}
                                                    onChange={(date) => handleDateChange('transaction_date', date)}
                                                    format="DD/MM/YYYY"
                                                    disabled={Boolean(id || Boolean(isPaymentSuccess === 1))}
                                                    maxDate={dayjs()}
                                                    minDate={dayjs().subtract(7, 'days')}
                                                    helperText={formErrors.transaction_date}
                                                    slotProps={{
                                                        textField: {
                                                            inputProps: {
                                                                readOnly: true // This makes the input read-only
                                                            },
                                                            fullWidth: true,
                                                            sx: {
                                                                '& .MuiOutlinedInput-root': {
                                                                    '&::before': {
                                                                        content: '""',
                                                                        position: 'absolute',
                                                                        left: 0,
                                                                        top: 0,
                                                                        bottom: 0,
                                                                        width: '3px',
                                                                        backgroundColor: 'red',
                                                                        zIndex: 1,
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }}
                                                />
                                            </LocalizationProvider>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <Dropdown
                                            label="Transaction Type"
                                            name="transaction_type"
                                            options={[
                                                { label: 'UPI', value: 'UPI' },
                                                { label: 'Net Banking', value: 'Net Banking' },
                                                { label: 'Credit Card', value: 'Credit Card' },
                                                { label: 'Debit Card', value: 'Debit Card' },
                                                { label: 'Wallet', value: 'Wallet' },
                                                { label: 'Other', value: 'Other' }
                                            ]}
                                            value={formData.transaction_type}
                                            onChange={handleChange}
                                            fullWidth
                                            required
                                            disabled={Boolean(isPaymentSuccess === 1)}
                                            helperText={formErrors.transaction_type}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={4} lg={3}>
                                        <CustomTextField
                                            name="receipt_number"
                                            label="Receipt Number"
                                            value={formData.receipt_number}
                                            onChange={handleChange}
                                            fullWidth
                                            isRequired
                                            isDisabled={Boolean(isPaymentSuccess === 1)}
                                            helperText={formErrors.receipt_number}
                                        />
                                    </Grid>
                                </>
                            )}
                        </Grid>
                    </Box>)}

                    {/* Remarks */}
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                Remarks
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid item xs={12}
                            sx={{
                                width: '100%',
                                padding: { xs: '1rem', sm: '1.5rem' },
                                '& .MuiGrid-item': {
                                    width: '100%'
                                }
                            }}>
                            <CustomTextField
                                label="Remarks"
                                multiline
                                rows={3}
                                fullWidth
                                disabled={Boolean(id)}
                                name="remarks"
                                value={formData.remarks}
                                onChange={handleChange}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Box>
                    {/* User Info */}
                    <Box sx={{
                        width: '100%',
                        borderTop: '2px solid #E0E0E0',
                        paddingTop: { xs: '0.2rem', sm: '0.5rem' },
                        paddingInline: { xs: '0.5rem', sm: '1rem' },

                    }}>
                        {/* Section Header */}
                        <Box sx={{
                            width: '100%',
                            padding: { xs: "10px", sm: "15px" },
                            borderRadius: "4px",
                        }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontSize: '18px',
                                    fontWeight: "700",
                                    color: '#4C5157',
                                }}
                            >
                                User Info
                            </Typography>
                        </Box>
                        <Divider />

                        {/* Form Fields */}
                        <Grid container spacing={2} sx={{
                            width: '100%',
                            padding: { xs: '1rem', sm: '1.5rem' },
                            '& .MuiGrid-item': {
                                width: '100%'
                            }
                        }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="created_by"
                                    label="Created By"
                                    value={formData.created_by || ''}
                                    fullWidth
                                    isDisabled={true}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <CustomTextField
                                    name="created_at"
                                    label="Created At"
                                    value={dayjs(formData.created_at).format('DD/MM/YYYY') || dayjs(new Date()).format('DD/MM/YYYY')}
                                    fullWidth
                                    isDisabled={true}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                </form>
            )
            }

            {/* Combined Dialog */}
            <Dialog
                open={openDialog}
                onClose={() => handleDialogClose(false)}
                aria-labelledby="dialog-title"
                aria-describedby="dialog-description"
                disableBackdropClick
                disableEscapeKeyDown
            >
                <DialogTitle id="dialog-title">
                    {dialogConfig.title}
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="dialog-description">
                        {dialogConfig.message}
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => handleDialogClose(false)} color="error">
                        {dialogConfig.type === 'ckyc' ? '' : dialogConfig.type === 'group_code' ? 'Create Group' : 'Cancel'}
                    </Button>
                    <Button onClick={() => handleDialogClose(true)} color="primary" variant="contained">
                        {dialogConfig.type === 'ckyc' ? 'Proceed' : dialogConfig.type === 'group_code' ? 'Link Group' : 'Proceed'}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box >
    )

}
export default CreateProposal;