const knexConfig = require('../../../knexfile');
const { getCurrentTimestamp, formatDateToDDMMYYYY } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new employee loan and its associated EMIs
const create = async (loanData) => {
    try {
        const loanResult = await db('employee_loan').insert(loanData);
        const loanId = loanResult[0];
        return loanId;
    } catch (error) {
        console.error("Error creating loan:", error);
        throw error;
    }
};

// Find all employee loans with employee details and loan type information
const findEmployeeLoans = async () => {
    try {
        const loans = await db('employee_loan')
            .leftJoin('pick_list', 'employee_loan.loan_type', '=', 'pick_list.id')
            .leftJoin('employee_personal_info', 'employee_loan.employee_id', '=', 'employee_personal_info.id')
            .select(
                'employee_loan.id',
                'employee_loan.loan_id',
                'employee_loan.admin_approval',
                'employee_loan.loan_amount',
                'employee_loan.paid_amount',
                db.raw('employee_loan.loan_amount - COALESCE(employee_loan.paid_amount, 0) as balance_amount'),
                'employee_loan.emi',
                'employee_loan.issue_date',
                'employee_loan.tenure',
                'employee_loan.end_date',
                'employee_loan.status',
                'pick_list.label_name as loan_type',
                db.raw("CONCAT(SUBSTRING_INDEX(employee_personal_info.employee_full_name, ' ', 1), ' (', employee_personal_info.user_id, ')') as employee_name")
            )
            .distinct('employee_loan.id')
            .orderBy('id', 'desc');
        // Format tenure and employee name
        return loans.map(loan => {
            return {
                ...loan,
                tenure: `${loan.tenure} months`,
            };
        });
    } catch (error) {
        throw error;
    }
};

// Find all EMIs related to a given loan ID
const findEmployeeLoanEmi = async (loanId) => {
    try {
        return await db('employee_loan_emi').where({ employee_loan_id: loanId });
    } catch (error) {
        throw error;
    }
};

// Find a loan by ID and return loan + employee data
const findEmployeeLoanById = async (id) => {
    try {
        return await db('employee_loan').
            // join('employees', 'employee_loan.employee_id', '=', 'employees.id').
            // join('employee_loan_emi', 'employee_loan.id', '=', 'employee_loan_emi.employee_loan_id').
            where('employee_loan.id', id).first();
    } catch (error) {
        throw error;
    }
};

// Update loan by ID
const updateLoan = async (id, data) => {
    try {
        if (data.admin_approval === 'APPROVED') {
            //Find the approved employee loans
            const employeeLoans = await db('employee_loan').where('admin_approval', 'APPROVED');
            const loan_id = "PWS-EMP-" + String(employeeLoans.length + 1).padStart(5, '0');
            const updatedData = {
                ...data,
                loan_id: loan_id,
                updated_at: getCurrentTimestamp(),
            }
            // Update the loan
            const employee_loan = await db('employee_loan').where({ id }).update(updatedData);
            if (employee_loan === 1) {
                // Create the EMIs
                let emiData = [];
                const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                let loan_amount = data.loan_amount;
                const emi_amount = Math.round(loan_amount / updatedData.tenure);
                let currentYear = new Date(updatedData.start_date).getFullYear(); // Get current year from start_date
                for (let i = 0; i < updatedData.tenure; i++) {
                    const emi_element = {
                        month: months[(new Date(updatedData.start_date).getMonth() + i + 1) % 12],
                        employee_loan_id: loan_id,
                        year: currentYear,
                        emi_amount: i !== updatedData.tenure - 1 ? emi_amount : loan_amount,
                        created_by: updatedData.updated_by,
                        updated_by: updatedData.updated_by,
                    }
                    emiData.push(emi_element);
                    loan_amount -= emi_amount;
                    if (months[(new Date(updatedData.start_date).getMonth() + i) % 12] === 'December') {
                        currentYear++;
                    }
                }
                const checkIfEmiExists = await db('employee_loan_emi').where({ employee_loan_id: loan_id });
                if (checkIfEmiExists.length === 0) {
                    return await db('employee_loan_emi').insert(emiData);
                }
            }
        } else if (data.admin_approval === 'REJECTED') {
            return await db('employee_loan').where({ id }).update({ status: 0, admin_approval: 'REJECTED', updated_at: getCurrentTimestamp() });
        }
    } catch (error) {
        throw error;
    }
};

// Update EMI by ID
const updateEmi = async (id, data) => {
    try {
        const { paid_amount, ...emiData } = data;
        const employeeLoan = await db('employee_loan').where({ loan_id: emiData.employee_loan_id }).update({ paid_amount, updated_at: getCurrentTimestamp(), updated_by: data.updated_by });
        if (employeeLoan) {
            return await db('employee_loan_emi').where({ id }).update({ ...emiData, updated_at: getCurrentTimestamp() });
        }
    } catch (error) {
        throw error;
    }
};

// Soft delete a loan by ID
const deleteLoan = async (id) => {
    try {
        return await db('employee_loan').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
    } catch (error) {
        throw error;
    }
};

// Soft delete an EMI by ID
const deleteEmi = async (id) => {
    try {
        return await db('employee_loan_emi').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findEmployeeLoans,
    findEmployeeLoanEmi,
    findEmployeeLoanById,
    updateLoan,
    updateEmi,
    deleteLoan,
    deleteEmi,
};