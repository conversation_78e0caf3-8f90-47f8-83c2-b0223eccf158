import React, { useState, useEffect } from 'react';
import {
    Box,
    Grid,
    Button,
    Typography,
    FormControl,
    RadioGroup,
    FormControlLabel,
    Radio,
    IconButton,
    Card,
    InputAdornment
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { toast } from 'react-toastify';
import ModuleName from '../../../components/table/ModuleName';
import Dropdown from '../../../components/table/DropDown';
import {
    getAllProducts, fetchInsuranceCompanies, getAllMasterProducts, getLocationByPincode,
    createQuotation, createFloaterOptions,
    getMasterProductByMainProductAndInsuranceCompany,
    getSubProductByProductDetails, getAllPickLists, getAllPAoccupationLists
} from '../../../redux/actions/action';
import CustomTextField from '../../../components/CustomTextField';
import CustomSection from '../../../components/CustomSection';
import BarLoader from '../../../components/BarLoader';
import Autocomplete from '@mui/material/Autocomplete';
import { calculateMaxSumInsured } from '../../../utils/Reusable';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';

const QuickQuotationPage = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const mainProducts = useSelector(state => state.mainProductReducer.products);
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
    const masterProducts = useSelector(state => state.productMasterReducer.products);
    const riskClass = useSelector(state => state.PA_occupationReducer.PA_occupation_Lists);

    const [productTypeOptions, setProductTypeOptions] = useState([]);
    const [coverTypeOptions, setCoverTypeOptions] = useState([]);
    const [formData, setFormData] = useState({
        family_type: 'individual',
        insurance_company_id: '',
        main_product_id: '',
        product_master_id: '',
        product_master_name: '',
        cover_type: '',
        deductible: '',
        pincode: '',
        company_name: '',
        duration: '',
        copay: 'no',
        relation: ''
    });
    const [deductableAmount, setDeductableAmount] = useState([]);
    const [loading, setLoading] = useState(false);
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [eligibilityMessage, setEligibilityMessage] = useState('');

    const durationOptions = [
        { value: '1', label: '1 Year' },
        { value: '2', label: '2 Years' },
        { value: '3', label: '3 Years' }
    ];

    // Add this after other state declarations
    const [additionalCover, setAdditionalCover] = useState({
        enabled: true,
        covers: {
            repatriation_funeral_expenses: 'yes', // Always yes, no toggle
            adaptation_allowance: 'no',
            child_education_support: 'no',
            family_transportation: 'no',
            hospital_cash: 'no',
            loan_protector: 'no',
            life_support_benefit: 'no',
            accidental_hospitalization: 'no',
            accidental_medical_expenses: 'no',
            broken_bones: 'no'
        }
    });

    useEffect(() => {
        // Check if user is logged in
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        setIsLoggedIn(!!token);
    }, []);

    useEffect(() => {
        dispatch(getAllPAoccupationLists());
    }, [dispatch]);

    const getSumInsuredOptions = (coverType) => {
        // If it's PLATINUM, we need to check which product it belongs to
        if (coverType === 'PLATINUM') {
            if (formData.product_master_name === 'FG HEALTH SURAKSHA') {
                return [
                    { value: '600000', label: '6 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '800000', label: '8 Lakhs' },
                    { value: '900000', label: '9 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            } else if (formData.product_master_name === 'FG HEALTH ABSOLUTE') {
                return [
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    { value: '3000000', label: '30 Lakhs' },
                    { value: '3500000', label: '35 Lakhs' }
                ];
            }
        }

        switch (coverType) {
            case 'GOLD':
                return [
                    //{ value: '50000', label: '50 Thousand' },
                   // { value: '100000', label: '1 Lakh' },
                   // { value: '150000', label: '1.5 Lakhs' },
                    //{ value: '200000', label: '2 Lakhs' },
                    //{ value: '250000', label: '2.5 Lakhs' },
                    //{ value: '300000', label: '3 Lakhs' },
                    //{ value: '350000', label: '3.5 Lakhs' },
                    //{ value: '400000', label: '4 Lakhs' },
                    //{ value: '450000', label: '4.5 Lakhs' },
                    { value: '500000', label: '5 Lakhs' }
                ];
            case 'PLATINUM':
                return [
                    { value: '600000', label: '6 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '800000', label: '8 Lakhs' },
                    { value: '900000', label: '9 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            case 'TOPAZ':
                return [
                    //{ value: '100000', label: '1 Lakh' },
                    //{ value: '200000', label: '2 Lakhs' },
                    //{ value: '300000', label: '3 Lakhs' },
                    //{ value: '400000', label: '4 Lakhs' },
                    { value: '500000', label: '5 Lakhs' }
                ];
            case 'RUBY':
                return [
                    { value: '600000', label: '6 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            case 'VITAL':
                return [
                    //{ value: '300000', label: '3 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            case 'SUPERIOR':
                return [
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' }
                ];
             case 'PREMIERE':
                 return [
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' }
                 ]; 
            case 'CLASSIC':
                return [
                    //{ value: '300000', label: '3 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            case 'PLATINUM':
                return [
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    { value: '3000000', label: '30 Lakhs' },
                    { value: '3500000', label: '35 Lakhs' }
                ];
              case 'SIGNATURE':
                  return [
                      { value: '5000000', label: '50 Lakhs' },
                      { value: '7500000', label: '75 Lakhs' },
                      { value: '10000000', label: '1 Crore' }
                  ]; 
            case 'VARISHTA BIMA':
                return [
                    { value: '200000', label: '2 Lakhs' },
                    { value: '300000', label: '3 Lakhs' },
                    { value: '400000', label: '4 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: ' 7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                ];
            default:
                return [];
        }
    };

    const getDeductableAmountOptions = (deductible, ageBand) => {
        // Get the starting age from the age band (e.g., "56-60" -> 56)
        const startAge = ageBand ? parseInt(ageBand.split('-')[0]) : 0;

        if (startAge <= 55) {
            // Day 1 - 55 years: Any deductible
            return [
                { value: '50000', label: '50 Thousand' },
                { value: '100000', label: '1 Lakh' },
                { value: '200000', label: '2 Lakhs' },
                { value: '300000', label: '3 Lakhs' },
                { value: '500000', label: '5 Lakhs' },
                { value: '750000', label: '7.5 Lakhs' },
                { value: '1000000', label: '10 Lakhs' },
                { value: '1500000', label: '15 Lakhs' },
                { value: '2000000', label: '20 Lakhs' },
                { value: '2500000', label: '25 Lakhs' },
                { value: '3000000', label: '30 Lakhs' },
                { value: '4000000', label: '40 Lakhs' }
            ];
        } else if (startAge <= 60) {
            // 56-60 years: Limited deductible options
            return [
                { value: '500000', label: '5 Lakhs' },
                { value: '750000', label: '7.5 Lakhs' },
                { value: '1000000', label: '10 Lakhs' },
                { value: '1500000', label: '15 Lakhs' },
                { value: '2000000', label: '20 Lakhs' },
                { value: '2500000', label: '25 Lakhs' },
                { value: '3000000', label: '30 Lakhs' },
                { value: '4000000', label: '40 Lakhs' }
            ];
        } else {
            // 61+ years: Most limited deductible options
            return [
                { value: '1500000', label: '15 Lakhs' },
                { value: '2000000', label: '20 Lakhs' },
                { value: '3000000', label: '30 Lakhs' },
                { value: '4000000', label: '40 Lakhs' }
            ];
        }
    };

    const getSumInsuredOptionsByDeductableAmount = (deductible, ageBand) => {
        const startAge = ageBand ? parseInt(ageBand.split('-')[0]) : 0;
        if (startAge <= 55) {
            return [
                //{ value: '50000', label: '50 Thousand' },
                //{ value: '100000', label: '1 Lakh' },
                //{ value: '200000', label: '2 Lakhs' },
                //{ value: '300000', label: '3 Lakhs' },
                { value: '500000', label: '5 Lakhs' },
                { value: '750000', label: '7.5 Lakhs' },
                { value: '1000000', label: '10 Lakhs' },
                { value: '1500000', label: '15 Lakhs' },
                { value: '2000000', label: '20 Lakhs' },
                { value: '2500000', label: '25 Lakhs' },
                { value: '3000000', label: '30 Lakhs' },
                { value: '4000000', label: '40 Lakhs' }
            ];
        } else if (startAge <= 60) {
            if (Number(deductible) <= 1000000) {
                return [
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                ]
            } else if (Number(deductible) > 2000000) {
                return [
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    { value: '3000000', label: '30 Lakhs' },
                    { value: '4000000', label: '40 Lakhs' },
                    { value: '5000000', label: '50 Lakhs' },
                    { value: '10000000', label: '1 Crore' },
                ]
            } else {
                return [
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    { value: '3000000', label: '30 Lakhs' },
                    { value: '4000000', label: '40 Lakhs' },
                    { value: '5000000', label: '50 Lakhs' },
                    { value: '10000000', label: '1 Crore' },
                ]
            }
        } else {
            return [
                { value: '1500000', label: '15 Lakhs' },
                { value: '2000000', label: '20 Lakhs' },
                { value: '2500000', label: '25 Lakhs' },
                { value: '3000000', label: '30 Lakhs' },
                { value: '4000000', label: '40 Lakhs' },
                { value: '5000000', label: '50 Lakhs' },
                { value: '10000000', label: '1 Crore' },
            ]
        }
    }

    const [members, setMembers] = useState([{
        ageBand: '',
        relation: 'SELF',
        occupation: '',
        annual_income: '',
        sumInsured: '',  // This will be used for all products except FG ACCIDENT SURAKSHA
        sumInsured_AD: '',
        sumInsured_PT: '',
        sumInsured_PP: '',
        sumInsured_TTD: ''
    }]);
    const [errors, setErrors] = useState({});

    useEffect(() => {
        dispatch(getAllProducts());
        dispatch(fetchInsuranceCompanies());
    }, [dispatch]);

    useEffect(() => {
        if (formData.main_product_id && formData.insurance_company_id) {
            dispatch(getMasterProductByMainProductAndInsuranceCompany({ mainProductId: formData.main_product_id, insuranceCompanyId: formData.insurance_company_id }));
        }
    }, [dispatch, formData.main_product_id, formData.insurance_company_id]);

    useEffect(() => {
        if (formData.product_master_id) {
            const selectedProduct = masterProducts.find(product => product.id === formData.product_master_id);

            if (selectedProduct) {
                dispatch(getSubProductByProductDetails({
                    mainProductId: formData.main_product_id,
                    insuranceCompanyId: formData.insurance_company_id,
                    productMasterId: formData.product_master_id
                }))
                    .then((response) => {
                        if (response?.payload) {
                            const coverOptions = response.payload.map(product => ({
                                value: product.sub_product_name,
                                label: product.sub_product_name
                            }));
                            setCoverTypeOptions(coverOptions);
                        } else {
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching cover types:', error);
                        setCoverTypeOptions([]);
                    });
            }
        } else {
        }
    }, [dispatch, formData.product_master_id, formData.main_product_id, formData.insurance_company_id, masterProducts]);

    // useEffect(() => {
    //     if (formData.cover_type) {
    //         if (formData.cover_type === 'SUPREME' || formData.cover_type === 'ELITE') {
    //             setDeductableAmount([
    //                 { value: '50000', label: '50 Thousand' },
    //                 { value: '100000', label: '1 Lakh' },
    //                 { value: '200000', label: '2 Lakhs' },
    //                 { value: '300000', label: '3 Lakhs' },
    //                 { value: '500000', label: '5 Lakhs' },
    //                 { value: '750000', label: '7.5 Lakhs' },
    //                 { value: '1000000', label: '10 Lakhs' },
    //                 { value: '1500000', label: '15 Lakhs' },
    //                 { value: '2000000', label: '20 Lakhs' },
    //                 { value: '2500000', label: '25 Lakhs' },
    //                 { value: '3000000', label: '30 Lakhs' },
    //                 { value: '4000000', label: '40 Lakhs' }
    //             ]);
    //         }
    //     }
    // }, [formData.cover_type]);

    useEffect(() => {
        if (String(formData.pincode).length === 6) {
            dispatch(getLocationByPincode(formData.pincode)).then((action) => {
                if (!action.payload || action.payload.length === 0) {
                    setErrors(prevErrors => ({
                        ...prevErrors,
                        pincode: 'This pincode does not exist'
                    }));
                } else {
                    setErrors(prevErrors => ({
                        ...prevErrors,
                        pincode: ''
                    }));
                }
            });
        }
    }, [formData.pincode, dispatch]);

    // Updated age band options with conditional logic
    const getAgeBandOptions = (productMasterName) => {
        const baseOptions = [
            { value: '0-17', label: '0-17 years' },
            { value: '18-25', label: '18-25 years' },
            { value: '26-30', label: '26-30 years' },
            { value: '31-35', label: '31-35 years' },
            { value: '36-40', label: '36-40 years' },
            { value: '41-45', label: '41-45 years' },
            { value: '46-50', label: '46-50 years' },
        ];

        const extendedOptions = [
            ...baseOptions,
            { value: '51-55', label: '51-55 years' },
            { value: '56-60', label: '56-60 years' },
            { value: '61-65', label: '61-65 years' },
            { value: '66-70', label: '66-70 years' },
            { value: '71-75', label: '71-75 years' },
            { value: '76-80', label: '76-80 years' },
        ];

        const varistaBimaOptions = [
            { value: '60-65', label: '60-65 years' },
            { value: '66-70', label: '66-70 years' },
            { value: '71-75', label: '71-75 years' },
            { value: '76-80', label: '76-80 years' }
        ];

        const varistaBimaFloaterOptions = [
            { value: '26-30', label: '26-30 years' },
            { value: '31-35', label: '31-35 years' },
            { value: '36-40', label: '36-40 years' },
            { value: '41-45', label: '41-45 years' },
            { value: '46-50', label: '46-50 years' },
            { value: '40-45', label: '40-45 years' },
            { value: '46-50', label: '46-50 years' },
            { value: '51-55', label: '51-55 years' },
            { value: '56-60', label: '56-60 years' },
            { value: '61-65', label: '61-65 years' },
            { value: '66-70', label: '66-70 years' },
            { value: '71-75', label: '71-75 years' },
            { value: '76-80', label: '76-80 years' }
        ];

        if (productMasterName === 'FG ADVANTAGE TOP UP') {
            return extendedOptions;
        } else if (productMasterName === 'FG VARISHTHA BIMA') {
            // Check if it's a floater policy
            if (formData.family_type === 'floater') {
                return varistaBimaFloaterOptions;
            }
            return varistaBimaOptions;
        } else {
            return baseOptions;
        }
    };

    useEffect(() => {
        if (formData.deductible) {
            const deductibleOptions = getDeductableAmountOptions(formData.deductible, formData.ageBand);
            if (deductibleOptions.length === 1) {
                // Auto-select the only available sum insured for all members
                setMembers(prevMembers =>
                    prevMembers.map(member => ({
                        ...member,
                        sumInsured: deductibleOptions[0].value,
                    }))
                );
            }
        }
    }, [formData.deductible]);

    useEffect(() => {
        if (formData.cover_type && formData.product_master_name !== 'FG ADVANTAGE TOP UP') {
            const sumInsuredOptions = getSumInsuredOptions(formData.cover_type);
            if (sumInsuredOptions.length === 1) {
                // Auto-select the only available sum insured for all members
                setMembers(prevMembers =>
                    prevMembers.map(member => ({
                        ...member,
                        sumInsured: sumInsuredOptions[0].value
                    }))
                );
            }
        }
    }, [formData.cover_type]);

    useEffect(() => {
        if (formData.insurance_company_id && formData.main_product_id) {
            setProductTypeOptions(handleProductTypeOptions());
        }
    }, [formData.insurance_company_id, formData.main_product_id])

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        if (name === 'pincode') {
            if (value.length > 6) {
                return
            };
            if (!/^\d*$/.test(value)) {
                setErrors(prevErrors => ({
                    ...prevErrors,
                    pincode: 'Please enter a numeric value'
                }));
                return;
            } else {
                setFormData(prev => ({
                    ...prev,
                    [name]: value
                }))
                setErrors(prev => ({
                    ...prev,
                    [name]: ''
                }))
            }
        } else if (name === 'deductible') {
            // For floater type, use the highest age band among members
            if (formData.family_type === 'floater') {
                const highestAgeBand = members.reduce((highest, member) => {
                    if (!member.ageBand) return highest;
                    const currentStart = parseInt(member.ageBand.split('-')[0]);
                    const highestStart = parseInt(highest.split('-')[0] || '0');
                    return currentStart > highestStart ? member.ageBand : highest;
                }, '');

                const validOptions = getDeductableAmountOptions(value, highestAgeBand);

                // Check if selected value is valid for the age group
                if (!validOptions.some(option => option.value === value)) {
                    // If invalid, select the first valid option
                    const defaultValue = validOptions[0]?.value || '';
                    setFormData(prev => ({
                        ...prev,
                        deductible: defaultValue
                    }));
                    setMembers(prevMembers =>
                        prevMembers.map(member => ({
                            ...member,
                            deductible: defaultValue,
                        }))
                    );
                    return;
                }
            }

            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
            setMembers(prevMembers =>
                prevMembers.map(member => ({
                    ...member,
                    [name]: value,
                }))
            );
        } else if (name === 'insurance_company_id') {
            const selectedCompany = insuranceCompanies.find(company => company.id === value);
            setFormData(prev => ({
                ...prev,
                [name]: value,
                company_name: selectedCompany ? selectedCompany.insurance_company_name : ''
            }));

            // Reset main product and product master selections when insurance company changes
            setFormData(prev => ({
                ...prev,
                main_product_id: '',
                product_master_id: '',
                product_master_name: ''
            }));
        } else if (name === 'main_product_id') {
            const selectedProduct = mainProducts.find(product => product.id === value);
            setFormData(prev => ({
                ...prev,
                [name]: value,
                product_master_name: selectedProduct ? selectedProduct?.main_product : ''
            }));
            // Reset product master selection when main product changes
            setFormData(prev => ({
                ...prev,
                product_master_id: ''
            }));
        } if (name === 'product_master_id') {
            const selectedProduct = masterProducts.find(product => product.id === value);
            setFormData(prev => ({
                ...prev,
                [name]: value,
                product_master_name: selectedProduct ? selectedProduct?.product_name : ''
            }));
        } else {
            // Handle other input changes
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }

        // Clear errors if any
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleRadioChange = (e) => {
        const newFamilyType = e.target.value;
        setFormData(prev => ({
            ...prev,
            family_type: newFamilyType
        }));

        if (newFamilyType === 'floater') {
            if (members.length === 1) {
                setMembers([
                    { ageBand: '', relation: '' },
                    { ageBand: '', relation: '' }
                ]);
            }
        }
    };

    const handleAddMember = () => {
        // Check if the product is FG ACCIDENT SURAKSHA
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            if (members.length < 4) { // Restrict total members to 4
                setMembers([...members, {
                    ageBand: '',
                    relation: '',
                    occupation: '',
                    annual_income: '',
                    sumInsured: '',
                    sumInsured_AD: '',
                    sumInsured_PT: '',
                    sumInsured_PP: '',
                    sumInsured_TTD: ''
                }]);
            } else {
                toast.warning('Maximum 4 members allowed for FG ACCIDENT SURAKSHA');
            }
        } else if (formData.product_master_name === 'FG HEALTH SURAKSHA' && formData.family_type === 'floater') {
            // Count existing children
            const childCount = members.filter(member =>
                member.relation === 'SON' || member.relation === 'DAUGHTER'
            ).length;

            // For floater, allow maximum of 5 members (self + spouse + 3 children)
            if (members.length < 5 && childCount < 3) {
                setMembers([...members, {
                    ageBand: '',
                    relation: '',
                    occupation: '',
                    annual_income: '',
                    sumInsured: ''
                }]);
            } else {
                toast.warning('Maximum 5 members allowed (Self + Spouse + 3 Children) for FG HEALTH SURAKSHA floater policy');
            }
        } else {
            // Existing logic for other products
            if (members.length < 7) {
                setMembers([...members, {
                    ageBand: '',
                    relation: '',
                    occupation: '',
                    annual_income: '',
                    sumInsured: '',
                    sumInsured_AD: '',
                    sumInsured_PT: '',
                    sumInsured_PP: '',
                    sumInsured_TTD: ''
                }]);
            } else {
                toast.warning('Maximum 7 members allowed');
            }
        }
    };

    const handleProductTypeOptions = () => {
        const mainProduct = mainProducts.find(product => product.id === formData.main_product_id);
        const insuranceCompany = insuranceCompanies.find(company => company.id === formData.insurance_company_id);

        if (mainProduct && insuranceCompany) {
            if (mainProduct.main_product === 'HEALTH' && insuranceCompany.insurance_company_name === 'FUTURE GENERALI') {
                return masterProducts
                    .filter(product => product.status === 1)
                    .map(product => ({
                        value: product.id,
                        label: product.product_name
                    }));
            }
        }

        return [];
    };

    const handleRemoveMember = (index) => {
        if (formData.family_type === 'floater' && members.length <= 2) {
            toast.warning('Minimum 2 members required for floater policy');
            return;
        }
        if (formData.family_type === 'individual' && members.length <= 1) {
            toast.warning('At least one member is required for individual policy');
            return;
        }
        const newMembers = members.filter((_, i) => i !== index);
        setMembers(newMembers);
    };




    const handleMemberChange = (index, field, value) => {
        const newMembers = [...members];
        newMembers[index] = { ...newMembers[index], [field]: value };

        // Get occupation details
        const selectedOccupation = occupationOptions.find(opt => opt.value === newMembers[index].occupation);
        const occupationLabel = selectedOccupation?.label || '';
        const riskClass = selectedOccupation?.risk;

        // Handle Student/Unemployed SELF case
        if (field === 'occupation' || field === 'relation') {

            // Special case for Student/Unemployed SELF
            if (newMembers[index].relation === 'SELF' &&
                (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {


                // Set fixed values for Student/Unemployed SELF
                newMembers[index] = {
                    ...newMembers[index],
                    annual_income: '0',
                    sumInsured_AD: '1000000',  // 10 lakhs
                    sumInsured_PT: '1000000',  // 10 lakhs
                    sumInsured_PP: '1000000',  // 10 lakhs
                    sumInsured_TTD: '0'        // No TTD coverage
                };
            }
        }

        // Handle sum insured changes
        if (field.startsWith('sumInsured_')) {
            const coverType = field.split('_')[1];
            if (value && !/^\d*$/.test(value)) return;

            // Always update the value first to allow typing
            const newMembers = [...members];
            newMembers[index] = { ...newMembers[index], [field]: value };
            setMembers(newMembers);

            // Then perform validations if there's a value
            if (value) {
                // First check minimum sum insured for all cases
                if (Number(value) < 50000) {
                    setErrors(prev => ({
                        ...prev,
                        [field]: 'Minimum sum insured should be ₹50,000'
                    }));
                    return;
                }

                // Get occupation details
                const selectedOccupation = occupationOptions.find(opt => opt.value === members[index].occupation);
                const occupationLabel = selectedOccupation?.label || '';

                // Check if Student/Unemployed SELF case
                if (members[index].relation === 'SELF' &&
                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                    if (Number(value) > 1000000) {
                        setErrors(prev => ({
                            ...prev,
                            [field]: 'Maximum allowed is ₹10,00,000 for Student/Unemployed'
                        }));
                        return;
                    }

                    // Validate AD relationship for PT and PP even in Student/Unemployed case
                    if (coverType !== 'AD') {
                        const adValue = members[index].sumInsured_AD;
                        if (adValue && Number(value) > Number(adValue)) {
                            setErrors(prev => ({
                                ...prev,
                                [field]: `Sum insured cannot exceed AD sum insured (₹${Number(adValue).toLocaleString()})`
                            }));
                            return;
                        }
                    }
                } else {
                    // Regular case - calculate based on income
                    const monthlyIncome = Number(members[index].annual_income) / 12;
                    let maxAllowed;

                    // Calculate max allowed based on cover type
                    if (['AD', 'PT', 'PP'].includes(coverType)) {
                        maxAllowed = Math.min(monthlyIncome * 144, 3000000); // 144 months or 30 lakhs cap

                        // Validate AD relationship for PT and PP
                        if (coverType !== 'AD') {
                            const adValue = members[index].sumInsured_AD;
                            if (adValue && Number(value) > Number(adValue)) {
                                setErrors(prev => ({
                                    ...prev,
                                    [field]: `Sum insured cannot exceed AD sum insured (₹${Number(adValue).toLocaleString()})`
                                }));
                                return;
                            }
                        }

                        if (Number(value) > maxAllowed) {
                            setErrors(prev => ({
                                ...prev,
                                [field]: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                            }));
                            return;
                        }
                    }
                }
            }

            // Clear errors if value is empty or valid
            setErrors(prev => ({ ...prev, [field]: undefined }));
            return;
        }

        // Handle annual income changes
        if (field === 'annual_income') {
            // For Student/Unemployed SELF, force annual income to 0
            if (newMembers[index].relation === 'SELF' &&
                (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                newMembers[index].annual_income = '0';
                newMembers[index].sumInsured_TTD = '0';
                return;
            }

            if (value && !/^\d*$/.test(value)) return;

            // Recalculate sum insured limits when income changes
            const monthlyIncome = Number(value) / 12;

            // Update AD, PT, PP limits
            const regularLimit = Math.min(monthlyIncome * 144, 3000000);
            ['AD', 'PT', 'PP'].forEach(type => {
                const currentValue = Number(newMembers[index][`sumInsured_${type}`]);
                if (currentValue > regularLimit) {
                    newMembers[index][`sumInsured_${type}`] = String(regularLimit);
                }
            });

            // Update TTD limit
            const ttdBase = monthlyIncome * 24;
            const ttdLimit = riskClass === 1 ? Math.min(ttdBase, 5000000) :
                Math.min(ttdBase, 2500000);

            const currentTTD = Number(newMembers[index].sumInsured_TTD);
            if (currentTTD > ttdLimit) {
                newMembers[index].sumInsured_TTD = String(ttdLimit);
            }
        }
        // Ensure TTD is not below the minimum value
        if (field === 'sumInsured_TTD' && Number(value) < 10000) {
            setErrors(prev => ({
                ...prev,
                sumInsured_TTD: 'Minimum sum insured for TTD should be ₹10,000'
            }));
            return;
        }

        setMembers(newMembers);

        if (field === 'occupation' || field === 'relation') {
            newMembers[index] = { ...newMembers[index], [field]: value };

            // Calculate sum insured based on relation and occupation
            const primaryMember = newMembers[0]; // Assuming the first member is the primary insured
            const primarySumInsuredAD = Number(primaryMember.sumInsured_AD);
            const primarySumInsuredPP = Number(primaryMember.sumInsured_PT);
            const primarySumInsuredPT = Number(primaryMember.sumInsured_PP);
            const primarySumInsuredTTD = Number(primaryMember.sumInsured_TTD);

            // Calculate sum insured for the current member based on relation and occupation

            const occupationName = occupationOptions.find(option => option.value === newMembers[index].occupation)?.label || '';

            if (
                newMembers[index].relation === 'SPOUSE' &&
                (occupationName === 'Housewife' || occupationName === 'Retired' || occupationName === 'Unemployed')
            ) {
                // Handle SPOUSE with specific occupations
                newMembers[index] = {
                    ...newMembers[index],
                    annual_income: '0',  // Set as string '0' to match input field format
                    sumInsured_AD: primarySumInsuredAD ? String(Math.min(primarySumInsuredAD * 0.5, 1000000)) : '',
                    sumInsured_PT: primarySumInsuredPP ? String(Math.min(primarySumInsuredPP * 0.5, 1000000)) : '',
                    sumInsured_PP: primarySumInsuredPT ? String(Math.min(primarySumInsuredPT * 0.5, 1000000)) : '',
                    sumInsured_TTD: primarySumInsuredTTD ? String(Math.min(primarySumInsuredTTD * 0.5, 100000)) : ''
                };
                // Debugging: Log the updated values

            } else if (
                (newMembers[index].relation === 'SON' || newMembers[index].relation === 'DAUGHTER') &&
                occupationName === 'Student'

            ) {
                // Handle SON/DAUGHTER with student occupation
                newMembers[index] = {
                    ...newMembers[index],
                    annual_income: '0',  // Set as string '0' to match input field format
                    sumInsured_AD: primarySumInsuredAD ? String(Math.min(primarySumInsuredAD * 0.25, 500000)) : '',
                    sumInsured_PT: primarySumInsuredPP ? String(Math.min(primarySumInsuredPP * 0.25, 500000)) : '',
                    sumInsured_PP: primarySumInsuredPT ? String(Math.min(primarySumInsuredPT * 0.25, 500000)) : '',
                    // sumInsured_TTD: primarySumInsuredTTD ? String(Math.min(primarySumInsuredTTD * 0.25, 1000000)) : ''
                    sumInsured_TTD: "0"

                };
            }
            else {
                // Default to 0 for other relations and occupations
                newMembers[index].sumInsured_AD = 0;
                newMembers[index].sumInsured_PT = 0;
                newMembers[index].sumInsured_PP = 0;
                newMembers[index].sumInsured_TTD = 0;
            }
        } else {
            // Handle other field changes normally
            newMembers[index] = { ...newMembers[index], [field]: value };
        }

        /*  // Clear helper text for sum insured if occupation is housewife
   if (newMembers[index].occupation === 'housewife' && newMembers[index].relation === 'SPOUSE') {
       // Assuming you have a way to manage helper text, clear it here
       // For example:
       // setErrors(prevErrors => ({ ...prevErrors, sumInsured: '' }));
   } else {
       // Handle other cases for helper text if needed
   } */


        if (field === 'ageBand') {
            // When age band changes for FG ADVANTAGE TOP UP, reset both deductible and sum insured
            // For other products, only reset sum insured if needed
            if (formData.product_master_name === 'FG ADVANTAGE TOP UP') {
                newMembers[index] = {
                    ...newMembers[index],
                    [field]: value,
                    deductible: '', // Reset deductible only for FG ADVANTAGE TOP UP
                    sumInsured: ''  // Reset sum insured
                };
            } else {
                newMembers[index] = {
                    ...newMembers[index],
                    [field]: value,
                    sumInsured: ''  // Reset sum insured for all products
                };
            }
            setMembers(newMembers);

            // Validation for FG VARISHTHA BIMA floater policy
            if (formData.product_master_name === 'FG VARISHTHA BIMA' &&
                formData.family_type === 'floater') {
                // Check ages of all members
                const allMembersBelow60 = newMembers.every(member => {
                    if (!member.ageBand) return true; // Skip empty age bands
                    const ageStart = parseInt(member.ageBand.split('-')[0]);
                    return ageStart < 60;
                });

                // If all selected members are below 60, show warning
                if (allMembersBelow60 && newMembers.filter(m => m.ageBand).length >= 2) {
                    toast.warning('For FG VARISHTHA BIMA floater policy, at least one member must be above 60 years');
                    return; // Don't update the state
                }
            }
        } else if (field === 'deductible') {
            // When deductible changes, only reset sum insured for FG ADVANTAGE TOP UP
            if (formData.product_master_name === 'FG ADVANTAGE TOP UP') {
                newMembers[index] = {
                    ...newMembers[index],
                    [field]: value,
                    sumInsured: '' // Reset sum insured only for FG ADVANTAGE TOP UP
                };
            } else {
                newMembers[index] = {
                    ...newMembers[index],
                    [field]: value
                };
            }
            setMembers(newMembers);
        } /* else if (field === 'annual_income') {
            const value = e.target.value;
            if (value && !/^\d*$/.test(value)) return;
            handleMemberChange(index, 'annual_income', value);

            // Reset all sum insured fields if they exceed new maximum
            const coverTypes = ['AD', 'PT', 'PP', 'TTD'];
            coverTypes.forEach(type => {
                const maxAllowed = calculateMaxSumInsured(Number(value), type);
                if (Number(newMembers[index][`sumInsured_${type}`]) > maxAllowed) {
                    handleMemberChange(index, `sumInsured_${type}`, '');
                }
            });
        } else if (field === 'sumInsured_AD') {
            const maxAllowed = calculateMaxSumInsured(Number(newMembers[index].annual_income), 'AD', newMembers[index].occupation, newMembers[index].relation);
            // Allow sum insured entry even if annual income is 0 for Unemployed/Student
            if (Number(value) > maxAllowed && !(newMembers[index].relation === 'SELF' && (newMembers[index].occupation === 'Unemployed' || newMembers[index].occupation === 'Student'))) {
                setErrors(prev => ({
                    ...prev,
                    sumInsured_AD: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                }));
                return;
            } */

        else {
            // Handle other field changes normally
            newMembers[index] = { ...newMembers[index], [field]: value };
            setMembers(newMembers);
        }
    };

    // Helper function to check if a relation is already selected
    const isRelationSelected = (relation, currentIndex, members) => {
        return members.some((member, idx) =>
            idx !== currentIndex && member.relation === relation
        );
    };

    const getRelationOptions = (index, members) => {
        // Define all possible relations
        const allRelations = [
            { value: 'SELF', label: 'Self' },
            { value: 'SPOUSE', label: 'Spouse' },
            { value: 'SON', label: 'Son' },
            { value: 'DAUGHTER', label: 'Daughter' },
            { value: 'FATHER', label: 'Father' },
            { value: 'MOTHER', label: 'Mother' }
        ];

        const currentAgeBand = members[index].ageBand;
        const isAgeBelow25 = currentAgeBand && parseInt(currentAgeBand.split('-')[0]) < 25;

        // If age is below 25, only show SON and DAUGHTER options
        if (isAgeBelow25) {
            return allRelations.filter(relation =>
                ['SON', 'DAUGHTER'].includes(relation.value)
            );
        }

        // For ages 25 and above, exclude SON and DAUGHTER options
        const adultRelations = allRelations.filter(relation =>
            !['SON', 'DAUGHTER'].includes(relation.value)
        );

        // Special handling for FG HEALTH SURAKSHA
        if (formData.product_master_name === 'FG HEALTH SURAKSHA') {
            // Count existing relations
            const existingRelations = members.map(m => m.relation);
            const childCount = existingRelations.filter(r => r === 'SON' || r === 'DAUGHTER').length;
            const parentCount = existingRelations.filter(r => r === 'FATHER' || r === 'MOTHER').length;
            const hasSelf = existingRelations.includes('SELF');
            const hasSpouse = existingRelations.includes('SPOUSE');

            if (formData.family_type === 'floater') {
                // For floater: only allow self, spouse, and up to 3 kids
                return allRelations.filter(relation => {
                    if (isRelationSelected(relation.value, index, members)) return false;

                    if (relation.value === 'FATHER' || relation.value === 'MOTHER') return false;
                    if ((relation.value === 'SON' || relation.value === 'DAUGHTER') && childCount >= 3) return false;
                    return true;
                });
            } else {
                // For individual: S+Sp+4C+2P with conditions
                return allRelations.filter(relation => {
                    if (isRelationSelected(relation.value, index, members)) return false;

                    // If we have parents, limit children to 2
                    if (parentCount > 0 && (relation.value === 'SON' || relation.value === 'DAUGHTER') && childCount >= 2) return false;
                    // If no parents, allow up to 4 children
                    if (parentCount === 0 && (relation.value === 'SON' || relation.value === 'DAUGHTER') && childCount >= 4) return false;
                    // Limit parents to 2
                    if ((relation.value === 'FATHER' || relation.value === 'MOTHER') && parentCount >= 2) return false;

                    return true;
                });
            }
        }

        // For ages 25 and above, proceed with product-specific logic
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            return adultRelations.filter(relation => {
                // Check if relation is already selected by previous members
                const isRelationSelected = members.some((m, i) =>
                    i < index && m.relation === relation.value
                );

                if (isRelationSelected) return false;
                return ['SELF', 'SPOUSE'].includes(relation.value);
            });
        }

        // For FG VARISHTHA BIMA
        if (formData.product_master_name === 'FG VARISHTHA BIMA') {
            const varisthaRelations = adultRelations.filter(relation =>
                ['SELF', 'SPOUSE'].includes(relation.value)
            );

            return varisthaRelations.filter(relation => {
                const isRelationSelected = members.some((m, i) =>
                    i < index && m.relation === relation.value
                );
                return !isRelationSelected;
            });
        }

        // For floater policies, exclude FATHER and MOTHER options
        if (formData.family_type === 'floater') {
            return adultRelations.filter(relation =>
                !['FATHER', 'MOTHER'].includes(relation.value)
            );
        }

        // For other products
        const isAdvantageTopup = formData.product_master_name === 'FG ADVANTAGE TOP UP' && formData.family_type === 'floater';
        const isFloater = formData.family_type === 'floater';

        return adultRelations.filter(relation => {
            // Check if relation is already selected by previous members
            const isRelationSelected = members.some((m, i) =>
                i < index && m.relation === relation.value
            );

            // If relation is already selected, exclude it
            if (isRelationSelected) return false;

            if (isAdvantageTopup) {
                return !isFloater || !['FATHER', 'MOTHER'].includes(relation.value);
            }

            // For all other cases, show all available unselected relations
            return true;
        });
    };

    // Add these validation functions after other const declarations
    const validateSumInsured = (value, type, adValue) => {
        const minAmount = 50000; // 50,000 minimum for all types
        const numValue = Number(value);


        // If empty value, don't show error (allow user to type)
        if (!value) return '';

        // Check minimum sum insured for all types (AD, PT, PP)
        if (numValue < minAmount) {
            return `Minimum sum insured should be ₹${minAmount.toLocaleString()}`;
        }

        // For PT and PP, check against AD value only if AD value exists
        if ((type === 'PT' || type === 'PP') && adValue) {
            if (numValue > Number(adValue)) {
                return `Sum insured cannot exceed AD sum insured (₹${Number(adValue).toLocaleString()})`;
            }
        }

        return '';
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.insurance_company_id) newErrors.insurance_company_id = 'Company is required';
        if (!formData.cover_type) newErrors.cover_type = 'Cover type is required';
        if (!formData.pincode) newErrors.pincode = 'Pincode is required';

        // Validation for FG VARISHTHA BIMA floater policy
        if (formData.product_master_name === 'FG VARISHTHA BIMA' && formData.family_type === 'floater') {
            // Check if at least one member is above 60 years
            const hasElderlyMember = members.some(member => {
                const ageBand = member.ageBand;
                // Check age bands that start with 60 or higher
                return ageBand && parseInt(ageBand.split('-')[0]) >= 60;
            });

            if (!hasElderlyMember) {
                newErrors.members = 'For FG VARISHTHA BIMA floater policy, at least one member should be above 60 years';
                toast.error('For FG VARISHTHA BIMA floater policy, at least one member should be above 60 years');
            }
        }

        if (formData.family_type === 'floater') {
            if (members.length < 2) {
                newErrors.members = 'Minimum 2 members required for floater policy';
            }
        }

        const isMembersValid = members.every(member =>
            member.ageBand && member.relation
        );

        if (!isMembersValid) {
            newErrors.members = 'All member details are required';
        }

        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            const isMembersValid = members.every(member => {
                if (!member.occupation) {
                    newErrors.occupation = 'Occupation is required';
                    return false;
                }
                if (!member.annual_income) {
                    newErrors.annual_income = 'Annual Income is required';
                    return false;
                }
                const maxAllowed = calculateMaxSumInsured(Number(member.annual_income), 'AD');
                if (Number(member.sumInsured) > maxAllowed) {
                    newErrors.sumInsured = `Sum Insured cannot exceed ₹${maxAllowed.toLocaleString()}`;
                    return false;
                }

                return true;
            });

            if (!isMembersValid) {
                newErrors.members = 'All member details are required and must be valid';
            }
        }

        // Validate sum insured amounts for all members
        members.forEach((member, index) => {
            ['AD', 'PT', 'PP'].forEach(type => {
                const fieldName = `sumInsured_${type}`;
                const value = member[fieldName];

                if (value) {
                    const validationError = validateSumInsured(
                        value,
                        type,
                        type !== 'AD' ? member.sumInsured_AD : undefined
                    );

                    if (validationError) {
                        newErrors[`member${index}_${fieldName}`] = validationError;
                    }
                }
            });
        });

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleCreateQuote = async (e) => {
        e.preventDefault();
        setLoading(true);

        // Check for members in risk class 3
        const hasRiskClass3Members = members.some(member => {
            const occupation = occupationOptions.find(option => option.value === member.occupation);
            return occupation && occupation.risk === 3;
        });

        if (hasRiskClass3Members) {
            toast.error('You cannot create a quote with members in risk class 3. Please select a different occupation.');
            setLoading(false);
            return;
        }
        // Add validation for minimum sum insured
        if (formData.product_master_name === 'FG VARISHTHA BIMA' || formData.product_master_name === 'FG HEALTH SURAKSHA') {
            const hasInvalidSumInsured = members.some(member => {
                const sumInsured = parseInt(member.sumInsured, 10);
                return sumInsured < 500000; // 5 lakhs = 500000
            });

            if (hasInvalidSumInsured) {
                toast.error('Minimum sum insured should be 5 Lakhs');
                setLoading(false);
                return;
            }
        }

        if (!validateForm()) {
            // Display all validation errors as toast notifications
            Object.keys(errors).forEach(errorKey => {
                toast.error(errors[errorKey]);
            });
            setLoading(false);
            return;
        }

        const quotationData = {
            family_type: formData.family_type,
            product_master_name: formData.product_master_name,
            cover_type: formData.cover_type,
            pincode: formData.pincode,
            company_name: formData.company_name,
            duration: formData.duration,
            copay: formData.product_master_name === 'FG VARISHTHA BIMA' ? formData.copay : undefined,
            members: members.map(member => {
                // For FG ACCIDENT SURAKSHA
                if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
                    // Destructure to remove sumInsured while keeping other fields
                    const { sumInsured, ...otherFields } = member;
                    return {
                        ...otherFields,
                        sumInsured_TTD: member.sumInsured_TTD || "0", // Set default value if not provided
                        gender: 'M'
                    };
                }
                // For other products
                if (formData.family_type === 'floater') {
                    return {
                        ageBand: member.ageBand,
                        relation: member.relation,
                        deductible: formData.product_master_name === 'FG ADVANTAGE TOP UP' ? formData.deductible : 0,
                        gender: member.gender || 'M'
                    };
                } else {
                    return {
                        ageBand: member.ageBand,
                        sumInsured: member.sumInsured || 0,
                        deductible: formData.product_master_name === 'FG ADVANTAGE TOP UP' ? member.deductible : 0,
                        relation: member.relation,
                        gender: member.gender || 'M'
                    };
                }
            }),
            additional_covers: formData.product_master_name === 'FG ACCIDENT SURAKSHA' ?
                additionalCover.enabled ? additionalCover.covers : null : null
        };


        try {
            let response;
            // Check if family type is floater
            if (formData.family_type === 'floater') {
                response = await createFloaterOptions(quotationData);
                toast.success('Floater created successfully');
            } else {
                response = await createQuotation(quotationData);
                toast.success('Quote created successfully');
            }

            // Modified navigation logic based on login status
            if (isLoggedIn) {
                navigate('/dashboard/quick-quotation-overview', { state: { quotationData: response } });
            } else {
                navigate('/quick-quotation-overview', { state: { quotationData: response } });
            }

        } catch (error) {
            console.error("❌ Error creating quote:", error);
    
            // Extract a meaningful error message
            let errorMessage = error?.message || "Unknown error occurred.";
            if (typeof error === "object" && error.response?.data?.message) {
                errorMessage = error.response.data.message;
            }
    
            toast.error(errorMessage); // Show correct error message
        } finally {
            setLoading(false);
        }
    };

    const occupationOptions = riskClass.map((occupation) => ({
        value: occupation.id,
        label: occupation.label_name,
        risk: occupation.risk_class
    }));

    // Add this function to handle additional cover changes
    const handleAdditionalCoverChange = (coverType, value) => {
        setAdditionalCover(prev => ({
            ...prev,
            covers: {
                ...prev.covers,
                [coverType]: value
            }
        }));
    };

    // Add this function to check if child members exist
    const hasChildMembers = () => {
        return members.some(member =>
            member.relation === 'SON' || member.relation === 'DAUGHTER'
        );
    };

    return (
        <Box sx={{ padding: '20px 40px' }}>
            <BarLoader loading={loading} />
            {loading ? null : (
                <form onSubmit={handleCreateQuote}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={8} sx={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: '#528a7e' }}
                            />
                            <ModuleName moduleName="Quick Quotation" pageName="Create" />
                        </Grid>

                        <Grid item xs={12} md={4} sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            alignItems: 'center',
                            gap: 2
                        }}>
                            {!isLoggedIn && (
                                <Button
                                    variant="contained"
                                    onClick={() => navigate('/')}
                                    sx={{
                                        backgroundColor: '#528A7E',
                                        color: 'white',
                                        '&:hover': {
                                            backgroundColor: '#397c63',
                                        },
                                        textTransform: 'none',
                                        fontWeight: 'bold',
                                    }}
                                >
                                    Login
                                </Button>
                            )}
                            <Button
                                type="submit"
                                variant="contained"
                                sx={{
                                    backgroundColor: '#528a7e',
                                }}
                            >
                                Create Quote
                            </Button>
                            {/* Export to PDF button - only shown when user is logged in */}
                            {/*  {isLoggedIn && (
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        onClick={handleExportPDF}
                                        disabled={loading}
                                        startIcon={<PictureAsPdfIcon />}
                                    >
                                        Export PDF
                                    </Button>
                                )} */}
                        </Grid>

                        {isLoggedIn && (
                            <Grid container>
                                <CustomSection
                                    titles={['Overview', 'Create']}
                                    page='quick-quotations'
                                />
                            </Grid>
                        )}

                        <Grid item xs={12}>
                            <Box sx={{ mt: 3, mb: 2 }}>
                                <Typography variant="h6">Family Type</Typography>
                                <hr />
                            </Box>
                            <FormControl>
                                <RadioGroup
                                    row
                                    name="family_type"
                                    value={formData.family_type}
                                    onChange={handleRadioChange}
                                >
                                    <FormControlLabel value="individual" control={<Radio />} label="Individual" />
                                    <FormControlLabel value="floater" control={<Radio />} label="Floater"
                                        disabled={formData.product_master_name === 'FG ACCIDENT SURAKSHA'} />
                                </RadioGroup>
                            </FormControl>
                        </Grid>

                        <Grid item xs={12}>
                            <Box sx={{ mt: 3, mb: 2 }}>
                                <Typography variant="h6">Company Details</Typography>
                                <hr />
                            </Box>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Insurance Company"
                                        name="insurance_company_id"
                                        value={formData.insurance_company_id}
                                        options={insuranceCompanies
                                            .filter(company => company.status === 1)
                                            .map(company => ({
                                                value: company.id,
                                                label: company.insurance_company_name
                                            }))}
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Main Product"
                                        name="main_product_id"
                                        value={formData.main_product_id}
                                        options={mainProducts
                                            .filter(product => product.status === 1)
                                            .map(product => ({
                                                label: product.main_product,
                                                value: product.id
                                            }))}
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Product Type"
                                        name="product_master_id"
                                        value={formData.product_master_id}
                                        options={masterProducts
                                            .filter(product => product.status === 1)
                                            .map(product => ({
                                                label: product.product_name,
                                                value: product.id
                                            }))}
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                    />
                                </Grid>
                            </Grid>
                        </Grid>

                        <Grid item xs={12}>
                            <Box sx={{ mt: 3, mb: 2 }}>
                                <Typography variant="h6">Cover Type</Typography>
                                <hr />
                            </Box>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Cover Type"
                                        name="cover_type"
                                        value={formData.cover_type}
                                        options={coverTypeOptions}
                                        onChange={handleInputChange}
                                        error={!!errors.cover_type}
                                        helperText={errors.cover_type}
                                        required
                                        fullWidth
                                    />
                                </Grid>
                                {
                                    formData.family_type === 'floater' && formData.product_master_name === 'FG ADVANTAGE TOP UP' && (formData.cover_type === 'SUPREME' || formData.cover_type === 'ELITE') && (
                                        <Grid item xs={12} md={3}>
                                            <Dropdown
                                                label="Deductible"
                                                name="deductible"
                                                value={formData.deductible}
                                                options={getDeductableAmountOptions(
                                                    formData.deductible,
                                                    members.reduce((highest, member) => {
                                                        if (!member.ageBand) return highest;
                                                        const currentStart = parseInt(member.ageBand.split('-')[0]);
                                                        const highestStart = parseInt(highest.split('-')[0] || '0');
                                                        return currentStart > highestStart ? member.ageBand : highest;
                                                    }, '')
                                                )}
                                                onChange={handleInputChange}
                                                error={!!errors.cover_type}
                                                helperText={errors.cover_type}
                                                required
                                                fullWidth
                                            />
                                        </Grid>
                                    )
                                }
                                <Grid item xs={12} md={3}>
                                    <CustomTextField
                                        label="Pincode"
                                        name="pincode"
                                        value={formData.pincode}
                                        onChange={handleInputChange}
                                        error={errors.pincode}
                                        helperText={errors.pincode}
                                        isRequired
                                        fullWidth
                                        inputProps={{
                                            maxLength: 6,
                                            pattern: '[0-9]*'
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Duration"
                                        name="duration"
                                        value={formData.duration}
                                        options={durationOptions}
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                        error={!!errors.duration}
                                        helperText={errors.duration}
                                    />
                                </Grid>
                                {formData.product_master_name === 'FG VARISHTHA BIMA' && (
                                    <Grid item xs={12} md={3} >
                                        <Typography variant="subtitle1" >Copay</Typography>
                                        <FormControl>
                                            <RadioGroup
                                                row
                                                name="copay"
                                                value={formData.copay}
                                                onChange={handleInputChange}
                                            >
                                                <FormControlLabel
                                                    value="yes"
                                                    control={<Radio />}
                                                    label="Yes"
                                                // sx={{ mr: 1}}
                                                />
                                                <FormControlLabel
                                                    value="no"
                                                    control={<Radio />}
                                                    label="No"
                                                />
                                            </RadioGroup>
                                        </FormControl>
                                    </Grid>
                                )}
                            </Grid>
                        </Grid>

                        <Grid item xs={12}>
                            <Box sx={{ mt: 3, mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Typography variant="h6">Age Group Details</Typography>
                                {members.length < 7 && (
                                    <Button
                                        variant="contained"
                                        startIcon={<AddIcon />}
                                        onClick={handleAddMember}
                                        sx={{
                                            backgroundColor: '#528a7e',
                                            '&:hover': { backgroundColor: 'darkgreen' }
                                        }}
                                    >
                                        Add Member
                                    </Button>
                                )}
                            </Box>
                            <hr />
                        </Grid>

                        {/* Member Fields */}
                        {members.map((member, index) => (
                            <Grid
                                item
                                xs={12}
                                container
                                spacing={2}
                                key={index}
                            >
                                <Grid item xs={12} sm={11}>
                                    <Card style={{ padding: '16px', marginBottom: '16px', backgroundColor: '#f9f9f9' }}>
                                        <Grid container spacing={2}>
                                            <Grid item xs={12} sm={6}>
                                                <Dropdown
                                                    label="Age Band"
                                                    value={member.ageBand}
                                                    options={getAgeBandOptions(formData.product_master_name)}
                                                    onChange={(e) => handleMemberChange(index, 'ageBand', e.target.value)}
                                                    required
                                                    fullWidth
                                                    helperText={
                                                        formData.product_master_name === 'FG VARISHTHA BIMA' &&
                                                            formData.family_type === 'floater'
                                                            ? 'At least one member must be above 60 years'
                                                            : ''
                                                    }
                                                />
                                            </Grid>
                                            {formData.family_type === 'individual' &&
                                                formData.product_master_name === 'FG ADVANTAGE TOP UP' &&
                                                (formData.cover_type === 'SUPREME' || formData.cover_type === 'ELITE') && (
                                                    <>
                                                        <Grid item xs={12} sm={6}>
                                                            <Dropdown
                                                                label="Deductible"
                                                                name="deductible"
                                                                value={member.deductible}
                                                                options={member.ageBand ? getDeductableAmountOptions(member.deductible, member.ageBand) : []}
                                                                onChange={(e) => handleMemberChange(index, 'deductible', e.target.value)}
                                                                error={!!errors.cover_type}
                                                                helperText={errors.cover_type}
                                                                required
                                                                fullWidth
                                                                disabled={!member.ageBand}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={6}>
                                                            <Dropdown
                                                                label="Sum Insured"
                                                                value={member.sumInsured}
                                                                options={
                                                                    member.deductible && member.ageBand
                                                                        ? getSumInsuredOptionsByDeductableAmount(member.deductible, member.ageBand)
                                                                        : []
                                                                }
                                                                onChange={(e) => handleMemberChange(index, 'sumInsured', e.target.value)}
                                                                required
                                                                fullWidth
                                                                disabled={!member.deductible || !member.ageBand}
                                                            />
                                                        </Grid>
                                                    </>
                                                )}
                                            {/* For other products, show sum insured based on cover type only */}
                                            {(!formData.product_master_name || (formData.product_master_name !== 'FG ADVANTAGE TOP UP' && formData.product_master_name !== 'FG ACCIDENT SURAKSHA')) &&
                                                formData.family_type !== 'floater' && (
                                                    <Grid item xs={12} sm={6}>
                                                        <Dropdown
                                                            label="Sum Insured"
                                                            value={member.sumInsured}
                                                            options={formData.cover_type ? getSumInsuredOptions(formData.cover_type) : []}
                                                            onChange={(e) => handleMemberChange(index, 'sumInsured', e.target.value)}
                                                            required
                                                            fullWidth
                                                            disabled={!formData.cover_type}
                                                        />
                                                    </Grid>
                                                )}
                                            <Grid item xs={12} sm={6}>
                                                <Dropdown
                                                    label="Relation"
                                                    value={member.relation}
                                                    options={getRelationOptions(index, members)}
                                                    onChange={(e) => handleMemberChange(index, 'relation', e.target.value)}
                                                    required
                                                    fullWidth
                                                />
                                            </Grid>
                                            {/* Add occupation and annual income fields when FG ACCIDENT SURAKSHA is selected */}
                                            {formData.product_master_name === 'FG ACCIDENT SURAKSHA' && (
                                                <>
                                                    <Grid item xs={12} sm={4}>
                                                        <Autocomplete
                                                            options={occupationOptions}
                                                            getOptionLabel={(option) =>
                                                                typeof option === 'string' ? option : option.label || ''
                                                            }
                                                            value={occupationOptions.find(option => option.value === member.occupation) || null}
                                                            onChange={(event, newValue) => {
                                                                handleMemberChange(index, 'occupation', newValue?.value || '');
                                                            }}
                                                            renderInput={(params) => (
                                                                <CustomTextField
                                                                    {...params}
                                                                    label="Occupation"
                                                                    error={!!errors.occupation}
                                                                    helperText={errors.occupation}
                                                                    isRequired
                                                                    fullWidth
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <CustomTextField
                                                            label="Annual Income"
                                                            name="annual_income"
                                                            value={member.annual_income || ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value && !/^\d*$/.test(value)) return;
                                                                handleMemberChange(index, 'annual_income', value);

                                                                // Reset all sum insured fields if they exceed new maximum
                                                                const coverTypes = ['AD', 'PT', 'PP', 'TTD'];
                                                                coverTypes.forEach(type => {
                                                                    const maxAllowed = calculateMaxSumInsured(Number(value), type,
                                                                        member.occupation, member.relation, occupationOptions);
                                                                    if (Number(member[`sumInsured_${type}`]) > maxAllowed) {
                                                                        handleMemberChange(index, `sumInsured_${type}`, '');
                                                                    }
                                                                });
                                                            }}
                                                            fullWidth
                                                            InputProps={{
                                                                startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                            }}
                                                            error={!!errors.annual_income}
                                                            helperText={errors.annual_income}
                                                            isRequired
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <CustomTextField
                                                            label="Sum Insured - Accidental Death (AD)"
                                                            name="sumInsured_AD"
                                                            value={member.sumInsured_AD || ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value && !/^\d*$/.test(value)) return;

                                                                // Get occupation details
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';
                                                                // Check if Student/Unemployed SELF case
                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    // For Student/Unemployed SELF, max is 10 lakhs
                                                                    if (Number(value) > 1000000) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_AD: 'Maximum allowed is ₹10,00,000 for Student/Unemployed'
                                                                        }));
                                                                        return;
                                                                    }
                                                                } else {
                                                                    // Regular case - calculate based on income
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const maxAllowed = Math.min(monthlyIncome * 144, 3000000); // 144 months or 30 lakhs cap

                                                                    if (Number(value) > maxAllowed) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_AD: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                        }));
                                                                        return;
                                                                    }
                                                                }

                                                                setErrors(prev => ({
                                                                    ...prev,
                                                                    sumInsured_AD: ''
                                                                }));

                                                                const newMembers = [...members];
                                                                newMembers[index] = { ...newMembers[index], sumInsured_AD: value };
                                                                setMembers(newMembers);
                                                                handleMemberChange(index, 'sumInsured_AD', value);
                                                            }}
                                                            fullWidth
                                                            InputProps={{
                                                                startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                            }}
                                                            error={!!errors.sumInsured_AD}
                                                            helperText={errors.sumInsured_AD || (() => {
                                                                // Calculate max allowed for helper text
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';

                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    return 'Max: ₹10,00,000';
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const maxAllowed = Math.min(monthlyIncome * 144, 3000000);
                                                                    return `Max: ₹${maxAllowed.toLocaleString()}`;
                                                                }
                                                            })()}
                                                            isRequired
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <CustomTextField
                                                            label="Sum Insured - Permanent Total Disability (PT)"
                                                            name="sumInsured_PT"
                                                            value={member.sumInsured_PT || ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value && !/^\d*$/.test(value)) return;

                                                                // Get occupation details
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';

                                                                // Check if Student/Unemployed SELF case
                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    if (Number(value) > 1000000) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_PT: 'Maximum allowed is ₹10,00,000 for Student/Unemployed'
                                                                        }));
                                                                        return;
                                                                    }
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const maxAllowed = Math.min(monthlyIncome * 144, 3000000); // 144 months or 30 lakhs cap

                                                                    if (Number(value) > maxAllowed) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_PT: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                        }));
                                                                        return;
                                                                    }
                                                                }

                                                                setErrors(prev => ({ ...prev, sumInsured_PT: undefined }));
                                                                handleMemberChange(index, 'sumInsured_PT', value);
                                                            }}
                                                            fullWidth
                                                            InputProps={{
                                                                startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                            }}
                                                            error={!!errors.sumInsured_PT}
                                                            helperText={errors.sumInsured_PT || (() => {
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';

                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    return 'Max: ₹10,00,000';
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const maxAllowed = Math.min(monthlyIncome * 144, 3000000);
                                                                    return `Max: ₹${maxAllowed.toLocaleString()}`;
                                                                }
                                                            })()}
                                                            isRequired
                                                        />
                                                    </Grid>

                                                    <Grid item xs={12} sm={4}>
                                                        <CustomTextField
                                                            label="Sum Insured - Permanent Partial Disability (PP)"
                                                            name="sumInsured_PP"
                                                            value={member.sumInsured_PP || ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value && !/^\d*$/.test(value)) return;

                                                                // Get occupation details
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';

                                                                // Check if Student/Unemployed SELF case
                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    if (Number(value) > 1000000) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_PP: 'Maximum allowed is ₹10,00,000 for Student/Unemployed'
                                                                        }));
                                                                        return;
                                                                    }
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const maxAllowed = Math.min(monthlyIncome * 144, 3000000); // 144 months or 30 lakhs cap

                                                                    if (Number(value) > maxAllowed) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_PP: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                        }));
                                                                        return;
                                                                    }
                                                                }

                                                                setErrors(prev => ({ ...prev, sumInsured_PP: undefined }));
                                                                handleMemberChange(index, 'sumInsured_PP', value);
                                                            }}
                                                            fullWidth
                                                            InputProps={{
                                                                startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                            }}
                                                            error={!!errors.sumInsured_PP}
                                                            helperText={errors.sumInsured_PP || (() => {
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';

                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    return 'Max: ₹10,00,000';
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const maxAllowed = Math.min(monthlyIncome * 144, 3000000);
                                                                    return `Max: ₹${maxAllowed.toLocaleString()}`;
                                                                }
                                                            })()}
                                                            isRequired
                                                        />
                                                    </Grid>

                                                    <Grid item xs={12} sm={4}>
                                                        <CustomTextField
                                                            label="Sum Insured - Temporary Total Disability (TTD)"
                                                            name="sumInsured_TTD"
                                                            value={member.sumInsured_TTD || ''}
                                                            onChange={(e) => {
                                                                const value = e.target.value;
                                                                if (value && !/^\d*$/.test(value)) return;

                                                                // Get occupation details
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';
                                                                const riskClass = selectedOccupation?.risk;

                                                                // Check if Student/Unemployed SELF case
                                                                if (member.relation === 'SELF' &&
                                                                    (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    setErrors(prev => ({
                                                                        ...prev,
                                                                        sumInsured_TTD: 'TTD not available for Student/Unemployed'
                                                                    }));
                                                                    return 0;
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const baseAmount = monthlyIncome * 24; // 24 months
                                                                    const maxAllowed = riskClass === 1
                                                                        ? Math.min(baseAmount, 5000000)  // 50 lakhs for class 1
                                                                        : Math.min(baseAmount, 2500000); // 25 lakhs for others

                                                                    if (Number(value) > maxAllowed) {
                                                                        setErrors(prev => ({
                                                                            ...prev,
                                                                            sumInsured_TTD: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                        }));
                                                                        return;
                                                                    }
                                                                }

                                                                setErrors(prev => ({ ...prev, sumInsured_TTD: undefined }));
                                                                handleMemberChange(index, 'sumInsured_TTD', value);
                                                            }}
                                                            fullWidth
                                                            InputProps={{
                                                                startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                            }}
                                                            error={!!errors.sumInsured_TTD}
                                                            helperText={errors.sumInsured_TTD || (() => {
                                                                const selectedOccupation = occupationOptions.find(opt => opt.value === member.occupation);
                                                                const occupationLabel = selectedOccupation?.label || '';
                                                                const riskClass = selectedOccupation?.risk;
                                                            
                                                                // Minimum sum insured message
                                                                let minMessage = 'Minimum sum insured for TTD should be ₹10,000';
                                                            
                                                                if (member.relation === 'SELF' && (occupationLabel === 'Student' || occupationLabel === 'Unemployed')) {
                                                                    return 'TTD not available';
                                                                } else {
                                                                    const monthlyIncome = Number(member.annual_income) / 12;
                                                                    const baseAmount = monthlyIncome * 24;
                                                                    const maxAllowed = riskClass === 1
                                                                        ? Math.min(baseAmount, 5000000)
                                                                        : Math.min(baseAmount, 2500000);
                                                                    return `${minMessage} | Max: ₹${maxAllowed.toLocaleString()}`;
                                                                }
                                                            })()}
                                                        />
                                                    </Grid>
                                                </>
                                            )}
                                        </Grid>
                                    </Card>
                                </Grid>

                                <Grid item xs={12} sm={1} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                    {(formData.family_type === 'individual' && index > 0) ||
                                        (formData.family_type === 'floater' && index > 1) ? (
                                        <IconButton
                                            onClick={() => handleRemoveMember(index)}
                                            sx={{ color: 'red' }}
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    ) : null}
                                </Grid>
                            </Grid>
                        ))}



                        {/* Error message for minimum members */}
                        {errors.members && (
                            <Grid item xs={12}>
                                <Typography color="error" sx={{ mt: 1 }}>
                                    {errors.members}
                                </Typography>
                            </Grid>
                        )}

                        {eligibilityMessage && (
                            <Typography color="error" sx={{ mt: 2 }}>
                                {eligibilityMessage}
                            </Typography>
                        )}

                        {formData.product_master_name === 'FG ACCIDENT SURAKSHA' && (
                            <Grid item xs={12}>
                                <Box sx={{ mt: 3, mb: 2 }}>
                                    <Typography variant="h6">Additional Cover</Typography>
                                    <hr />
                                </Box>
                                <Grid container spacing={2}>
                                    <Grid item xs={12}>
                                        <FormControl>
                                            <RadioGroup
                                                row
                                                value={additionalCover.enabled}
                                                onChange={(e) => setAdditionalCover(prev => ({
                                                    ...prev,
                                                    enabled: e.target.value === 'true'
                                                }))}
                                            >
                                                <FormControlLabel value={true} control={<Radio />} label="Yes" />
                                                {/*  <FormControlLabel value={false} control={<Radio />} label="No" /> */}
                                            </RadioGroup>
                                        </FormControl>
                                    </Grid>

                                    {additionalCover.enabled && (
                                        <Grid item xs={12}>
                                            <Card sx={{ p: 2 }}>
                                                <Grid container spacing={2}>
                                                    {/* Repatriation and Funeral Expenses - Always Yes */}
                                                    <Grid item xs={12} md={6}>
                                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                            <Typography>Repatriation and Funeral Expenses</Typography>
                                                            <Typography variant="subtitle2" color="primary">
                                                                Always Included
                                                            </Typography>
                                                        </Box>
                                                    </Grid>

                                                    {/* Other cover options */}
                                                    {Object.entries(additionalCover.covers)
                                                        .filter(([key]) => key !== 'repatriation_funeral_expenses')
                                                        .map(([key, value]) => {
                                                            // Skip Child Education Support if no child members
                                                            if (key === 'child_education_support' && !hasChildMembers()) {
                                                                return null;
                                                            }

                                                            return (
                                                                <Grid item xs={12} md={6} key={key}>
                                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                        <Typography>
                                                                            {key.split('_').map(word =>
                                                                                word.charAt(0).toUpperCase() + word.slice(1)
                                                                            ).join(' ')}
                                                                        </Typography>
                                                                        <FormControl>
                                                                            <RadioGroup
                                                                                row
                                                                                value={value}
                                                                                onChange={(e) => handleAdditionalCoverChange(key, e.target.value)}
                                                                            >
                                                                                <FormControlLabel
                                                                                    value="yes"
                                                                                    control={<Radio />}
                                                                                    label="Yes"
                                                                                />
                                                                                <FormControlLabel
                                                                                    value="no"
                                                                                    control={<Radio />}
                                                                                    label="No"
                                                                                />
                                                                            </RadioGroup>
                                                                        </FormControl>
                                                                    </Box>
                                                                </Grid>
                                                            );
                                                        })}
                                                </Grid>
                                            </Card>
                                        </Grid>
                                    )}
                                </Grid>

                            </Grid>

                        )}
                    </Grid>
                </form>
            )}
        </Box>

    );
};

export default QuickQuotationPage;
