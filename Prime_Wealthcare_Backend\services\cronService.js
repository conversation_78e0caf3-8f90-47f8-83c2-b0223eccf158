const cron = require('node-cron');
const knexConfig = require('../knexfile.js');
const db = require('knex')(knexConfig.development);
const { sendSOAPRequest } = require('../Admin/Proposal/SoapService/PolicyIssuance/GetPolicyPdf');
const { sendEmail } = require('../services/emailService');

// Function to fetch and update policy PDFs for policies created today
async function fetchTodayPolicyPDFs() {
    try {
        console.log('Running scheduled policy PDF fetch job at:', new Date().toISOString());

        // Get today's date in YYYY-MM-DD format
        const today = new Date().toISOString().split('T')[0];

        // Query for policies created today that don't have PDFs yet
        const healthPolicies = await db('proposals')
            .where(function () {
                this.where('created_at', 'like', `${today}%`)
                    .orWhere('updated_at', 'like', `${today}%`);
            })
            .where(function () {
                this.where('policy_number', '!=', '')
                    .orWhere('policy_number', '!=', null);
            })
            .where(function () {
                this.where('policy_pdf', '')
                    .orWhere('policy_pdf', null);
            })

        const paPolicies = await db('proposals_pa')
            .where(function () {
                this.where('created_at', 'like', `${today}%`)
                    .orWhere('updated_at', 'like', `${today}%`);
            })
            .where(function () {
                this.where('policy_number', '!=', '')
                    .orWhere('policy_number', '!=', null);
            })
            .where(function () {
                this.where('policy_pdf', '')
                    .orWhere('policy_pdf', null);
            })

        console.log(`Found ${healthPolicies.length} health policies and ${paPolicies.length} PA policies to process`);

        // Process health policies
        for (const policy of healthPolicies) {
            await processPolicyPDF(policy.policy_number, 'proposals');
        }

        // Process PA policies
        for (const policy of paPolicies) {
            await processPolicyPDF(policy.policy_number, 'proposals_pa');
        }
    } catch (error) {
        console.error('Error in scheduled policy PDF fetch job:', error);
    }
}

// Helper function to process a single policy
async function processPolicyPDF(policyId, tableName) {
    try {
        if (policyId) {
            // Call the SOAP service to get the PDF URL (reusing your existing logic)
            const pdfResult = await sendSOAPRequest(policyId);
            // Update database if PDF URL is available
            if (pdfResult && pdfResult.pdfUrl && pdfResult.pdfUrl !== 'Kindly contact FG for policy document.') {
                await db(tableName)
                    .where('policy_number', policyId)
                    .update({
                        policy_pdf: pdfResult.pdfUrl
                    });

                // Single join query to get all required data
                const combinedData = await db(tableName)
                    .where(`${tableName}.policy_number`, policyId)
                    .join('customer_personal_info', `${tableName}.customer_id`, 'customer_personal_info.id')
                    .join('agents', `${tableName}.agent_code`, 'agents.id')
                    .join('product_master', `${tableName}.product_name`, 'product_master.id')
                    .select(
                        `${tableName}.policy_number`,
                        `${tableName}.policy_pdf`,
                        'customer_personal_info.email as customer_email',
                        'customer_personal_info.first_name as customer_first_name',
                        'agents.official_email as agent_email',
                        'product_master.product_name'
                    )
                    .first();

                const emailSubject = `Your ${combinedData.product_name} Policy Document`;
                const emailText = `Dear ${combinedData.customer_first_name},\n\nThank you for choosing Prime Wealthcare. Your policy document is now available.\n\nYou can access your policy document using the following link:\n${pdfResult.pdfUrl}\n\nPolicy Number: ${combinedData.policy_number}`;
                const emailHtml = `
                    <div style="font-family: Arial, sans-serif;">
                        <h2>Your Policy Document is Ready</h2>
                        <p>Dear ${combinedData.customer_first_name},</p>
                        <p>Thank you for choosing Prime Wealthcare. Your policy document for <strong>${combinedData.product_name}</strong> is now available.</p>
                        <p>Policy Details:</p>
                        <ul>
                            <li>Policy Number: ${combinedData.policy_number}</li>
                            <li>Product: ${combinedData.product_name}</li>
                        </ul>
                        <p>You can access your policy document by clicking the button below:</p>
                        <p style="margin: 20px 0;">
                            <a href="${pdfResult.pdfUrl}" 
                               style="background-color: #528a7e; 
                                      color: white; 
                                      padding: 10px 20px; 
                                      text-decoration: none; 
                                      border-radius: 5px;">
                                View Policy Document
                            </a>
                        </p>
                        <p>If you are unable to see the policy, Kindly contact your Relationship Manager or Prime Wealth Care Branch!</p>
                    </div>
                `;

                // Send email to customer
                await sendEmail(
                    combinedData.customer_email,
                    emailSubject,
                    emailText,
                    emailHtml
                );

                // Send email to agent
                const agentEmailSubject = `Policy Document for ${combinedData.customer_first_name} - ${combinedData.policy_number}`;
                const agentEmailText = `Dear Agent,\n\nA policy document for your client ${combinedData.customer_first_name} is now available.\n\nPolicy Number: ${combinedData.policy_number}\nProduct: ${combinedData.product_name}\n\nYou can access the policy document using the following link:\n${pdfResult.pdfUrl}`;
                const agentEmailHtml = `
                    <div style="font-family: Arial, sans-serif;">
                        <h2>Client Policy Document Available</h2>
                        <p>Dear Agent,</p>
                        <p>A policy document for your client <strong>${combinedData.customer_first_name}</strong> is now available.</p>
                        <p>Policy Details:</p>
                        <ul>
                            <li>Policy Number: ${combinedData.policy_number}</li>
                            <li>Product: ${combinedData.product_name}</li>
                            <li>Client Name: ${combinedData.customer_first_name}</li>
                        </ul>
                        <p>You can access the policy document by clicking the button below:</p>
                        <p style="margin: 20px 0;">
                            <a href="${pdfResult.pdfUrl}" 
                               style="background-color: #528a7e; 
                                      color: white; 
                                      padding: 10px 20px; 
                                      text-decoration: none; 
                                      border-radius: 5px;">
                                View Policy Document
                            </a>
                        </p>
                        <p>If you are unable to see the policy, Kindly contact Prime Wealth Care Branch!</p>
                    </div>
                `;

                if (combinedData.agent_email) {
                    await sendEmail(
                        combinedData.agent_email,
                        agentEmailSubject,
                        agentEmailText,
                        agentEmailHtml
                    );
                }
            } else {
                console.log(`PDF data not available for policy ${policyId}`);
            }
        } else {
            console.log('Policy Number not found');
        }
    } catch (error) {
        console.error(`Error processing policy ${policyId}:`, error);
    }
}

// Initialize cron jobs
function initCronJobs() {
    cron.schedule('*/10 * * * *', async () => {
        const currentHour = new Date().getHours();
        if (currentHour >= 10 && currentHour < 22) {
            await fetchTodayPolicyPDFs();
        }
    });
}

module.exports = { initCronJobs }; 