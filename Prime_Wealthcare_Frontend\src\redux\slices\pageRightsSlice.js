import { createSlice } from '@reduxjs/toolkit';
import { fetchAllUsers, fetchUserAccessRights, createPageRights } from '../actions/action';

const initialState = {
    users: [],
    selectedUserRights: null,
    loading: false,
    error: null,
    success: false
};

const pageRightsSlice = createSlice({
    name: 'pageRights',
    initialState,
    reducers: {
        clearPageRightsError: (state) => {
            state.error = null;
        },
        clearPageRightsSuccess: (state) => {
            state.success = false;
        }
    },
    extraReducers: (builder) => {
        // Fetch All Users
        builder
            .addCase(fetchAllUsers.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchAllUsers.fulfilled, (state, action) => {
                state.loading = false;
                state.users = action.payload.data;
                state.error = null;
            })
            .addCase(fetchAllUsers.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message;
            })

        // Fetch User Access Rights
        builder
            .addCase(fetchUserAccessRights.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchUserAccessRights.fulfilled, (state, action) => {
                state.loading = false;
                state.selectedUserRights = action.payload.data;
                state.error = null;
            })
            .addCase(fetchUserAccessRights.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message;
            })

        // Create Page Rights
        builder
            .addCase(createPageRights.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.success = false;
            })
            .addCase(createPageRights.fulfilled, (state) => {
                state.loading = false;
                state.success = true;
                state.error = null;
            })
            .addCase(createPageRights.rejected, (state, action) => {
                state.loading = false;
                state.success = false;
                state.error = action.payload?.message;
            });
    }
});

export const { clearPageRightsError, clearPageRightsSuccess } = pageRightsSlice.actions;
export default pageRightsSlice.reducer; 