const express = require('express');
const CommissionRateController = require('../Controllers/commissionRateController');

const router = express.Router();

// Define routes
router.get('/', CommissionRateController.getAll); // Get all commission rates
router.get('/:id', CommissionRateController.getById); // Get commission rate by ID
router.post('/', CommissionRateController.create); // Create a new commission rate

// Place more specific routes before more general ones
router.get('/insurance-company/:insuranceCompanyId', CommissionRateController.getCommissionRatesByCompany); // Get commission rates by insurance company ID

// Update multiple commission rates
router.put('/update-multiple', CommissionRateController.updateMultiple); // Update multiple commission rates at once

// Update commission rate by ID
router.put('/:id', CommissionRateController.updateCommissionRate); // Update commission rate by ID

// Reinstate and delete routes
router.delete('/:id', CommissionRateController.delete); // Delete commission rate by ID
router.post('/reinstate/:id', CommissionRateController.reinstate); // Reinstate a deleted commission rate

// Route to get Commission Rates by specific criteria (new, deactivated, edited)
router.get('/criteria/:criteria', CommissionRateController.getCommissionRatesByCriteria);

// Route to get Commission Rates by name
router.get('/name/:name', CommissionRateController.getCommissionRatesByName);

module.exports = router;





































/* const express = require('express');
const CommissionRateController = require('../Controllers/commissionRateController');

const router = express.Router();

// Define routes
router.get('/', CommissionRateController.getAll); // Get all commission rates
router.get('/:id', CommissionRateController.getById); // Get commission rate by ID
router.post('/', CommissionRateController.create); // Create a new commission rate
//router.put('/:id', CommissionRateController.update); // Update commission rate by ID
router.delete('/:id', CommissionRateController.delete); // Delete commission rate by ID
router.post('/reinstate/:id', CommissionRateController.reinstate); // Reinstate a commission rate
router.get('/insurance-company/:insuranceCompanyId', CommissionRateController.getCommissionRatesByCompany);

// Update multiple commission rates
router.put('/update-multiple', CommissionRateController.updateMultiple);

router.put('/:id', CommissionRateController.updateCommissionRate);





module.exports = router;
 */