import React from 'react';
import { TextField, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

const SearchBar = ({ placeholder, onSearch }) => {
  const handleKeyDown = (event) => {
    if (event.target.value === '') {
      onSearch('');
    } else {
      if (event.target.value.length >= 3) {
        onSearch(event.target.value); 
      }
    }
  };
  return (
    <TextField
      variant="outlined"
      placeholder={placeholder}
      onChange={handleKeyDown} 
      size="small"
      fullWidth
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        ),
      }}
    />
  );
};

export default SearchBar;
