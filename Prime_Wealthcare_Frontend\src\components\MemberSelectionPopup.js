import React, { useEffect, useState, useMemo, useCallback } from 'react';
import {
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Radio,
    Typography
} from '@mui/material';
import dayjs from 'dayjs';

function MemberSelectionPopup({
    open,
    onClose,
    existingMembers = [],
    potentialMembers,
    onSave,
    relationOptions = []
}) {
    const [selectedMembers, setSelectedMembers] = useState([]);

    // Memoize a mapping from relation id to label for fast lookups.
    const relationMap = useMemo(() => {
        return relationOptions.reduce((acc, rel) => {
            acc[Number(rel.id)] = rel.label_name;
            return acc;
        }, {});
    }, [relationOptions]);

    // Helper to generate a unique key for a member using optional chaining.
    const generateMemberKey = (member) => {
        const fullName =
            member?.full_name ||
            `${member?.first_name ?? ''} ${member?.last_name ?? ''}`.trim();
        const relationLabel = member?.relation || relationMap[member?.relation_id] || '';
        return `${fullName}-${relationLabel}`;
    };

    useEffect(() => {
        if (!potentialMembers && membersData.length === 0) {
            setSelectedMembers([]);
            return;
        }

        const { customer = {}, membersData = [] } = potentialMembers;

        const customerKey = `${customer?.first_name ?? ''}-${customer?.last_name ?? ''}`;
        const isCustomerExisting = existingMembers.some(
            m => `${m?.first_name ?? ''}-${m?.last_name ?? ''}` === customerKey
        );

        const existingKeySet = new Set(
            existingMembers.map(m => generateMemberKey(m))
        );

        // Add age check for children
        const filteredMembers = membersData
            .filter((member) => {
                const key = generateMemberKey(member);
                const relation = relationMap[member?.relation_id]?.toLowerCase();

                // Check age for children (son/daughter)
                if (relation && ['son', 'daughter'].includes(relation.toLowerCase())) {
                    const dob = dayjs(member.date_of_birth);
                    if (dob.isValid()) {
                        const age = dayjs().diff(dob, 'year');
                        if (age >= 25) {
                            return false;
                        }
                    }
                }
                return !existingKeySet.has(key);
            }).map((member, index) => ({
                ...member,
                isExisting: false,
                id: `member-${member?.id || index}`
            }));

        const customerEntry = !isCustomerExisting
            ? [{ ...customer, isExisting: false, id: `customer-${customer?.id || 'default'}` }]
            : [];

        setSelectedMembers([...customerEntry, ...filteredMembers]);
    }, [potentialMembers, existingMembers, relationMap]);

    // Toggle selection based on unique id.
    const handleToggle = useCallback((memberId) => {
        setSelectedMembers(prev =>
            prev.map(member =>
                member.id === memberId ? { ...member, isExisting: !member.isExisting } : member
            )
        );
    }, []);

    // Save only the selected members.
    const handleSave = useCallback(() => {
        const membersToSave = selectedMembers
            .filter(member => member.isExisting)
            .map(({ isExisting, ...rest }) => rest);
        onSave(membersToSave);
        onClose();
    }, [selectedMembers, onSave, onClose]);

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>Select Members</DialogTitle>
            <DialogContent>
                <List sx={{ width: '100%' }}>
                    {selectedMembers.map(member => {
                        const fullName =
                            member?.full_name ||
                            `${member?.first_name ?? ''} ${member?.last_name ?? ''}`.trim();
                        const dob = member?.date_of_birth ? dayjs(member.date_of_birth).format('DD/MM/YYYY') : 'N/A';
                        const relation = member?.relation || relationMap[member?.relation_id] || 'Self';
                        return (
                            <ListItem key={member.id} divider>
                                <ListItemIcon>
                                    <Radio
                                        checked={member?.isExisting}
                                        onChange={() => handleToggle(member.id)}
                                        sx={{ color: '#528A7E' }}
                                    />
                                </ListItemIcon>
                                <ListItemText
                                    primary={fullName}
                                    secondary={
                                        <Typography variant="body2" color="text.secondary">
                                            DOB: {dob} | Relation: {relation}
                                        </Typography>
                                    }
                                />
                            </ListItem>
                        );
                    })}
                </List>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose} color="error">
                    Cancel
                </Button>
                <Button
                    onClick={handleSave}
                    color="primary"
                    variant="contained"
                    sx={{ backgroundColor: '#528A7E' }}
                >
                    Save
                </Button>
            </DialogActions>
        </Dialog>
    );
}

export default MemberSelectionPopup;
