import { createSlice } from '@reduxjs/toolkit';
import { fetchAllBanks } from '../../actions/action';

const bankSlice = createSlice({
    name: 'banks',
    initialState: {
        banks: [],
        loading: false,
        error: null,
    },
    reducers: {
        // You can add additional reducers here if needed
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAllBanks.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchAllBanks.fulfilled, (state, action) => {
                state.loading = false;
                state.banks = action.payload; // Store the fetched banks
            })
            .addCase(fetchAllBanks.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload; // Store the error message
            });
    },
});

export const { } = bankSlice.actions; // Export any additional actions if needed
export default bankSlice.reducer;