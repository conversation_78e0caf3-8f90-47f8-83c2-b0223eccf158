// Top20Agents.jsx

import React, { useState, memo } from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Card,
    CardContent,
    CardMedia,
    Typography,
    IconButton,
    Paper,
    ToggleButton,
    ToggleButtonGroup
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Carousel from 'react-multi-carousel';
import 'react-multi-carousel/lib/styles.css';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { currentFinancialYear, formatIndianValue } from '../../utils/Reusable';
import { keyframes } from '@mui/system';
import AvatarImage from '../AvatarImage';

// Add animations after imports
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

const gradientBackground = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

// 1. Card, height driven by prop "cardHeight"
const StyledCard = styled(Card, {
    shouldForwardProp: (prop) => prop !== 'cardHeight',
})(({ theme, cardHeight }) => ({
    position: 'relative',
    width: 280,
    height: cardHeight,
    margin: theme.spacing(2, 1),
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    transition: 'transform 0.3s ease-in-out',
    animation: `${fadeIn} 0.5s ease-out`,
    background: 'linear-gradient(45deg, #ffffff 0%, #f8f9fa 100%)',
    borderRadius: '16px',
    overflow: 'visible',
    '&:hover': {
        transform: 'translateY(-8px) scale(1.02)',
        boxShadow: '0 12px 24px rgba(0,0,0,0.2)',
        '&:after': {
            opacity: 1,
        },
    },
    '&:after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: '16px',
        background: 'linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%)',
        opacity: 0,
        transition: 'opacity 0.3s ease',
        zIndex: -1,
    },
}));

// 2. Ranking badge (static size)
const RankingBadge = styled(Box)(({ theme, rank }) => ({
    position: 'absolute',
    top: 8,
    left: 8,
    width: 32,
    height: 32,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#000',
    fontSize: '0.875rem',
    fontWeight: 'bold',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    backgroundColor:
        rank === 1
            ? '#FFD700'
            : rank === 2
                ? '#C0C0C0'
                : rank === 3
                    ? '#CD7F32'
                    : '#FFF',
    zIndex: 1,
    animation: `${pulse} 1s ease`,
    transition: 'all 0.3s ease',
    '&:hover': {
        transform: rank <= 3 ? 'scale(1.2)' : 'scale(1.1)',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
    },
}));

// 3. Avatar fallback (circular), size driven by avatarSize prop
const InitialAvatar = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'avatarSize',
})(({ theme, avatarSize }) => ({
    width: avatarSize,
    height: avatarSize,
    borderRadius: '50%',
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
    fontSize: avatarSize * 0.5,
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
    margin: 'auto',
}));

// 4. Carousel container
const CarouselWrapper = styled(Box)({
    position: 'relative',
    padding: '0 40px',
    width: '100%',
    flexGrow: 1,
    display: 'flex',
    alignItems: 'center',
});

// 5. Responsive carousel
const StyledCarousel = styled(Carousel)({
    width: '100%',
});

// Add new shine effect
const ShineEffect = styled('div')({
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '50%',
    height: '100%',
    background: 'linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)',
    animation: `${gradientBackground} 3s ease infinite`,
});

// Add animated toggle button
const AnimatedToggleButton = styled(ToggleButton)(({ theme }) => ({
    transition: 'all 0.3s ease',
    '&.Mui-selected': {
        background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
        color: '#fff',
        boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
        transform: 'translateY(-2px)',
    },
    '&:hover': {
        transform: 'translateY(-2px)',
    },
}));

// --- Custom Arrow ---
const CustomArrow = ({ onClick, direction, label }) => (
    <IconButton
        onClick={onClick}
        aria-label={label}
        sx={{
            position: 'absolute',
            [direction === 'right' ? 'right' : 'left']: 0,
            top: '50%',
            transform: 'translateY(-50%)',
            bgcolor: 'background.paper',
            '&:hover': { bgcolor: 'primary.light' },
            zIndex: 2,
        }}
    >
        {direction === 'right' ? <ArrowForwardIosIcon /> : <ArrowBackIosIcon />}
    </IconButton>
);

CustomArrow.propTypes = {
    onClick: PropTypes.func.isRequired,
    direction: PropTypes.oneOf(['left', 'right']).isRequired,
    label: PropTypes.string,
};
CustomArrow.defaultProps = {
    label: 'carousel arrow',
};

// --- Agent Card ---
const AgentCard = memo(({ agent, premium, count, rank, cardHeight }) => {
    // Compute derived sizes
    const imageContainerHeight = Math.floor(cardHeight * 0.4);
    const avatarSize = Math.floor(cardHeight * 0.34);

    const getInitial = (name) => name ? name.charAt(0).toUpperCase() : '?';

    // Helper to extract name and code
    const formatDisplayName = (fullName) => {
        const match = fullName?.match(/^([^(]+)\s*\(([^)]+)\)/);
        if (!match) return { name: fullName || '', code: null };
        return { name: match[1].trim(), code: match[2].trim() };
    };

    const MAX_NAME_LENGTH = 20;
    const { name: displayName, code } = formatDisplayName(agent.name);
    const truncatedName = displayName.length > MAX_NAME_LENGTH
        ? `${displayName.substring(0, MAX_NAME_LENGTH - 3)}...`
        : displayName;

    return (
        <StyledCard cardHeight={cardHeight}>
            <RankingBadge rank={rank}>#{rank}</RankingBadge>

            <Box
                sx={{
                    height: imageContainerHeight,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    mt: 1,
                }}
            >
                {agent.profile_picture ? (
                    <Box
                        sx={{
                            width: avatarSize,
                            height: avatarSize,
                            borderRadius: '50%',
                            overflow: 'hidden',
                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                        }}
                    >
                        <AvatarImage
                            src={agent.profile_picture}
                            alt={truncatedName || 'Agent'}
                            size={avatarSize}
                            sx={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                            }}
                        />
                    </Box>
                ) : (
                    <InitialAvatar avatarSize={avatarSize}>
                        {getInitial(truncatedName)}
                    </InitialAvatar>
                )}
            </Box>

            <CardContent
                sx={{
                    flexGrow: 1,
                    p: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: 1,
                }}
            >
                <Typography
                    variant="h6"
                    sx={{
                        width: '100%',
                        textAlign: 'center',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    }}
                >
                    {truncatedName}
                </Typography>

                {code && (
                    <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{
                            width: '100%',
                            textAlign: 'center',
                            mt: -1,
                            lineHeight: 1.2
                        }}
                    >
                        ({code})
                    </Typography>
                )}

                <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                        width: '100%',
                        textAlign: 'center',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                    }}
                >
                    {agent.branch}
                </Typography>
                <Typography
                    variant="h6"
                    color="primary"
                    sx={{ textAlign: 'center' }}
                >
                    {formatIndianValue(premium)}
                </Typography>
            </CardContent>
        </StyledCard>
    );
});

AgentCard.propTypes = {
    agent: PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        name: PropTypes.string,
        branch: PropTypes.string,
        profile_pic: PropTypes.string,
    }).isRequired,
    premium: PropTypes.number.isRequired,
    count: PropTypes.number.isRequired,
    rank: PropTypes.number.isRequired,
    cardHeight: PropTypes.number.isRequired,
};

// --- Carousel breakpoints ---
const responsive = {
    superLargeDesktop: { breakpoint: { max: 4000, min: 3000 }, items: 5 },
    desktop: { breakpoint: { max: 3000, min: 1024 }, items: 4 },
    tablet: { breakpoint: { max: 1024, min: 464 }, items: 2 },
    mobile: { breakpoint: { max: 464, min: 0 }, items: 1 },
};

// --- Top20Agents Component ---
const Top20Agents = ({ data, cardHeight = 350 }) => {
    const [timeFilter, setTimeFilter] = useState('thisMonth');
    const handleTimeFilterChange = (_, newFilter) => {
        if (newFilter) setTimeFilter(newFilter);
    };

    const { agents = [], totalPremiums = [], totalCounts = [] } =
        data[timeFilter] || {};

    return (
        <Paper
            elevation={3}
            sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: '#fff',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                '&:hover': { boxShadow: '0 8px 16px rgba(0,0,0,0.1)' },
                animation: `${fadeIn} 0.5s ease-out`,
                background: 'linear-gradient(145deg, #ffffff, #f5f5f5)',
                position: 'relative',
                overflow: 'hidden',
                '&:before': {
                    content: '""',
                    position: 'absolute',
                    top: '-50%',
                    left: '-50%',
                    width: '200%',
                    height: '200%',
                    background: 'conic-gradient(from 45deg, transparent 20%, #2196F3 50%, transparent 80%)',
                    animation: `${gradientBackground} 6s linear infinite`,
                    opacity: 0.1,
                },
            }}
        >
            {/* Header + Toggle */}
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 3,
                }}
            >
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
                    Top Performing Agents {currentFinancialYear()}
                </Typography>
                <ToggleButtonGroup
                    value={timeFilter}
                    exclusive
                    onChange={handleTimeFilterChange}
                    size="small"
                >
                    <AnimatedToggleButton value="thisMonth">This Month</AnimatedToggleButton>
                    <AnimatedToggleButton value="thisQuarter">This Quarter</AnimatedToggleButton>
                    <AnimatedToggleButton value="thisFinancialYear">This Year</AnimatedToggleButton>
                </ToggleButtonGroup>
            </Box>

            {/* Carousel or No Data Message */}
            {agents?.length > 0 ? (
                <CarouselWrapper>
                    <StyledCarousel
                        responsive={responsive}
                        infinite
                        autoPlay
                        autoPlaySpeed={3000}
                        customLeftArrow={<CustomArrow direction="left" label="Previous agent" />}
                        customRightArrow={<CustomArrow direction="right" label="Next agent" />}
                        removeArrowOnDeviceType={['tablet', 'mobile']}
                        containerClass="carousel-container"
                        itemClass="carousel-item"
                    >
                        {agents.map((agent, idx) => (
                            <Box key={agent.id || idx} sx={{ display: 'flex', justifyContent: 'center' }}>
                                <AgentCard
                                    agent={agent}
                                    premium={totalPremiums[idx] || 0}
                                    count={totalCounts[idx] || 0}
                                    rank={idx + 1}
                                    cardHeight={cardHeight}
                                />
                            </Box>
                        ))}
                    </StyledCarousel>
                </CarouselWrapper>
            ) : (
                    <Typography 
                        variant="h5" 
                        color="text.secondary"
                        sx={{
                            padding: 3,
                            backgroundColor: 'background.paper',
                            borderRadius: 1,
                            boxShadow: 1
                        }}
                    >
                        No data available for this time period
                    </Typography>
            )}
        </Paper>
    );
};

Top20Agents.propTypes = {
    data: PropTypes.shape({
        thisMonth: PropTypes.object,
        thisQuarter: PropTypes.object,
        thisFinancialYear: PropTypes.object,
    }).isRequired,
    cardHeight: PropTypes.number,  // height in pixels
};

export default Top20Agents;
