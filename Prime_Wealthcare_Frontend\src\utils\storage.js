export const isTokenExpired = (token) => {
    if (!token) return true;

    try {
        const base64Url = token.split('.')[1];
        if (!base64Url) return true;

        const decoded = JSON.parse(window.atob(base64Url.replace(/-/g, '+').replace(/_/g, '/')));
        return decoded.exp * 1000 < Date.now();
    } catch (error) {
        console.error('Error checking token expiration:', error);
        return true;
    }
};

export const getToken = () => {
    // First check sessionStorage for current tab
    let token = sessionStorage.getItem('token');
    
    // If no token in sessionStorage, check localStorage
    if (!token) {
        token = localStorage.getItem('token');
        // If found in localStorage, also set in sessionStorage
        if (token) {
            sessionStorage.setItem('token', token);
        }
    }

    if (!token) {
        return null;
    }

    if (isTokenExpired(token)) {
        clearUserSession();
        return null;
    }

    return token;
};

export const getUserData = () => {
    const token = getToken();

    if (!token) {
        return null;
    }

    try {
        const base64Url = token.split('.')[1];
        const userData = JSON.parse(window.atob(base64Url.replace(/-/g, '+').replace(/_/g, '/')));
        if (userData) {
            // Store complete user data
            const userDataToStore = {
                ...userData,
                username: userData.userId || userData.user_id, // Ensure username is set
                full_name: userData.employee_full_name || userData.full_name
            };
            sessionStorage.setItem('user', JSON.stringify(userDataToStore));
        }
        const decoded = JSON.parse(window.atob(base64Url.replace(/-/g, '+').replace(/_/g, '/')));
        return decoded;
    } catch (error) {
        console.error('❌ Error decoding token:', error);
        return null;
    }
};

export const setUserSession = (token, keepLoggedIn = false) => {
    // Always store in both storages for cross-tab functionality
    sessionStorage.setItem('token', token);
    localStorage.setItem('token', token);
    
    // Store the keepLoggedIn preference
    localStorage.setItem('keepLoggedIn', keepLoggedIn.toString());

    try {
        const base64Url = token.split('.')[1];
        const userData = JSON.parse(window.atob(base64Url.replace(/-/g, '+').replace(/_/g, '/')));
        if (userData) {
            localStorage.setItem('user', JSON.stringify(userData));
        }
    } catch (error) {
        console.error('❌ Error parsing user data from token:', error);
    }
};

export const clearUserSession = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('keepLoggedIn');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    console.log('✨ Session storage cleared');
};
