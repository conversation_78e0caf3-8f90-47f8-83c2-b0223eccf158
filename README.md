# Prime WealthCare Application

## Overview
Prime WealthCare is a full-stack application with separate frontend and backend components, now consolidated into a single repository.

## Project Structure
```
Prime_Wealthcare/
├── Prime_Wealthcare_Frontend/    # React frontend application
├── Prime_Wealthcare_Backend/     # Node.js backend application
├── package.json                  # Root package for running both applications
└── README.md                     # This file
```

## Prerequisites
- Node.js and npm installed

## Installation

1. **Clone the Repository**

```bash
git clone https://github.com/SimpLiTechs/Prime_Wealthcare.git
cd Prime_Wealthcare
```

2. **Checkout the Branch**

Switch to the development branch:

```bash
git checkout deploy
```

3. **Install Dependencies**

Install all dependencies (backend, frontend, and root):

```bash
npm run install:all
```

Or install them individually:

```bash
# Install root dependencies
npm install

# Install backend dependencies
npm run install:backend

# Install frontend dependencies
npm run install:frontend
```

## Development

Run both frontend and backend in development mode:

```bash
npm run dev
# or
npm start
```

Run only backend:

```bash
npm run start:backend
```

Run only frontend:

```bash
npm run start:frontend
```

Build frontend for production:

```bash
npm run build:frontend


## Production Deployment with PM2

Start both applications with PM2:
```bash
npm run pm2:start
```

Start only backend with PM2:
```bash
npm run pm2:start:backend
```

Start only frontend with PM2:
```bash
npm run pm2:start:frontend
```

Manage PM2 processes:
```bash
# View status of all PM2 processes
npm run pm2:status

# Stop both applications
npm run pm2:stop

# Restart both applications
npm run pm2:restart

# Delete PM2 processes
npm run pm2:delete
```

```

## Backend Setup

1. **Create a Master Schema in MySQL Workbench**

Before running the backend, create a master schema (database) in MySQL Workbench.

2. **Create a .env File**

In the root of your project, create a .env file and add the following environment variables with your database credentials and app configurations:

```bash
DB_HOST=localhost
DB_USER=root
DB_PASSWORD= # Add your password here
DB_NAME=master
HOST=localhost
PORT=5000
CORS_ORIGIN=http://localhost:3000
```

3. **Run the Backend Server**

Start the backend server using nodemon:

```bash
npx nodemon
```

or simply:

```bash
nodemon
```

## Frontend Setup

1. **Run the Project**

To start the development server, use:

```bash
npm start
```

This will open the application in your default web browser.

2. **Development Scripts**

- `npm start` - Runs the development server.
- `npm run build` - Builds the application for production.
- `npm test` - Runs the test suite.

## Node.js Version

Ensure you are using Node.js version v20.16.0. You can check your Node.js version with:

```bash
node -v
```

If you need to switch Node.js versions, consider using a version manager like nvm.

## Configuration
- Frontend runs on the default React port (3000)
- Backend configuration can be found in the Prime_Wealthcare_Backend directory

## Note

Make sure to configure any environment variables needed for your application before deployment.