import { createSlice } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { 
  generateReport,
  getFinancialYears
} from '../../actions/action';

const initialState = {
  reportData: null,
  financialYears: [],
  loading: false,
  error: null,
};

const reportsSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    clearReportData: (state) => {
      state.reportData = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Generate Report
      .addCase(generateReport.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateReport.fulfilled, (state, action) => {
        state.loading = false;
        state.reportData = action.payload.data;
        toast.success('Report generated successfully');
      })
      .addCase(generateReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error(action.payload?.message || 'Failed to generate report');
      })
      
      // Get Financial Years
      .addCase(getFinancialYears.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getFinancialYears.fulfilled, (state, action) => {
        state.loading = false;
        state.financialYears = action.payload.data;
      })
      .addCase(getFinancialYears.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearReportData } = reportsSlice.actions;
export default reportsSlice.reducer;