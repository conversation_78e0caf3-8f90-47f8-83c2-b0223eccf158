import React, { useState, useEffect } from 'react';
import {
	Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
	Paper, TablePagination, Checkbox, IconButton, tableCellClasses, Typography,
	Box,
	FormControl, Button
} from '@mui/material';
import { styled } from '@mui/material/styles';
import EditIcon from '@mui/icons-material/Edit';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import RedEyeIconButton from '../eyeIconComponent';
import CustomTextField from '../CustomTextField';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

function CustomEditableTable({
	data = [], columns = [], onEdit, onDelete, onReinstate, onView,
	onSelectionChange, selectedRows, onSelectAll, showActions = true,
	hideDelete = false, showEyeIcon = false, handleViewData,
	isCheckboxRequired = true, isViewEnabled = true, onCellEdit,
	logicColumn, updateButtonText, isDisabled = false
}) {
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(10);
	const [deletedIds, setDeletedIds] = useState(() => {
		const savedIds = localStorage.getItem('deletedIds');
		return savedIds ? JSON.parse(savedIds) : [];
	});
	const [editCell, setEditCell] = useState({ rowId: null, field: null });
	const [editValue, setEditValue] = useState('');

	useEffect(() => {
		localStorage.setItem('deletedIds', JSON.stringify(deletedIds));
	}, [deletedIds]);

	const StyledTableCell = styled(TableCell)(({ theme, status }) => ({
		[`&.${tableCellClasses.head}`]: {
			backgroundColor: "#528A7E",
			color: theme.palette.common.white,
			padding: '8px',
			textAlign: 'center',
		},
		[`&.${tableCellClasses.body}`]: {
			fontSize: 14,
			color: status === 0 ? 'red' : 'inherit',
			borderBottom: 'none',
			padding: '8px',
			textAlign: 'center',
		},
		width: `${100 / (columns.length + (isCheckboxRequired ? 1 : 0) + (showActions ? 1 : 0))}%`,
		'&.actions': {
			display: 'flex',
			justifyContent: 'center',
			alignItems: 'center',
			gap: '8px',
		}
	}));

	const StyledTableRow = styled(TableRow)(({ theme, status }) => ({
		'&:nth-of-type(odd)': {
			backgroundColor: theme.palette.action.hover,
		},
		'&:last-child td, &:last-child th': {
			border: 0,
		},
	}));

	const handleChangePage = (event, newPage) => {
		setPage(newPage);
	};

	const handleChangeRowsPerPage = (event) => {
		const newRowsPerPage = parseInt(event.target.value, 10);
		setRowsPerPage(newRowsPerPage);
		setPage(0);
	};

	const handleSelectAll = (event) => {
		onSelectAll(event.target.checked);
	};

	const handleEditCell = (rowId, field, value) => {
		setEditCell({ rowId, field });
		setEditValue(value);
	};

	const handleCellEdit = (value) => {
		setEditValue(value);
	};

	const handleCellUpdate = () => {
		onCellEdit(editCell.rowId, editCell.field, editValue);
		setEditCell({ rowId: null, field: null });
	}

	return (
		<Box sx={{ position: 'relative', width: '100%' }}>
			<TableContainer component={Paper} sx={{ width: '100%', marginInline: 'auto', paddingTop: '1rem' }}>
				<Table sx={{ tableLayout: 'auto' }} aria-label="customized table">
					<TableHead>
						<TableRow>
							<StyledTableCell sx={{ width: '5%', }}>Serial No.</StyledTableCell>
							{isCheckboxRequired && (
								<StyledTableCell padding="checkbox">
									<Checkbox
										onChange={handleSelectAll}
										checked={data.length > 0 && selectedRows?.length === data.length}
									/>
								</StyledTableCell>
							)}
							{columns.map((column) => (
								<StyledTableCell key={column.field}>
									{column.headerName}
								</StyledTableCell>
							))}
							{showActions && <StyledTableCell>Action</StyledTableCell>}
						</TableRow>
					</TableHead>
					<TableBody>
						{Array.isArray(data) && data.length > 0 ? (
							data.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((item, index) => (
								<StyledTableRow key={item.id} status={item.status}>
									<StyledTableCell sx={{ width: 'fit-content' }}>
										<Typography variant="small" align="center">
											{page * rowsPerPage + index + 1}
										</Typography>
									</StyledTableCell>
									{isCheckboxRequired && (
										<StyledTableCell padding="checkbox">
											<Checkbox
												checked={selectedRows?.includes(item.id)}
												onChange={() => onSelectionChange(item.id)}
											/>
										</StyledTableCell>
									)}
									{columns.map((column) => (
										<StyledTableCell key={column.field} status={item.status} sx={{
											margin: 'auto',
										}}>
											<Box id='editable-cell' sx={{
												border: (item.status !== 0 && column.editable && !isDisabled) ? '2px solid black' : 'inherit',
												width: '100%'
											}}>
												{!isDisabled && editCell.rowId === item.id && editCell.field === column.field && item.status !== 0 && onCellEdit && column.editable ? (
													column.inputType === 'text' ? (
														<CustomTextField
															value={editValue}
															onChange={(e) => handleCellEdit(e.target.value)}
															sx={{ width: '100%', height: '100%' }}
															size='large'
														/>
													) : column.inputType === 'date' ? (
														<FormControl fullWidth required>
															<LocalizationProvider dateAdapter={AdapterDayjs}>
																<DatePicker
																	value={editValue ? dayjs(editValue) : null}
																	onChange={(newDate) => handleCellEdit(dayjs(newDate))}
																	format="DD/MM/YYYY"
																	minDate={column.minDate ? dayjs(column.minDate) : null}
																	maxDate={column.maxDate ? dayjs(column.maxDate) : null}
																	slotProps={{
																		textField: {
																			fullWidth: true,
																		}
																	}}
																/>
															</LocalizationProvider>
														</FormControl>
													) : null
												) : (
													<span style={{ width: '100%', display: 'block' }} onClick={() => item.status !== 0 && onCellEdit ? handleEditCell(item.id, column.field, item[column.field]) : null}>
														{column.inputType === 'date' ? item[column.field] ? dayjs(item[column.field]).format('DD/MM/YYYY') : "N/A" : item[column.field] || "N/A"}
													</span>
												)}
											</Box>
										</StyledTableCell>
									))}
									{showActions && (
										<StyledTableCell className="actions" sx={{ margin: 'auto' }}>
											<Button
												variant="contained"
												size='small'
												onClick={() => handleCellUpdate()}
												sx={{
													color: 'white',
													padding: '8px',
													backgroundColor: item.status ? 'red' : '#528A7E',
													'&:hover': {
														backgroundColor: item.status ? '#b22222' : '#3a6b6a'
													},
													'&:disabled': {
														backgroundColor: item.status ? 'rgba(255, 0, 0, 0.5)' : 'rgba(82, 138, 126, 0.5)',
														color: 'white'
													}
												}}
												disabled={editCell.rowId !== item.id || isDisabled}
											>
												{updateButtonText[Number(item.status)] || 'Update'}
											</Button>
										</StyledTableCell>
									)}
								</StyledTableRow>
							))
						) : (
							<StyledTableRow>
								<StyledTableCell colSpan={columns.length + (showActions ? 2 : 1) + 1}>
									<Typography variant="body1" align="center" sx={{ padding: '1rem' }}>
										No data present
									</Typography>
								</StyledTableCell>
							</StyledTableRow>
						)}
					</TableBody>
				</Table>
			</TableContainer>
			<Box sx={{
				position: 'sticky',
				right: 0,
				bottom: 0,
				width: '100%',
				backgroundColor: 'white',
				borderTop: '1px solid rgba(224, 224, 224, 1)',
				display: 'flex',
				justifyContent: 'flex-end'
			}}>
				<TablePagination
					rowsPerPageOptions={[
						10,
						20,
						{ label: 'All', value: data.length }
					]}
					component="div"
					count={Array.isArray(data) ? data.length : 0}
					rowsPerPage={rowsPerPage}
					page={page}
					onPageChange={handleChangePage}
					onRowsPerPageChange={handleChangeRowsPerPage}
				/>
			</Box>
		</Box>
	);
}

export default CustomEditableTable;