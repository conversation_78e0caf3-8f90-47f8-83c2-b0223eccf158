import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
    deleteLocationByPincode,
    getAllLocations,
    getLocationByCriteria,
    getLocationByName
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { startLoading, stopLoading } from '../../../redux/slices/loadingSlice';
import { usePermissions } from '../../../hooks/usePermissions';

function LocationTypePage() {
    const [selectedOption, setSelectedOption] = useState('none');
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const locations = useSelector(state => state.areaManagementReducer.locations);
    const [sortedLocations, setSortedLocations] = useState(locations || []);
    const isLoading = useSelector(state => state.areaManagementReducer.status);
    const loading = useSelector(state => state.loading.isLoading);

    const permissions = usePermissions('Master', 'Area Master') || {};

    useEffect(() => {
        if (isLoading === 'rejected') {
            dispatch(startLoading());
        } else {
            dispatch(stopLoading());
        }
    }, [isLoading, dispatch])


    useEffect(() => {
        dispatch(getAllLocations())
    }, [dispatch])
    useEffect(() => {
        const fetchLocations = () => {
            dispatch(getLocationByCriteria(selectedOption));
        };
        fetchLocations();
    }, [selectedOption, dispatch]);

    useEffect(() => {
        setSortedLocations(locations);
    }, [locations])

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleConfirmDelete = () => {
        dispatch(deleteLocationByPincode(selectedItem))
            .then(() => {
                dispatch(getAllLocations());
                setOpenDeletePopup(false);
                setOpenSuccessPopup(true);
            })
            .catch(error => {
                console.error('Failed to delete location:', error);
                setOpenDeletePopup(false);
            });
    };

    const extractNumber = (str) => {
        const parts = str.split('-');
        const number = parseInt(parts[0], 10);
        return number;
    }

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/area-form');
    };

    const handleDelete = (id) => {
        handleOpenDeletePopup(locations.find(location => location.id === id));
    };


    const handleEdit = (id) => {
        navigate(`/dashboard/area-form/edit/${extractNumber(id)}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedLocations.map(location => location.id) : []);
    };

    const onSearch = (query) => {
        if (query === '') {
            dispatch(getAllLocations());
        } else {
            dispatch(getLocationByName(query));
        }
    };

    const handleRefreshClick = () => {
        setSelectedOption('none');
        dispatch(getAllLocations());
    }

    const columns = [
        { field: 'area_name', headerName: 'Area Name' },
        { field: 'city_name', headerName: 'City' },
        { field: 'location_pincode', headerName: 'Pincode' },
        { field: 'state', headerName: 'State' },
    ];

    const dataMapping = {
        'Area Name': 'area_name',
        City: 'city_name',
        Pincode: 'location_pincode',
        State: 'state',
        Status: 'status',
    };

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Area" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                    {permissions.can_add && (
                        <Button onClick={handleAdd} sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}>
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={sortedLocations.map(location => ({
                                ...location,
                                status: location.status === 1 ? 'Active' : 'Inactive'
                            }))}
                            headNames={['Area Name', 'City', 'Pincode', 'State', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="locations.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Area Report"
                        />
                    </ButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingInline: '1rem', ml: 5 }}>
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' }, 
                        ]}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SearchBar placeholder="Search..." onSearch={onSearch} />
                        <IconActions
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>

                <Box sx={{ mt: -1, maxHeight: '400px' }}>
                    <CustomTable
                        data={sortedLocations}
                        columns={columns}
                        onDelete={permissions.can_delete ? handleDelete : null}
                        onEdit={permissions.can_edit ? handleEdit : null}
                        onSelectionChange={handleSelectionChange}
                        selectedRows={selectedRows}
                        onSelectAll={handleSelectAll}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                message={`Are you sure you want to delete this city and all the related areas`}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.city_name : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                deleted={'deleted'}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.city_name : ''}
            />
        </Container>
    )
}

export default LocationTypePage