// models/EmployeeModel.js
const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const { sendEmail } = require('../../../services/emailService');  // Assuming emailService.js is inside services folder


// Function to generate a password based on the Employee's first name
const generatePasswordFromFirstName = (firstName) => {
  return `${firstName}@123`;
};

// const generateUserId = async (branch_name_array) => {
//     // Ensure the branch_name is an array and use the first element for the prefix
//     const branch_name = Array.isArray(branch_name_array) ? branch_name_array[0] : branch_name_array;

//     // Get the first 3 letters of the branch name and convert to uppercase
//     const prefix = branch_name.slice(0, 3).toUpperCase();

//     // Retrieve the most recent user ID that matches the prefix, sorted in descending order
//     const lastUser = await db('employee_personal_info')
//         .where('user_id', 'like', `${prefix}%`)
//         .orderBy('user_id', 'desc')
//         .first();

//     // Extract the numeric part from the last user ID or start at 0 if none exists
//     const lastNumericPart = lastUser ? parseInt(lastUser.user_id.slice(3), 10) : 0;

//     // Increment the numeric part by 1
//     const newNumericPart = lastNumericPart + 1;

//     // Format the new user ID with the prefix and a zero-padded numeric part (to 3 digits)
//     const newUserId = `${prefix}${String(newNumericPart).padStart(3, '0')}`;

//     return newUserId;
// };
// const generateUserId = async (selectedBranches, roleId) => {
//   if (!selectedBranches || selectedBranches.length === 0) {
//       throw new Error("No branch selected.");
//   }

//   // Extract the branch name for the prefix
//   const branch_name = Array.isArray(selectedBranches) ? selectedBranches[0] : selectedBranches;
//   const branchPrefix = branch_name.slice(0, 3).toUpperCase(); // First 3 letters of the branch name
//   const prefixData = "PWS-";
//   const prefixEmp = "-EMP";

//   // Fetch the role name from the role_management table
//   const role = await db('role_management').where({ id: roleId }).first();
//   if (!role || !role.role_name) {
//       throw new Error("Invalid role ID or role not found.");
//   }

//   // Determine if the role is a leader or an employee
//   const isLeader = role.role_name.toLowerCase() === 'leader';

//   // Retrieve the latest user ID from the database
//   const lastUser = await db('employee_personal_info')
//       .select('user_id')
//       .where('user_id', isLeader ? 'like' : 'not like', 'LD%') // Filter based on role type
//       .orderBy('user_id', 'desc')
//       .first();

//   let newNumericPart = 1; // Start the sequence at 1 if no IDs exist

//   if (lastUser && lastUser.user_id) {
//       const lastUserId = lastUser.user_id;
//       if (isLeader) {
//           // Extract the numeric part from a leader ID (e.g., LD0001)
//           const lastLeaderNumericPart = parseInt(lastUserId.slice(2), 10);
//           newNumericPart = isNaN(lastLeaderNumericPart) ? 1 : lastLeaderNumericPart + 1;
//       } else {
//           // Extract the numeric part from an employee ID (e.g., PWS-ANI-EMP001)
//           const lastEmployeeNumericPart = parseInt(lastUserId.split(prefixEmp)[1], 10);
//           newNumericPart = isNaN(lastEmployeeNumericPart) ? 1 : lastEmployeeNumericPart + 1;
//       }
//   }

//   // Generate the new user ID based on the role
//   let newUserId;
//   if (isLeader) {
//       newUserId = `LD${String(newNumericPart).padStart(4, '0')}`; // Leader ID format
//   } else {
//       newUserId = `${prefixData}${branchPrefix}${prefixEmp}${String(newNumericPart).padStart(3, '0')}`; // Employee ID format
//   }

//   return newUserId;
// };



const create = async (data, files) => {
  try {
    // Generate the password based on the first name
    data.password = generatePasswordFromFirstName(data.employee_full_name.split(' ')[0]);

    // Use the first branch name from the array to generate user ID
    //const firstBranchName = Array.isArray(data.branch_id) ? data.branch_id[0] : data.branch_id;
    // data.user_id = await generateUserId(firstBranchName, data.role_id);

    // Split branch names and fetch IDs
    // const branchNames = data.branch_id.split(','); // Split branch names into an array
    // const branchIds = await db('imf_branches')
    //     .whereIn('branch_name', branchNames)
    //     .pluck('id');

    // if (branchIds.length > 0) {
    //     data.branch_id = branchIds.join(','); // Convert IDs to a comma-separated string
    // } else {
    //     throw new Error("No matching branch IDs found for provided branch names.");
    // }


    // if (branchIds.length > 0) {
    //     // Convert the array of branch IDs to a comma-separated string
    //     data.branch_id = branchIds.join(',');
    // } else {
    //     // Handle the case where no branch IDs were found
    //     throw new Error("No valid branch IDs found.");
    // }


    // Handle file paths (if files were uploaded)
    if (files) {
      data.emp_photo = req.files.photoFile ? req.files.photoFile[0].path : null;
      data.emp_adhar_front_pdf = req.files.emp_adhar_front_pdf ? req.files.emp_adhar_front_pdf[0].path : null;
      data.emp_adhar_back_pdf = req.files.emp_adhar_back_pdf ? req.files.emp_adhar_back_pdf[0].path : null;
      data.emp_PAN_pdf = req.files.panFile ? req.files.panFile[0].path : null;
      data.emp_signed_offer_letter_pdf = req.files.offerLetterFile ? req.files.offerLetterFile[0].path : null;
      data.emp_driving_license_pdf = req.files.drivingLicenseFile ? req.files.drivingLicenseFile[0].path : null;
    }
    // Insert the employee data into the employee_personal_info table
    const result = await db('employee_personal_info').insert(data);
    // Prepare the email content with a login link
    const subject = 'Welcome to Prime Wealthcare';
    const text = `Dear ${data.employee_full_name},\n\nYou have been successfully registered as an employee at Prime Wealthcare.\n\nYour User ID: ${data.user_id}\nYour Password: ${data.password}\n\nPlease use the link below to log in and access your account:\n\nhttp://localhost:3000\n\nBest regards,\nPrime Wealthcare Team`;

    // HTML content for the email to include the clickable link
    const htmlContent = `
            <p>Dear ${data.employee_full_name},</p>
            <p>You have been successfully registered as an Employee at Prime Wealthcare.</p>
            <p>Your User ID: <strong>${data.user_id}</strong></p>
            <p>Your Password: <strong>${data.password}</strong></p>
            <p>Please click the link below to log in and access your account:</p>
            <p><a href="http://localhost:3000" target="_blank">Login to Prime Wealthcare</a></p>
            <p><strong>Important:</strong> For security reasons, you will be required to change your password upon first login.</p>
           
        `;

    // Send email to official_email if provided
    if (data.official_email) {
      await sendEmail(data.official_email, subject, text, htmlContent);
    }

    // Send email to personal_email (assuming it's required)
    if (data.personal_email) {
      await sendEmail(data.personal_email, subject, text, htmlContent);
    }


    return result;


  } catch (error) {
    console.error("Error creating employee:", error);
    throw error;
  }
};

const findAll = async () => {
  try {
    return await db('employee_personal_info')
      .leftJoin('role_management', 'employee_personal_info.role_id', 'role_management.id')
      .leftJoin('employee_personal_info as first_mgr', 'employee_personal_info.first_reporting_manager_id', 'first_mgr.id')
      .leftJoin('employee_personal_info as second_mgr', 'employee_personal_info.second_reporting_manager_id', 'second_mgr.id')
      .leftJoin(db.raw(`(
        SELECT 
          SUBSTRING_INDEX(SUBSTRING_INDEX(t.branch_id, ',', n.n), ',', -1) branch_id,
          t.id as employee_id
        FROM employee_personal_info t
        CROSS JOIN (
          SELECT a.N + b.N * 10 + 1 n
          FROM (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) a
          CROSS JOIN (SELECT 0 AS N UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9) b
          ORDER BY n
        ) n
        WHERE n.n <= 1 + (LENGTH(t.branch_id) - LENGTH(REPLACE(t.branch_id, ',', '')))
      ) numbers`), 'employee_personal_info.id', 'numbers.employee_id')
      .leftJoin('imf_branches', 'numbers.branch_id', 'imf_branches.id')
      .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
      .select(
        'employee_personal_info.*',
        db.raw('GROUP_CONCAT(DISTINCT imf_branches.branch_name) as branch_names'),
        'role_management.department_name as department_name',
        'first_mgr.employee_full_name as first_manager_name',
        'second_mgr.employee_full_name as second_manager_name',
        db.raw('GROUP_CONCAT(DISTINCT locations.city) as branch_city')
      )
      .groupBy(
        'employee_personal_info.id',
        'role_management.department_name',
        'first_mgr.employee_full_name',
        'second_mgr.employee_full_name'
      );
  } catch (error) {
    console.error('Error in findAll:', error);
    throw error;
  }
};



// Find agent by ID
const findById = async (id) => {
  try {
    // Fetch employee data with all related information
    const employee = await db('employee_personal_info')
      .leftJoin('role_management', 'employee_personal_info.role_id', 'role_management.id')
      .leftJoin('employee_personal_info as first_mgr', 'employee_personal_info.first_reporting_manager_id', 'first_mgr.id')
      .leftJoin('employee_personal_info as second_mgr', 'employee_personal_info.second_reporting_manager_id', 'second_mgr.id')
      .where('employee_personal_info.id', id)
      .first(
        'employee_personal_info.*',
        'role_management.department_name',
        'first_mgr.employee_full_name as first_manager_name',
        'second_mgr.employee_full_name as second_manager_name'
      );

    if (employee && employee.branch_id) {
      // Get all branch names for the employee
      const branchQuery = await db('imf_branches')
        .whereIn('id', employee.branch_id.split(','))
        .select(db.raw('GROUP_CONCAT(branch_name) as branch_names'));

      if (branchQuery && branchQuery[0]) {
        employee.branch_names = branchQuery[0].branch_names;
      }

      // Get city information (if needed)
      const cityQuery = await db('imf_branches')
        .leftJoin('locations', 'imf_branches.pincode_city', 'locations.pincode_city')
        .whereIn('imf_branches.id', employee.branch_id.split(','))
        .select(db.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'))
        .first();

      if (cityQuery && cityQuery.cities) {
        employee.branch_city = cityQuery.cities;
      }
    }

    // Ensure we have default values for null fields
    return {
      ...employee,
      branch_names: employee.branch_names || 'N/A',
      branch_city: employee.branch_city || 'N/A',
      department_name: employee.department_name || 'N/A',
      first_manager_name: employee.first_manager_name || 'N/A',
      second_manager_name: employee.second_manager_name || 'N/A'
    };

  } catch (error) {
    console.error('Error in findById:', error);
    throw error;
  }
};



// Update agent by ID
// const updateById = async (id, data) => {
//     try {
//         // Split branch names into an array
//         const branchNames = data.branch_id.split(',');

//         // Fetch branch IDs from `imf_branches` table where the names match
//         const branchIds = await db('imf_branches')
//             .whereIn('branch_name', branchNames)
//             .pluck('id'); // Retrieve only the IDs

//         if (branchIds.length > 0) {
//             // Convert array of branch IDs to a comma-separated string for saving
//             data.branch_id = branchIds.join(',');
//         } else {
//             throw new Error("No valid branch IDs found for the provided branch names.");
//         }

//         // Set the current timestamp for `updated_at` field
//         data.updated_at = getCurrentTimestamp();

//         // Update the `employee_personal_info` table with the modified data
//         return await db('employee_personal_info').where({ id }).update(data);
//     } catch (error) {
//         console.error("Error updating employee:", error);
//         throw error;
//     }
// };
const updateById = async (id, data) => {
  try {
    // ... existing code ...
    // Ensure the branch IDs are updated based on the branch names
    const branchNames = data.branch_id.split(',');
    const branchIds = await db('imf_branches')
      .whereIn('id', branchNames)
      .pluck('id');
    if (branchIds.length > 0) {
      data.branch_id = branchIds.join(',');
    } else {
      throw new Error("No valid branch IDs found for the provided branch names.");
    }
    // Set the current timestamp for `updated_at` field
    data.updated_at = getCurrentTimestamp();
    // Update the `employee_personal_info` table with the modified data
    return await db('employee_personal_info').where({ id }).update(data);
  } catch (error) {
    console.error("Error updating employee:", error);
    throw error;
  }
}


// Soft delete agent by ID
const softDeleteById = async (id) => {
  try {
    return await db('employee_personal_info').where({ id }).update({ status: false, updated_at: getCurrentTimestamp() });
  } catch (error) {
    throw error;
  }
};

// Reinstate agent by ID
const reinstateById = async (id) => {
  try {
    return await db('employee_personal_info').where({ id }).update({ status: true, updated_at: getCurrentTimestamp() });
  } catch (error) {
    throw error;
  }
};

const getEmployeeByName = async (name) => {
  try {
    const employee = await db('employee_personal_info')
      .leftJoin('role_management', 'employee_personal_info.role_id', 'role_management.id') // Adjust key if necessary
      .select(
        'employee_personal_info.*',
        'role_management.department_name'
      )
      .where(function () {
        this.where('employee_personal_info.employee_full_name', 'LIKE', `%${name}%`)
          .orWhere('role_management.department_name', 'LIKE', `%${name}%`);
      });

    return employee;
  } catch (error) {
    console.error(`Error fetching Employees by name: ${name}`, error);
    throw error;
  }
};

const newEmployeeLastWeek = async () => {
  try {
    const employee = await db('employee_personal_info')
      .select(
        'employee_personal_info.*',

      )
    [
      db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
      db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
    ];

    return employee;
  } catch (error) {
    console.error('Error fetching new employee created last week:', error);
    throw error;
  }
};

const newEmployeeThisWeek = async () => {
  try {
    const employee = await db('employee_personal_info')
      .select(
        'employee_personal_info.*'

      )
    [
      db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
      db.raw('NOW()')
    ];

    return employee;
  } catch (error) {
    console.error('Error fetching new employee created this week:', error);
    throw error;
  }
};


const deactivatedEmployeeThisWeek = async () => {
  try {
    const employee = await db('employee_personal_info')
      .select(
        'employee_personal_info.*'

      )
      .where('employee_personal_info.status', 0)
      .whereBetween('employee_personal_info.updated_at', [
        db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
        db.raw('NOW()')
      ]);

    return employee;
  } catch (error) {
    console.error('Error fetching deactivated employee updated this week:', error);
    throw error;
  }
};


const deactivatedEmployeeLastWeek = async () => {

  try {
    const employee = await db('employee_personal_info')
      .select(
        'employee_personal_info.*',

      )

      .where('employee_personal_info.status', 0)
      .whereBetween('employee_personal_info.updated_at', [
        db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return employee;
  } catch (error) {
    console.error('Error fetching deactivated employee updated last week:', error);
    throw error;
  }
};


const editedEmployeeThisWeek = async () => {
  try {

    const employee = await db('employee_personal_info')

      .select(
        'employee_personal_info.*'

      )

      .whereBetween('employee_personal_info.updated_at', [
        db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
        db.raw('NOW()')
      ]);
    return employee;
  } catch (error) {
    console.error('Error fetching edited employee updated this week:', error);
    throw error;
  }
};

const editedEmployeeLastWeek = async () => {
  try {
    const employee = await db('employee_personal_info')
      .select(
        'employee_personal_info.*'

      )

      .whereBetween('employee_personal_info.updated_at', [
        db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return employee;
  } catch (error) {
    console.error('Error fetching edited employee updated last week:', error);
    throw error;
  }
}

const getEmployeeByReportingManagerUserId = async (user_id) => {
  try {
    // First, get the main employee details
    const employee = await db('employee_personal_info')
      .where({ user_id, status: 1 })
      .first();

    if (!employee) {
      throw new Error('Employee not found with the provided User ID');
    }

    // Get all reporting employees (where first_reporting_manager_id matches the employee's ID)
    const reportingEmployees = await db('employee_personal_info')
      .where({
        first_reporting_manager_id: employee.id,
        status: 1
      })
      .select('*');

    // Return both the employee and their reporting employees
    return reportingEmployees


  } catch (error) {
    throw error;
  }
}

const getEmployeeByUserId = async (user_id) => {
  try {
    const employee = await db('employee_personal_info')
      .where({ user_id, status: 1 },)
      .first();

    return employee;
  } catch (error) {
    throw error;
  }
};




module.exports = {
  create,
  findAll,
  findById,
  updateById,
  softDeleteById,
  reinstateById,
  getEmployeeByName,
  deactivatedEmployeeLastWeek,
  deactivatedEmployeeThisWeek,
  editedEmployeeLastWeek,
  editedEmployeeThisWeek,
  newEmployeeThisWeek,
  newEmployeeLastWeek,
  getEmployeeByReportingManagerUserId,
  getEmployeeByUserId
};
