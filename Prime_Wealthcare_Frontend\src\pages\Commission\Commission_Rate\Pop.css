/* Popup.css */

/* Style for the date picker input fields */
.date-picker {
  width: 100%; /* Ensures full width within the Grid item */
  padding: 8px 12px;
  font-size: 16px;
  height: 56px; /* Adjust as needed for better visibility */
  box-sizing: border-box;
}

/* Customize the calendar popover to appear above other elements */
.MuiDatePicker-popper {
  z-index: 1500 !important; /* Higher than MUI Dialog's default z-index */
}

/* Optional: Customize the calendar’s width */
.custom-calendar {
  width: 300px !important; /* Set desired width */
}

/* Optional: Further customize the calendar's appearance */
.custom-calendar .MuiPickersCalendar-root {
  width: 100%; /* Ensures it takes the full width of the popover */
}

/* Ensure the popover is not cut off */
.MuiDatePicker-popper {
  overflow: visible;
}
