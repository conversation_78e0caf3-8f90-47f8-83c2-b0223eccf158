const express = require('express');
const agentLoanController = require('../Controllers/agentLoanController');
const router = express.Router();

// Route to get all Agent Loans
router.get('/', agentLoanController.getAllLoans);

// Route to get an Agent Loan by ID
router.get('/:id', agentLoanController.getLoanById);

// Route to create a new Agent Loan
router.post('/', agentLoanController.createLoan);

// Route to update an Agent Loan by ID
router.put('/:id', agentLoanController.updateLoan);

// Route to delete (deactivate) an Agent Loan by ID
router.delete('/:id', agentLoanController.deleteLoan);

// Route to get all EMIs related to a given loan ID
router.get('/:loanId/emis', agentLoanController.getLoanEmi);

// Route to update an EMI by ID
router.put('/emis/:id', agentLoanController.updateEmi);

// Route to delete (deactivate) an EMI by ID
router.delete('/emis/:id', agentLoanController.deleteEmi);

module.exports = router;
