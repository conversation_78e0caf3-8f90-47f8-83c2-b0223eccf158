import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Box, Container, Button, ButtonGroup, Typography, Grid, Avatar } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import { useNavigate, useParams } from 'react-router-dom';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useDispatch, useSelector } from 'react-redux';
import DetailsDropdown from '../../../components/table/DetailsDropdown';
import CustomSection from '../../../components/CustomSection';
import { deleteEmployeeSalary, fetchAllEmployeeSalary, fetchEmployeeById, getEmployeeBankDetailsByEmployeeId, getAllEmployeeBankDetails, getEmployeeBankDetailsById } from '../../../redux/actions/action';
import { getAllRoles } from '../../../redux/actions/action';
import ExportToPDF from '../../../components/ExportToPDF';
import dayjs from 'dayjs';
import { usePermissions } from '../../../hooks/usePermissions';
import AvatarImage from '../../../components/AvatarImage';

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-GB').format(date); // 'en-GB' formats date as dd/mm/yyyy
}

function EmployeeOverviewPage() {

    const { id } = useParams();
    const [selectedOption, setSelectedOption] = useState('none');
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const items = useSelector(state => state.areaManagementReducer.items);
    const employeeSalary = useSelector(state => state.employeeSalaryReducer.salaries);
    const employeeBankDetails = useSelector(state => state.employeeBankReducer.employeeBankDetails);
    const [sortedItems, setSortedItems] = useState(items || []);
    const [selectedRows, setSelectedRows] = useState([]);
    const permissions = usePermissions('User Management', 'Employee Overview')
    // Get all the required permissions
    const overviewPermissions = usePermissions('User Management', 'Employee Overview');
    const bankPermissions = usePermissions('User Management', 'Employee Bank Details');
    const salaryPermissions = usePermissions('User Management', 'Employee Salary Details');
    const taskPermissions = usePermissions('Task Management', 'Employee Tasks');

    // Replace the single permissions variable with these specific ones throughout the component
    const employee = useSelector(state => state.employeeInfoReducer.employeeDetail);
    const roles = useSelector(state => state.roleManagementReducer.roles);

    const employeeInformation = useMemo(() => {
        if (!employee) return null;

        const role = roles?.find(r => r.id === employee.role_id);
        return {
            ...employee,
            role_name: role?.role_name || 'N/A',
            department_name: role?.department_name || 'N/A'
        };
    }, [employee, roles]); // Add dependencies

    useEffect(() => {
        const loadData = async () => {
            try {
                // Clear previous employee data before loading new data
                dispatch({ type: 'CLEAR_EMPLOYEE_DETAILS' }); // You'll need to add this action type

                // Sequential loading to prevent race conditions
                await dispatch(fetchEmployeeById(id));
                await dispatch(getAllRoles());
                await Promise.all([
                    dispatch(fetchAllEmployeeSalary(id)),
                    dispatch(getEmployeeBankDetailsByEmployeeId(id))
                ]);
            } catch (error) {
                console.error('Error loading employee data:', error);
            }
        };

        if (id) {
            loadData();
        }

        // Cleanup function
        return () => {
            dispatch({ type: 'CLEAR_EMPLOYEE_DETAILS' });
        };
    }, [id, dispatch]);

    useEffect(() => {
        setSortedItems(items);
    }, [items]);


    const handleAdd = () => {
        navigate('/dashboard/employee-personal-information'); // Navigate to the create personal detail page
    };

    const handleExportToPDF = () => {
        const doc = new jsPDF({ orientation: 'landscape' });
        autoTable(doc, {
            head: [['Full Name', 'User ID', 'Office Mobile', 'Office Email', 'Department', 'Role', 'Personal Mobile', 'Personal Email', 'Joining Date', 'Branch Name', 'City', 'Date of Birth']],
            body: [[
                employeeInformation.employee_full_name,
                employeeInformation.user_id,
                employeeInformation.official_mobile,
                employeeInformation.official_email,
                employeeInformation.department_name,
                employeeInformation.role_name,
                employeeInformation.personal_mobile,
                employeeInformation.personal_email,
                formatDate(employeeInformation.date_of_joining),
                employeeInformation.branch_name,

                formatDate(employeeInformation.date_of_birth)
            ]],
        });
        doc.save('employee_personal_information.pdf');
    };

    const handleCancel = () => {
        navigate('/dashboard/employee-Master');
    };
    const maskMobile = (mobile) => {
        if (!mobile || typeof mobile !== 'string' || mobile.length < 4) {
            return mobile; // Return the original mobile number if invalid
        }

        // Replace all but the last 4 digits with asterisks
        const visiblePart = mobile.slice(-4); // Last 4 digits
        const maskedMobile = `******${visiblePart}`;

        return maskedMobile;
    };

    // Example usage
    const mobileNumber = '9876543210';
    const maskedMobileNumber = maskMobile(mobileNumber);

    const maskEmail = (email) => {
        if (!email || typeof email !== 'string') {
            return '';
        }

        // Split the email into local part and domain
        const [localPart, domain] = email.split('@');

        if (!localPart || !domain) {
            return email; // Return the original email if it's not valid
        }

        // Mask the local part (e.g., "anitapatil" -> "**itamati**")
        const visiblePart = localPart.slice(2, -2); // Take the middle portion
        const maskedLocalPart = `**${visiblePart}**`;

        // Combine the masked local part with the domain
        return `${maskedLocalPart}@${domain}`;
    };

    // Example usage
    const email = '<EMAIL>';
    const maskedEmail = maskEmail(email);

    const renderAvatar = () => {
        const { emp_photo, employee_full_name } = employeeInformation || {};
        const initials = employee_full_name ? employee_full_name.split(' ').slice(0, 2).map(n => n[0].toUpperCase()).join('') : 'N/A';
        return (
            <Box sx={{
                minWidth: emp_photo ? 100 : 50,  // Add minWidth
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: '2rem'  // Add margin to separate from content
            }}>
                <AvatarImage
                    alt={employee_full_name}
                    src={emp_photo || ''}
                    sx={{
                        width: emp_photo ? 100 : 50,
                        height: emp_photo ? 100 : 50,
                        border: '1px solid #e0e0e0'  // Add border for better visibility
                    }}
                >
                    {!emp_photo && initials}
                </AvatarImage>
            </Box>
        );
    };

    // Add data mapping for PDF export
    const dataMapping = {
        'Employee Name': 'employee_name',
        'Employee ID': 'employee_id',
        'Department': 'department',
        'Designation': 'designation',
        'Status': 'status',
        // Add other relevant fields
    };

    return (
        <Container maxWidth="xl">
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName='Employee' pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                            <Button
                                onClick={handleAdd}
                                sx={{
                                    borderTopRightRadius: 0,
                                    borderBottomRightRadius: 0,
                                    border: '1px solid', // Add border
                                    borderColor: 'primary.main' // Use theme color for border
                                }}
                            >
                                New
                            </Button>
                        )}
                        <ExportToPDF
                            data={employeeInformation}
                            headNames={['Employee Name', 'Employee ID', 'Department', 'Designation', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="employees.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Employee Report"
                        />
                        <Button
                            onClick={handleCancel}
                            sx={{
                                borderColor: 'red',
                                color: 'red',
                                borderTopLeftRadius: 0,
                                borderBottomLeftRadius: 0,
                                mr: '8px',
                                border: '1px solid' // Ensure the border is visible
                            }}
                        >
                            Cancel
                        </Button>

                    </ButtonGroup>
                </Box>
                <Grid container >
                    <CustomSection titles={['Overview', 'Personal Details', 'Address']} page='employee' />
                </Grid>
                <Box display="flex" p={2} sx={{
                    padding: '1rem',
                    borderBlock: '1px solid #e0e0e0',
                    gap: '1rem',  // Add gap between elements
                    alignItems: 'flex-start'  // Align items to top
                }}>
                    {renderAvatar(employeeInformation?.emp_photo, employeeInformation?.employee_full_name)}
                    <Box sx={{
                        flex: 1,
                        overflow: 'hidden'  // Prevent content overflow
                    }}>
                        <Grid container spacing={2} sx={{ alignItems: 'flex-start' }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Full Name:</strong> {employeeInformation?.employee_full_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>User ID:</strong> {employeeInformation?.user_id}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Mobile:</strong> {employeeInformation?.official_mobile}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Email:</strong> {employeeInformation?.official_email}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Department:</strong> {employeeInformation?.department_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Role:</strong> {employeeInformation?.role_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Mobile:</strong> {maskMobile(employeeInformation?.personal_mobile)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Email:</strong>{maskEmail(employeeInformation?.personal_email)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Joining Date:</strong> {formatDate(employeeInformation?.date_of_joining)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Branch Name:</strong> {employeeInformation?.branch_names}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>City:</strong> {employeeInformation?.branch_city}</Typography>
                            </Grid>

                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Date of Birth:</strong> {employeeInformation?.date_of_birth ? `**/**/${new Date(employeeInformation.date_of_birth).getFullYear()}` : 'N/A'}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1.25rem' }}>
                    {bankPermissions.can_view && (
                        <DetailsDropdown
                            headerText={`Bank Details (${Array.isArray(employeeBankDetails) ? employeeBankDetails.length : 0})`}
                            tableHeadings={['Account Holder Name', 'Account Number', 'Bank Name', 'Bank Branch', 'IFSC Code', 'Status', 'Created At', 'Updated At']}
                            tableData={Array.isArray(employeeBankDetails) ? employeeBankDetails.map(detail => ({
                                'Account Holder Name': detail.account_holder_name,
                                'Account Number': detail.account_number,
                                'Bank Name': detail.bank_name,
                                'IFSC Code': detail.IFSC_code,
                                'Bank Branch': detail.branch_name,
                                'Status': detail.status,
                                'Created At': detail.created_at,
                                'Updated At': detail.updated_at,
                            })) : []}

                            onEdit={employee.status !== 0 && bankPermissions.can_edit
                                ? () => navigate(`/dashboard/employee-bank-details-form/edit/${id}`)
                                : () => { }
                            }

                            onView={employee.status !== 0 && bankPermissions.can_view
                                ? () => navigate(`/dashboard/employee-bank-details-form/view/${id}`)
                                : null
                            }

                            handleCreate={employee.status !== 0 && bankPermissions.can_add && employeeBankDetails.length === 0
                                ? () => navigate(`/dashboard/employee-bank-details-form/${id}`)
                                : null
                            }
                        // onEdit={bankPermissions.can_edit ? () => navigate(`/dashboard/employee-bank-details-form/edit/${id}`) : null}
                        // onView={bankPermissions.can_view ? () => navigate(`/dashboard/employee-bank-details-form/view/${id}`) : null}
                        // handleCreate={bankPermissions.can_add && employeeBankDetails.length === 0 ?
                        //     () => navigate(`/dashboard/employee-bank-details-form/${id}`) : null}
                        />
                    )}
                    {salaryPermissions.can_view && (
                        <DetailsDropdown
                            headerText={`Salary Details (${employeeSalary?.length})`}
                            tableHeadings={['Employee Name', 'User Id', 'Joining Date', 'Gross Salary', 'Deductable', 'Net Salary', 'Year', 'Created At', 'Updated At']}
                            tableData={employeeSalary?.map(salary => ({
                                'Employee Name': employeeInformation.employee_full_name,
                                'User Id': employeeInformation.user_id,
                                'Joining Date': dayjs(employeeInformation.date_of_joining).format('DD/MM/YYYY'),
                                'Gross Salary': salary.gross_salary,
                                'Deductable': salary.deductible_amount,
                                'Net Salary': salary.net_salary,
                                'Year': salary.year,
                                'Created At': dayjs(salary.created_at).format('DD-MM-YYYY'),
                                'Updated At': dayjs(salary.updated_at).format('DD-MM-YYYY'),
                                'status': salary.status,
                            }))}
                            onEdit={employee.status !== 0 && salaryPermissions.can_edit ? () => navigate(`/dashboard/salary-form/${id}`) : () => { }}
                            onView={employee.status !== 0 && salaryPermissions.can_view ? () => navigate(`/dashboard/salary-form/view/${id}`) : null}
                            handleCreate={employee.status !== 0 && salaryPermissions.can_add ? () => navigate(`/dashboard/salary-form/create/${id}`) : null}
                        />
                    )}
                    {/* {taskPermissions.can_view && (
                     <DetailsDropdown
                        headerText={`Task Management (${agentInformation?.taskManagement?.length})`}
                        tableHeadings={['Assigned To', 'User Id', 'Department', 'In Progress', 'Completed']}
                        tableData={agentInformation?.taskManagement?.map(task => ({
                            'Assigned To': task.assignedTo,
                            'User Id': task.userId,
                            'Department': task.department,
                            'In Progress': task.inProgress,
                            'Completed': task.completed,
                        }))}
                        handleCreate={taskPermissions.can_add ? () => navigate('/dashboard/task-board') : null}
                        /> 
                )} */}
                </Box>

            </Box>
        </Container>
    );
}

export default EmployeeOverviewPage;
