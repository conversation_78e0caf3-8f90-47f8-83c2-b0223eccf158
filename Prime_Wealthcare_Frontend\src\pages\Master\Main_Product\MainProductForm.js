import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { createProduct, getAllProducts, getProductById, getProductByName, updateProduct } from '../../../redux/actions/action';
import { trimFormData } from '../../../utils/Reusable';

function MainProductForm() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const product = useSelector(state => state.mainProductReducer.currentProduct);
    const products = useSelector(state => state.mainProductReducer.products);

    const [formData, setFormData] = useState({
        main_product: '',
        created_at: new Date().toISOString(),
    });

    const [errors, setErrors] = useState({
        main_product: false,
    });

    // Prevent form submission on Enter key press
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
        }
    };

    useEffect(() => {
        if (id) {
            dispatch(getProductById(id));
        } else {
            resetFormData();
        }
    }, [id, dispatch]);

    useEffect(() => {
        if (product && id) {
            setFormData({
                main_product: product.main_product || '',
                created_at: product.created_at || new Date().toISOString(),
            });
        }
    }, [product, id]);

    const resetFormData = () => {
        setFormData({
            main_product: '',
            created_at: new Date().toISOString(),
        });
    };

    const validate = () => {
        let tempErrors = {};
        if (!formData.main_product) {
            tempErrors.main_product = "Main product is required";
        } else {
            // Check for duplicate product names
            const isDuplicate = products?.find(p => p.main_product === formData.main_product && p.id !== Number(id));
            if (isDuplicate) {
                tempErrors.main_product = 'Main product already exists'
            }
        }
        setErrors(tempErrors);
        return Object.keys(tempErrors).length === 0;
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        if (value === ' ') {
            setErrors({
                ...errors,
                [name]: 'Do not start with a whitespace character'
            })
            return;
        }
        const data = value.toUpperCase().replace(/\s{2,}$/, ' ')
        setFormData({
            ...formData,
            [name]: data,
        });
        setErrors({
            ...errors,
            [name]: false,
        });
        if (name === 'main_product' && value !== '') {
            dispatch(getProductByName(value));
        }
    };

    const handleCreationAndUpdate = () => {
        if (!validate()) return;

        const { main_product } = formData;
        const data= { main_product };
        const filteredData = trimFormData(data);

        if (id) {
            dispatch(updateProduct({ id, data: filteredData }));
        } else {
            dispatch(createProduct(filteredData));
        }
        setTimeout(() => {
            dispatch(getAllProducts());
        }, 500);
    };

    const handleSave = () => {
        handleCreationAndUpdate();
        if (validate()) {
            handleCancel();
        }
    };

    const handleSaveAndNew = () => {
        handleCreationAndUpdate();
        if (validate()) {
            resetFormData();
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/main-product-list');
    };

    const formatDate = (date) => {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    return (
        <form onKeyDown={handleKeyDown}>
            <Grid container spacing={2}>
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ModuleName moduleName="Main Product" pageName={id ? product?.status === 0 ? 'View' : 'Edit' : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!id && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '120px', width: '120px', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleSaveAndNew}
                        >
                            Save & New
                        </Button>
                    )}
                    {
                        (product?.status === 1 || !id) && <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleSave}
                        >
                            Save
                        </Button>
                    }
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>

                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
                        <h2>Main Product Information</h2>
                    </Box>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label={'Main Product'}
                            name={'main_product'}
                            value={formData.main_product}
                            onChange={handleChange}
                            helperText={errors.main_product || ''}
                            isDisabled={id && product?.status === 0}
                            isRequired
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Created At"
                            name="created_at"
                            value={formatDate(product?.created_at || new Date().toISOString())}
                            onChange={handleChange}
                            type="datetime-local"
                            disabled={true}
                            isRequired
                        />
                    </Grid>
                </Box>
            </Grid>
        </form>
    );
}

export default MainProductForm;