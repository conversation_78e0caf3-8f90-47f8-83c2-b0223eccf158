const express = require('express');
const router = express.Router();

app.post('/soap-service', async (req, res) => {
    try {
      const quotationData = req.body; // Assuming the quotation data is sent in the request body
      const { createdQuotation, soapResponse } = await createQuotation(quotationData);
      res.status(200).json({ success: true, data: { createdQuotation, soapResponse } });
    } catch (error) {
      console.error('SOAP Service Error:', error.message);
      res.status(500).json({ success: false, message: error.message });
    }
});

module.exports = router;