import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { TextField, FormControl, InputLabel, Select, MenuItem, Checkbox, ListItemText, Typography } from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { toast } from 'react-toastify';
import CustomTextField from '../../../components/CustomTextField';
import CustomFileUpload from '../../../components/CustomFileUpload';
import CustomSection from '../../../components/CustomSection';
import Dropdown from '../../../components/table/DropDown';
import ModuleName from '../../../components/table/ModuleName';
import { FormHelperText } from '@mui/material';
import MultiSelectDropdown from '../../../components/table/MultiSelectDropdown';
import CircularProgress from '@mui/material/CircularProgress';
import {
    getAllRoles,
    fetchAllImfBranches,
    fetchEmployeeData,
    createEmployeeInfo,
    fetchEmployeeById,
    updateEmployeeInfo,
    getAllPickLists,
    getRoleById,
    getAllAgentDetails
} from '../../../redux/actions/action';
import axios from 'axios';
const Employee_Personal_Information = () => {

    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const isDisabled = location.state?.isDisabled || false;
    const [loading, setLoading] = useState(false); // Add loading state
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { id } = useParams();
    const [existingAgentEmails, setExistingAgentEmails] = useState([]); // State to hold existing agent email addresses

    const [previousFilePaths, setPreviousFilePaths] = useState({
        photo: null,
        aadhaar: null,
        pan: null,
        offerLetter: null,
        drivingLicense: null,
    });
    const [photo, setPhoto] = useState(null);
    const [aadhaarFrontFile, setAadhaarFrontFile] = useState(null);
    const [aadhaarBackFile, setAadhaarBackFile] = useState(null);
    const [panFile, setPanFile] = useState(null);
    const [offerLetterFile, setOfferLetterFile] = useState(null);
    const [drivingLicenseFile, setDrivingLicenseFile] = useState(null);
    const [fileKey, setFileKey] = useState(Date.now()); // Unique key
    const [resetFiles, setResetFiles] = useState(false);
    const isEditMode = !!id;
    const [error, setError] = useState('');

    const [dob, setDob] = useState(null);
    const [dateOfJoining, setDateOfJoining] = useState(null);
    const minAgeDate = dayjs().subtract(18, 'year');
    const today = dayjs();


    const handleDateOfBirthChange = (newValue) => {
        setDob(newValue);

        const validDate = dayjs.isDayjs(newValue) ? newValue : null;
        setDob(validDate);
        // Update formData with selected date
        setFormData((prevData) => ({
            ...prevData,
            //date_of_birth: validDate ? validDate.format('YYYY-MM-DD') : null,
            date_of_birth: newValue ? newValue.format('YYYY-MM-DD') : null,
        }));
        // Clear the error for DOB if a valid option is selected
        // if (validDate) {
        if (newValue) {
            setError((prev) => ({ ...prev, date_of_birth: null }));
        }
    };

    // Handle change for Date of Joining
    const handleDateChange = (newValue) => {
        setDateOfJoining(newValue);
        // Update formData with selected date
        setFormData((prevData) => ({
            ...prevData,
            date_of_joining: newValue ? newValue.format('YYYY-MM-DD') : null,
        }));
        // Clear the error for Date of Joining if a valid option is selected
        if (newValue) {
            setError((prev) => ({ ...prev, date_of_joining: null }));
        }

    };
    const handleMarriageDateChange = (newValue) => {
        setMarriageDate(newValue);
        setFormData((prevData) => ({
            ...prevData,
            marriage_date: newValue ? newValue.format('YYYY-MM-DD') : null,
        }));
        if (newValue) {
            setError((prev) => ({ ...prev, marriage_date: null }));
        }
    };

    // State for the selected first and second reporting managers
    const [firstManager, setFirstManager] = useState(null);
    const [secondManager, setSecondManager] = useState(null);
    const [showPassword, setShowPassword] = useState(false);
    const [selectedBranchNames, setSelectedBranchNames] = useState([]);
    const [formData, setFormData] = useState({
        employee_full_name: '',
        gender: '',
        education: '',
        adhar_number: '',
        PAN_number: '',
        personal_email: '',
        personal_mobile: '',
        date_of_birth: null,
        blood_group: '',
        marital_status: '',
        driving_license_number: '',
        user_id: '',
        role_id: '',
        branch_id: [],
        first_reporting_manager_id: '',
        second_reporting_manager_id: '',
        official_email: '',
        official_mobile: '',
        date_of_joining: '',
        emp_photo: null,
        emp_adhar_front_pdf: null,
        emp_adhar_back_pdf: null,
        emp_PAN_pdf: null,
        emp_signed_offer_letter_pdf: null,
        emp_driving_license_pdf: null,
        marriage_date: null, // Add marriage date to formData
    });
    const [formErrors, setFormErrors] = useState({ ...formData });
    const [marriageDate, setMarriageDate] = useState(null); // Add state for marriage date

    const GenderData = useSelector((state) => state.pickListReducer.genderOptions);
    const EducationData = useSelector((state) => state.pickListReducer.educationOptions);
    const bloodGroupData = useSelector((state) => state.pickListReducer.bloodGroupOptions);
    const maritalStatusData = useSelector((state) => state.pickListReducer.maritalStatusOptions);
    const roles = useSelector(state => state.roleManagementReducer.roles);
    const role = useSelector(state => state.roleManagementReducer.role);
    const imfBranchData = useSelector((state) => state.imfBranchReducer.data);
    const employeeInfo = useSelector((state) => state.employeeInfoReducer.employees);
    const employeeDetails = useSelector((state) => state.employeeInfoReducer.employeeDetail);
    const [existingPans, setExistingPans] = useState([]); // State to hold existing PAN numbers
    const [existingAadhaarNumbers, setExistingAadhaarNumbers] = useState([]); // State to hold existing Aadhaar numbers
    const [existingEmails, setExistingEmails] = useState([]); // State to hold existing email addresses
    const [existingMobileNumbers, setExistingMobileNumbers] = useState([]); // State to hold existing mobile numbers

    // Get employee data from the Redux store
    const employees = useSelector((state) => state.employeeInfoReducer.employees);

    const [roleId, setRoleId] = useState(''); // Add state for selected role ID

    useEffect(() => {
        const initializePage = async () => {
            try {
                // Fetch basic data needed for both create and edit modes
                await Promise.all([
                    dispatch(getAllRoles()),
                    dispatch(fetchAllImfBranches()),
                    dispatch(getAllPickLists())
                ]);

                if (id) {
                    // Edit mode - fetch specific employee data
                    setLoading(true);
                    await dispatch(fetchEmployeeById(id));
                } else {
                    // Create mode - fetch all employees for validation
                    await dispatch(fetchEmployeeData());
                }
            } catch (error) {
                console.error('Error initializing page:', error);
                toast.error('Failed to load required data');
            } finally {
                setLoading(false);
            }
        };

        initializePage();
    }, [dispatch, id]); // Only depend on dispatch and id

    useEffect(() => {
        if (id && employeeDetails) {

            const birthDate = employeeDetails.date_of_birth ? dayjs(employeeDetails.date_of_birth) : null;
            const joiningDate = employeeDetails.date_of_joining ? dayjs(employeeDetails.date_of_joining) : null;
            const marriageDate = employeeDetails.marriage_date ? dayjs(employeeDetails.marriage_date) : null; // Parse date

            // Set marital status as a number
            const maritalStatus = Number(employeeDetails.marital_status); // Convert to number

            // Set form data
            setFormData((prevData) => ({
                ...prevData,
                marital_status: maritalStatus, // Store as a number
                marriage_date: marriageDate ? marriageDate.format('YYYY-MM-DD') : null, // Update formData
                employee_full_name: employeeDetails.employee_full_name || '',
                gender: employeeDetails.gender ? employeeDetails.gender.charAt(0).toUpperCase() + employeeDetails.gender.slice(1).toLowerCase() : '',
                education: employeeDetails.education || '',
                adhar_number: employeeDetails.adhar_number || '',
                PAN_number: employeeDetails.PAN_number || '',
                personal_email: employeeDetails.personal_email || '',
                personal_mobile: employeeDetails.personal_mobile || '',
                date_of_birth: birthDate,
                user_id: employeeDetails.user_id || "",
                blood_group: employeeDetails.blood_group || '',
                driving_license_number: employeeDetails.driving_license_number || '',
                role_id: employeeDetails.role_id || '',
                branch_id: branchNames, // Ensure this is set correctly
                first_reporting_manager_id: employeeDetails.first_reporting_manager_id || '', // Set to '' if null
                second_reporting_manager_id: employeeDetails.second_reporting_manager_id || '', // Set to '' if null
                official_email: employeeDetails.official_email || '',
                official_mobile: employeeDetails.official_mobile || '',
                date_of_joining: joiningDate,
            }));

            // Additional logic for branch names, DOB, and joining date
            const branchIds = employeeDetails.branch_id ? employeeDetails.branch_id.split(',') : [];
            const branchNames = imfBranchData
                .filter(branch => branchIds.includes(String(branch.id))) // Convert branch.id to string for comparison
                .map(branch => branch.branch_name); // Assuming branch.branch_name is the name

            setSelectedBranchNames(branchNames); // Set selected branch names
            setDob(birthDate);
            setDateOfJoining(joiningDate);
            setFirstManager(employeeDetails.first_reporting_manager_id || ''); // Set to '' if null
            setSecondManager(employeeDetails.second_reporting_manager_id || ''); // Set to '' if null
        }
    }, [id, employeeDetails]);

    // Fetch employee data and existing PAN numbers
    useEffect(() => {
        const fetchData = async () => {
            await dispatch(fetchEmployeeData()); // Fetch employee data
        };
        fetchData();
    }, [dispatch]);

    useEffect(() => {
        if (employees.length > 0) {
            const pans = employees.map(employee => employee.PAN_number); // Extract PAN numbers
            setExistingPans(pans); // Set existing PAN numbers
        }
    }, [employees]); // Run this effect whenever employees change

    // New useEffect for Aadhaar numbers
    useEffect(() => {
        if (employees.length > 0) {
            const aadhaars = employees.map(employee => employee.adhar_number); // Extract Aadhaar numbers
            setExistingAadhaarNumbers(aadhaars); // Set existing Aadhaar numbers
        }
    }, [employees]); // Run this effect whenever employees change

    // New useEffect for email addresses
    useEffect(() => {
        if (employees.length > 0) {
            const emails = employees.map(employee => employee.personal_email); // Extract email addresses
            setExistingEmails(emails); // Set existing email addresses
        }
    }, [employees]); // Run this effect whenever employees change

    // New useEffect for mobile numbers
    useEffect(() => {
        if (employees.length > 0) {
            const mobiles = employees.map(employee => employee.personal_mobile); // Extract mobile numbers
            setExistingMobileNumbers(mobiles); // Set existing mobile numbers
        }
    }, [employees]); // Run this effect whenever employees change

    // Function to check if PAN number already exists
    const checkPanExists = (panNumber) => {
        return existingPans.includes(panNumber);
    };


    // Modify handleCancel to force reset of file states
    const handleCancel = () => {
        resetAllFileStates();
        navigate('/dashboard/employee-Master');
    };

    function extractFileName(filePath) {
        if (!filePath) return ''; // Return empty string for null/undefined paths
        try {
            const parts = filePath?.split(/[/\\]/);
            const fileName = parts[parts.length - 1];
            return fileName.replace(/^\d+-/, '');
        } catch (error) {
            console.warn('Error extracting filename:', error);
            return '';
        }
    }

    // Update the file name variables with null checks
    const existingPhoto = employeeDetails?.emp_photo ? extractFileName(employeeDetails.emp_photo) : '';
    const existingadharfrontfile = employeeDetails?.emp_adhar_front_pdf ? extractFileName(employeeDetails.emp_adhar_front_pdf) : '';
    const existingadharbackfile = employeeDetails?.emp_adhar_back_pdf ? extractFileName(employeeDetails.emp_adhar_back_pdf) : '';
    const existingPanfile = employeeDetails?.emp_PAN_pdf ? extractFileName(employeeDetails.emp_PAN_pdf) : '';
    const existingOfferLetterfile = employeeDetails?.emp_signed_offer_letter_pdf ? extractFileName(employeeDetails.emp_signed_offer_letter_pdf) : '';
    const existingDrivingLicensefile = employeeDetails?.emp_driving_license_pdf ? extractFileName(employeeDetails.emp_driving_license_pdf) : '';

    const getErrorKey = (fileType) => {
        switch (fileType) {
            case 'photo':
                return 'emp_photo';
            case 'aadhaarFront':
                return 'emp_adhar_front_pdf';
            case 'aadhaarBack':
                return 'emp_adhar_back_pdf';
            case 'pan':
                return 'emp_PAN_pdf';
            case 'offerLetter':
                return 'emp_signed_offer_letter_pdf';
            case 'drivingLicense':
                return 'emp_driving_license_pdf';
            default:
                return '';
        }
    }
    // Modify handleFileSelect to handle null values properly
    const handleFileSelect = (file, fileType) => {
        if (file && file.size > 10 * 1024 * 1024) {
            setError((prevErrors) => ({
                ...prevErrors,
                [getErrorKey(fileType)]: 'File size must not exceed 10MB', // Set error message
            }));
            return; // Exit the function if file size is too large
        } else {
            setError((prevErrors) => ({
                ...prevErrors,
                [getErrorKey(fileType)]: null, // Clear error if file size is valid
            }));
        }
        switch (fileType) {
            case 'photo':
                setPhoto(file);
                setError((prevErrors) => ({ ...prevErrors, emp_photo: null })); // Clear error for photo
                break;
            case 'aadhaarFront':
                setAadhaarFrontFile(file);
                setError((prevErrors) => ({ ...prevErrors, emp_adhar_front_pdf: null })); // Clear error for Aadhaar front
                break;
            case 'aadhaarBack':
                setAadhaarBackFile(file);
                setError((prevErrors) => ({ ...prevErrors, emp_adhar_back_pdf: null })); // Clear error for Aadhaar back
                break;
            case 'pan':
                setPanFile(file);
                setError((prevErrors) => ({ ...prevErrors, emp_PAN_pdf: null })); // Clear error for PAN
                break;
            case 'offerLetter':
                setOfferLetterFile(file);
                setError((prevErrors) => ({ ...prevErrors, emp_signed_offer_letter_pdf: null })); // Clear error for offer letter
                break;
            case 'drivingLicense':
                setDrivingLicenseFile(file);
                setError((prevErrors) => ({ ...prevErrors, emp_driving_license_pdf: null })); // Clear error for driving license
                break;
            default:
                break;
        }
    };

    const genderOptions = GenderData.map((gender) => ({
        value: gender.id,
        label: gender.label_name

    }));

    const educationOptions = EducationData.map((education) => ({
        value: education.id,
        label: education.label_name
    }))

    const blood_groupOptions = bloodGroupData.map((bloodGroup) => ({
        value: bloodGroup.id,
        label: bloodGroup.label_name
    }))
    const maritalOptions = maritalStatusData.map((maritalStatus) => ({
        value: maritalStatus.id,
        label: maritalStatus.label_name
    }))

    // Modify roleOptions to include active roles plus the current role if in edit mode
    const roleOptions = roles
        .filter(role => {
            if (isEditMode && role.id === formData.role_id) {
                // Include the current role in edit mode regardless of status
                return true;
            }
            // For all other roles, only include if active
            return role.status === 1;
        })
        .map((role) => ({
            value: role.id,
            label: role.role_name
        }));

    // Modify the first_reporting_manager_Options to only include active employees
    const first_reporting_manager_Options = employeeInfo
        .filter(employee => employee.status === 1) // Only include active employees
        .map((employee) => ({
            value: employee.id,
            label: employee.employee_full_name,
        }));

    // Filter to exclude the first manager from the second manager dropdown
    // and only include active employees
    const filteredSecondManagerOptions = first_reporting_manager_Options.filter(
        (option) => option.value !== firstManager
    );

    const handleFirstManagerChange = (event) => {
        const value = event.target.value;
        setFirstManager(value);

        // Update formData directly here
        setFormData((prevData) => ({
            ...prevData,
            first_reporting_manager_id: value,
        }));

        // Clear the error for first_reporting_manager_id if a valid option is selected
        if (value) {
            setError((prevErrors) => ({
                ...prevErrors,
                first_reporting_manager_id: null,
            }));
        }

        // Optionally reset the second manager when the first manager changes
        if (secondManager === value) {
            setSecondManager(null);
            setError((prevErrors) => ({
                ...prevErrors,
                second_reporting_manager_id: null,
            }));
        }
    };

    const handleSecondManagerChange = (event) => {
        const value = event.target.value;
        setSecondManager(value);

        // Update formData directly here
        setFormData((prevData) => ({
            ...prevData,
            second_reporting_manager_id: value,
        }));

        // Clear the error for second_reporting_manager_id if a valid option is selected
        if (value) {
            setError((prevErrors) => ({
                ...prevErrors,
                second_reporting_manager_id: null,
            }));
        }
    };

    // Adjusting the handleBranchChange to reflect the correct branch names and IDs
    const handleBranchChange = (event) => {
        const { value } = event.target;
        const selectedValues = Array.isArray(value) ? value : [];

        setSelectedBranchNames(selectedValues);
        setFormData(prev => ({
            ...prev,
            branch_id: selectedValues, // Assuming branch_names are stored in branch_id for this example
        }));

        if (selectedValues.length > 0) {
            setError(prevErrors => ({
                ...prevErrors,
                branch_id: null,
            }));
        }
    };
    const validateAadhaar = (aadhaarNumber) => {
        const aadhaarPattern = /^\d{4}-\d{4}-\d{4}$/; // Matches the format 1234-5678-9012
        if (!aadhaarPattern.test(aadhaarNumber)) {
            return 'Aadhaar number must be in the format 1234-5678-9012.';
        }
        // Check for uniqueness if not in edit mode
        if (!isEditMode && existingAadhaarNumbers.includes(aadhaarNumber)) {
            return 'Aadhaar number already exists.';
        }
        return '';
    };

    const validatePAN = (panNumber, existingPans = []) => {
        const panPattern = /^[A-Z]{5}[0-9]{4}[A-Z]$/; // Matches the format **********

        // Check if the PAN number is in the correct format
        if (!panPattern.test(panNumber)) {
            return 'PAN number must be in the format **********.';
        }

        // Check if the PAN number already exists only if not in edit mode
        if (!isEditMode && existingPans.includes(panNumber)) {
            return 'PAN number already exists.';
        }

        return ''; // No error
    };
    const validateMobile = (mobileNumber) => {
        const mobilePattern = /^[789]\d{9}$/; // Matches a 10-digit number starting with 7, 8, or 9
        if (!mobilePattern.test(mobileNumber)) {
            return 'Mobile number must be a 10-digit number starting with 7, 8, or 9.';
        }
        // Check for uniqueness if not in edit mode
        if (!isEditMode && existingMobileNumbers.includes(mobileNumber)) {
            return 'Mobile number already exists.';
        }
        return '';
    };
    useEffect(() => {
        const fetchAgentEmails = async () => {
            const response = await dispatch(getAllAgentDetails()); // Fetch agents
            if (Array.isArray(response.payload)) { // Check if payload is an array
                const agentEmails = response.payload.map(agent => agent.personal_email); // Extract emails
                setExistingAgentEmails(agentEmails); // Set existing agent emails
            } else {
                console.error("Expected agents to be an array, but got:", response);
                setExistingAgentEmails([]); // Reset to empty array if not an array
            }
        };
        fetchAgentEmails();
    }, [dispatch]);
    const validateEmail = (email) => {
        // const emailPattern = /^[a-z0-9]+(\.[a-z0-9]+)*@[a-z0-9-]+(\.[a-z]{2,})+$/;
        // const emailPattern = /^[a-zA-Z0-9._+]+@[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+$/;
        const emailPattern = /^[a-z0-9._]+@[a-z0-9-]+(\.[a-z]{2,})+$/;

        if (!emailPattern.test(email)) {
            return 'Invalid email format. Please enter a valid email address';
        }
        // Check for uniqueness if not in edit mode
        if (!isEditMode && (existingEmails.includes(email) || existingAgentEmails.includes(email))) {
            return 'Email already exists.';
        }
        return '';
    };

    // Update the maskValue function
    const maskValue = (value, type) => {
        if (!value) return '';

        if (type === 'pan') {
            // Show first letter and last 2 characters of PAN (A*******4F)
            return value.replace(/^(.)(.+)(..)$/, '$1*******$3');
        } else if (type === 'aadhaar') {
            // Show only first digit and last 4 digits of Aadhaar (1***-****-9012)
            return value.replace(/^(\d)(?=[\d-]*)([\d-]+)(\d{4})$/, '****-****-$3');
        }
        return value;
    };
    // Add this function for date masking
    const formatDateDisplay = (date) => {
        if (!date) return '';
        return `**/**/${date.format('YYYY')}`;
    };

    const handleChange = (event) => {
        const { name, value } = event.target;
        let formattedValue = value;

        // Check for marital status change
        if (name === 'marital_status') {
            const maritalStatusValue = Number(value); // Convert to number
            setFormData((prevData) => ({
                ...prevData,
                marital_status: maritalStatusValue,
            }));

            // If marital status is changed to "5" (Married), set the marriage date
            if (maritalStatusValue === 5) {
                const currentDate = dayjs(); // Get the current date
                setMarriageDate(null); // Set marriage date to current date
                setFormData((prevData) => ({
                    ...prevData,
                    marriage_date: null, // Also clear in formData
                }));
            }
        }
        // Clear error for required fields when valid data is added
        if (['role_id', 'gender', 'education', 'blood_group', 'marital_status'].includes(name)) {
            setError((prevErrors) => ({
                ...prevErrors,
                [name]: null, // Clear the error for the specific field
            }));
        }

        // Convert Employee Full Name to uppercase and validate
        if (name === 'employee_full_name') {
            // Remove any non-alphabetic characters and convert to uppercase
            formattedValue = value.replace(/[^A-Za-z\s]/g, "").toUpperCase();

            // Validate: only alphabets and spaces allowed
            if (!/^[A-Z\s]*$/.test(formattedValue)) {
                setError((prevErrors) => ({
                    ...prevErrors,
                    employee_full_name: "Only alphabets are allowed.",
                }));
            } else {
                // Clear the error if valid
                setError((prevErrors) => ({
                    ...prevErrors,
                    employee_full_name: null,
                }));
            }
        }
        // Format Aadhaar number if the current field is 'adhar_number'
        if (name === 'adhar_number' && !isEditMode) {
            formattedValue = value.replace(/\D/g, ""); // Remove any non-digit characters

            // Limit the number of digits to 12
            if (formattedValue.length > 12) {
                formattedValue = formattedValue.slice(0, 12);
            }

            // Format to add hyphens after every 4 digits
            formattedValue = formattedValue.replace(/(.{4})/g, "$1-");

            // Remove the trailing hyphen if present
            if (formattedValue.endsWith("-")) {
                formattedValue = formattedValue.slice(0, -1);
            }
        }

        // Convert PAN number to uppercase and limit its length
        if (name === 'PAN_number' && !isEditMode) {
            formattedValue = value.toUpperCase(); // Convert to uppercase
            // Limit to 10 characters
            if (formattedValue.length > 10) {
                formattedValue = formattedValue.slice(0, 10);
            }

        }

        // Format mobile numbers to only accept up to 10 digits for both personal and official mobile fields
        if (name === 'personal_mobile' || name === 'official_mobile') {
            formattedValue = value.replace(/\D/g, ""); // Remove non-digit characters

            // Limit to 10 digits
            if (formattedValue.length > 10) {
                formattedValue = formattedValue.slice(0, 10);
            }
        }

        if (name === 'personal_email') {
            formattedValue = value.toLowerCase();
        }
        if (name === 'official_email') {
            formattedValue = value.toLowerCase();
        }

        // Check for PAN uniqueness
        if (name === 'PAN_number') {
            if (checkPanExists(formattedValue)) {
                setError((prevErrors) => ({
                    ...prevErrors,
                    PAN_number: 'PAN number already exists.',
                }));
            } else {
                setError((prevErrors) => ({
                    ...prevErrors,
                    PAN_number: null, // Clear error if PAN is unique
                }));
            }
        }

        // Update the form data with formattedValue for adhar_number and mobile numbers
        setFormData((prevData) => ({
            ...prevData,
            [name]: formattedValue,
        }));

        // Initialize a new error state based on previous errors
        setError((prevErrors) => {
            const newErrors = { ...prevErrors };

            // Validate Aadhaar number
            if (name === 'adhar_number') {
                newErrors.adhar_number = validateAadhaar(formattedValue);
            }
            // Validate PAN number
            else if (name === 'PAN_number') {
                newErrors.PAN_number = validatePAN(formattedValue, existingPans);
            }
            // Validate personal mobile number
            else if (name === 'personal_mobile') {
                newErrors.personal_mobile = validateMobile(formattedValue);
            }
            // Validate official mobile number
            else if (name === 'official_mobile') {
                newErrors.official_mobile = validateMobile(formattedValue);
            }
            // Validate personal email
            else if (name === 'personal_email') {
                newErrors.personal_email = validateEmail(value);
            }
            // Validate official email
            else if (name === 'official_email') {
                newErrors.official_email = validateEmail(value);
            }
            // Clear error for other fields
            // else {
            //   newErrors[name] = ''; // Clear error for the specific field
            // }

            return newErrors; // Return updated errors state
        });
    };

    const validateForm = () => {
        const newErrors = {};

        if (!formData.employee_full_name?.trim()) {
            newErrors.employee_full_name = 'Employee name is required';
        }
        // if (formData.marital_status === 5 && !marriageDate) {
        //     newErrors.marriage_date = 'Marriage date is required.';
        // }


        if (!formData.gender) {
            newErrors.gender = 'Gender is required';
        }

        if (!formData.education) {
            newErrors.education = 'Education is required';
        }

        // Modify Aadhaar validation to skip in edit mode
        if (!isEditMode) {
            if (!formData.adhar_number) {
                newErrors.adhar_number = 'Aadhaar number is required';
            } else if (!/^\d{4}-\d{4}-\d{4}$/.test(formData.adhar_number)) {
                newErrors.adhar_number = 'Invalid Aadhaar format';
            }
        }

        // Modify PAN validation to skip in edit mode
        if (!isEditMode) {
            if (!formData.PAN_number) {
                newErrors.PAN_number = 'PAN number is required';
            } else if (!/^[A-Z]{5}[0-9]{4}[A-Z]$/.test(formData.PAN_number)) {
                newErrors.PAN_number = 'Invalid PAN format';
            }
        }

        if (!formData.personal_email) {
            newErrors.personal_email = 'Personal email is required';
        } else if (!/^[a-z0-9._]+@[a-z0-9-]+(\.[a-z]{2,})+$/.test(formData.personal_email)) {
            newErrors.personal_email = 'Invalid email format';
        }

        if (!formData.personal_mobile) {
            newErrors.personal_mobile = 'Personal mobile is required';
        } else if (!/^[789]\d{9}$/.test(formData.personal_mobile)) {
            newErrors.personal_mobile = 'Invalid mobile number';
        }

        if (!dob) {
            newErrors.date_of_birth = 'Date of birth is required';
        }

        if (!formData.blood_group) {
            newErrors.blood_group = 'Blood group is required';
        }

        if (!formData.marital_status) {
            newErrors.marital_status = 'Marital status is required';
        }

        if (!formData.role_id) {
            newErrors.role_id = 'Role is required';
        }

        if (!selectedBranchNames.length) {
            newErrors.branch_id = 'At least one branch must be selected';
        }

        if (!firstManager) {
            newErrors.first_reporting_manager_id = 'First reporting manager is required';
        }

        if (!secondManager) {
            newErrors.second_reporting_manager_id = 'Second reporting manager is required';
        }

        if (!dateOfJoining) {
            newErrors.date_of_joining = 'Date of joining is required';
        }

        // File validations for new entries (not edit mode)
        if (!isEditMode) {
            if (!photo && !previousFilePaths.photo) {
                newErrors.emp_photo = 'Employee photo is required';
            }
            if (!aadhaarFrontFile && !previousFilePaths.aadhaarFront) {
                newErrors.emp_adhar_front_pdf = 'Aadhaar front document is required';
            }
            if (!aadhaarBackFile && !previousFilePaths.aadhaarBack) {
                newErrors.emp_adhar_back_pdf = 'Aadhaar back document is required';
            }
            if (!panFile && !previousFilePaths.pan) {
                newErrors.emp_PAN_pdf = 'PAN document is required';
            }
            // if (!offerLetterFile && !previousFilePaths.offerLetter) {
            //   newErrors.emp_signed_offer_letter_pdf = 'Offer letter is required';
            // }
            // if (!drivingLicenseFile && !previousFilePaths.drivingLicense) {
            //   newErrors.emp_driving_license_pdf = 'Driving license is required';
            // }


        }

        // Optional field validations
        if (formData.official_email && !/^[a-z0-9]+(\.[a-z0-9]+)*@[a-z0-9-]+(\.[a-z]{2,})+$/.test(formData.official_email)) {
            newErrors.official_email = 'Invalid email format';
        }

        if (formData.official_mobile && !/^[789]\d{9}$/.test(formData.official_mobile)) {
            newErrors.official_mobile = 'Invalid mobile number';
        }

        return newErrors;
    };

    const generateUserId = async (selectedBranches, roleId, employeeInfo) => {
        if (!selectedBranches || selectedBranches.length === 0) {
            throw new Error("No branch selected.");
        }

        const branch_name = selectedBranches[0]; // Get the first branch name
        const branchPrefix = branch_name.slice(0, 3).toUpperCase(); // First 3 letters of the branch name
        const prefixData = "PWS-";
        const prefixEmp = "-EMP";

        // Fetch the role name from the roles array
        const role = roles.find(role => role.id === roleId);
        if (!role || !role.role_name) {
            throw new Error("Invalid role ID or role not found.");
        }

        const isLeader = role.role_name.toLowerCase() === 'leader';

        let maxEmployeeNumericPart = 0; // Track the highest numeric part for employees
        let maxLeaderNumericPart = 0; // Track the highest numeric part for leaders

        // Iterate through employeeInfo to find the highest numeric parts
        employeeInfo.forEach(employee => {
            const userId = employee.user_id;
            if (userId.startsWith('LD')) {
                // Extract numeric part for leader IDs
                const numericPart = parseInt(userId.slice(2), 10);
                if (!isNaN(numericPart) && numericPart > maxLeaderNumericPart) {
                    maxLeaderNumericPart = numericPart;
                }
            } else if (userId.startsWith(prefixData)) {
                // Extract numeric part for employee IDs
                const numericPart = parseInt(userId.split(prefixEmp)[1], 10);
                if (!isNaN(numericPart) && numericPart > maxEmployeeNumericPart) {
                    maxEmployeeNumericPart = numericPart;
                }
            }
        });

        // Determine the new numeric part based on the highest existing parts
        let newNumericPart;
        if (isLeader) {
            newNumericPart = maxLeaderNumericPart + 1; // Increment for leader
            return `LD${String(newNumericPart).padStart(4, '0')}`; // Leader ID format
        } else {
            newNumericPart = maxEmployeeNumericPart + 1; // Increment for employee
            return `${prefixData}${branchPrefix}${prefixEmp}${String(newNumericPart).padStart(3, '0')}`; // Employee ID format
        }
    };
    // Update the handleSubmit function to properly handle file updates
    const handleSubmit = async (event) => {
        event.preventDefault();
        //  setIsSubmitting(true);
        const fileSizeErrors = {};
        const fileSizeLimit = 10 * 1024 * 1024; // 10 MB in bytes

        if (photo && photo.size > fileSizeLimit) {
            fileSizeErrors.emp_photo = 'File size must not exceed 10MB';
        }
        if (aadhaarFrontFile && aadhaarFrontFile.size > fileSizeLimit) {
            fileSizeErrors.emp_adhar_front_pdf = 'File size must not exceed 10MB';
        }
        if (aadhaarBackFile && aadhaarBackFile.size > fileSizeLimit) {
            fileSizeErrors.emp_adhar_back_pdf = 'File size must not exceed 10MB';
        }
        if (panFile && panFile.size > fileSizeLimit) {
            fileSizeErrors.emp_PAN_pdf = 'File size must not exceed 10MB';
        }
        if (offerLetterFile && offerLetterFile.size > fileSizeLimit) {
            fileSizeErrors.emp_signed_offer_letter_pdf = 'File size must not exceed 10MB';
        }
        if (drivingLicenseFile && drivingLicenseFile.size > fileSizeLimit) {
            fileSizeErrors.emp_driving_license_pdf = 'File size must not exceed 10MB';
        }

        // Combine file size errors with existing validation errors
        const validationErrors = validateForm();
        const combinedErrors = { ...validationErrors, ...fileSizeErrors };

        if (Object.keys(combinedErrors).length > 0) {
            setError(combinedErrors);
            toast.error('Please fill all required fields correctly and ensure file sizes are within limits');
            return;
        }
        setIsSubmitting(true);
        const emailError = validateEmail(formData.personal_email);
        if (emailError) {
            setError((prevErrors) => ({
                ...prevErrors,
                personal_email: emailError,
            }));
            toast.error(emailError);
            return;
        }
        // Create FormData object
        const formDataToSend = new FormData();
        let userId; // Declare userId variable
        if (!isEditMode) { // Generate user ID only if not in edit mode
            userId = await generateUserId(formData.branch_id, formData.role_id, employeeInfo);
            //formDataToSend.append('user_id', userId); // Append the generated user ID
        }
        // Append basic form fields
        Object.entries({
            employee_full_name: formData.employee_full_name.trim(),
            gender: formData.gender,
            education: formData.education,
            adhar_number: formData.adhar_number,
            PAN_number: formData.PAN_number,
            personal_email: formData.personal_email,
            personal_mobile: formData.personal_mobile,
            date_of_birth: dob ? dob.format('YYYY-MM-DD') : null,
            blood_group: formData.blood_group,
            marital_status: formData.marital_status,
            driving_license_number: formData.driving_license_number.trim(),
            user_id: isEditMode ? formData.user_id : userId,
            role_id: formData.role_id,
            branch_id: selectedBranchNames.map(branchName => {
                const branch = imfBranchData.find(b => b.branch_name === branchName);
                return branch ? branch.id : null; // Get the ID of the branch
            }).filter(id => id !== null).join(','), first_reporting_manager_id: firstManager,
            second_reporting_manager_id: secondManager,
            official_email: formData.official_email,
            official_mobile: formData.official_mobile,
            date_of_joining: dateOfJoining ? dateOfJoining.format('YYYY-MM-DD') : null,
            marriage_date: formData.marriage_date, // Add marriage date to formDataToSend
        }).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                formDataToSend.append(key, value);
            }
        });

        // Handle file uploads for both create and update
        const handleFileAppend = (file, existingPath, fieldName) => {
            if (file) {
                // New file selected
                formDataToSend.append(fieldName, file);
            } else if (isEditMode && existingPath) {
                // In edit mode with existing file path
                formDataToSend.append(fieldName, existingPath);
            }
        };


        // Append files using the helper function
        handleFileAppend(photo, previousFilePaths.photo, 'emp_photo');
        handleFileAppend(aadhaarFrontFile, previousFilePaths.aadhaarFront, 'emp_adhar_front_pdf');
        handleFileAppend(aadhaarBackFile, previousFilePaths.aadhaarBack, 'emp_adhar_back_pdf');
        handleFileAppend(panFile, previousFilePaths.pan, 'emp_PAN_pdf');
        handleFileAppend(offerLetterFile, previousFilePaths.offerLetter, 'emp_signed_offer_letter_pdf');
        handleFileAppend(drivingLicenseFile, previousFilePaths.drivingLicense, 'emp_driving_license_pdf');

        try {
            if (isEditMode) {
                await dispatch(updateEmployeeInfo({ id, data: formDataToSend })).unwrap();
                toast.success("Employee updated successfully!");
                navigate(`/dashboard/employee-Master`);

            } else {
                //get response from createEmployeeInfo
                const response = await dispatch(createEmployeeInfo(formDataToSend)).unwrap();
                //  await dispatch(createEmployeeInfo(formDataToSend)).unwrap();

                toast.success("Employee created successfully!");
                const employeeId = response?.id;
                if (employeeId) {
                    const finalId = Array.isArray(employeeId) ? employeeId[0] : employeeId;
                    navigate(`/dashboard/employee-address/${finalId}`);

                } else {
                    toast.error("Could not get employee ID");
                }
            }
            setError({});


        } catch (error) {
            console.error("Error during employee creation/update:", error); // Log the error
            toast.error(isEditMode ? "Failed to update employee" : "Failed to create employee");
        }
        finally {
            setIsSubmitting(false);
        }

    };

    // Modify the resetAllFileStates function to be more thorough
    const resetAllFileStates = () => {
        setPreviousFilePaths({
            photo: null,
            aadhaarFront: null,
            aadhaarBack: null,
            pan: null,
            offerLetter: null,
            drivingLicense: null,
        });
        setPhoto(null);
        setAadhaarFrontFile(null);
        setAadhaarBackFile(null);
        setPanFile(null);
        setOfferLetterFile(null);
        setDrivingLicenseFile(null);
        setFileKey(Date.now());
        setResetFiles(true); // Add this line to trigger reset in CustomFileUpload

        // Reset form data including file-related fields
        setFormData(prev => ({
            ...prev,
            emp_photo: null,
            emp_adhar_front_pdf: null,
            emp_adhar_back_pdf: null,
            emp_PAN_pdf: null,
            emp_signed_offer_letter_pdf: null,
            emp_driving_license_pdf: null,
        }));

        // Reset other form-related states
        setDob(null);
        setDateOfJoining(null);
        setFirstManager(null);
        setSecondManager(null);
        setSelectedBranchNames([]);
        setError({});
    };

    // Add useEffect to handle resetFiles state
    useEffect(() => {
        if (resetFiles) {
            // Reset the resetFiles flag after a brief delay
            const timer = setTimeout(() => {
                setResetFiles(false);
            }, 100);
            return () => clearTimeout(timer);
        }
    }, [resetFiles]);

    // Modify the useEffect for handling mode changes
    useEffect(() => {
        if (!id) { // If we're in "new" mode
            resetAllFileStates();
        } else if (id && employeeDetails) { // If we're in "edit" mode
            setPreviousFilePaths({
                photo: employeeDetails?.emp_photo || null,
                aadhaarFront: employeeDetails?.emp_adhar_front_pdf || null,
                aadhaarBack: employeeDetails?.emp_adhar_back_pdf || null,
                pan: employeeDetails?.emp_PAN_pdf || null,
                offerLetter: employeeDetails?.emp_signed_offer_letter_pdf || null,
                drivingLicense: employeeDetails?.emp_driving_license_pdf || null,
            });
        }
    }, [id, employeeDetails]);

    // Add cleanup effect when component unmounts
    useEffect(() => {
        return () => {
            resetAllFileStates();
        };
    }, []);

    return (

        <Box sx={{ paddingLeft: '40px', paddingRight: '40px', paddingBottom: '40px' }}>
            {(loading || isSubmitting) && (
                <Box
                    sx={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)', // Semi-transparent background
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1000 // Ensure it is above other content
                    }}
                    aria-live="polite" // Accessibility feature
                >
                    <Box sx={{ textAlign: 'center' }}>
                        <CircularProgress /> {/* Show loading spinner */}
                        <Typography variant="h6" sx={{ marginTop: 2 }}>
                            {isSubmitting ? "Submitting..." : "Loading..."} {/* Custom message */}
                        </Typography>
                    </Box>
                </Box>
            )}
            <form encType="multipart/form-data">
                <Grid container spacing={2} style={{ display: 'flex', alignItems: 'center' }}>
                    {/* Header Row */}
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Employee" pageName={id ? employeeInfo?.status === 0 ? "View" : "Edit" : "Create"} />
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        {/* {!id && (
              <Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}>
                Save & New
              </Button>
            )} */}
                        {(!isDisabled && (employeeDetails?.status === 1 || !id)) && (
                            <Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleSubmit}>
                                Save
                            </Button>)}
                        <Button variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }} onClick={handleCancel}>
                            Cancel
                        </Button>
                    </Grid>
                    <Grid container >
                        <CustomSection titles={['Overview', 'Personal Details', 'Address']} page='employee' />
                    </Grid>

                    {/* Form Fields */}
                    {/* Row 1 */}
                    <Grid item xs={12} >
                        <Box
                            sx={{
                                //backgroundColor: '#f0f0f0',
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',
                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Personal Details</h5>
                            <div style={{ height: '100vh', display: 'flex', alignItems: 'center' }}>

                            </div>
                        </Box>
                        <hr></hr></Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Employee Full Name"
                            name="employee_full_name"
                            value={formData.employee_full_name}
                            onChange={handleChange}
                            helperText={error.employee_full_name}
                            // disabled={id && employeeDetails?.status === 0}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={3} >
                        <Dropdown
                            label="Gender"
                            name="gender"
                            required
                            onChange={handleChange}
                            options={genderOptions}
                            value={formData.gender}
                            helperText={error.gender}
                            fullWidth
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            required
                            label="Education"
                            name="education"
                            options={educationOptions}
                            onChange={handleChange}
                            value={formData.education}
                            helperText={error.education}
                            fullWidth
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Aadhaar Number (1234-5678-9012)"
                            name="adhar_number"
                            onChange={handleChange}
                            value={isEditMode ? maskValue(formData.adhar_number, 'aadhaar') : formData.adhar_number}
                            disabled={isEditMode} // Disable in edit mode
                            helperText={error.adhar_number} // Pass error status to CustomTextField if applicable
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                        {/* {error.adhar_number && <FormHelperText error>{error.adhar_number}</FormHelperText>} */}

                    </Grid>

                    {/* Row 2 */}
                    <Grid item xs={3}>
                        <CustomTextField
                            label="PAN Number (**********)"
                            name="PAN_number"
                            onChange={handleChange}
                            disabled={isEditMode} // Disable in edit mode
                            value={isEditMode ? maskValue(formData.PAN_number, 'pan') : formData.PAN_number}
                            helperText={error.PAN_number}// Pass error status to CustomTextField if applicable
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />

                        {/* {error.PAN_number && <FormHelperText error>{error.PAN_number}</FormHelperText>} */}
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Personal Email Id"
                            name="personal_email"

                            onChange={handleChange}
                            value={formData.personal_email}
                            helperText={error.personal_email}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>

                        <CustomTextField
                            label="Personal Mobile"
                            name="personal_mobile"
                            onChange={handleChange}
                            value={formData.personal_mobile}
                            type="tel"
                            helperText={error.personal_mobile}// Pass error status
                            applyPrefix

                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                        {/* {error.personal_mobile && <FormHelperText error>{error.personal_mobile}</FormHelperText>} */}

                    </Grid>
                    <Grid item xs={3}>
                        <FormControl fullWidth required error={Boolean(error.date_of_birth)}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                {isEditMode ? (
                                    <TextField
                                        label="DOB"
                                        value={dob ? `**/**/${dob.format('YYYY')}` : ''}
                                        disabled
                                        fullWidth
                                        error={Boolean(error.date_of_birth)}
                                        helperText={error.date_of_birth}
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            },
                                        }}
                                    />
                                ) : (
                                    <DatePicker
                                        label="DOB"
                                        value={dob}
                                        onChange={handleDateOfBirthChange}
                                        maxDate={minAgeDate}
                                        disabled={isDisabled || (id && employeeDetails?.status === 0)}
                                        format="DD/MM/YYYY"
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                // required: true,
                                                error: Boolean(error.date_of_birth),
                                                helperText: error.date_of_birth,
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                )}
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>

                    <Grid item xs={3}>
                        <Dropdown
                            required
                            label="Blood Group"
                            name="blood_group"
                            options={blood_groupOptions}
                            value={formData.blood_group}
                            onChange={handleChange}
                            helperText={error.blood_group}
                            fullWidth
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Marital Status"
                            name="marital_status"
                            options={maritalOptions}
                            onChange={handleChange}
                            value={formData.marital_status}
                            helperText={error.marital_status}
                            fullWidth
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            required
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    {formData.marital_status === 5 && ( // Ensure this condition is correct
                        <Grid item xs={3}>
                            <FormControl fullWidth required error={Boolean(error.marriage_date)}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="Marriage Date"
                                        name="marriage_date"
                                        value={marriageDate} // Convert to dayjs object for the DatePicker
                                        onChange={handleMarriageDateChange}
                                        maxDate={today} // Prevent future dates
                                        format="DD/MM/YYYY"
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                error: Boolean(error.marriage_date),
                                                helperText: error.marriage_date,
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            zIndex: 1,
                                                            //   backgroundColor: 'red',
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                    )}
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Driving License No"
                            name="driving_license_number"
                            onChange={handleChange}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            value={formData.driving_license_number}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        //backgroundColor: 'red',
                                    },
                                },
                            }}
                        />
                    </Grid>

                    {/* Row 3 */}
                    <Grid item xs={12}>

                        <Box
                            sx={{
                                //backgroundColor: '#f0f0f0',
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',
                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Employee Details</h5>
                        </Box>
                        <hr></hr>
                    </Grid>
                    {/* Row 4 */}
                    <Grid item xs={3}>
                        <Dropdown
                            label="Role"
                            required
                            name="role_id"
                            options={roleOptions}
                            onChange={handleChange}
                            value={formData.role_id}
                            helperText={error.role_id}
                            fullWidth
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <FormControl fullWidth required error={Boolean(error.branch_id)}>
                            <MultiSelectDropdown
                                label="Branch Name"
                                name="branch_id"

                                required
                                options={imfBranchData
                                    .filter(branch => {
                                        if (isEditMode && selectedBranchNames.includes(branch.branch_name)) {
                                            // Include currently assigned branches in edit mode
                                            return true;
                                        }
                                        // For all other branches, only include if active
                                        return branch.status === 1;
                                    })
                                    .map(branch => ({
                                        value: branch.branch_name,
                                        label: branch.branch_name
                                    }))}
                                value={Array.isArray(selectedBranchNames) ? selectedBranchNames : []}
                                onChange={handleBranchChange}
                                disabled={isDisabled || (id && employeeDetails?.status === 0)}
                                helperText={error.branch_id}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    }
                                }}
                            />
                        </FormControl>
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="First Reporting Manager"
                            name="first_reporting_manager_id"
                            required
                            value={firstManager}
                            onChange={handleFirstManagerChange}
                            options={first_reporting_manager_Options}
                            helperText={error.first_reporting_manager_id}
                            fullWidth
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            InputLabelProps={{ shrink: true }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <Dropdown
                            label="Second Reporting Manager"
                            name="second_reporting_manager_id"
                            required
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            value={secondManager}
                            onChange={handleSecondManagerChange}
                            options={filteredSecondManagerOptions} // Filtered options
                            helperText={error.second_reporting_manager_id}
                            fullWidth
                            InputLabelProps={{ shrink: true }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>
                        <CustomTextField
                            label="Official Email Id"
                            name="official_email"
                            onChange={handleChange}
                            value={formData.official_email}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            helperText={error.official_email}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        //backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={3}>

                        <CustomTextField
                            label="Official Mobile"
                            name="official_mobile"
                            onChange={handleChange}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            value={formData.official_mobile}
                            helperText={error.official_mobile}// Pass error status
                            type="tel"
                            applyPrefix
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        // backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                        {/* {error.official_mobile && <FormHelperText error>{error.official_mobile}</FormHelperText>} Display error message */}

                    </Grid>
                    <Grid item xs={3}>
                        <FormControl fullWidth required error={Boolean(error.date_of_joining)}>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Date of Joining"
                                    name="date_of_joining"
                                    value={dateOfJoining}
                                    disabled={isEditMode} // Disable in edit mode
                                    onChange={handleDateChange}
                                    maxDate={today} // Prevent future dates
                                    format="DD/MM/YYYY"
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            // required: true,
                                            // error: Boolean(error.date_of_joining),
                                            // helperText: error.date_of_joining,
                                            sx: {
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }
                                        }
                                    }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            //   helperText={error.dateOfJoining}
                                            sx={{
                                                width: '100%',
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px', // Width of the red line
                                                        backgroundColor: 'red', // Color of the line
                                                        zIndex: 1,
                                                    }
                                                }
                                            }}
                                            required

                                        />
                                    )}
                                    disableMaskedInput={false} // Enables input masking
                                />

                            </LocalizationProvider>
                            {error.date_of_joining && (
                                <FormHelperText>{error.date_of_joining}</FormHelperText>
                            )}
                        </FormControl>
                    </Grid>


                    <Grid item xs={12}>

                        <Box
                            sx={{
                                //backgroundColor: '#f0f0f0',
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',
                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Documents</h5>
                        </Box>
                        <hr></hr>
                    </Grid >

                    <Grid item xs={3}>
                        <CustomFileUpload
                            label={formData?.emp_photo ? extractFileName(formData.emp_photo) : "Upload photo"}
                            key={`${fileKey}-photo`}
                            reset={resetFiles}
                            value={null} // Set to null by default
                            accept=".png,.jpg,.pdf"
                            fileName={isEditMode ? existingPhoto : ''}
                            onFileSelect={(file) => handleFileSelect(file, 'photo')}
                            helperText={error.emp_photo || "Upload in Jpeg, Png, Jpg format"}
                            error={error.emp_photo}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            required={!isEditMode}
                            isRequired
                            fileType="photo"
                            sx={{
                                '& .MuiFormHelperText-root': {
                                    display: 'block',
                                    visibility: 'visible'
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <CustomFileUpload
                            label="Upload Aadhaar Front"
                            key={fileKey}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            accept=".jpeg,.png,.jpg,.pdf"
                            reset={resetFiles}
                            fileName={existingadharfrontfile}
                            value={formData.emp_adhar_front_pdf}
                            onFileSelect={(file) => handleFileSelect(file, 'aadhaarFront')}
                            helperText={error.emp_adhar_front_pdf || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            error={error.emp_adhar_front_pdf}
                            required={!isEditMode}
                            fileType="document"
                            sx={{
                                width: '100%',
                                position: 'relative',
                                '&:before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: '3px',
                                    backgroundColor: 'red',
                                    zIndex: 1,
                                },
                                '& .MuiFormHelperText-root.Mui-error': {
                                    color: 'red'
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <CustomFileUpload
                            label="Upload Aadhaar Back"
                            key={fileKey}
                            accept=".jpeg,.png,.jpg,.pdf"
                            fileName={existingadharbackfile}
                            reset={resetFiles}
                            value={formData.emp_adhar_back_pdf}
                            onFileSelect={(file) => handleFileSelect(file, 'aadhaarBack')}
                            helperText={error.emp_adhar_back_pdf || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            error={error.emp_adhar_back_pdf}
                            required={!isEditMode}
                            fileType="document"
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                width: '100%',
                                position: 'relative',
                                '&:before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: '3px',
                                    backgroundColor: 'red',
                                    zIndex: 1,
                                },
                                '& .MuiFormHelperText-root.Mui-error': {
                                    color: 'red'
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <CustomFileUpload
                            label="Upload PAN"
                            key={fileKey}
                            reset={resetFiles}
                            value={formData.emp_PAN_pdf}
                            fileName={existingPanfile}
                            accept=".jpeg,.png,.jpg,.pdf"
                            onFileSelect={(file) => handleFileSelect(file, 'pan')}
                            helperText={error.emp_PAN_pdf || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            error={error.emp_PAN_pdf}
                            required={!isEditMode}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            fileType="document" // Specify file type as document
                            sx={{
                                width: '100%',
                                position: 'relative',
                                '&:before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: '3px',
                                    backgroundColor: 'red',
                                    zIndex: 1,
                                },
                                '& .MuiFormHelperText-root.Mui-error': {
                                    color: 'red'
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <CustomFileUpload
                            label="Upload Signed Offer Letter"
                            key={fileKey}
                            reset={resetFiles}
                            accept=".jpeg,.png,.jpg,.pdf"
                            value={formData.emp_signed_offer_letter_pdf}
                            fileName={existingOfferLetterfile}
                            onFileSelect={(file) => handleFileSelect(file, 'offerLetter')}
                            helperText={error.emp_signed_offer_letter_pdf || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            error={error.emp_signed_offer_letter_pdf}
                            required={!isEditMode}
                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            fileType="document" // Specify file type as document
                            sx={{
                                width: '100%',
                                position: 'relative',
                                '&:before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: '3px',
                                    // backgroundColor: 'red',
                                    zIndex: 1,
                                },
                                '& .MuiFormHelperText-root.Mui-error': {
                                    color: 'red'
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={3}>
                        <CustomFileUpload
                            label="Upload Driving License"
                            key={fileKey}
                            reset={resetFiles}
                            accept=".jpeg,.png,.jpg,.pdf"
                            onFileSelect={(file) => handleFileSelect(file, 'drivingLicense')}
                            value={formData.emp_driving_license_pdf}
                            fileName={existingDrivingLicensefile}
                            // helperText={"Upload in .pdf format"}
                            helperText={error.emp_driving_license_pdf || "Upload in Pdf, Jpeg, Png, Jpg format"}
                            error={error.emp_driving_license_pdf}

                            disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            fileType="document" // Specify file type as document
                            sx={{
                                width: '100%',
                                position: 'relative',
                                '&:before': {
                                    content: '""',
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: '3px',
                                    // backgroundColor: 'red',
                                    zIndex: 1,
                                },
                                '& .MuiFormHelperText-root.Mui-error': {
                                    color: 'red'
                                }
                            }}
                        />
                    </Grid>

                </Grid >
            </form >
        </Box >
    );
};

export default Employee_Personal_Information;
