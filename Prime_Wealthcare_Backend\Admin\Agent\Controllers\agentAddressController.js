const AgentAddress = require('../Models/agentAddress');

// Get all agent addresses
exports.getAllAddresses = async (req, res) => {
    try {
        const data = await AgentAddress.findAll();
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching addresses', error });
    }
};

// Get an agent address by ID
exports.getAddressById = async (req, res) => {
    try {
        const id = req.params.id;
        const data = await AgentAddress.findById(id);
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching address', error });
    }
};

// Create a new agent address
exports.createAddress = async (req, res) => {
    try {
        const data = req.body;
        const result = await AgentAddress.create({...data});
        res.status(201).json(result);
    } catch (error) {
        res.status(500).json({ message: 'Error creating address', error });
    }
};

// Update an agent address by ID
exports.updateAddress = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const response = await AgentAddress.updateById(id, data);
        res.status(200).json({ message: 'Address updated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error updating address', error });
    }
};

// Delete (deactivate) an agent address by ID
exports.deleteAddress = async (req, res) => {
    try {
        const id = req.params.id;
        const result = await AgentAddress.deleteById(id);
        if (result) {
            res.status(200).json({ message: 'Address deactivated successfully' });
        } else {
            res.status(404).json({ message: 'Address not found' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Error deactivating address', error });
    }
};
