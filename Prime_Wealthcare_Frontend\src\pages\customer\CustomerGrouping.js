import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Typography, Grid, Avatar } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import { useNavigate, useParams } from 'react-router-dom';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useDispatch, useSelector } from 'react-redux';
import DetailsDropdown from '../../components/table/DetailsDropdown';
import CustomSection from '../../components/CustomSection';
import { getAllCustomer, getAllGroups, getAllPickLists, getCustomerById, updateGrouping } from '../../redux/actions/action';
import { getAllRoles } from '../../redux/actions/action';
import CustomFileUpload from '../../components/CustomFileUpload';
import CustomTextField from '../../components/CustomTextField';
import CustomCheckbox from '../../components/CheckboxWithLabel';
import Dropdown from '../../components/table/DropDown';
import { TextField, FormControl, InputLabel, Select, MenuItem, Checkbox, ListItemText, FormControlLabel } from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-GB').format(date); // 'en-GB' formats date as dd/mm/yyyy
}

function CustomerGrouping() {

    const { id } = useParams();
    const [selectedOption, setSelectedOption] = useState('none');
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [formData, setFormData] = useState({
        relation: '',
        head_name: '',
        group_code: '',
        mobile: '',
        whatsApp_number: '',
        email: '',
        assigned_to: '',
        created_at: new Date().toISOString()
    });

    const items = useSelector(state => state.areaManagementReducer.items);
    const [sortedItems, setSortedItems] = useState(items || []);

    const relationData = useSelector((state) => state.pickListReducer.relationOptions);
    const dataByCustomerId = useSelector((state) => state.customerReducer.customerDetails);
    const allCustomers = useSelector((state) => state.customerReducer.customer); // Assuming this is the array of customers
    const groupData = useSelector((state) => state.customerGroupingReducer.groups)
    useEffect(() => {
        if (id) {
            // Fetch data only if in edit mode (id exists)
            dispatch(getCustomerById(id));

        } else {
            // Initialize formData for new mode
            setFormData({
                relation: '',
                head_name: '',
                group_code: '',
                mobile: '',
                whatsApp_number: '',
                email: '',
                assigned_to: '',
                created_at: new Date().toISOString()
            });
        }
        dispatch(getAllPickLists());
        dispatch(getAllCustomer());
        dispatch(getAllGroups());
    }, [id, dispatch]);
    const headnameOptions = allCustomers
        .filter(customer => customer.head_name === 1)
        .map(customer => ({
            value: customer.id,
            label: customer?.customer_category === 'corporate' ? customer?.company_name : `${customer?.first_name || ''} ${customer?.last_name || ''}`.trim(),
        }));
    const groupCodeOptions = allCustomers
        .filter(customer => customer.head_name === 1)
        .map(customer => ({
            value: customer.group_code,
            label: customer.group_code, // Use group_code directly as the label
        }));


    useEffect(() => {
        if (dataByCustomerId) {

            if (dataByCustomerId.head_name === 1) {
                // If head_name is 1, fill all fields with customer data
                const relatedGroup = groupData.find(group => group.customer_id === dataByCustomerId.id); // Find the related group

                setFormData({
                    relation: dataByCustomerId ? dataByCustomerId.relation_id : '', // Set relation from the found group
                    head_name: dataByCustomerId?.head_name, // Directly set head_name from dataByCustomerId
                    group_code: dataByCustomerId?.group_code, // Set group_code from the found group
                    mobile: dataByCustomerId?.mobile,
                    whatsApp_number: dataByCustomerId?.whatsApp_number,
                    email: dataByCustomerId?.email,
                    assigned_to: dataByCustomerId?.assigned_to,
                    created_at: dataByCustomerId?.created_at || new Date().toISOString() // Ensure created_at is valid
                });
            } else {
                // // If head_name is 0, set created_at to the current date
                // setFormData(prevState => ({
                //     ...prevState,
                //     created_at: new Date().toISOString() // Set to current date
                // }));
                const relatedGroup = groupData.find(group => group.customer_id === dataByCustomerId.id); // Find the related group

                setFormData({
                    relation: relatedGroup ? relatedGroup.relation_id : '', // Set relation from the found group
                    head_name: relatedGroup ? relatedGroup.head_name : '',
                    group_code: relatedGroup ? relatedGroup.group_code : '', // Set group_code from the found group
                    mobile: dataByCustomerId?.mobile,
                    whatsApp_number: dataByCustomerId?.whatsApp_number,
                    email: dataByCustomerId?.email,
                    assigned_to: dataByCustomerId?.assigned_to,
                    created_at: dataByCustomerId?.created_at || new Date().toISOString() // Ensure created_at is valid
                });
            }
        }
    }, [dataByCustomerId, groupData]);

    useEffect(() => {
        setSortedItems(items);
    }, [items]);

    const relationOptions = relationData.map((relation) => ({
        value: relation.id,
        label: relation.label_name,

    }))


    const handleAdd = () => {
        // Prepare the data to be sent to the grouping table
        const groupingData = {
            relation_id: formData.relation,
            head_name: formData.head_name === 1, // Convert head_name to boolean
            group_code: formData.group_code,
            assigned_to: formData.assigned_to,
        };

        // Dispatch an action to save the grouping data (you may need to create this action)
        dispatch(updateGrouping({ id: id, data: groupingData })); // Ensure you have an action creator for this

        navigate('/dashboard/customer-Master'); // Navigate to the create personal detail page
    };

    const handleExportToPDF = () => {
        const doc = new jsPDF({ orientation: 'landscape' });
        autoTable(doc, {
            head: [['Full Name', 'User ID', 'Office Mobile', 'Office Email', 'Department', 'Role', 'Personal Mobile', 'Personal Email', 'Joining Date', 'Branch Name', 'City', 'Date of Birth']],
            body: [[
                `${dataByCustomerId.first_name} ${dataByCustomerId.last_name}`,
                dataByCustomerId.user_id,
                dataByCustomerId.official_mobile,
                dataByCustomerId.official_email,
                dataByCustomerId.department_name,
                dataByCustomerId.role_name,
                dataByCustomerId.personal_mobile,
                dataByCustomerId.personal_email,
                formatDate(dataByCustomerId.date_of_joining),
                dataByCustomerId.branch_name,

                formatDate(dataByCustomerId.date_of_birth)
            ]],
        });
        doc.save('CustomerGroupings.pdf');
    };

    const handleCancel = () => {
        navigate('/dashboard/customer-Master');
    };

    const handleInputChange = (e) => {
        const { name, value: originalValue } = e.target; // Rename value to originalValue
        let value = originalValue; // Create a new variable for modified value

        // Check if the changed field is head_name
        if (name === 'head_name') {
            const selectedCustomer = allCustomers.find(customer => customer.id === value);
            if (selectedCustomer) {
                // Update mobile and email based on selected customer
                setFormData(prevState => ({
                    ...prevState,
                    mobile: selectedCustomer.mobile,
                    email: selectedCustomer.email,
                    whatsApp_number: selectedCustomer.whatsApp_number,
                    // If head_name is 1, fill all fields
                    ...(selectedCustomer.head_name === 1 && {
                        group_code: groupData.find(group => group.customer_id === selectedCustomer.id)?.group_code || '',
                        relation: selectedCustomer.relation,
                        assigned_to: selectedCustomer.assigned_to,
                        //  created_at: selectedCustomer?.created_at
                    })
                }));
            }
        }

        // Check if the changed field is group_code
        if (name === 'group_code') {
            const selectedGroup = groupData.find(group => group.group_code === value);
            if (selectedGroup) {
                // Find the customer associated with the selected group
                const customer = allCustomers.find(customer => customer.id === selectedGroup.customer_id);
                if (customer) {
                    // Update formData based on the selected customer
                    setFormData(prevState => ({
                        ...prevState,
                        mobile: customer.mobile, // Get mobile from the customer data
                        email: customer.email, // Get email from the customer data
                        whatsApp_number: customer.whatsApp_number, // Get WhatsApp number from the customer data
                        head_name: selectedGroup.customer_id, // Assuming customer_id is used for head_name
                    }));
                }
            }
        }

        setFormData(prevState => ({ ...prevState, [name]: value }));
    }
    const renderAvatar = () => {
        const { photo, employee_full_name } = dataByCustomerId || {};
        const initials = employee_full_name ? employee_full_name.split(' ').slice(0, 2).map(n => n[0].toUpperCase()).join('') : 'N/A';
        return (
            <Avatar
                alt={employee_full_name}
                src={photo || ''}
                sx={{ width: photo ? 100 : 50, height: photo ? 100 : 50 }}
            >
                {!photo && initials}
            </Avatar>
        );
    };

    return (
        <Box sx={{
            paddingLeft: { xs: '20px', md: '40px' },
            paddingRight: { xs: '20px', md: '40px' },
            paddingBottom: '40px'
        }}>
            <form encType="multipart/form-data">
                <Grid container spacing={{ xs: 1, md: 2 }} style={{ display: 'flex', alignItems: 'center' }}>
                    {/* Header Row */}
                    <Grid item xs={12} md={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Customer" pageName={"Create"} />
                        </Box>
                    </Grid>


                    <Grid item xs={12} md={4} sx={{
                        display: 'flex',
                        justifyContent: { xs: 'center', md: 'flex-end' },
                        flexWrap: { xs: 'wrap', md: 'nowrap' },
                        gap: { xs: 1, md: 0 }
                    }}>
                        <Button variant="outlined" size="small"
                            sx={{
                                minWidth: { xs: '20%', md: '100px' },
                                mx: { xs: 0.5, md: 0.5 },
                                color: 'green',
                                borderColor: 'green',
                                mt: 3,
                                textTransform: 'none'
                            }}>
                            Save & New
                        </Button>

                        <Button variant="outlined" size="small" sx={{
                            minWidth: { xs: '20%', md: '100px' },
                            mx: { xs: 0.5, md: 0.5 },
                            color: 'green',
                            borderColor: 'green',
                            mt: 3,
                            textTransform: 'none'
                        }}
                            onClick={handleAdd}
                        >
                            Save
                        </Button>
                        <Button variant="outlined" size="small" sx={{
                            minWidth: { xs: '20%', md: '100px' },
                            mx: { xs: 0.5, md: 0.5 },
                            color: 'red',
                            borderColor: 'red',
                            mt: 3,
                            textTransform: 'none'
                        }}
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                    </Grid>

                    <Grid container >
                        <CustomSection titles={['Overview', 'Personal Details', 'Member Information', 'Address', 'Grouping']} page='customer' customerType={dataByCustomerId?.customer_category} />
                    </Grid>

                    <Container maxWidth="xl">
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 1
                        }}>
                            <Box display="flex"
                                flexDirection={{ xs: 'column', sm: 'row' }}
                                p={2}
                                sx={{
                                    padding: { xs: '0.5rem', sm: '1rem' },
                                    borderBlock: '1px solid black',
                                    gap: { xs: 2, sm: 0 }
                                }}
                            >
                                {/* Avatar Section */}
                                <Box sx={{
                                    mr: { xs: 0, sm: 2 },
                                    display: 'flex',
                                    justifyContent: { xs: 'center', sm: 'flex-start' }
                                }}>
                                    {renderAvatar()}
                                </Box>

                                {/* Information Section */}
                                <Box sx={{ flex: 1 }}>
                                    <Grid container spacing={{ xs: 1, sm: 2 }} sx={{
                                        alignItems: 'center',
                                        textAlign: { xs: 'center', sm: 'left' }
                                    }}>
                                        {/* Full Name */}
                                        <Grid item xs={12} sm={6} md={4} lg={4}>
                                            <Typography
                                                noWrap
                                                sx={{
                                                    fontSize: { xs: '0.875rem', sm: '1rem' },
                                                    mb: { xs: 0.5, sm: 0 }
                                                }}
                                            >
                                                <strong>Full Name:</strong>  {dataByCustomerId?.first_name} {dataByCustomerId?.last_name}

                                            </Typography>
                                        </Grid>

                                        {/* Mobile */}
                                        <Grid item xs={12} sm={6} md={4} lg={4}>
                                            <Typography
                                                noWrap
                                                sx={{
                                                    fontSize: { xs: '0.875rem', sm: '1rem' },
                                                    mb: { xs: 0.5, sm: 0 }
                                                }}
                                            >
                                                <strong>Mobile:</strong> {dataByCustomerId?.mobile}
                                            </Typography>
                                        </Grid>

                                        {/* Email */}
                                        <Grid item xs={12} sm={6} md={4} lg={4}>
                                            <Typography
                                                noWrap
                                                sx={{
                                                    fontSize: { xs: '0.875rem', sm: '1rem' },
                                                    mb: { xs: 0.5, sm: 0 }
                                                }}
                                            >
                                                <strong>Email:</strong> {dataByCustomerId?.email}
                                            </Typography>
                                        </Grid>

                                        {/* Area */}
                                        <Grid item xs={12} sm={6} md={4} lg={4}>
                                            <Typography
                                                noWrap
                                                sx={{
                                                    fontSize: { xs: '0.875rem', sm: '1rem' },
                                                    mb: { xs: 0.5, sm: 0 }
                                                }}
                                            >
                                                <strong>Area:</strong> {dataByCustomerId?.department_name}
                                            </Typography>
                                        </Grid>

                                        {/* City */}
                                        <Grid item xs={12} sm={6} md={4} lg={4}>
                                            <Typography
                                                noWrap
                                                sx={{
                                                    fontSize: { xs: '0.875rem', sm: '1rem' },
                                                    mb: { xs: 0.5, sm: 0 }
                                                }}
                                            >
                                                <strong>City:</strong> {dataByCustomerId?.role_name}
                                            </Typography>
                                        </Grid>

                                        {/* State */}
                                        <Grid item xs={12} sm={6} md={4} lg={4}>
                                            <Typography
                                                noWrap
                                                sx={{
                                                    fontSize: { xs: '0.875rem', sm: '1rem' },
                                                    mb: { xs: 0.5, sm: 0 }
                                                }}
                                            >
                                                <strong>State:</strong> {dataByCustomerId?.personal_mobile}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Box>
                            </Box>
                        </Box>
                    </Container>
                    <Grid item xs={12} >
                        <Box
                            sx={{
                                //backgroundColor: '#f0f0f0',
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',

                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Grouping Name</h5>
                            <div style={{ height: '100vh', display: 'flex', alignItems: 'center' }}>

                            </div>
                        </Box>
                        <hr></hr></Grid>
                    <Grid item xs={12} sm={6} md={3} sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Dropdown
                            label="Head Name"
                            name="head_name"
                            required
                            options={headnameOptions} // Check if options are available
                            value={formData?.head_name} // Ensure value is taken from formData
                            onChange={handleInputChange}
                            disabled={dataByCustomerId?.head_name === 1}
                            fullWidth
                            InputLabelProps={{
                                shrink: true,
                            }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>

                        <Dropdown
                            label="Group Code"
                            name="group_code"
                            required
                            value={formData?.group_code} // Ensure this is set to formData.group_code
                            options={groupCodeOptions}
                            onChange={handleInputChange}
                            disabled={dataByCustomerId?.head_name === 1}
                            fullWidth
                            InputLabelProps={{
                                shrink: true,
                            }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>

                        <Dropdown
                            label=" Relation"
                            name="relation"
                            required
                            disabled={dataByCustomerId?.head_name === 1} // Disable if head_name is 1

                            value={formData.relation}
                            options={relationOptions}
                            onChange={handleInputChange}
                            fullWidth

                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                }
                            }}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>

                        <CustomTextField
                            label="Mobile"
                            name="mobile"
                            disabled
                            value={formData?.mobile} // Bind to formData
                            type="tel"
                            //    value={dataByCustomerId?.mobile}
                            // helperText={error.personal_mobile}// Pass error status
                            applyPrefix

                            // disabled={isDisabled || (id && employeeDetails?.status === 0)}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                        {/* {error.personal_mobile && <FormHelperText error>{error.personal_mobile}</FormHelperText>} */}

                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="Whatsapp Number"
                            name="whatsApp_number"
                            type="tel"
                            value={formData?.whatsApp_number} // Bind to formData

                            disabled
                            //   value={dataByCustomerId?.whatsApp_number} // Assigning customer WhatsApp number data

                            applyPrefix
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label=" Email "
                            disabled
                            value={formData?.email} // Assigning customer email data

                            name="email"
                            InputLabelProps={{
                                shrink: true, // This will ensure the label shrinks when the field is filled
                            }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>



                    <Grid item xs={12} >
                        <Box
                            sx={{
                                //backgroundColor: '#f0f0f0',
                                display: 'flex', alignItems: 'center',
                                padding: '10px',
                                borderRadius: '4px',
                                height: '60px',
                                fontSize: '18px',
                                fontStyle: 'normal',
                                fontWeight: '700',
                                lineHeight: '27px',
                                color: '#4C5157'
                            }}
                        >
                            <h5>Created Info</h5>
                            <div style={{ height: '100vh', display: 'flex', alignItems: 'center' }}>

                            </div>
                        </Box>
                        <hr></hr></Grid>
                    <Grid item xs={12} container spacing={2} justifyContent="space-between">
                        {/* Left Aligned TextField */}
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Assigned To"
                                disabled
                                value={formData.assigned_to}
                                onChange={handleInputChange}
                                name="assigned_to"
                                fullWidth
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>

                        {/* Right Aligned TextField */}
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Created At"
                                disabled
                                value={formatDate(formData?.created_at)} // Format the created_at date for display
                                name="created_at"
                                fullWidth
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>
                    </Grid>
                </Grid>
            </form>
        </Box>

    );
}

export default CustomerGrouping;
