import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Avatar } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify'
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { fetchEmployeeData, softDeleteImfEmployee, reinstateEmployee, searchEmployee, fetchEmployeeByCriteria } from '../../../redux/actions/action';
import ExportToPDF from '../../../components/ExportToPDF';
import {usePermissions} from '../../../hooks/usePermissions';
import AvatarImage from '../../../components/AvatarImage';

const Employee_masterPage = () => {

    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [selectedRows, setSelectedRows] = useState([]);
    const employeeData = useSelector((state) => state.employeeInfoReducer.employees);
    const [sortedEmployee, setSortedEmployee] = useState([]);
    const [selectedOption, setSelectedOption] = useState('none');
    const [statusFilter, setStatusFilter] = useState('all');
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const { data } = useSelector(state => state.imfBranchReducer);
    const [selectedItem, setSelectedItem] = useState(null);
    const [deletedIds, setDeletedIds] = useState([]);

    const permissions = usePermissions('User Management', 'Employee');
    useEffect(() => {
        dispatch(fetchEmployeeData());
    }, [dispatch]);
    useEffect(() => {
        const filterEmployeeByStatus = () => {
            if (!employeeData || employeeData.length === 0) {
                setSortedEmployee([]); // Handle empty employeeData
                return;
            }
            if (statusFilter === 'all') {
                setSortedEmployee(employeeData);
            } else if (statusFilter === 'none') {
                // Optionally, you can handle 'none' differently if needed
                setSortedEmployee([]); // Clear sortedEmployee if 'none'
            } else {
                setSortedEmployee(employeeData.filter(employee =>
                    employee.status === (statusFilter === 'active' ? 1 : 0)
                ));
            }
        }
        filterEmployeeByStatus();
    }, [statusFilter, employeeData]);
    useEffect(() => {
        if (selectedOption) {
            dispatch(fetchEmployeeByCriteria(selectedOption));
        }
    }, [selectedOption, dispatch]);

    //  useEffect(() => {
    //     const filterEmployeeByOption = () => {
    //         if (!employeeData) return;

    //         const currentDate = new Date();
    //         const oneWeekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    //         const twoWeeksAgo = new Date(currentDate.getTime() - 14 * 24 * 60 * 60 * 1000);

    //         switch (selectedOption) {
    //             case 'none':
    //                 setSortedEmployee(employeeData);
    //                 break;
    //             case 'newLastWeek':
    //                 setSortedEmployee(employeeData.filter(emp => {
    //                     const createdDate = new Date(emp.created_at);
    //                     return createdDate >= twoWeeksAgo && createdDate < oneWeekAgo;
    //                 }));
    //                 break;
    //             case 'newThisWeek':
    //                 setSortedEmployee(employeeData.filter(emp => {
    //                     const createdDate = new Date(emp.created_at);
    //                     return createdDate >= oneWeekAgo;
    //                 }));
    //                 break;
    //             case 'deactivatedThisWeek':
    //                 setSortedEmployee(employeeData.filter(emp => {
    //                     const deactivatedDate = new Date(emp.deactivated_at);
    //                     return deactivatedDate >= oneWeekAgo && emp.status === 0;
    //                 }));
    //                 break;
    //             case 'deactivatedLastWeek':
    //                 setSortedEmployee(employeeData.filter(emp => {
    //                     const deactivatedDate = new Date(emp.deactivated_at);
    //                     return deactivatedDate >= twoWeeksAgo && deactivatedDate < oneWeekAgo && emp.status === 0;
    //                 }));
    //                 break;
    //             case 'editedLastWeek':
    //                 setSortedEmployee(employeeData.filter(emp => {
    //                     const updatedDate = new Date(emp.updated_at);
    //                     return updatedDate >= twoWeeksAgo && updatedDate < oneWeekAgo;
    //                 }));
    //                 break;
    //             case 'editedThisWeek':
    //                 setSortedEmployee(employeeData.filter(emp => {
    //                     const updatedDate = new Date(emp.updated_at);
    //                     return updatedDate >= oneWeekAgo;
    //                 }));
    //                 break;
    //             default:
    //                 setSortedEmployee(employeeData);
    //         }
    //     };

    //     filterEmployeeByOption();
    // }, [selectedOption, employeeData]);

    // Add this new handler for selecting all rows

    const handleSelectAll = (isSelected) => {
        if (isSelected) {
            const allIds = sortedEmployee.map(emp => emp.id);
            setSelectedRows(allIds);
        } else {
            setSelectedRows([]);
        }
    };
    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelectedRows => {
            if (prevSelectedRows.includes(id)) {
                return prevSelectedRows.filter(selectedId => selectedId !== id);
            } else {
                return [...prevSelectedRows, id];
            }
        });
    };

    const handleAllClick = () => setStatusFilter('all');
    const handleActiveClick = () => setStatusFilter('active');
    const handleInactiveClick = () => setStatusFilter('inactive');
    const handleRefreshClick = () => setStatusFilter('all');




    const handleAdd = () => {
        navigate('/dashboard/employee-personal-information');
    };


    const onSearch = (query) => {
        if (query.trim() === '') {        // If the search query is empty, reset to all employee data
            setSortedEmployee(employeeData);
            return; // Exit the function early
        }
        dispatch(searchEmployee(query)); // Ensure this action updates the state with search results
        // Update sortedEmployee with the search results
        const filteredEmployees = employeeData.filter(employee => {
            const lowerCaseQuery = query.toLowerCase();
            return (
                (employee.employee_full_name && employee.employee_full_name.toLowerCase().includes(lowerCaseQuery)) ||
                (employee.department_name && employee.department_name.toLowerCase().includes(lowerCaseQuery)) ||
                (employee.personal_mobile && employee.personal_mobile.toLowerCase().includes(lowerCaseQuery)) ||
                (employee.official_mobile && employee.official_mobile.toLowerCase().includes(lowerCaseQuery)) ||
                (Array.isArray(employee.branch_names) && employee.branch_names.length > 0 &&
                    employee.branch_names.some(branch => branch.toLowerCase().includes(lowerCaseQuery))) || // Check if any branch name matches
                (employee.branch_city && employee.branch_city.toLowerCase().includes(lowerCaseQuery))
            );
        });
        // Check if there are no filtered employees
        if (filteredEmployees.length === 0) {
            toast.error('Failed to find employee'); // Show error message if no employees found
        }
        setSortedEmployee(filteredEmployees);
    }
    // const handleReinstate = (id) => {
    //   dispatch(reinstateEmployee(id))
    //     .then(() => {
    //       dispatch(fetchEmployeeData());
    //     })
    //     .catch(error => {
    //       console.error("Failed to reinstate Employee:", error);
    //     });
    // };
    const handleReinstate = (id) => {
        dispatch(reinstateEmployee(id))
            .then(() => {
                dispatch(fetchEmployeeData());
                toast.success('Employee reinstated successfully');
            })
            .catch(error => {
                console.error("Failed to reinstate Employee:", error);
                toast.error('Failed to reinstate employee');
            });
    };

    const handleView = (id) => {
        navigate(`/dashboard/employee-master-overview/${id}`, {
            state: {
                mode: 'view',
                isDisabled: true
            }
        });
    };
    const handleEdit = (id) => {
        navigate(`/dashboard/employee-personal-information/edit/${id}`, {
            state: {
                mode: 'edit',
                isDisabled: false
            }
        });
    };

    const handleDelete = (id) => {
        // Find the employee from employeeData instead of data
        const employeeToDelete = employeeData.find(employee => employee.id === id);
        handleOpenDeletePopup(employeeToDelete);
    }

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };
    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };



    const handleConfirmDelete = () => {
        if (!selectedItem) return;

        dispatch(softDeleteImfEmployee(selectedItem.id))
            .then(() => {
                dispatch(fetchEmployeeData());
                setOpenDeletePopup(false);
                setOpenSuccessPopup(true);
                toast.success('Employee deactivated successfully');
            })
            .catch(error => {
                console.error("Failed to deactivate employee:", error);
                setOpenDeletePopup(false);
                toast.error('Failed to deactivate employee');
            });
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };


    const columns = [
        {
            field: 'photo',
            headerName: 'Image',
            renderCell: (params) => (
                <AvatarImage
                alt={params.row.employee_full_name}
                src={params.row.emp_photo || ''}
                sx={{ width: 40 , height: 40 }}
                >
                    {!params?.row?.emp_photo && params?.row?.full_name?.split(' ').slice(0, 2).map(n => n[0].toUpperCase()).join('')}
                </AvatarImage>
            )

        },
        {
            field: 'employee_full_name',
            headerName: 'Employee Name',
            width: '20%',

        },
        { field: 'user_id', headerName: 'User Id', width: '10%' },
        { field: 'department_name', headerName: 'Department', width: '10%' },
        { field: 'personal_mobile', headerName: 'Personal Mobile', width: '15%' },
        { field: 'official_mobile', headerName: 'Office Mobile', width: '15%' },
        { field: 'branch_names', headerName: 'Branch Name', width: '20%' },
        { field: 'branch_city', headerName: 'City', width: '10%' },
    ];
    const dataMapping = {
        'Employee Name': 'employee_full_name',
        'User Id': 'user_id',
        'Department': 'department_name',
        'Personal Mobile': 'personal_mobile',
        'Office Email': 'official_email',
        'Branch Name': 'branch_names',
        'City': 'branch_city',
        'Status': 'status'
    };

    return (

        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            {/*    <Navbar/>  */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Employee" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                        <Button
                            onClick={handleAdd}
                            sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
                        >
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={sortedEmployee.map(employee => ({
                                ...employee,
                                status: employee.status === 1 ? 'Active' : 'Inactive',
                                // Handle branch_names if it's an array
                                branch_names: Array.isArray(employee.branch_names) 
                                    ? employee.branch_names.join(', ') 
                                    : employee.branch_names
                            }))}
                            headNames={[
                                'Employee Name',
                                'User Id',
                                'Department',
                                'Personal Mobile',
                                'Office Email',
                                'Branch Name',
                                'City',
                                'Status'
                            ]}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="employee_data.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Employee Report"
                        />
                    </ButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingBottom: '1rem', ml: 5 }}>
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                    />
                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <SearchBar placeholder="Search..."
                            onSearch={onSearch}
                        />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>

                <CustomTable
                    data={sortedEmployee || []}
                    showAvatar={true}
                    columns={columns}
                    onEdit={permissions.can_edit ? handleEdit : null}
                    onDelete={permissions.can_delete ? handleDelete : null}
                    onReinstate={handleReinstate}
                    // onView={handleView}
                    selectedRows={selectedRows}
                    onSelectionChange={handleSelectionChange}
                    onSelectAll={handleSelectAll}
                    hideRefreshButton={true}
                    //  showEyeIcon={(row) => row.status === 1}
                    deletedIds={deletedIds}
                    setDeletedIds={setDeletedIds}
                    handleViewData={handleView}
                />
            </Box>
            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.employee_full_name : ''}
            />

            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.employee_full_name : ''}
            />
        </Container>
    );
};

export default Employee_masterPage;