const express = require('express');
const mainProductController = require('../Controllers/mainProductController');
const router = express.Router();

// Route to get all Products
router.get('/', mainProductController.getAllProducts);

// Route to get a Product by ID
router.get('/:id', mainProductController.getProductById);

// Route to get a Product by name
router.get('/name/:name', mainProductController.getProductByName);

// Route to create a new Product
router.post('/', mainProductController.createProduct);

// Route to update a Product by ID
router.put('/:id', mainProductController.updateProduct);

// Route to delete a Product by ID
router.delete('/:id', mainProductController.deleteProduct);

// Route to reinstate a Product by ID
router.put('/reinstate/:id', mainProductController.reinstateProduct);

// Route to get Products by specific criteria (new, deactivated, edited)
router.get('/criteria/:criteria', mainProductController.getProductsByCriteria);

module.exports = router;
