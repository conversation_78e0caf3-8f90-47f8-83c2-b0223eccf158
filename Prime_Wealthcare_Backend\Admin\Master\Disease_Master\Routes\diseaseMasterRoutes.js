const express = require('express');
const diseaseMasterController = require('../Controllers/diseaseMasterController');
const router = express.Router();

// Route to get all Diseases
router.get('/', diseaseMasterController.getAllDiseases);

// Route to get a Disease by ID
router.get('/:id', diseaseMasterController.getDiseaseById);

// Route to get a Disease by name
router.get('/name/:name', diseaseMasterController.getDiseaseByName);

// Route to create a new Disease
router.post('/', diseaseMasterController.createDisease);

// Route to update a Disease by ID
router.put('/:id', diseaseMasterController.updateDisease);

// Route to delete a Disease by ID
router.delete('/:id', diseaseMasterController.deleteDisease);

// Route to reinstate a Disease by ID
router.put('/reinstate/:id', diseaseMasterController.reinstateDisease);

// Route to get Diseases by specific criteria (new, deactivated, edited)
router.get('/criteria/:criteria', diseaseMasterController.getDiseasesByCriteria);

module.exports = router;
