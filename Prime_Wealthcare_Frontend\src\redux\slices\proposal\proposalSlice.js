import { createSlice } from '@reduxjs/toolkit';
import { transferBusiness, createProposal, getAllProposals, getAllProposalsByUserId, getPolicyPdf, getProposalById, getProposalByQuotationNumber, getProposalDetailsByPolicyNumber, submitProposal, updateProposal } from '../../actions/action';
import { toast } from 'react-toastify';

const initialState = {
    proposalDetails: null,
    loading: false,
    proposal: null,
    error: null,
    proposalList: [],
    selectedProposal: null,
    selectedQuotation: null,
};

const proposalSlice = createSlice({
    name: 'proposal',
    initialState,
    reducers: {
        setProposalDetails: (state, action) => {
            state.proposalDetails = action.payload;
        },
        setLoading: (state, action) => {
            state.loading = action.payload;
        },
        setError: (state, action) => {
            state.error = action.payload;
        },
        setProposalList: (state, action) => {
            state.proposalList = action.payload;
        },
        setSelectedProposal: (state, action) => {
            state.selectedProposal = action.payload;
        },
        setSelectedQuotation: (state, action) => {
            state.selectedQuotation = action.payload;
        },
        clearSelectedQuotation: (state) => {
            state.selectedQuotation = null;
        },
        clearProposalDetails: (state) => {
            state.proposalDetails = null;
            state.proposal = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getProposalByQuotationNumber.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getProposalByQuotationNumber.fulfilled, (state, action) => {
                state.proposal = action.payload;
                state.loading = false;
            })
            .addCase(getProposalByQuotationNumber.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(getAllProposals.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getAllProposals.fulfilled, (state, action) => {
                state.proposalList = action.payload;
                state.loading = false;
            })
            .addCase(getAllProposals.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(getAllProposalsByUserId.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getAllProposalsByUserId.fulfilled, (state, action) => {
                state.proposalList = action.payload;
                state.loading = false;
            })
            .addCase(getAllProposalsByUserId.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(getProposalById.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getProposalById.fulfilled, (state, action) => {
                state.proposal = action.payload;
                state.loading = false;
            })
            .addCase(getProposalById.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(getProposalDetailsByPolicyNumber.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getProposalDetailsByPolicyNumber.fulfilled, (state, action) => {
                state.proposal = action.payload;
                state.loading = false;
            })
            .addCase(getProposalDetailsByPolicyNumber.rejected, (state, action) => {
                toast.error(action.payload);
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(submitProposal.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(submitProposal.fulfilled, (state, action) => {
                state.proposal = action.payload;
                toast.success('Proposal submitted successfully');
                state.loading = false;
            })
            .addCase(submitProposal.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })
            .addCase(updateProposal.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(updateProposal.fulfilled, (state, action) => {
                state.proposal = action.payload;
                toast.success('Proposal updated successfully');
                state.loading = false;
            })
            .addCase(updateProposal.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to update proposal');
                state.loading = false;
            })
            .addCase(createProposal.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(createProposal.fulfilled, (state, action) => {
                state.proposal = action.payload;
                toast.success('Proposal created successfully');
                state.loading = false;
            })
            .addCase(createProposal.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to create proposal');
                state.loading = false;
            })
            .addCase(getPolicyPdf.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getPolicyPdf.fulfilled, (state, action) => {
                toast.success('Pdf fetched successfully')
                state.loading = false;
            })
            .addCase(getPolicyPdf.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })

            .addCase(transferBusiness.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(transferBusiness.fulfilled, (state, action) => {
                state.loading = false;
                // Update the proposal list with transferred business
                state.proposalList = state.proposalList.map(proposal =>
                    proposal.id === action.payload.id ? action.payload : proposal
                );
                toast.success('Business transferred successfully');
            })
            .addCase(transferBusiness.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error(action.payload || 'Failed to transfer business');
            })
    }
});

export const {
    setProposalDetails,
    setLoading,
    setError,
    setProposalList,
    setSelectedProposal,
    clearProposalDetails,
    setSelectedQuotation,
    clearSelectedQuotation,
    setCurrentProposalId
} = proposalSlice.actions;

export default proposalSlice.reducer;
