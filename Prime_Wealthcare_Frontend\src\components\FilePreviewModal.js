import * as React from 'react';
import { Box, Modal, Typography, Button, IconButton, CircularProgress } from '@mui/material';
import { Download } from '@mui/icons-material';
import { useEffect, useState } from 'react';
import axios from 'axios';

export default function FilePreviewModal({ isOpen, onClose, fileUrl, fileName, isLocalFile = false, rawFileData = null }) {
    const host = process.env.REACT_APP_HOST;
    const port = process.env.REACT_APP_PORT;

    const [loadError, setLoadError] = useState(false);
    const [fileType, setFileType] = useState('unknown');
    const [displayUrl, setDisplayUrl] = useState('');
    const [isLoading, setIsLoading] = useState(false); // <-- Added loading state for both preview and download
    const [isPreviewLoaded, setIsPreviewLoaded] = useState(false); // <-- New: for iframe/img load status

    useEffect(() => {
        if (!fileUrl && fileName && fileName.toLowerCase().endsWith('.pdf') && rawFileData) {
            if (typeof rawFileData === 'string' && rawFileData.startsWith('blob:')) {
                setDisplayUrl(rawFileData);
            }
        }
    }, [fileUrl, fileName, rawFileData]);

    useEffect(() => {
        if (rawFileData && typeof rawFileData === 'object' && 'section_name' in rawFileData) {
            // Handle complex raw data if needed
        }
    }, [rawFileData]);

    useEffect(() => {
        if (fileUrl) {
            if (isLocalFile) {
                setDisplayUrl(fileUrl);
            } else if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
                setDisplayUrl(fileUrl);
            } else {
                const normalizedPath = fileUrl
                    .replace(/\\/g, '/') // Convert backslashes to forward slashes
                    .replace(/^([A-Z]):/i, '$1') // Remove colon from drive letter
                    .replace(/\/\//g, '/'); // Replace any double slashes with single

                const link = process.env.REACT_APP_IMAGE_URL;
                setDisplayUrl(`${link}${normalizedPath}`);
            }
        } else {
            setDisplayUrl('');
        }
    }, [fileUrl, isLocalFile, host, port]);

    const detectFileTypeFromUrl = (url) => {
        if (!url) return 'unknown';
        const lowerUrl = url.toLowerCase();
        if (lowerUrl.includes('.pdf') || lowerUrl.endsWith('/pdf')) return 'pdf';
        if (['.jpg', '.jpeg', '.png', '.gif', '.bmp'].some(ext => lowerUrl.includes(ext))) return 'image';
        return 'unknown';
    };

    useEffect(() => {
        if (fileUrl || fileName) {
            if (fileName) {
                const lowerFileName = fileName.toLowerCase();
                if (lowerFileName.endsWith('.pdf')) {
                    setFileType('pdf');
                    return;
                } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp'].some(ext => lowerFileName.endsWith(ext))) {
                    setFileType('image');
                    return;
                }
            }

            if (fileUrl) {
                const urlFileType = detectFileTypeFromUrl(fileUrl);
                if (urlFileType !== 'unknown') {
                    setFileType(urlFileType);
                    return;
                }
            }

            if (fileUrl && !isLocalFile && displayUrl) {
                fetch(displayUrl, { method: 'HEAD' })
                    .then(response => {
                        const contentType = response.headers.get('content-type');
                        if (contentType?.includes('application/pdf')) {
                            setFileType('pdf');
                        } else if (contentType?.includes('image/')) {
                            setFileType('image');
                        } else {
                            setFileType('unknown');
                        }
                    })
                    .catch(() => {
                        setFileType('unknown');
                    });
            } else {
                setFileType('unknown');
            }
        } else {
            setFileType('unknown');
        }
    }, [fileUrl, fileName, displayUrl, isLocalFile]);

    const openInNewTab = () => {
        window.open(displayUrl, '_blank');
    };

    const handleDownload = async () => {
        setIsLoading(true);
        try {
            let finalUrl = displayUrl;

            // Handle different URL types
            if (!isLocalFile && displayUrl.startsWith('http')) {
                const response = await axios({
                    url: displayUrl,
                    method: 'GET',
                    responseType: 'blob',
                    headers: {
                        'Accept': 'application/pdf',
                    }
                });

                // Create blob URL from response
                const blob = new Blob([response.data], { type: 'application/pdf' });
                finalUrl = window.URL.createObjectURL(blob);
            }

            // Open in new tab instead of downloading
            window.open(finalUrl, '_blank');

            // Cleanup blob URL if created (after a delay to ensure tab opens)
            if (finalUrl.startsWith('blob:')) {
                setTimeout(() => window.URL.revokeObjectURL(finalUrl), 1000);
            }

        } catch (error) {
            console.error('Failed to open PDF:', error);
            alert('Failed to open file. Please try again or contact support.');
        } finally {
            setIsLoading(false);
        }
    };

    const handlePreviewLoad = () => {
        setIsPreviewLoaded(true);
    };

    return (
        <Modal
            open={isOpen}
            onClose={() => {
                onClose();
                setIsPreviewLoaded(false); // Reset preview loading when modal closes
            }}
            sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(8px)', // <-- add blur effect
                backgroundColor: 'rgba(0, 0, 0, 0.4)', // <-- slightly dark background with transparency
            }}
        >
            <Box sx={{
                bgcolor: 'background.paper',
                boxShadow: 24,
                p: 4,
                borderRadius: 2,
                maxWidth: '90vw',
                maxHeight: '90vh',
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column',
            }}>
                {/* Download Button */}
                {/* <IconButton
                    sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        backgroundColor: 'primary.main',
                        color: 'white',
                        '&:hover': {
                            backgroundColor: 'primary.dark',
                        },
                        '&.Mui-disabled': {
                            backgroundColor: 'action.disabledBackground',
                            color: 'action.disabled',
                        },
                        boxShadow: 2,
                    }}
                    onClick={handleDownload}
                    disabled={isLoading || !displayUrl}
                    title="Download file"
                >
                    {isLoading ? (
                        <CircularProgress size={24} sx={{ color: 'white' }} />
                    ) : (
                        <Download />
                    )}
                </IconButton> */}

                {/* File content section */}
                {fileUrl ? (
                    <>
                        {!isPreviewLoaded && (
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '300px' }}>
                                <CircularProgress />
                            </Box>
                        )}
                        {fileType === 'pdf' ? (
                            <iframe
                                src={`${displayUrl}#toolbar=0&zoom=80`}
                                title="PDF Preview"
                                style={{
                                    width: '1000px',
                                    height: '85vh',
                                    border: 'none',
                                    display: isPreviewLoaded ? 'block' : 'none',
                                }}
                                onLoad={handlePreviewLoad}
                            />
                        ) : fileType === 'image' ? (
                            <img
                                src={displayUrl}
                                alt={fileName || 'File preview'}
                                style={{
                                    maxWidth: '100%',
                                    maxHeight: '80vh',
                                    objectFit: 'contain',
                                    display: isPreviewLoaded ? 'block' : 'none',
                                    margin: '0 auto',
                                }}
                                onLoad={handlePreviewLoad}
                            />
                        ) : (
                            <Box sx={{ textAlign: 'center', p: 3 }}>
                                <Typography variant="h6" gutterBottom>
                                    File: {fileName || 'Unknown file'}
                                </Typography>
                                <Typography variant="body2" sx={{ mb: 3 }}>
                                    This file type cannot be previewed directly.
                                </Typography>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    onClick={openInNewTab}
                                >
                                    Open in New Tab
                                </Button>
                            </Box>
                        )}
                    </>
                ) : (
                    <Typography variant="body1">No file to display</Typography>
                )}
            </Box>
        </Modal>
    );
}
