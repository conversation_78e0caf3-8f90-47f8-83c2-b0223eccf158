const RoleManagement = require('../Models/roleManagement');

// Get all roles
exports.getAllRoles = async (req, res, next) => {
    try {
        const roles = await RoleManagement.findAll();
        res.status(200).json(roles);
    } catch (error) {
        next(error); // Passes the error to the error-handling middleware
    }
};

// Get role by ID
exports.getRoleById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const role = await RoleManagement.findById(id);
        if (!role) {
            return res.status(404).json({ message: 'Role not found' });
        }
        res.status(200).json(role);
    } catch (error) {
        next(error);
    }
};

// Get role by name
exports.getRoleByName = async (req, res, next) => {
    try {
        const { name } = req.params;
        const role = await RoleManagement.findByName(name);
        res.status(200).json(role);
    } catch (error) {
        next(error);
    }
};

// Create a new role
exports.createRole = async (req, res, next) => {
    try {
        const data = req.body;
        const newRole = await RoleManagement.create(data);
        res.status(201).json({ id: newRole, message: 'Role created successfully' });
    } catch (error) {
        next(error);
    }
};

// Update a role by ID
exports.updateRole = async (req, res, next) => {
    try {
        const { id } = req.params;
        const data = req.body;
        const affectedRows = await RoleManagement.updateById(id, data);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Role not found' });
        }
        res.status(200).json({ message: 'Role updated successfully' });
    } catch (error) {
        next(error);
    }
};

// Delete a role by ID
exports.deleteRole = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await RoleManagement.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Role not found' });
        }
        res.status(204).json({ message: 'Role deleted successfully' });
    } catch (error) {
        next(error);
    }
};

// Reinstate a role by ID
exports.reinstateRole = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await RoleManagement.reinstate(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Role not found' });
        }
        res.status(200).json({ message: 'Role reinstated successfully' });
    } catch (error) {
        next(error);
    }
};

// Get roles by criteria
exports.getRoleByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await RoleManagement.findAll();
                break;
            case 'newLastWeek':
                data = await RoleManagement.newLastWeek();
                break;
            case 'newThisWeek':
                data = await RoleManagement.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await RoleManagement.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await RoleManagement.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await RoleManagement.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await RoleManagement.editedLastWeek();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
