import { createSlice } from '@reduxjs/toolkit';
import { getAllQuotationResponse, getQuotationResponseById, getQuotationDetailsByQuotationId, getAllQuotationsByUserId } from '../../actions/action';

const initialState = {
    quotationResponses: [],
    quotationResponse: {}, // Store responses separately for different sources
    quotationDetails: [],
    loading: false,
    error: null,
};

const quotationSlice = createSlice({
    name: 'quotationResponse',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Fetch All Quotations
            .addCase(getAllQuotationResponse.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllQuotationResponse.fulfilled, (state, action) => {
                state.loading = false;
                state.quotationResponses = action.payload;
            })
            .addCase(getAllQuotationResponse.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Fetch All Quotations by User ID
            .addCase(getAllQuotationsByUserId.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllQuotationsByUserId.fulfilled, (state, action) => {
                state.loading = false;
                state.quotationResponses = action.payload;
            })
            .addCase(getAllQuotationsByUserId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Fetch Quotation Response by ID (with sourceTable)
            .addCase(getQuotationResponseById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getQuotationResponseById.fulfilled, (state, action) => {
                state.loading = false;
                const { tableSource, data } = action.payload;
                state.quotationResponse[tableSource] = data;  // Store separately by tableSource
            })
            .addCase(getQuotationResponseById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Fetch Quotation Details by ID
            .addCase(getQuotationDetailsByQuotationId.pending, (state) => {
                state.loading = true;
            })
            .addCase(getQuotationDetailsByQuotationId.fulfilled, (state, action) => {
                state.loading = false;
                state.quotationDetails = action.payload;
            })
            .addCase(getQuotationDetailsByQuotationId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    },
});

export const { clearError } = quotationSlice.actions;
export default quotationSlice.reducer;
