import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import { deleteRole, getAllRoles, getFilterRoles, getRolesByName, reinstateRole } from '../../../redux/actions/action'
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';

const RoleListPage = () => {
  const [selectedOption, setSelectedOption] = useState('none');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRows, setSelectedRows] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [selectedItem, setSelectedItem] = useState(null);
  const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const roles = useSelector(state => state.roleManagementReducer.roles);
  const [sortedRoles, setSortedRoles] = useState([]);
  const permissions = usePermissions('Master', 'Role Management');

  useEffect(() => {
    dispatch(getAllRoles());
  }, [dispatch]);

  useEffect(() => {
    const fetchRoles = () => {
      dispatch(getFilterRoles(selectedOption));
    }
    fetchRoles();
  }, [selectedOption, dispatch]);

  useEffect(() => {
    const filterRolesByStatus = () => {
      if (statusFilter === 'all') {
        setSortedRoles(roles);
      } else if (statusFilter === 'none') {
        dispatch(getAllRoles());
      } else {
        setSortedRoles(roles.filter(role => role.status === (statusFilter === 'active' ? 1 : 0)))
      }
    }
    filterRolesByStatus();
  }, [statusFilter, roles, dispatch])

  const handleOpenDeletePopup = (item) => {
    setSelectedItem(item);
    setOpenDeletePopup(true);
  };

  const handleCloseDeletePopup = () => {
    setOpenDeletePopup(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    dispatch(deleteRole(selectedItem.id))
      .then(() => {
        dispatch(getAllRoles());
        setOpenDeletePopup(false);
        setOpenSuccessPopup(true);
      })
      .catch(error => {
        console.error("Failed to delete role:", error);
        setOpenDeletePopup(false);
      });
  };

  const handleCloseSuccessPopup = () => {
    setOpenSuccessPopup(false);
  };

  const handleAdd = () => {
    navigate('/dashboard/role-form');
  };

  const handleDelete = (id) => {
    handleOpenDeletePopup(roles.find(role => role.id === id));
  };

  const handleReinstate = (id) => {
    dispatch(reinstateRole(id))
      .then(() => {
        dispatch(getAllRoles());
      })
      .catch(error => {
        console.error("Failed to reinstate role:", error);
      });
  };

  const handleEdit = (id) => {
    navigate(`/dashboard/role-list/edit/${id}`);
  };

  const onSearch = (query) => {
    if (query === '') {
      dispatch(getAllRoles());
    } else {
      dispatch(getRolesByName(query));
    }
  };

  const handleSelectionChange = (id) => {
    setSelectedRows(prevSelected =>
      prevSelected.includes(id)
        ? prevSelected.filter(rowId => rowId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSelectAll = (isSelected) => {
    setSelectedRows(isSelected ? roles.map(role => role.id) : []);
  };

  const handleAllClick = () => setStatusFilter('all');
  const handleActiveClick = () => setStatusFilter('active');
  const handleInactiveClick = () => setStatusFilter('inactive');
  const handleRefreshClick = () => {
    setSelectedOption('none');
    dispatch(getAllRoles());
  }

  const columns = [
    { field: 'id', headerName: 'ID' },
    { field: 'role_name', headerName: 'Role Name' },
    { field: 'department_name', headerName: 'Department' },
  ];

  const dataMapping = {
    ID: 'id',
    'Role Name': 'role_name',
    'Department': 'department_name',
    'Status': 'status',
  };

  return (
    <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/image.png"
              alt="module icon"
              style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
            />
            <ModuleName moduleName="Role" pageName="List" />
          </Box>
          <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
            {permissions.can_add && (
            <Button
              onClick={handleAdd}
              sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
            >
              New
            </Button>
            )}
            <ExportToPDF
              data={sortedRoles.map(role => ({
                ...role,
                status: role.status === 1 ? 'Active' : 'Inactive'
              }))}
              headNames={['ID', 'Role Name', 'Department', 'Status']}
              selectedRows={selectedRows}
              imageUrl="/logo.png"
              watermarkUrl="/gray-logo.png"
              fileName="roles.pdf"
              dataMapping={dataMapping}
              headerTitle="Role Report"
            />
          </ButtonGroup>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingInline: '1rem', ml: 5 }}>
          <DropDown
            label=""
            value={selectedOption}
            onChange={(e) => setSelectedOption(e.target.value)}
            options={[
              { value: 'none', label: 'None' },
              { value: 'newLastWeek', label: 'New Last Week' },
              { value: 'newThisWeek', label: 'New this Week' },
              { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
              { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
              { value: 'editedLastWeek', label: 'Edited Last Week' },
              { value: 'editedThisWeek', label: 'Edited This Week' },
            ]}
          />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchBar placeholder="Search..." onSearch={onSearch} />
            <IconActions
              onAllClick={handleAllClick}
              onActiveClick={handleActiveClick}
              onInactiveClick={handleInactiveClick}
              onRefreshClick={handleRefreshClick}
            />
          </Box>
        </Box>

        <Box sx={{ mt: -1, maxHeight: '400px' }}>
          <CustomTable
            data={sortedRoles}
            columns={columns}
            onDelete={permissions.can_delete ? handleDelete : null}
            onEdit={permissions.can_edit ? handleEdit : null}
            onReinstate={handleReinstate}
            onSelectionChange={handleSelectionChange}
            selectedRows={selectedRows}
            onSelectAll={handleSelectAll}
          />
        </Box>
      </Box>

      <DeletePopup
        open={openDeletePopup}
        onClose={handleCloseDeletePopup}
        onConfirm={handleConfirmDelete}
        modulename={selectedItem ? selectedItem.role_name : ''}
      />
      <SuccessPopup
        open={openSuccessPopup}
        onClose={handleCloseSuccessPopup}
        modulename={selectedItem ? selectedItem.role_name : ''}
      />
    </Container>
  );
};

export default RoleListPage;
