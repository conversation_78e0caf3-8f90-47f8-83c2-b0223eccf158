exports.up = function (knex) {
    return knex.schema.createTable('payment_recon_logs', (table) => {
        table.increments('id').primary();
        table.string('transaction_id').notNullable();
        table.text('request_payload');
        table.text('response_payload');
        table.timestamp('created_at').defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('payment_recon_logs');
};