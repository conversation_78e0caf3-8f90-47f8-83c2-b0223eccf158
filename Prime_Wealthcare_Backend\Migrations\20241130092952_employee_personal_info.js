exports.up = function (knex) {
    return knex.schema.hasTable('employee_personal_info').then(function (exists) {
      if (!exists) {
        return knex.schema.createTable('employee_personal_info', function (table) {
          table.increments('id').primary();
  
          // Insurance company fields
          table.string('employee_full_name', 500).notNullable();
          table.string('gender', 500).notNullable();
          table.string('education', 500).notNullable();
          table.string('adhar_number', 500).notNullable();
          table.string('PAN_number', 500).notNullable().unique();
          table.string('personal_email', 500).notNullable();
          table.string('personal_mobile', 500).notNullable();
          table.timestamp('date_of_birth').notNullable();
          table.string('blood_group').notNullable();
          table.string('marital_status', 500).notNullable();
          table.timestamp('marriage_date').nullable();
          table.string('driving_license_number', 500).nullable();
          table.string('user_id', 500).notNullable();
          table.string('password', 500).notNullable();
          table.integer('role_id').unsigned().nullable();
          table.foreign('role_id').references('id').inTable('role_management').onDelete('CASCADE');
          table.string('branch_id', 500).nullable();
          //table.foreign('branch_id').references('id').inTable('imf_branches').onDelete('CASCADE');
          table.integer('first_reporting_manager_id').unsigned().nullable()
          .references('id').inTable('employee_personal_info').onDelete('CASCADE');
          table.integer('second_reporting_manager_id').unsigned().nullable()
          .references('id').inTable('employee_personal_info').onDelete('CASCADE');
          table.string('official_email', 500).nullable();
          table.string('official_mobile', 500).nullable();
          table.timestamp('date_of_joining').notNullable();
          table.string('emp_photo', 500).nullable();
          table.string('emp_adhar_front_pdf', 500).nullable();
          table.string('emp_adhar_back_pdf', 500).nullable();
          table.string('emp_PAN_pdf', 500).nullable();
          table.string('emp_signed_offer_letter_pdf', 500).nullable();
          table.string('emp_driving_license_pdf', 500).nullable();

          // Audit fields
          table.boolean('status').notNullable().defaultTo(true);
          table.integer('created_by').notNullable().defaultTo(1);
          table.integer('updated_by').notNullable().defaultTo(1);
          table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
          table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
  
         
        });
      }
    });
  };
  
  exports.down = function (knex) {
    return knex.schema.dropTableIfExists('employee_personal_info');
  };