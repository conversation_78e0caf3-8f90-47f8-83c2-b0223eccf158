exports.up = function (knex) {
    return knex.schema.createTable('soap_responses', function (table) {
        table.increments('id').primary();
        table.integer('quotation_id').unsigned();
        // Policy-level details
        table.decimal('base_premium', 10, 2);
        table.decimal('term_premium', 10, 2);
        table.decimal('family_discount_rate', 5, 2);
        table.decimal('family_discount', 10, 2);
        table.decimal('premium_without_service_tax', 10, 2);
        table.decimal('premium_with_load', 10, 2);
        table.decimal('premium_amount', 10, 2);
        table.decimal('service_tax_rate', 5, 2);
        table.decimal('service_tax', 10, 2);
        table.decimal('premium_with_service_tax', 10, 2);
        table.decimal('copay_percentage', 10, 2).nullable();
        table.decimal('co_pay_amount', 10, 2).nullable();
        // Member-specific details
        table.string('member_name');
        table.string('relation');
        table.string('cover_type');
        table.decimal('sum_insured', 10, 2);
        table.decimal('bmi', 5, 2);
        table.decimal('bmi_loading_percent', 5, 2);
        table.decimal('per_person_premium', 10, 2);

        // Response metadata
        table.string('status').defaultTo('PENDING');
        table.timestamps(true, true);
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('soap_responses');
};