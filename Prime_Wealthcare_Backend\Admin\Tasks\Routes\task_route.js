const express = require('express');
const TaskController = require('../Controllers/task_controller');
const router = express.Router();

//task
router.post('/', TaskController.createTask);

router.post('/comments/:id', TaskController.createComment);

router.post('/comments/reply/:id', TaskController.addCommentReply);

router.put('/task-notifications/:id', TaskController.updateTaskNotification);

router.put('/comment-notifications/:id', TaskController.updateCommentNotification);

router.put('/:id', TaskController.updateTask);

router.get('/task-details/:userId', TaskController.getTaskDetailsByUserId);

router.get('/task-notifications/:id', TaskController.getTaskNotifications);

router.get('/comment-notifications/:id', TaskController.getCommentNotifications);

router.get('/task-notifications', TaskController.getAllTaskNotifications);

router.get('/comment-notifications', TaskController.getAllCommentNotifications);

router.get('/', TaskController.getAllTasks);

router.get('/:id', TaskController.getTaskById);

router.get('/assigned-to/:id', TaskController.getTaskByAssignedTo);

router.get('/assigned-by/:id', TaskController.getTaskByAssignedBy);

router.delete('/:id', TaskController.deleteTaskById);

router.get('/comments/replies/:id', TaskController.getAllCommentReplies);




module.exports = router;


