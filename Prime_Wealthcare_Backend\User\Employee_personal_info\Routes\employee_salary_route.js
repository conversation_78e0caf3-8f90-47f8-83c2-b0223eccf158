const express = require('express');
const router = express.Router();
const EmployeeSalaryController = require('../Controllers/employee_salary_controller');

// Create a new Employee
router.post('/', EmployeeSalaryController.createEmployeeSalary);

// Get all Employees
router.get('/:id', EmployeeSalaryController.getAllEmployeeSalary); 

// Get Salary By Employee =[;]\[D
router.get('/edit_salary/:id', EmployeeSalaryController.getEmployeeSalaryByEmployeeId);

// Get Salary By Id
router.get('/id/:id', EmployeeSalaryController.getEmployeeSalaryById);

// Update Employee by ID
router.put('/:id', EmployeeSalaryController.updateEmployeeSalary);

// Soft delete Employee by ID
router.delete('/:id', EmployeeSalaryController.deleteEmployeeSalary);

module.exports = router;