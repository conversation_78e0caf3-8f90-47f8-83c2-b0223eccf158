const knexConfig = require('../../../../knexfile');
const knex = require('knex')(knexConfig.development);

const InsuranceType = {
  // Get all insurance types
  getAll: async () => {
    const response = await knex('insurance_type').select('*');

    return response;
  },

  // Get insurance type by id
  getById: (id) => {
    return knex('insurance_type').where('id', id).first();
  }
};

module.exports = InsuranceType;
