const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const knex = require('knex');
const knexConfig = require('../../../../knexfile');
const db = knex(knexConfig.development);

require('dotenv').config();

const SOAP_API_URL = process.env.SOAP_API_URLPDF; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTIONPDF;

const SOAP_BODY = (policyNumber) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
   <soapenv:Header/>
   <soapenv:Body>
      <tem:GetPDF>
         <!--Optional:-->
         <tem:PolicyNO>${policyNumber}</tem:PolicyNO>
      </tem:GetPDF>
   </soapenv:Body>
</soapenv:Envelope>`

const sendSOAPRequest = async (policyNumber) => {
    try {
        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };
        const requestBody = SOAP_BODY(policyNumber);
        console.log('SOAP Request Body:', requestBody);

        const response = await axios.post(SOAP_API_URL, requestBody, { headers });
        console.log('SOAP Response:', response.data);

        const jsonResponse = await parseStringPromise(response.data);

        const pdfUrl = jsonResponse['s:Envelope']['s:Body'][0]
        ['GetPDFResponse'][0]['GetPDFResult'][0];

        return {
            status: 'success',
            pdfUrl: pdfUrl
        };
    } catch (error) {
        console.error("SOAP Request Error Details: ", error);
        throw {
            error: {
                message: error.message || 'Failed to fetch policy PDF',
                timestamp: new Date().toISOString(),
                type: 'PDF_FETCH_ERROR'
            }
        };
    }
}

module.exports = {
    sendSOAPRequest
};