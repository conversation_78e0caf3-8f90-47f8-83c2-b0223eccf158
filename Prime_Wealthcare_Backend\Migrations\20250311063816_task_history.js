/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.createTable('task_history', function (table) {
        // Primary key
        table.increments('id').primary();

        // Foreign key to tasks table
        table.integer('task_id').unsigned().notNullable();
        table.foreign('task_id').references('id').inTable('tasks').onDelete('CASCADE');

        // Status enum
        table.enu('status', ['To-Do', 'In Progress', 'Completed']).notNullable();

        // Changed by reference
        table.integer('changed_by').unsigned().notNullable();
        table.foreign('changed_by').references('id').inTable('employee_personal_info').onDelete('CASCADE');


        // Timestamps
        table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
        table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
        table.integer('created_by').unsigned().nullable();
        table.integer('updated_by').unsigned().nullable();
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
    return knex.schema.dropTable('task_history');
};
