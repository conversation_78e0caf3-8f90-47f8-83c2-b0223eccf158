import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  updateCommissionRate,
  updateMultipleCommissionRates,
} from '../../../redux/actions/action'; // Adjust the import path as needed
//import { toast } from 'react-toastify';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  Grid,
  Box,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,

} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import CustomTextField from '../../../components/CustomTextField'; // Adjust the import path as needed
import ModuleName from '../../../components/table/ModuleName'; // Adjust the import path as needed
import DropDown from '../../../components/table/DropDown'; // Ensure this path is correct
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import './Pop.css'; // Import your custom CSS


const generateOptions = (start, end, step) => {
  const options = [];
  for (let i = start; i <= end; i += step) {
    options.push({ value: i, label: i.toLocaleString() });
  }
  return options;
};

const CommissionRateEditForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const commissionRate = useSelector((state) =>
    state.commissionRateReducer.commissionRates.find(
      (rate) => rate.id === parseInt(id)
    )
  );

  const [fixPercentValues, setFixPercentValues] = useState({});


  const [formData, setFormData] = useState({
    // First Set of Fields
    insurance_company_name: '',
    main_product_name: '',
    product_master_name: '',
    sub_product_name: '',
    commission_type: '',
    commission_source: '',
    policy_type: '',
    fixed_percentage: '',
    extra_percentage: '',
    rangeFrom: '',
    rangeTo: '',
    effectiveFrom: null,
    effectiveTo: null,
    // Second Set of Fields
    rangeFrom2: '',
    rangeTo2: '',
    extraPercent2: '',
    effectiveFrom2: null,
    effectiveTo2: null,
    showSecondSet: false, // Flag to show second set
  });

  const [errors, setErrors] = useState({
    // First Set of Fields
    rangeFrom: '',
    rangeTo: '',
    extraPercent: '',
    effectiveFrom: '',
    effectiveTo: '',
    // Second Set of Fields
    rangeFrom2: '',
    rangeTo2: '',
    extraPercent2: '',
    effectiveFrom2: '',
    effectiveTo2: '',
  });

  const [selectedOption, setSelectedOption] = useState('sum_insured');

  useEffect(() => {
    if (id && commissionRate) {
      setFormData({
        insurance_company_name: commissionRate.insurance_company_name || '',
        main_product_name: commissionRate.main_product_name || '',
        product_master_name: commissionRate.product_master_name || '',
        sub_product_name: commissionRate.sub_product_name || '',
        commission_type: commissionRate.commission_type || '',
        commission_source: commissionRate.commission_source || '',
        policy_type: commissionRate.policy_type || '',
        fixed_percentage:
          commissionRate.fixed_percentage !== undefined
            ? commissionRate.fixed_percentage
            : '',
        extra_percentage:
          commissionRate.extra_percentage !== undefined
            ? commissionRate.extra_percentage
            : '',
        rangeFrom: commissionRate.range_from || '',
        rangeTo: commissionRate.range_to || '',
        effectiveFrom: commissionRate.effective_from
          ? dayjs(commissionRate.effective_from)
          : null,
        effectiveTo: commissionRate.effective_to
          ? dayjs(commissionRate.effective_to)
          : null,
        rangeFrom2: commissionRate.range_from2 || '',
        rangeTo2: commissionRate.range_to2 || '',
        extraPercent2: commissionRate.extra_percentage2 || '',
        effectiveFrom2: commissionRate.effective_from2
          ? dayjs(commissionRate.effective_from2)
          : null,
        effectiveTo2: commissionRate.effective_to2
          ? dayjs(commissionRate.effective_to2)
          : null,
        showSecondSet: commissionRate.range_from2 ? true : false,
      });

      // If 'calculated_on' exists, set it
      if (commissionRate.calculated_on) {
        setSelectedOption(commissionRate.calculated_on);
      }
    }
  }, [id, commissionRate]);

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "fixed_percentage") {
      const numericValue = value === '' ? '' : parseFloat(value);

      // Validate the value for the fixed percentage
      if (value === '' || (numericValue >= 0 && numericValue <= 99)) {
        // Update both formData and fixPercentValues here
        setFixPercentValues((prevValues) => ({
          ...prevValues,
          [e.target.id]: numericValue // Keep track of fix percentage values
        }));
      }

      // Update formData for fixed_percentage
      setFormData((prevFormData) => ({
        ...prevFormData,
        [name]: numericValue, // Update with numeric value for fixed percentage
      }));
    } else {
      setFormData((prevFormData) => ({
        ...prevFormData,
        [name]: value, // For other fields
      }));
    }

    // Clear the error for the field being updated
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };


  const handleDateChange = (name, date) => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: date,
    }));

    // Clear the error for the date field being updated
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };

  const handleAddMore = () => {
    setFormData((prevData) => ({
      ...prevData,
      showSecondSet: true,
    }));
  };

  const handleDeleteSecondSet = () => {
    setFormData((prevData) => ({
      ...prevData,
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: null,
      effectiveTo2: null,
      showSecondSet: false,
    }));
    // Clear errors related to the second set
    setErrors((prevErrors) => ({
      ...prevErrors,
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: '',
      effectiveTo2: '',
    }));
  };

  const validateForm = () => {
    let valid = true;
    let newErrors = {
      rangeFrom: '',
      rangeTo: '',
      extraPercent: '',
      effectiveFrom: '',
      effectiveTo: '',
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: '',
      effectiveTo2: '',
    };

    // Fixed % validation remains independent
    if (!formData.fixed_percentage) {
      newErrors.fixedPercentage = '"Fixed %" is required.';
      valid = false;
    }

    // Validate for first set of fields
    if (formData.rangeFrom || formData.rangeTo || formData.extra_percentage) {
      if (!formData.rangeFrom) {
        newErrors.rangeFrom = '"Range From" is required.';
        valid = false;
      }
      if (!formData.rangeTo) {
        newErrors.rangeTo = '"Range To" is required.';
        valid = false;
      }
      if (formData.extra_percentage && !formData.extra_percentage) {
        newErrors.extraPercent = '"Extra %" is required when ranges are filled.';
        valid = false;
      }

      // Validate date fields
      if (!formData.effectiveFrom) {
        newErrors.effectiveFrom = '"Effective From" date is required.';
        valid = false;
      }
      if (!formData.effectiveTo) {
        newErrors.effectiveTo = '"Effective To" date is required.';
        valid = false;
      }

      // Range and date validations
      if (formData.rangeFrom && formData.rangeTo) {
        if (Number(formData.rangeFrom) >= Number(formData.rangeTo)) {
          newErrors.rangeFrom = '"Range From" should be less than "Range To".';
          newErrors.rangeTo = '"Range To" should be greater than "Range From".';
          valid = false;
        }
      }
      if (formData.effectiveFrom && formData.effectiveTo) {
        if (!dayjs(formData.effectiveFrom).isBefore(dayjs(formData.effectiveTo))) {
          newErrors.effectiveFrom = '"Effective From" should be before "Effective To".';
          newErrors.effectiveTo = '"Effective To" should be after "Effective From".';
          valid = false;
        }
      }
    }

    // Second set validation
    if (formData.rangeFrom2 || formData.rangeTo2 || formData.extraPercent2) {
      if (!formData.rangeFrom2) {
        newErrors.rangeFrom2 = '"Range From 2" is required.';
        valid = false;
      }
      if (!formData.rangeTo2) {
        newErrors.rangeTo2 = '"Range To 2" is required.';
        valid = false;
      }
      if (formData.extraPercent2 && !formData.extraPercent2) {
        newErrors.extraPercent2 = '"Extra % 2" is required when ranges are filled.';
        valid = false;
      }

      // Validate date fields
      if (!formData.effectiveFrom2) {
        newErrors.effectiveFrom2 = '"Effective From 2" date is required.';
        valid = false;
      }
      if (!formData.effectiveTo2) {
        newErrors.effectiveTo2 = '"Effective To 2" date is required.';
        valid = false;
      }

      // Range and date validations
      if (formData.rangeFrom2 && formData.rangeTo2) {
        if (Number(formData.rangeFrom2) >= Number(formData.rangeTo2)) {
          newErrors.rangeFrom2 = '"Range From 2" should be less than "Range To 2".';
          newErrors.rangeTo2 = '"Range To 2" should be greater than "Range From 2".';
          valid = false;
        }
      }
      if (formData.effectiveFrom2 && formData.effectiveTo2) {
        if (!dayjs(formData.effectiveFrom2).isBefore(dayjs(formData.effectiveTo2))) {
          newErrors.effectiveFrom2 = '"Effective From 2" should be before "Effective To 2".';
          newErrors.effectiveTo2 = '"Effective To 2" should be after "Effective From 2".';
          valid = false;
        }
      }
    }

    setErrors(newErrors);
    return valid;
  };




  const handleSave = async () => {
    if (!validateForm()) {
      toast.error('Please fix the validation errors before saving.');
      return;
    }

    // Prepare the data for submission
    const effectiveFrom = formData.effectiveFrom
      ? dayjs(formData.effectiveFrom).format('YYYY-MM-DD')
      : null;
    const effectiveTo = formData.effectiveTo
      ? dayjs(formData.effectiveTo).format('YYYY-MM-DD')
      : null;
    const effectiveFrom2 = formData.effectiveFrom2
      ? dayjs(formData.effectiveFrom2).format('YYYY-MM-DD')
      : null;
    const effectiveTo2 = formData.effectiveTo2
      ? dayjs(formData.effectiveTo2).format('YYYY-MM-DD')
      : null;

    const commissionRateData = {
      range_from: formData.rangeFrom,
      range_to: formData.rangeTo,
      extra_percentage: parseFloat(formData.extra_percentage),
      effective_from: effectiveFrom,
      effective_to: effectiveTo,
      range_from2: formData.rangeFrom2 || null,
      range_to2: formData.rangeTo2 || null,
      extra_percentage2: formData.extraPercent2
        ? parseFloat(formData.extraPercent2)
        : null,
      effective_from2: effectiveFrom2,
      effective_to2: effectiveTo2,
      calculated_on: selectedOption,
    };

    // Prepare the updates for fix percentages
    const updates = Object.keys(fixPercentValues)
      .filter(id => fixPercentValues[id] !== undefined && fixPercentValues[id] !== '')
      .map(id => ({
        id: parseInt(id), // Convert id to integer
        fixed_percentage: parseFloat(fixPercentValues[id]) // Include fixed_percentage
      }));

    if (updates.length === 0 && Object.values(commissionRateData).every(value => value === '')) {
      toast.warning("No changes made to save.");
      return;
    }

    let fixPercentUpdated = false;
    let extraPercentUpdated = false;

    try {
      // Dispatch updates for Fix % if there are any changes
      if (updates.length > 0) {
        await dispatch(updateMultipleCommissionRates(updates));
        fixPercentUpdated = true;
      }

      // Dispatch the update for other commission rate data
      if (Object.values(commissionRateData).some(value => value !== '')) {
        await dispatch(updateCommissionRate({ id, updateData: commissionRateData }));
        extraPercentUpdated = true;
      }

      // Show a common success message if any updates were made
      if (fixPercentUpdated || extraPercentUpdated) {
        toast.success("Updated successfully.");
      }
      // Navigate to the commission rate list page
      navigate('/dashboard/commission-rate-list');
    } catch (error) {
      console.error('Error saving commission rates:', error);
      toast.error(`Failed to update commission rates: ${error.message}`);
    }
  };


  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
    
      <Box sx={{ padding: '20px' }}>
        {/* Header Section */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={8} md={8} style={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/image.png"
              alt="module icon"
              style={{
                width: '20px',
                padding: '1px ',
                marginLeft: '20px',
                backgroundColor: 'green',
              }}
            />

            <ModuleName moduleName="Commission Rate" pageName="Edit" />

          </Grid>
          <Grid
            item
            xs={4}
            md={4}
            style={{ display: 'flex', justifyContent: 'flex-end' }}
          >
            {commissionRate?.status !== 0 && (
              <Button
                variant="outlined"
                size="small"
                sx={{
                  maxWidth: '100px',
                  width: '100%',
                  mx: 0.5,
                  color: 'green',
                  borderColor: 'green',
                  // mt: 3,
                  textTransform: 'none',
                }}
                onClick={handleSave}
              >
                Update
              </Button>



            )}
            <Button
              variant="outlined"
              size="small"
              sx={{
                maxWidth: '100px',
                width: '100%',
                mx: 1.5,
                color: 'red',
                borderColor: 'red',
                // mt: 3,
                textTransform: 'none',
              }}
              onClick={() => navigate('/dashboard/commission-rate-list')}
            >
              Cancel
            </Button>
          </Grid>
        </Grid>

        {/* Commission Rate Information Header */}
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box
              sx={{
                backgroundColor: '#f0f0f0',
                padding: '14px',
                borderRadius: '4px',
                height: '60px',
              }}
            >
              <h2>Commission Rate Information</h2>
            </Box>
          </Grid>

          {/* First Set of Fields */}
          <Grid item xs={12}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Insurance Company"
                  name="insurance_company_name"
                  value={formData.insurance_company_name || ''}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Main Product"
                  name="main_product_name"
                  value={formData.main_product_name || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Product Master"
                  name="product_master_name"
                  value={formData.product_master_name || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Sub Product"
                  name="sub_product_name"
                  value={formData.sub_product_name || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Commission Type"
                  name="commission_type"
                  value={formData.commission_type || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Commission Source"
                  name="commission_source"
                  value={formData.commission_source || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Policy Type"
                  name="policy_type"
                  value={formData.policy_type || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Fix %"
                  name="fixed_percentage"
                  type="number"
                  id={commissionRate.id} // Make sure the ID is set correctly

                  value={formData.fixed_percentage || ''}
                  onChange={handleInputChange}
                  isRequired
                  error={Boolean(errors.fixed_percentage)}
                  helperText={errors.fixed_percentage}
                  disabled={id && commissionRate?.status === 0}  // Disable if status is 0
                />
              </Grid>

            </Grid>
          </Grid>

          {/* Add Extra % Section */}
          <Grid item xs={12}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                padding: '10px',
                borderRadius: '5px',
                border: '1px solid #ccc',
                marginTop: '20px',
              }}
            >
              <span style={{ marginRight: 'auto', fontWeight: 'bold' }}>Add Extra%</span>
              <RadioGroup row value={selectedOption} onChange={handleOptionChange}
                disabled={id && commissionRate?.status === 0} >
                <FormControlLabel value="sum_insured" control={<Radio />} label="Sum Insured"
                  disabled={id && commissionRate?.status === 0}  // Disable this option

                />
                <FormControlLabel value="premium" control={<Radio />} label="Premium"
                  disabled={id && commissionRate?.status === 0}  // Disable this option
                />
              </RadioGroup>
            </Box>
          </Grid>

          {/* Dynamic Fields After Add Extra% */}
          <Grid item xs={12}>
            <Grid container spacing={3}>

              <Grid item xs={12} sm={6} md={3}>
                <DropDown
                  label="Range From"
                  name="rangeFrom"
                  options={generateOptions(50000, 1000000, 50000)}
                  value={formData.rangeFrom}
                  onChange={handleInputChange}
                  fullWidth
                  required
                  error={Boolean(errors.rangeFrom)}
                  helperText={errors.rangeFrom}
                  disabled={id && commissionRate?.status === 0}
                />
              </Grid>


              <Grid item xs={12} sm={6} md={3}>
                <DropDown
                  label="Range To"
                  name="rangeTo"
                  options={generateOptions(50000, 1000000, 50000)}
                  value={formData.rangeTo}
                  onChange={handleInputChange}
                  fullWidth
                  required
                  error={Boolean(errors.rangeTo)}
                  helperText={errors.rangeTo}
                  disabled={id && commissionRate?.status === 0}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Extra %"
                  name="extra_percentage"
                  type="number"
                  value={formData.extra_percentage || ''}
                  onChange={handleInputChange}
                  isRequired
                  error={Boolean(errors.extraPercent)}
                  helperText={errors.extraPercent}
                  disabled={id && commissionRate?.status === 0}
                />
              </Grid>

              {/* Effective From */}
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Effective From"
                  value={formData.effectiveFrom}
                  onChange={(newValue) => handleDateChange('effectiveFrom', newValue)}
                  disabled={id && commissionRate?.status === 0}
                  renderInput={(params) => (
                    <CustomTextField
                      {...params}
                      label="Effective From"
                      name="effectiveFrom"
                      required
                      error={Boolean(errors.effectiveFrom)}
                      helperText="Select the starting date for the commission rate."
                    />
                  )}
                  format="DD/MM/YYYY"
                  className="date-picker"
                  PopperProps={{
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 10],
                        },
                      },
                    ],
                  }}
                  calendarClassName="custom-calendar"
                />
              </Grid>

              {/* Effective To */}
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Effective To"
                  value={formData.effectiveTo}
                  onChange={(newValue) => handleDateChange('effectiveTo', newValue)}
                  minDate={formData.effectiveFrom} // Disable dates before 'Effective From'
                  disabled={id && commissionRate?.status === 0}
                  renderInput={(params) => (
                    <CustomTextField
                      {...params}
                      label="Effective To"
                      name="effectiveTo"
                      required
                      error={Boolean(errors.effectiveTo)}
                      helperText="Select the starting date for the commission rate."
                    />
                  )}
                  format="DD/MM/YYYY"
                  className="date-picker"
                  PopperProps={{
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 10],
                        },
                      },
                    ],
                  }}
                  calendarClassName="custom-calendar"
                />
              </Grid>
            </Grid>
          </Grid>

          {/* Second Set of Fields */}
          {formData.showSecondSet && (
            <Grid item xs={12}>
              <Grid container spacing={3}>
                {/* Range From 2 */}
                <Grid item xs={12} sm={6} md={3}>
                  <DropDown
                    label="Range From"
                    name="rangeFrom2"
                    options={generateOptions(50000, 1000000, 50000)}
                    value={formData.rangeFrom2}
                    onChange={handleInputChange} // Corrected onChange
                    fullWidth
                    required={
                      formData.rangeFrom2 ||
                      formData.rangeTo2 ||
                      formData.extraPercent2 ||
                      formData.effectiveFrom2 ||
                      formData.effectiveTo2
                    }
                    error={Boolean(errors.rangeFrom2)}
                    helperText={errors.rangeFrom2}
                    disabled={id && commissionRate?.status === 0}
                  />
                </Grid>

                {/* Range To 2 */}
                <Grid item xs={12} sm={6} md={3}>
                  <DropDown
                    label="Range To"
                    name="rangeTo2"
                    options={generateOptions(50000, 1000000, 50000)}
                    value={formData.rangeTo2}
                    onChange={handleInputChange} // Corrected onChange
                    minDate={formData.effectiveFrom} // Disable dates before 'Effective From'
                    fullWidth
                    required={
                      formData.rangeFrom2 ||
                      formData.rangeTo2 ||
                      formData.extraPercent2 ||
                      formData.effectiveFrom2 ||
                      formData.effectiveTo2
                    }
                    error={Boolean(errors.rangeTo2)}
                    helperText={errors.rangeTo2}
                    disabled={id && commissionRate?.status === 0}
                  />
                </Grid>

                {/* Extra % 2 */}
                <Grid item xs={12} sm={6} md={3}>
                  <CustomTextField
                    label="Extra % "
                    name="extraPercent2"
                    type="number"
                    value={formData.extraPercent2 || ''}
                    onChange={handleInputChange}
                    required={
                      formData.rangeFrom2 ||
                      formData.rangeTo2 ||
                      formData.extraPercent2 ||
                      formData.effectiveFrom2 ||
                      formData.effectiveTo2
                    }
                    error={Boolean(errors.extraPercent2)}
                    helperText={errors.extraPercent2}
                    disabled={id && commissionRate?.status === 0}
                  />
                </Grid>

                {/* Effective From 2 */}
                <Grid item xs={12} sm={6} md={3}>
                  <DatePicker
                    label="Effective From"
                    value={formData.effectiveFrom2}
                    onChange={(newValue) => handleDateChange('effectiveFrom2', newValue)}
                    disabled={id && commissionRate?.status === 0}
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        label="Effective From"
                        name="effectiveFrom2"
                        required={
                          formData.rangeFrom2 ||
                          formData.rangeTo2 ||
                          formData.extraPercent2 ||
                          formData.effectiveFrom2 ||
                          formData.effectiveTo2
                        }
                        error={Boolean(errors.effectiveFrom2)}
                        helperText={errors.effectiveFrom2}
                      />
                    )}
                    format="DD/MM/YYYY"
                    className="date-picker"
                    PopperProps={{
                      modifiers: [
                        {
                          name: 'offset',
                          options: {
                            offset: [0, 10],
                          },
                        },
                      ],
                    }}
                    calendarClassName="custom-calendar"
                  />
                </Grid>

                {/* Effective To 2 */}
                <Grid item xs={12} sm={6} md={3}>
                  <DatePicker
                    label="Effective To"
                    value={formData.effectiveTo2}
                    minDate={formData.effectiveFrom2} // Correct reference
                    onChange={(newValue) => handleDateChange('effectiveTo2', newValue)}
                    disabled={id && commissionRate?.status === 0}
                    renderInput={(params) => (
                      <CustomTextField
                        {...params}
                        label="Effective To 2"
                        name="effectiveTo2"
                        required={
                          formData.rangeFrom2 ||
                          formData.rangeTo2 ||
                          formData.extraPercent2 ||
                          formData.effectiveFrom2 ||
                          formData.effectiveTo2
                        }
                        error={Boolean(errors.effectiveTo2)}
                        helperText={errors.effectiveTo2}
                      />
                    )}
                    format="DD/MM/YYYY"
                    className="date-picker"
                    PopperProps={{
                      modifiers: [
                        {
                          name: 'offset',
                          options: {
                            offset: [0, 10],
                          },
                        },
                      ],
                    }}
                    calendarClassName="custom-calendar"
                  />
                </Grid>
              </Grid>
            </Grid>
          )}

          {/* Add More / Delete Buttons */}
          <Grid item xs={12}>
            {!formData.showSecondSet ? (
              <Button
                variant="outlined"
                size="small"
                onClick={handleAddMore}
              >
                Add More
              </Button>
            ) : (
              <Button
                variant="outlined"
                size="small"
                color="secondary"
                startIcon={<DeleteIcon />}
                onClick={handleDeleteSecondSet}
              >
                Delete
              </Button>
            )}
          </Grid>
        </Grid>
      </Box>
    </LocalizationProvider>
  );
};

export default CommissionRateEditForm;
