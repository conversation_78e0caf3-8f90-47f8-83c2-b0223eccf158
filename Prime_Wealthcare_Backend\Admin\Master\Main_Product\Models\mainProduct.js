const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new product
const create = async (data) => {
    try {
        const { result } = await db('main_product').insert(data);

        return result;
    } catch (error) {
        console.error("Error creating main product:", error);
        throw error;
    }
};

// Find all products
const findAll = async () => {
    try {
        return await db('main_product').select('*');
    } catch (error) {
        throw error;
    }
};

// Find product by ID
const findById = async (id) => {
    try {
        const product = await db('main_product').where({ id }).first();
        return product;
    } catch (error) {
        throw error;
    }
};

// Find products by product name (partial match)
const findByName = async (productName) => {
    try {
        const products = await db('main_product')
            .where('main_product', 'like', `%${productName}%`); // Ensure 'product_name' is the correct column
        return products;
    } catch (error) {
        console.error("Error finding products by name:", error);
        throw error;
    }
};

const updateById = async (id, data) => {
    try {
        // Add the formatted updated_at field to the data object
        data.updated_at = getCurrentTimestamp();

        const result = await db('main_product')
            .where({ id })
            .update(data);  // Use the passed data object for the update
        return result;
    } catch (error) {
        throw error;
    }
};



// Delete product by ID (soft delete by updating status)
const deleteById = async (id) => {
    try {
        const result = await db('main_product').where({ id }).update({ status: 0 , updated_at: getCurrentTimestamp()});
        return result;
    } catch (error) {
        throw error;
    }
};

// Reinstate product by ID
const reinstate = async (id) => {
    try {
        const result = await db('main_product').where({ id }).update({ status: 1 , updated_at: getCurrentTimestamp()});
        return result;
    } catch (error) {
        throw error;
    }
};

const newLastWeek = async () => {
    try {
        return await db('main_product')
            .where('created_at', '<', db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'));
    } catch (error) {
        throw error;
    }
};


// New products created this week
const newThisWeek = async () => {
    try {
        const query = db('main_product')
            .whereBetween('created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ]);
        return await query;
    } catch (error) {
        throw error;
    }
};


// Deactivated products updated this week
const deactivatedThisWeek = async () => {
    try {
        return await db('main_product')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);
    } catch (error) {
        throw error;
    }
};

// Deactivated products updated last week
const deactivatedLastWeek = async () => {
    try {
        const query = db('main_product')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);
        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited products updated this week
const editedThisWeek = async () => {
    try {
        const query = db('main_product')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited products updated last week
const editedLastWeek = async () => {
    try {
        return await db('main_product')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findAll,
    findById,
    findByName,
    updateById,
    deleteById,
    reinstate,
    newLastWeek,
    newThisWeek,
    deactivatedThisWeek,
    deactivatedLastWeek,
    editedThisWeek,
    editedLastWeek
};
