const knexConfig = require('../../../../knexfile');
const knex = require('knex')(knexConfig.development);

const formatDate = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};

const getAllInsuranceBranches = async () => {

  const formatDate = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  };
  try {
    const insuranceBranches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id') // Join with insurance_company
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      }) // Join with agency_code
      .select(
        'insurance_branch.*', // Select all columns from insurance branch
        'insurance_company.insurance_company_name', // Select insurance company name
        'agency_code.agency_code', // Select the agency code
        'agency_code.agent_name', // Select additional fields from agency_code if needed
        'agency_code.license_no',
        'agency_code.license_valid_from',
        'agency_code.license_valid_till',
        'insurance_branch.status' // Add status here
      );
    // Format the dates
    insuranceBranches.forEach(branch => {
      branch.license_valid_from = formatDate(branch.license_valid_from);
      branch.license_valid_till = formatDate(branch.license_valid_till);
    });

    return insuranceBranches;
  } catch (error) {
    console.error('Error fetching insurance branches:', error);
    throw error;
  }
};

const getInsuranceBranchById = async (id) => {


  try {
    // Ensure that ID is valid
    if (!id) {
      throw new Error('Invalid input: ID is missing');
    }

    const insuranceBranch = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id') // Join with insurance_company
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      }) // Join with agency_code
      .where('insurance_branch.id', id)
      .select(
        'insurance_branch.*', // Select all columns from the insurance branch
        'insurance_company.insurance_company_name', // Select insurance company name
        'agency_code.agency_code', // Select the agency code
        'agency_code.agent_name', // Select additional fields from agency_code if needed
        'agency_code.license_no',
        'agency_code.license_valid_from',
        'agency_code.license_valid_till',
        'insurance_branch.status' // Add status here
      )
      .first(); // Fetch the first matching record

    if (!insuranceBranch) {
      throw new Error('Branch not found with the provided ID');
    }

    // Format the dates
    insuranceBranch.license_valid_from = formatDate(insuranceBranch.license_valid_from);
    insuranceBranch.license_valid_till = formatDate(insuranceBranch.license_valid_till);

    return insuranceBranch;
  } catch (error) {
    console.error('Error fetching insurance branch by ID:', error);
    throw error;
  }
};
const getInsuranceBranchByInsuranceCompanyId = async (id) => {

  try {
    // Ensure that ID is valid
    if (!id) {
      throw new Error('Invalid input: ID is missing');
    }

    const insuranceBranch = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id') // Join with insurance_company
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      }) // Join with agency_code
      .where('insurance_branch.insurance_company_id', id)
      .where('insurance_branch.status', 1)
      .select(
        'insurance_branch.*', // Select all columns from the insurance branch
        'insurance_company.insurance_company_name', // Select insurance company name
        'agency_code.agency_code', // Select the agency code
        'agency_code.agent_name', // Select additional fields from agency_code if needed
        'agency_code.license_no',
        'agency_code.license_valid_from',
        'agency_code.license_valid_till',
        'insurance_branch.status' // Add status here
      )
    if (!insuranceBranch) {
      throw new Error('Branch not found with the provided ID');
    }
    return insuranceBranch;
  } catch (error) {
    console.error('Error fetching insurance branch by ID:', error);
    throw error;
  }
};


const insertInsuranceBranch = async (data) => {
  try {

    await knex('insurance_branch')
      .insert(data);

  } catch (error) {
    console.error('Error inserting insurance branch:', error);
    throw error;
  }
};

const updateInsuranceBranch = async (id, data) => {
  try {
    // Ensure that there is valid data to update
    if (!id || Object.keys(data).length === 0) {
      throw new Error('Invalid input: ID or update data missing');
    }

    // Perform the update
    const result = await knex('insurance_branch')
      .where({ id })
      .update(data);

    if (result === 0) {
      throw new Error('Failed to update the branch or no branch was found with the provided ID');
    }

    return result;
  } catch (error) {
    console.error('Error updating insurance branch:', error);
    throw error;
  }
};


//Soft delete insurance branch by ID
const deleteInsuranceBranch = async (branchId) => {
  try {
    const branch = await knex('insurance_branch')
      .where({ id: branchId })
      .first();

    if (!branch) {
      throw new Error("Branch not found");
    }

    const { insurance_co_branch_name, insurance_company_id } = branch;

    // Soft delete in the insurance_branch table
    await knex('insurance_branch')
      .where({ insurance_co_branch_name, insurance_company_id })
      .update({
        status: 0,  // Soft delete status
        updated_at: new Date()
      });

    // Soft delete in the agency_code table using the same fields
    await knex('agency_code')
      .where({ insurance_co_branch_name, insurance_company_id }) // Match on both fields
      .del();

    return true;
  } catch (error) {
    throw new Error("Failed to soft delete the branch and agency code: " + error.message);
  }
};

// Reinstate insurance branch by ID
const reinstateInsuranceBranch = async (branchId) => {
  try {
    const branch = await knex('insurance_branch')
      .where({ id: branchId })
      .first();

    if (!branch) {
      throw new Error("Branch not found");
    }

    const { insurance_co_branch_name, insurance_company_id } = branch;

    // Reinstate in the insurance_branch table
    await knex('insurance_branch')
      .where({ insurance_co_branch_name, insurance_company_id })
      .update({
        status: 1,  // Reinstate status
        updated_at: new Date()
      });

    // Check if the agency code was deleted when the branch was soft-deleted
    const agencyCode = await knex('agency_code')
      .where({ insurance_co_branch_name, insurance_company_id })
      .first();

    // If no agency code exists, allow creating a new one
    if (!agencyCode) {
      // Logic to allow the user to enter a new agency code
      return { message: "Branch reinstated. You can now create a new agency code." };
    }

    // If an agency code exists, return a message stating the branch is reinstated but agency code remains unchanged
    return { message: "Branch reinstated. The existing agency code remains active." };

  } catch (error) {
    throw new Error("Failed to reinstate the branch and agency code: " + error.message);
  }
};

const getBranchAndAgencyCode = async () => {
  try {
    const data = await knex('insurance_branch')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'agency_code.*'
      );

    return data;
  } catch (error) {
    console.error('Error fetching all branch and agency code data:', error);
    throw error;
  }
};

const createBranchAndAgencyCode = async (branchData, agencyCodeData) => {
  try {
    await knex.transaction(async (trx) => {
      // Insert branch data into 'insurance_branch' table
      const [branchId] = await trx('insurance_branch').insert(branchData).returning('id');

      // Retrieve the branch based on insurance_company_id and insurance_co_branch_name
      const branch = await trx('insurance_branch')
        .where({
          insurance_company_id: branchData.insurance_company_id,
          insurance_co_branch_name: branchData.insurance_co_branch_name
        })
        .first();

      // Insert agency code into 'agency_code' table
      agencyCodeData.insurance_company_id = branch.insurance_company_id;
      agencyCodeData.insurance_co_branch_name = branch.insurance_co_branch_name;
      await trx('agency_code').insert(agencyCodeData);
    });

    return { message: "Branch and Agency Code created successfully!" };
  } catch (error) {
    console.error('Error creating branch and agency code:', error);
    throw new Error('Failed to create branch and agency code');
  }
};

const getInsuranceBranchesByName = async (name) => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', 'insurance_branch.insurance_co_branch_name', 'agency_code.insurance_co_branch_name')
      .select(
        'insurance_branch.id',
        'insurance_branch.insurance_co_branch_name',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'insurance_branch.*',
        'agency_code.*'
      )
      .where(function () {
        this.where('insurance_branch.insurance_co_branch_name', 'LIKE', `%${name}%`)
          .orWhere('insurance_company.insurance_company_name', 'LIKE', `%${name}%`)
          .orWhere('agency_code.agency_code', 'LIKE', `%${name}%`);
      });
    return branches;
  } catch (error) {
    console.error(`Error fetching insurance branches by name: ${name}`, error);
    throw error;
  }
};


const newLastWeek = async () => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'agency_code.*'
      )
      .whereBetween('insurance_branch.created_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching branches created last week:', error);
    throw error;
  }
};

// Fetch new branches created this week
const newThisWeek = async () => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'agency_code.*'
      )
      .whereBetween('insurance_branch.created_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
        knex.raw('NOW()')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching branches created this week:', error);
    throw error;
  }
};

// Fetch deactivated branches updated this week
const deactivatedThisWeek = async () => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'agency_code.*'
      )
      .where('insurance_branch.status', 0) // Assuming 0 means deactivated
      .whereBetween('insurance_branch.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
        knex.raw('NOW()')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching deactivated branches updated this week:', error);
    throw error;
  }
};

// Fetch deactivated branches updated last week
const deactivatedLastWeek = async () => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'agency_code.*'
      )
      .where('insurance_branch.status', 0) // Assuming 0 means deactivated
      .whereBetween('insurance_branch.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching deactivated branches updated last week:', error);
    throw error;
  }
};

const editedThisWeek = async () => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'agency_code.*'
      )
      .whereBetween('insurance_branch.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
        knex.raw('NOW()')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching edited branches updated this week:', error);
    throw error;
  }
};

// Find edited products updated last week
const editedLastWeek = async () => {
  try {
    const branches = await knex('insurance_branch')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('agency_code', function () {
        this.on('insurance_branch.insurance_co_branch_name', '=', 'agency_code.insurance_co_branch_name')
          .andOn('insurance_branch.insurance_company_id', '=', 'agency_code.insurance_company_id');
      })
      .select(
        'insurance_branch.*',
        'insurance_company.insurance_company_name',
        'agency_code.agency_code',
        'agency_code.*'
      )
      .whereBetween('insurance_branch.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching edited branches updated last week:', error);
    throw error;
  }
};



module.exports = {
  getAllInsuranceBranches,
  insertInsuranceBranch,
  updateInsuranceBranch,
  deleteInsuranceBranch,
  reinstateInsuranceBranch,
  getBranchAndAgencyCode,
  getInsuranceBranchById,
  getInsuranceBranchByInsuranceCompanyId,
  getInsuranceBranchesByName,
  createBranchAndAgencyCode,
  newLastWeek,
  newThisWeek,
  deactivatedThisWeek,
  deactivatedLastWeek,
  editedLastWeek,
  editedThisWeek

};

