import React, { useEffect, useState } from 'react';
import Avatar from '@mui/material/Avatar';
import PersonIcon from '@mui/icons-material/Person';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';

const host = process.env.REACT_APP_HOST;
const port = process.env.REACT_APP_PORT;

const AvatarImage = ({
    src,
    alt = '',
    size = 40,
    isLocalFile = false,
    sx = {},
    ...props
}) => {
    const [displayUrl, setDisplayUrl] = useState('');
    const [loading, setLoading] = useState(!!src);
    const [error, setError] = useState(false);

    const getInitial = (name) => {
        if (!name) return '';
        const words = name.trim().split(' ');
        return words[0][0]?.toUpperCase() || '';
    };

    useEffect(() => {
        if (!src) {
            setDisplayUrl('');
            setLoading(false);
            return;
        }
        if (isLocalFile) {
            setDisplayUrl(src);
        } else if (src.startsWith('http://') || src.startsWith('https://')) {
            setDisplayUrl(src);
        } else {
            const normalizedPath = src
                .replace(/\\/g, '/') // Convert backslashes to forward slashes
                .replace(/^([A-Z]):/i, '$1') // Remove colon from drive letter
                .replace(/\/\//g, '/'); // Replace any double slashes with single

            const link = process.env.REACT_APP_IMAGE_URL;
            setDisplayUrl(`${link}${normalizedPath}`);
        }
        setLoading(true);
        setError(false);
    }, [src, isLocalFile]);

    const shouldShowInitial = (!displayUrl || error || loading);

    return (
        <Box sx={{ position: 'relative', width: size, height: size, display: 'inline-block' }}>
            <Avatar
                alt={alt}
                sx={{
                    width: size,
                    height: size,
                    fontSize: size * 0.4,
                    ...sx,
                }}
                {...props}
            >
                {shouldShowInitial ? (
                    getInitial(alt) || <PersonIcon fontSize="medium" />
                ) : null}
                {displayUrl && !error && (
                    <img
                        src={displayUrl}
                        alt={alt}
                        style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            display: loading ? 'none' : 'block',
                            borderRadius: '50%',
                        }}
                        onLoad={() => setLoading(false)}
                        onError={() => {
                            setError(true);
                            setLoading(false);
                        }}
                        draggable={false}
                    />
                )}
            </Avatar>
            {loading && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: size,
                        height: size,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 1,
                    }}
                >
                    <CircularProgress size={size / 2} />
                </Box>
            )}
        </Box>
    );
};

export default AvatarImage;
