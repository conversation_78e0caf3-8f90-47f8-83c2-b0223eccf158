const express = require('express');
const subProductRiderController = require('../Controllers/subProductRiderControllers.js');
const router = express.Router();

// Route to create a new sub products
router.post('/', subProductRiderController.create);

// Route to update a sub products by ID
router.put('/:id', subProductRiderController.update);

// Route to delete a sub products by ID
router.delete('/:id', subProductRiderController.delete);

module.exports = router;
