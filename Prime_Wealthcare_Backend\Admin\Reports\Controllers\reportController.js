const reportModel = require('../Models/reportModel');

// Generate a report based on the provided filters
exports.generateReport = async (req, res) => {
    try {
        const filters = req.body;
        
        // Validate the incoming request
        if (!filters.financial_years || filters.financial_years.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'At least one financial year must be selected'
            });
        }

        if (!filters.group_by || filters.group_by.length < 2) {
            return res.status(400).json({
                success: false,
                message: 'At least two group by options must be selected'
            });
        }
        
        // Get report data from the model
        const reportData = await reportModel.generateReport(filters);
        
        res.status(200).json({
            success: true,
            data: reportData
        });
    } catch (error) {
        console.error('Error generating report:', error);
        res.status(500).json({
            success: false,
            message: 'Error generating report',
            error: error.message
        });
    }
};

/**
 * Get list of available financial years
 */
exports.getFinancialYears = async (req, res) => {
    try {
        const financialYears = await reportModel.getFinancialYears();
        
        res.status(200).json({
            success: true,
            data: financialYears
        });
    } catch (error) {
        console.error('Error fetching financial years:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching financial years',
            error: error.message
        });
    }
};