const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

class SubProduct {
    // Get all sub-products with main product, insurance company, and product master details
    static async getAll() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'main_product.id as main_product_id',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'insurance_company.id as insurance_company_id',
                    'product_master.product_name as product_master_name',
                    'product_master.id as product_master_id',
                    'sub_product.status'
                );
            return products;
        } catch (error) {
            console.error('Error fetching sub-products:', error);
            throw error;
        }
    }
    static async getSubProductByProductDetails(mainProductId, insuranceCompanyId, productMasterId) {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'main_product.id as main_product_id',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'insurance_company.id as insurance_company_id',
                    'product_master.product_name as product_master_name',
                    'product_master.id as product_master_id',
                    'sub_product.status'
                )
                .where({
                    'sub_product.main_product_id': mainProductId,
                    'sub_product.insurance_company_id': insuranceCompanyId,
                    'sub_product.product_master_id': productMasterId,
                   // 'sub_product.status': 1
                });
    
            return products;
        } catch (error) {
            console.error('Error fetching sub products by product details:', error);
            throw error;
        }
    }

    // Get a sub-product by ID with main product, insurance company, and product master details
    static async getById(id) {
        if (!id) throw new Error("Sub-product ID is required");

        try {
            // Fetch sub-product along with main product, insurance company, and product master
            const product = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.*',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name'
                )
                .where('sub_product.id', id)
                .first();

            if (!product) {
                console.error(`No sub-product found with ID: ${id}`);
                return null;
            }

            // Fetch all sub_product_age_sum entries related to the sub_product
            const ageSums = await db('sub_product_age_sum')
                .where('sub_product_id', id)
                .select('min_age', 'max_age', 'sum_insured', 'id');

            // Fetch all sub_product_rider entries related to the sub_product
            const riders = await db('sub_product_rider')
                .where('sub_product_id', id)
                .select('rider_details', 'id');

            // Add the arrays to the main product object
            product.sub_product_age_sum = ageSums;
            product.sub_product_rider_details = riders;

            return product;
        } catch (error) {
            console.error(`Error fetching sub-product with ID: ${id}`, error);
            throw error;
        }
    }

    // Create a new sub-product
    static async create(productData) {
        const {
            sub_product_name,
            main_product_id,
            insurance_company_id,
            product_master_id,
            co_pay,
            child_separation_age,
            pre_hospitalization_days,
            post_hospitalization_days,
        } = productData;

        const subProductData = {
            sub_product_name,
            main_product_id,
            insurance_company_id,
            product_master_id,
            co_pay,
            child_separation_age,
            pre_hospitalization_days: pre_hospitalization_days || null,
            post_hospitalization_days: post_hospitalization_days || null,
        };

        try {
            // Insert into sub_product table if main fields are present
            const [subProduct] = await db('sub_product').insert(subProductData);
            return subProduct;
        } catch (error) {
            console.error('Error inserting sub-product:', error);
            throw error;
        }
    }


    // Update an existing sub-product by ID
    static async update(id, productData) {
        if (!id) throw new Error("Sub-product ID is required");

        const {
            sub_product_name,
            main_product_id,
            insurance_company_id,
            product_master_id,
            co_pay,
            child_separation_age,
            pre_hospitalization_days,
            post_hospitalization_days,
            sum_insured,
            rider_details
        } = productData;

        const subProductData = {
            sub_product_name,
            main_product_id,
            insurance_company_id,
            product_master_id,
            co_pay,
            child_separation_age,
            pre_hospitalization_days: pre_hospitalization_days,
            post_hospitalization_days: post_hospitalization_days,
            updated_at: getCurrentTimestamp()
        };

        try {
            // Update the sub_product table
            const result = await db('sub_product').where('id', id).update(subProductData);
            if (!result) {
                console.error(`No sub-product found with ID: ${id} to update`);
                return;
            }

            // Update sub_product_age_sum if sum_insured is present
            if (sum_insured && sum_insured.length > 0) {
                // First, delete existing age sum records for the sub_product
                await db('sub_product_age_sum').where('sub_product_id', id).del();

                // Then, insert new sum_insured records
                await db('sub_product_age_sum').insert(
                    sum_insured.map(row => ({
                        sub_product_id: id,
                        min_age: row.min_age,
                        max_age: row.max_age,
                        sum_insured: row.sum_insured,
                    }))
                );
            }

            // Update sub_product_rider if rider_details are present
            if (rider_details && rider_details.length > 0) {
                // First, delete existing rider details for the sub_product
                await db('sub_product_rider').where('sub_product_id', id).del();

                // Then, insert new rider details records
                await db('sub_product_rider').insert(
                    rider_details.map(row => ({
                        sub_product_id: id,
                        rider_details: row.rider_details
                    }))
                );
            }

        } catch (error) {
            console.error(`Error updating sub-product with ID: ${id}`, error);
            throw error;
        }
    }


    // Soft delete a sub-product by ID (set status to 0)
    static async delete(id) {
        try {
            const result = await db('sub_product').where('id', id).update({ status: 0, updated_at: getCurrentTimestamp() });
            if (!result) {
                console.error(`No sub-product found with ID: ${id} to delete`);
            }
        } catch (error) {
            console.error(`Error deleting sub-product with ID: ${id}`, error);
            throw error;
        }
    }

    // Reinstate a sub-product by ID (set status to 1)
    static async reinstate(id) {
        try {
            await db('sub_product').where('id', id).update({ status: 1, updated_at: getCurrentTimestamp() });
            return { message: 'Successfully reinstated sub-product' };
        } catch (error) {
            console.error(`Error reinstating sub-product with ID: ${id}`, error);
            throw error;
        }
    }

    // Find sub-products by name (searches in insurance_company, main_product, product_master, and sub_product_name)
    static async getByName(name) {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .where(function () {
                    this.where('sub_product.sub_product_name', 'LIKE', `%${name}%`)
                        .orWhere('main_product.main_product', 'LIKE', `%${name}%`)
                        .orWhere('insurance_company.insurance_company_name', 'LIKE', `%${name}%`)
                        .orWhere('product_master.product_name', 'LIKE', `%${name}%`);
                });

            return products;
        } catch (error) {
            console.error(`Error fetching sub-products by name: ${name}`, error);
            throw error;
        }
    }

    // Find new sub-products not created in the past 7 days
    static async newLastWeek() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .whereBetween('sub_product.created_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching sub-products created last week:', error);
            throw error;
        }
    }

    // Find new sub-products created in the past 7 days
    static async newThisWeek() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .whereBetween('sub_product.created_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching sub-products created this week:', error);
            throw error;
        }
    }

    // Find deactivated sub-products not updated in the past 7 days
    static async deactivatedLastWeek() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .where('sub_product.status', 0)
                .whereBetween('sub_product.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching deactivated sub-products from last week:', error);
            throw error;
        }
    }

    // Find deactivated sub-products updated in the past 7 days
    static async deactivatedThisWeek() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .where('sub_product.status', 0)
                .whereBetween('sub_product.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching deactivated sub-products from this week:', error);
            throw error;
        }
    }

    // Find edited sub-products updated in the past 7 days
    static async editedThisWeek() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .whereBetween('sub_product.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching edited sub-products from this week:', error);
            throw error;
        }
    }

    // Find edited sub-products updated in the previous week (not this week)
    static async editedLastWeek() {
        try {
            const products = await db('sub_product')
                .join('main_product', 'sub_product.main_product_id', 'main_product.id')
                .join('insurance_company', 'sub_product.insurance_company_id', 'insurance_company.id')
                .join('product_master', 'sub_product.product_master_id', 'product_master.id')
                .select(
                    'sub_product.id',
                    'sub_product.sub_product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.product_name as product_master_name',
                    'sub_product.status'
                )
                .whereBetween('sub_product.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching edited sub-products from last week:', error);
            throw error;
        }
    }

}

module.exports = SubProduct;