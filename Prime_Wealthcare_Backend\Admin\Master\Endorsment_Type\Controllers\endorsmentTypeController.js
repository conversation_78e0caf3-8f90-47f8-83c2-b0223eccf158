const EndorsmentType = require('../Model/endorsmentType');

// Get all endorsments
exports.getAllEndorsments = async (req, res, next) => {
    try {
        const data = await EndorsmentType.findAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get endorsment by ID
exports.getEndorsmentById = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = await EndorsmentType.findById(id);
        if (!data) {
            return res.status(404).json({ message: 'Endorsment not found' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get endorsment by name
exports.getEndorsmentByName = async (req, res, next) => {
    try {
        const name = req.params.name;

        const data = await EndorsmentType.findByName(name);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Create a new endorsment
exports.createEndorsment = async (req, res,next) => {
    try {
        const data = req.body;
        const result = await EndorsmentType.create(data);

        res.status(201).json(result);
    } catch (error) {
        next(error);
    }
};

// Update endorsment by ID
exports.updateEndorsment = async (req, res,next) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const result = await EndorsmentType.updateById(id, data);
        if (result) {
            res.status(200).json({ message: 'Endorsment updated successfully' });
        } else {
            res.status(404).json({ message: 'Endorsment not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Delete (deactivate) endorsment by ID
exports.deleteEndorsment = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await EndorsmentType.deleteById(id);
        if (result) {
            res.status(200).json({ message: 'Endorsment deactivated successfully' });
        } else {
            res.status(404).json({ message: 'Endorsment not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Reinstate a endorsment by ID
exports.reinstateEndorsment = async (req, res,next) => {
    try {
        const id = req.params.id;
        const result = await EndorsmentType.reinstate(id);
        if (result) {
            res.status(200).json({ message: 'Endorsment reinstated successfully' });
        } else {
            res.status(404).json({ message: 'Endorsment not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Get endorsments by specific criteria (new, deactivated, edited)
exports.getEndorsmentsByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await EndorsmentType.findAll();
                break;
            case 'newLastWeek':
                data = await EndorsmentType.newLastWeek();
                break;
            case 'newThisWeek':
                data = await EndorsmentType.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await EndorsmentType.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await EndorsmentType.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await EndorsmentType.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await EndorsmentType.editedLastWeek();
                break;
            case 'allActive':
                data = await EndorsmentType.allActive();
                break;
            case 'allInactive':
                data = await EndorsmentType.allInactive();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }


        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
