exports.up = function(knex) {
    return knex.schema.createTable('bank_list', (table) => {
        table.increments('id').primary();           // Auto-incrementing primary key
        table.string('type_name').notNullable();    
        table.string('label_name').notNullable();  
        table.boolean('is_active').notNullable().defaultTo(true); 
        table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
        table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
    });
};


exports.down = function(knex) {
    return knex.schema.dropTableIfExists('bank_list');
};

