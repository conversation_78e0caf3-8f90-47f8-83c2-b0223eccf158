//const { getCustomerAddressByCustomerId } = require('../../../../Prime_Wealthcare_Frontend/src/redux/actions/action');
const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');


const create = async (data) => {
    try {
        const [id] = await db('customer_personal_info').insert(data);
        return { id };
    } catch (error) {
        console.error('Error inserting Customer info:', error);
        throw error;
    }
};

const getAll = async () => {
    try {
        const customers = await db('customer_personal_info').select(
            '*',
            db.raw("DATE_FORMAT(created_at, '%d/%m/%Y') as created_at"),
            db.raw("DATE_FORMAT(updated_at, '%d/%m/%Y') as updated_at")
        );
        return customers;
    } catch (error) {
        console.error('Error retrieving customer info:', error);
        throw error;
    }
};


const findById = async (id) => {
    try {
        const customer = await db('customer_personal_info').where({ id }).first();
        return customer;
    } catch (error) {
        throw error;
    }
};

// Update Customer by ID
const update = async (id, customerData) => {
    if (!id) throw new Error("Customer ID is required");
    try {
        customerData.updated_at = getCurrentTimestamp();

        const result = await db('customer_personal_info').where('id', id).update(customerData);
        if (result) {

        } else {
            console.error(`No Customer found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating Customer with ID: ${id}`, error);
        throw error;
    }
};

const fetchCustomerAndAddress = async () => {
    try {
        const customer = await db('customer_personal_info')
            .leftJoin(
                'customer_address',
                'customer_personal_info.id',
                'customer_address.customer_id'
            )
            .leftJoin(
                'locations as current_location',
                'customer_address.current_city',
                'current_location.id'
            )
            .leftJoin(
                'locations as permanent_location',
                'customer_address.permanent_city',
                'permanent_location.id'
            )
            .select(
                db.raw(
                    db.raw("CONCAT_WS(' ', first_name, middle_name, last_name) as full_name")

                ), // Combine names with a space separator
                'customer_personal_info.*',
                'current_location.city as current_city',
                'permanent_location.city as permanent_city',
                'customer_address.current_state',
                'customer_address.permanent_state',
                db.raw("DATE_FORMAT(customer_personal_info.created_at, '%d/%m/%Y') as created_at"),
                db.raw("DATE_FORMAT(customer_personal_info.updated_at, '%d/%m/%Y') as updated_at")
            );
        return customer;
    } catch (error) {
        throw error;
    }
};





module.exports = {
    create,
    getAll,
    findById,
    update,
    fetchCustomerAndAddress
}