const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');


const create = async (data) => {
    try {
        await db('customer_member_info').insert(data);

    } catch (error) {
        console.error('Error inserting Customer Member info:', error);
        throw error;
    }
};

const getAll = async () => {
    try {
        const customers = await db('customer_member_info')
            .leftJoin('pick_list as relation', function () {
                this.on('customer_member_info.relation_id', '=', 'relation.id')
                    .andOn('relation.type_name', '=', db.raw('?', ['Relation']));
            })
            .select(
                'customer_member_info.*',
                'relation.label_name as relation'
            )
            .where('customer_member_info.status', 1)
            .orderBy('customer_member_info.created_at', 'desc');

        if (!customers || customers.length === 0) {
            return [];
        }
        return customers;
    } catch (error) {
        console.error('Error retrieving customer member info:', error);
        throw error;
    }
};

const findById = async (id) => {
    try {
        if (!id) throw new Error('ID is required');

        const customer = await db('customer_member_info')
            .leftJoin('pick_list as relation', function () {
                this.on('customer_member_info.relation_id', '=', 'relation.id')
                    .andOn('relation.type_name', '=', db.raw('?', ['Relation']));
            })
            .select(
                'customer_member_info.*',
                'relation.label_name as relation'
            )
            .where({ 'customer_member_info.id': id, 'customer_member_info.status': 1 })
            .first();

        if (!customer) {
            throw new Error(`No customer member found with ID: ${id}`);
        }
        return customer;
    } catch (error) {
        console.error(`Error finding customer member with ID: ${id}:`, error);
        throw error;
    }
};

const findByCustomerId = async (id) => {
    try {
        if (!id) throw new Error('Customer ID is required');

        const customers = await db('customer_member_info')
            .leftJoin('pick_list as relation', function () {
                this.on('customer_member_info.relation_id', '=', 'relation.id')
                    .andOn('relation.type_name', '=', db.raw('?', ['Relation']));
            })
            .select(
                'customer_member_info.*',
                'relation.label_name as relation'
            )
            .where({
                'customer_member_info.customer_id': id,
                'customer_member_info.status': 1
            })
            .orderBy('customer_member_info.created_at', 'desc');

        if (!customers || customers.length === 0) {
            return [];
        }
        return customers;
    } catch (error) {
        console.error(`Error finding customer members for customer ID: ${id}:`, error);
        throw error;
    }
};

// Update Customer by ID
const update = async (id, customerData) => {
    if (!id) throw new Error("Customer member ID is required");

    try {
        customerData.updated_at = getCurrentTimestamp();

        const result = await db('customer_member_info').where('id', id).update(customerData);
        if (result) {

        } else {
            console.error(`No Customer member found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating Customer member with ID: ${id}`, error);
        throw error;
    }
};
// Delete role by ID
const deleteById = async (id) => {
    try {
        const result = await db('customer_member_info').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    getAll,
    findById,
    update,
    deleteById,
    findByCustomerId
}