const knexConfig = require('../../../knexfile')
const db = require('knex')(knexConfig.development);
const { getCurrentTimeStamp } = require('../../../Reusable/reusable')

// Create
const create = async (data) => {
    try {
        const result = await db('customer_documents').insert(data);
        return result;
    } catch (error) {
        throw error;
    }
}

// Get All
const getAll = async () => {
    try {
        const result = await db('customer_documents')
            .leftJoin('pick_list as document_type', function () {
                this.on('customer_documents.document_type_id', '=', 'document_type.id')
                    .andOn('document_type.type_name', '=', db.raw('?', ['Document Type']));
            })
            .leftJoin('customer_member_info as member', function () {
                this.on('customer_documents.member_id', '=', 'member.id')
            })
            .select('customer_documents.*', 'document_type.label_name as document_type', 'member.full_name as member_name');
        return result;
    } catch (error) {
        throw error;
    }
}

// Get By Id
const getById = async (id) => {
    try {
        const result = await db('customer_documents').where('id', id).first();
        return result;
    } catch (error) {
        throw error;
    }
}

// Get By Customer Id
const getByCustomerId = async (id) => {
    try {
        const result = await db('customer_documents')
            .leftJoin('pick_list as document_type', function () {
                this.on('customer_documents.document_type_id', '=', 'document_type.id')
                    .andOn('document_type.type_name', '=', db.raw('?', ['Document Type']));
            })
            .leftJoin('customer_member_info as member', function () {
                this.on('customer_documents.member_id', '=', 'member.id')
            })
            .leftJoin('pick_list as relation', function () {
                this.on('member.relation_id', '=', 'relation.id')
                    .andOn('relation.type_name', '=', db.raw('?', ['Relation']));
            })
            .where('customer_documents.customer_id', id)
            .select(
                'customer_documents.id as id',
                'customer_documents.*',
                'document_type.label_name as document_type',
                'member.full_name',
                'member.created_by',
                'member.created_at',
                'member.updated_at',
                'relation.label_name as relation'
            );

        return result;
    } catch (error) {
        throw error;
    }
}

//Update
const update = async (id, data) => {
    try {
        const result = await db('customer_documents').where('member_id', id).where('document_type_id', data.document_type_id).update(data);
        return result;

    } catch (error) {
        throw error;
    }
}

// Delete
const deleteById = async (id) => {
    try {
        const result = await db('customer_documents').where('id', id).delete();
        return result;
    } catch (error) {
        throw error;
    }
}

module.exports = {
    create,
    getAll,
    getById,
    getByCustomerId,
    update,
    deleteById
}