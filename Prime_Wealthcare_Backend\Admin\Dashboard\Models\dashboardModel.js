const knexConfig = require('../../../knexfile');
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

async function getProposalDetailsAndPremium(proposalNumbers, livePolicies, status) {
    try {

        const isCancel = status.length === 1;
        // Get proposals from all tables
        const regularProposals = await db('proposals')
            .whereIn('ProposalNumber', proposalNumbers)
            .whereIn('proposals.status', status)
            .select('ProposalNumber', 'proposal_type', 'net_premium');

        const paProposals = await db('proposals_pa')
            .whereIn('ProposalNumber', proposalNumbers)
            .whereIn('proposals_pa.status', status)
            .select('ProposalNumber', 'proposal_type', 'net_premium');

        const rolloverProposals = await db('proposals_rollover_migration')
            .whereIn('proposal_Number', proposalNumbers) // Fixed case to match other queries
            .whereIn('status', status)
            .select('proposal_Number', 'proposal_type', 'net_premium') // Aliasing to match format
            .then(results => results.map(row => ({
                ProposalNumber: row.proposal_Number,
                proposal_type: row.proposal_type
            })));

        // Combine all proposals
        const allProposals = [...regularProposals, ...paProposals, ...rolloverProposals];

        // Create a map of proposal numbers and their types
        const proposalTypeMap = new Map();
        allProposals.forEach(proposal => {
            proposalTypeMap.set(proposal.ProposalNumber, proposal.proposal_type);
        });

        // Categorize and sum premiums
        const result = {
            newProposalsPremium: 0,
            otherProposalsPremium: 0
        };

        livePolicies.forEach(policy => {
            const proposalType = proposalTypeMap.get(policy.ProposalNumber);
            const temp = allProposals
                .filter(p => p.ProposalNumber === policy.ProposalNumber)
                .reduce((sum, p) => sum + (Number(p.net_premium) || 0), 0);
            const net_premium = isCancel ? -temp : temp;
            if (proposalType === 'NEW') {
                result.newProposalsPremium += net_premium || 0;
            } else {
                result.otherProposalsPremium += net_premium || 0;
            }
        });
        result.newProposalsPremium = Math.round(result.newProposalsPremium);
        result.otherProposalsPremium = Math.round(result.otherProposalsPremium);
        return result;

    } catch (error) {
        console.error("Error in getProposalDetailsAndPremium:", error);
        throw error;
    }
}

exports.getTotalPremiumInTimePeriod = async (title, startDate, endDate) => {
    try {
        // Get proposals from all tables with non-zero net premium
        const getValidProposals = async (tableName, proposalColumn = 'ProposalNumber') => {
            return await db(tableName)
                .select(proposalColumn)
                .whereIn('status', title === 'cancel' || title === 'pastCancel' ? ['CANCELLED'] : ['SUCCESS', 'CANCELLED'])
                .whereBetween('start_date', [startDate, endDate])
                .whereNotNull('net_premium')
                .where('net_premium', '>', 0);
        };

        // Get valid proposal numbers from each table
        const [regularProposals, paProposals, rolloverProposals] = await Promise.all([
            getValidProposals('proposals'),
            getValidProposals('proposals_pa'),
            getValidProposals('proposals_rollover_migration', 'proposal_Number')
        ]);

        const proposalNumbers = [
            ...regularProposals.map(p => p.ProposalNumber),
            ...paProposals.map(p => p.ProposalNumber),
            ...rolloverProposals.map(p => p.proposal_Number)
        ];

        console.log('these are the proposal numbers', proposalNumbers)

        const status = title === 'cancel' || title === 'pastCancel' ? ['CANCELLED'] : ['SUCCESS', 'CANCELLED'];

        // Create mock livePolicies array with unique proposal numbers
        const livePolicies = [...new Set(proposalNumbers)].map(ProposalNumber => ({ ProposalNumber }));

        // Get categorized premiums
        const categorizedPremiums = await getProposalDetailsAndPremium(proposalNumbers, livePolicies, status);

        return {
            newProposalsPremium: categorizedPremiums.newProposalsPremium,
            otherProposalsPremium: categorizedPremiums.otherProposalsPremium,
            totalPremium: categorizedPremiums.newProposalsPremium + categorizedPremiums.otherProposalsPremium
        };
    } catch (error) {
        console.error("Error fetching total premium:", error);
        throw error;
    }
}

exports.getProposalsByInsuranceCompany = async (startDate, endDate) => {
    try {
        // First, get proposal numbers and their total net premiums from each table
        const getProposalTotals = async (tableName, proposalColumn = 'ProposalNumber') => {
            return await db(tableName)
                .select(proposalColumn)
                .sum('net_premium as total_premium')
                .whereIn('status', ['SUCCESS', 'CANCELLED'])
                .whereBetween('start_date', [startDate, endDate])
                .groupBy(proposalColumn)
                .having(db.raw('total_premium <> 0')); // Only keep proposals where total premium isn't 0
        };

        // Get valid proposal numbers from each table
        const regularTotals = await getProposalTotals('proposals');
        const paTotals = await getProposalTotals('proposals_pa');
        const rolloverTotals = await getProposalTotals('proposals_rollover_migration', 'proposal_Number');

        // Get valid proposal numbers
        const validProposalNumbers = [
            ...regularTotals.map(p => p.ProposalNumber),
            ...paTotals.map(p => p.ProposalNumber),
            ...rolloverTotals.map(p => p.proposal_Number)
        ];

        // Get regular proposals with aggregated data
        const regularProposals = await db('proposals as p')
            .select([
                'ic.short_name as insurance_company',
                'p.proposal_type',
                db.raw('COUNT(*) as count'),
                db.raw('COALESCE(SUM(p.net_premium), 0) as premium')
            ])
            .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
            .whereNotNull('ic.short_name')
            .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
            .whereIn('p.ProposalNumber', validProposalNumbers)
            .whereBetween('p.start_date', [startDate, endDate])
            .groupBy('ic.short_name', 'p.proposal_type');

        // Get PA proposals with aggregated data
        const paProposals = await db('proposals_pa as p')
            .select([
                'ic.short_name as insurance_company',
                'p.proposal_type',
                db.raw('COUNT(*) as count'),
                db.raw('COALESCE(SUM(p.net_premium), 0) as premium')
            ])
            .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
            .whereNotNull('ic.short_name')
            .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
            .whereIn('p.ProposalNumber', validProposalNumbers)
            .whereBetween('p.start_date', [startDate, endDate])
            .groupBy('ic.short_name', 'p.proposal_type');

        // Get rollover proposals with aggregated data
        let rolloverProposals = [];
        try {
            const hasRolloverTable = await db.schema.hasTable('proposals_rollover_migration');
            if (hasRolloverTable) {
                rolloverProposals = await db('proposals_rollover_migration as p')
                    .select([
                        'ic.short_name as insurance_company',
                        'p.proposal_type',
                        db.raw('COUNT(*) as count'),
                        db.raw('COALESCE(SUM(p.net_premium), 0) as premium')
                    ])
                    .leftJoin('insurance_company as ic', 'p.insurance_company', 'ic.id')
                    .whereNotNull('ic.short_name')
                    .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
                    .whereIn('p.proposal_Number', validProposalNumbers)
                    .whereBetween('p.start_date', [startDate, endDate])
                    .groupBy('ic.short_name', 'p.proposal_type');
            }
        } catch (error) {
            console.error('Error querying rollover proposals:', error);
        }

        // Combine all results
        const allProposals = [...regularProposals, ...paProposals, ...rolloverProposals]
            .map(item => ({
                ...item,
                premium: parseFloat(item.premium) || 0,
                count: parseInt(item.count) || 0
            }));

        return allProposals;

    } catch (error) {
        console.error("Error fetching proposals by insurance company:", error);
        throw error;
    }
};

exports.getProposalsByAgent = async (startDate, endDate) => {
    try {
        // 1. Collect all proposal numbers with non-zero net premium
        const getProposalTotals = async (tableName, proposalColumn = 'ProposalNumber') => {
            return await db(tableName)
                .select(proposalColumn)
                .sum('net_premium as total_premium')
                .whereIn('status', ['SUCCESS', 'CANCELLED'])
                .whereBetween('start_date', [startDate, endDate])
                .groupBy(proposalColumn)
                .having(db.raw('total_premium <> 0'));
        };

        const regularTotals = await getProposalTotals('proposals');
        const paTotals = await getProposalTotals('proposals_pa');
        const rolloverTotals = await getProposalTotals('proposals_rollover_migration', 'proposal_Number');

        const validNums = new Set([
            ...regularTotals.map(r => r.ProposalNumber),
            ...paTotals.map(r => r.ProposalNumber),
            ...rolloverTotals.map(r => r.proposal_Number)
        ]);

        // SQL WHERE clause helper for only valid proposals
        const filterValid = (query, column = 'ProposalNumber') =>
            query.whereIn(`p.${column}`, Array.from(validNums));

        // 2. Fetch and combine all three sources
        const buildAgentQuery = (tableName, proposalCol = 'ProposalNumber') =>
            db(`${tableName} as p`)
                .select([
                    db.raw("CONCAT(SUBSTRING_INDEX(a.full_name, ' ', 1), ' (', a.agent_id, ')') as agent_name"),
                    'p.proposal_type',
                    db.raw('COUNT(*) as count'),
                    db.raw('COALESCE(SUM(p.net_premium), 0) as premium')
                ])
                .leftJoin('agents as a', 'p.agent_code', 'a.id')
                .whereNotNull('a.full_name')
                .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
                .whereBetween('p.start_date', [startDate, endDate])
                .modify(q => filterValid(q, proposalCol))
                .groupBy('agent_name', 'p.proposal_type');

        const [reg, pa, roll] = await Promise.all([
            buildAgentQuery('proposals'),
            buildAgentQuery('proposals_pa'),
            db.schema.hasTable('proposals_rollover_migration')
                .then(exists => exists
                    ? buildAgentQuery('proposals_rollover_migration', 'proposal_Number')
                    : Promise.resolve([]))
        ]);

        const combined = [...reg, ...pa, ...roll];

        // 3. Pivot and summarize top 10 agents by total premium
        const pivot = combined.reduce((acc, { agent_name, proposal_type, count, premium }) => {
            if (!acc[agent_name]) {
                acc[agent_name] = {
                    NEW: { count: 0, premium: 0 },
                    RENEWAL: { count: 0, premium: 0 },
                    ROLLOVER: { count: 0, premium: 0 },
                    MIGRATION: { count: 0, premium: 0 },
                    total_count: 0,
                    total_premium: 0
                };
            }
            // normalize types
            const typeKey = proposal_type === 'RENEW' ? 'RENEWAL'
                : proposal_type === 'Roll Over' ? 'ROLLOVER'
                    : proposal_type === 'Migration' ? 'MIGRATION'
                        : 'NEW';

            const c = parseInt(count, 10);
            const p = Math.round(parseFloat(premium));
            if (p > 0) {
                acc[agent_name][typeKey].count += c;
                acc[agent_name][typeKey].premium += p;
                acc[agent_name].total_count += c;
                acc[agent_name].total_premium += p;
            }
            return acc;
        }, {});

        // sort and slice top 10
        const top = Object.entries(pivot)
            .filter(([_, d]) => d.total_premium > 0)
            .sort(([, a], [, b]) => b.total_premium - a.total_premium)
            .slice(0, 10);

        const agents = top.map(([name]) => name);
        const data = {
            NEW: top.map(([, d]) => d.NEW.count),
            RENEWAL: top.map(([, d]) => d.RENEWAL.count),
            ROLLOVER: top.map(([, d]) => d.ROLLOVER.count),
            MIGRATION: top.map(([, d]) => d.MIGRATION.count)
        };
        const premiums = {
            NEW: top.map(([, d]) => d.NEW.premium),
            RENEWAL: top.map(([, d]) => d.RENEWAL.premium),
            ROLLOVER: top.map(([, d]) => d.ROLLOVER.premium),
            MIGRATION: top.map(([, d]) => d.MIGRATION.premium)
        };
        const totalPremiums = top.map(([, d]) => d.total_premium);
        const totalCounts = top.map(([, d]) => d.total_count);

        return { agents, data, premiums, totalPremiums, totalCounts };

    } catch (error) {
        console.error('Error in getProposalsByAgent:', error);
        throw error;
    }
};

exports.getCurrentFinancialYearProposals = async (startDate, endDate) => {
    try {
        // Create a subquery to get unique proposals prioritizing CANCELLED status
        const getUniqueProposals = (tableName) => {
            // Handle different column names for different tables
            const proposalNumberCol = tableName === 'proposals_rollover_migration' ? 'proposal_Number' : 'ProposalNumber';

            return db(tableName)
                .select([
                    db.raw(`${proposalNumberCol} as ProposalNumber`),
                    'proposal_type',
                    'status',
                    db.raw(`ROW_NUMBER() OVER (PARTITION BY ${proposalNumberCol} ORDER BY CASE WHEN status = "CANCELLED" THEN 0 ELSE 1 END) as row_num`)
                ])
                .whereBetween('start_date', [startDate, endDate]);
        };
        // Query regular proposals
        const regularProposals = await db
            .select(['proposal_type', 'status'])
            .count('* as count')
            .from(function () {
                this.from(getUniqueProposals('proposals').as('ranked'))
                    .where('row_num', 1)
                    .as('unique_proposals');
            })
            .groupBy(['proposal_type', 'status']);

        // Query PA proposals
        const paProposals = await db
            .select(['proposal_type', 'status'])
            .count('* as count')
            .from(function () {
                this.from(getUniqueProposals('proposals_pa').as('ranked'))
                    .where('row_num', 1)
                    .as('unique_proposals');
            })
            .groupBy(['proposal_type', 'status']);

        // Query rollover proposals
        const rolloverProposals = await db
            .select(['proposal_type', 'status'])
            .count('* as count')
            .from(function () {
                this.from(getUniqueProposals('proposals_rollover_migration').as('ranked'))
                    .where('row_num', 1)
                    .as('unique_proposals');
            })
            .groupBy(['proposal_type', 'status']);

        // Combine all results
        const allProposals = [...regularProposals, ...paProposals, ...rolloverProposals];

        // Rest of the aggregation logic remains the same
        const proposalCounts = allProposals.reduce((acc, curr) => {
            const type = curr.proposal_type;
            const status = curr.status;

            if (!acc[type]) {
                acc[type] = {
                    total: 0,
                    PENDING: 0,
                    SUCCESS: 0,
                    CANCELLED: 0
                };
            }

            acc[type][status] = (acc[type][status] || 0) + parseInt(curr.count);
            acc[type].total += parseInt(curr.count);

            return acc;
        }, {});

        const result = {
            NEW: proposalCounts['NEW'] || {
                total: 0,
                PENDING: 0,
                SUCCESS: 0,
                CANCELLED: 0
            },
            RENEW: proposalCounts['RENEW'] || {
                total: 0,
                PENDING: 0,
                SUCCESS: 0,
                CANCELLED: 0
            },
            ROLLOVER: proposalCounts['Roll Over'] || {
                total: 0,
                PENDING: 0,
                SUCCESS: 0,
                CANCELLED: 0
            },
            MIGRATION: proposalCounts['Migration'] || {
                total: 0,
                PENDING: 0,
                SUCCESS: 0,
                CANCELLED: 0
            },
            total: Object.values(proposalCounts).reduce((a, b) => a + b.total, 0)
        };

        return result;

    } catch (error) {
        console.error('Error in getCurrentFinancialYearProposals:', error);
        throw error;
    }
};

exports.getTop10Branches = async (startDate, endDate) => {
    try {
        // Query regular proposals with branch info
        const regularProposals = await db('proposals as p')
            .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
            .select('p.imf_branch', 'ib.branch_name', 'p.proposal_type', 'p.net_premium', 'p.ProposalNumber')
            .count('* as count')
            .whereBetween('p.start_date', [startDate, endDate])
            .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
            .groupBy('p.imf_branch', 'ib.branch_name', 'p.proposal_type', 'p.net_premium', 'p.ProposalNumber');

        // Query PA proposals with branch info
        const paProposals = await db('proposals_pa as p')
            .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
            .select('p.imf_branch', 'ib.branch_name', 'p.proposal_type', 'p.net_premium', 'p.ProposalNumber')
            .count('* as count')
            .whereBetween('p.start_date', [startDate, endDate])
            .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
            .groupBy('p.imf_branch', 'ib.branch_name', 'p.proposal_type', 'p.net_premium', 'p.ProposalNumber');

        // Query rollover proposals with branch info
        const rolloverProposals = await db('proposals_rollover_migration as p')
            .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
            .select('p.imf_branch', 'ib.branch_name', 'p.proposal_type', 'p.net_premium', 'p.proposal_Number as ProposalNumber')
            .count('* as count')
            .whereBetween('p.start_date', [startDate, endDate])
            .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
            .groupBy('p.imf_branch', 'ib.branch_name', 'p.proposal_type', 'p.net_premium', 'p.proposal_Number');

        // Combine all results
        const allProposals = [...regularProposals, ...paProposals, ...rolloverProposals];

        // Aggregate counts and premium by branch and proposal type
        const branchCounts = allProposals.reduce((acc, curr) => {
            const branchId = curr.imf_branch;
            const branchName = curr.branch_name;
            const premium = parseFloat(curr.net_premium) || 0;
            const proposalType = curr.proposal_type === 'RENEW' ? 'RENEWAL' :
                curr.proposal_type === 'Roll Over' ? 'ROLLOVER' :
                    curr.proposal_type === 'Migration' ? 'MIGRATION' : 'NEW';

            // Skip if premium is 0 (this will be handled by grouping later)
            if (premium === 0) return acc;

            if (!acc[branchId]) {
                acc[branchId] = {
                    branch_id: branchId,
                    branch_name: branchName || 'Unknown Branch',
                    total_proposals: 0,
                    total_premium: 0,
                    proposal_types: {
                        NEW: { count: 0, premium: 0 },
                        RENEWAL: { count: 0, premium: 0 },
                        ROLLOVER: { count: 0, premium: 0 },
                        MIGRATION: { count: 0, premium: 0 }
                    },
                    processed_proposals: new Set() // Track processed proposal numbers
                };
            }

            // Get proposal number (assuming it exists in the query result)
            const proposalNumber = curr.ProposalNumber || curr.proposal_Number;

            // Only count if we haven't processed this proposal before
            if (!acc[branchId].processed_proposals.has(proposalNumber)) {
                acc[branchId].processed_proposals.add(proposalNumber);
                acc[branchId].total_proposals += parseInt(curr.count);
                acc[branchId].total_premium += premium;
                acc[branchId].proposal_types[proposalType].count += parseInt(curr.count);
                acc[branchId].proposal_types[proposalType].premium += premium;
            }

            return acc;
        }, {});

        // When converting to array, remove the processed_proposals set
        const sortedBranches = Object.values(branchCounts)
            .sort((a, b) => b.total_premium - a.total_premium)
            .slice(0, 10)
            .map(branch => {
                const { processed_proposals, ...branchData } = branch;
                return {
                    ...branchData,
                    total_premium: Math.round(branchData.total_premium),
                    proposal_types: {
                        NEW: {
                            count: branchData.proposal_types.NEW.count,
                            premium: Math.round(branchData.proposal_types.NEW.premium)
                        },
                        RENEWAL: {
                            count: branchData.proposal_types.RENEWAL.count,
                            premium: Math.round(branchData.proposal_types.RENEWAL.premium)
                        },
                        ROLLOVER: {
                            count: branchData.proposal_types.ROLLOVER.count,
                            premium: Math.round(branchData.proposal_types.ROLLOVER.premium)
                        },
                        MIGRATION: {
                            count: branchData.proposal_types.MIGRATION.count,
                            premium: Math.round(branchData.proposal_types.MIGRATION.premium)
                        }
                    }
                };
            });

        return sortedBranches;

    } catch (error) {
        console.error('Error in getTop10Branches:', error);
        throw error;
    }
};

exports.getTop20Agents = async (startDate, endDate) => {
    try {
        // Step 1: Get proposal numbers from successful payments
        const payments = await db('payment_master')
            .select('ProposalNumber')
            .whereBetween('created_at', [startDate, endDate])
            .where('status', 'SUCCESS')
            .groupBy('ProposalNumber');

        const proposalNumbers = payments.map(p => p.ProposalNumber);

        // Step 2: Get only NEW proposal details from proposals and proposals_pa with net_premium
        const proposalQueries = [
            // Regular proposals (NEW only)
            db('proposals as p')
                .select(
                    'p.ProposalNumber',
                    'a.full_name',
                    'a.agent_id',
                    'a.photo',
                    'ib.branch_name',
                    'p.net_premium'
                )
                .leftJoin('agents as a', 'p.agent_code', 'a.id')
                .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
                .whereIn('p.ProposalNumber', proposalNumbers)
                .where('p.proposal_type', 'NEW')
                .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
                .whereBetween('p.created_at', [startDate, endDate]),

            // PA proposals (NEW only)
            db('proposals_pa as p')
                .select(
                    'p.ProposalNumber',
                    'a.full_name',
                    'a.agent_id',
                    'a.photo',
                    'ib.branch_name',
                    'p.net_premium'
                )
                .leftJoin('agents as a', 'p.agent_code', 'a.id')
                .leftJoin('imf_branches as ib', 'p.imf_branch', 'ib.id')
                .whereIn('p.ProposalNumber', proposalNumbers)
                .where('p.proposal_type', 'NEW')
                .whereIn('p.status', ['SUCCESS', 'CANCELLED'])
                .whereBetween('p.created_at', [startDate, endDate])
        ];

        // Execute queries
        const [regular, pa] = await Promise.all(proposalQueries);
        const allProposals = [...regular, ...pa];

        // Step 3: Aggregate data by agent
        const agentMap = new Map();
        let totalPremium = 0;

        allProposals.forEach(proposal => {
            const agentKey = `${proposal.full_name} (${proposal.agent_id})`;
            const netPremium = parseFloat(proposal.net_premium || 0);
            totalPremium += netPremium;

            if (!agentMap.has(agentKey)) {
                agentMap.set(agentKey, {
                    name: agentKey,
                    branch: proposal.branch_name || 'Unknown Branch',
                    photo: proposal.photo,
                    total_proposals: 0,
                    total_premium: 0
                });
            }

            const agent = agentMap.get(agentKey);
            agent.total_proposals += 1;
            agent.total_premium += netPremium;
        });

        // Step 4: Convert to array and sort by total proposals
        const agents = Array.from(agentMap.values())
            .sort((a, b) => b.total_premium - a.total_premium)
            .slice(0, 20);

        // Prepare arrays for return, rounding total premiums to nearest integer
        const agentList = agents.map(a => ({
            name: a.name,
            branch: a.branch,
            profile_picture: a.photo
        }));
        const totalCounts = agents.map(a => a.total_proposals);
        const totalPremiums = agents.map(a => Math.round(a.total_premium));

        return {
            agents: agentList,
            totalCounts,
            totalPremiums
        };

    } catch (error) {
        console.error('Error in getTop20Agents:', error);
        throw error;
    }
};