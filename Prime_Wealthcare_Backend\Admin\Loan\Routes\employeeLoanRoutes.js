const express = require('express');
const employeeLoanController = require('../Controllers/employeeLoanController');
const router = express.Router();

// Route to get all Employee Loans
router.get('/', employeeLoanController.getAllLoans);

// Route to get an Employee Loan by ID
router.get('/:id', employeeLoanController.getLoanById);

// Route to create a new Employee Loan
router.post('/', employeeLoanController.createLoan);

// Route to update an Employee Loan by ID
router.put('/:id', employeeLoanController.updateLoan);

// Route to delete (deactivate) an Employee Loan by ID
router.delete('/:id', employeeLoanController.deleteLoan);

// Route to get all EMIs related to a given loan ID
router.get('/:loanId/emis', employeeLoanController.getLoanEmi);

// Route to update an EMI by ID
router.put('/emis/:id', employeeLoanController.updateEmi);

// Route to delete (deactivate) an EMI by ID
router.delete('/emis/:id', employeeLoanController.deleteEmi);

module.exports = router;
