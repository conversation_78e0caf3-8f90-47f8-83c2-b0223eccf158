const knexConfig = require('../../../knexfile');
const { up } = require('../../../Migrations/20250311063905_task_notifications');
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);
const { sendTaskNotificationEmail } = require('../../../Reusable/reusable');
const { sendEmail } = require('../../../services/emailService');

// create a new task 
const create = async (data) => {
    try {
        const [id] = await db('tasks').insert(data);

        // Get assigned user's email
        const assignedUser = await db('employee_personal_info')
            .where('id', data.assigned_to)
            .select('*')
            .first();

        // Insert into notification table
        await db('task_notifications').insert({
            task_id: id,
            user_id: data.assigned_to,
            message: 'New task created',
            created_at: getCurrentTimestamp(),
            created_by: data.created_by,

        });

        // Insert into history table
        await db('task_history').insert({
            task_id: id,
            status: "To-Do",
            changed_by: data.assigned_by,
            created_at: getCurrentTimestamp(),
            created_by: data.created_by,
        });

        // Send email notification if email exists
        if (assignedUser && assignedUser.personal_email) {
            const emailSubject = 'New Task Assignment';
            const emailText = `A new task has been assigned to you: ${data.title}`;
            const emailHtml = `
            Dear ${assignedUser.employee_full_name},
            
                <h2>New Task Assigned</h2>
                <p>A new task has been assigned to you:</p>
                <ul>
                    <li><strong>Task Code:</strong> ${data.task_code}</li>
                    <li><strong>Title:</strong> ${data.title}</li>
                    <li><strong>Description:</strong> ${data.description}</li>
                    <li><strong>Status:</strong> ${data.status || 'To-Do'}</li>
                </ul>
                <p>Please log in to the system to view more details.</p>
            `;

            await sendEmail(
                assignedUser.personal_email,
                emailSubject,
                emailText,
                emailHtml
            );
        }

        return { id };
    } catch (error) {
        console.error('Error inserting Task info:', error);
        throw error;
    }
};

const getAll = async () => {
    try {
        const result = await db('tasks').select();
        return result;
    } catch (error) {
        console.error('Error fetching Task info:', error);
        throw error;
    }
};

// get by id 
const getById = async (id) => {
    try {
        const result = await db('tasks').where('id', id).first();
        return result;
    } catch (error) {
        console.error('Error fetching Task info:', error);
        throw error;
    }
}

// get by assigned_to
const getByAssignedTo = async (assigned_to) => {
    try {
        const result = await db('tasks').where('assigned_to', assigned_to).select();
        return result;
    } catch (error) {
        console.error('Error fetching Task info:', error);
        throw error;
    }
}

// get by assigned by 
const getByAssignedBy = async (assigned_by) => {
    try {
        const result = await db('tasks').where('assigned_by', assigned_by).select();
        return result;
    } catch (error) {
        console.error('Error fetching Task info:', error);
        throw error;
    }
}

// update task info
const update = async (data, id) => {
    try {
        const result = await db('tasks').where({ id }).update({
            ...data,
            updated_at: getCurrentTimestamp()
        });

        // Fetch existing task to get assigned_by and assigned_to
        const existingTask = await db('tasks').where({ id }).first();
        if (!existingTask) {
            throw new Error("Task not found");
        }

        // Get updated by user's details
        const updatedByUser = await db('employee_personal_info')
            .where('id', data.updated_by)
            .select('employee_full_name')
            .first();

        // Get assigned by user's email and details
        const assignedByUser = await db('employee_personal_info')
            .where('id', existingTask.assigned_by)
            .select('personal_email', 'employee_full_name')
            .first();

        // Ensure assigned_by and assigned_to are present
        const assignedTo = existingTask.assigned_to;
        const assignedBy = existingTask.assigned_by;

        // Insert into notification table
        await db('task_notifications').insert({
            task_id: id,
            user_id: assignedBy,
            message: 'Task updated',
            created_at: getCurrentTimestamp(),
            created_by: data.updated_by,
            updated_at: getCurrentTimestamp(),
            updated_by: data.updated_by,
        });

        // Insert into history table
        await db('task_history').insert({
            task_id: id,
            status: data.status,
            changed_by: assignedTo,
            created_at: getCurrentTimestamp(),
            created_by: data.updated_by,
            updated_at: getCurrentTimestamp(),
            updated_by: data.updated_by,
        });

        // Send email notification if email exists
        if (assignedByUser && assignedByUser.personal_email) {
            const emailSubject = 'Task Update Notification';
            const emailText = `Task ${existingTask.title} has been updated`;
            const emailHtml = `
            Dear ${assignedByUser.employee_full_name},
            
                <h2>Task Update Notification</h2>
                <p>A task you assigned has been updated:</p>
                <ul>
                     <li><strong>Task Code:</strong> ${existingTask.task_code}</li>
                    <li><strong>Title:</strong> ${existingTask.title}</li>
                    <li><strong>New Status:</strong> ${data.status}</li>
                    <li><strong>Updated By:</strong> ${updatedByUser.employee_full_name}</li>
                </ul>
                <p>Please log in to the system to view more details.</p>
            `;

            await sendEmail(
                assignedByUser.personal_email,
                emailSubject,
                emailText,
                emailHtml
            );
        }

        return result;
    } catch (error) {
        console.error('Error updating Task info:', error);
        throw error;
    }
}

// delete by id 
const deleteById = async (id) => {
    try {
        const result = await db('tasks').where(id).del();
        return result;
    } catch (error) {
        console.error('Error deleting Task info:', error);
        throw error;
    }
}

// addComment
const addComment = async (taskId, comment) => {
    try {
        // Insert comment
        const result = await db('task_comments').insert({
            ...comment,
            task_id: taskId,
            created_at: getCurrentTimestamp()
        });

        // Get task details
        const task = await db('tasks')
            .where('id', taskId)
            .first();

        // Get commenter's details
        const commenter = await db('employee_personal_info')
            .where('id', comment.created_by)
            .select('employee_full_name')
            .first();

        // Get task owner's (assigned_by) details
        const taskOwner = await db('employee_personal_info')
            .where('id', comment.user_id)
            .select('personal_email', 'employee_full_name')
            .first();

        // Insert notification
        await db('comment_notifications').insert({
            task_id: taskId,
            user_id: comment.user_id,
            comment_id: result[0],
            message: 'New comment added',
            created_at: getCurrentTimestamp(),
            created_by: comment.created_by,
        });

        // Send email notification if email exists
        if (taskOwner && taskOwner.personal_email) {
            const emailSubject = 'New Comment on Task';
            const emailText = `A new comment has been added to task: ${task.title}`;
            const emailHtml = `
            Dear ${taskOwner.employee_full_name},
            
                <h2>New Comment on Task</h2>
                <p>A new comment has been added to your task:</p>
                <ul>
                    <li><strong>Task:</strong> ${task.title}</li>
                    <li><strong>Commented By:</strong> ${commenter.employee_full_name}</li>
                    <li><strong>Comment:</strong> ${comment.comment}</li>
                </ul>
                <p>Please log in to the system to view and respond to this comment.</p>
            `;

            await sendEmail(
                taskOwner.personal_email,
                emailSubject,
                emailText,
                emailHtml
            );
        }

        return { result };
    } catch (error) {
        console.error('Error inserting Task comment:', error);
        throw error;
    }
}

// add comment reply
const addCommentReply = async (commentId, reply) => {
    try {
        const result = await db('task_comments').insert({
            ...reply,
            // parent_comment_id: commentId,
            created_at: getCurrentTimestamp()
        });
        // Get task details
        const task = await db('tasks')
            .where('id', reply.task_id)
            .first();

        // Get original commenter's details
        const originalComment = await db('task_comments')
            .where('id', commentId)
            .first();

        const originalCommenter = await db('employee_personal_info')
            .where('id', originalComment.user_id)
            .select('personal_email', 'employee_full_name')
            .first();

        // Get replier's details
        const replier = await db('employee_personal_info')
            .where('id', reply.created_by)
            .select('employee_full_name')
            .first();

        await db('comment_notifications').insert({
            task_id: reply.task_id,
            user_id: reply.user_id,
            comment_id: result[0],
            message: 'New comment reply added',
            created_at: getCurrentTimestamp(),
            created_by: reply.created_by,
        });
        // Send email notification if email exists
        if (originalCommenter && originalCommenter.personal_email) {
            const emailSubject = 'New Reply to Your Comment';
            const emailText = `${originalCommenter.employee_full_name} replied to your comment on task: ${task.title}`;
            const emailHtml = `
            Dear ${originalCommenter.employee_full_name},
            
                <h2>New Reply to Your Comment</h2>
                <p>${originalCommenter.employee_full_name} has replied to your comment on task "${task.title}":</p>
                <ul>
                    <li><strong>Task:</strong> ${task.title}</li>
                    <li><strong>Your Comment:</strong> ${originalComment.comment}</li>
                    <li><strong>Reply By:</strong> ${replier.employee_full_name}</li>
                    <li><strong>Reply:</strong> ${reply.comment}</li>
                </ul>
                <p>Please log in to the system to view and respond to this reply.</p>
            `;

            await sendEmail(
                originalCommenter.personal_email,
                emailSubject,
                emailText,
                emailHtml
            );
        }
        return { result };
    } catch (error) {
        console.error('Error inserting Task comment reply:', error);
        throw error;
    }
}

// update task notification table is_read
const updateNotification = async (id, data) => {
    try {
        const result = await db('task_notifications').where({ task_id: id }).update({

            is_read: 1,
            updated_by: data.updated_by,
            updated_at: getCurrentTimestamp()
        });
        return result;
    } catch (error) {
        console.error('Error updating Task notification:', error);
        throw error;
    }
}
// update comment notifications
const updateCommentNotification = async (id, data) => {
    try {
        const result = await db('comment_notifications').where({ task_id: id }).update({
            is_read: 1,
            updated_by: data.updated_by,
            updated_at: getCurrentTimestamp()
        });
        return result;

    } catch (error) {
        console.error('Error updating Task notification:', error);
        throw error;
    }
}


// get all comments  by task id
const getAllCommentReplies = async (taskId) => {
    try {
        const comments = await db("task_comments AS c1")
            .leftJoin("task_comments AS c2", "c1.id", "c2.parent_comment_id") // Join for replies
            .leftJoin("employee_personal_info AS u1", "c1.user_id", "u1.id") // Get commenter's name
            .leftJoin("employee_personal_info AS u2", "c2.user_id", "u2.id") // Get replier's name
            .select(
                "c1.id AS comment_id",
                "c1.comment AS comment_text",
                "c1.task_id",
                "c1.parent_comment_id",
                "c1.created_at",
                "u1.employee_full_name AS commenter_name",
                "c2.id AS reply_id",
                "c2.comment AS reply_text",
                "c2.created_at AS reply_created_at",
                "u2.employee_full_name AS replier_name"
            )
            .where("c1.task_id", taskId)
            .orderBy("c1.created_at", "asc");

        // Organize comments and replies in a structured format
        const commentMap = new Map();

        comments.forEach((row) => {
            if (!commentMap.has(row.comment_id)) {
                commentMap.set(row.comment_id, {
                    comment_id: row.comment_id,
                    task_id: row.task_id,
                    comment_text: row.comment_text,
                    commenter_name: row.commenter_name,
                    created_at: row.created_at,
                    replies: [],
                });
            }

            if (row.reply_id) {
                commentMap.get(row.comment_id).replies.push({
                    reply_id: row.reply_id,
                    reply_text: row.reply_text,
                    replier_name: row.replier_name,
                    created_at: row.reply_created_at,
                });
            }
        });
        // Convert the map values to an array
        const commentsWithReplies = Array.from(commentMap.values());

        return commentsWithReplies;


    } catch (error) {
        console.error("Error fetching comments:", error);
        throw error;

    }
}
const getCommentNotifications = async (userId) => {
    try {
        const result = await db('comment_notifications').where({ user_id: userId }).select();
        return result;
    } catch (error) {
        console.error('Error fetching Task comments:', error);
        throw error;
    }
}
const getTaskNotifications = async (userId) => {
    try {
        const result = await db('task_notifications AS tn')
            .leftJoin('tasks AS t', 'tn.task_id', 't.id')
            .select(
                'tn.*',
                't.task_code',
                't.title'
            )
            .where({ 'tn.user_id': userId });

        return result;
    } catch (error) {
        console.error('Error fetching Task notifications:', error);
        throw error;
    }
}

const getAllTaskNotifications = async () => {
    try {
        const result = await db('task_notifications').select();
        return result;
    } catch (error) {
        console.error('Error fetching Task comments:', error);
        throw error;
    }
}
const getAllCommentNotifications = async () => {
    try {
        // Debug: Log the query being executed
        const query = db('comment_notifications').select();

        const result = await query;
        return result;
    } catch (error) {
        console.error('Error fetching comment notifications:', error);
        throw error;
    }
}

// get task details and its commennts and replies  by u(created_by) join tables tasks,task_comments,employee_personal_info
const getTaskDetails = async (id) => {
    try {
        const result = await db('tasks AS t')
            .leftJoin('task_comments AS c', 't.id', 'c.task_id')
            .leftJoin('employee_personal_info AS u', 't.created_by', 'u.id')
            .leftJoin('employee_personal_info AS u2', 't.assigned_to', 'u2.id')
            .leftJoin('task_history AS h', 't.id', 'h.task_id')
            .leftJoin('task_notifications AS n', 't.id', 'n.task_id')
            .leftJoin('comment_notifications AS cn', 'c.id', 'cn.comment_id')
            .select(
                't.id',
                't.title',
                't.task_code',
                't.description',
                't.status',
                't.assigned_to',
                't.assigned_by',
                't.created_by',
                't.created_at',
                't.updated_at',
                'u.employee_full_name AS created_by_name',
                'u2.employee_full_name AS assigned_to_name',
                'c.id AS comment_id',
                'c.comment',
                'c.created_at AS comment_created_at',
                'c.updated_at AS comment_updated_at',
                'c.updated_by AS comment_updated_by',
                'c.parent_comment_id',
                // 'h.id AS history_id',
                // 'h.status AS history_status',
                // 'h.changed_by AS history_changed_by',
                // 'h.created_at AS history_created_at',
                'n.is_read AS task_is_read',
                'cn.is_read AS comment_is_read',
                'cn.user_id',
            )
            .where((builder) => {
                builder.whereRaw('t.created_by = ? OR t.assigned_to = ?', [id, id]);
            }).orderBy(['c.created_at'], 'asc');

        // Organize tasks, comments, and history
        const taskMap = new Map();
        const commentMap = new Map();

        for (const row of result) {
            if (!taskMap.has(row.id)) {
                taskMap.set(row.id, {
                    id: row.id,
                    title: row.title,
                    task_code: row.task_code,
                    description: row.description,
                    status: row.status,
                    assigned_to: row.assigned_to,
                    assigned_by: row.assigned_by,
                    created_by: row.created_by,
                    created_at: row.created_at,
                    updated_at: row.updated_at,
                    created_by_name: row.created_by_name,
                    assigned_to_name: row.assigned_to_name,
                    is_read: row.task_is_read,

                    comments: [],
                    // history: []
                });
            }

            // Add history entry if it exists
            if (row.history_id && !taskMap.get(row.id).history.some(h => h.id === row.history_id)) {
                taskMap.get(row.id).history.push({
                    id: row.history_id,
                    status: row.history_status,
                    changed_by: row.history_changed_by,
                    created_at: row.history_created_at
                });
            }

            if (row.comment_id) {
                const commentData = {
                    comment_id: row.comment_id,
                    comment: row.comment,
                    created_at: row.comment_created_at,
                    updated_at: row.comment_updated_at,
                    updated_by: row.comment_updated_by,
                    is_read: row.comment_is_read,
                    user_id: row.user_id,
                    replies: []
                };

                // Store in commentMap for future reference
                commentMap.set(row.comment_id, commentData);

                // Check if it's a reply
                if (row.parent_comment_id) {
                    const parentComment = commentMap.get(row.parent_comment_id);
                    if (parentComment) {
                        parentComment.replies.push(commentData);
                    }
                } else {
                    // Top-level comment
                    if (!taskMap.get(row.id).comments.some(c => c.comment_id === row.comment_id)) {
                        taskMap.get(row.id).comments.push(commentData);
                    }
                }
            }
        }

        return Array.from(taskMap.values());
    } catch (error) {
        console.error('Error fetching Task details:', error);
        throw error;
    }
};

module.exports = {
    create,
    getAll,
    getById,
    getByAssignedTo,
    getByAssignedBy,
    update,
    deleteById,
    addComment,
    addCommentReply,
    updateNotification,
    updateCommentNotification,
    getAllCommentReplies,
    getCommentNotifications,
    getTaskNotifications,
    getAllTaskNotifications,
    getAllCommentNotifications,
    getTaskDetails
};
