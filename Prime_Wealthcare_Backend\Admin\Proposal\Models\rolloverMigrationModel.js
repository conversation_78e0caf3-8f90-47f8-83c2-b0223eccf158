const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);

exports.createRolloverMigration = async (migrationData, memberData, paymentData) => {
    // Use transaction to ensure all operations succeed or fail together
    return db.transaction(async trx => {
        try {
            // Insert migration data
            const [migrationId] = await trx('proposals_rollover_migration')
                .insert(migrationData)
                .returning('id');

            // Add policy_number and migration ID to each member record
            const membersToInsert = memberData.map(member => ({
                ...member,
                policy_number: migrationData.policy_number,
                prm_id: migrationId
            }));

            // Insert all members
            await trx('proposals_rollover_migration_members')
                .insert(membersToInsert);

            // Add payment record if provided
            if (paymentData) {
                await trx('payment_master')
                    .insert(paymentData);
            }

            return {
                success: true,
                data: {
                    migrationId,
                    policy_number: migrationData.policy_number
                }
            };
        } catch (error) {
            console.log('Error in createRolloverMigration:', error);
            throw error;
        }
    });
};

exports.getRolloverMigrationById = async (migrationId) => {
    try {
        // Get the migration data
        const migration = await db('proposals_rollover_migration as rm')
            .where('rm.id', migrationId)
            .first();

        if (!migration) {
            return null;
        }

        // Get associated members
        const members = await db('proposals_rollover_migration_members')
            .where('prm_id', migrationId);

        // Get payment details using proposal number
        const paymentDetails = await db('payment_master')
            .where('ProposalNumber', migration.proposal_Number)
            .first();

        // Combine all data
        return {
            ...migration,
            members,
            payment: paymentDetails || null
        };

    } catch (error) {
        console.log('Error in getRolloverMigrationById:', error);
        throw error;
    }
};

exports.getRolloverMigrationByPolicyNumber = async (policyNumber) => {
    try {
        // Get the migration data
        const migration = await db('proposals_rollover_migration')
            .where('policy_number', policyNumber)
            .first();

        if (!migration) {
            return null;
        }

        // Get associated members
        const members = await db('proposals_rollover_migration_members')
            .where('policy_number', policyNumber);

        return {
            ...migration,
            members
        };
    } catch (error) {
        console.log('Error in getRolloverMigrationByPolicyNumber:', error);
        throw error;
    }
};

exports.getAllRolloverMigrations = async () => {
    try {
        // Query for rollover migrations with joins for better data
        const migrations = await db('proposals_rollover_migration as rm')
            .select([
                'rm.*',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL THEN c.company_name 
                    ELSE CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.middle_name, ''), ' ', COALESCE(c.last_name, ''))
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name'
            ])
            .leftJoin('customer_personal_info as c', 'rm.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'rm.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'rm.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'rm.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'rm.sub_product', 'sp.id')
            .orderBy('rm.Created_at', 'desc');

        // Get count of members for each migration
        const migrationsWithMemberCounts = await Promise.all(
            migrations.map(async (migration) => {
                const memberCount = await db('proposals_rollover_migration_members')
                    .where('policy_number', migration.policy_number)
                    .count('id as count')
                    .first();

                return {
                    ...migration,
                    memberCount: memberCount?.count || 0,
                    status: migration.status || 'ACTIVE'
                };
            })
        );

        return migrationsWithMemberCounts;
    } catch (error) {
        console.error('Error in getAllRolloverMigrations:', error);
        throw error;
    }
};

exports.getAllRolloverMigrationsByUserId = async (userId) => {
    try {
        // Function to get agent IDs for employee
        const getAgentIdsForEmployee = async (userId) => {
            const employee = await db('employee_personal_info')
                .where('user_id', userId)
                .first();

            if (!employee || !employee.branch_id) {
                return [];
            }

            const branchIds = employee.branch_id.split(',').map(id => id.trim());
            const agents = await db('agents')
                .whereIn('branch_id', branchIds)
                .select('id');

            return agents.map(agent => agent.id);
        };

        // Base query for rollover migrations
        let migrationsQuery = db('proposals_rollover_migration as rm')
            .select([
                'rm.*',
                db.raw(`CASE 
                    WHEN c.company_name IS NOT NULL THEN c.company_name 
                    ELSE CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.middle_name, ''), ' ', COALESCE(c.last_name, ''))
                END as customer_full_name`),
                'ic.short_name as insurance_company_name',
                'a.agent_id as agent_code',
                'pm.product_name as master_product_name',
                'sp.sub_product_name'
            ])
            .leftJoin('customer_personal_info as c', 'rm.customer_id', 'c.id')
            .leftJoin('insurance_company as ic', 'rm.insurance_company', 'ic.id')
            .leftJoin('agents as a', 'rm.agent_code', 'a.id')
            .leftJoin('product_master as pm', 'rm.product_name', 'pm.id')
            .leftJoin('sub_product as sp', 'rm.sub_product', 'sp.id');

        // Apply filters based on user role
        if (userId.includes('ADM')) {
            // Admin sees all migrations, no additional filters needed
        } else if (userId.includes('RM')) {
            // RM sees only their migrations
            const agent = await db('agents').where('agent_id', userId).first();
            migrationsQuery = migrationsQuery.where('rm.agent_code', agent.id);
        } else {
            // Other employees see migrations for agents in their branches
            const agentIds = await getAgentIdsForEmployee(userId);
            if (agentIds.length === 0) {
                return [];
            }
            migrationsQuery = migrationsQuery.whereIn('rm.agent_code', agentIds);
        }

        // Add ordering
        migrationsQuery = migrationsQuery.orderBy('rm.Created_at', 'desc');

        // Execute query
        const migrations = await migrationsQuery;

        return migrations.map(migration => ({
            ...migration,
            status: migration.status || 'ACTIVE'
        }));
    } catch (error) {
        console.error('Error in getAllRolloverMigrationsByUserId:', error);
        throw error;
    }
};

// Update the model function to include Updated_by and Updated_at
exports.updateRolloverMigration = async (id, migrationData) => {
    return db.transaction(async trx => {
        try {
            // Update fields including policy details and tracking info
            const updateFields = {
                policy_number: migrationData.policy_number,
                start_date: migrationData.start_date,
                end_date: migrationData.end_date,
                tenure: migrationData.tenure,
                Updated_by: migrationData.Updated_by,
                Updated_at: new Date()
            };

            // Update the migration record
            await trx('proposals_rollover_migration')
                .where('id', id)
                .update(updateFields);

            return {
                success: true,
                data: {
                    id,
                    policy_number: migrationData.policy_number,
                    Updated_by: migrationData.Updated_by,
                    Updated_at: migrationData.Updated_at
                }
            };
        } catch (error) {
            console.log('Error in updateRolloverMigration:', error);
            throw error;
        }
    });
};