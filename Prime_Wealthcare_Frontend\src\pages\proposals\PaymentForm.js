import React, { useState, useEffect, useRef } from 'react';
import { Container, Button, Typography, Box, CircularProgress, Backdrop } from '@mui/material';
import CustomTextField from '../../components/CustomTextField';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import { setTransactionDetails } from '../../redux/slices/payment/paymentSlice';
import { updatePayment, payLater } from '../../redux/actions/action';

const PaymentForm = () => {
    const { WS_P_ID, TID, PGID, Premium, Response } = Object.fromEntries(
        new URLSearchParams(useLocation().search).entries()
    );

    const navigate = useNavigate();
    const dispatch = useDispatch();

    const paymentData = useSelector((s) => s.paymentReducer.paymentDetails);
    const currentProposalId = useSelector((s) => s.paymentReducer.currentProposalId);

    const [urlData] = useState({ WS_P_ID, TID, PGID, Premium, Response });
    const [loading, setLoading] = useState(false);

    // Prevent double‐processing
    const hasProcessedPayment = useRef(false);

    useEffect(() => {
        if (
            !hasProcessedPayment.current &&
            urlData.WS_P_ID &&
            urlData.PGID &&
            urlData.Response
        ) {
            hasProcessedPayment.current = true;

            let updatedPaymentData;
            if (urlData.Response === 'Success') {
                dispatch(setTransactionDetails(urlData));
                toast.success('Payment successful!');
                updatedPaymentData = {
                    WS_P_ID: urlData.WS_P_ID,
                    PGID: Number(urlData.PGID),
                    Status: 'SUCCESS',
                };
            } else if (urlData.Response === 'Failed') {
                toast.error('Payment failed!');
                updatedPaymentData = {
                    WS_P_ID: urlData.WS_P_ID,
                    PGID: Number(urlData.PGID),
                    Status: 'FAILED',
                };
            } else if (urlData.Response === 'UserCancelled') {
                toast.error('Payment is cancelled!');
                updatedPaymentData = {
                    WS_P_ID: urlData.WS_P_ID,
                    PGID: Number(urlData.PGID),
                    Status: 'USERCANCELLED',
                };
            }

            dispatch(updatePayment({ id: paymentData?.id, paymentData: updatedPaymentData }))
                .then(() => {
                    navigate(`/dashboard/edit-proposal/${currentProposalId}`);
                });
        }
        // Empty deps: only on mount
    }, []);

    // Build the formData from paymentData once
    const host = process.env.REACT_APP_INTERNAL_HOST;
    const port = process.env.REACT_APP_INTERNAL_PORT;
    const paymentUrl = process.env.REACT_APP_PAYMENT_URL;
    const link = port ? `${host}:${port}` : host;

    const [formData, setFormData] = useState({
        TransactionID: paymentData?.TransactionID || '',
        PaymentOption: paymentData?.PaymentOption || '3',
        ResponseURL: paymentData?.ResponseURL || `${link}/dashboard/payment-form`,
        ProposalNumber: paymentData?.proposal_number || '',
        PremiumAmount: paymentData?.amount || '',
        UserIdentifier: paymentData?.UserIdentifier || 'webagg',
        UserId: paymentData?.UserId || '',
        FirstName: paymentData?.firstName?.trim() || '',
        LastName: paymentData?.lastName?.trim() || '',
        Mobile: paymentData?.mobile?.trim() || '',
        Email: paymentData?.email?.trim() || '',
        Vendor: paymentData?.Vendor || '1',
        CheckSum: paymentData?.CheckSum || '',
    });

    const [errors, setErrors] = useState({});
    const [isDataComplete, setIsDataComplete] = useState(true);

    useEffect(() => {
        if (
            (!paymentData ||
                !paymentData.amount ||
                !paymentData.firstName ||
                !paymentData.lastName ||
                !paymentData.mobile ||
                !paymentData.email) &&
            !urlData.Response
        ) {
            toast.error('Processing request contains insufficient data. Try again.', {
                onClose: () => navigate(-1),
            });
            setIsDataComplete(false);
        }
    }, [paymentData, urlData, navigate]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData((f) => ({ ...f, [name]: value }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        try {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = paymentUrl;
            Object.entries(formData).forEach(([key, val]) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = val;
                form.appendChild(input);
            });
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        } catch (err) {
            console.error('Payment Error:', err);
            toast.error('Failed to process payment. Please try again later.');
        }
    };

    const handlePayLater = async () => {
        setLoading(true);
        try {
            const response = await dispatch(
                payLater({ paymentData: { ...formData, ResponseURL: 'https://homepage.primewealthcare.in', paymentUrl } })
            );
            if (response?.payload?.success) {
                toast.success('Payment link sent successfully!');
                navigate(`/dashboard/edit-proposal/${currentProposalId}`);
            } else {
                toast.error(response?.payload?.error?.message || 'Failed to send payment link');
            }
        } catch (err) {
            console.error('Pay Later Error:', err);
            toast.error(err.message || 'Failed to process pay later request');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Backdrop
                sx={{
                    color: '#fff',
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                }}
                open={loading}
            >
                <CircularProgress color="inherit" size={60} />
            </Backdrop>
            <Container maxWidth="md">
                <Box
                    sx={{
                        py: 4,
                        px: { xs: 2, sm: 4 },
                        backgroundColor: 'background.paper',
                        borderRadius: 2,
                        boxShadow: 3,
                        my: 4,
                    }}
                >
                    <Typography
                        variant="h4"
                        align="center"
                        gutterBottom
                        sx={{ color: '#528A7E', fontWeight: 600, mb: 4 }}
                    >
                        Payment Details
                    </Typography>

                    <Box
                        component="form"
                        onSubmit={handleSubmit}
                        sx={{
                            display: 'grid',
                            gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
                            gap: 3,
                            '& .MuiTextField-root': {
                                backgroundColor: 'background.default',
                                borderRadius: 1,
                            },
                        }}
                    >
                        {/* Transaction Details */}
                        <Box sx={{ gridColumn: { xs: '1', md: '1 / -1' } }}>
                            <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                                Transaction Details
                            </Typography>
                            <Box
                                sx={{
                                    display: 'grid',
                                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                                    gap: 2,
                                }}
                            >
                                <CustomTextField
                                    label="Transaction ID"
                                    name="TransactionID"
                                    value={formData.TransactionID}
                                    onChange={handleInputChange}
                                    error={errors.TransactionID}
                                    helperText={errors.TransactionID}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Proposal Number"
                                    name="ProposalNumber"
                                    value={formData.ProposalNumber}
                                    onChange={handleInputChange}
                                    error={errors.ProposalNumber}
                                    helperText={errors.ProposalNumber}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                            </Box>
                        </Box>

                        {/* Personal Information */}
                        <Box sx={{ gridColumn: { xs: '1', md: '1 / -1' } }}>
                            <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                                Personal Information
                            </Typography>
                            <Box
                                sx={{
                                    display: 'grid',
                                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                                    gap: 2,
                                }}
                            >
                                <CustomTextField
                                    label="First Name"
                                    name="FirstName"
                                    value={formData.FirstName}
                                    onChange={handleInputChange}
                                    error={errors.FirstName}
                                    helperText={errors.FirstName}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Last Name"
                                    name="LastName"
                                    value={formData.LastName}
                                    onChange={handleInputChange}
                                    error={errors.LastName}
                                    helperText={errors.LastName}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Mobile"
                                    name="Mobile"
                                    value={formData.Mobile}
                                    onChange={handleInputChange}
                                    error={errors.Mobile}
                                    helperText={errors.Mobile}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Email"
                                    name="Email"
                                    value={formData.Email}
                                    onChange={handleInputChange}
                                    error={errors.Email}
                                    helperText={errors.Email}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                            </Box>
                        </Box>

                        {/* Payment Details */}
                        <Box sx={{ gridColumn: { xs: '1', md: '1 / -1' } }}>
                            <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                                Payment Details
                            </Typography>
                            <Box
                                sx={{
                                    display: 'grid',
                                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                                    gap: 2,
                                }}
                            >
                                <CustomTextField
                                    label="Premium Amount"
                                    name="PremiumAmount"
                                    value={formData.PremiumAmount}
                                    onChange={handleInputChange}
                                    error={errors.PremiumAmount}
                                    helperText={errors.PremiumAmount}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Payment Option"
                                    name="PaymentOption"
                                    value={formData.PaymentOption}
                                    onChange={handleInputChange}
                                    error={errors.PaymentOption}
                                    helperText={errors.PaymentOption}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Vendor"
                                    name="Vendor"
                                    value={formData.Vendor}
                                    onChange={handleInputChange}
                                    error={errors.Vendor}
                                    helperText={errors.Vendor}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                            </Box>
                        </Box>

                        {/* System Details */}
                        <Box sx={{ gridColumn: { xs: '1', md: '1 / -1' } }}>
                            <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                                System Details
                            </Typography>
                            <Box
                                sx={{
                                    display: 'grid',
                                    gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                                    gap: 2,
                                }}
                            >
                                <CustomTextField
                                    label="User Identifier"
                                    name="UserIdentifier"
                                    value={formData.UserIdentifier}
                                    onChange={handleInputChange}
                                    error={errors.UserIdentifier}
                                    helperText={errors.UserIdentifier}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="User ID"
                                    name="UserId"
                                    value={formData.UserId}
                                    onChange={handleInputChange}
                                    error={errors.UserId}
                                    helperText={errors.UserId}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Response URL"
                                    name="ResponseURL"
                                    value={formData.ResponseURL}
                                    onChange={handleInputChange}
                                    error={errors.ResponseURL}
                                    helperText={errors.ResponseURL}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                                <CustomTextField
                                    label="Checksum"
                                    name="CheckSum"
                                    value={formData.CheckSum}
                                    onChange={handleInputChange}
                                    error={errors.CheckSum}
                                    helperText={errors.CheckSum}
                                    isRequired
                                    fullWidth
                                    isDisabled
                                />
                            </Box>
                        </Box>

                        {/* Actions */}
                        <Box sx={{ gridColumn: '1 / -1', mt: 2 }}>
                            <Button
                                type="submit"
                                variant="contained"
                                fullWidth
                                disabled={!isDataComplete}
                                sx={{
                                    py: 1.5,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    boxShadow: 2,
                                    backgroundColor: '#528A7E',
                                    '&:hover': {
                                        boxShadow: 4,
                                        backgroundColor: '#3D685E',
                                    },
                                }}
                            >
                                Proceed to Payment
                            </Button>

                            <Box sx={{ my: 2, textAlign: 'center' }}>
                                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                    OR
                                </Typography>
                            </Box>

                            <Button
                                onClick={handlePayLater}
                                variant="contained"
                                fullWidth
                                disabled={!isDataComplete}
                                sx={{
                                    py: 1.5,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    boxShadow: 2,
                                    backgroundColor: '#528A7E',
                                    '&:hover': {
                                        boxShadow: 4,
                                        backgroundColor: '#3D685E',
                                    },
                                }}
                            >
                                Pay Later
                            </Button>
                        </Box>
                    </Box>
                </Box>
            </Container>
        </>
    );
};

export default PaymentForm;
