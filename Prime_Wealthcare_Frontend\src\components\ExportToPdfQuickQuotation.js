import React from 'react';
import { Button } from '@mui/material';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';

const ExportToPdfQuickQuotation = ({ quotationData }) => {
    // Add console log to check incoming props

    const logoMapping = {
        'FUTURE GENRALI INDIA INSURANCE CO. LTD.': '/future-generali-logo.png',
        'Another Company': '/another-company-logo.png',
    };

    const handleExport = async () => {
        try {
            const doc = new jsPDF({ orientation: 'portrait' });
            const pageWidth = doc.internal.pageSize.getWidth();
            const pageHeight = doc.internal.pageSize.getHeight();

            // Draw Border
            doc.setDrawColor(0);
            doc.rect(5, 5, pageWidth - 10, pageHeight - 10);

            // Load and add logos
            try {
                // Main Logo
                const mainLogoData = await loadImage('/prime_logo-01.png');
                const logoWidth = 60;
                const mainLogoAspectRatio = mainLogoData.width / mainLogoData.height;
                const mainLogoHeight = logoWidth / mainLogoAspectRatio;
                
                doc.addImage(
                    mainLogoData.dataUrl,
                    'PNG',
                    (pageWidth - logoWidth) / 2,
                    10,
                    logoWidth,
                    mainLogoHeight
                );

                // Greeting Section
                const greetingYPosition = mainLogoHeight + 20;
                doc.setFontSize(14);
                doc.setTextColor(0);
                doc.text(`Dear Customer`, pageWidth / 2, greetingYPosition, { align: 'center' });

                // Customize greeting based on product type
                const greetingText = quotationData.policyDetails.product?.toLowerCase().includes('accident') 
                    ? 'Please find below your Personal Accident insurance quotation for'
                    : 'Please find below your health insurance quotation for';
                doc.text(greetingText, pageWidth / 2, greetingYPosition + 10, { align: 'center' });

                // Company Logo
                const selectedCompany = quotationData?.policyDetails?.company_name?.trim() || '';
                const fgLogoPath = logoMapping[selectedCompany] || '/logo_icon.png';
                const fgLogoData = await loadImage(fgLogoPath);
                
                const fgLogoWidth = 40;
                const fgLogoHeight = 20;
                doc.addImage(
                    fgLogoData.dataUrl,
                    'PNG',
                    (pageWidth - fgLogoWidth) / 2,
                    greetingYPosition + 20,
                    fgLogoWidth,
                    fgLogoHeight
                );

                // Product Info
                const productInfoY = greetingYPosition + 50;
                doc.setFontSize(14);
                doc.text(
                    `${quotationData.policyDetails.product} ${quotationData.policyDetails.policyType} - ${quotationData.policyDetails.coverType || 'Accident Surakash'}`,
                    pageWidth / 2,
                    productInfoY,
                    { align: 'center' }
                );

                let currentY = productInfoY + 20;

                // Generate appropriate table based on policy type
                const isFloater = quotationData.policyDetails.policyType?.toLowerCase().includes('floater');
                const isPAQuotation = quotationData.policyDetails.product?.toLowerCase().includes('pa') || 
                                    quotationData.policyDetails.product?.toLowerCase().includes('accident');

                if (isPAQuotation) {
                    currentY = generatePATable(doc, quotationData, currentY);
                } else if (isFloater) {
                    currentY = generateFloaterTable(doc, quotationData, currentY);
                } else {
                    currentY = generateIndividualTable(doc, quotationData, currentY);
                }

                // After table generation, add footer content
                let finalY = doc.lastAutoTable.finalY + 5;

                // Add verification message in white background with reduced spacing
                doc.setFontSize(12);
                doc.setTextColor(0, 0, 0); // Black text
                doc.text(
                    'Above quotation is subject to verification by insurance company',
                    pageWidth / 2,
                    finalY + 10,
                    { align: 'center' }
                );

                // Add the new terms and conditions line
                doc.setFontSize(11);
                doc.setFont(undefined, 'italic'); // Make it italic to stand out
                doc.text(
                    '*Premium may vary on basis of age slab',
                    pageWidth / 2,
                    finalY + 16,
                    { align: 'center' }
                );

                // Reset font style for remaining text
                doc.setFont(undefined, 'normal');
                doc.setFontSize(12);
                doc.text(
                    'For more information, please contact',
                    pageWidth / 2,
                    finalY + 22, // Adjusted spacing to accommodate new line
                    { align: 'center' }
                );

                // Add contact details in white background with reduced spacing
                doc.setFontSize(14);
                doc.setFont(undefined, 'bold');
                doc.text(
                    'PIYUSH PANDYA',
                    pageWidth / 2,
                    finalY + 30,
                    { align: 'center' }
                );
                doc.text(
                    'Risk Manager',
                    pageWidth / 2,
                    finalY + 35,
                    { align: 'center' }
                );

                // Add contact information with reduced spacing
                doc.setFontSize(12);
                doc.setFont(undefined, 'normal');
                doc.text(
                    '+91 9725245005',
                    pageWidth / 2,
                    finalY + 42,
                    { align: 'center' }
                );
                doc.text(
                    '<EMAIL>',
                    pageWidth / 2,
                    finalY + 47,
                    { align: 'center' }
                );

                // Add green background footer with reduced spacing
                const footerHeight = 40;
                const footerY = pageHeight - 45; // Fixed position from bottom

                doc.setFillColor(82, 138, 126); // Using your theme color
                doc.rect(5, footerY, pageWidth - 10, footerHeight, 'F');

                doc.setFontSize(10);
                doc.setTextColor(255, 255, 255); // White text
                doc.text(
                    'Regd: 403/404 Bhavani Skyline, Atabhai Road, Nr. Piyusha Fast Food, Opp. Jogger\'s Park, Bhavnagar - 364002',
                    pageWidth / 2,
                    footerY + 15,
                    { align: 'center' }
                );
                doc.text(
                    'Website: www.primewealthcare.com',
                    pageWidth / 2,
                    footerY + 25,
                    { align: 'center' }
                );

                doc.save('quick_quotation.pdf');

            } catch (error) {
                console.error('Error processing images:', error);
            }
        } catch (error) {
            console.error('Error in handleExport:', error);
        }
    };

    // Table generation functions
    const generatePATable = (doc, data, startY) => {
        try {
            
            if (!data?.results?.premiums || !data?.results?.emiDetails) {
                console.error('Missing required data:', data);
                throw new Error('Invalid data structure');
            }

            // Get page dimensions
            const pageWidth = doc.internal.pageSize.getWidth();
            const pageHeight = doc.internal.pageSize.getHeight();
            const footerHeight = 45;
            const marginBottom = 10;

            // Add border to first page
            doc.setDrawColor(0);
            doc.rect(5, 5, pageWidth - 10, pageHeight - 10);

            const premiums = data.results.premiums || [];
            const emiDetails = data.results.emiDetails || [];

            // Check if any member has TTD > 0
            const showTTColumn = premiums.some(member => parseFloat(member.sumInsured_TTD || 0) > 0);

            // Generate sum insured table first
            const sumInsuredHeaders = [
                [{
                    content: 'Members Sum Insured',
                    colSpan: showTTColumn ? 5 : 4,
                    styles: { 
                        halign: 'center',
                        fillColor: [255, 255, 255],
                        textColor: [0, 0, 0],
                        fontStyle: 'bold'
                    }
                }],
                showTTColumn 
                    ? ['Relation', 'Accidental death', 'Permanent Partial Disabilement', 'Permanent Total Disablement', 'Temporary Total Disablement']
                    : ['Relation', 'Accidental death', 'Permanent Partial Disabilement', 'Permanent Total Disablement']
            ];

            const sumInsuredRows = premiums.map(member => {
                const row = [
                    member.relation || 'N/A',
                    member.sumInsured_AD?.toLocaleString() || 'N/A',
                    member.sumInsured_PP?.toLocaleString() || 'N/A',
                    member.sumInsured_PT?.toLocaleString() || 'N/A'
                ];
                if (showTTColumn) {
                    row.push(member.sumInsured_TTD?.toLocaleString() || 'N/A');
                }
                return row;
            });

            // Generate sum insured table
            autoTable(doc, {
                startY: startY - 5,
                head: sumInsuredHeaders,
                body: sumInsuredRows,
                styles: {
                    halign: 'center',
                    fontSize: 12,
                    cellPadding: 2,
                    lineWidth: 0.5,
                    lineColor: [0, 0, 0]
                },
                headStyles: {
                    fillColor: [82, 138, 126],
                    textColor: [255, 255, 255],
                    fontStyle: 'bold'
                },
                margin: { left: 10, right: 10 },
                theme: 'grid'
            });

            let currentY = doc.lastAutoTable.finalY + 15;

            // Generate a table for each duration
            emiDetails.forEach((emiDetail, index) => {
                // Check if we need a new page
                const estimatedTableHeight = 10; // Approximate height for each premium table
                if (currentY + estimatedTableHeight > pageHeight - footerHeight) {
                    doc.addPage();
                    doc.setDrawColor(0);
                    doc.rect(5, 5, pageWidth - 10, pageHeight - 10);
                    currentY = 20;
                }

                // Premium details table for each duration
                const tableHeaders = [
                    [{
                        content: `Duration - ${emiDetail.duration} Year${emiDetail.duration > 1 ? 's' : ''}`,
                        colSpan: 4,
                        styles: { 
                            halign: 'center',
                            fillColor: [255, 255, 255],
                            textColor: [0, 0, 0],
                            fontStyle: 'bold'
                        }
                    }],
                    ['Net Premium', 'Family Discount', 'Long Term Discount', 'Total Premium']
                ];

                const premiumRow = [
                    Math.round(parseFloat(emiDetail.fullPayment?.premium || 0)).toLocaleString(),
                    Math.round(parseFloat(emiDetail.discounts?.family?.amount || 0)).toLocaleString(),
                    Math.round(parseFloat(emiDetail.discounts?.longTerm?.amount || 0)).toLocaleString(),
                    Math.round(parseFloat(emiDetail.fullPayment?.totalAmount || 0)).toLocaleString()
                ];

                // Generate premium table for this duration
                autoTable(doc, {
                    startY: currentY,
                    head: tableHeaders,
                    body: [premiumRow],
                    styles: {
                        halign: 'center',
                        fontSize: 12,
                        cellPadding: 2,
                        lineWidth: 0.5,
                        lineColor: [0, 0, 0]
                    },
                    headStyles: {
                        fillColor: [82, 138, 126],
                        textColor: [255, 255, 255],
                        fontStyle: 'bold'
                    },
                    margin: { left: 10, right: 10 },
                    theme: 'grid'
                });

                currentY = doc.lastAutoTable.finalY + (index < emiDetails.length - 1 ? 15 : 0);
            });

            return currentY;
        } catch (error) {
            console.error('Error in generatePATable:', error);
            throw error;
        }
    };

    const generateFloaterTable = (doc, data, startY) => {
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const footerHeight = 30;
        const marginBottom = footerHeight + 10;

        const addPageBorder = () => {
            doc.setDrawColor(0);
            doc.setLineWidth(0.5);
            doc.rect(5, 5, pageWidth - 10, pageHeight - 10);
        };

        let currentY = startY;

        // Group by sum insured
        const sumInsuredGroups = data.results.reduce((acc, item) => {
            const si = item.sumInsured;
            if (!acc[si]) {
                acc[si] = {
                    premiums: item.premiums,
                    outputResponse: item.outputResponse
                };
            }
            return acc;
        }, {});

        // Process each sum insured group
        Object.entries(sumInsuredGroups).forEach(([sumInsured, groupData], index) => {
            // Calculate if new page is needed
            const headerHeight = 20; // Height for SI header
            const estimatedTableHeight = (groupData.premiums.length + 4) * 15 + headerHeight; // +4 for header and summary rows

            if (needsNewPage(currentY, estimatedTableHeight, pageHeight, marginBottom)) {
                doc.addPage();
                addPageBorder();
                currentY = 20;

                // Repeat the product header on new page
                doc.setFontSize(13);
                //doc.setTextColor(82, 138, 126);
                doc.text(
                    `${data.policyDetails.product} ${data.policyDetails.policyType.toUpperCase()} - SI ${parseInt(sumInsured).toLocaleString()}`,
                    pageWidth / 2,
                    currentY,
                    { align: 'center' }
                );
                currentY += 10;
            } else {
                // Add SI header on same page
                doc.setFontSize(13);
               // doc.setTextColor(82, 138, 126);
                doc.text(
                    `${data.policyDetails.product} ${data.policyDetails.policyType.toUpperCase()} - SI ${parseInt(sumInsured).toLocaleString()}`,
                    pageWidth / 2,
                    currentY,
                    { align: 'center' }
                );
                currentY += 10;
            }

            const tableHeaders = [
                ['Relation', 'Cover Type', 'Sum Insured', 'Premium']
            ];

            const tableRows = groupData.premiums.map(member => [
                member.relation || 'N/A',
                member.coverType || 'N/A',
                `${ Math.round(parseFloat(member.sumInsured)).toLocaleString()}`,
                `${ Math.round(parseFloat(member.basePremium)).toLocaleString()}`
            ]);

            // Add premium summary rows
            const outputResponse = groupData.outputResponse;
            tableRows.push(
                ['', '', 'Basic Premium', `${ Math.round(parseFloat(outputResponse.premiumAmt)).toLocaleString()}`],
                ['', '', 'GST (18%)', `${ Math.round(parseFloat(outputResponse.serviceTax)).toLocaleString()}`],
                ['', '', 'Total Premium', `${ Math.round(parseFloat(outputResponse.premWithServiceTax)).toLocaleString()}`]
            );

            // Generate table
            autoTable(doc, {
                startY: currentY,
                head: tableHeaders,
                body: tableRows,
                styles: {
                    halign: 'center',
                    fontSize: 12,
                    cellPadding: 2,
                    lineWidth: 0.5,
                    lineColor: [0, 0, 0],
                    minCellHeight: 10
                },
                headStyles: {
                    fillColor: [82, 138, 126],
                    textColor: [255, 255, 255],
                    fontStyle: 'bold',
                    minCellHeight: 12
                },
                
                didParseCell: function(data) {
                    if (data.row.raw) {
                        if (data.row.raw[2] === 'Total Premium') {
                            data.cell.styles.fillColor = [128, 0, 0];
                            data.cell.styles.textColor = [255, 255, 255];
                            data.cell.styles.fontStyle = 'bold';
                        } else if (data.row.raw[2] === 'Basic Premium' || data.row.raw[2] === 'GST (18%)') {
                            data.cell.styles.fontStyle = 'bold';
                        }
                    }
                },
                margin: { left: 15, right: 15 },
                theme: 'grid',
                showHead: 'everyPage', // This ensures headers are shown on every page
                tableWidth:180,
                didDrawPage: function(data) {
                    // Add border to new pages
                    addPageBorder();
                }
            });

            currentY = doc.lastAutoTable.finalY + (index < Object.keys(sumInsuredGroups).length - 1 ? 20 : 0);
        });

        return currentY;
    };

    // Helper function to check if we need a new page
    const needsNewPage = (currentY, requiredHeight, pageHeight, marginBottom) => {
        return (currentY + requiredHeight + marginBottom) > pageHeight;
    };

    const generateIndividualTable = (doc, data, startY) => {
        // Similar structure as floater but without grouping
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const footerHeight = 30;
        const marginBottom = footerHeight + 10;

        const premiums = data.results.premiums || [];
        const outputResponse = data.results.outputResponse || {};
        let currentY = startY;

        const tableHeaders = [
            ['Relation', 'Sum Insured', 'Premium']
        ];

        const tableRows = premiums.map(member => [
            member.relation || 'N/A',
            //member.age || 'N/A',
            parseInt(member.sumInsured || 0).toLocaleString(),
            Math.round(parseFloat(member.basePremium || 0)).toLocaleString()
        ]);

        // Add totals
        tableRows.push(
            ['', '', 'Basic Premium', Math.round(parseFloat(outputResponse.premiumAmt || 0)).toLocaleString()],
            ['', '', 'GST (18%)', Math.round(parseFloat(outputResponse.serviceTax || 0)).toLocaleString()],
            ['', '', 'Total Premium', Math.round(parseFloat(outputResponse.premWithServiceTax || 0)).toLocaleString()]
        );

        autoTable(doc, {
            startY: currentY,
            head: tableHeaders,
            body: tableRows,
            styles: {
                halign: 'center',
                fontSize: 12,
                cellPadding: 2,
                lineWidth: 0.5,
                lineColor: [0, 0, 0],
                minCellHeight: 10
            },
            headStyles: {
                fillColor: [82, 138, 126],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                minCellHeight: 15
            },
            didParseCell: function(data) {
                if (data.row.raw && data.row.raw[2] === 'Total Premium') {
                    data.cell.styles.fillColor = [128, 0, 0];
                    data.cell.styles.textColor = [255, 255, 255];
                    data.cell.styles.fontStyle = 'bold';
                }
            },
            margin: { left: 15, right: 15 },
            theme: 'grid'
        });

        return doc.lastAutoTable.finalY;
    };

    // Helper function to load images
    const loadImage = (src) => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                resolve({
                    dataUrl: canvas.toDataURL('image/png'),
                    width: img.width,
                    height: img.height
                });
            };
            img.onerror = reject;
            img.src = src;
        });
    };

    return (
        <Button
            variant="outlined"
            color="primary"
            onClick={() => {
                handleExport();
            }}
            startIcon={<PictureAsPdfIcon />}
            sx={{
                backgroundColor: '#fff',
                borderColor: '#528a7e',
                color: '#528a7e',
                '&:hover': {
                    backgroundColor: '#f5f5f5',
                    borderColor: '#528a7e',
                },
                ml: 2
            }}
        >
            Export PDF
        </Button>
    );
};

export default ExportToPdfQuickQuotation; 