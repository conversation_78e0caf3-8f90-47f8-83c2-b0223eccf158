import { createSlice } from '@reduxjs/toolkit';
import {
  getAllTasks,
  createTask,
  updateTask,
  getTaskById,
  getTasksByAssignedTo,
  getTasksByAssignedBy,
  deleteTask,
  createTaskComment,
  addCommentReply,
  updateTaskNotification,
  updateCommentNotification,
  getTaskDetails,
  getTaskNotifications
} from '../../actions/action';
import { toast } from 'react-toastify';

const initialState = {
  tasks: {
    todo: [],
    inProgress: [],
    done: []
  },
  taskDetails: [],
  currentTask: null,
  loading: false,
  error: null,
  comments: [],
  notifications: {
    comments: [],
    newTasks: [],
    unreadCount: 0,
    taskNotifications: [],
    loading: false,
    error: null
  }
};

const taskSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    clearTaskDetails: (state) => {
      state.currentTask = null;
      state.error = null;
    },
    organizeTasksByStatus: (state, action) => {
      const tasks = action.payload;
      state.tasks = {
        todo: tasks.filter(task => task.status === 'To-Do'),
        inProgress: tasks.filter(task => task.status === 'In Progress'),
        done: tasks.filter(task => task.status === 'Completed')
      };
    },
    addTaskNotification: (state, action) => {
      state.notifications.newTasks.push(action.payload);
      state.notifications.unreadCount += 1;
    },
    clearTaskNotifications: (state) => {
      state.notifications.newTasks = [];
      state.notifications.unreadCount = 0;
    }
  },
  extraReducers: (builder) => {
    builder
      // ... existing cases for getAllTasks, createTask, updateTask, getTaskById ...

      // Get Tasks by Assigned To
      .addCase(getTasksByAssignedTo.pending, (state) => {
        state.loading = true;
      })
      .addCase(getTasksByAssignedTo.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
      })
      .addCase(getTasksByAssignedTo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error('Failed to fetch assigned tasks');
      })

      // Delete Task
      .addCase(deleteTask.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.loading = false;
        Object.keys(state.tasks).forEach(status => {
          state.tasks[status] = state.tasks[status].filter(
            task => task.id !== action.payload
          );
        });
        toast.success('Task deleted successfully');
      })
      .addCase(deleteTask.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error('Failed to delete task');
      })

      // Create Comment
      .addCase(createTaskComment.pending, (state) => {
        state.loading = true;
      })
      .addCase(createTaskComment.fulfilled, (state, action) => {
        state.loading = false;
        const { taskId, comment } = action.payload;
        
        // Initialize currentTask if null
        if (!state.currentTask) {
          state.currentTask = {
            id: taskId,
            comments: []
          };
        }
        
        // Initialize comments array if null
        if (!state.currentTask.comments) {
          state.currentTask.comments = [];
        }
        
        state.currentTask.comments.push(comment);
        
        // Also update the comment in tasks list if present
        if (Array.isArray(state.tasks)) {
          const task = state.tasks.find(t => t.id === taskId);
          if (task) {
            if (!task.comments) {
              task.comments = [];
            }
            task.comments.push(comment);
          }
        }
        
        toast.success('Comment added successfully');
      })
      .addCase(createTaskComment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error('Failed to add comment');
      })

      // get task details 
      .addCase(getTaskDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(getTaskDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
      })
      .addCase(getTaskDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error('Failed to fetch task details');
      })

      // Update Comment Notification
      .addCase(updateCommentNotification.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateCommentNotification.fulfilled, (state, action) => {
        state.loading = false;
        
        // Update comment read status in currentTask
        if (state.currentTask && state.currentTask.comments) {
          const comment = state.currentTask.comments.find(
            c => c.comment_id === action.payload.commentId
          );
          if (comment) {
            comment.is_read = 1;
          }
        }

        // Update comment read status in tasks list
        if (Array.isArray(state.tasks)) {
          const task = state.tasks.find(t => t.id === action.payload.taskId);
          if (task && task.comments) {
            const comment = task.comments.find(
              c => c.comment_id === action.payload.commentId
            );
            if (comment) {
              comment.is_read = 1;
            }
          }
        }
      })
      .addCase(updateCommentNotification.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        toast.error('Failed to update notification status');
      })

      .addCase(createTask.fulfilled, (state, action) => {
        
        if (!action.payload?.task) {
            console.error('No task data in payload');
            return;
        }

        const taskData = action.payload.task;

        // Add notification for assigned employee
        const notification = {
            id: taskData.id,
            title: taskData.title,
            task_code: taskData.task_code,
            description: taskData.description,
            status: taskData.status || 'To-Do',
            assigned_to: taskData.assigned_to, // This should now be the employee ID
            assigned_by: taskData.assigned_by, // This should also be employee ID
            timestamp: new Date().toISOString(),
            is_read: false
        };

        state.notifications.newTasks.push(notification);
        state.notifications.unreadCount += 1;
      })

      // Get Task Notifications
      .addCase(getTaskNotifications.pending, (state) => {
        state.notifications.loading = true;
        state.notifications.error = null;
      })
      .addCase(getTaskNotifications.fulfilled, (state, action) => {
        state.notifications.loading = false;
        state.notifications.taskNotifications = action.payload;
        
        // Update unread count
        state.notifications.unreadCount = action.payload.filter(
            notification => !notification.is_read
        ).length;
        
      })
      .addCase(getTaskNotifications.rejected, (state, action) => {
        state.notifications.loading = false;
        state.notifications.error = action.payload;
        console.error('Failed to load task notifications:', action.payload);
      })
      .addCase(updateTaskNotification.fulfilled, (state, action) => {
        // Update the notification in taskNotifications array
        const notification = state.notifications.taskNotifications.find(
            n => n.task_id === action.payload.taskId && 
                 n.user_id === action.payload.userId
        );
        if (notification) {
            notification.is_read = 1;
        }

        // Update unread count
        state.notifications.unreadCount = state.notifications.taskNotifications.filter(
            n => !n.is_read
        ).length;
      });
  }
});

export const { clearTaskDetails, organizeTasksByStatus, addTaskNotification, clearTaskNotifications } = taskSlice.actions;
export default taskSlice.reducer;