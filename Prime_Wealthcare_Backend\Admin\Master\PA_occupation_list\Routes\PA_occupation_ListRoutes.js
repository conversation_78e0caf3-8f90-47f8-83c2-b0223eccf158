// routes/pickListRoutes.js
const express = require('express');
const router = express.Router();
const PA_Controller = require('../Controllers/PA_Controller');

// Define routes
router.get('/', PA_Controller.getAllPA_occupation_list);
router.get('/:typeName', PA_Controller.getPA_occupation_listByTypeName);
router.post('/', PA_Controller.createPA_occupation_list);
router.put('/:id', PA_Controller.updatePA_occupation_list);
router.delete('/:id', PA_Controller.deletePA_occupation_list);
router.post('/soft-delete/:id', PA_Controller.softDeletePA_occupation_list);
router.post('/reinstate/:id', PA_Controller.reinstatePA_occupation_list);

module.exports = router;
