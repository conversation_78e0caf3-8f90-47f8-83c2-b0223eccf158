import { createSlice } from '@reduxjs/toolkit';
import {
    createEmployeeBankDetails,
    getEmployeeBankDetailsByEmployeeId,
    getAllEmployeeBankDetails,
    getEmployeeBankDetailsById,
    updateEmployeeBankDetails,
    deleteEmployeeBankDetails,
    deleteFirstBankDetails,
    deleteSecondBankDetails,
} from '../../actions/action';

const initialState = {
    employeeBankDetails: [],
    employeeBankDetail: null,
    loading: false,
    error: null,
};

const employeeBankSlice = createSlice({
    name: 'employeeBank',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(createEmployeeBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(createEmployeeBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetails.push(action.payload);
            })
            .addCase(createEmployeeBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getEmployeeBankDetailsByEmployeeId.pending, (state) => {
                state.loading = true;
            })
            .addCase(getEmployeeBankDetailsByEmployeeId.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetails = action.payload;
            })
            .addCase(getEmployeeBankDetailsByEmployeeId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getAllEmployeeBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllEmployeeBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetails = action.payload;
            })
            .addCase(getAllEmployeeBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getEmployeeBankDetailsById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getEmployeeBankDetailsById.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetail = action.payload;
            })
            .addCase(getEmployeeBankDetailsById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(updateEmployeeBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(updateEmployeeBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                const index = state.employeeBankDetails.findIndex(detail => detail.id === action.payload.id);
                if (index !== -1) {
                    state.employeeBankDetails[index] = action.payload;
                }
            })
            .addCase(updateEmployeeBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(deleteEmployeeBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteEmployeeBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetails = state.employeeBankDetails.filter(detail => detail.id !== action.payload);
            })
            .addCase(deleteEmployeeBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            .addCase(deleteFirstBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteFirstBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetails = state.employeeBankDetails.filter(detail => detail.id !== action.payload);
            })
            .addCase(deleteFirstBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(deleteSecondBankDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteSecondBankDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.employeeBankDetails = state.employeeBankDetails.filter(detail => detail.id !== action.payload);
            })
            .addCase(deleteSecondBankDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
        // ... existing code ...
    },
});

export const { clearError } = employeeBankSlice.actions;

export default employeeBankSlice.reducer;
