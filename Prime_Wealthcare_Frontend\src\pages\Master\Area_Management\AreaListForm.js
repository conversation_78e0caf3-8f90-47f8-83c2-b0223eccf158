import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { createLocation, createArea, getAreasByPincodeAndCity, getLocationById, updateArea, deleteArea, getLocationByPincodeAndCity } from '../../../redux/actions/action';
import CustomTable from '../../../components/table/CustomTable';
import { trimFormData } from '../../../utils/Reusable';

function AreaListForm() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedRows, setSelectedRows] = useState([]);
    const [locationFormData, setLocationFormData] = useState({ state: "", city: "", pincode: "" });
    const [areaFormData, setAreaFormData] = useState({ area: "" });
    const [areasData, setAreasData] = useState([]);
    const [errors, setErrors] = useState({ state: false, city: false, area: false, pincode: false });
    const [addingArea, setAddingArea] = useState(false);

    const location = useSelector(state => state.areaManagementReducer.location);
    const areas = useSelector(state => state.areaManagementReducer.areas);

    useEffect(() => {
        if (id) dispatch(getLocationById(id));
    }, [id, dispatch]);

    useEffect(() => {
        if (location && id) {
            setLocationFormData({
                state: location.state || '',
                city: location.city || '',
                pincode: location.pincode || ''
            });
            if (location.pincode) {
                dispatch(getAreasByPincodeAndCity({ pincode: location.pincode, city: location.city }));
            }
        }
    }, [id, location, dispatch]);

    useEffect(() => {
        if (areas && id) setAreasData(areas);
    }, [areas, id]);

    const validate = async () => {
        let tempErrors = {};
        if (!errors.city) {
            if (!locationFormData.city) {
                tempErrors.city = 'City is required';
            } else if (locationFormData.city.length < 3) {
                tempErrors.city = 'City should be at least 3 characters long';
            } else {
                await handleCityDuplicateCheck();
            }
            if (!locationFormData.state) tempErrors.state = 'State is required';
            if (!locationFormData.pincode || !/^[1-9][0-9]{5}$/.test(locationFormData.pincode)) {
                tempErrors.pincode = 'Pincode should be a 6-digit number starting with a non-zero digit';
            }
        }
        setErrors(prevErrors => ({ ...prevErrors, ...tempErrors }));
        return Object.keys(tempErrors).length === 0;
    };

    const validateArea = async () => {
        let tempErrors = {};
        if (areaFormData.area && id) {
            const isPresent = areas.find(area => area.area === areaFormData.area);
            if (isPresent && areaFormData.id !== isPresent.id) {
                tempErrors.area = 'Area already exists';
            }
        }
        setErrors(prevErrors => ({ ...prevErrors, ...tempErrors }));
        return Object.keys(tempErrors).length === 0;
    }

    const handleSaveAndNew = async () => {
        await handleLocationCreation().then(res => {
            if (res) {
                setLocationFormData({ state: "", city: "", pincode: '' });
            }
        });
    };

    const handleLocationFormSave = async () => {
        await handleLocationCreation().then((res) => {

            if (res) {
                navigate('/dashboard/area-list');
            }
        });
    };

    const handleLocationCreation = async () => {
        const valid = await validate();
        if (valid) {
            const createdLocationId = await dispatch(createLocation(trimFormData(locationFormData)));
            return createdLocationId.payload?.[0];
        }
    }

    const handleAreaCreation = async () => {
        const valid = await validateArea();
        if (!valid) {
            return;
        }
        const data = trimFormData(areaFormData);
        const newData = { ...data, pincode: locationFormData.pincode, city: locationFormData.city };
        await dispatch(createArea(newData));
    }

    const handleCancel = () => navigate('/dashboard/area-list');

    const handleLocationChange = (e) => {
        const { name, value } = e.target;
        if (value === ' ') {
            setErrors({ ...errors, [name]: 'Do not start with a whitespace character' });
            return;
        }
        const data = value.toUpperCase().replace(/\s{2,}$/, ' ');
        if (name === 'pincode' && (!Number.isInteger(Number(value)) || value.length > 6)) {
            setErrors({ ...errors, [name]: 'Please enter a valid pincode' });
            return;
        }
        setLocationFormData({ ...locationFormData, [name]: name === 'pincode' ? Number(data) || '' : data });
        setErrors({ ...errors, [name]: false });
    };

    const handleCityDuplicateCheck = async () => {
        const { pincode, city } = locationFormData;
        if (pincode && city) {
            const response = await dispatch(getLocationByPincodeAndCity({ pincode, city }));
            if (response.payload?.city === city) {
                setErrors({ ...errors, city: 'This city already exists for this pincode' });
                setLocationFormData({ ...locationFormData, city: '' });
            }
        }
    };

    const handleAreaSave = async () => {
        if (validateArea()) {
            let createdLocationId = null;
            if (!id) {
                await handleLocationCreation().then(async res => {
                    if (res) {
                        createdLocationId = res;
                        await handleAreaCreation();
                    }
                });
            } else {
                const data = trimFormData(areaFormData);
                if (areaFormData.id) {
                    await dispatch(updateArea({ id: data.id, data }));
                } else {
                    await handleAreaCreation();
                }
            }
            navigate(`/dashboard/area-form/edit/${createdLocationId || id}`);
            dispatch(getAreasByPincodeAndCity({ pincode: locationFormData.pincode, city: locationFormData.city }));
            setAreaFormData({ area: '' });
        }
    };

    const isLocationFormComplete = () => {
        const { state, city, pincode } = locationFormData;
        return state && city && pincode;
    };

    const handleAreaChange = (e) => {
        const { name, value } = e.target;
        if (value === ' ') {
            setErrors({ ...errors, [name]: 'Do not start with a whitespace character' });
            return;
        }
        setAreaFormData({ ...areaFormData, [name]: value.toUpperCase().replace(/\s{2,}$/, ' ') });
        setErrors({ ...errors, [name]: false });
        setAddingArea(value);
    };

    const handleDelete = (rowId) => dispatch(deleteArea(rowId));

    const handleEdit = (rowId) => {
        const area = areasData.find((area) => area.id === rowId);
        setAreaFormData(area);
    };

    return (
        <form>
            <Grid container spacing={2}>
                {/* Form Header */}
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                    <Box sx={{ width: '100%', margin: '0 20px' }}>
                        <ModuleName moduleName="Area" pageName={id ? location?.status === 0 ? "View" : "Edit" : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!id && !addingArea && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '120px', width: '120px', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleSaveAndNew}
                        >
                            Save & New
                        </Button>
                    )}
                    {!id && !addingArea && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleLocationFormSave}
                        >
                            Save
                        </Button>
                    )}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>

                {/* Area Information */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
                        <h2>Location Information</h2>
                    </Box>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Pincode"
                            name='pincode'
                            value={locationFormData.pincode}
                            onChange={handleLocationChange}
                            isDisabled={!!id}
                            isRequired
                            helperText={errors.pincode || ''}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label='State'
                            name='state'
                            value={locationFormData.state}
                            onChange={handleLocationChange}
                            isDisabled={!!id}
                            isRequired
                            helperText={errors.state || ''}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label='City'
                            name='city'
                            value={locationFormData.city}
                            onChange={handleLocationChange}
                            onBlur={handleCityDuplicateCheck}
                            isDisabled={!!id || !locationFormData.pincode || locationFormData.pincode.toString().length !== 6}
                            isRequired
                            helperText={errors.city || ''}
                        />
                    </Grid>
                </Box>

                {/* Area Information */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2, display: 'flex', justifyContent: 'space-between' }}>
                        <h2>Area Information</h2>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{ color: 'green', borderColor: 'green', textTransform: 'none', mx: 1 }}
                                onClick={handleAreaSave}
                                disabled={!isLocationFormComplete() || !areaFormData.area}
                            >
                                Save
                            </Button>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{ color: 'red', borderColor: 'red', textTransform: 'none' }}
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </Box>
                    </Box>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
                    <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Area"
                            name="area"
                            value={areaFormData.area}
                            onChange={handleAreaChange}
                            isRequired
                            helperText={errors.area || ''}
                            isDisabled={!isLocationFormComplete()}
                        />
                    </Grid>
                </Box>

                {/* Area Table */}
                <Grid item xs={12}>
                    <CustomTable
                        data={areasData}
                        columns={[
                            { field: 'area', headerName: 'Area' },
                            { field: 'pincode', headerName: 'Pincode' },
                        ]}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        onSelectAll={setSelectedRows}
                        onRowSelection={setSelectedRows}
                        selectedRows={selectedRows}
                        isCheckboxRequired={false}
                    />
                </Grid>
            </Grid>
        </form>
    );
}

export default AreaListForm;