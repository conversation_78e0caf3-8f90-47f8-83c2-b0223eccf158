import React, { useState } from 'react';
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Checkbox,
    ListItemText,
    FormHelperText,
} from '@mui/material';

const MultiSelectDropdown = ({
    label,
    helperText,
    required,
    options,
    value,
    onChange,
    fullWidth = false,
    width = 'auto',
    ...rest
}) => {
    // Helper function to handle selection changes
    const handleSelect = (event) => {
        onChange(event); // Directly pass the selected values
    };

    return (
        <FormControl fullWidth={fullWidth} sx={{ width: fullWidth ? '100%' : width }} required={required}>
            <InputLabel
                shrink={Array.isArray(value) && value.length > 0}
                sx={{
                    '& .MuiFormLabel-asterisk': {
                        display: 'none',
                    },
                    position: 'absolute',
                    transform: (Array.isArray(value) && value.length > 0)
                        ? 'translate(14px, -9px) scale(0.75)'
                        : 'translate(14px, 16px)',
                    background: (Array.isArray(value) && value.length > 0) ? '#fff' : 'transparent',
                    padding: (Array.isArray(value) && value.length > 0) ? '0 4px' : '0',
                    pointerEvents: 'none',
                }}
            >
                {label}
            </InputLabel>
            <Select
                multiple
                value={value}
                onChange={handleSelect}
                renderValue={(selected) => {
                    // Convert IDs to company names using options
                    const selectedNames = selected.map(id =>
                        options.find(option => option.value === id)?.label || id
                    );
                    return selectedNames.join(', ');
                }}
                MenuProps={{
                    PaperProps: {
                        style: {
                            maxHeight: 250,
                            maxWidth: 250,
                        },
                    },
                }}
                {...rest}
            >
                {options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                        <Checkbox checked={value.indexOf(option.value) > -1} />
                        <ListItemText primary={option.label} />
                    </MenuItem>
                ))}
            </Select>
            {helperText && <FormHelperText sx={{ color: 'red' }}>{helperText}</FormHelperText>}
        </FormControl>
    );
};

export default MultiSelectDropdown;
