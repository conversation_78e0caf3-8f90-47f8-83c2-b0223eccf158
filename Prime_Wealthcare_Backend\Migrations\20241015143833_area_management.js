exports.up = function (knex) {
  return knex.schema
    .createTable('locations', function (table) {
      table.increments('id').primary(); // Auto-incrementing ID
      table.integer('pincode').notNullable();
      table.string('city').notNullable();
      table.index('city'); 
      table.string('state').notNullable();
      table.integer('created_by').notNullable().defaultTo(1);
      table.integer('updated_by').notNullable().defaultTo(1);
      table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
      table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
      table.boolean('status').notNullable().defaultTo(true);
      table.index('pincode_city');
      table.string('pincode_city', 255).unique().notNullable();
    })
    .createTable('areas', function (table) {
      table.increments('id').primary(); // Auto-incrementing ID
      table.integer('pincode').notNullable();
      table.string('city').notNullable();
      table.string('area').notNullable();
      table.string('pincode_city').references('pincode_city').inTable('locations').onDelete('CASCADE');
      table.foreign('city'); // Define foreign key correctly
      table.boolean('status').notNullable().defaultTo(true);
    });
};

exports.down = function (knex) {
  return knex.schema
    .dropTable('areas')
    .dropTable('locations');
};
