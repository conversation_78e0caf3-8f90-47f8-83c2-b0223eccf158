import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { checkUserAccess } from '../redux/actions/action';
import { Navigate } from 'react-router-dom';

const withAccessControl = (WrappedComponent, moduleName, pageName) => {
    return function WithAccessControl(props) {
        const [permissions, setPermissions] = useState({
            can_view: false,
            can_add: false,
            can_edit: false,
            can_delete: false
        });
        const [loading, setLoading] = useState(true);
        const user = useSelector(state => state.auth.user);
        const dispatch = useDispatch();

        useEffect(() => {
            const checkAccess = async () => {
                if (!user?.userId) {
                    setLoading(false);
                    return;
                }

                try {
                    const response = await dispatch(checkUserAccess({
                        user_id: user.userId,
                        module_name: moduleName,
                        page_name: pageName
                    })).unwrap();

                    setPermissions(response.data);
                } catch (error) {
                    console.error('Access check failed:', error);
                    setPermissions({
                        can_view: false,
                        can_add: false,
                        can_edit: false,
                        can_delete: false
                    });
                }
                setLoading(false);
            };

            checkAccess();
        }, [user, moduleName, pageName]);

        if (loading) {
            return <div>Loading...</div>;
        }

        if (!permissions.can_view) {
            return <Navigate to="/unauthorized" replace />;
        }

        return <WrappedComponent {...props} permissions={permissions} />;
    };
};

export default withAccessControl; 