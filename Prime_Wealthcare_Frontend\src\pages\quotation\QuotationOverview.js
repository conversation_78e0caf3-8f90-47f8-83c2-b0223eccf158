import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, Container, Grid, Typography, Checkbox, Button, duration } from '@mui/material';
import ModuleName from '../../components/table/ModuleName';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { fetchInsuranceCompanyById, getCustomerById, getQuotationResponseById, getAgentById, getProposalByQuotationNumber, getMasterProductById, getProductById, getCustomerAddressByCustomerId, getLocationById } from '../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import BarLoader from '../../components/BarLoader';
import ExportToPdfQuotation from '../../components/ExportToPdfQuotation'
import { setSelectedQuotation } from '../../redux/slices/proposal/proposalSlice';
import { toast } from 'react-toastify';


function QuotationOverview() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { id, tableSource } = useParams();
    const [loading, setLoading] = useState(false);
    const [groupedData, setGroupedData] = useState({});

    useEffect(() => {
        dispatch(getQuotationResponseById({ id, tableSource }));
    }, [dispatch, id, tableSource]);

    useEffect(() => {
        if (!window.location.hash) {
            window.location = window.location + '#loaded';
            window.location.reload();
        }
    }, []);

    // useEffect(() => {
    //     const fetchData = async () => {
    //         setLoading(true); // Start loading
    //         await dispatch(getQuotationResponseById(id)); // Dispatch the action to fetch data by ID
    //         setLoading(false); // Stop loading after fetching
    //     };
    //     fetchData();
    // }, [dispatch, id]);
    //const quotationData = useSelector(state => state.quotationReducer.quotationResponse[tableSource] || []);
    // Update how quotationData is accessed from the reducer
    // Helper function to parse DD/MM/YYYY format
    const parseDate = (dateStr) => {
        const [day, month, year] = dateStr.split('/');
        return new Date(year, month - 1, day);
    };

    const quotationResponse = useSelector(state => state.quotationReducer.quotationResponse);

    const lostQuotations = quotationResponse?.quotations?.filter(q => {
        if (!q.quotation_created_at || q.quotation_status !== 'Pending') return false;

        const createdDate = new Date(q.quotation_created_at.split('/').reverse().join('-')); // assuming format is "DD/MM/YYYY"
        const currentDate = new Date();

        // Calculate difference in days
        const diffTime = currentDate - createdDate;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);

        return diffDays > 30;
    });

    const quotationData = useMemo(() => {
        const response = quotationResponse[tableSource];
        if (!response) return [];

        if (tableSource === 'pa_quotations') {
            if (!response.data) return [];

            return [response.data];
        }

        return Array.isArray(response) ? response : [];
    }, [quotationResponse, tableSource]);

    const memoizedDispatch = useCallback((action) => {
        dispatch(action);
    }, [dispatch]);

    useEffect(() => {
        const fetchData = async () => {
            if (!quotationData?.length || !quotationData[0]) return;

            setLoading(true);
            const quotation = quotationData[0]; // Get the first quotation data

            // Check if the product is "Accident Suraksha"
            if (quotation.product === 7) { // Assuming 7 is the ID for "Accident Suraksha"
                // Fetch proposal data using a different action if needed
                await memoizedDispatch(getProposalByQuotationNumber(quotation.quotation_number));
            } else if (!quotation.responses) {
                await memoizedDispatch(getProposalByQuotationNumber(quotation.quotation_number));
            }
            setLoading(false);
        };
        fetchData();
    }, [quotationData, memoizedDispatch]);
    const fetchedProposal = useSelector(state => state.proposalReducer.proposal);

    useEffect(() => {
        if (tableSource === 'pa_quotations') {
            const customerId = quotationData[0]?.customer_id;
            if (customerId) {
                memoizedDispatch(getCustomerById(customerId));
            }
        } else {
            if (!quotationData?.length || !quotationData[0]) return;
            memoizedDispatch(getCustomerById(quotationData[0].customer_id));
        }
    }, [quotationData, memoizedDispatch, tableSource]);

    useEffect(() => {
        if (!quotationData?.length || !quotationData[0]) return;
        const insuranceCompanyId = quotationData[0].insurance_company;
        const product_master_id = quotationData[0].product;
        memoizedDispatch(fetchInsuranceCompanyById(insuranceCompanyId));
        memoizedDispatch(getMasterProductById(product_master_id));
    }, [quotationData, memoizedDispatch]);

    useEffect(() => {
        if (quotationData && quotationData.length > 0) {
            const agentId = quotationData[0].agent_id; // Assuming agent_id is in your quotation data
            dispatch(getAgentById(agentId));
        }
    }, [dispatch, quotationData]);

    const customer = useSelector(state => state.customerReducer.customerDetails);
    const companyData = useSelector(state => state.insuranceCompanyReducer.currentCompany)
    const productData = useSelector(state => state.productMasterReducer.product)
    const agentData = useSelector(state => state.agentReducer.agent);
    let customerInfo = {
        fullName: '',
        mobile: '',
        email: '',
        quotationNumber: '',
        quotationDate: '',
        insuranceCompany: ' ',
        productType: ' ',
        familyType: '',
        duration: '',
        //coPay: '',
    };

    // Ensure quotationData is not empty before accessing it
    if (tableSource === 'pa_quotations') {
        const quotationDetails = quotationData[0];
        customerInfo = {
            fullName: customer?.first_name + ' ' + customer?.last_name || '',
            mobile: customer?.mobile || '',
            email: customer?.email || '',
            quotationNumber: quotationDetails?.quotation_number || '',
            quotationDate: quotationDetails?.quotation_created_at || '',
            insuranceCompany: companyData?.insurance_company_name || '',
            productType: productData?.product_name || '',
            familyType: 'Individual',
            duration: quotationDetails?.duration ? `${quotationDetails.duration} Year` : '',
        };

    } else if (quotationData && quotationData.length > 0 && quotationData[0]?.premium_amount) {
        const quotationDetails = quotationData[0]; // Assuming the data is in the first item

        // Map the data to customerInfo
        customerInfo = {
            fullName: quotationDetails?.member_name || '',
            mobile: customer?.mobile || '',
            email: customer?.email || '',
            quotationNumber: quotationDetails?.quotation_number || '',
            quotationDate: quotationDetails?.quotation_created_at || '',
            insuranceCompany: companyData?.insurance_company_name || '',
            productType: productData?.product_name || '',
            familyType: quotationDetails?.policyType ? (quotationDetails.policyType.endsWith('I') ? 'Individual' : (quotationDetails.policyType.endsWith('F') ? 'Family Floater' : '')) : '',
            duration: quotationDetails?.duration ? `${quotationDetails.duration} Year` : '',
            // coPay: quotationDetails?.coPay
            //     ? quotationDetails.coPay === 'Y'
            //         ? 'YES'
            //         : 'NO'
            //     : '',
        };
    }


    const validateSuratAddress = async (customerId, productName, sumInsured, dispatch) => {
        try {
            // Get customer's address
            const response = await dispatch(getCustomerAddressByCustomerId(customerId));
            const customerAddress = response?.payload;

            // Determine which city to check based on used_address
            const cityToCheck = customerAddress?.used_address === 'current'
                ? parseInt(customerAddress?.current_city)
                : parseInt(customerAddress?.permanent_city);

            if (!cityToCheck) {
                return {
                    isValid: true,
                    message: null
                };
            }

            // Get city details
            const cityResponse = await dispatch(getLocationById(cityToCheck));
            const cityName = cityResponse?.payload?.city?.toUpperCase();

            // Check conditions based on city and product
            if (cityName === 'SURAT') {
                return {
                    isValid: !(parseFloat(sumInsured) < 1000000),
                    message: "Sum insured below 10L not available for Surat customers",
                    cityName
                };
            } else if (cityName === 'VADODARA' && (productName === 'FG HEALTH SURAKSHA' || productName === 'FG VARISHTHA BIMA')) {
                return {

                    isValid: !(parseFloat(sumInsured) < 1000000),
                    message: "Sum insured below 10L not available for Vadodara customers with FG HEALTH SURAKSHA",
                    cityName
                };
            }

            return {
                isValid: true,
                message: null,
                cityName
            };
        } catch (error) {
            console.error('Error in validateSuratAddress:', error);
            return {
                isValid: true,
                message: null
            };
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/quotations-list');
    };

    useEffect(() => {
        const groupQuotationData = async () => {
            const grouped = {};

            for (const quotation of quotationData) {
                const sumInsured = quotation.soap_sum_insured;
                const isHealthProduct = productData?.product_name?.includes('FG HEALTH TOTAL') ||
                    productData?.product_name?.includes('FG HEALTH ABSOLUTE');
                const isSuraksha = productData?.product_name === 'FG HEALTH SURAKSHA';
                const isVarishtaBima = productData?.product_name === 'FG VARISHTHA BIMA'; // Check for VARISHTHA BIMA

                const isFloater = quotation.policyType?.endsWith('F');

                if ((isHealthProduct && isFloater) || isSuraksha || (isVarishtaBima && isFloater)) { // Add condition for VARISHTHA BIMA
                    const validation = await validateSuratAddress(
                        quotationData[0]?.customer_id,
                        productData?.product_name,
                        sumInsured,
                        dispatch
                    );

                    // Skip if validation conditions are not met
                    if (!validation.isValid) {
                        continue;
                    }
                }

                if (!grouped[sumInsured]) {
                    grouped[sumInsured] = [];
                }
                grouped[sumInsured].push(quotation);
            }

            setGroupedData(grouped);
        };

        if (quotationData?.length > 0) {
            groupQuotationData();
        }
    }, [quotationData, productData, dispatch]);

    const totalPremiumAmount = quotationData[0]?.premium_amount || 0;
    const totalPremiumWithServiceTax = quotationData[0]?.premium_with_service_tax || 0;
    const serviceTax = quotationData[0]?.service_tax || 0;
    const CoverType = quotationData[0]?.soap_cover_type || 0;
    const CopayPercentage = quotationData[0]?.copay_percentage || 0;
    const copayAmount = quotationData[0]?.co_pay_amount || 0;
    const results = quotationData.map(item => ({
        totalPremiumAmount: item?.premium_amount || 0,
        totalPremiumWithServiceTax: item?.premium_with_service_tax || 0,
        serviceTax: item?.service_tax || 0,
        coverType: item?.cover_type || 0
    }));

    const handleProceedPolicy = (sumInsured = null, yearPlanData = null) => {

        if (customerInfo.productType === 'FG ACCIDENT SURAKSHA' && customer?.occupation === 193) {
            // Show toaster message
            toast.error("This policy cannot be issued online. Contact 1800220233 or nearest branch");
            return; // Prevent proceeding to policy
        }
        let dataToPass;
        if (tableSource === 'pa_quotations') {
            dataToPass = {
                quotationsData: quotationData,
                customerInfo: customerInfo,
                yearPlanData: yearPlanData, // Add the selected year plan data
                totalPremium: {
                    premiumAmount: yearPlanData.full_payment_premium,
                    serviceTax: yearPlanData.full_payment_tax,
                    totalPremiumWithServiceTax: yearPlanData.total_full_payment
                }
            };
        }
        else if (customerInfo.familyType === 'Individual') {
            dataToPass = {
                quotationsData: quotationData,
                customerInfo: customerInfo,
                totalPremium: {
                    premiumAmount: totalPremiumAmount,
                    serviceTax: serviceTax,
                    totalPremiumWithServiceTax: totalPremiumWithServiceTax
                }
            };
        } else {
            // Filter quotations for the selected sum insured
            const selectedQuotations = quotationData.filter(
                quotation => quotation.soap_sum_insured === sumInsured
            );

            // Calculate totals for the selected group only
            const groupTotal = {
                premiumAmount: selectedQuotations.reduce((acc, item) => parseFloat(item.premium_amount || 0), 0),
                serviceTax: selectedQuotations.reduce((acc, item) => parseFloat(item.service_tax || 0), 0),
                totalPremiumWithServiceTax: selectedQuotations.reduce((acc, item) => parseFloat(item.premium_with_service_tax || 0), 0)
            };

            dataToPass = {
                quotationsData: selectedQuotations,
                customerInfo: customerInfo,
                totalPremium: groupTotal  // Use the calculated totals for the selected group
            };
        }

        // Log the data being passed
        dispatch(setSelectedQuotation(dataToPass));
        navigate(`/dashboard/create-proposal/${0}`);
    };

    const exportCustomerInfo = {
        fullName: customerInfo.fullName || '',
        familyType: customerInfo.familyType || '',
        productType: customerInfo.productType || '',
        insuranceCompany: customerInfo.insuranceCompany || '',
        agentName: agentData?.full_name || '',
        agentMobile: agentData?.official_mobile || '',
        agentEmail: agentData?.official_email || '',
        agentRole: agentData?.role_name || '',
        duration: customerInfo.duration || ''
    };

    return (
        <Container maxWidth="xl" sx={{ pb: 4 }}>
            <BarLoader loading={loading} />
            {loading ? null : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>

                    {/* Header Section */}
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                            />
                            <ModuleName moduleName='Quotation' pageName="Overview" />
                        </Box>
                        <Box sx={{ display: 'flex', gap: 2 }}>
                            <ExportToPdfQuotation
                                data={quotationData || []}
                                customerInfo={exportCustomerInfo}
                                fileName="QuotationOverview.pdf"
                            />
                            <Button
                                variant="outlined"
                                sx={{
                                    borderColor: 'red',
                                    color: 'red'
                                }}
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </Box>
                    </Box>

                    {/* Customer Information Grid */}
                    <Box display="flex" alignItems="center" p={2} sx={{ borderBlock: '1px solid #e0e0e0' }}>
                        <Grid container spacing={2}>
                            {Object.entries(customerInfo).map(([key, value]) => (
                                <Grid item xs={12} md={4} key={key}>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                        <Typography variant="subtitle2" color="textSecondary" sx={{ minWidth: '140px' }}>
                                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:
                                        </Typography>
                                        <Typography variant="body1" fontWeight="medium">
                                            {value}
                                        </Typography>
                                    </Box>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>

                    {customerInfo.familyType === 'Individual' && tableSource !== 'pa_quotations' && (

                        <>
                            {!fetchedProposal && lostQuotations.length === 0 && (
                                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                                    <Button
                                        variant="outlined"
                                        onClick={() => handleProceedPolicy()}

                                        sx={{
                                            minWidth: 200,
                                            borderColor: 'green',
                                            color: 'green',
                                            mr: '8px'
                                        }}
                                    >
                                        Proceed Policy
                                    </Button>
                                </Box>
                            )}
                            <Box sx={{ mt: 4, p: 3, border: '1px solid #e0e0e0', borderRadius: '8px', backgroundColor: '#f9f9f9', width: '80%', margin: '0 auto', height: '100%', }}>
                                <Typography variant="h6" align="left">Total Family Premium:</Typography>
                                <Grid container spacing={2} alignItems="center">
                                    <Grid item xs={12} md={3}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Cover Type:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                {CoverType}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Premium:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                ₹{totalPremiumAmount.toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Service Tax:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                ₹{serviceTax.toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Total Premium with Service Tax:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                ₹{totalPremiumWithServiceTax.toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    {quotationData[0].coPay === 'Y' && (
                                        <Grid item xs={12} md={3}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Typography variant="subtitle2" color="textSecondary">
                                                    coPay Amount:
                                                </Typography>
                                                <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                    ₹{copayAmount.toLocaleString()}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    )}
                                    {quotationData[0].coPay === 'Y' && (
                                        <Grid item xs={12} md={3}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Typography variant="subtitle2" color="textSecondary">
                                                    CopayPercentage:
                                                </Typography>
                                                <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                    ₹{CopayPercentage.toLocaleString()}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    )}
                                </Grid>
                            </Box>




                            <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                <Box sx={{ width: '70%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                                    {quotationData.map((quotation, index) => (
                                        <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 4 }}>

                                            <Box
                                                sx={{
                                                    width: '100%',
                                                    height: '100%',
                                                    padding: '20px',
                                                    borderRadius: '10px 10px 20px 20px',
                                                    border: '1px solid #e0e0e0',
                                                    position: 'relative'
                                                }}
                                            >
                                                <Grid container spacing={2} alignItems="center">

                                                    <Grid item xs={12} md={4}>
                                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                Relation:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold">
                                                                {quotation.soap_relation}
                                                            </Typography>
                                                        </Box>
                                                    </Grid>
                                                    {/* Sum Insured */}
                                                    <Grid item xs={12} md={4}>
                                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                Sum Insured:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold">
                                                                ₹{parseInt(quotation.soap_sum_insured).toLocaleString()}
                                                            </Typography>
                                                        </Box>
                                                    </Grid>

                                                    {/* Premium Amount */}
                                                    <Grid item xs={12} md={4}>
                                                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                                            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                                                                <Typography variant="subtitle2" color="textSecondary">
                                                                    Premium Amount:
                                                                </Typography>
                                                                <Typography
                                                                    variant="body1"
                                                                    fontWeight="bold"
                                                                    sx={{
                                                                        color: 'white',
                                                                        bgcolor: 'green',
                                                                        px: 2,
                                                                        py: 0.5,
                                                                        borderRadius: 1
                                                                    }}
                                                                >
                                                                    ₹{parseFloat(quotation.per_person_premium).toLocaleString()}
                                                                </Typography>
                                                            </Box>
                                                            {quotation.premiumBreakdown && (
                                                                <Typography
                                                                    variant="caption"
                                                                    color="textSecondary"
                                                                    sx={{ mt: 1 }}
                                                                >
                                                                    (₹{parseInt(quotation.premiumBreakdown.basePremium).toLocaleString()} premium +
                                                                    ₹{parseInt(quotation.premiumBreakdown.serviceTax).toLocaleString()} service tax)
                                                                </Typography>
                                                            )}
                                                        </Box>
                                                    </Grid>
                                                </Grid>
                                            </Box>
                                        </Box>
                                    ))}
                                </Box>
                            </Box>
                        </>
                    )}
                    <>
                        {customerInfo.familyType !== 'Individual' && tableSource !== 'pa_quotations' && (
                            <>
                                {Object.entries(groupedData).map(([sumInsured, group], index) => {
                                    // Calculate totals for this group
                                    const totalPremiumAmount = group.reduce(
                                        (acc, item) => parseFloat(item.premium_amount),
                                        0
                                    );
                                    const totalServiceTax = group.reduce(
                                        (acc, item) => parseFloat(item.service_tax),
                                        0
                                    );
                                    const totalPremiumWithServiceTax = group.reduce(
                                        (acc, item) => parseFloat(item.premium_with_service_tax),
                                        0
                                    );
                                    const CoPayAmount = group.reduce(
                                        (acc, item) => parseFloat(item.co_pay_amount),
                                        0
                                    );
                                    const CopayPercentage = group.reduce(
                                        (acc, item) => parseFloat(item.copay_percentage),
                                        0
                                    );

                                    return (
                                        <Box
                                            key={index}
                                            sx={{
                                                mt: 4,
                                                p: 3,
                                                border: "1px solid #e0e0e0",
                                                borderRadius: "8px",
                                                backgroundColor: "#f9f9f9",
                                                width: "80%",
                                                margin: "0 auto",
                                            }}
                                        >
                                            {!fetchedProposal && lostQuotations.length === 0 && (
                                                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', paddingBottom: '20px', }}>
                                                    <Button
                                                        variant="outlined"
                                                        onClick={() => handleProceedPolicy(sumInsured)}

                                                        sx={{
                                                            minWidth: 200,
                                                            borderColor: 'green',
                                                            color: 'green',
                                                            mr: '8px',

                                                        }}
                                                    >
                                                        Proceed Policy
                                                    </Button>
                                                </Box>
                                            )}
                                            <Typography variant="h6" align="left">
                                                Total Family Premium for Sum Insured: ₹{parseInt(sumInsured).toLocaleString()}
                                            </Typography>
                                            <Grid container spacing={3} alignItems="center">
                                                <Grid item xs={12} md={3}>
                                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                        <Typography variant="subtitle2" color="textSecondary">
                                                            Cover Type:
                                                        </Typography>
                                                        <Typography variant="body1" fontWeight="bold" sx={{ color: "green" }}>
                                                            {CoverType}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12} md={3}>
                                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                        <Typography variant="subtitle2" color="textSecondary">
                                                            Premium:
                                                        </Typography>
                                                        <Typography variant="body1" fontWeight="bold" sx={{ color: "green" }}>
                                                            ₹{totalPremiumAmount.toLocaleString()}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12} md={3}>
                                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                        <Typography variant="subtitle2" color="textSecondary">
                                                            Service Tax:
                                                        </Typography>
                                                        <Typography variant="body1" fontWeight="bold" sx={{ color: "green" }}>
                                                            ₹{totalServiceTax.toLocaleString()}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12} md={3}>
                                                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                        <Typography variant="subtitle2" color="textSecondary">
                                                            Total Premium with Service Tax:
                                                        </Typography>
                                                        <Typography variant="body1" fontWeight="bold" sx={{ color: "green" }}>
                                                            ₹{totalPremiumWithServiceTax.toLocaleString()}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                {quotationData[0].coPay === 'Y' && (
                                                    <Grid item xs={12} md={3}>
                                                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                CoPayAmount:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold" sx={{ color: "green" }}>
                                                                ₹{CoPayAmount.toLocaleString()}
                                                            </Typography>
                                                        </Box>
                                                    </Grid>
                                                )}
                                                {quotationData[0].coPay === 'Y' && (
                                                    <Grid item xs={12} md={3}>
                                                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                CopayPercentage:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold" sx={{ color: "green" }}>
                                                                ₹{CopayPercentage.toLocaleString()}
                                                            </Typography>
                                                        </Box>
                                                    </Grid>
                                                )}
                                            </Grid>

                                            <Box sx={{ mt: 3 }}>
                                                {group.map((quotation, idx) => (
                                                    <Box
                                                        key={idx}
                                                        sx={{
                                                            display: "flex",
                                                            flexDirection: "column",
                                                            border: "1px solid #e0e0e0",
                                                            borderRadius: "8px",
                                                            padding: "16px",
                                                            backgroundColor: "white",
                                                            gap: 2,
                                                            marginBottom: 2,
                                                        }}
                                                    >
                                                        <Grid container spacing={3} alignItems="center">
                                                            <Grid item xs={12} md={4}>
                                                                <Box sx={{ display: "flex", gap: 1 }}>
                                                                    <Typography variant="subtitle2" color="textSecondary">
                                                                        Relation:
                                                                    </Typography>
                                                                    <Typography variant="body1" fontWeight="bold">
                                                                        {quotation.soap_relation}
                                                                    </Typography>
                                                                </Box>
                                                            </Grid>
                                                            <Grid item xs={12} md={4}>
                                                                <Box sx={{ display: "flex", gap: 1 }}>
                                                                    <Typography variant="subtitle2" color="textSecondary">
                                                                        Member Name:
                                                                    </Typography>
                                                                    <Typography variant="body1" fontWeight="bold">
                                                                        {quotation.member_name}
                                                                    </Typography>
                                                                </Box>
                                                            </Grid>
                                                            <Grid item xs={12} md={4}>
                                                                <Box sx={{ display: "flex", gap: 1 }}>
                                                                    <Typography variant="subtitle2" color="textSecondary">
                                                                        Premium Amount:
                                                                    </Typography>
                                                                    <Typography
                                                                        variant="body1"
                                                                        fontWeight="bold"
                                                                        sx={{
                                                                            color: "white",
                                                                            bgcolor: "green",
                                                                            px: 3,
                                                                            py: 1,
                                                                            borderRadius: 1,
                                                                        }}
                                                                    >
                                                                        ₹{parseFloat(quotation.per_person_premium).toLocaleString()}
                                                                    </Typography>
                                                                </Box>
                                                            </Grid>
                                                        </Grid>
                                                    </Box>
                                                ))}
                                            </Box>


                                        </Box>
                                    );
                                })}
                            </>

                        )}
                    </>
                    <>
                        {tableSource === "pa_quotations" && (
                            <Box sx={{ mt: 4 }}>
                                <Typography variant="h6" gutterBottom sx={{ mb: 3, textAlign: 'center' }}>Payment Options</Typography>
                                <Grid container spacing={3}>
                                    {quotationData?.[0]?.responses?.map((response, index) => (
                                        <Grid item xs={12} md={4} key={index}>
                                            <Box
                                                sx={{
                                                    p: 3,
                                                    border: '1px solid #e0e0e0',
                                                    borderRadius: 2,
                                                    backgroundColor: '#fff',
                                                    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                                                    height: '100%'
                                                }}
                                            >
                                                <Typography
                                                    variant="h6"
                                                    gutterBottom
                                                    sx={{
                                                        color: '#528a7e',
                                                        borderBottom: '2px solid #528a7e',
                                                        pb: 1,
                                                        mb: 2
                                                    }}
                                                >
                                                    {response.duration} Year Plan
                                                </Typography>

                                                {/* Full Payment Details */}
                                                <Box sx={{ mb: 3 }}>
                                                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                                                        Premium Breakdown
                                                    </Typography>
                                                    <Grid container spacing={1}>
                                                        <Grid item xs={12}>
                                                            <Box sx={{
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                py: 0.5
                                                            }}>
                                                                <Typography color="text.secondary">Premium:</Typography>
                                                                <Typography>₹{response.full_payment_premium}</Typography>
                                                            </Box>
                                                        </Grid>
                                                        <Grid item xs={12}>
                                                            <Box sx={{
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                py: 0.5
                                                            }}>
                                                                <Typography color="text.secondary">Service Tax:</Typography>
                                                                <Typography>₹{response.full_payment_tax}</Typography>
                                                            </Box>
                                                        </Grid>
                                                    </Grid>
                                                </Box>

                                                {/* Discounts Section */}
                                                <Box sx={{ mb: 3 }}>
                                                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                                                        Applied Discounts
                                                    </Typography>

                                                    {/* Family Discount */}
                                                    {response.family_discount_amt > 0 && (
                                                        <Box sx={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            py: 0.5
                                                        }}>
                                                            <Typography color="text.secondary">
                                                                Family Discount:
                                                            </Typography>
                                                            <Typography color="red">
                                                                (-₹ {response.family_discount_amt})
                                                            </Typography>
                                                        </Box>
                                                    )}

                                                    {/* Long Term Discount */}
                                                    {response.long_term_discount_amount > 0 && (
                                                        <Box sx={{
                                                            display: 'flex',
                                                            justifyContent: 'space-between',
                                                            py: 0.5
                                                        }}>
                                                            <Typography color="text.secondary">
                                                                Long Term Discount:
                                                            </Typography>
                                                            <Typography color="red">
                                                                (-₹ {response.long_term_discount_amount})
                                                            </Typography>
                                                        </Box>
                                                    )}
                                                </Box>

                                                {/* Total Amount */}
                                                <Box sx={{
                                                    mt: 2,
                                                    pt: 2,
                                                    borderTop: '2px solid #e0e0e0'
                                                }}>
                                                    <Box sx={{
                                                        display: 'flex',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'center'
                                                    }}>
                                                        <Typography variant="subtitle1" fontWeight="bold">
                                                            Final Amount:
                                                        </Typography>
                                                        <Typography
                                                            variant="h6"
                                                            color="primary.main"
                                                            fontWeight="bold"
                                                        >
                                                            ₹{response.total_full_payment}
                                                        </Typography>
                                                    </Box>
                                                </Box>

                                                {/* Proceed to Policy Button */}
                                                {!fetchedProposal && lostQuotations.length === 0 && (
                                                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                                                        <Button
                                                            variant="outlined"
                                                            onClick={() => handleProceedPolicy(null, response)}
                                                            sx={{
                                                                minWidth: 200,
                                                                borderColor: 'green',
                                                                color: 'green',
                                                                '&:hover': {
                                                                    borderColor: 'darkgreen',
                                                                    backgroundColor: 'rgba(0, 128, 0, 0.04)'
                                                                }
                                                            }}
                                                        >
                                                            Proceed to Policy
                                                        </Button>
                                                    </Box>
                                                )}
                                            </Box>
                                        </Grid>
                                    ))}
                                </Grid>
                            </Box>

                        )}
                    </>


                </Box>
            )}
        </Container>
    );
}

export default QuotationOverview;