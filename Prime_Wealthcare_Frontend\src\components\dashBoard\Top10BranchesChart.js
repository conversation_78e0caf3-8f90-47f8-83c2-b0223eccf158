import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
} from 'chart.js';
import { Typography, Paper } from '@mui/material';
import { currentFinancialYear, formatIndianValue } from '../../utils/Reusable';

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);

// Update the valueLabelsPlugin
const valueLabelsPlugin = {
    id: 'valueLabels',
    afterDraw: (chart) => {
        // Set lower z-index by drawing before tooltip
        if (chart.tooltip._active && chart.tooltip._active.length) {
            return;
        }

        const { ctx } = chart;
        ctx.save();

        // Add total values at the end of each bar
        chart.data.datasets.forEach((dataset, datasetIndex) => {
            const meta = chart.getDatasetMeta(datasetIndex);
            if (meta.hidden) return;

            meta.data.forEach((bar, index) => {
                const value = dataset.data[index];
                if (value > 0) {
                    const formattedAmount = formatIndianValue(value);

                    let totalWidth = 0;
                    chart.data.datasets.forEach((ds, i) => {
                        const currentMeta = chart.getDatasetMeta(i);
                        if (i <= datasetIndex && !currentMeta.hidden) {
                            totalWidth += ds.data[index] || 0;
                        }
                    });

                    const barWidth = chart.scales.x.getPixelForValue(value) - chart.scales.x.getPixelForValue(0);
                    if (barWidth > 40) {
                        ctx.font = 'bold 11px Arial';
                        ctx.fillStyle = `rgba(0, 0, 0, 0.8)`;
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';

                        const startX = chart.scales.x.getPixelForValue(totalWidth - value);
                        const endX = chart.scales.x.getPixelForValue(totalWidth);
                        const centerX = startX + ((endX - startX) / 2);

                        ctx.fillText(formattedAmount, centerX, bar.y);
                    }
                }
            });
        });

        // Add total values at the end of each bar
        chart.data.labels.forEach((_, index) => {
            let total = 0;
            chart.data.datasets.forEach((dataset, i) => {
                const meta = chart.getDatasetMeta(i);
                if (!meta.hidden) {
                    total += dataset.data[index] || 0;
                }
            });

            if (total > 0) {
                const formattedTotal = formatIndianValue(total);
                const meta = chart.getDatasetMeta(0);
                const y = meta.data[index].y;
                const x = chart.scales.x.getPixelForValue(total);

                ctx.font = 'bold 12px Arial';
                ctx.fillStyle = `rgba(0, 0, 0, 1)`;
                ctx.textAlign = 'left';
                ctx.textBaseline = 'middle';
                ctx.fillText(`${formattedTotal}`, x + 8, y);
            }
        });

        ctx.restore();
    }
};

const Top10BranchesChart = ({ branchData, height }) => {
    if (!branchData) {
        return <div>Loading...</div>;
    }

    const colors = {
        NEW: {
            background: 'rgba(54, 162, 235, 0.8)',
            border: 'rgb(54, 162, 235)'
        },
        RENEWAL: {
            background: 'rgba(75, 192, 192, 0.8)',
            border: 'rgb(75, 192, 192)'
        },
        ROLLOVER: {
            background: 'rgba(255, 159, 64, 0.8)',
            border: 'rgb(255, 159, 64)'
        },
        MIGRATION: {
            background: 'rgba(153, 102, 255, 0.8)',
            border: 'rgb(153, 102, 255)'
        }
    };

    const data = {
        labels: branchData.map(item => item.branch_name),
        datasets: Object.keys(colors).map(type => ({
            label: type,
            data: branchData.map((item, index) => item.proposal_types[type].premium),
            backgroundColor: branchData.map((_, index) => {
                const opacity = 1 - (index * 0.07); // Creates fade effect from top to bottom
                const color = colors[type].background.replace('0.8', opacity);
                return color;
            }),
            borderColor: colors[type].border,
            borderWidth: 1,
            borderRadius: 5,
        }))
    };

    // Update the options object with tooltip z-index
    const options = {
        indexAxis: 'y',
        responsive: true,
        maintainAspectRatio: false,
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        },
        scales: {
            x: {
                stacked: true,
                beginAtZero: true,
                max: Math.max(...branchData.map(item => item.total_premium)) * 1.2,
                grid: {
                    display: true,
                    color: 'rgba(0, 0, 0, 0.05)'
                },
                ticks: {
                    callback: function (value) {
                        return formatIndianValue(value);
                    },
                    font: {
                        size: 11
                    }
                }
            },
            y: {
                stacked: true,
                grid: {
                    display: false
                },
                ticks: {
                    font: {
                        size: 11,
                        weight: 'bold'
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: true,
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            },
            title: { display: false },
            tooltip: {
                backgroundColor: 'white',
                titleColor: '#333',
                bodyColor: '#666',
                borderColor: '#ddd',
                borderWidth: 1,
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                padding: 12,
                usePointStyle: true,
                callbacks: {
                    label: function (context) {
                        const type = context.dataset.label;
                        const item = branchData[context.dataIndex].proposal_types[type];
                        return [
                            `${type}: ${formatIndianValue(item.premium)}`,
                            `Count: ${item.count}`
                        ];
                    }
                }
            }
        }
    };

    return (
        <Paper
            elevation={3}
            sx={{
                p: 2,
                borderRadius: 2,
                height: height,
                backgroundColor: '#ffffff',
                '&:hover': {
                    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
                }
            }}
        >
            <Typography
                variant="h6"
                sx={{
                    mb: 2,
                    fontWeight: 'bold',
                    color: '#333333',
                    textAlign: 'center',
                }}
            >
                Top 10 Branches by Premium (FY {currentFinancialYear()})
            </Typography>
            {!branchData?.length ? (
                <Typography>No Data Present</Typography>
            ) : (
                <div style={{ height: 'calc(100% - 48px)', minHeight: '300px' }}>
                    <Bar data={data} options={options} plugins={[valueLabelsPlugin]} />
                </div>
            )}
        </Paper>
    );
};

export default Top10BranchesChart;
