import React, { useEffect, useState } from 'react';
import { Box, Card, Typography, Grid, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { formatIndianValue } from '../../utils/Reusable';

const ValueTypography = styled(Typography)(({ ispositive }) => ({
    color: (ispositive === 'true') ? '#4caf50' : '#f44336',
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
}));

const SingleInformationCard = ({ currentData, contestedData, title, reverseCompare = false }) => {
    const totalCurrent = currentData.reduce((sum, item) => sum + item.value, 0);
    const totalContested = contestedData.reduce((sum, item) => sum + item.value, 0);

    // State for animated totals and individual values
    const [animatedTotal, setAnimatedTotal] = useState(0);
    const [animatedValues, setAnimatedValues] = useState(currentData.map(() => 0));

    // Animate Total
    useEffect(() => {
        let start;
        const duration = 1000;

        const animate = (timestamp) => {
            if (!start) start = timestamp;
            const progress = Math.min((timestamp - start) / duration, 1);
            setAnimatedTotal(Math.floor(progress * totalCurrent));
            if (progress < 1) requestAnimationFrame(animate);
        };

        requestAnimationFrame(animate);
    }, [totalCurrent]);

    // Animate Each Value
    useEffect(() => {
        let start;
        const duration = 1000;

        const animate = (timestamp) => {
            if (!start) start = timestamp;
            const progress = Math.min((timestamp - start) / duration, 1);

            const updated = currentData.map(item =>
                Math.floor(progress * item.value)
            );
            setAnimatedValues(updated);

            if (progress < 1) requestAnimationFrame(animate);
        };

        requestAnimationFrame(animate);
    }, [currentData]);

    const isValuePositive = (current, contested) => !reverseCompare
        ? (current >= contested)
        : (current <= contested);

    const getArrowIcon = (current, contested) => {
        if (current === contested) return null;
        const isHigher = current > contested;

        return reverseCompare
            ? (isHigher ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />)
            : (isHigher ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />);
    };

    return (
        <Paper
            elevation={3}
            sx={{
                p: 2,
                borderRadius: 2,
                width: '100%',
                height: '100%',
                backgroundColor: '#ffffff',
                '&:hover': {
                    boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
                }
            }}
        >
            <Grid container spacing={2}>
                {/* Top Row */}
                <Grid item xs={12}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <Typography variant="h6" fontWeight="bold">
                            {title}
                        </Typography>
                        <ValueTypography
                            variant="h6"
                            fontWeight="bold"
                            ispositive={isValuePositive(totalCurrent, totalContested).toString()}
                        >
                            {getArrowIcon(totalCurrent, totalContested)}
                            {formatIndianValue(animatedTotal)}
                        </ValueTypography>
                    </Box>
                </Grid>

                {/* Bottom Row */}
                <Grid item xs={12}>
                    <Grid container spacing={2}>
                        {currentData.map((item, index) => (
                            <Grid item xs={6} key={index}>
                                <Box>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        {item.title}
                                    </Typography>
                                    <ValueTypography
                                        variant="h6"
                                        ispositive={isValuePositive(item.value, contestedData[index].value).toString()}
                                    >
                                        {getArrowIcon(item.value, contestedData[index].value)}
                                        {formatIndianValue(animatedValues[index])}
                                    </ValueTypography>
                                </Box>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            </Grid>
        </Paper>
    );
};

export default SingleInformationCard;
