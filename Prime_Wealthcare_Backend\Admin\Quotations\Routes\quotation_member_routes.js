const express = require('express');
const router = express.Router();
const QuotationMemberController = require('../Controllers/quotation_member_controller');

// Route to fetch all members for a specific quotation
router.get('/:quotationId', QuotationMemberController.getAllMembersByQuotationId);

// Route to fetch a single member by ID
router.get('/:id', QuotationMemberController.getMemberById);


// Route to create a new member
router.post('/', QuotationMemberController.createMember);

// Route to update a member
router.put('/:id', QuotationMemberController.updateMember);

// Route to delete a member
router.delete('/:id', QuotationMemberController.deleteMember);

module.exports = router;
