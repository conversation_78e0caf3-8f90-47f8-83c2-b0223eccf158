const knexConfig = require('../../../../knexfile');
const knex = require('knex')(knexConfig.development);

// Get all IMF branches with their related locations and sub-areas
const getAllIMFBranches = async () => {
  return knex('imf_branches')
    .select(
      'imf_branches.*',
      knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
      'locations.state',
      'areas.area', // Getting the sub-area name
      knex.raw("DATE_FORMAT(imf_branches.created_at, '%d/%m/%Y') as created_at")
    )
    .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
    .leftJoin('areas', 'imf_branches.area', 'areas.id')
    .groupBy('imf_branches.id', 'locations.state', 'areas.area');
};

// Get a single IMF branch by ID
const getIMFBranchById = async (id) => {
  return knex('imf_branches')
    .select(
      'imf_branches.*',
      'areas.id', // Getting the sub-area name
      knex.raw("DATE_FORMAT(imf_branches.created_at, '%d/%m/%Y') as created_at")
    )
    .leftJoin('areas', 'imf_branches.area', 'areas.id')
    .where('imf_branches.id', id)
    .groupBy('imf_branches.id', 'areas.area')
    .first();
};

// Create a new IMF branch
const createIMFBranch = async (data) => {
  try {
    return await knex('imf_branches').insert(data);
  } catch (error) {
    console.error('Error creating IMF Branch: ', error);
    throw error;
  }
};

// Update an existing IMF branch
const updateIMFBranch = async (id, data) => {
  return knex('imf_branches')
    .where('id', id)
    .update(data);
};

// Soft delete an IMF branch (mark status as false)
const softDeleteIMFBranch = async (id) => {
  return knex('imf_branches')
    .where('id', id)
    .update({ status: false });
};

// Reinstate a soft-deleted IMF branch
const reinstateIMFBranch = async (id) => {
  return knex('imf_branches')
    .where('id', id)
    .update({ status: true });
};
// Search IMF branches by name
const getIMFBranchesByName = async (name) => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area_id', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .where(function () {
        this.where('imf_branches.branch_name', 'LIKE', `%${name}%`)
        .orWhere('imf_branches.branch_code', 'LIKE', `%${name}%`)
          .orWhere('locations.city', 'LIKE', `%${name}%`)
          .orWhere('locations.state', 'LIKE', `%${name}%`)
          .orWhere('areas.area', 'LIKE', `%${name}%`);
      });
    return branches;
  } catch (error) {
    console.error(`Error fetching IMF branches by name: ${name}`, error);
    throw error;
  }
};

// Fetch IMF branches created last week
const newLastWeek = async () => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .whereBetween('imf_branches.created_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching IMF branches created last week:', error);
    throw error;
  }
};

// Fetch new IMF branches created this week
const newThisWeek = async () => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .whereBetween('imf_branches.created_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
        knex.raw('NOW()')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching IMF branches created this week:', error);
    throw error;
  }
};

// Fetch deactivated IMF branches updated this week
const deactivatedThisWeek = async () => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .where('imf_branches.status', 0) // Assuming 0 means deactivated
      .whereBetween('imf_branches.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
        knex.raw('NOW()')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching deactivated IMF branches updated this week:', error);
    throw error;
  }
};

// Fetch deactivated IMF branches updated last week
const deactivatedLastWeek = async () => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .where('imf_branches.status', 0) // Assuming 0 means deactivated
      .whereBetween('imf_branches.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching deactivated IMF branches updated last week:', error);
    throw error;
  }
};

// Fetch IMF branches edited this week
const editedThisWeek = async () => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .whereBetween('imf_branches.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
        knex.raw('NOW()')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching edited IMF branches updated this week:', error);
    throw error;
  }
};

// Fetch IMF branches edited last week
const editedLastWeek = async () => {
  try {
    const branches = await knex('imf_branches')
      .leftJoin('locations', 'imf_branches.pincode', 'locations.pincode')
      .leftJoin('areas', 'imf_branches.area', 'areas.id')
      .select(
        'imf_branches.*',
        'locations.city',
        'locations.state',
        'areas.area'
      )
      .whereBetween('imf_branches.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ]);

    return branches;
  } catch (error) {
    console.error('Error fetching edited IMF branches updated last week:', error);
    throw error;
  }
};

module.exports = {
  getAllIMFBranches,
  getIMFBranchById,
  createIMFBranch,
  updateIMFBranch,
  softDeleteIMFBranch,
  reinstateIMFBranch,
  getIMFBranchesByName,
  newLastWeek,
  newThisWeek,
  deactivatedThisWeek,
  deactivatedLastWeek,
  editedThisWeek,
  editedLastWeek

};
