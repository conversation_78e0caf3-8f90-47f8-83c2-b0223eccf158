const CustomerMemberMaster = require('../Models/customer_member_info_model');

// Create new Customer
exports.createCustomerMember = async (req, res, next) => {
    try {
        const customerMemberData = req.body;
        await CustomerMemberMaster.create(customerMemberData);
        res.status(200).json({ message: 'Customer Member created successfully' });
    } catch (error) {
        next(error);
    }
};

// Get all Customer
exports.getCustomerMember = async (req, res, next) => {
    try {
        const data = await CustomerMemberMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get customer by ID
exports.getCostomerMemberById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMemberMaster.findById(id);
        if (customer) {
            res.status(200).json(customer);
        } else {
            res.status(404).json({ message: 'customer not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Get customer by ID
exports.getCostomerMemberByCustomerId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMemberMaster.findByCustomerId(id);
        if (customer) {
            res.status(200).json(customer);
        } else {
            res.status(404).json({ message: 'customer not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Update customer by ID
exports.updateCustomerMember = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customerData = req.body;
        const data = await CustomerMemberMaster.update(id, customerData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
// Delete a role by ID
exports.deleteCustomerMember = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await CustomerMemberMaster.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Member not found' });
        }
        res.status(204).json({ message: 'Member deleted successfully' });
    } catch (error) {
        next(error);
    }
};