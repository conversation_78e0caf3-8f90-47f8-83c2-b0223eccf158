import { createSlice } from '@reduxjs/toolkit';
import {
  getAllSubProducts,
  createSubProduct,
  getSubProductById,
  updateSubProduct,
  deleteSubProduct,
  reinstateSubProduct,
  searchSubProduct,
  getFilterSubProducts,
  createSumInsured,
  createRidersDetails,
  deleteSumInsured,
  updateSumInsured,
  updateRiderDetails,
  deleteRiderDetails,
  getSubProductByProductDetails,
} from '../../actions/action'; // Adjust the path if needed
import { toast } from 'react-toastify';

const initialState = {
  subProducts: [],
  subProduct: {},
  sumInsured: [],
  riderDetails: [],
  loading: false,
  error: null,
};

const subProductSlice = createSlice({
  name: 'subProduct',
  initialState,

  reducers: {
    resetIsSubProductAdded: (state) => {
      state.isSubProductAdded = false; // Reset the flag
    },
    setIsSubProductAdded: (state) => {
      state.isSubProductAdded = true; // Set the flag when a sub-product is added
    },
    clearSubProduct: (state) => {
      state.subProducts = [];
      state.subProduct = {};
      state.sumInsured = [];
      state.riderDetails = [];
    },
  },

  extraReducers: (builder) => {
    builder
      // Get all sub-products
      .addCase(getAllSubProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllSubProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts = action.payload; // Replace sub-products array
      })
      .addCase(getAllSubProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || action.error.message;
        toast.error('Failed to load sub-products'); // Error toast
      })

      .addCase(getSubProductByProductDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSubProductByProductDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts = action.payload;
      })
      .addCase(getSubProductByProductDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to load sub-products by product details'); // Error toast
      })

      // Create a new sub-product
      .addCase(createSubProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts.push(action.payload); // Add the new sub-product to the array
        state.isSubProductAdded = true; // Set the flag to true
        toast.success('Successfully created a new sub-product'); // Success toast
      })
      .addCase(createSubProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to create sub-product'); // Error toast
      })

      // Get sub-product by ID
      .addCase(getSubProductById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSubProductById.fulfilled, (state, action) => {
        state.loading = false;
        const data = action.payload;

        if (data) {
          const { sub_product_age_sum, sub_product_rider_details, ...rest } = data;

          state.sumInsured = sub_product_age_sum;
          state.riderDetails = sub_product_rider_details;
          state.subProduct = rest;
        }
      })


      .addCase(getSubProductById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to load sub-product details'); // Error toast
      })

      // Update an existing sub-product
      .addCase(updateSubProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubProduct.fulfilled, (state, action) => {
        state.loading = false;
        toast.success('Sub-product updated successfully'); // Success toast
      })
      .addCase(updateSubProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to update sub-product'); // Error toast
      })

      // Delete sub-product by ID
      .addCase(deleteSubProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSubProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts = state.subProducts.filter(
          (subProduct) => subProduct.id !== action.payload
        );
        toast.success('Sub-product deleted successfully'); // Success toast
      })
      .addCase(deleteSubProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to delete sub-product'); // Error toast
      })

      // Reinstate a deleted sub-product by ID
      .addCase(reinstateSubProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(reinstateSubProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts.push(action.payload); // Add the reinstated sub-product to the array
        toast.success('Sub-product reinstated successfully'); // Success toast
      })
      .addCase(reinstateSubProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to reinstate sub-product'); // Error toast
      })

      // Search sub-product by name
      .addCase(searchSubProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchSubProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts = action.payload; // Replace the sub-products with search results
      })
      .addCase(searchSubProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to find sub-products'); // Error toast
      })

      // Filter sub-products by criteria
      .addCase(getFilterSubProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getFilterSubProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.subProducts = action.payload; // Replace the sub-products with filtered results
      })
      .addCase(getFilterSubProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to apply filter'); // Error toast
      })

      .addCase(createSumInsured.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSumInsured.fulfilled, (state, action) => {
        state.loading = false;
        state.sumInsured.push(action.payload);
        toast.success('Sum insured created successfully'); // Success toast
      })
      .addCase(createSumInsured.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to create sum insured'); // Error toast
      })

      .addCase(updateSumInsured.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSumInsured.fulfilled, (state, action) => {
        state.loading = false;

        const index = state.sumInsured.findIndex(sumInsured => sumInsured.id === action.payload.id);
        if (index !== -1) {
          state.sumInsured[index] = action.payload;
        }
        toast.success('Sum insured updated successfully'); // Success toast
      })
      .addCase(updateSumInsured.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to update sum insured'); // Error toast
      })

      .addCase(deleteSumInsured.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSumInsured.fulfilled, (state, action) => {
        state.loading = false;

        state.sumInsured = state.sumInsured.filter(sumInsured => sumInsured.id !== action.payload);
        toast.success('Sum insured deleted successfully'); // Success toast
      })
      .addCase(deleteSumInsured.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to delete sum insured'); // Error toast
      })


      .addCase(createRidersDetails.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createRidersDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.riderDetails.push(action.payload);
        toast.success('Rider details created successfully'); // Success toast
      })
      .addCase(createRidersDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to create rider details'); // Error toast
      })

      .addCase(updateRiderDetails.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateRiderDetails.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.riderDetails.findIndex(riderDetails => riderDetails.id === action.payload.id);
        if (index !== -1) {
          state.subProduct.sum_insured_rider_details[index] = action.payload;
        }
        toast.success('Rider details updated successfully'); // Success toast
      })
      .addCase(updateRiderDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to update rider details'); // Error toast
      })
      .addCase(deleteRiderDetails.pending, (state, action) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteRiderDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.riderDetails = state.riderDetails.filter(riderDetails => riderDetails.id !== action.payload);
        toast.success('Rider details deleted successfully'); // Success toast
      })
      .addCase(deleteRiderDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        toast.error('Failed to delete rider details'); // Error toast
      })
  },
});


export const { resetIsSubProductAdded, setIsSubProductAdded, clearSubProduct } = subProductSlice.actions;

export default subProductSlice.reducer;
