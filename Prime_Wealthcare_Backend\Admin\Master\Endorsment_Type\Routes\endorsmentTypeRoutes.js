const express = require('express');
const endorsmentTypeController = require('../Controllers/endorsmentTypeController');
const router = express.Router();

// Route to get all Endorsments
router.get('/', endorsmentTypeController.getAllEndorsments);

// Route to get a Endorsment by ID
router.get('/:id', endorsmentTypeController.getEndorsmentById);

// Route to get a Endorsment by name
router.get('/name/:name', endorsmentTypeController.getEndorsmentByName);

// Route to create a new Endorsment
router.post('/', endorsmentTypeController.createEndorsment);

// Route to update a Endorsment by ID
router.put('/:id', endorsmentTypeController.updateEndorsment);

// Route to delete a Endorsment by ID
router.delete('/:id', endorsmentTypeController.deleteEndorsment);

// Route to reinstate a Endorsment by ID
router.put('/reinstate/:id', endorsmentTypeController.reinstateEndorsment);

// Route to get Endorsments by specific criteria (new, deactivated, edited)
router.get('/criteria/:criteria', endorsmentTypeController.getEndorsmentsByCriteria);

module.exports = router;
