exports.seed = async function (knex) {

  // Define the modules and pages
  const sections = [
    {
      title: 'Dashboard',
      items: ['CRM Dashboard', 'HR Dashboard'],
    },
    {
      title: 'Master',
      items: ['Area Master','Area Form', 'Insurance Company','Insurance Company Form', 
          'IMF Branch','IMF Branch Form','Insurance Branch','Insurance Branch Form',
          'Main Product','Main Product Form','Product Master','Product Master Form',
          'Sub Product','Sub Product Form','Network Master','Network Form',
          'Disease Master','Disease Master Form', 'Role Management', 'Role List Form',
          'Endorsement Type','Endorsement Type Form','Page Right Access',]
  },
  {
      title: 'User Management',
      items: [
          'Employee', // New subtitle
          'Employee Personal Information',
          'Employee Overview',
          'Employee Address',
          'Salary Form',
          'Employee Salary Details',
          'Employee Bank Details',
          'Employee Bank Fotm'
      ],
  },
  {
    'title':'Agent',
    'items':[
      'Agent Master',
      'Agent Personal Information',
      'Agent Overview',
      'Agent Address',
      'Agent Bank Details',
      'Agent Bank Form',
      ]
    
  },
  {
    'title':'Customer',
    'items':[
      'Customer Master',
      'Customer Personal Information',
      'Customer Address',
      'Customer Member Information',
      'Customer Grouping',
      'Documents',
      'Quotations',
      'Proposal'
    ]
  },
  {
    'title':'Quotation',
    'items':[
   'Quotation List',
   'Quotation Create',
   'Quotation Overview',
    ]
  },
  {
    'title':'Quick Quotation',
    'items':[
      'Quick Quotation Create',
      'Quick Quotation Overview'
    ]
  },
  {
    'title':'Proposal', 
    'items':[
      'Proposal Create', 
      'Proposal List'           
    ]
  },
  {
    'title': 'Task Management',
    'items': [
        'Task Board',
        'Employee Tasks'   
            ]
},
{
    'title': 'Loan',
    'items': [
        'Employee Loan',
        'Employee Loan Form', // Create/Edit form
        'Employee Loan EMI', // EMI details page
        'Agent Loan', // List page
        'Agent Loan Form', // Create/Edit form
        'Agent Loan EMI', // EMI details page
        ]

}
  ];

  // Generate page rights for all modules and pages
  const generatePageRights = (userId) => {
    const pageRights = [];
    sections.forEach((section) => {
      section.items.forEach((item) => {
        pageRights.push({
          user_id: userId,
          module_name: section.title,
          page_name: item,
          can_add: true,
          can_edit: true,
          can_view: true,
          can_delete: true,
          created_at: knex.fn.now(),
          updated_at: knex.fn.now(),
        });
      });
    });
    return pageRights;
  };

  // Insert page rights for PIYUSH PANDYA (user_id: PWS-ADM001)
  const pageRights1 = generatePageRights('PWS-ADM001');

  // Insert page rights for MEHUL PRADHAN (user_id: PWS-ADM002)
  const pageRights2 = generatePageRights('PWS-ADM002');

  // Delete existing data and insert new data
  return knex('page_rights')
    .del()
    .then(async function () {
      await knex('page_rights').insert(pageRights1);
      await knex('page_rights').insert(pageRights2);
    });
};