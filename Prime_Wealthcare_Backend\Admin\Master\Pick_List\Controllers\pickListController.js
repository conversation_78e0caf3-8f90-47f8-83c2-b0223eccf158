// controllers/pickListController.js
const PickList = require('../Models/pickList');

exports.getAllPickLists = async (req, res) => {
    try {
        const pickLists = await PickList.getAll();
        res.status(200).json(pickLists);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching pick lists', error: error.message });
    }
};

exports.getPickListsByTypeName = async (req, res) => {
    const { typeName } = req.params;
    try {
        const pickLists = await PickList.getByTypeName(typeName);
        res.status(200).json(pickLists);
    } catch (error) {
        res.status(500).json({ message: `Error fetching pick lists for type: ${typeName}`, error: error.message });
    }
};

exports.createPickList = async (req, res) => {
    const pickListData = req.body;
    try {
        const newPickList = await PickList.create(pickListData);
        res.status(201).json(newPickList);
    } catch (error) {
        res.status(500).json({ message: 'Error creating pick list', error: error.message });
    }
};

exports.updatePickList = async (req, res) => {
    const { id } = req.params;
    const pickListData = req.body;
    try {
        const updatedPickList = await PickList.update(id, pickListData);
        res.status(200).json(updatedPickList);
    } catch (error) {
        res.status(500).json({ message: `Error updating pick list with id: ${id}`, error: error.message });
    }
};

exports.deletePickList = async (req, res) => {
    const { id } = req.params;
    try {
        await PickList.delete(id);
        res.status(200).json({ message: 'Pick list item deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: `Error deleting pick list item with id: ${id}`, error: error.message });
    }
};

exports.softDeletePickList = async (req, res) => {
    const { id } = req.params;
    try {
        const softDeletedPickList = await PickList.softDelete(id);
        res.status(200).json(softDeletedPickList);
    } catch (error) {
        res.status(500).json({ message: `Error soft-deleting pick list item with id: ${id}`, error: error.message });
    }
};

exports.reinstatePickList = async (req, res) => {
    const { id } = req.params;
    try {
        const reinstatedPickList = await PickList.reinstate(id);
        res.status(200).json(reinstatedPickList);
    } catch (error) {
        res.status(500).json({ message: `Error reinstating pick list item with id: ${id}`, error: error.message });
    }
};
