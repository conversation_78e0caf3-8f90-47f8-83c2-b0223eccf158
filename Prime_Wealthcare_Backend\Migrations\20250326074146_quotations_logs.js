exports.up = function(knex) {
    return knex.schema.createTable('quotations_logs', table => {
        table.increments('id').primary();
        table.string('quotation_number');
        table.text('request_body');
        table.text('response_body');
        table.string('status').defaultTo('PENDING');
        table.text('error_message');
        table.string('created_by');
        table.string('customer_name');
        table.string('insurance_company');
        table.string('product_name');
        table.timestamp('created_at').defaultTo(knex.fn.now());

        // Add indexes for better query performance
        table.index('quotation_number');
        table.index('status');
        table.index('created_at');
        
    });
};

exports.down = function(knex) {
    return knex.schema.dropTableIfExists('quotations_logs');
};