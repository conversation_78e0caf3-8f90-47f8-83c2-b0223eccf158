const Location = require('../Models/locations');

exports.getAllLocations = async (req, res, next) => {
    try {

        const locations = await Location.getAllLocations();
        res.json(locations);
    } catch (error) {
        //res.status(500).json({ message: "Error fetching locations", error });
        next(error);
    }
};

exports.createLocation = async (req, res, next) => {
    try {
        const data = req.body;

        const newLocation = await Location.insertLocation(data);

        res.status(201).send(newLocation);
    } catch (error) {
        next(error);
    }
};

exports.updateLocation = async (req, res, next) => {
    try {

        const { pincode } = req.params;
        const data = req.body;
        await Location.updateLocation(pincode, data);

        res.send('Location updated');
    } catch (error) {
        next(error);
    }
};

exports.getLocationById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const data = await Location.getLocationById(id);
        res.status(200).send(data);
    } catch (error) {
        next(error);
    }
}

exports.deleteLocation = async (req, res, next) => {
    try {
        const { pincode, city } = req.params;

        await Location.deleteLocation({ pincode, city });
        res.status(200).send('Location deleted');
    } catch (error) {
        next(error);
    }
};

exports.getLocationByPincode = async (req, res, next) => {
    try {
        const { pincode } = req.params;

        const location = await Location.getLocationByPincode(pincode);
        res.status(200).json(location);
    } catch (error) {
        next(error);
    }
};

exports.getLocationByPincodeAndCity = async (req, res, next) => {
    try {
        const { pincode, city } = req.params;

        const location = await Location.getLocationByPincodeAndCity(pincode, city);
        res.status(200).json(location);
    } catch (error) {
        next(error);
    }
};

exports.getLocationAndSubAreaByPincode = async (req, res, next) => {
    try {
        const { pincode } = req.params;
        const result = await Location.getLocationAndSubAreaByPincode(pincode);
        if (result.length === 0) {
            return res.status(404).json({ message: `No data found for pincode '${pincode}'` });
        }
        res.json(result);
    } catch (error) {
        next(error);
    }
};

exports.getLocationBySearch = async (req, res, next) => {
    try {
        const { query } = req.params;
        const result = await Location.getLocationBySearch(query);
        res.status(200).json(result);
    } catch (error) {
        next(error);
    }
}

exports.getLocationByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;

        let data;
        switch (criteria) {
            case 'none':
                data = await Location.getAllLocations();
                break;
            case 'newLastWeek':
                data = await Location.newLastWeek();
                break;
            case 'newThisWeek':
                data = await Location.newThisWeek();
                break;
            case 'editedThisWeek':
                data = await Location.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await Location.editedLastWeek();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }

        res.status(200).json(data);
    } catch (error) {

        next(error);
    }
};