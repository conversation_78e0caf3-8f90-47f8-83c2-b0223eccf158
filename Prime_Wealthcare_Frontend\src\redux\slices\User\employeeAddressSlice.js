import { createSlice } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import {
    getAllEmployeeAddresses,
    getEmployeeAddressById,
    createEmployeeAddress,
    updatemployeeAddress,
    deleteEmployeeAddress,
    getEmployeeAddressByEmployeeId,
} from '../../actions/action';

const initialState = {
    addresses: [], // List of all addresses
    currentAddress: null, // For viewing or editing a single address
    currentAddresses: null, // For viewing or editing a single address by customer id
    isLoading: false,
    error: null,
};

const employeeAddressSlice = createSlice({
    name: 'customerAddress',
    initialState,
    reducers: {
        clearCurrentAddress(state) {
            state.currentAddress = null; // Clear current address details
            state.currentAddresses = null;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Get all customer addresses
            .addCase(getAllEmployeeAddresses.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(getAllEmployeeAddresses.fulfilled, (state, action) => {
                state.isLoading = false;
                state.addresses = action.payload;
            })
            .addCase(getAllEmployeeAddresses.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee addresses');
            })

            // Get a customer address by ID
            .addCase(getEmployeeAddressById.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(getEmployeeAddressById.fulfilled, (state, action) => {
                state.isLoading = false;
                state.currentAddresses = action.payload;
            })
            .addCase(getEmployeeAddressById.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee address');
            })

            // Get a customer address by Customer ID
            .addCase(getEmployeeAddressByEmployeeId.pending, (state) => {
                state.isLoading = true;
                state.error = null;
                state.currentAddress = null;
            })
            .addCase(getEmployeeAddressByEmployeeId.fulfilled, (state, action) => {
                state.isLoading = false;
                if (action.payload === null || action.payload === undefined || action.payload === '') {
                    state.currentAddress = null;
                } else {
                    state.currentAddress = action.payload;
                }
            })
            .addCase(getEmployeeAddressByEmployeeId.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to fetch employee address');
            })

            // Create a customer address
            .addCase(createEmployeeAddress.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(createEmployeeAddress.fulfilled, (state, action) => {
                state.isLoading = false;
                state.addresses.push(action.payload); // Add new address to list
                toast.success('Employee address created successfully');
            })
            .addCase(createEmployeeAddress.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to create employee address');
            })

            // Update a customer address
            .addCase(updatemployeeAddress.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(updatemployeeAddress.fulfilled, (state, action) => {
                state.isLoading = false;
                const index = state.addresses.findIndex((addr) => addr.id === action.payload.id);
                if (index !== -1) {
                    state.addresses[index] = action.payload; // Update address in list
                }
                toast.success('Employee address updated successfully');
            })
            .addCase(updatemployeeAddress.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to update employee address');
            })

            // Delete a customer address
            .addCase(deleteEmployeeAddress.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(deleteEmployeeAddress.fulfilled, (state, action) => {
                state.isLoading = false;
                state.addresses = state.addresses.filter((addr) => addr.id !== action.payload); // Remove address
                toast.success('Employee address deleted successfully');
            })
            .addCase(deleteEmployeeAddress.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload;
                toast.error('Failed to delete employee address');
            });
    },
});

export const { clearCurrentAddress } = employeeAddressSlice.actions;

export default employeeAddressSlice.reducer;
