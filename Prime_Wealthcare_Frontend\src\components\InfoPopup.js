import React from 'react';
import { Dialog, DialogActions, DialogTitle, Button } from '@mui/material';

const InfoPopup = ({ open, onClose, onConfirm, message, currentSection }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '376px',
          height: '210px',
          padding: '10px 0px 0px 0px',
          gap: '15px',
          borderRadius: '10px',
          position: 'relative',
          margin: 'auto',
          opacity: 1,
          borderRight: '2px solid red',
          borderLeft: '2px solid red',
          borderBottom: '4px solid red',
        },
      }}
    >
      <DialogTitle>
        {message}
      </DialogTitle>
      <DialogActions
        style={{
          justifyContent: 'center',
        }}
      >
        <Button
          onClick={() => onConfirm(currentSection)}
          sx={{ backgroundColor: '#528A7E', color: 'white', '&:hover': { backgroundColor: '#3f6e5e' } }}
        >
          Yes
        </Button>
        <Button
          onClick={onClose}
          sx={{ backgroundColor: 'red', color: 'white', '&:hover': { backgroundColor: '#d32f2f' } }}
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InfoPopup;
