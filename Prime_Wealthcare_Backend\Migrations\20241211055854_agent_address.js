exports.up = function(knex) {
    return knex.schema.createTable('agent_address', function(table) {
      table.increments('id').primary();  // INT AUTO_INCREMENT PRIMARY KEY
  
      // Foreign Key to the Agents table
      table.string('agent_id').notNullable()  // VARCHAR - Foreign Key for agent code
        .references('agent_id').inTable('agents').onDelete('CASCADE');
  
      // Current Address Fields
      table.string('current_apartment_no', 50).notNullable();  // VARCHAR(50)
      table.string('current_apartment_name', 100).notNullable();  // VARCHAR(100)
      table.string('current_address_line1', 255).notNullable();  // VARCHAR(255)
      table.string('current_address_line2', 255).nullable();  // VARCHAR(255) - Nullable
      table.string('current_pincode').nullable();
      table.integer('current_area_id').unsigned().nullable()  // INT - Foreign Key for subareas
        .references('id').inTable('areas').onDelete('CASCADE');
      table.string('current_city', 100).notNullable();  // VARCHAR(100)
      table.string('current_state', 100).notNullable();  // VARCHAR(100)

      // Permanent Address Fields
      table.string('permanent_apartment_no', 50).notNullable();  // VARCHAR(50)
      table.string('permanent_apartment_name', 100).notNullable();  // VARCHAR(100)
      table.string('permanent_address_line1', 255).notNullable();  // VARCHAR(255)
      table.string('permanent_address_line2', 255).nullable();  // VARCHAR(255) - Nullable
      table.string('permanent_pincode').nullable();
      table.integer('permanent_area_id').unsigned().nullable()  // INT - Foreign Key for subareas
        .references('id').inTable('areas').onDelete('CASCADE');
      table.string('permanent_city', 100).notNullable();  // VARCHAR(100)
      table.string('permanent_state', 100).notNullable();  // VARCHAR(100)

      table.string('used_address', 100).notNullable();  // VARCHAR(100)
      table.string('use_current_as_permanent',50).nullable();

      // Common Fields
      table.boolean('status').notNullable().defaultTo(true);  // Status Field
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
  };
  
  exports.down = function(knex) {
    return knex.schema.dropTable('agent_address');
  };
  