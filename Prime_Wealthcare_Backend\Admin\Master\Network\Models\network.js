const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

class Network {
    // Get all networks with insurance company name
    static async getAll() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                );
            return networks;
        } catch (error) {
            console.error('Error fetching all networks:', error);
            throw error;
        }
    }

    static async getNetworkByInsuranceCompanyId(insuranceCompanyId) {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .where('insurance_company_id', insuranceCompanyId);
            return networks;
        } catch (error) {
            console.error('Error fetching networks by insurance company ID:', error);
            throw error;
        }
    }

    // Get a network by ID with insurance company name
    static async getById(id) {
        if (!id) throw new Error("Network ID is required");

        try {
            const network = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .where('network.id', id)
                .first();

            if (!network) {
                console.error(`No network found with ID: ${id}`);
                return null;
            }

            return network;
        } catch (error) {
            console.error(`Error fetching network with ID: ${id}`, error);
            throw error;
        }
    }

    // Create a new network
    static async create(networkData) {
        try {
            await db('network').insert(networkData);
        } catch (error) {
            console.error('Error inserting network:', error);
            throw error;
        }
    }

    // Update an existing network by ID
    static async update(id, networkData) {
        if (!id) throw new Error("Network ID is required");

        try {
            networkData.updated_at = getCurrentTimestamp();

            const result = await db('network').where('id', id).update(networkData);
            if (result) {

            } else {
                console.error(`No network found with ID: ${id} to update`);
            }
            return result;
        } catch (error) {
            console.error(`Error updating network with ID: ${id}`, error);
            throw error;
        }
    }

    // Soft delete a network by ID (set status to 0)
    static async delete(id) {
        try {
            const result = await db('network')
                .where('id', id)
                .update({ status: 0, updated_at: getCurrentTimestamp() });
            if (result) {

            } else {
                console.error(`No network found with ID: ${id} to deactivate`);
            }
            return result;
        } catch (error) {
            console.error(`Error deactivating network with ID: ${id}`, error);
            throw error;
        }
    }

    // Reinstate a network by ID (set status to 1)
    static async reinstate(id) {
        try {
            const result = await db('network')
                .where('id', id)
                .update({ status: 1, updated_at: getCurrentTimestamp() });
            if (result) {

            } else {
                console.error(`No network found with ID: ${id} to reinstate`);
            }
            return result;
        } catch (error) {
            console.error(`Error reinstating network with ID: ${id}`, error);
            throw error;
        }
    }

    // Find networks by name with insurance company name
    static async getByName(name) {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .where(function () {
                    this.where('network.hospital_name', 'LIKE', `%${name}%`)
                        .orWhere('network.city', 'LIKE', `%${name}%`)
                        .orWhere('network.area', 'LIKE', `%${name}%`)
                        .orWhere('network.state', 'LIKE', `%${name}%`)
                        .orWhere('insurance_company.insurance_company_name', 'LIKE', `%${name}%`)
                })
            return networks;
        } catch (error) {
            console.error(`Error fetching networks with name: ${name}`, error);
            throw error;
        }
    }

    // Find new networks created last week with insurance company name
    static async newLastWeek() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .whereBetween('network.created_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return networks;
        } catch (error) {
            console.error('Error fetching networks created last week:', error);
            throw error;
        }
    }

    // Find new networks created this week with insurance company name
    static async newThisWeek() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .whereBetween('network.created_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                    db.raw('NOW()')
                ]);

            return networks;
        } catch (error) {
            console.error('Error fetching networks created this week:', error);
            throw error;
        }
    }

    // Find deactivated networks updated this week with insurance company name
    static async deactivatedThisWeek() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .where('network.status', 0)
                .whereBetween('network.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                    db.raw('NOW()')
                ]);

            return networks;
        } catch (error) {
            console.error('Error fetching deactivated networks updated this week:', error);
            throw error;
        }
    }

    // Find deactivated networks updated last week with insurance company name
    static async deactivatedLastWeek() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .where('network.status', 0)
                .whereBetween('network.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return networks;
        } catch (error) {
            console.error('Error fetching deactivated networks updated last week:', error);
            throw error;
        }
    }

    // Find edited networks updated this week with insurance company name
    static async editedThisWeek() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .whereBetween('network.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                    db.raw('NOW()')
                ]);

            return networks;
        } catch (error) {
            console.error('Error fetching edited networks updated this week:', error);
            throw error;
        }
    }

    // Find edited networks updated last week with insurance company name
    static async editedLastWeek() {
        try {
            const networks = await db('network')
                .join('insurance_company', 'network.insurance_company_id', 'insurance_company.id')
                .leftJoin('areas', 'network.area', 'areas.id')
                .select(
                    'network.*',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'areas.area as area_name'
                )
                .whereBetween('network.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return networks;
        } catch (error) {
            console.error('Error fetching edited networks updated last week:', error);
            throw error;
        }
    }
}

module.exports = Network;
