const knexConfig = require('../../../../knexfile');
const db = require('knex')(knexConfig.development);

// Retrieve all areas
const getAllAreas = async () => {
    try {
        const areas = await db('areas').select('*');
        return areas;
    } catch (error) {
        console.error('Error fetching areas:', error);
        throw error;
    }
};

// Retrieve areas by pincode
const getAreasByPincodeAndCity = async (pincode, city) => {
    try {
        const areas = await db('areas').select('*').where('pincode', pincode).andWhere('city', city);
        return areas;
    } catch (error) {
        console.error('Error fetching areas with the specified pincode:', error);
        throw error;
    }
};
// Retrieve areas by pincode
const getAreasByPincode_City = async (pincode_city) => {
    try {
        const areas = await db('areas').select('*').where('pincode_city', pincode_city);
        return areas;
    } catch (error) {
        console.error('Error fetching areas with the specified pincode:', error);
        throw error;
    }
};

const getAreaById = async (id) => {
    try {
        const data = await db('areas').where('id', id).first();
        return data;
    } catch (error) {
        console.error('Error fetching area by ID:', error);
        throw (error);
    }
}

// Get the ID of an area by pincode and area name
const getIdByPincodeAndName = async (pincode, area) => {
    try {
        const result = await db('areas')
            .select('id')
            .where({ pincode: pincode, area: area })
            .first();
        return result ? result.id : null;
    } catch (error) {
        console.error('Error fetching area by pincode and name:', error);
        throw error;
    }
};

// Insert a new area
const createArea = async (data) => {
    try {
        const newData = { ...data, pincode_city: `${data.pincode}${data.city.replace(/\s+/g, '')}` };
        await db('areas').insert(newData);
    } catch (error) {
        console.error('Error inserting area:', error);
        throw error;
    }
};

// Update an existing area by ID
const updateArea = async (id, data) => {
    if (!id) throw new Error("Area ID is required");

    try {
        const result = await db('areas').where('id', id).update(data);
        if (!result) {
            console.error(`No area found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating area with ID: ${id}`, error);
        throw error;
    }
};

// Delete an area by ID
const deleteArea = async (id) => {
    try {
        await db('areas').where('id', id).del();
    } catch (error) {
        console.error(`Error deleting area with ID: ${id}`, error);
        throw error;
    }
};

module.exports = {
    getAllAreas,
    getAreasByPincodeAndCity,
    getAreasByPincode_City,
    getIdByPincodeAndName,
    getAreaById,
    createArea,
    updateArea,
    deleteArea,
};
