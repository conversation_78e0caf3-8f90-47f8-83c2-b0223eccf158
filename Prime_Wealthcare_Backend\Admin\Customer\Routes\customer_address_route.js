const express = require('express');
const customerAddressController = require('../Controllers/customer_address_controller');
const router = express.Router();

// Route to create a new customer info
router.post('/', customerAddressController.createCustomerAddress);

router.get('/customer_id/:id', customerAddressController.getCostomerAddressByCustomerId);

router.get('/', customerAddressController.getCustomerAddress);

router.get('/:id', customerAddressController.getCostomerAddressById);



router.put('/:id', customerAddressController.updateCustomerAddress);

router.delete('/:id', customerAddressController.deleteCustomerAddress);

module.exports = router;