const ProposalModel = require('../Models/proposalModel.js');
const uploadDir = process.env.UPLOAD_DIR;
// Create Proposal
exports.createProposal = async (req, res) => {
    try {
        // Fix req.body
        let parsedBody = req.body;
        // If req.body.data exists and is a string, parse it
        if (parsedBody.data && typeof parsedBody.data === 'string') {
            try {
                parsedBody = JSON.parse(parsedBody.data);
            } catch (error) {
                console.error('Error parsing req.body.data:', error);
            }
        }
        const proposalData = parsedBody.proposalData;
        const memberData = parsedBody.memberData;

        // Check if files exist and handle the policy PDF
        if (req.files && req.files.policy_pdf && req.files.policy_pdf.length > 0) {
            const path = req.files.policy_pdf[0].path;
            proposalData.policy_pdf = path.replace(uploadDir, '');
        }

        const proposal = await ProposalModel.createProposal(proposalData, memberData);
        res.status(201).json(proposal);
    } catch (error) {
        console.error('Error creating proposal:', error);
        res.status(500).json({
            message: 'Error creating proposal',
            error: error.message,
            code: error.code
        });
    }
};

// Update Proposal
exports.updateProposal = async (req, res) => {
    try {
        const { id } = req.params;
        // Fix req.body
        let parsedBody = req.body;
        // If req.body.data exists and is a string, parse it
        if (parsedBody.data && typeof parsedBody.data === 'string') {
            try {
                parsedBody = JSON.parse(parsedBody.data);
            } catch (error) {
                console.error('Error parsing req.body.data:', error);
            }
        }
        const proposalData = parsedBody.proposalData;
        const memberData = parsedBody.memberData;

        // Check if files exist and handle the policy PDF
        if (req.files && req.files.policy_pdf && req.files.policy_pdf.length > 0) {
            const path = req.files.policy_pdf[0].path;
            proposalData.policy_pdf = path.replace(uploadDir, '');
        }
        // if (req.files?.policy_pdf) {
        //     proposalData.policy_pdf = `/proposals/${proposalData.policy_number}/${req.files.policy_pdf[0].filename}`;
        // }

        const result = await ProposalModel.updateProposal(id, proposalData, memberData);
        res.status(200).json(result);
    } catch (error) {
        console.error('Error updating proposal:', error);
        res.status(500).json({
            message: 'Error updating proposal',
            error: error.message
        });
    }
};

exports.getProposalById = async (req, res) => {
    try {
        const { id } = req.params;
        const proposal = await ProposalModel.getProposalById(id);

        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }

        res.status(200).json(proposal);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching proposal', error });
    }
};

exports.getProposalByQuotationNumber = async (req, res) => {
    try {
        const { quotation_number } = req.params;
        const proposal = await ProposalModel.getProposalByQuotationNumber(quotation_number);
        res.status(200).json(proposal);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching proposal', error });
    }
};

exports.getProposalByPolicyNumber = async (req, res) => {
    try {
        const { policy_number, policy_type } = req.params;
        const proposal = await ProposalModel.getProposalByPolicyNumber({ policy_number, policy_type });
        if (proposal?.error) {
            return res.status(404).json({ message: proposal.error });
        }
        res.status(200).json(proposal);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching proposal', error });
    }
}

exports.getAllProposals = async (req, res) => {
    try {
        const proposals = await ProposalModel.getAllProposals();
        res.status(200).json(proposals);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching proposals', error });
    }
};

exports.getAllProposalsByUserId = async (req, res) => {
    try {
        const { userId } = req.params;
        const proposals = await ProposalModel.getAllProposalsByUserId(userId);
        res.status(200).json(proposals);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching proposals', error });
    }
};

exports.getProposalByNumber = async (req, res) => {
    try {
        const { proposal_number } = req.params;
        const proposal = await ProposalModel.getProposalByNumber(proposal_number);

        if (!proposal) {
            return res.status(404).json({ message: 'Proposal not found' });
        }

        res.status(200).json(proposal);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching proposal', error });
    }
};

exports.getProposalCount = async (req, res) => {
    try {
        const result = await ProposalModel.getProposalCount(req.params.prefix);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
}

exports.deleteProposal = async (req, res) => {
    try {
        const { remarks, proposal_number } = req.body;
        const { id, policy_type } = req.params;
        const result = await ProposalModel.deleteProposal(id, policy_type, proposal_number, remarks);
        if (result) {
            res.status(200).json({ message: 'Proposal deleted successfully' });
        } else {
            res.status(404).json({ message: 'Proposal not found' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Error deleting proposal', error });
    }
}


exports.transferBusiness = async (req, res) => {
    try {
        const { id } = req.params;
        const { agent_id } = req.body;

        // Validate input data
        if (!id || !agent_id) {
            return res.status(400).json({
                success: false,
                message: 'ID and Agent ID are required'
            });
        }

        // Call the model function with separate parameters
        const result = await ProposalModel.transferBusiness(id, agent_id);

        if (result.success) {
            return res.status(200).json({
                success: true,
                message: 'Business transferred successfully',
                data: result.data
            });
        } else {
            return res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error transferring business:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
};