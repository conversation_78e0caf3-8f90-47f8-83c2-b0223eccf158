exports.up = function (knex) {
    return knex.schema.createTable('insurance_branch', function (table) {
        table.increments('id');
        table.integer('insurance_company_id').unsigned().notNullable();
        table.foreign('insurance_company_id').references('id').inTable('insurance_company').onDelete('CASCADE');
        table.string('insurance_co_branch_name', 255).notNullable();
        table.string('imf_code', 255);
        table.string('branch_code', 255);
        table.unique(['imf_code', 'branch_code'], 'unique_imf_branch_code');
        table.string('email_id', 255);
        table.string('branch_manager_name', 255);
        table.string('branch_manager_number', 10);
        table.string('insurance_type', 255);
        table.string('assistant_branch_manager_name', 255);
        table.string('assistant_branch_manager_no', 10);
        table.string('sales_manager_name', 255);
        table.string('sales_manager_no', 10);
        table.decimal('company_percentage', 5, 2).defaultTo(0.00);
        table.boolean('status').notNullable().defaultTo(true);
        table.timestamps(true, true);
        table.unique(['insurance_company_id', 'insurance_co_branch_name'],
            'ins_co_id_branch_name_unique');
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('insurance_branch');
};
