import React from 'react';
import { Box, Grid, Typography } from '@mui/material';
import { useNavigate, useLocation, useParams } from 'react-router-dom';

export default function CustomSection({ titles, page, customerType }) {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  //const { customerType } = useParams();
  const getActiveTitle = () => {
    const pathIncludes = (str) => location.pathname.includes(str);
    const prefix = page === 'agent' ? 'agent' :
      page === 'employee' ? 'employee' :
        page === 'quotations' ? 'quotation' : 'customer';

    if (page === 'quotations') {
      if (pathIncludes('quotation-overview')) {
        return 'Overview';
      }
      if (pathIncludes('quotation')) {
        return 'Quotation';
      }
    }

    if (pathIncludes(`${prefix}-personal-information`)) {
      return 'Personal Details';
    }
    if (pathIncludes(`${prefix}-address`)) {
      return 'Address';
    }
    if (pathIncludes(`${prefix}-member-information`)) {
      return 'Member Information';
    }
    if (pathIncludes(`${prefix}-grouping`)) {
      return 'Grouping';
    }
    if (page === 'quick-quotations') {
      if (pathIncludes('quick-quotation-overview')) {
        return 'Overview';
      }
      if (pathIncludes('quick-quotation')) {
        return 'Create';
      }
    }
    return 'Overview';
  };

  const activeTitle = getActiveTitle();

  const handleTitleClick = (title) => {
    if (!id && page !== 'quotations' && page !== 'quick-quotations') {
      if (title !== activeTitle) {
        return;
      }
    }

    switch (title) {
      case 'Overview':
        if (page === 'quick-quotations') {
          navigate(`/dashboard/quick-quotation-overview`);
        } else if (page === 'quotations') {
          navigate(`/dashboard/quotation-overview`);
        } else if (page === 'agent') {
          navigate(`/dashboard/agent-master-overview/${id}`);
        } else if (page === 'employee') {
          navigate(`/dashboard/employee-master-overview/${id}`);
        } else if (page === 'customer') {
          navigate(`/dashboard/customer-follow-up/${id}`);
        }
        break;
      case 'Quotation':
        if (page === 'quotations') {
          navigate(`/dashboard/quotation`);
        }
        break;
      case 'Personal Details':
        if (!id) return; // Don't navigate if no ID
        if (page === 'agent') {
          navigate(`/dashboard/agent-personal-information/${id}`);
        } else if (page === 'employee') {
          navigate(`/dashboard/employee-personal-information/${id}`);
        } else if (page === 'customer') {
          navigate(`/dashboard/customer-personal-information/${id}`);
        }
        break;
      case 'Address':
        if (page === 'agent') {
          navigate(`/dashboard/agent-address/${id}`);
        } else if (page === 'employee') {
          navigate(`/dashboard/employee-address/${id}`);
        }
        else if (page === 'customer') {
          navigate(`/dashboard/customer-address/${id}`);
        }
        break;
      case 'Member Information':
        if (!id) return; // Don't navigate if no ID
        if (page === 'customer' && customerType === 'individual') {
          navigate(`/dashboard/customer-member-information/${id}`);
        }
        break;
      case 'Grouping':
        if (!id) return; // Don't navigate if no ID
        if (page === 'customer' && customerType === 'individual') {
          navigate(`/dashboard/customer-grouping/${id}`);
        }
        break;
      case 'Create':
        if (page === 'quick-quotations') {
          navigate(`/dashboard/quick-quotation`);
        }
        break;
      default:
        break;
    }
  };

  return (
    <Grid item xs={12}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          padding: '10px',
          borderRadius: '4px',
          height: '60px',
          fontSize: '18px',
          fontStyle: 'normal',
          fontWeight: '700',
          lineHeight: '27px',
          color: '#4C5157',
          flexWrap: 'wrap',
          justifyContent: {
            xs: 'center',
            sm: 'flex-start'
          },
          height: {
            xs: 'auto',
            sm: '60px'
          }
        }}
      >
        {titles.map((title, index) => (
          <Box
            key={index}
            sx={{
              marginRight: {
                xs: '20px',
                sm: index < titles.length - 1 ? '40px' : 0,
                md: index < titles.length - 1 ? '80px' : 0
              },
              marginBottom: {
                xs: '10px',
                sm: 0
              },
              cursor: (!id && title !== activeTitle && page !== 'quotations' && page !== 'quick-quotations') ? 'not-allowed' : 'pointer',
              opacity: (!id && title !== activeTitle && page !== 'quotations' && page !== 'quick-quotations') ? 0.5 : 1,
              '&:hover': {
                cursor: (!id && title !== activeTitle && page !== 'quotations' && page !== 'quick-quotations') ? 'not-allowed' : 'pointer'
              }
            }}
            onClick={() => handleTitleClick(title)}
          >
            <Typography
              variant="body2"
              sx={{
                fontWeight: activeTitle === title ? 'bold' : 'normal',
                color: activeTitle === title ? '#528A7E' : 'inherit',
                fontSize: {
                  xs: '14px',
                  sm: '16px',
                  md: '18px'
                }
              }}
            >
              {title}
            </Typography>
          </Box>
        ))}
      </Box>
      <hr />
    </Grid >
  );
}