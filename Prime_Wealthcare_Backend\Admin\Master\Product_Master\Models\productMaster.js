const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

class ProductMaster {
    // Get all products with insurance company and main product names
    static async getAll() {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.created_by',
                    'product_master.updated_by',
                    'product_master.created_at',
                    'product_master.updated_at',
                    'product_master.status'
                );

            return products;
        } catch (error) {
            console.error('Error fetching products:', error);
            throw error;
        }
    }

    // Get a product by main product and insurance company
    static async getMasterProductByMainProductAndInsuranceCompany(mainProductId, insuranceCompanyId) {
        try {
            const product = await db('product_master')
                .where('product_master.status', 1)
                .where('product_master.main_product_id', mainProductId)
                .where('product_master.insurance_company_id', insuranceCompanyId);
            return product;
        } catch (error) {
            console.error('Error fetching product by main product and insurance company:', error);
            throw error;
        }
    }

    // Get a product by ID with insurance company and main product names
    static async getById(id) {
        if (!id) throw new Error("Product ID is required");

        try {
            const product = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.main_product_id',
                    'product_master.insurance_company_id',
                    'product_master.created_by',
                    'product_master.updated_by',
                    'product_master.created_at',
                    'product_master.updated_at',
                    'product_master.status'
                )
                .where('product_master.id', id)
                .first();

            if (!product) {
                console.error(`No product found with ID: ${id}`);
                return null;
            }

            return product;
        } catch (error) {
            console.error(`Error fetching product with ID: ${id}`, error);
            throw error;
        }
    }

    // Create a new product
    static async create(productData) {

        try {
            await db('product_master').insert(productData);

        } catch (error) {
            console.error('Error inserting product:', error);
            throw error;
        }
    }

    // Update an existing product by ID
    static async update(id, productData) {
        if (!id) throw new Error("Product ID is required");

        try {
            productData.updated_at = getCurrentTimestamp();

            const result = await db('product_master').where('id', id).update(productData);
            if (result) {

            } else {
                console.error(`No product found with ID: ${id} to update`);
            }
        } catch (error) {
            console.error(`Error updating product with ID: ${id}`, error);
            throw error;
        }
    }

    // Soft delete a product by ID (set status to 0)
    static async delete(id) {
        try {

            const result = await db('product_master').where('id', id).update({ status: 0, updated_at: getCurrentTimestamp() });
            
        } catch (error) {
            console.error(`Error deactivating product with ID: ${id}`, error);
            throw error;
        }
    }

    // Reinstate a product by ID (set status to 1)
    static async reinstate(id) {
        try {

            await db('product_master').where('id', id).update({ status: 1 , updated_at: getCurrentTimestamp()});
            return ({ message: 'Sucessfully reinstated product' })
        } catch (error) {
            console.error(`Error reinstating product with ID: ${id}`, error);
            throw error;
        }
    }

    // Find products by name
    static async getByName(name) {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.created_by',
                    'product_master.updated_by',
                    'product_master.created_at',
                    'product_master.updated_at',
                    'product_master.status'
            )
                .where(function () {
                    this.where('product_master.product_name', 'LIKE', `%${name}%`)
                    .orWhere('main_product.main_product', 'LIKE', `%${name}%`)
                    
                })

            return products;
        } catch (error) {
            console.error(`Error fetching products with name: ${name}`, error);
            throw error;
        }
    }

    // Find new products created last week
    static async newLastWeek() {
        try {
            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.created_at',
                    'product_master.status'
                )
                .whereBetween('product_master.created_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);
            return products;
        } catch (error) {
            console.error('Error fetching products created last week:', error);
            throw error;
        }
    }

    // Find new products created this week
    static async newThisWeek() {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.created_at',
                    'product_master.status'
                )
                .whereBetween('product_master.created_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching products created this week:', error);
            throw error;
        }
    }
    // Find deactivated products updated this week
    static async deactivatedThisWeek() {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.updated_at',
                    'product_master.status'
                )
                .where('product_master.status', 0)
                .whereBetween('product_master.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching deactivated products updated this week:', error);
            throw error;
        }
    }
    // Find deactivated products updated last week
    static async deactivatedLastWeek() {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.updated_at',
                    'product_master.status'
                )
                .where('product_master.status', 0)
                .whereBetween('product_master.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching deactivated products updated last week:', error);
            throw error;
        }
    }
    // Find edited products updated this week
    // Remove this duplicate definition
    static async editedThisWeek() {
        try {
            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.updated_at',
                    'product_master.status'
                )
                .whereBetween('product_master.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching edited products updated this week:', error);
            throw error;
        }
    }

    // Find edited products updated this week
    static async editedThisWeek() {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.updated_at',
                    'product_master.status'
                )
                .whereBetween('product_master.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                    db.raw('NOW()')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching edited products updated this week:', error);
            throw error;
        }
    }
    // Find edited products updated last week
    static async editedLastWeek() {
        try {

            const products = await db('product_master')
                .join('main_product', 'product_master.main_product_id', 'main_product.id')
                .join('insurance_company', 'product_master.insurance_company_id', 'insurance_company.id')
                .select(
                    'product_master.id',
                    'product_master.product_name',
                    'main_product.main_product as main_product_name',
                    'insurance_company.insurance_company_name as insurance_company_name',
                    'product_master.updated_at',
                    'product_master.status'
                )
                .whereBetween('product_master.updated_at', [
                    db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                    db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
                ]);

            return products;
        } catch (error) {
            console.error('Error fetching edited products updated last week:', error);
            throw error;
        }
    }

}

module.exports = ProductMaster;
