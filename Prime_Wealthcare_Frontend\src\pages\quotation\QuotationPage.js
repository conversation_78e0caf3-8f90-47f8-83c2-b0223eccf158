import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import CustomSection from '../../components/CustomSection';
import ModuleName from '../../components/table/ModuleName';
import Dropdown from '../../components/table/DropDown';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import CustomTextField from '../../components/CustomTextField';
import AddIcon from '@mui/material/Icon';
import DeleteIcon from '@mui/icons-material/Delete';
import { useDispatch, useSelector } from 'react-redux';
import { getAllProducts, fetchInsuranceCompanies, getAllMasterProducts, getAllCustomer, getMemberByCustomerId, createQuotationWithMembers, getCustomerAddressByCustomerId, getAllAgentDetails, getQuotationDetailsByQuotationId, getMasterProductByMainProductAndInsuranceCompany, updateQuotationWithMembers, getSubProductByProductDetails, getAllPAoccupationLists, getLocationById, getEmployeeOrAgentById } from '../../redux/actions/action';
import { Autocomplete, TextField, Typography, CircularProgress, IconButton, Card, duration, InputAdornment } from '@mui/material';
import BarLoader from '../../components/BarLoader';
import { toast } from 'react-toastify';

const QuotationPage = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { user } = useSelector((state) => state.auth);
    const userId = user?.userId;
    const { id } = useParams();
    const mainProducts = useSelector(state => state.mainProductReducer.products);
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
    const masterProducts = useSelector(state => state.productMasterReducer.products);
    const [selectedCustomer, setSelectedCustomer] = useState(null);
    const coverTypes = useSelector(state => state.pickListReducer.coverTypeOptions);
    const [coverTypeOptions, setCoverTypeOptions] = useState([]);
    // Get customers and members from Redux store
    const customers = useSelector(state => state.customerReducer.customer);
    const customerMembers = useSelector(state => state.customerMemberReducer.customerMember);
    const quotationDetails = useSelector(state => state.quotationReducer.quotationDetails);
    const riskClass = useSelector(state => state.PA_occupationReducer.PA_occupation_Lists);

    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);

    const [filteredCustomers, setFilteredCustomers] = useState([]);
    const [deductableAmount, setDeductableAmount] = useState([]);
    const [formData, setFormData] = useState({
        customer_id: '',
        insurance_company_id: '',
        main_product_id: '',
        product_master_id: '',
        product_master_name: '',
        cover_type: '',
        cover_type_id: '',
        sum_insured: [],
        duration: '',
        copay: 'N', // Add this new field
        deductible: '',
        installments: '',
        family_type: 'individual',
        assigned_to: '',
        created_by: userId,
        agent_id: '',
        updated_by: '',
        created_at: new Date(),
        updated_at: new Date(),
        member: '',

    });
    const agents = useSelector(state => state.agentReducer.agents);

    const educationOptions = useSelector((state) => state.pickListReducer.OccupationOptions);
    useEffect(() => {
        dispatch(getAllAgentDetails());
        dispatch(getAllPAoccupationLists());
    }, [dispatch]);

    const [agentOptions, setAgentOptions] = useState([])

    const [errors, setErrors] = useState({
        members: [] // Array to store member-specific errors
    });
    const [members, setMembers] = useState([]);
    //const [errors, setErrors] = useState({}); // State for error messages
    useEffect(() => {
        // if (isEditing) {
        //     return;
        // }
        if (selectedCustomer && customerMembers) {
            const customerAsMember = {
                id: selectedCustomer.id,
                full_name: `${selectedCustomer.first_name} ${selectedCustomer.last_name}`,
                date_of_birth: selectedCustomer.date_of_birth,
                relation: 'SELF',
                deductible: '',
                weight: selectedCustomer.weight || 0,
                height: selectedCustomer.height || 0,
                isSmoking: selectedCustomer.isSmoking || '',
                isTobacco: selectedCustomer.isTobacco || '',
                occupation: educationOptions.find(edu => edu.id === selectedCustomer.occupation)?.label_name || '',
            };

            // Filter members to only include allowed relations
            //const allowedRelations = ['SELF', 'SPOUSE', 'SON', 'DAUGHTER'];
            const membersWithWeights = customerMembers
                //  .filter(member => allowedRelations.includes(member.relation))
                .map(member => {
                    return {
                        ...member,
                        deductible: '',
                        weight: member.member_weight || 0,
                        height: member.member_height || 0,
                        isSmoking: member.isSmoking || '',
                        isTobacco: member.isTobacco || '',
                        occupation: educationOptions.find(edu => edu.id === member.member_occupation)?.label_name || '',
                    };
                });

            setMembers([customerAsMember, ...membersWithWeights]);
        }
    }, [selectedCustomer, customerMembers, educationOptions]);


    useEffect(() => {
        if (agents && !id) {
            if (userId?.includes('RM')) {
                const agent = agents.find(agent => agent.agent_id === userId);
                setFormData(prev => ({
                    ...prev,
                    agent_id: agent?.id
                }));
            } else if (userId?.includes('EMP')) {
                dispatch(getEmployeeOrAgentById(userId)).then((res) => {
                    const branchIds = res.payload?.branch_id?.split(',')?.map(id => Number(id.trim()));
                    const filteredAgents = agents.filter(agent => branchIds?.includes(agent.branch_id));
                    setAgentOptions(filteredAgents.map((agent) => ({
                        value: agent.id,
                        label: `${agent.agent_id} (${agent.full_name})`  // Modified format
                    })));
                });
            } else {
                setAgentOptions(agents.filter(agent => agent.status === 1).map((agent) => ({
                    value: agent.id,
                    label: `${agent.agent_id} (${agent.full_name})`  // Modified format
                })));
            }
        }
    }, [agents])

    const handleDeleteMember = (index) => {
        setMembers((prevMembers) => {
            const updatedMembers = prevMembers.filter((_, i) => i !== index); // Remove member at the specified index
            return updatedMembers; // Update the state
        });
    };

    const handleSumInsuredChange = (index, value) => {
        // Update the sum_insured array with the new value
        setFormData((prev) => {
            const updatedSumInsured = [...(prev.sum_insured || [])];
            updatedSumInsured[index] = value; // Update only the selected index
            return { ...prev, sum_insured: updatedSumInsured };
        });
        // Clear the sum_insured error when a value is changed
        setErrors((prevErrors) => ({
            ...prevErrors,
            sum_insured: undefined, // Clear the error for sum_insured
        }));
    };
    // Function to filter customers based on search input
    const filterCustomers = (searchText) => {
        if (!searchText || searchText.length < 2) {
            setFilteredCustomers([]);
            return;
        }

        const searchLower = searchText.toLowerCase();
        const filtered = customers.filter(customer =>
            `${customer.first_name} ${customer.last_name}`.toLowerCase().includes(searchLower) ||
            customer.mobile?.includes(searchText) ||
            customer.email?.toLowerCase().includes(searchLower)
        );
        setFilteredCustomers(filtered);
    };
    // Handle input change for search
    const handleSearchInputChange = (event, newInputValue) => {
        setSearchTerm(newInputValue);
        filterCustomers(newInputValue);
    };

    const getOccupationRiskClass = (occupationName) => {
        const occupationId = educationOptions.find(edu => edu.label_name === occupationName)?.id;
        const riskClasses = riskClass.find(risk => risk.pick_list_id === occupationId)?.risk_class;
        return riskClasses;
    };

    const calculateMaxSumInsured = (annualIncome, coverType, riskClass, occupation, relation) => {
        // Special case for Unemployed/Student who is SELF
        if ((occupation === 'Unemployed' || occupation === 'Student') && relation === 'SELF') {
            if (['AD', 'PT', 'PP'].includes(coverType)) {
                return 1000000; // 10 lakhs maximum for AD, PTD, PPD
            }
            if (coverType === 'TTD') {
                return 0; // No TTD for Unemployed/Student
            }
        }

        const monthlyIncome = annualIncome / 12;

        // Multiplying factors based on the table
        const multipliers = {
            'AD': 144,
            'PT': 144,
            'PP': 144,
            'TTD': 24
        };

        // Calculate the maximum based on income
        const calculatedMax = monthlyIncome * (multipliers[coverType] || 0);

        // For AD, PP, and PT, cap at 30 lakhs
        if (['AD', 'PP', 'PT'].includes(coverType)) {
            return Math.min(calculatedMax, 3000000);
        }
        else if (coverType === 'TTD') {
            const riskClassStr = String(riskClass);
            if (riskClassStr === '1' || riskClassStr === '1') {
                return Math.min(calculatedMax, 5000000);
            } else if (riskClassStr === '2' || riskClass === 2) {
                return Math.min(calculatedMax, 2500000);
            }
            return Math.min(calculatedMax, 2500000);
        }

        return calculatedMax;
    };

    const handleMemberChange = (index, field, value) => {

        const newMembers = [...members];
        newMembers[index][field] = value;

        // Check if family type is floater
        if (formData.family_type === 'floater' || formData.product_master_name === 'FG ACCIDENT SURAKSHA') {

            const dependentRelations = ['FATHER', 'MOTHER', 'BROTHER', 'SISTER'];
            if (dependentRelations.includes(value.relation)) {
                toast.error("Dependent parents (Father, Mother, Brother, Sister) are not allowed in a floater policy.");
                return; // Exit the function if the relation is not allowed
            }
        }

        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {

            // Only update other members' corresponding fields
            if (newMembers[index].relation === 'SELF') {
                const annualIncome = Number(newMembers[index].annual_income);
                const occupationName = newMembers[index].occupation;
                const riskClass = getOccupationRiskClass(occupationName);

                // Update only the corresponding field in other members
                newMembers.forEach((member, memberIndex) => {
                    if (memberIndex === index) return; // Skip primary member

                    const memberOccupation = member.occupation;

                    // Check if the sum insured values need to be validated
                    const adSumInsured = Number(newMembers[index].ad_sum_insured || 0);
                    const ppSumInsured = Number(newMembers[index].pp_sum_insured || 0);
                    const ptSumInsured = Number(newMembers[index].pt_sum_insured || 0);
                    const ttSumInsured = Number(newMembers[index].tt_sum_insured || 0);

                    // Validate AD sum insured against PP, PT, and TTD
                    if (adSumInsured < Math.max(ppSumInsured, ptSumInsured, ttSumInsured)) {
                        setErrors(prev => {
                            const newMemberErrors = [...(prev.members || [])];
                            newMemberErrors[index] = {
                                ...(newMemberErrors[index] || {}),
                                ad_sum_insured: 'AD sum insured must be greater than or equal to PP, PT, and TTD sum insured'
                            };
                            return { ...prev, members: newMemberErrors };
                        });
                    } else {
                        // Clear the error if the condition is met
                        setErrors(prev => {
                            const newMemberErrors = [...(prev.members || [])];
                            newMemberErrors[index] = {
                                ...(newMemberErrors[index] || {}),
                                ad_sum_insured: undefined // Clear the error for ad_sum_insured
                            };
                            return { ...prev, members: newMemberErrors };
                        });
                    }
                    if (field === 'annual_income') {
                        if (['Housewife', 'Retired', 'Unemployed'].includes(memberOccupation) &&
                            member.relation === 'SPOUSE') {
                            member.annual_income = '0';
                        } else if (['SON', 'DAUGHTER'].includes(member.relation) &&
                            memberOccupation === 'Student') {
                            member.annual_income = '0';
                        }
                    }
                    else if (field === 'ad_sum_insured') {
                        const maxAD = Math.min(calculateMaxSumInsured(annualIncome, 'AD'), 3000000);
                        if (Number(value) < 50000) {
                            setErrors(prev => {
                                const newMemberErrors = [...(prev.members || [])];
                                newMemberErrors[index] = {
                                    ...(newMemberErrors[index] || {}),
                                    ad_sum_insured: 'Minimum sum insured is ₹50,000'
                                };
                                return { ...prev, members: newMemberErrors };
                            });
                            return; // Exit the function if the value is less than 50,000
                        }
                        if (['Housewife', 'Retired', 'Unemployed'].includes(memberOccupation) &&
                            member.relation === 'SPOUSE') {
                            member.ad_sum_insured = String(Math.min(Number(value) * 0.5, 1000000));
                        } else if (['SON', 'DAUGHTER'].includes(member.relation) &&
                            memberOccupation === 'Student') {
                            const age = calculateAge(member.date_of_birth);
                            if (age >= 3 && age <= 25) {
                                member.ad_sum_insured = String(Math.min(Number(value) * 0.25, 500000));
                            }
                        }
                    }
                    else if (field === 'pt_sum_insured') {
                        const maxPT = Math.min(calculateMaxSumInsured(annualIncome, 'PT'), 3000000);
                        if (Number(value) < 50000) {
                            setErrors(prev => {
                                const newMemberErrors = [...(prev.members || [])];
                                newMemberErrors[index] = {
                                    ...(newMemberErrors[index] || {}),
                                    pt_sum_insured: 'Minimum sum insured is ₹50,000'
                                };
                                return { ...prev, members: newMemberErrors };
                            });
                            return; // Exit the function if the value is less than 50,000
                        }
                        if (['Housewife', 'Retired', 'Unemployed'].includes(memberOccupation) &&
                            member.relation === 'SPOUSE') {
                            member.pt_sum_insured = String(Math.min(Number(value) * 0.5, 1000000));
                        } else if (['SON', 'DAUGHTER'].includes(member.relation) &&
                            memberOccupation === 'Student') {
                            const age = calculateAge(member.date_of_birth);
                            if (age >= 3 && age <= 25) {
                                member.pt_sum_insured = String(Math.min(Number(value) * 0.25, 500000));
                            }
                        }
                    }
                    else if (field === 'pp_sum_insured') {
                        const maxPP = Math.min(calculateMaxSumInsured(annualIncome, 'PP'), 3000000);
                        if (Number(value) < 50000) {
                            setErrors(prev => {
                                const newMemberErrors = [...(prev.members || [])];
                                newMemberErrors[index] = {
                                    ...(newMemberErrors[index] || {}),
                                    pp_sum_insured: 'Minimum sum insured is ₹50,000'
                                };
                                return { ...prev, members: newMemberErrors };
                            });
                            return; // Exit the function if the value is less than 50,000
                        }
                        if (['Housewife', 'Retired', 'Unemployed'].includes(memberOccupation) &&
                            member.relation === 'SPOUSE') {
                            member.pp_sum_insured = String(Math.min(Number(value) * 0.5, 1000000));
                        } else if (['SON', 'DAUGHTER'].includes(member.relation) &&
                            memberOccupation === 'Student') {
                            const age = calculateAge(member.date_of_birth);
                            if (age >= 3 && age <= 25) {
                                member.pp_sum_insured = String(Math.min(Number(value) * 0.25, 500000));
                            }
                        }
                    }
                    else if (field === 'tt_sum_insured') {
                        const maxTTD = calculateMaxSumInsured(annualIncome, 'TTD', riskClass);
                        if (Number(value) < 10000) {
                            setErrors(prev => {
                                const newMemberErrors = [...(prev.members || [])];
                                newMemberErrors[index] = {
                                    ...(newMemberErrors[index] || {}),
                                    tt_sum_insured: 'Minimum sum insured is ₹10,000'
                                };
                                return { ...prev, members: newMemberErrors };
                            });
                            return; // Exit the function if the value is less than 50,000
                        }
                        if (['Housewife', 'Retired', 'Unemployed'].includes(memberOccupation) &&
                            member.relation === 'SPOUSE') {
                            member.tt_sum_insured = String(Math.min(Number(value) * 0.5, 100000));
                            // member.tt_sum_insured = '0';

                        } else if (['SON', 'DAUGHTER'].includes(member.relation) &&
                            memberOccupation === 'Student') {
                            member.tt_sum_insured = '0'; // Students don't get TTD
                        }
                    }
                });
            }
        }

        // If additionalCover is being set to 'N', reset all sub-covers
        if (field === 'additionalCover' && value === 'N') {
            const subCovers = [
                'ChildEducationSupport',
                'LifeSupportBenefit',
                'AdaptationBenefits',
                'FamilyTransportAllowance',
                'HospitalCashAllowance',
                'LoanProtector',
                'AccidentHospitalization',
                'AccidentMedicalExpenses',
                'RepatriationAndFuneralExpenses',
                'BrokenBones'
            ];

            const updatedMember = { ...newMembers[index], [field]: value };
            subCovers.forEach(cover => {
                updatedMember[cover] = 'N';
            });
            newMembers[index] = updatedMember;
        } else {
            newMembers[index][field] = value;
            // Reset childEducationSupport if there are no children
            if (field === 'additionalCover' && value === 'Y') {
                const hasChildren = members.some(m => m.relation === 'SON' || m.relation === 'DAUGHTER');
                if (!hasChildren) {
                    newMembers[index].ChildEducationSupport = 'N';
                }
            }
        }

        setMembers(newMembers);
    };
    useEffect(() => {
        dispatch(getAllProducts());
        dispatch(fetchInsuranceCompanies());
        dispatch(getAllCustomer());
    }, [dispatch]);

    useEffect(() => {
        if (formData.main_product_id && formData.insurance_company_id) {
            dispatch(getMasterProductByMainProductAndInsuranceCompany({ mainProductId: formData.main_product_id, insuranceCompanyId: formData.insurance_company_id }));
        }
    }, [dispatch, formData.main_product_id, formData.insurance_company_id]);


    useEffect(() => {
        if (formData.product_master_id) {
            const selectedProduct = masterProducts.find(product => product.id === formData.product_master_id);

            if (selectedProduct) {
                dispatch(getSubProductByProductDetails({
                    mainProductId: formData.main_product_id,
                    insuranceCompanyId: formData.insurance_company_id,
                    productMasterId: formData.product_master_id
                }))
                    .then((response) => {
                        if (response?.payload) {
                            const coverOptions = response.payload
                                .filter(product => product.status === 1) // Filter only products with status 1
                                .map(product => ({
                                    value: product.id,
                                    label: product.sub_product_name
                                }));
                            setCoverTypeOptions(coverOptions);
                        } else {
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching cover types:', error);
                        setCoverTypeOptions([]);
                    });
            }
        } else {
        }
    }, [dispatch, formData.product_master_id, formData.main_product_id, formData.insurance_company_id, masterProducts]);


    useEffect(() => {
        if (formData.cover_type) {
            if (formData.cover_type === 'SUPREME' || formData.cover_type === 'ELITE') {
                setDeductableAmount([
                    { value: '50000', label: '50 Thousand' },
                    { value: '100000', label: '1 Lakh' },
                    { value: '200000', label: '2 Lakhs' },
                    { value: '300000', label: '3 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    { value: '3000000', label: '30 Lakhs' },
                    { value: '4000000', label: '40 Lakhs' },
                    { value: '5000000', label: '50 Lakhs' },
                    { value: '10000000', label: '1 Crore' },
                ]);
            }
        }
    }, [formData.cover_type]);

    // Handle customer selection
    const handleCustomerSelect = async (event, newValue) => {
        setSelectedCustomer(newValue);
        if (newValue) {
            await dispatch(getMemberByCustomerId(newValue.id));
            setFormData(prev => ({
                ...prev,
                customer_id: newValue.id,
            }));
            // Check if the customer has an address
            const hasAddress = await checkCustomerAddress(newValue.id);
            if (!hasAddress) {
                // Handle the case where the customer does not have an address
                toast.error("This customer does not have an address.");
                return;
            }
            const hasMember = await checkCustomerMember(newValue.id);
            // Clear the customer_id error when a customer is selected
            setErrors((prevErrors) => ({
                ...prevErrors,
                customer_id: undefined, // Clear the error for customer_id
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                customer_id: '',
            }));
        }
    };

    const checkCustomerAddress = async (customerId) => {
        const response = await dispatch(getCustomerAddressByCustomerId(customerId));
        return response?.payload?.current_pincode;
    };

    // Add this helper function near the top of the file with other helper functions
    const validateSuratAddress = async (customerId, productName, dispatch) => {
        // Get customer's address
        const response = await dispatch(getCustomerAddressByCustomerId(customerId));
        const customerAddress = response?.payload;

        // Determine which city to check based on used_address
        const cityToCheck = customerAddress?.used_address === 'current'
            ? parseInt(customerAddress?.current_city)  // Convert to number
            : parseInt(customerAddress?.permanent_city); // Convert to number

        if (!cityToCheck) {
            return {
                isValid: true,
                message: null
            };
        }
        //getLocationById
        // Fetch city details
        const cityResponse = await dispatch(getLocationById(cityToCheck));
        const cityName = cityResponse?.payload?.city?.toUpperCase();

        // Check if customer is from Surat for both products
        if (cityName === 'SURAT' &&
            (productName === 'FG HEALTH SURAKSHA' || productName === 'FG VARISHTHA BIMA')) {
            return {
                isValid: false,
                message: `Cannot proceed with ${productName} for customers in Surat.`
            };
        }

        return {
            isValid: true,
            message: null
        };
    };
    const checkCustomerMember = async (customerId) => {
        const response = await dispatch(getMemberByCustomerId(customerId));

        // Check if payload is an array and has members
        if (Array.isArray(response?.payload) && response.payload.length > 0) {
            // Example: Get the customer IDs or full names of all members
            const customerIds = response.payload.map(member => member.customer_id);
            const fullNames = response.payload.map(member => member.full_name);

            // Return the full array or specific data as needed
            return fullNames; // Or `customerIds` or any other processing
        } else {
            return null; // Or handle the empty case as required
        }
    };


    const getSumInsuredOptions = (coverType) => {
        // If it's PLATINUM, we need to check which product it belongs to
        if (coverType === 'PLATINUM') {
            if (formData.product_master_name === 'FG HEALTH SURAKSHA') {
                return [
                    { value: '600000', label: '6 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '800000', label: '8 Lakhs' },
                    { value: '900000', label: '9 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            } else if (formData.product_master_name === 'FG HEALTH ABSOLUTE') {
                return [
                    //{ value: '1500000', label: '15 Lakhs' },
                    //{ value: '2000000', label: '20 Lakhs' },
                    //{ value: '2500000', label: '25 Lakhs' },
                    // { value: '3000000', label: '30 Lakhs' },
                    // { value: '3500000', label: '35 Lakhs' }
                ];
            }
        }
        switch (coverType) {
            case 'GOLD':
                return [
                    // { value: '50000', label: '50 Thousand' },
                    // { value: '100000', label: '1 Lakh' },
                    // { value: '150000', label: '1.5 Lakhs' },
                    // { value: '200000', label: '2 Lakhs' },
                    // { value: '250000', label: '2.5 Lakhs' },
                    // { value: '300000', label: '3 Lakhs' },
                    // { value: '350000', label: '3.5 Lakhs' },
                    // { value: '400000', label: '4 Lakhs' },
                    // { value: '450000', label: '4.5 Lakhs' },
                    { value: '500000', label: '5 Lakhs' }
                ];
            /* case 'PLATINUM':
                return [
                    { value: '600000', label: '6 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '800000', label: '8 Lakhs' },
                    { value: '900000', label: '9 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ]; */
            case 'TOPAZ':
                return [
                    // { value: '100000', label: '1 Lakh' },
                    // { value: '200000', label: '2 Lakhs' },
                    // { value: '300000', label: '3 Lakhs' },
                    // { value: '400000', label: '4 Lakhs' },
                    { value: '500000', label: '5 Lakhs' }
                ];
            /*  case 'RUBY':
                 return [
                     { value: '600000', label: '6 Lakhs' },
                     { value: '750000', label: '7.5 Lakhs' },
                     { value: '1000000', label: '10 Lakhs' }
                 ]; */
            case 'VITAL':
                return [
                    //{ value: '300000', label: '3 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            /*  case 'SUPERIOR':
                 return [
                     { value: '1500000', label: '15 Lakhs' },
                     { value: '2000000', label: '20 Lakhs' },
                     { value: '2500000', label: '25 Lakhs' }
                 ]; */
            // case 'PREMIUM':
            //     return [
            //         { value: '5000000', label: '50 Lakhs' },
            //         { value: '10000000', label: '1 Crore' }
            //     ];
            case 'CLASSIC':
                return [
                    //  { value: '300000', label: '3 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' }
                ];
            /* case 'PLATINUM':
                return [
                    { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    // { value: '3000000', label: '30 Lakhs' },
                    // { value: '3500000', label: '35 Lakhs' }
                ]; */
            case 'VARISHTA BIMA':
                return [
                    // { value: '200000', label: '2 Lakhs' },
                    // { value: '300000', label: '3 Lakhs' },
                    // { value: '400000', label: '4 Lakhs' },
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: ' 7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                ];
            default:
                return [];
        }
    };
    const getDeductableAmountOptions = (member) => {
        // Calculate age from date of birth
        const age = calculateAge(member.date_of_birth);

        if (age <= 55) {
            // Day 1 - 55 years: Any deductible
            return [
                { value: '50000', label: '50 Thousand' },
                { value: '100000', label: '1 Lakh' },
                { value: '200000', label: '2 Lakhs' },
                { value: '300000', label: '3 Lakhs' },
                { value: '500000', label: '5 Lakhs' },
                { value: '750000', label: '7.5 Lakhs' },
                { value: '1000000', label: '10 Lakhs' },
                // { value: '1500000', label: '15 Lakhs' },
                // { value: '2000000', label: '20 Lakhs' },
                //{ value: '2500000', label: '25 Lakhs' },
                //{ value: '3000000', label: '30 Lakhs' },
                //{ value: '4000000', label: '40 Lakhs' }
            ];
        } else if (age <= 60) {
            // 56-60 years: Limited deductible options
            return [
                { value: '500000', label: '5 Lakhs' },
                { value: '750000', label: '7.5 Lakhs' },
                { value: '1000000', label: '10 Lakhs' },
                //{ value: '1500000', label: '15 Lakhs' },
                //{ value: '2000000', label: '20 Lakhs' },
                //{ value: '2500000', label: '25 Lakhs' },
                //{ value: '3000000', label: '30 Lakhs' },
                //{ value: '4000000', label: '40 Lakhs' }
            ];
        } else {
            // 61+ years: Most limited deductible options
            return [
                /*  { value: '1500000', label: '15 Lakhs' },
                 { value: '2000000', label: '20 Lakhs' },
                 { value: '3000000', label: '30 Lakhs' },
                 { value: '4000000', label: '40 Lakhs' } */
            ];
        }
    };

    const getSumInsuredByDeductibleAndAge = (deductible, member) => {
        const age = calculateAge(member.date_of_birth);

        // Internal function to get sum insured options based on deductible
        const getDeductibleBasedOptions = (deductibleAmount) => {
            const options = {
                50000: [
                    { value: '500000', label: '5 Lakhs' }
                ],
                100000: [
                    { value: '500000', label: '5 Lakhs' }
                ],
                200000: [
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    // { value: '1500000', label: '15 Lakhs' },
                    // { value: '2000000', label: '20 Lakhs' },
                    // { value: '2500000', label: '25 Lakhs' }
                ],
                300000: [
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    //{ value: '1500000', label: '15 Lakhs' },
                    //{ value: '2000000', label: '20 Lakhs' },
                    //{ value: '2500000', label: '25 Lakhs' }
                ],
                400000: [
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    //{ value: '1500000', label: '15 Lakhs' },
                    //{ value: '2000000', label: '20 Lakhs' },
                    //{ value: '2500000', label: '25 Lakhs' }
                ],
                500000: [
                    { value: '500000', label: '5 Lakhs' },
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    /* { value: '1500000', label: '15 Lakhs' },
                    { value: '2000000', label: '20 Lakhs' },
                    { value: '2500000', label: '25 Lakhs' },
                    { value: '3000000', label: '30 Lakhs' },
                    { value: '4000000', label: '40 Lakhs' },
                    { value: '5000000', label: '50 Lakhs' },
                    { value: '10000000', label: '1 Crore' } */
                ],
                750000: [
                    { value: '750000', label: '7.5 Lakhs' },
                    { value: '1000000', label: '10 Lakhs' },
                    /*  { value: '1500000', label: '15 Lakhs' },
                     { value: '2000000', label: '20 Lakhs' },
                     { value: '2500000', label: '25 Lakhs' },
                     { value: '3000000', label: '30 Lakhs' },
                     { value: '4000000', label: '40 Lakhs' },
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' } */
                ],
                1000000: [
                    { value: '1000000', label: '10 Lakhs' },
                    /*  { value: '1500000', label: '15 Lakhs' },
                     { value: '2000000', label: '20 Lakhs' },
                     { value: '2500000', label: '25 Lakhs' },
                     { value: '3000000', label: '30 Lakhs' },
                     { value: '4000000', label: '40 Lakhs' },
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' } */
                ],
                /*  1500000: [
                     { value: '1500000', label: '15 Lakhs' },
                     { value: '2000000', label: '20 Lakhs' },
                     { value: '2500000', label: '25 Lakhs' },
                     { value: '3000000', label: '30 Lakhs' },
                     { value: '4000000', label: '40 Lakhs' },
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' }
                 ],
                 2000000: [
                     { value: '2000000', label: '20 Lakhs' },
                     { value: '2500000', label: '25 Lakhs' },
                     { value: '3000000', label: '30 Lakhs' },
                     { value: '4000000', label: '40 Lakhs' },
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' }
                 ],
                 3000000: [
                     { value: '3000000', label: '30 Lakhs' },
                     { value: '4000000', label: '40 Lakhs' },
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' }
                 ],
                 4000000: [
                     { value: '4000000', label: '40 Lakhs' },
                     { value: '5000000', label: '50 Lakhs' },
                     { value: '10000000', label: '1 Crore' }
                 ] */
            };

            return options[deductibleAmount] || [];
        };

        // Age-based options
        const ageBasedOptions = {
            /*   above60: [
                  { value: '1500000', label: '15 Lakhs' },
                  { value: '2000000', label: '20 Lakhs' },
                  { value: '2500000', label: '25 Lakhs' },
                  { value: '3000000', label: '30 Lakhs' },
                  { value: '4000000', label: '40 Lakhs' },
                  { value: '5000000', label: '50 Lakhs' },
                  { value: '10000000', label: '1 Crore' }
              ], */
            between56And60: [
                { value: '500000', label: '5 Lakhs' },
                { value: '750000', label: '7.5 Lakhs' },
                { value: '1000000', label: '10 Lakhs' },
                /*  { value: '1500000', label: '15 Lakhs' },
                 { value: '2000000', label: '20 Lakhs' },
                 { value: '2500000', label: '25 Lakhs' },
                 { value: '3000000', label: '30 Lakhs' },
                 { value: '4000000', label: '40 Lakhs' },
                 { value: '5000000', label: '50 Lakhs' },
                 { value: '10000000', label: '1 Crore' } */
            ]
        };

        // Return options based on age
        if (age > 60) {
            return ageBasedOptions.above60;
        }

        if (age > 55 && age <= 60) {
            return ageBasedOptions.between56And60;
        }

        // For age <= 55, use deductible-based options
        return getDeductibleBasedOptions(Number(deductible));
    };

    const durationOptions = [
        { value: '1', label: '1 Year' },
        { value: '2', label: '2 Years' },
        { value: '3', label: '3 Years' }
    ];

    const handleInputChange = (e) => {
        const { name, value } = e.target;

        // Clear the specific error for the field being changed
        setErrors((prevErrors) => ({
            ...prevErrors,
            [name]: undefined, // Clear the error for the specific field
        }));
        if (name.startsWith('sum_insured_')) { // Check if the name is for a sum insured field
            setFormData(prev => ({
                ...prev,
                [name]: value // Update the specific sum insured field
            }));
        } else if (name === 'product_master_id') {
            const selectedProduct = masterProducts.find(product => product.id === value);
            setFormData(prev => ({
                ...prev,
                [name]: value,
                product_master_name: selectedProduct ? selectedProduct?.product_name : ''
            }));
        } else if (name === 'cover_type_id') {
            const selectedCover = coverTypeOptions.find(option => option.value === value);
            setFormData(prev => ({
                ...prev,
                cover_type_id: value,
                cover_type: selectedCover ? selectedCover.label : ''
            }));
        } else {

            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/quotations-list');
    };

    const formatDateForSQL = (date) => {
        if (!date) return null;
        const newDate = new Date(date);
        return newDate.toISOString().slice(0, 19).replace('T', ' '); // Convert to 'YYYY-MM-DD HH:MM:SS'
    };
    const formatDateOfBirth = (date) => {
        if (!date) return null;
        const newDate = new Date(date);
        const day = String(newDate.getDate()).padStart(2, '0');  // Ensure two digits for day
        const month = String(newDate.getMonth() + 1).padStart(2, '0');  // Get the month (0-based, so add 1)
        const year = newDate.getFullYear();

        return `${day}/${month}/${year}`;
    };

    const calculateBMI = (weightKg, heightCm) => {
        // Convert height from cm to meters
        const heightM = heightCm / 100;
        // Calculate BMI using the formula: weight (kg) / (height (m))²
        if (heightM > 0 && weightKg > 0) {
            const bmi = weightKg / (heightM * heightM);
            return Number(bmi.toFixed(2)); // Round to 2 decimal places
        }
        return 0;
    };

    // Add this function to calculate age
    const calculateAge = (dateOfBirth) => {
        if (!dateOfBirth) return 0;
        const dob = new Date(dateOfBirth);
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
            age--;
        }
        return age;
    };

    // Update the validateBMI function
    const validateBMI = (bmi, dateOfBirth) => {
        const age = calculateAge(dateOfBirth);

        // Skip BMI validation for members under 16
        if (age < 16) {
            return true;
        }

        // Update the maximum BMI for FG ADVANTAGE TOP UP
        if (formData.product_master_name === 'FG ADVANTAGE TOP UP') {
            return bmi >= 18 && bmi <= 34; // Changed max BMI to 34
        }

        // Apply BMI validation for members 16 and older
        return bmi >= 18 && bmi <= 32.1;
    };

    const formatCoverType = (coverType) => {
        if (!coverType) return '';
        // Convert to lowercase first, then capitalize first letter
        return coverType.charAt(0).toUpperCase() + coverType.slice(1).toLowerCase();
    };

    const handleCreateQuote = async (event) => {
        event.preventDefault();
        setLoading(true);

        // Add this validation for FG HEALTH ABSOLUTE children count
        if (formData.product_master_name === 'FG HEALTH ABSOLUTE') {
            const childrenCount = members.filter(member =>
                member.relation === 'SON' || member.relation === 'DAUGHTER'
            ).length;

            if (childrenCount > 3) {
                toast.error('FG HEALTH ABSOLUTE allows maximum of 3 children');
                setLoading(false);
                return;
            }
        }

        // Add this validation for FG ACCIDENT SURAKSHA
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            const selfMember = members.find(member => member.relation === 'SELF');
            const spouseMember = members.find(member => member.relation === 'SPOUSE');

            // Check minimum values for SELF member
            if (selfMember) {
                const adSumInsured = Number(selfMember?.ad_sum_insured || 0);
                const ppSumInsured = Number(selfMember?.pp_sum_insured || 0);
                const ptSumInsured = Number(selfMember?.pt_sum_insured || 0);
                const ttSumInsured = Number(selfMember?.tt_sum_insured || 0);

                if (adSumInsured < 50000) {
                    toast.error('AD sum insured must be at least ₹50,000 for SELF member.');
                    setLoading(false);
                    return;
                }
                if (ppSumInsured < 50000) {
                    toast.error('PP sum insured must be at least ₹50,000 for SELF member.');
                    setLoading(false);
                    return;
                }
                if (ptSumInsured < 50000) {
                    toast.error('PT sum insured must be at least ₹50,000 for SELF member.');
                    setLoading(false);
                    return;
                }
                // if (ttSumInsured < 10000) {
                //     toast.error('TTD sum insured must be at least ₹10,000 for SELF member.');
                //     setLoading(false);
                //     return;
                // }
            }

            // Check minimum values for SPOUSE member
            if (spouseMember && !['House Wife', 'Retired', 'Unemployed'].includes(spouseMember.occupation)) {
                const adSumInsured = Number(spouseMember?.ad_sum_insured || 0);
                const ppSumInsured = Number(spouseMember?.pp_sum_insured || 0);
                const ptSumInsured = Number(spouseMember?.pt_sum_insured || 0);
                const ttSumInsured = Number(spouseMember?.tt_sum_insured || 0);

                if (adSumInsured < 50000) {
                    toast.error('AD sum insured must be at least ₹50,000 for SPOUSE member.');
                    setLoading(false);
                    return;
                }
                if (ppSumInsured < 50000) {
                    toast.error('PP sum insured must be at least ₹50,000 for SPOUSE member.');
                    setLoading(false);
                    return;
                }
                if (ptSumInsured < 50000) {
                    toast.error('PT sum insured must be at least ₹50,000 for SPOUSE member.');
                    setLoading(false);
                    return;
                }
                // if (ttSumInsured < 10000) {
                //     toast.error('TTD sum insured must be at least ₹10,000 for SPOUSE member.');
                //     setLoading(false);
                //     return;
                // }
            }
        }

        // Check if customer exists in members array
        const customerExists = members.some(member => member.relation === 'SELF');
        // Add validation for Health Total married customer - only if customer exists as member
        if (customerExists &&
            (formData.product_master_name === 'FG HEALTH TOTAL' || formData.product_master_name === 'FG HEALTH SURAKSHA' || formData.product_master_name === 'FG VARISHTHA BIMA') &&
            selectedCustomer?.marital_status_id === 5) {

            const hasSpouse = members.some(member => member.relation === 'SPOUSE');
            if (!hasSpouse) {
                toast.error('For This Product married customers must include their spouse in the policy');
                setLoading(false);
                return;
            }
        }

        if (formData.family_type === 'floater') {
            const dependentRelations = ['FATHER', 'MOTHER', 'BROTHER', 'SISTER'];
            const invalidRelations = members.filter(member => dependentRelations.includes(member.relation));

            if (invalidRelations.length > 0) {
                toast.error("Dependent parents are not allowed in a this policy.");
                setLoading(false); // Stop loading
                return; // Exit the function if invalid relations are found
            }
        }

        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            const dependentRelations = ['FATHER', 'MOTHER'];
            const invalidRelations = members.filter(member => dependentRelations.includes(member.relation));

            if (invalidRelations.length > 0) {
                toast.error("Dependent parents are not allowed in FG ACCIDENT SURAKSHA.");
                setLoading(false); // Stop loading
                return; // Exit the function if invalid relations are found
            }
        }
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            const selfMember = members.find(member => member.relation === 'SELF');
            const spouseMember = members.find(member => member.relation === 'SPOUSE');
            const childrenCount = members.filter(member => member.relation === 'SON' || member.relation === 'DAUGHTER').length;
            const totalMembers = members.length;

            // Ensure 'SELF' is mandatory
            if (!selfMember) {
                toast.error("SELF is mandatory for FG ACCIDENT SURAKSHA.");
                setLoading(false);
                return;
            }

            // Maximum allowed members: 4
            if (totalMembers > 4) {
                toast.error("Maximum of 4 members (SELF, SPOUSE, and up to 2 children) are allowed.");
                setLoading(false);
                return;
            }

            // Maximum of 2 children allowed
            if (childrenCount > 2) {
                toast.error("A maximum of 2 children are allowed.");
                setLoading(false);
                return;
            }

            // Allow only valid combinations
            if (
                !(
                    totalMembers === 1 || // SELF only
                    (totalMembers === 2 && spouseMember) || // SELF + SPOUSE
                    (totalMembers === 2 && childrenCount === 1) || // SELF + 1 CHILD
                    (totalMembers === 3 && spouseMember && childrenCount === 1) || // SELF + SPOUSE + 1 CHILD
                    (totalMembers === 3 && childrenCount === 2) || // SELF + 2 CHILDREN
                    (totalMembers === 4 && spouseMember && childrenCount === 2) // SELF + SPOUSE + 2 CHILDREN
                )
            ) {
                toast.error("Invalid combination! Allowed options: SELF, SELF+SPOUSE, SELF+1 CHILD, SELF+2 CHILDREN, SELF+SPOUSE+1 or 2 CHILDREN.");
                setLoading(false);
                return;
            }
        }

        if (formData.family_type === 'floater' || formData.product_master_name === 'FG HEALTH SURSKSHA') {
            const dependentRelations = ['FATHER', 'MOTHER'];
            const invalidRelations = members.filter(member => dependentRelations.includes(member.relation));

            if (invalidRelations.length > 0) {
                toast.error("Dependent parents are not allowed in a floater policy.");
                setLoading(false); // Stop loading
                return; // Exit the function if invalid relations are found
            }

            // New validation for Family Floater - Self + Spouse + max 3 kids
            const selfMember = members.find(member => member.relation === 'SELF');
            const spouseMember = members.find(member => member.relation === 'SPOUSE');
            const childrenCount = members.filter(member => member.relation === 'SON' || member.relation === 'DAUGHTER').length;

            if (!selfMember) {
                toast.error("Family Floater must include a Self member.");
                setLoading(false);
                return;
            }

            if (!spouseMember) {
                toast.error("Family Floater must include a Spouse member.");
                setLoading(false);
                return;
            }

            if (childrenCount > 3) {
                toast.error("Family Floater can include a maximum of 3 children.");
                setLoading(false);
                return;
            }
        }
        if (formData.family_type === 'individual' && formData.product_master_name === 'FG HEALTH SURAKSHA') {
            const selfMember = members.find(member => member.relation === 'SELF');
            const spouseMember = members.find(member => member.relation === 'SPOUSE');
            const childrenCount = members.filter(member => member.relation === 'SON' || member.relation === 'DAUGHTER').length;
            const parentCount = members.filter(member => member.relation === 'FATHER' || member.relation === 'MOTHER').length;

            if (!selfMember) {
                toast.error("Individual policy must include a Self member.");
                setLoading(false);
                return;
            }

            // if (!spouseMember) {
            //     toast.error("Individual policy must include a Spouse member.");
            //     setLoading(false);
            //     return;
            // }

            if (parentCount > 2) {
                toast.error("Individual policy can include a maximum of 2 parents.");
                setLoading(false);
                return;
            }

            if (parentCount === 2 && childrenCount > 2) {
                toast.error("If dependent parents are included, only 2 children are allowed.");
                setLoading(false);
                return;
            }

            if (parentCount === 0 && childrenCount > 4) {
                toast.error("If no dependent parents are included, a maximum of 4 children are allowed.");
                setLoading(false);
                return;
            }
        }


        // Add this validation in the handleCreateQuote function
        if (formData.product_master_name) {
            const selfMember = members.find(member => member.relation === 'SELF');
            const dependentMembers = members.filter(member =>
                ['SPOUSE', 'SON', 'DAUGHTER', 'FATHER', 'MOTHER'].includes(member.relation)
            );

            if (selfMember && dependentMembers.length > 0) {
                const selfSumInsured = Number(formData.sum_insured[members.indexOf(selfMember)] || 0);

                // Check each dependent member's sum insured
                for (const dependentMember of dependentMembers) {
                    const dependentSumInsured = Number(formData.sum_insured[members.indexOf(dependentMember)] || 0);

                    if (dependentSumInsured > selfSumInsured) {
                        const relation = dependentMember.relation.toLowerCase();
                        toast.error(`The sum insured for the ${relation} must be less than or equal to that of the self member.`);
                        setLoading(false);
                        return;
                    }
                }
            }
        }


        // // New validation for FG VARISHTHA BIMA and preExistingDisease
        // if (formData.product_master_name === 'FG VARISHTHA BIMA' && selectedCustomer?.preExistingDisease === 'Y') {
        //     toast.error("Cannot proceed with Varishta Bima as the customer has a pre-existing disease.");
        //     setLoading(false);
        //     return; // Exit the function if the condition is met
        // }
        // New validation for FG VARISHTHA BIMA and preExistingDisease
        if (formData.product_master_name === 'FG VARISHTHA BIMA') {
            const hasPreExistingDisease = members.some(member => member.preExistingDisease === 'Y');
            if (hasPreExistingDisease || selectedCustomer?.preExistingDisease === 'Y') {
                toast.error("One of the Member(s) has a pre-existing condition and is ineligible for online issuance of the FG VARISHTHA BIMA policy. Please visit your nearest branch for assistance.");
                setLoading(false);
                return; // Exit the function if the condition is met
            }
        }


        const hasPreExistingDisease = members.some(member => member.preExistingDisease === 'Y');
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA' && (hasPreExistingDisease || selectedCustomer?.preExistingDisease === 'Y')) {
            toast.error("One of the Member(s) has a pre-existing condition and is ineligible for online issuance of the FG PA policy. Please visit your nearest branch for assistance..");
            setLoading(false);
            return; // Exit the function if the condition is met
        }

        // const hasPreExistingDisease = members.some(member => member.preExistingDisease === 'Y');
        // const membersWithDisease = members
        //     .filter(member => member.preExistingDisease === 'Y')
        //     .map(member => {
        //         // Check if the member is the self member and return the appropriate name
        //         if (member.relation === 'SELF') {
        //             return `${member.first_name} (SELF)`; // Indicate that this is the self member
        //         }
        //         return member.full_name; // Use full_name for other members
        //     });

        // // Check if the selected customer has a pre-existing disease
        // const customerHasPreExistingDisease = selectedCustomer?.preExistingDisease === 'Y';

        // // Combine the messages for members and customer
        // if (formData.product_master_name === 'FG ACCIDENT SURAKSHA' && (hasPreExistingDisease || customerHasPreExistingDisease)) {
        //     let errorMessage = "";

        //     // Check if there are members with pre-existing conditions
        //     if (membersWithDisease.length > 0) {
        //         errorMessage += "One or more members have a pre-existing condition and are ineligible for online issuance of the FG PA policy. Please visit your nearest branch for assistance.";
        //     }

        //     // Check if the customer has a pre-existing disease
        //     if (customerHasPreExistingDisease) {
        //         if (errorMessage) {
        //             errorMessage += " Additionally, the customer has a pre-existing disease.";
        //         } else {
        //             errorMessage += "The customer has a pre-existing disease.";
        //         }
        //     }

        //     // Only show the toast if there is an error message
        //     if (errorMessage) {
        //         toast.error(errorMessage);
        //         setLoading(false);
        //         return; // Exit the function if the condition is met
        //     }
        // }

        const maritalStatusMap = {
            1: 'Single',
            2: 'Married',
            3: 'Divorced',
            4: 'Widow',
            // Add other statuses as needed
        };
        // New validation for FG VARISHTHA BIMA
        const maritalStatus = maritalStatusMap[selectedCustomer?.marital_status_id]; // Get the marital status string
        if (
            ['Single', 'Divorced', 'Widow'].includes(maritalStatus)) {
            const hasSpouse = members.some(member => member.relation === 'SPOUSE');
            if (hasSpouse) {
                toast.error("Spouse cannot be added for Single, Divorced, or Widow customers.");
                setLoading(false);
                return;
            }
        }
        // Add risk class validation
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            // Check customer's occupation risk class
            const customerOccupation = selectedCustomer?.occupation;
            const occupationRiskClass = riskClass.find(risk => risk.pick_list_id === customerOccupation)?.risk_class;

            if (occupationRiskClass === 3) {
                toast.error("Cannot proceed with risk class 3 occupations for FG ACCIDENT SURAKSHA");
                setLoading(false);
                return;
            }

            // Also check members' occupations
            const membersWithRiskClass3 = members.filter(member => {
                const memberOccupationId = educationOptions.find(edu => edu.label_name === member.occupation)?.id;
                const memberRiskClass = riskClass.find(risk => risk.pick_list_id === memberOccupationId)?.risk_class;
                return memberRiskClass === 3;
            });

            if (membersWithRiskClass3.length > 0) {
                toast.error("Cannot proceed with members having risk class 3 occupations for FG ACCIDENT SURAKSHA");
                setLoading(false);
                return;
            }
        }

        if (formData.product_master_name === 'FG VARISHTHA BIMA' || formData.product_master_name === 'FG HEALTH SURAKSHA') {
            const invalidSumInsured = formData.sum_insured.some(amount => parseInt(amount) < 500000);
            if (invalidSumInsured) {
                toast.error('Minimum sum insured should be 5 Lakhs');
                setLoading(false);
                return;
            }
        }
        // Add age validation check specifically for children first
        const invalidAgeChildren = members.filter(member =>
            (member.relation === 'SON' || member.relation === 'DAUGHTER') &&
            calculateAge(member.date_of_birth) > 25
        );

        if (invalidAgeChildren.length > 0) {
            const childName = invalidAgeChildren[0].full_name;
            const childAge = calculateAge(invalidAgeChildren[0].date_of_birth);
            toast.error(`${childName}: Age ${childAge} exceeds maximum allowed age of 25 years for children`);
            setLoading(false);
            return;
        }

        // Then check other age validations
        const invalidAgeMembers = members.filter(member =>
            !validateAge(member.date_of_birth, formData.product_master_name, member.relation)
        );

        if (invalidAgeMembers.length > 0 && formData.product_master_name === 'FG VARISHTHA BIMA') {
            toast.error('One or more members must be 60 years or above for FG VARISHTHA BIMA');
            setLoading(false);
            return;
        }
        if (invalidAgeMembers.length > 0 && (formData.product_master_name === 'FG HEALTH TOTAL' || formData.product_master_name === 'FG HEALTH ABSOLUTE' || formData.product_master_name === 'FG ADVANTAGE TOP UP')) {
            toast.error('All members must be 50 years or below');
            setLoading(false);
            return;
        }

        if (invalidAgeMembers.length > 0 && formData.product_master_name === 'FG HEALTH SURAKSHA') {
            const ageInDays = calculateAgeInDays(invalidAgeChildren?.[0]?.date_of_birth)
            if (ageInDays < 90) {
                toast.error('Member age must be above 90 days for this product');
            } else {
                toast.error('Member age must be 50 years or below for this product');
            }
            setLoading(false);
            return;
        }

        // Add validation for minimum members in floater type
        if (formData.family_type === 'floater' && members.length < 2) {
            toast.error('Family Floater requires at least 2 members');
            setLoading(false);
            return;
        }

        // Add this validation for Sports Person occupation
        const selfMember = members.find(member => member.relation === 'SELF');
        if (selfMember && selfMember.occupation === 'Sports Person' && formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            toast.error("Occupation 'Sports Person': This policy cannot be issued online. Contact 1800220233 or the nearest branch.");
            setLoading(false);
            return; // Exit the function if the condition is met
        }


        // Add BMI validation
        const invalidBMIMembers = members.filter(member => {
            const age = calculateAge(member.date_of_birth);
            if (age < 16) return false; // Skip BMI validation for members under 16

            const bmi = calculateBMI(member.weight, member.height);
            return bmi && !validateBMI(bmi, member.date_of_birth);
        });

        if (invalidBMIMembers.length > 0) {
            toast.error('One or more members (16 years or older) have BMI outside the acceptable range (18-30)');
            setLoading(false);
            return;
        }

        // Validate Surat address
        const addressValidation = await validateSuratAddress(formData.customer_id, formData.product_master_name, dispatch);
        if (!addressValidation.isValid) {
            toast.error(addressValidation.message);
            setLoading(false);
            return;
        }
        // Check if the selected customer has an address
        const hasAddress = await checkCustomerAddress(formData.customer_id);
        if (!hasAddress && formData.customer_id) {
            toast.error("The selected customer does not have an address. Please update their address before proceeding.");
            setLoading(false);
            return;
        }




        const hasMember = await checkCustomerMember(formData.customer_id);
        if (!hasMember && formData.family_type === 'floater') {
            toast.error("Please add at least two members before proceeding with Floater.");
            setLoading(false);
            return;
        }

        const invalidMembers = members.filter((member, index) => {
            const adSumInsured = Number(member.ad_sum_insured || 0);
            const ppSumInsured = Number(member.pp_sum_insured || 0);
            const ptSumInsured = Number(member.pt_sum_insured || 0);
            const ttSumInsured = Number(member.tt_sum_insured || 0);

            let errorMessage = "";

            // Apply validations only for SELF and SPOUSE with specific occupations
            if ((member.relation === 'SELF' || member.relation === 'SPOUSE') &&
                (member.occupation === 'House Wife' || member.occupation === 'Unemployed' || member.occupation === 'Retired')) {
                if (adSumInsured < Math.max(ppSumInsured, ptSumInsured, ttSumInsured)) {
                    errorMessage = 'AD sum insured must be greater than or equal to PP, PT, and TTD sum insured';
                } else if (adSumInsured < 50000) {
                    errorMessage = 'AD sum insured must be at least ₹50,000';
                } else if (ptSumInsured < 50000) {
                    errorMessage = 'PT sum insured must be at least ₹50,000';
                } else if (ppSumInsured < 50000) {
                    errorMessage = 'PP sum insured must be at least ₹50,000';
                } else if (ttSumInsured < 10000) {
                    errorMessage = 'TTD sum insured must be at least ₹10,000';
                }
            }

            // Skip validations for SON and DAUGHTER below 25 years
            if ((member.relation === 'SON' || member.relation === 'DAUGHTER') && calculateAge(member.date_of_birth) < 25) {
                return false; // Skip validation for these members
            }

            if (errorMessage) {
                toast.error(errorMessage);
                return true; // Member is invalid
            }
            return false; // Member is valid
        });

        if (invalidMembers.length > 0) {
            setLoading(false);
            return;
        }

        // Add this validation for FG ACCIDENT SURAKSHA
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            const invalidAgeMembers = members.filter(member =>
                calculateAge(member.date_of_birth) < 3
            );

            if (invalidAgeMembers.length > 0) {
                const childName = invalidAgeMembers[0].full_name;
                toast.error(`${childName} must be at least 3 years old for FG ACCIDENT SURAKSHA.`);
                setLoading(false);
                return;
            }
        }
        if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            const invalidAgeMembers = members.filter(member =>
                calculateAge(member.date_of_birth) > 50 // Check if age exceeds 70
            );

            if (invalidAgeMembers.length > 0) {
                const childName = invalidAgeMembers[0].full_name;
                toast.error(`${childName} must be below 50 years old for FG ACCIDENT SURAKSHA.`); // Show toast notification
                setLoading(false);
                return;
            }
        }

        // const members = getAllMembersWithCustomer(); // Get all members associated with the quotation
        // Determine major class based on selected product name
        let majorClass;
        let contractType;
        let policyType;
        switch (formData.product_master_name) {
            case 'FG ADVANTAGE TOP UP':
                //  majorClass = 'FAT';
                //  contractType = 'FAT';
                policyType = formData.family_type === 'individual' ? 'HTI' : 'HTF';
                break;
            case 'FG HEALTH ABSOLUTE':
                //  majorClass = 'FHA';
                //  contractType = 'FHA';
                policyType = formData.family_type === 'individual' ? 'HAI' : 'HAF';
                break;
            case 'FG HEALTH TOTAL':
                //  majorClass = 'HTO';
                // contractType = 'HTO';
                policyType = formData.family_type === 'individual' ? 'HTI' : 'HTF';
                break;
            case 'FG HEALTH SURAKSHA':
                //   majorClass = 'FHR';
                //   contractType = 'FHR';
                policyType = formData.family_type === 'individual' ? 'FHI' : 'FHF';
                break;
            case 'FG VARISHTHA BIMA':
                //  majorClass = 'FVB';
                //  contractType = 'FVB';
                policyType = formData.family_type === 'individual' ? 'VBI' : 'VBF';
                break;
            default:

            //   majorClass = 'UNKNOWN'; // Default value if no match
        }
        const generateQuotationNumber = () => {
            // Generate a unique identifier (UUID) or use a shorter timestamp
            const uniqueId = Math.floor(Math.random() * 1000000); // Generates a number between 0 and 999999
            return `QTN-${uniqueId}`; // Quotation number in the format QTN-234234
        };

        // Prepare quotation data
        const quotationData = {
            quotation_number: generateQuotationNumber(),

            // quotation_number: `QTN-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            // quotation_number: `QTN-${Math.floor(Math.random() * 100000)}`, // Generate a unique quotation number
            productMasterId: formData.product_master_name,
            product: formData.product_master_id,
            // .toLowerCase()
            // .split(' ')
            // .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            // .join(''),
            insurance_company: formData.insurance_company_id,
            main_product: formData.main_product_id,
            //  venderCode: "webagg",
            //  majorClass: majorClass,
            //  contractType: contractType,
            //  policyIssueType: "I",
            policyType: policyType,
            //  paymentType: "CC",
            customer_id: formData.customer_id,
            agent_id: formData.agent_id,
            status: "PENDING",
            Created_by: formData.created_by,
            duration: formData.duration || 0,
            copay: formData.product_master_name === 'FG VARISHTHA BIMA' ? formData.copay : 'N',

        };

        try {


            // Use the current state of members directly
            const membersData = members.map((member, index) => ({
                member_id_no: index + 1,
                member_id: member.id,
                insuredName: member.full_name || 'N/A',
                insuredDob: formatDateForSQL(member.date_of_birth),
                coverType: formatCoverType(formData.cover_type) || 'unknown',
                cover_type_id: formData.cover_type_id || '',
                sumInsured: formData.sum_insured[index] ?? 0,
                deductableDiscount: formData.product_master_name === "FG ADVANTAGE TOP UP"
                    ? (formData.family_type === "floater"
                        ? formData.deductible ?? 0
                        : member.deductible ?? 0)
                    : 0,
                relation: member.relation ?? 'Self',
                status: "Active",
                Created_by: formData.created_by,
                insuredWeight: member.weight,
                insuredHeight: member.height,
                isSmoking: member.isSmoking,
                isTobacco: member.isTobacco,
                occupation: member.occupation,
                annual_income: member.annual_income || 0,
                ad_sum_insured: member.ad_sum_insured || 0,
                pt_sum_insured: member.pt_sum_insured || 0,
                pp_sum_insured: member.pp_sum_insured || 0,
                tt_sum_insured: member.tt_sum_insured || 0,

                // Add additional cover fields
                ...(formData.product_master_name === 'FG ACCIDENT SURAKSHA' && member.relation === 'SELF' ? {
                    additionalCover: member.additionalCover || 'Y',
                    // Only include childEducationSupport if there are children
                    ...(members.some(m => m.relation === 'SON' || m.relation === 'DAUGHTER') && {
                        childEducationSupport: member.childEducationSupport || 'N'
                    }),
                    lifeSupportBenefit: member.lifeSupportBenefit || 'N',
                    adaptationBenefits: member.adaptationBenefits || 'N',
                    familyTransportAllowance: member.familyTransportAllowance || 'N',
                    hospitalCashAllowance: member.hospitalCashAllowance || 'N',
                    loanProtector: member.loanProtector || 'N',
                    accidentHospitalization: member.accidentHospitalization || 'N',
                    accidentMedicalExpenses: member.accidentMedicalExpenses || 'N',
                    repatriationAndFuneralExpenses: member.repatriationAndFuneralExpenses || 'Y',
                    brokenBones: member.brokenBones || 'N'
                } : {})
            }));

            // Filter out any members that may not have valid data
            const filteredMembersData = membersData.filter(member => member.insuredName && member.insuredDob);

            // Dispatch the action to create the quotation and members
            const response = await dispatch(createQuotationWithMembers({ quotationData, membersData }));
            // Check if there's an error in the response
            if (response.error) {
                // Extract the error message from the response
                const errorMessage = response.error.message ||
                    response.error.response?.data?.message ||
                    'An error occurred while creating the quotation';

                // Show error in toast
                toast.error(errorMessage);
                setLoading(false);
                return;
            }
            if (response?.payload?.quotation_id) {
                let tableSource;
                if (formData.product_master_name === 'FG ACCIDENT SURAKSHA') {
                    tableSource = 'pa_quotations';
                } else {
                    tableSource = 'quotations';
                }
                navigate(`/dashboard/quotation-overview/${tableSource}/${response.payload.quotation_id}`);
            }
        } catch (error) {
            // Fallback error handling
            const errorMessage = error.message || 'An error occurred while creating the quotation';
            toast.error(errorMessage);
        } finally {
            setLoading(false);
        }
    };
    // Add this new function to calculate age in days
    const calculateAgeInDays = (dateOfBirth) => {
        if (!dateOfBirth) return 0;
        const dob = new Date(dateOfBirth);
        const today = new Date();
        const diffTime = Math.abs(today - dob);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
    };
    const validateAge = (dateOfBirth, productName, relation) => {
        const age = calculateAge(dateOfBirth);
        const ageInDays = calculateAgeInDays(dateOfBirth);


        const productsWithAgeLimit = [

            'FG HEALTH TOTAL',
            'FG HEALTH ABSOLUTE',
            'FG ADVANTAGE TOP UP'
        ];

        // Add validation for sons and daughters
        if ((relation === 'SON' || relation === 'DAUGHTER') && age > 25) {
            return false;
        }

        // Add specific validation for FG ACCIDENT SURAKSHA
        if (productName === 'FG ACCIDENT SURAKSHA') {
            return age >= 3 && age <= 50;
        }

        if (productName === 'FG HEALTH SURAKSHA') {
            return age <= 50 && ageInDays >= 90;
        }
        // Special handling for FG VARISHTHA BIMA
        if (productName === 'FG VARISHTHA BIMA') {
            // For individual type, everyone needs to be 60+
            if (formData.family_type === 'individual') {
                return age >= 60;
            }
            // For floater type, SELF needs to be 60+ and others need to be 25+
            if (formData.family_type === 'floater') {
                if (relation === 'SELF') {
                    return age >= 60;
                }
                return age >= 25;
            }
        }

        // For other products with age limit
        if (productsWithAgeLimit.includes(productName)) {
            return age <= 50;
        }
        return true;  // Return true for FG ADVANTAGE TOP UP and any other products
    };

    const handleRadioChange = (event) => {

        setFormData(prevState => ({
            ...prevState,
            family_type: event.target.value
        }));
        // Check for floater selection

    };

    const formatDate = (date) => {
        if (!date) return '';
        return new Date(date).toLocaleString();
    };

    // Add this handler for the Add Customer button
    const handleAddCustomer = () => {
        navigate('/dashboard/customer-personal-information');
    };

    // Add useEffect for floater type validation
    useEffect(() => {
        if (formData.family_type === 'floater' && formData.product_master_name === 'FG ADVANTAGE TOP UP') {
            // Find the oldest member's age
            const oldestMemberAge = Math.max(...members.map(member => calculateAge(member.date_of_birth)));

            // Get valid deductible options for the oldest member
            const validDeductibleOptions = getDeductableAmountOptions({ date_of_birth: members.find(m => calculateAge(m.date_of_birth) === oldestMemberAge)?.date_of_birth });

            // Check if current deductible is valid for the age group
            if (formData.deductible && !validDeductibleOptions.some(option => option.value === formData.deductible)) {
                // If invalid, select the first valid option
                const defaultValue = validDeductibleOptions[0]?.value || '';
                setFormData(prev => ({
                    ...prev,
                    deductible: defaultValue
                }));
            }
        }
    }, [members, formData.family_type, formData.product_master_name]);

    return (
        <Box sx={{ padding: '20px 40px' }}>
            <BarLoader loading={loading} />
            {loading ? null : (
                <form onSubmit={handleCreateQuote}>
                    <Grid container spacing={2}>
                        <Grid container spacing={2}>
                            {/* Header Row */}
                            <Grid item xs={12} md={8} style={{ display: 'flex', alignItems: 'center' }}>
                                <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <ModuleName moduleName="Quotation" pageName="Create" />

                                </Box>
                            </Grid>

                            {/* Buttons Row */}
                            <Grid item xs={12} md={4} sx={{
                                display: 'flex',
                                justifyContent: { xs: 'center', md: 'flex-end' },
                                gap: 2,
                                marginTop: 3
                            }}>
                                <Button
                                    type="submit"
                                    variant="outlined"
                                    sx={{
                                        color: 'green',
                                        borderColor: 'green',
                                        textTransform: 'none'
                                    }}
                                //  onClick={handleCreateQuote}
                                >
                                    Create Quote
                                </Button>
                                <Button
                                    variant="outlined"
                                    sx={{
                                        color: 'red',
                                        borderColor: 'red',
                                        textTransform: 'none'
                                    }}
                                    onClick={handleCancel}
                                >
                                    Cancel
                                </Button>
                            </Grid>

                            {/* Sections Row */}
                            {/* <Grid container>
                                <CustomSection titles={['Overview', 'Quotation']} page='quotations' />
                            </Grid> */}

                            {/* Updated Customer Search Section */}
                            <Grid
                                item
                                xs={12}
                                container
                                spacing={2}
                                justifyContent="center"
                                alignItems="center"
                                sx={{ mt: 2 }}
                            >
                                <Grid item xs={12} md={3} >
                                    <Autocomplete
                                        options={filteredCustomers}
                                        getOptionLabel={(option) =>
                                            `${option.first_name} ${option.last_name} - ${option.mobile}`
                                        }
                                        renderOption={(props, option) => (
                                            <li {...props}>
                                                <Box item xs={12} md={4}>
                                                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                                                        {option.first_name} {option.last_name}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        Mobile: {option.mobile}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        Email: {option.email}
                                                    </Typography>
                                                </Box>
                                            </li>
                                        )}
                                        renderInput={(params) => (
                                            <CustomTextField
                                                {...params}
                                                label="Search Customer"
                                                isRequired
                                                fullWidth
                                                error={!!errors.customer_id}
                                                helperText={errors.customer_id}
                                                InputProps={{
                                                    ...params.InputProps,
                                                    endAdornment: (
                                                        <>
                                                            {loading && (
                                                                <CircularProgress
                                                                    color="inherit"
                                                                    size={20}
                                                                    sx={{ mr: 2 }}
                                                                />
                                                            )}
                                                            {params.InputProps.endAdornment}
                                                        </>
                                                    ),
                                                }}
                                            // helperText={
                                            //     searchTerm && searchTerm.length < 2
                                            //         ? "Enter at least 2 characters to search"
                                            //         : ""
                                            // }
                                            />
                                        )}
                                        value={selectedCustomer}
                                        onChange={handleCustomerSelect}
                                        onInputChange={handleSearchInputChange}
                                        isOptionEqualToValue={(option, value) =>
                                            option?.id === value?.id
                                        }
                                        noOptionsText={
                                            searchTerm.length < 2
                                                ? "Enter at least 2 characters to search"
                                                : "No customers found"
                                        }
                                        filterOptions={(x) => x} // Disable built-in filtering
                                        loading={loading}
                                        loadingText="Searching..."

                                    />
                                </Grid>
                                <Grid item>
                                    <Button
                                        variant="outlined"
                                        sx={{
                                            color: 'green',
                                            borderColor: 'green',
                                            textTransform: 'none'
                                        }}
                                        onClick={handleAddCustomer}
                                    >
                                        Add Customer
                                    </Button>
                                </Grid>
                            </Grid>

                            {/* Selected Customer Details */}
                            {selectedCustomer && (
                                <Grid item xs={12} container spacing={2} justifyContent="center">
                                    <Grid item xs={12} md={3}>
                                        <Box sx={{
                                            p: 2,
                                            border: '1px solid #e0e0e0',
                                            borderRadius: 1,
                                            mt: 1,
                                            backgroundColor: '#f5f5f5'
                                        }}>
                                            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                                                Selected Customer Details
                                            </Typography>
                                            <Typography variant="body2">
                                                <strong>Name:</strong> {selectedCustomer.first_name} {selectedCustomer.last_name}
                                            </Typography>
                                            <Typography variant="body2">
                                                <strong>Mobile:</strong> {selectedCustomer.mobile}
                                            </Typography>
                                            <Typography variant="body2">
                                                <strong>Email:</strong> {selectedCustomer.email}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                </Grid>
                            )}

                            {/* Family Type Section - Add left padding */}
                            <Grid item xs={12} sx={{ pl: 4 }}>
                                <Box sx={{
                                    padding: '10px 20px',
                                    height: '60px',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    lineHeight: '27px',
                                    color: '#4C5157'
                                }}>
                                    <h5>Family Type</h5>
                                </Box>
                                <hr />
                            </Grid>

                            <Grid item xs={12} sx={{ pl: 4 }}>
                                <FormControl
                                >
                                    <RadioGroup
                                        row
                                        name="family_type"
                                        value={formData.family_type}
                                        onChange={handleRadioChange}
                                        required

                                    >
                                        <FormControlLabel value="individual" control={<Radio />} label="Individual" />
                                        <FormControlLabel value="floater" control={<Radio />} label="Floater" disabled={formData.product_master_name === 'FG ACCIDENT SURAKSHA'} />
                                    </RadioGroup>
                                    {formData.family_type === 'floater' && members.length < 2 && (
                                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                                            Family Floater requires at least 2 members
                                        </Typography>
                                    )}
                                </FormControl>
                            </Grid>

                            {/* Company Section - Add left padding */}
                            <Grid item xs={12} sx={{ pl: 4, pb: 3 }}>
                                <Box sx={{
                                    padding: '10px 20px',
                                    height: '60px',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    lineHeight: '27px',
                                    color: '#4C5157'
                                }}>
                                    <h5>Company Details</h5>
                                </Box>
                                <hr />
                            </Grid>

                            {/* Company Details Fields - Add left padding */}
                            <Grid container spacing={2} sx={{ pl: 4 }}>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Insurance Company"
                                        name="insurance_company_id"
                                        value={formData.insurance_company_id}
                                        options={insuranceCompanies
                                            .filter(company => company.status === 1)
                                            .map(company => ({
                                                label: company.insurance_company_name,
                                                value: company.id
                                            }))
                                        }
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                        error={!!errors.insurance_company_id}
                                        helperText={errors.insurance_company_id}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Main Product"
                                        name="main_product_id"
                                        value={formData.main_product_id}
                                        options={mainProducts
                                            .filter(product => product.status === 1)
                                            .map(product => ({
                                                label: product.main_product,
                                                value: product.id
                                            }))
                                        }
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                        error={!!errors.main_product_id}
                                        helperText={errors.main_product_id}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Product Type"
                                        name="product_master_id"
                                        value={formData.product_master_id}
                                        options={masterProducts
                                            .filter(product => product.status === 1)
                                            .map(product => ({
                                                label: product.product_name,
                                                value: product.id
                                            }))
                                        }
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                        error={!!errors.product_master_id}
                                        helperText={errors.product_master_id}
                                    />
                                </Grid>
                            </Grid>

                            {/* Plan Type Section - Add left padding */}
                            <Grid item xs={12} sx={{ pl: 4, pb: 3 }}>
                                <Box sx={{
                                    padding: '10px 20px',
                                    height: '60px',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    lineHeight: '27px',
                                    color: '#4C5157'
                                }}>
                                    <h5>Plan Type</h5>
                                </Box>
                                <hr />
                            </Grid>

                            {/* Plan Type Fields - Add left padding */}
                            <Grid container spacing={2} sx={{ pl: 4 }}>
                                <Grid item xs={12} md={3}>
                                    <Dropdown
                                        label="Cover Type"
                                        name="cover_type_id"
                                        value={formData.cover_type_id}
                                        options={coverTypeOptions}
                                        onChange={handleInputChange}
                                        required
                                        fullWidth
                                        error={!!errors.cover_type}
                                        helperText={errors.cover_type}
                                    />
                                </Grid>
                                {
                                    formData.family_type !== 'individual' && formData.product_master_name === 'FG ADVANTAGE TOP UP' && (formData.cover_type === 'SUPREME' || formData.cover_type === 'ELITE') && (
                                        <Grid item xs={12} md={3}>
                                            <Dropdown
                                                label="Deductible"
                                                name="deductible"
                                                value={formData.deductible}
                                                options={deductableAmount}
                                                onChange={handleInputChange}
                                                // error={!!errors.cover_type}
                                                // helperText={errors.cover_type}
                                                required
                                                fullWidth
                                            // error={!!errors.deductible}
                                            // helperText={errors.deductible}
                                            />
                                        </Grid>
                                    )
                                }

                                {/*  <Grid item xs={4}>
                            <Dropdown
                                label="Sum Insured"
                                name="sum_insured"
                                value={formData.sum_insured}
                                options={getSumInsuredOptions(formData.cover_type)}
                                onChange={handleInputChange}
                                required
                                disabled={!formData.cover_type}
                                fullWidth
                            />
                        </Grid> */}
                                {formData.product_master_name !== 'FG ACCIDENT SURAKSHA' && (
                                    <Grid item xs={12} md={3}>
                                        <Dropdown
                                            label="Duration"
                                            name="duration"
                                            value={formData.duration}
                                            options={durationOptions}
                                            onChange={handleInputChange}
                                            required
                                            fullWidth
                                            error={!!errors.duration}
                                            helperText={errors.duration}
                                        />
                                    </Grid>
                                )}

                                {formData.product_master_name === 'FG VARISHTHA BIMA' && (
                                    <Grid item xs={12} md={3}>
                                        <FormControl required >
                                            <Typography variant="body1" sx={{ mb: 1 }}>Copay</Typography>
                                            <RadioGroup
                                                row
                                                name="copay"
                                                value={formData.copay}
                                                onChange={handleInputChange}
                                                required
                                            >
                                                <FormControlLabel
                                                    value="Y"
                                                    control={<Radio />}
                                                    label="Yes"
                                                />
                                                <FormControlLabel
                                                    value="N"
                                                    control={<Radio />}
                                                    label="No"
                                                />
                                            </RadioGroup>

                                        </FormControl>
                                    </Grid>
                                )}
                            </Grid>
                            {/* Age Group Details - Always visible */}
                            <Grid item xs={12} sx={{ pl: 4, pb: 3 }}>
                                <Box sx={{
                                    padding: '10px 20px',
                                    height: '60px',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    lineHeight: '27px',
                                    color: '#4C5157'
                                }}>
                                    <h5>Member Details</h5>
                                </Box>
                                <hr />
                            </Grid>

                            {/* Member details shown only when customer is selected */}
                            {members.map((member, index) => (
                                <>
                                    <Grid
                                        item
                                        xs={12}
                                        container
                                        spacing={2}
                                        key={index}
                                    >
                                        <Grid item xs={12} sm={11}>
                                            <Card style={{
                                                padding: '16px',
                                                marginBottom: '16px',
                                                backgroundColor: '#f9f9f9',
                                                border: (
                                                    (formData.product_master_name === 'FG HEALTH TOTAL' &&
                                                        selectedCustomer?.marital_status === 'MARRIED') &&
                                                    !members.some(member => member.relation === 'SPOUSE') ||
                                                    // Existing age and BMI validation
                                                    (calculateAge(member.date_of_birth) >= 16 &&
                                                        calculateBMI(member.weight, member.height) &&
                                                        !validateBMI(calculateBMI(member.weight, member.height), member.date_of_birth)) ||
                                                    // Add validation for SON and DAUGHTER for FG HEALTH SURAKSHA and FG VARISHTHA BIMA
                                                    ((formData.product_master_name === 'FG HEALTH SURAKSHA' || formData.product_master_name === 'FG VARISHTHA BIMA') &&
                                                        (member.relation === 'SON' || member.relation === 'DAUGHTER') &&
                                                        !validateAge(member.date_of_birth, formData.product_master_name, member.relation)) ||
                                                    // Add validation for SELF and SPOUSE for FG HEALTH SURAKSHA and FG VARISHTHA BIMA
                                                    ((formData.product_master_name === 'FG HEALTH SURAKSHA' || formData.product_master_name === 'FG VARISHTHA BIMA') &&
                                                        (member.relation === 'SELF' || member.relation === 'SPOUSE') &&
                                                        !validateAge(member.date_of_birth, formData.product_master_name, member.relation)) ||
                                                    // Ensure that the age validation for SON and DAUGHTER is checked for red border
                                                    ((formData.product_master_name === 'FG HEALTH SURAKSHA' || formData.product_master_name === 'FG VARISHTHA BIMA') &&
                                                        (member.relation === 'SON' || member.relation === 'DAUGHTER') &&
                                                        (calculateAge(member.date_of_birth) > 25)) // Adjust this condition as per your age limit

                                                ) ? '2px solid red' : 'none'
                                            }}>
                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} sm={3}>
                                                        <CustomTextField
                                                            label="Member Name"
                                                            value={member.full_name || ''}
                                                            disabled
                                                            fullWidth
                                                            isRequired
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={3}>
                                                        <CustomTextField
                                                            label="Member DOB"
                                                            value={formatDateOfBirth(member.date_of_birth || '')}
                                                            disabled
                                                            fullWidth
                                                            isRequired
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={3}>
                                                        <CustomTextField
                                                            label="Member Relation"
                                                            value={member.relation || ''}
                                                            disabled
                                                            fullWidth
                                                            isRequired
                                                        />
                                                    </Grid>
                                                    {formData.family_type === "individual" && formData.product_master_name === 'FG ADVANTAGE TOP UP' && (

                                                        <Grid item xs={12} sm={3}>
                                                            <Dropdown
                                                                label="Deductible"
                                                                name={`deductible_${index}`}
                                                                value={member.deductible}
                                                                options={getDeductableAmountOptions(member)}
                                                                onChange={(e) => {
                                                                    const newValue = e.target.value;
                                                                    // Reset sum insured when deductible changes
                                                                    handleMemberChange(index, 'deductible', newValue);
                                                                    handleSumInsuredChange(index, '');
                                                                }}
                                                                isRequired
                                                                fullWidth
                                                            />
                                                        </Grid>

                                                    )}
                                                    {formData.family_type === "individual" && formData.product_master_name !== 'FG ACCIDENT SURAKSHA' && (
                                                        <Grid item xs={12} sm={3}>
                                                            <Dropdown
                                                                label="Sum Insured"
                                                                name={`sum_insured_${index}`}
                                                                value={formData.sum_insured[index] || ''}
                                                                options={
                                                                    formData.product_master_name === 'FG ADVANTAGE TOP UP'
                                                                        ? getSumInsuredByDeductibleAndAge(member.deductible, member)
                                                                        : getSumInsuredOptions(formData.cover_type)
                                                                }
                                                                onChange={(e) => handleSumInsuredChange(index, e.target.value)}
                                                                isRequired
                                                                fullWidth
                                                            />
                                                        </Grid>
                                                    )}

                                                    {formData.product_master_name === 'FG ACCIDENT SURAKSHA' && (
                                                        <>
                                                            <Grid item xs={12} sm={3}>
                                                                <CustomTextField
                                                                    isRequired
                                                                    label="Annual Income"
                                                                    name={`annual_income_${index}`}
                                                                    value={member.annual_income || ''}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                                                        // Clear errors for this specific member
                                                                        setErrors(prev => {
                                                                            const newMemberErrors = [...(prev.members || [])];
                                                                            newMemberErrors[index] = {
                                                                                ...(newMemberErrors[index] || {}),
                                                                                ad_sum_insured: undefined,
                                                                                pp_sum_insured: undefined,
                                                                                pt_sum_insured: undefined,
                                                                                tt_sum_insured: undefined
                                                                            };
                                                                            return { ...prev, members: newMemberErrors };
                                                                        });
                                                                        handleMemberChange(index, 'annual_income', value);
                                                                    }}
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={3}>
                                                                <CustomTextField
                                                                    isRequired
                                                                    label="AD Sum Insured"
                                                                    name={`ad_sum_insured_${index}`}
                                                                    value={member.ad_sum_insured || ''}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value;
                                                                        if (value && !/^\d*$/.test(value)) return;

                                                                        const maxAllowed = calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'AD',
                                                                            null,
                                                                            member.occupation,
                                                                            member.relation
                                                                        );

                                                                        if (Number(value) > maxAllowed) {
                                                                            setErrors(prev => {
                                                                                const newMemberErrors = [...(prev.members || [])];
                                                                                newMemberErrors[index] = {
                                                                                    ...(newMemberErrors[index] || {}),
                                                                                    ad_sum_insured: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                                };
                                                                                return { ...prev, members: newMemberErrors };
                                                                            });
                                                                            return;
                                                                        }

                                                                        setErrors(prev => {
                                                                            const newMemberErrors = [...(prev.members || [])];
                                                                            newMemberErrors[index] = {
                                                                                ...(newMemberErrors[index] || {}),
                                                                                ad_sum_insured: undefined
                                                                            };
                                                                            return { ...prev, members: newMemberErrors };
                                                                        });

                                                                        handleMemberChange(index, 'ad_sum_insured', value);
                                                                    }}
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                                    }}
                                                                    error={!!errors.members?.[index]?.ad_sum_insured}
                                                                    helperText={errors.members?.[index]?.ad_sum_insured ||
                                                                        `Max: ₹${calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'AD',
                                                                            null,
                                                                            member.occupation,
                                                                            member.relation
                                                                        ).toLocaleString()}`
                                                                    }
                                                                />
                                                            </Grid>

                                                            <Grid item xs={12} sm={3}>
                                                                <CustomTextField
                                                                    isRequired
                                                                    label="PP Sum Insured"
                                                                    name={`pp_sum_insured_${index}`}
                                                                    value={member.pp_sum_insured || ''}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value;
                                                                        if (value && !/^\d*$/.test(value)) return;

                                                                        const maxAllowed = calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'PT',
                                                                            null,
                                                                            member.occupation,
                                                                            member.relation
                                                                        );
                                                                        if (Number(value) > maxAllowed) {
                                                                            setErrors(prev => {
                                                                                const newMemberErrors = [...(prev.members || [])];
                                                                                newMemberErrors[index] = {
                                                                                    ...(newMemberErrors[index] || {}),
                                                                                    pp_sum_insured: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                                };
                                                                                return { ...prev, members: newMemberErrors };
                                                                            });
                                                                            return;
                                                                        }

                                                                        setErrors(prev => {
                                                                            const newMemberErrors = [...(prev.members || [])];
                                                                            newMemberErrors[index] = {
                                                                                ...(newMemberErrors[index] || {}),
                                                                                pp_sum_insured: undefined
                                                                            };
                                                                            return { ...prev, members: newMemberErrors };
                                                                        });

                                                                        handleMemberChange(index, 'pp_sum_insured', value);
                                                                    }} fullWidth
                                                                    error={!!errors.members?.[index]?.pp_sum_insured}
                                                                    helperText={errors.members?.[index]?.pp_sum_insured ||
                                                                        `Max: ₹${calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'AD',
                                                                            null,
                                                                            member.occupation,
                                                                            member.relation
                                                                        ).toLocaleString()}`
                                                                    }

                                                                    InputProps={{
                                                                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={3}>
                                                                <CustomTextField
                                                                    isRequired
                                                                    label="PT Sum Insured"
                                                                    name={`pt_sum_insured_${index}`}
                                                                    value={member.pt_sum_insured || ''}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value;
                                                                        if (value && !/^\d*$/.test(value)) return;

                                                                        const maxAllowed = calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'PT',
                                                                            null,
                                                                            member.occupation,
                                                                            member.relation
                                                                        );
                                                                        if (Number(value) > maxAllowed) {
                                                                            setErrors(prev => {
                                                                                const newMemberErrors = [...(prev.members || [])];
                                                                                newMemberErrors[index] = {
                                                                                    ...(newMemberErrors[index] || {}),
                                                                                    pt_sum_insured: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                                };
                                                                                return { ...prev, members: newMemberErrors };
                                                                            });
                                                                            return;
                                                                        }

                                                                        setErrors(prev => {
                                                                            const newMemberErrors = [...(prev.members || [])];
                                                                            newMemberErrors[index] = {
                                                                                ...(newMemberErrors[index] || {}),
                                                                                pt_sum_insured: undefined
                                                                            };
                                                                            return { ...prev, members: newMemberErrors };
                                                                        });

                                                                        handleMemberChange(index, 'pt_sum_insured', value);
                                                                    }} fullWidth
                                                                    error={!!errors.members?.[index]?.pt_sum_insured}
                                                                    helperText={errors.members?.[index]?.pt_sum_insured ||
                                                                        `Max: ₹${calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'AD',
                                                                            null,
                                                                            member.occupation,
                                                                            member.relation
                                                                        ).toLocaleString()}`}

                                                                    InputProps={{
                                                                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={3}>
                                                                <CustomTextField
                                                                    //isRequired={!(member.occupation === 'Unemployed' || member.occupation === 'Student')}
                                                                    label="TT Sum Insured"
                                                                    name={`tt_sum_insured_${index}`}
                                                                    value={member.tt_sum_insured || ''}
                                                                    onChange={(e) => {
                                                                        const value = e.target.value;
                                                                        if (value && !/^\d*$/.test(value)) return;

                                                                        // If Unemployed/Student, TTD should be 0
                                                                        if ((member.occupation === 'Unemployed' || member.occupation === 'Student') && member.relation === 'SELF') {
                                                                            handleMemberChange(index, 'tt_sum_insured', '0');
                                                                            return;
                                                                        }

                                                                        const riskClass = getOccupationRiskClass(member.occupation);
                                                                        const maxAllowed = calculateMaxSumInsured(
                                                                            Number(member.annual_income),
                                                                            'TTD',
                                                                            riskClass,
                                                                            member.occupation,
                                                                            member.relation
                                                                        );

                                                                        if (Number(value) > maxAllowed) {
                                                                            setErrors(prev => {
                                                                                const newMemberErrors = [...(prev.members || [])];
                                                                                newMemberErrors[index] = {
                                                                                    ...(newMemberErrors[index] || {}),
                                                                                    tt_sum_insured: `Maximum allowed is ₹${maxAllowed.toLocaleString()}`
                                                                                };
                                                                                return { ...prev, members: newMemberErrors };
                                                                            });
                                                                            return;
                                                                        }

                                                                        setErrors(prev => {
                                                                            const newMemberErrors = [...(prev.members || [])];
                                                                            newMemberErrors[index] = {
                                                                                ...(newMemberErrors[index] || {}),
                                                                                tt_sum_insured: undefined
                                                                            };
                                                                            return { ...prev, members: newMemberErrors };
                                                                        });

                                                                        handleMemberChange(index, 'tt_sum_insured', value);
                                                                    }}
                                                                    fullWidth
                                                                    disabled={(member.occupation === 'Unemployed' || member.occupation === 'Student') && member.relation === 'SELF'}
                                                                    error={!!errors.members?.[index]?.tt_sum_insured}
                                                                    helperText={
                                                                        (member.occupation === 'Unemployed' || member.occupation === 'Student') && member.relation === 'SELF'
                                                                            ? 'TTD not applicable for Unemployed/Student'
                                                                            : errors.members?.[index]?.tt_sum_insured ||
                                                                            `Max: ₹${calculateMaxSumInsured(
                                                                                Number(member.annual_income),
                                                                                'TTD',
                                                                                getOccupationRiskClass(member.occupation),
                                                                                member.occupation,
                                                                                member.relation
                                                                            ).toLocaleString()}`
                                                                    }
                                                                    InputProps={{
                                                                        startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                                                    }}
                                                                />
                                                            </Grid>
                                                        </>
                                                    )}


                                                    <Grid item xs={12}>
                                                        <Typography color="error">
                                                            {/* Age validation error message */}
                                                            {!validateAge(member.date_of_birth, formData.product_master_name, member.relation) && (
                                                                <span style={{ display: 'block' }}>
                                                                    {`${member.full_name}: ${
                                                                        // Check for children age first
                                                                        (member.relation === 'SON' || member.relation === 'DAUGHTER')
                                                                            ? calculateAge(member.date_of_birth) > 25
                                                                                ? `Age ${calculateAge(member.date_of_birth)} exceeds maximum allowed age of 25 years for children`
                                                                                : calculateAge(member.date_of_birth) < 0
                                                                                    ? `Invalid age for children`
                                                                                    : ''
                                                                            // Then check other conditions
                                                                            : formData.product_master_name === 'FG VARISHTHA BIMA'
                                                                                ? formData.family_type === 'floater'
                                                                                    ? member.relation === 'SELF'
                                                                                        ? `Age ${calculateAge(member.date_of_birth)} is below minimum required age of 60 years for primary member`
                                                                                        : `Age ${calculateAge(member.date_of_birth)} is below minimum required age of 25 years for family member`
                                                                                    : `Age ${calculateAge(member.date_of_birth)} is below minimum required age of 60 years`
                                                                                : formData.product_master_name === 'FG HEALTH SURAKSHA'
                                                                                    ? calculateAgeInDays(member.date_of_birth) < 90
                                                                                        ? `Member age must be at least 90 days old`
                                                                                        : `Age ${calculateAge(member.date_of_birth)} exceeds maximum allowed age of 50 years`
                                                                                    : formData.product_master_name === 'FG ACCIDENT SURAKSHA'
                                                                                        ? calculateAge(member.date_of_birth) < 3
                                                                                            ? `Member must be at least 3 years old`
                                                                                            : calculateAge(member.date_of_birth) > 50
                                                                                                ? `Age ${calculateAge(member.date_of_birth)} exceeds maximum allowed age of 50 years`
                                                                                                : ''
                                                                                        : `Age ${calculateAge(member.date_of_birth)} exceeds maximum allowed age of 50 years`
                                                                        }`}
                                                                </span>
                                                            )}

                                                            {/* Marriage validation message */}
                                                            {formData.product_master_name === 'FG HEALTH TOTAL' &&
                                                                selectedCustomer?.marital_status === 'MARRIED' &&
                                                                !members.some(m => m.relation === 'SPOUSE') && (
                                                                    <span style={{ display: 'block', marginTop: '4px' }}>
                                                                        For FG HEALTH TOTAL, married customers must include their spouse in the policy
                                                                    </span>
                                                                )}

                                                            {/* Risk class validation message */}
                                                            {formData.product_master_name === 'FG ACCIDENT SURAKSHA' &&
                                                                getOccupationRiskClass(member.occupation) === 3 && (
                                                                    <span style={{ display: 'block', marginTop: '4px' }}>
                                                                        {`${member.full_name}: Occupation risk class 3 is not allowed for FG ACCIDENT SURAKSHA`}
                                                                    </span>
                                                                )}
                                                        </Typography>

                                                        {/* BMI validation message */}
                                                        <Typography
                                                            color={calculateAge(member.date_of_birth) >= 16 &&
                                                                calculateBMI(member.weight, member.height) &&
                                                                !validateBMI(calculateBMI(member.weight, member.height), member.date_of_birth)
                                                                ? 'error'
                                                                : 'textSecondary'}
                                                        >
                                                            {calculateAge(member.date_of_birth) < 16
                                                                ? `Age: ${calculateAge(member.date_of_birth)} (BMI not applicable for members under 16)`
                                                                : `BMI: ${calculateBMI(member.weight, member.height) || 'N/A'}${calculateBMI(member.weight, member.height) &&
                                                                    !validateBMI(calculateBMI(member.weight, member.height), member.date_of_birth)
                                                                    ? ' - BMI should be between 18 and 32'
                                                                    : ''
                                                                }`
                                                            }
                                                        </Typography>
                                                    </Grid>
                                                </Grid>
                                            </Card>

                                        </Grid>

                                        <Grid item xs={12} sm={1} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>

                                            <IconButton
                                                onClick={() => handleDeleteMember(index)} //19/12/2024
                                                sx={{ color: 'red' }}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Grid>
                                        {formData.product_master_name === 'FG ACCIDENT SURAKSHA' && member.relation === 'SELF' && (
                                            <Grid item xs={12}>
                                                <Card sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5' }}>
                                                    <Typography variant="h6" gutterBottom>
                                                        Additional Covers
                                                    </Typography>
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={12} sm={12} md={12} key="additionalCovers">
                                                            <Grid container spacing={2}>
                                                                <Grid item xs={12}>
                                                                    <FormControl component="fieldset">
                                                                        <RadioGroup
                                                                            row
                                                                            name="additionalCover"
                                                                            value={member.additionalCover || 'Y'}
                                                                            onChange={(e) => handleMemberChange(index, 'additionalCover', e.target.value)}
                                                                        >
                                                                            <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                                                            {/* <FormControlLabel value="N" control={<Radio />} label="No" /> */}
                                                                        </RadioGroup>
                                                                    </FormControl>
                                                                </Grid>

                                                                {/* Sub-covers - only show if additionalCover is 'Y' */}
                                                                {/* {member.additionalCover === 'Y' && ( */}
                                                                <Grid container item xs={12} spacing={2}>
                                                                    {[
                                                                        // Only include childEducationSupport if there are children
                                                                        ...(members.some(m => m.relation === 'SON' || m.relation === 'DAUGHTER')
                                                                            ? [{ key: 'childEducationSupport', label: 'Child Education Support' }]
                                                                            : []),
                                                                        { key: 'lifeSupportBenefit', label: 'Life Support Benefit' },
                                                                        { key: 'adaptationBenefits', label: 'Adaptation Benefits' },
                                                                        { key: 'familyTransportAllowance', label: 'Family Transport Allowance' },
                                                                        { key: 'hospitalCashAllowance', label: 'Hospital Cash Allowance' },
                                                                        { key: 'loanProtector', label: 'Loan Protector' },
                                                                        { key: 'accidentHospitalization', label: 'Accident Hospitalization' },
                                                                        { key: 'accidentMedicalExpenses', label: 'Accident Medical Expenses' },
                                                                        // Always included, no radio option
                                                                        { key: 'repatriationAndFuneralExpenses', label: 'Repatriation And Funeral Expenses', alwaysIncluded: true },
                                                                        { key: 'brokenBones', label: 'Broken Bones' }
                                                                    ].map(cover => (
                                                                        <Grid item xs={12} sm={6} md={4} key={cover.key}>
                                                                            {cover.alwaysIncluded ? (
                                                                                <>
                                                                                    <Typography variant="subtitle2" gutterBottom>
                                                                                        {cover.label}
                                                                                    </Typography>
                                                                                    <Typography variant="body2" color="blue">
                                                                                        Always Included
                                                                                    </Typography>
                                                                                </>
                                                                            ) : (
                                                                                <FormControl component="fieldset">
                                                                                    <Typography variant="subtitle2" gutterBottom>
                                                                                        {cover.label}
                                                                                    </Typography>
                                                                                    <RadioGroup
                                                                                        row
                                                                                        name={`${cover.key}_${index}`}
                                                                                        value={member[cover.key] || 'N'}
                                                                                        onChange={(e) => handleMemberChange(index, cover.key, e.target.value)}
                                                                                    >
                                                                                        <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                                                                        <FormControlLabel value="N" control={<Radio />} label="No" />
                                                                                    </RadioGroup>
                                                                                </FormControl>
                                                                            )}
                                                                        </Grid>
                                                                    ))}
                                                                </Grid>
                                                                {/* )} */}
                                                            </Grid>
                                                        </Grid>
                                                    </Grid>
                                                </Card>
                                            </Grid>
                                        )}
                                    </Grid>

                                </>
                            ))}

                            {/* User Info Section */}
                            <Grid item xs={12} sx={{ pl: 4, pb: 3 }}>
                                <Box sx={{
                                    padding: '10px',
                                    height: '60px',
                                    fontFamily: 'Poppins, sans-serif',
                                    fontSize: '18px',
                                    fontWeight: '700',
                                    lineHeight: '27px',
                                    color: '#4C5157',
                                    marginTop: '20px'
                                }}>
                                    <h5>User Info</h5>
                                </Box>
                                <hr />
                            </Grid>

                            {/* First Row: Assigned To */}
                            {/* <Grid item xs={12} md={3} container spacing={2}>
                                <Grid item xs={3}>
                                    <CustomTextField
                                        label="Assigned To"
                                        disabled
                                        name="assigned_to"
                                        value={formData.assigned_to}
                                        fullWidth
                                        isRequired

                                    />
                                </Grid>
                            </Grid> */}

                            {/* Second Row: Created By and Created At */}
                            <Grid item xs={12} container spacing={2}>
                                <Grid item xs={12} md={3}>
                                    <CustomTextField

                                        label="Created By"
                                        disabled
                                        name="created_by"
                                        value={userId}
                                        fullWidth
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={3}>
                                    <Dropdown
                                        label="Select Agent"
                                        name="agent_id"
                                        value={formData.agent_id}
                                        onChange={handleInputChange}
                                        options={agentOptions}
                                        required
                                        fullWidth
                                        disabled={userId?.includes('RM')}
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            }
                                        }}
                                    />

                                </Grid>
                                {/* <Grid item xs={12} md={3}>
                                    <CustomTextField
                                        label="Created At"
                                        disabled
                                        name="created_at"
                                        value={formatDate(formData.created_at)}
                                        fullWidth
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            },
                                        }}
                                    />
                                </Grid> */}
                            </Grid>

                            {/* Third Row: Updated By and Updated At */}
                            {/* <Grid item xs={12} container spacing={2}>
                                <Grid item xs={3}>
                                    <CustomTextField
                                        label="Updated By"
                                        disabled
                                        name="updated_by"
                                        value={formData.updated_by}
                                        fullWidth
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={3}>
                                    <CustomTextField
                                        label="Updated At"
                                        disabled
                                        name="updated_at"
                                        value={formatDate(formData.updated_at)}
                                        fullWidth
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&::before': {
                                                    content: '""',
                                                    position: 'absolute',
                                                    left: 0,
                                                    top: 0,
                                                    bottom: 0,
                                                    width: '3px',
                                                    backgroundColor: 'red',
                                                    zIndex: 1,
                                                }
                                            },
                                        }}
                                    />
                                </Grid>
                            </Grid> */}
                        </Grid>
                    </Grid>
                </form>
            )}
        </Box>
    );
};

export default QuotationPage;