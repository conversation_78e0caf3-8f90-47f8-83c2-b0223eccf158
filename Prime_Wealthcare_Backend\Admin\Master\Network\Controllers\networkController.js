const Network = require('../Models/network');

// Get all networks
exports.getAll = async (req, res, next) => {
    try {
        const data = await Network.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get network by insurance company ID
exports.getNetworkByInsuranceCompanyId = async (req, res, next) => {
    try {
        const insuranceCompanyId = req.params.insuranceCompanyId;
        const data = await Network.getNetworkByInsuranceCompanyId(insuranceCompanyId);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get network by ID
exports.getNetworkById = async (req, res, next) => {
    try {
        const id = req.params.id;

        const data = await Network.getById(id);
        if (!data) {
            return res.status(404).json({ message: 'Network not found' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get network by name
exports.getNetworkByName = async (req, res, next) => {
    try {
        const name = req.params.name;
        const data = await Network.getByName(name);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Create a new network
exports.createNetwork = async (req, res, next) => {
    try {
        const data = req.body;

        const result = await Network.create(data);

        res.status(201).json(result);
    } catch (error) {
        next(error);
    }
};

// Update network by ID
exports.updateNetwork = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const result = await Network.update(id, data);

        if (result) {
            res.status(200).json({ message: 'Network updated successfully' });
        } else {
            res.status(404).json({ message: 'Network not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Delete (deactivate) network by ID
exports.deleteNetwork = async (req, res,next) => {
    try {
        const id = req.params.id;
        const result = await Network.delete(id);

        if (result) {
            res.status(200).json({ message: 'Network deactivated successfully' });
        } else {
            res.status(404).json({ message: 'Network not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Reinstate a network by ID
exports.reinstateNetwork = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await Network.reinstate(id);
        if (result) {
            res.status(200).json({ message: 'Network reinstated successfully' });
        } else {
            res.status(404).json({ message: 'Network not found' });
        }
    } catch (error) {
        next(error);
    }
};

// Get networks by specific criteria (new, deactivated, edited)
exports.getNetworksByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;

        let data;
        switch (criteria) {
            case 'none':
                data = await Network.getAll();
                break;
            case 'newLastWeek':
                data = await Network.newLastWeek();
                break;
            case 'newThisWeek':
                data = await Network.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await Network.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await Network.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await Network.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await Network.editedLastWeek();
                break;
            case 'allActive':
                data = await Network.allActive();
                break;
            case 'allInactive':
                data = await Network.allInactive();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
