exports.up = function (knex) {
    return knex.schema.hasTable('main_product').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('main_product', function (table) {
                table.increments('id').primary();
                table.string('main_product', 255).notNullable().unique();
                table.integer('created_by').notNullable().defaultTo(1);
                table.integer('updated_by').notNullable().defaultTo(1);
                table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
                table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
                table.boolean('status').notNullable().defaultTo(true);
            });
        }
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('main_product');
};