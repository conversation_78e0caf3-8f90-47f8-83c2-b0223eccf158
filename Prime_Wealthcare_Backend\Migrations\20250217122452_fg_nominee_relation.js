exports.up = function(knex) {
    return knex.schema.createTable('fg_nominee_relation', (table) => {
        table.increments('id').primary();           // Auto-incrementing primary key
        table.string('type_name').notNullable();    // Dropdown category (e.g., Gender, Payment Type)
        table.string('api_name').notNullable();     // API reference (e.g., G_Male, G_Female)
        table.string('label_name').notNullable();   // Label to display in the dropdown
        table.boolean('is_active').notNullable().defaultTo(true); // Whether the pick list item is active
        table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
        table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
    });
};


exports.down = function(knex) {
    return knex.schema.dropTableIfExists('fg_nominee_relation');
};

