const express = require('express');
const router = express.Router();
const axios = require('axios');
const ProposalController = require('../Controllers/proposalController.js');
const knexConfig = require('../../../knexfile');
const { toProperCase } = require('../../../Reusable/reusable.js');
const { sendEmail } = require('../../../services/emailService.js');
const db = require('knex')(knexConfig.development);
const upload = require('../Middleware/upload');

// Remove policy_number from upload fields since it's not a file
const uploadFields = upload.fields([
    { name: 'policy_pdf', maxCount: 1 }
]);

// Update routes to use both middlewares
router.post('/create', uploadFields, ProposalController.createProposal);
router.put('/agent-business-transfer/:id', ProposalController.transferBusiness);
router.put('/:id', uploadFields, ProposalController.updateProposal);
router.get('/', ProposalController.getAllProposals);
router.get('/user/:userId', ProposalController.getAllProposalsByUserId);
router.get('/:id', ProposalController.getProposalById);
router.get('/quotation/:quotation_number', ProposalController.getProposalByQuotationNumber);
router.get('/number/:proposal_number', ProposalController.getProposalByNumber);
router.get('/count/:prefix', ProposalController.getProposalCount);
router.get('/policy-details/:policy_number/:policy_type', ProposalController.getProposalByPolicyNumber);
router.post('/:id/:policy_type', ProposalController.deleteProposal);
// router.get('/policy_pdf/:id', ProposalController.getPolicyPdf);

const getRelationAPI = async (id) => {
    if (!id) return null;
    const pickListItem = await db('fg_nominee_relation')
        .where('id', id)
        .first();
    return pickListItem ? pickListItem.api_name : null;
};

router.post('/submit-proposal', async (req, res) => {
    try {
        const getPickListValue = async (id) => {
            if (!id) return null;
            const pickListItem = await db('pick_list')
                .where('id', id)
                .first();
            return pickListItem ? pickListItem.label_name : null;
        };
        // api_name

        const getPickListAPI = async (id) => {
            if (!id) return null;
            const pickListItem = await db('pick_list')
                .where('id', id)
                .first();
            return pickListItem ? pickListItem.api_name : null;
        };

        const formatDate = (dateString) => {
            if (!dateString) return null;
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            }).replace(/\//g, '/');
        };

        const formatDate1 = (dateString) => {
            if (!dateString) return '';
            // Parse date in DD-MM-YYYY format
            const [day, month, year] = dateString.split('-');
            const date = new Date(`${year}-${month}-${day}`); // Convert to YYYY-MM-DD
            if (isNaN(date)) return ''; // Return empty string if the date is invalid
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        };

        // Add new function to calculate age
        const calculateAge = (birthDate) => {
            if (!birthDate) return null;
            const today = new Date();
            const birth = new Date(birthDate);
            let age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                age--;
            }
            return age;
        };
        // Get the proposal data from request body
        const rawProposalData = Array.isArray(req.body) ? req.body[0] : req.body;

        // Add this new query to fetch proposal details
        let proposalDetails = await db('proposals')
            .where('ProposalNumber', rawProposalData.proposal_number)
            .select('proposal_Id', 'ckyc_number', 'start_date', 'end_date', 'client_id', 'receipt_no')
            .first();

        if (!proposalDetails) {
            proposalDetails = await db('proposals_pa')
                .where('ProposalNumber', rawProposalData.proposal_number)
                .select('proposal_Id', 'ckyc_number', 'start_date', 'end_date', 'client_id', 'receipt_no')
                .first();
        }
        if (!proposalDetails) {
            throw new Error(`Proposal details not found for proposal number: ${rawProposalData.proposal_number}`);
        }

        // First try the regular quotations table
        let quotationData = await db('quotations')
            .where('quotation_number', rawProposalData.quotation_number)
            .first();

        // If not found, try the pa_quotation table
        if (!quotationData) {
            quotationData = await db('pa_quotations')
                .where('quotation_number', rawProposalData.quotation_number)
                .first();
        }

        if (!quotationData) {
            throw new Error(`Quotation not found with number: ${rawProposalData.quotation_number} in either quotations or pa_quotation tables`);
        }
        // Initialize our three data sets
        let customerData = {};
        let membersData = [];
        let proposalData = {
            ...rawProposalData,
            // product_name: product_name,
            duration: quotationData.duration || '',
            policyType: quotationData.policyType || '',
            product_name: quotationData.product || '',
            proposal_Id: proposalDetails.proposal_Id || '',
            ckyc_number: proposalDetails.ckyc_number || '',
            start_date: formatDate(proposalDetails.start_date) || '',
            end_date: formatDate(proposalDetails.end_date) || '',
            coPay: quotationData.coPay || '',
            salutation: getPickListAPI(proposalDetails.salutation) || '',
            client_id: proposalDetails.client_id || '',
            receipt_no: proposalDetails.receipt_no || '',
        };  // Create a copy of the proposal data


        const paymentDetails = await db('payment_master')
            .where('ProposalNumber', proposalData.proposal_number)
            .where('status', 'SUCCESS')
            .select('WS_P_ID', 'PGID', 'online_amount', 'updated_at')
            .first();

        if (!paymentDetails) {
            throw new Error(`Payment details not found for proposal number: ${proposalData.proposal_number}`);
        }


        const customerAddress = await db('customer_address')
            .where('customer_id', proposalData.customer_id)
            .first();

        if (!customerAddress) {
            throw new Error(`Address not found for customer ID: ${proposalData.customer_id}`);
        }

        // Create an object with only the relevant address fields based on used_address
        let selectedAddress = {};
        if (customerAddress.used_address === 'current') {
            selectedAddress = {
                apartment_no: customerAddress.current_apartment_no || '',
                apartment_name: customerAddress.current_apartment_name || '',
                address_line1: customerAddress.current_address_line1 || '',
                address_line2: customerAddress.current_address_line2 || '',
                pincode: customerAddress.current_pincode || '',
                city: customerAddress.current_city || '',
                state: customerAddress.current_state || '',
                area: customerAddress.current_area || '',
            };
        } else if (customerAddress.used_address === 'permanent') {
            selectedAddress = {
                apartment_no: customerAddress.permanent_apartment_no || '',
                apartment_name: customerAddress.permanent_apartment_name || '',
                address_line1: customerAddress.permanent_address_line1 || '',
                address_line2: customerAddress.permanent_address_line2 || '',
                pincode: customerAddress.permanent_pincode || '',
                city: customerAddress.permanent_city || '',
                state: customerAddress.permanent_state || '',
                area: customerAddress.permanent_area || '',
            };
        }




        // Fetch the actual city name if it's an ID
        if (!isNaN(selectedAddress.city)) {
            const cityName = await db('locations')
                .where('id', selectedAddress.city)
                .select('city')
                .first();

            if (cityName) {
                selectedAddress.city = cityName.city; // Update city with the actual name
            } else {
                console.log(`City ID ${selectedAddress.city} not found in locations table.`);
            }
        }
        // Fetch city value from areas table based on selectedAddress
        const areaDetails = await db('areas')
            .where('pincode', selectedAddress.pincode)
            .andWhereRaw('LOWER(city) = ?', [selectedAddress.city.toLowerCase().trim()])
            .first();

        if (areaDetails) {
            selectedAddress.city = areaDetails.city; // Update city if found
            selectedAddress.area = areaDetails.area;
            customerData.city = areaDetails.city; // Assign city to customerData
        } else {
            console.log('No area details found for the given pincode and city');
        }

        // Fetch customer details
        customerData = await db('customer_personal_info')
            .where('id', rawProposalData.customer_id)
            .first();

        if (!customerData) {
            throw new Error(`Customer not found with ID: ${rawProposalData.customer_id}`);
        }
        customerData.gender = await getPickListValue(customerData.gender);
        // customerData.first_name = customerData.full_name?.trim();

        // customerData.last_name = customerData.last_name?.trim();
        // Add customer as first member with SELF relation
        const selfMember = rawProposalData.members.find(m => m.relation === 39);
        if (selfMember) {
            membersData.push({
                ...customerData,
                relation: 'SELF',
                member_id: customerData.id,
                date_of_birth: formatDate(customerData.date_of_birth),
                gender: await getPickListAPI(customerData.gender_id),
                occupation: await getPickListAPI(customerData.occupation),
                medicalLoading: customerData.isSmoking === 'Y' ? 10 : 0,
            });
        }
        console.log(customerData, "customerData")
        // Process other members
        for (let member of rawProposalData.members) {
            if (member.relation === 39) continue;

            // Add validation for member_id
            if (!member.member_id) {
                console.log('Invalid member data:', member);
                continue; // Skip this member if no valid ID
            }

            try {
                const memberDetails = await db('customer_member_info')
                    .where('id', member.member_id)
                    .first();

                if (!memberDetails) {
                    console.log(`No member found with ID: ${member.member_id}`);
                    continue; // Skip this member if not found
                }

                // memberDetails.middle_name = memberDetails.middle_name?.trim();
                // memberDetails.last_name = memberDetails.last_name?.trim();
                //   memberDetails.full_name = memberDetails.first_name?.trim();

                // Find the corresponding member data from proposalData.members
                const memberProposalData = proposalData.members.find(m =>
                    m.member_id === member.member_id && m.relation === member.relation
                );
                // console.log(memberProposalData, "memberProposalData");
                //const first_name = memberDetails.full_name?.trim();
                const relationValue = await getPickListAPI(memberDetails.relation_id);
                const genderValue = await getPickListAPI(memberDetails.gender_id);
                const nomineeRelationValue = await getRelationAPI(memberProposalData?.nominee_relation);
                const nomineeGenderValue = await getPickListAPI(memberProposalData?.nominee_gender);
                const appointeeRelationValue = await getRelationAPI(memberProposalData?.appointee_relation);
                const appointeeGenderValue = await getPickListAPI(memberProposalData?.appointee_gender);


                membersData.push({
                    ...memberDetails,
                    gender: genderValue,
                    date_of_birth: formatDate(memberDetails.date_of_birth),
                    member_age: calculateAge(memberDetails.date_of_birth),
                    ...member,
                    first_name: memberDetails.full_name,
                    relation: relationValue,
                    height: memberDetails.member_height,
                    weight: memberDetails.member_weight,
                    coverType: toProperCase(memberProposalData.coverType),
                    occupation: await getPickListAPI(memberDetails.member_occupation),
                    sumInsured: memberProposalData.sum_insured,
                    deductableDiscount: memberProposalData.deductible,
                    annual_income: memberProposalData.annual_income,
                    ad_sum_insured: memberProposalData.ad_sum_insured,
                    pp_sum_insured: memberProposalData.pp_sum_insured,
                    pt_sum_insured: memberProposalData.pt_sum_insured,
                    tt_sum_insured: memberProposalData.tt_sum_insured,
                    nominee_name: memberProposalData.nominee_name,
                    nominee_gender: nomineeGenderValue,
                    nominee_dob: formatDate(memberProposalData.nominee_dob),
                    nominee_age: calculateAge(memberProposalData.nominee_dob),
                    nominee_relation: nomineeRelationValue,
                    appointee_name: memberProposalData.appointee_name,
                    appointee_gender: appointeeGenderValue,
                    appointee_dob: formatDate(memberProposalData.appointee_dob),
                    appointee_age: calculateAge(memberProposalData.appointee_dob),
                    appointee_relation: appointeeRelationValue,
                    medicalLoading: memberProposalData.isSmoking === 'Y' ? 10 : 0,
                });
            } catch (error) {
                console.error(`Error processing member ${member.member_id}:`, error);
                continue; // Skip this member if there's an error
            }
        }

        // Also add nominee and appointee details for SELF member (first member)
        const selfMemberProposalData = proposalData.members.find(m => m.relation === 39);
        if (selfMemberProposalData) {
            const selfNomineeRelationValue = await getRelationAPI(selfMemberProposalData.nominee_relation);
            const selfAppointeeRelationValue = await getRelationAPI(selfMemberProposalData.appointee_relation);
            const genderValue = await getPickListAPI(selfMemberProposalData.gender_id);
            const appointeeGenderValue = await getPickListAPI(selfMemberProposalData?.appointee_gender);


            membersData[0] = {
                ...membersData[0],
                //  gender: genderValue,
                deductableDiscount: selfMemberProposalData.deductible || 0,
                coverType: toProperCase(selfMemberProposalData.coverType) || '',
                member_age: calculateAge(selfMemberProposalData.date_of_birth) || '',
                sumInsured: selfMemberProposalData.sum_insured || 0,
                nominee_name: selfMemberProposalData.nominee_name || '',
                nominee_gender: await getPickListAPI(selfMemberProposalData.nominee_gender || ''),
                nominee_dob: formatDate(selfMemberProposalData.nominee_dob) || '',
                nominee_age: calculateAge(selfMemberProposalData.nominee_dob) || '',
                nominee_relation: selfNomineeRelationValue || '',
                appointee_name: selfMemberProposalData.appointee_name || '',
                appointee_gender: appointeeGenderValue || '',
                appointee_dob: formatDate(selfMemberProposalData.appointee_dob) || '',
                appointee_age: calculateAge(selfMemberProposalData.appointee_dob) || '',
                appointee_relation: selfAppointeeRelationValue || '',
                annual_income: selfMemberProposalData?.annual_income || '',
                ad_sum_insured: selfMemberProposalData?.ad_sum_insured || '',
                pp_sum_insured: selfMemberProposalData?.pp_sum_insured || '',
                pt_sum_insured: selfMemberProposalData?.pt_sum_insured || '',
                tt_sum_insured: selfMemberProposalData?.tt_sum_insured || '',
            };
        }
        const formattedCustomerData = {
            first_name: customerData.first_name || '',
            last_name: customerData.last_name || '',
            date_of_birth: formatDate(customerData.date_of_birth) || '',
            gender: await getPickListAPI(customerData.gender_id) || '',
            marital_status: await getPickListAPI(customerData.marital_status_id) || '',
            occupation: await getPickListAPI(customerData.occupation) || '',
            pincode: selectedAddress.pincode || '',
            address_line1: selectedAddress.address_line1 || '',
            address_line2: selectedAddress.address_line2 || '',
            city: selectedAddress.city || '',
            state: selectedAddress.state || '',
            mobile: customerData.mobile || '',
            email: customerData.email || '',
            pan_number: customerData.pan_number || '',
        };
        // Structure the final data sets
        const finalData = {
            customerData: formattedCustomerData,
            membersData: membersData,
            proposalData: {
                ...proposalData,
                //  start_date: formatDate(proposalData.start_date),
                //  end_date: formatDate(proposalData.end_date),
                // quotationData: quotationData
                WS_P_ID: paymentDetails?.WS_P_ID || '',
                PGID: paymentDetails?.PGID || '',
                PremiumAmount: Math.floor(paymentDetails?.online_amount) || '',
                transaction_date: formatDate(paymentDetails?.updated_at || ''),
                salutation: await getPickListAPI(rawProposalData.salutation) || '',
            }
        };
        // Extract product_name from the proposal data
        const product_name = proposalData.product_name;
        if (!product_name) {
            throw new Error('Product name is required');
        }
        // console.log(product_name, "product_name");
        // console.log('Final organized data:', JSON.stringify(finalData, null, 2));

        // Determine which SOAP service to use based on product name
        let soapService;
        switch (String(product_name)) {
            case '1':
            case 'FG HEALTH ABSOLUTE':
                soapService = require('../SoapService/HealthAbsoluteProposalSoapService');
                break;
            case '4':
            case 'FG HEALTH TOTAL':
                soapService = require('../SoapService/HealthTotalProposalSoapService');
                break;
            case '2':
            case 'FG ADVANTAGE TOP UP':
                soapService = require('../SoapService/AdvantageToUpProposalSoapService');
                break;
            case '3':
            case 'FG VARISHTA BIMA':
                soapService = require('../SoapService/VarishtaBimaSoapService.js');
                break;
            case '5':
            case 'FG HEALTH SURAKSHA':
                soapService = require('../SoapService/HealthSurakshaProposalSoapService.js');
                break;
            case '7':
            case 'FG ACCIDENT SURAKSHA':
                soapService = require('../SoapService/PAProposalSoapService.js');
                break;
            default:
                throw new Error(`Invalid product name: ${product_name}`);
        }

        // Call the appropriate SOAP service with the organized data
        const result = await soapService.sendSOAPRequest(
            finalData.membersData,
            finalData.proposalData,
            proposalData.id,
            finalData.customerData
        );
        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('Proposal submission error:', error);

        // Handle SOAP service specific errors
        if (error.error && error.error.message) {
            // This is a formatted SOAP error from the service
            return res.status(500).json({
                success: false,
                error: {
                    message: error.error.message,
                    timestamp: error.error.timestamp,
                    type: error.error.type
                }
            });
        }

        // Handle other types of errors
        res.status(500).json({
            success: false,
            error: {
                message: error.message || 'Failed to submit proposal',
                timestamp: new Date().toISOString(),
                type: 'PROPOSAL_SUBMISSION_ERROR'
            }
        });
    }
});

router.post('/submit-policy', async (req, res) => {
    try {
        const getPickListValue = async (id) => {
            if (!id) return null;
            const pickListItem = await db('pick_list')
                .where('id', id)
                .first();
            return pickListItem ? pickListItem.label_name : null;
        };
        // api_name

        const getPickListAPI = async (id) => {
            if (!id) return null;
            const pickListItem = await db('pick_list')
                .where('id', id)
                .first();
            return pickListItem ? pickListItem.api_name : null;
        };

        const formatDate = (dateString) => {
            if (!dateString) return null;
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            }).replace(/\//g, '/');
        };

        const formatDate1 = (dateString) => {
            if (!dateString) return '';
            // Parse date in DD-MM-YYYY format
            const [day, month, year] = dateString.split('-');
            const date = new Date(`${year}-${month}-${day}`); // Convert to YYYY-MM-DD
            if (isNaN(date)) return ''; // Return empty string if the date is invalid
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        };


        // Add new function to calculate age
        const calculateAge = (birthDate) => {
            if (!birthDate) return null;
            const today = new Date();
            const birth = new Date(birthDate);
            let age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                age--;
            }
            return age;
        };
        // Get the proposal data from request body
        const rawProposalData = Array.isArray(req.body) ? req.body[0] : req.body;
        // console.log('this is the raw proposal data', rawProposalData);

        // Add this new query to fetch proposal details
        let proposalDetails = await db('proposals')
            .where('ProposalNumber', rawProposalData.proposal_number)
            .select('proposal_Id', 'ckyc_number', 'start_date', 'end_date', 'tenure', 'client_id', 'receipt_no')
            .first();

        if (!proposalDetails) {
            proposalDetails = await db('proposals_pa')
                .where('ProposalNumber', rawProposalData.proposal_number)
                .select('proposal_Id', 'ckyc_number', 'start_date', 'end_date', 'tenure', 'client_id', 'receipt_no')
                .first();
        }
        if (!proposalDetails) {
            throw new Error(`Proposal details not found for proposal number: ${rawProposalData.proposal_number}`);
        }

        // First try the regular quotations table
        let quotationData = await db('quotations')
            .where('quotation_number', rawProposalData.quotation_number)
            .first();

        // If not found, try the pa_quotation table
        if (!quotationData) {
            quotationData = await db('pa_quotations')
                .where('quotation_number', rawProposalData.quotation_number)
                .first();
        }

        if (!quotationData) {
            throw new Error(`Quotation not found with number: ${rawProposalData.quotation_number} in either quotations or pa_quotation tables`);
        }
        // Initialize our three data sets
        let customerData = {};
        let membersData = [];
        let proposalData = {
            ...rawProposalData,
            // product_name: product_name,
            duration: proposalDetails.tenure || '',
            policyType: quotationData.policyType || '',
            product_name: quotationData.product || '',
            proposal_Id: proposalDetails.proposal_Id || '',
            ckyc_number: proposalDetails.ckyc_number || '',
            start_date: formatDate(proposalDetails.start_date) || '',
            end_date: formatDate(proposalDetails.end_date) || '',
            coPay: quotationData.coPay || '',
            salutation: await getPickListAPI(rawProposalData.salutation) || '',
            client_id: proposalDetails.client_id || '',
            receipt_no: proposalDetails.receipt_no || '',
        };  // Create a copy of the proposal data

        const salutationValue = await getPickListAPI(proposalDetails.salutation);

        const paymentDetails = await db('payment_master')
            .where('ProposalNumber', proposalData.proposal_number)
            .where('status', 'SUCCESS')
            .select('WS_P_ID', 'PGID', 'online_amount', 'updated_at')
            .first();

        if (!paymentDetails) {
            throw new Error(`Payment details not found for proposal number: ${proposalData.proposal_number}`);
        }


        const customerAddress = await db('customer_address')
            .where('customer_id', proposalData.customer_id)
            .first();

        if (!customerAddress) {
            throw new Error(`Address not found for customer ID: ${proposalData.customer_id}`);
        }

        // Create an object with only the relevant address fields based on used_address
        let selectedAddress = {};
        if (customerAddress.used_address === 'current') {
            selectedAddress = {
                apartment_no: customerAddress.current_apartment_no || '',
                apartment_name: customerAddress.current_apartment_name || '',
                address_line1: customerAddress.current_address_line1 || '',
                address_line2: customerAddress.current_address_line2 || '',
                pincode: customerAddress.current_pincode || '',
                city: customerAddress.current_city || '',
                state: customerAddress.current_state || '',
                area: customerAddress.current_area || '',
            };
        } else if (customerAddress.used_address === 'permanent') {
            selectedAddress = {
                apartment_no: customerAddress.permanent_apartment_no || '',
                apartment_name: customerAddress.permanent_apartment_name || '',
                address_line1: customerAddress.permanent_address_line1 || '',
                address_line2: customerAddress.permanent_address_line2 || '',
                pincode: customerAddress.permanent_pincode || '',
                city: customerAddress.permanent_city || '',
                state: customerAddress.permanent_state || '',
                area: customerAddress.permanent_area || '',
            };
        }



        // Fetch the actual city name if it's an ID
        if (!isNaN(selectedAddress.city)) {
            const cityName = await db('locations')
                .where('id', selectedAddress.city)
                .select('city')
                .first();

            if (cityName) {
                selectedAddress.city = cityName.city; // Update city with the actual name
            } else {
                console.log(`City ID ${selectedAddress.city} not found in locations table.`);
            }
        }
        // Fetch city value from areas table based on selectedAddress
        const areaDetails = await db('areas')
            .where('pincode', selectedAddress.pincode)
            .andWhereRaw('LOWER(city) = ?', [selectedAddress.city.toLowerCase().trim()])
            .first();

        if (areaDetails) {
            // console.log('Area Details:', areaDetails);
            selectedAddress.city = areaDetails.city; // Update city if found
            selectedAddress.area = areaDetails.area;
            customerData.city = areaDetails.city; // Assign city to customerData
        } else {
            console.log('No area details found for the given pincode and city');
        }

        // Fetch customer details
        customerData = await db('customer_personal_info')
            .where('id', rawProposalData.customer_id)
            .first();

        if (!customerData) {
            throw new Error(`Customer not found with ID: ${rawProposalData.customer_id}`);
        }
        customerData.gender = await getPickListValue(customerData.gender);
        // customerData.first_name = customerData.full_name?.trim();

        // customerData.last_name = customerData.last_name?.trim();
        // Add customer as first member with SELF relation
        const selfMember = rawProposalData.members.find(m => m.relation === 39);
        if (selfMember) {
            membersData.push({
                ...customerData,
                relation: 'SELF',
                member_id: customerData.id,
                date_of_birth: formatDate(customerData.date_of_birth),
                member_age: calculateAge(customerData.date_of_birth),
                gender: await getPickListAPI(customerData.gender_id),
                occupation: await getPickListAPI(customerData.occupation),
                medicalLoading: customerData.isSmoking === 'Y' ? 10 : 0,
            });
        }
        // Process other members
        for (let member of rawProposalData.members) {
            if (member.relation === 39) continue;

            // Add validation for member_id
            if (!member.member_id) {
                console.log('Invalid member data:', member);
                continue; // Skip this member if no valid ID
            }

            try {
                const memberDetails = await db('customer_member_info')
                    .where('id', member.member_id)
                    .first();

                if (!memberDetails) {
                    console.log(`No member found with ID: ${member.member_id}`);
                    continue; // Skip this member if not found
                }

                // memberDetails.middle_name = memberDetails.middle_name?.trim();
                // memberDetails.last_name = memberDetails.last_name?.trim();
                //   memberDetails.full_name = memberDetails.first_name?.trim();

                // Find the corresponding member data from proposalData.members
                const memberProposalData = proposalData.members.find(m =>
                    m.member_id === member.member_id && m.relation === member.relation
                );
                if (!memberProposalData) {
                    console.log(`No proposal data found for member ID: ${member.member_id}`);
                    continue;
                }
                //const first_name = memberDetails.full_name?.trim();
                const relationValue = await getPickListAPI(memberDetails.relation_id);
                const genderValue = await getPickListAPI(memberDetails.gender_id);
                const nomineeRelationValue = await getRelationAPI(memberProposalData?.nominee_relation);
                const nomineeGenderValue = await getPickListAPI(memberProposalData?.nominee_gender);
                const appointeeRelationValue = await getRelationAPI(memberProposalData?.appointee_relation);
                const appointeeGenderValue = await getPickListAPI(memberProposalData?.appointee_gender);


                membersData.push({
                    ...memberDetails,
                    gender: genderValue || '',
                    date_of_birth: formatDate(memberDetails.date_of_birth) || '',
                    member_age: calculateAge(memberDetails.date_of_birth) || '',
                    ...member,
                    first_name: memberDetails.full_name || '',
                    relation: relationValue || '',
                    height: memberDetails.member_height || '',
                    weight: memberDetails.member_weight || '',
                    coverType: toProperCase(memberProposalData.coverType) || '',
                    occupation: await getPickListAPI(memberDetails.member_occupation) || '',
                    sumInsured: memberProposalData.sum_insured || '0',
                    deductableDiscount: memberProposalData.deductible || '0',
                    annual_income: memberProposalData.annual_income || '0',
                    ad_sum_insured: memberProposalData.ad_sum_insured || '0',
                    pp_sum_insured: memberProposalData.pp_sum_insured || '0',
                    pt_sum_insured: memberProposalData.pt_sum_insured || '0',
                    tt_sum_insured: memberProposalData.tt_sum_insured || '0',
                    RF_suminsured: memberProposalData.rf_sum_insured || '0',
                    AA_suminsured: memberProposalData.aa_sum_insured || '0',
                    CS_suminsured: memberProposalData.cs_sum_insured || '0',
                    FT_suminsured: memberProposalData.ft_sum_insured || '0',
                    HC_suminsured: memberProposalData.hc_sum_insured || '0',
                    ME_suminsured: memberProposalData.me_sum_insured || '0',
                    LP_suminsured: memberProposalData.lp_sum_insured || '0',
                    LS_suminsured: memberProposalData.ls_sum_insured || '0',
                    AM_suminsured: memberProposalData.am_sum_insured || '0',
                    BB_suminsured: memberProposalData.bb_sum_insured || '0',

                    nominee_name: memberProposalData.nominee_name || '',
                    nominee_gender: nomineeGenderValue || '',
                    nominee_dob: formatDate(memberProposalData.nominee_dob) || '',
                    nominee_age: calculateAge(memberProposalData.nominee_dob) || '',
                    nominee_relation: nomineeRelationValue || '',
                    appointee_name: memberProposalData.appointee_name || '',
                    appointee_gender: appointeeGenderValue || '',
                    appointee_dob: formatDate(memberProposalData.appointee_dob) || '',
                    appointee_age: calculateAge(memberProposalData.appointee_dob) || '',
                    appointee_relation: appointeeRelationValue || '',
                    medicalLoading: memberProposalData.isSmoking === 'Y' ? 10 : 0,
                });

            } catch (error) {
                console.error(`Error processing member ${member.member_id}:`, error);
                continue; // Skip this member if there's an error
            }
        }

        // Also add nominee and appointee details for SELF member (first member)
        const selfMemberProposalData = proposalData.members.find(m => m.relation === 39);
        if (selfMemberProposalData) {
            const selfNomineeRelationValue = await getRelationAPI(selfMemberProposalData.nominee_relation);
            const selfAppointeeRelationValue = await getRelationAPI(selfMemberProposalData.appointee_relation);
            const genderValue = await getPickListAPI(selfMemberProposalData.gender_id);
            const appointeeGenderValue = await getPickListAPI(selfMemberProposalData?.appointee_gender);

            membersData[0] = {
                ...membersData[0],
                //  gender: genderValue,
                deductableDiscount: selfMemberProposalData.deductible || 0,
                coverType: toProperCase(selfMemberProposalData.coverType) || '',
                // member_age: calculateAge(selfMemberProposalData.date_of_birth) || '',
                sumInsured: selfMemberProposalData.sum_insured || 0,
                nominee_name: selfMemberProposalData.nominee_name || '',
                nominee_gender: await getPickListAPI(selfMemberProposalData.nominee_gender || ''),
                nominee_dob: formatDate(selfMemberProposalData.nominee_dob) || '',
                nominee_age: calculateAge(selfMemberProposalData.nominee_dob) || '',
                nominee_relation: selfNomineeRelationValue || '',
                appointee_name: selfMemberProposalData.appointee_name || '',
                appointee_gender: appointeeGenderValue || '',
                appointee_dob: formatDate(selfMemberProposalData.appointee_dob) || '',
                appointee_age: calculateAge(selfMemberProposalData.appointee_dob) || '',
                appointee_relation: selfAppointeeRelationValue || '',
                annual_income: selfMemberProposalData.annual_income || '0',
                ad_sum_insured: selfMemberProposalData.ad_sum_insured || '0',
                pp_sum_insured: selfMemberProposalData.pp_sum_insured || '0',
                pt_sum_insured: selfMemberProposalData.pt_sum_insured || '0',
                tt_sum_insured: selfMemberProposalData.tt_sum_insured || '0',
                RF_suminsured: selfMemberProposalData.rf_sum_insured || '0',
                AA_suminsured: selfMemberProposalData.aa_sum_insured || '0',
                CS_suminsured: selfMemberProposalData.cs_sum_insured || '0',
                FT_suminsured: selfMemberProposalData.ft_sum_insured || '0',
                HC_suminsured: selfMemberProposalData.hc_sum_insured || '0',
                ME_suminsured: selfMemberProposalData.me_sum_insured || '0',
                LP_suminsured: selfMemberProposalData.lp_sum_insured || '0',
                LS_suminsured: selfMemberProposalData.ls_sum_insured || '0',
                AM_suminsured: selfMemberProposalData.am_sum_insured || '0',
                BB_suminsured: selfMemberProposalData.bb_sum_insured || '0',
            };
        }
        const formattedCustomerData = {
            first_name: customerData.first_name || '',
            last_name: customerData.last_name || '',
            date_of_birth: formatDate(customerData.date_of_birth) || '',
            gender: await getPickListAPI(customerData.gender_id) || '',
            marital_status: await getPickListAPI(customerData.marital_status_id) || '',
            occupation: await getPickListAPI(customerData.occupation) || '',
            pincode: selectedAddress.pincode || '',
            address_line1: selectedAddress.address_line1 || '',
            address_line2: selectedAddress.address_line2 || '',
            city: selectedAddress.city || '',
            state: selectedAddress.state || '',
            mobile: customerData.mobile || '',
            email: customerData.email || '',
            isSmoking: customerData.isSmoking || '',
            pan_number: customerData.pan_number || '',
        };
        // Structure the final data sets
        const finalData = {
            customerData: formattedCustomerData,
            membersData: membersData,
            proposalData: {
                ...proposalData,
                //  start_date: formatDate(proposalData.start_date),
                //  end_date: formatDate(proposalData.end_date),
                // quotationData: quotationData
                WS_P_ID: paymentDetails?.WS_P_ID || '',
                PGID: paymentDetails?.PGID || '',
                PremiumAmount: Math.floor(paymentDetails?.online_amount) || '',
                transaction_date: formatDate(paymentDetails?.updated_at || ''),
                salutation: await getPickListAPI(rawProposalData.salutation) || '',
            }
        };
        // Extract product_name from the proposal data
        const product_name = proposalData.product_name;
        if (!product_name) {
            throw new Error('Product name is required');
        }

        // Determine which SOAP service to use based on product name
        let soapService;
        switch (String(product_name)) {
            case '1':
            case 'FG HEALTH ABSOLUTE':
                soapService = require('../SoapService/PolicyIssuance/HealthAbsolutePolicy.js');
                break;
            case '4':
            case 'FG HEALTH TOTAL':
                soapService = require('../SoapService/PolicyIssuance/HealthTotalPolicy.js');
                break;
            case '2':
            case 'FG ADVANTAGE TOP UP':
                soapService = require('../SoapService/PolicyIssuance/AdvantageTopupPolicy.js');
                break;
            case '3':
            case 'FG VARISHTA BIMA':
                soapService = require('../SoapService/VarishtaBimaSoapService.js');
                break;
            case '5':
            case 'FG HEALTH SURAKSHA':
                soapService = require('../SoapService/PolicyIssuance/HealthSurakashPolicy.js');
                break;
            case '7':
            case 'FG ACCIDENT SURAKSHA':
                soapService = require('../SoapService/PAProposalSoapService.js');
                break;
            default:
                throw new Error(`Invalid product name: ${product_name}`);
        }

        // Call the appropriate SOAP service with the organized data
        const result = await soapService.sendSOAPRequest(
            finalData.membersData,
            finalData.proposalData,
            proposalData.proposal_Id,
            finalData.customerData
        );

        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('Proposal submission error:', error);

        // Handle SOAP service specific errors
        if (error.error && error.error.message) {
            // This is a formatted SOAP error from the service
            return res.status(500).json({
                success: false,
                error: {
                    message: error.error.message,
                    timestamp: error.error.timestamp,
                    type: error.error.type
                }
            });
        }

        // Handle other types of errors
        res.status(500).json({
            success: false,
            error: {
                message: error.message || 'Failed to submit proposal',
                timestamp: new Date().toISOString(),
                type: 'PROPOSAL_SUBMISSION_ERROR'
            }
        });
    }
});

router.post('/check-for-ckyc', async (req, res) => {
    try {
        const ckycUrl = process.env.CKYC_API_URL;
        const username = process.env.CKYC_AUTHENTICATION_USERNAME;
        const password = process.env.CKYC_AUTHENTICATION_PASSWORD;
        const token = process.env.CKYC_HEADER_TOKEN;
        const response = await axios.post(
            ckycUrl,
            req.body,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(JSON.stringify(req.body)),
                    'User-Agent': 'axios/0.21.1',
                    accept: '*/*',
                    'Accept-Encoding': 'gzip, deflate, br',
                    connection: 'keep-alive',
                    Token: token ? token : ''
                },
                auth: {
                    username: username ? username : '',
                    password: password ? password : ''
                }
            }
        );
        res.json(response.data);
    } catch (error) {
        console.error('CKYC API Error:', error);
        res.status(error.response?.status || 500).json({
            error: error.response?.data || 'Failed to check CKYC'
        });
    }
});

router.post('/check-ckyc-status', async (req, res) => {
    try {
        // console.log('this is the ckyc check request', req.body)
        const ckycUrl = process.env.CHECK_CKYC_STATUS_URL;
        const username = process.env.CKYC_AUTHENTICATION_USERNAME;
        const password = process.env.CKYC_AUTHENTICATION_PASSWORD;
        const token = process.env.CKYC_HEADER_TOKEN;
        const response = await axios.post(
            ckycUrl,
            req.body,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Content-Length': Buffer.byteLength(JSON.stringify(req.body)),
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'User-Agent': 'axios/0.21.1',
                    Token: token ? token : ''
                },
                auth: {
                    username: username ? username : '',
                    password: password ? password : ''
                }
            });

        res.json(response.data);
    } catch (error) {
        console.error('CKYC API Error:', error);
        res.status(error.response?.status || 500).json({
            success: false,
            error: {
                message: error.message || 'Failed to check CKYC status',
                timestamp: new Date().toISOString(),
                type: 'CKYC_STATUS_CHECK_ERROR'
            }
        });
    }
});

// Route to get policy PDF
router.get('/policy/:id', async (req, res) => {
    const policyId = req.params.id;
    try {
        const tableName = policyId.includes('PAL') ? 'proposals_pa' : 'proposals';
        if (!policyId) {
            return res.status(404).json({ message: 'Policy not found' });
        }

        // Call the SOAP service to get the PDF URL
        const pdfResult = await require('../SoapService/PolicyIssuance/GetPolicyPdf.js').sendSOAPRequest(
            policyId
        );
        // console.log('this is the pdf result : ', pdfResult);

        // Handle database update in routes
        if (pdfResult && pdfResult?.pdfUrl && pdfResult?.pdfUrl !== 'Kindly contact FG for policy document.') {
            await db(tableName)
                .where('policy_number', policyId)
                .update({
                    policy_pdf: pdfResult.pdfUrl
                });

            // Get combined data for email
            const combinedData = await db(tableName)
                .where(`${tableName}.policy_number`, policyId)
                .join('customer_personal_info', `${tableName}.customer_id`, 'customer_personal_info.id')
                .join('agents', `${tableName}.agent_code`, 'agents.id')
                .join('product_master', `${tableName}.product_name`, 'product_master.id')
                .select(
                    `${tableName}.policy_number`,
                    `${tableName}.policy_pdf`,
                    'customer_personal_info.email as customer_email',
                    'customer_personal_info.first_name as customer_first_name',
                    'agents.official_email as agent_email',
                    'product_master.product_name'
                )
                .first();

            // Prepare email content for customer
            const emailSubject = `Your ${combinedData.product_name} Policy Document`;
            const emailText = `Dear ${combinedData.customer_first_name},\n\nThank you for choosing Prime Wealthcare. Your policy document is now available.\n\nYou can access your policy document using the following link:\n${pdfResult.pdfUrl}\n\nPolicy Number: ${combinedData.policy_number}`;
            const emailHtml = `
                <div style="font-family: Arial, sans-serif;">
                    <h2>Your Policy Document is Ready</h2>
                    <p>Dear ${combinedData.customer_first_name},</p>
                    <p>Thank you for choosing Prime Wealthcare. Your policy document for <strong>${combinedData.product_name}</strong> is now available.</p>
                    <p>Policy Details:</p>
                    <ul>
                        <li>Policy Number: ${combinedData.policy_number}</li>
                        <li>Product: ${combinedData.product_name}</li>
                    </ul>
                    <p>You can access your policy document by clicking the button below:</p>
                    <p style="margin: 20px 0;">
                        <a href="${pdfResult.pdfUrl}" 
                           style="background-color: #528a7e; 
                                  color: white; 
                                  padding: 10px 20px; 
                                  text-decoration: none; 
                                  border-radius: 5px;">
                            View Policy Document
                        </a>
                    </p>
                    <p>If you are unable to see the policy, Kindly contact your Relationship Manager or Prime Wealth Care Branch!</p>
                </div>
            `;

            // Send email to customer
            await sendEmail(
                combinedData.customer_email,
                emailSubject,
                emailText,
                emailHtml
            );

            // Prepare and send email to agent
            const agentEmailSubject = `Policy Document for ${combinedData.customer_first_name} - ${combinedData.policy_number}`;
            const agentEmailText = `Dear Agent,\n\nA policy document for your client ${combinedData.customer_first_name} is now available.\n\nPolicy Number: ${combinedData.policy_number}\nProduct: ${combinedData.product_name}\n\nYou can access the policy document using the following link:\n${pdfResult.pdfUrl}`;
            const agentEmailHtml = `
                <div style="font-family: Arial, sans-serif;">
                    <h2>Client Policy Document Available</h2>
                    <p>Dear Agent,</p>
                    <p>A policy document for your client <strong>${combinedData.customer_first_name}</strong> is now available.</p>
                    <p>Policy Details:</p>
                    <ul>
                        <li>Policy Number: ${combinedData.policy_number}</li>
                        <li>Product: ${combinedData.product_name}</li>
                        <li>Client Name: ${combinedData.customer_first_name}</li>
                    </ul>
                    <p>You can access the policy document by clicking the button below:</p>
                    <p style="margin: 20px 0;">
                        <a href="${pdfResult.pdfUrl}" 
                           style="background-color: #528a7e; 
                                  color: white; 
                                  padding: 10px 20px; 
                                  text-decoration: none; 
                                  border-radius: 5px;">
                            View Policy Document
                        </a>
                    </p>
                    <p>If you are unable to see the policy, Kindly contact Prime Wealth Care Branch!</p>
                </div>
            `;

            if (combinedData.agent_email) {
                await sendEmail(
                    combinedData.agent_email,
                    agentEmailSubject,
                    agentEmailText,
                    agentEmailHtml
                );
            }

            // console.log('PDF URL saved successfully', pdfResult?.pdfUrl);
            res.json({ success: true, pdfUrl: pdfResult.pdfUrl });
        } else {
            // console.log('PDF data not available:', pdfResult);
            res.status(500).json({ message: 'PDF data not available' });
        }
    } catch (error) {
        console.error('Error fetching policy:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Add this new route
router.get('/policy-logs/:quotationNumber', async (req, res) => {
    try {
        const { quotationNumber } = req.params;

        const logs = await db('policy_logs')
            .where('quotation_number', quotationNumber)
            .select(
                // 'id',
                //'quotation_number',
                //'policy_type',
                'request_payload',
                'response_payload',
                //'status',
                //'error_message',
                //'created_at'
            )
            .orderBy('created_at', 'desc');

        if (!logs.length) {
            return res.status(404).json({
                success: false,
                message: 'No logs found for this quotation number'
            });
        }

        // Get proposal details
        const proposal = await db('proposals')
            .where('quotation_number', quotationNumber)
            .select(
                'ProposalNumber',
                'policy_number',
                'status as proposal_status'
            )
            .first();

        res.json({
            success: true,
            data: {
                logs,
                proposal_details: proposal || {}
            },
            count: logs.length
        });

    } catch (error) {
        console.error('Error fetching policy logs:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching policy logs',
            error: error.message
        });
    }
});


module.exports = router;