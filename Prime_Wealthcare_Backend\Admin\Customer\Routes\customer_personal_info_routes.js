const express = require('express');
const customerMasterController = require('../Controllers/customer_personal_info_controller');
const { route } = require('./customer_address_route');
const router = express.Router();

// Route to create a new customer info
router.post('/', customerMasterController.createCustomer);

router.get('/customerAndAddress', customerMasterController.getCustomerAndAddress);

router.get('/', customerMasterController.getCustomer);

router.get('/:id', customerMasterController.getCostomerById);

router.put('/:id', customerMasterController.updateCustomer);



module.exports = router;