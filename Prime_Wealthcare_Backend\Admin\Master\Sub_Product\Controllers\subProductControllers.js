const SubProduct = require('../Models/subProduct');
const { insertCommissionRates } = require('../../Commission_Rate/Models/commissionRate');

// Get all products
exports.getAll = async (req, res, next) => {
    try {
        const data = await SubProduct.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

exports.getByProductDetails = async (req, res, next) => {
    try {
        const { mainProductId, insuranceCompanyId, productMasterId } = req.params;
        const data = await SubProduct.getSubProductByProductDetails(mainProductId, insuranceCompanyId, productMasterId);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
}

// Get product by ID
exports.getById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const product = await SubProduct.getById(id);
        res.status(200).json(product);
    } catch (error) {
        next(error);
    }
};

// Create new product
exports.create = async (req, res, next) => {
    try {
        const productData = req.body;
        const id = await SubProduct.create(productData);
        await insertCommissionRates();
        res.status(201).json(id);
    } catch (error) {
        next(error);
    }
};

// Update product by ID
exports.update = async (req, res, next) => {
    try {
        const { id } = req.params;
        const productData = req.body;

        await SubProduct.update(id, productData);
        res.status(200).json(productData);
    } catch (error) {
        next(error);
    }
};

// Soft delete (deactivate) product by ID
exports.delete = async (req, res, next) => {
    try {
        const { id } = req.params;
        await SubProduct.delete(id);
        res.status(200).json({ message: 'Sub product deactivated successfully' });
    } catch (error) {
        next(error);
    }
};

// Reinstate product by ID
exports.reinstate = async (req, res, next) => {
    try {
        const { id } = req.params;
        await SubProduct.reinstate(id);
        res.status(200).json({ message: 'Sub product reinstated successfully' });
    } catch (error) {
        next(error);
    }
};

exports.getByName = async (req, res, next) => {
    try {
        const { name } = req.params;
        const product = await SubProduct.getByName(name);
        res.status(200).json(product);
    } catch (error) {
        next(error);
    }
};

exports.getByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await SubProduct.getAll();
                break;
            case 'newLastWeek':
                data = await SubProduct.newLastWeek();
                break;
            case 'newThisWeek':
                data = await SubProduct.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await SubProduct.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await SubProduct.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await SubProduct.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await SubProduct.editedLastWeek();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }


        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
}
