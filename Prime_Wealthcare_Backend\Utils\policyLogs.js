const knex = require('knex');
const knexConfig = require('../knexfile');
const db = knex(knexConfig.development);

const savePolicyLog = async (quotationNumber, policyType, requestPayload, responsePayload, status, errorMessage = null) => {
    try {
        await db('policy_logs').insert({
            quotation_number: quotationNumber,
            policy_type: policyType,
            request_payload: JSON.stringify(requestPayload),
            response_payload: JSON.stringify(responsePayload),
            status: status,
            error_message: errorMessage
        });
    } catch (error) {
        console.error('Error saving policy log:', error);
        // Don't throw the error - we don't want logging failures to affect the main flow
    }
};

module.exports = { savePolicyLog };