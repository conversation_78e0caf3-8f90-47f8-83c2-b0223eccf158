import React, { useState } from 'react';
import { Checkbox, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

// Custom styling for the Checkbox
const CustomCheckbox = styled(Checkbox)(({ theme }) => ({
  '&.Mui-checked': {
    color: '#4caf50', // Green color when checked
  },
  '&.MuiCheckbox-root': {
    color: '#9e9e9e', // Gray color when unchecked
  },
  '& .MuiSvgIcon-root': {
    fontSize: 28, // Custom icon size
  },
}));

// CheckboxWithLabel component definition
export default function CheckboxWithLabel({
  label,
  checked = false,
  onChange,
  disabled = false,
  direction = 'column',
  alignRight = false
}) {
  // Internal state for managing checked status if onChange is not provided
  const [isChecked, setIsChecked] = useState(checked);

  // Handle checkbox toggle
  const handleChange = (event) => {
    const newChecked = event.target.checked;

    // If onChange is provided, call it; otherwise, update internal state
    if (onChange) {
      onChange(newChecked);
    } else {
      setIsChecked(newChecked);
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: {
        xs: 'column', // aMobile: Stack vertically
        sm: direction, // Tablet: Use provided direction
        md: direction  // Laptop: Use provided direction
      },
      alignItems: {
        xs: 'center',  // Mobile: Center align
        sm: alignRight ? 'baseline' : 'center', // Tablet & up: Use provided alignment
      },
      justifyContent: {
        xs: 'center',
        sm: 'flex-start'
      },
      ...(alignRight && {
        right: {
          xs: '5px',    // Mobile
          sm: '8px',    // Tablet
          md: '10px'    // Laptop
        }
      })
    }}>
      <CustomCheckbox
        checked={onChange ? checked : isChecked}
        onChange={handleChange}
        disabled={disabled}
        sx={{
          '& .MuiSvgIcon-root': {
            fontSize: {
              xs: 24,  // Mobile: Smaller checkbox
              sm: 26,  // Tablet
              md: 28   // Laptop: Original size
            }
          }
        }}
      />
      <Typography
        variant="body2"
        sx={{
          color: disabled ? '#b0b0b0' : 'gray',
          textWrap: 'nowrap',
          fontSize: {
            xs: '0.875rem',  // Mobile: Smaller text
            sm: '0.9rem',    // Tablet
            md: '1rem'       // Laptop: Normal size
          },
          ...(alignRight && {
            marginBottom: {
              xs: '4px',
              sm: '6px',
              md: '8px'
            }
          })
        }}
      >
        {label}
      </Typography>


    </Box>
  );
}
