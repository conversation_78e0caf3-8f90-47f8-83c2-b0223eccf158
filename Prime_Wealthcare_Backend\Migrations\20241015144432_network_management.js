exports.up = function (knex) {
    return knex.schema.hasTable('network').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('network', function (table) {
                table.increments('id').primary();
                table.string('insurance_company_id', 255).notNullable();
                table.string('hospital_name', 255).notNullable();
                table.string('helpline_number', 20);
                table.string('mobile_number', 20);
                table.string('email_id', 255).nullable();
                table.string('address_line_1', 255);
                table.string('address_line_2', 255);
                table.integer('pincode', 255).notNullable();
                table.string('city', 255).notNullable();
                table.string('pincode_city').references('pincode_city').inTable('locations').onDelete('CASCADE ');
                table.string('state', 255).notNullable();
                table.integer('area').unsigned().nullable()
                    .references('id').inTable('areas') // Foreign key to `areas`
                    .onDelete('CASCADE');

                // Other fields
                table.integer('created_by').notNullable().defaultTo(1);
                table.integer('updated_by').notNullable().defaultTo(1);
                table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
                table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
                table.boolean('status').notNullable().defaultTo(true);
            });
        }
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('network');
};
