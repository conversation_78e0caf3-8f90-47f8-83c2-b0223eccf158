import React, { useEffect } from 'react';
import './index.css';
import { Route, Routes, Navigate, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast, ToastContainer } from 'react-toastify';
import { hideToast } from './redux/slices/toastSlice';
import LoginPage from './pages/login/LoginPage';
import { validateToken } from './redux/actions/action';
import { getToken, setUserSession, clearUserSession } from './utils/storage'
import CrmDashboard from './pages/crm/CrmDashboard';
import InsuranceCompanyPage from './pages/Master/Insurance_Company/InsuranceCompanyPage';
import InsuranceCompanyForm from './pages/Master/Insurance_Company/InsuranceCompanyForm';
import ForgotPassword from './pages/login/ForgotPassword';
import OtpVerification from './pages/login/OtpVerification';
import NewPassword from './pages/login/NewPassword';
import RoleListForm from './pages/Master/Role/RoleListForm';
import RoleListPage from './pages/Master/Role/RoleListPage';
import AreaListPage from './pages/Master/Area_Management/AreaListPage';
import AreaListForm from './pages/Master/Area_Management/AreaListForm';
import MainProductPage from './pages/Master/Main_Product/MainProductPage';
import MainProductForm from './pages/Master/Main_Product/MainProductForm';
import InsuranceBranchForm from './pages/Master/Insurance_Branch/InsuranceBranchForm';
import InsuranceBranchPage from './pages/Master/Insurance_Branch/InsuranceBranchPage';
import ProductMasterPage from './pages/Master/Product_Master/ProductMasterPage';
import ProductMasterForm from './pages/Master/Product_Master/ProductMasterForm';
import ImfBranchPage from './pages/Master/Imf_Branch/ImfBranchPage';
import ImfBranchForm from './pages/Master/Imf_Branch/ImfBranchForm';
import SubProductPage from './pages/Master/Sub_Product/SubProductPage';
import SubProductForm from './pages/Master/Sub_Product/SubProductForm';
import Dashboard from './pages/dashboard/Dashboard';
import CommissionRatePage from './pages/Commission/Commission_Rate/CommissionRatePage'
import CommissionRateListPage from './pages/Commission/Commission_Rate/CommissionRateListPage';
import CommissionRateEditForm from './pages/Commission/Commission_Rate/CommissionRateEditForm'
import EndorsmentTypePage from './pages/Master/Endorsment_Type/EndorsmentTypePage';
import EndorsmentTypeForm from './pages/Master/Endorsment_Type/EndorsmentTypeForm';
import DiseaseMasterPage from './pages/Master/Disease_Master/DiseaseMasterPage';
import DiseaseMasterForm from './pages/Master/Disease_Master/DiseaseMasterForm';
import NetworkPage from './pages/Master/Network/NetworkPage';
import NetworkForm from './pages/Master/Network/NetworkForm';
import Employee_Personal_Information from './pages/User/Employee/Employee_Personal_Information';
import Employee_address from './pages/User/Employee/Employee_address';
import Employee_masterPage from './pages/User/Employee/Employee_masterPage';
import AgentMasterPage from './pages/agent/Agent/agentMasterPage'
import AgentMasterOverviewPage from './pages/agent/Agent/agentMasterOverviewPage';
import AgentPersonalInformation from './pages/agent/Agent/AgentPersonalInformation';
import AgentAddress from './pages/agent/Agent/agentAddress';
import { getAllPickLists } from './redux/actions/action';
import EmployeeOverviewPage from './pages/User/Employee/EmployeeOverviewPage';

import CustomerMasterPage from './pages/customer/CustomerMaster';
import CustomerPersonalInfo from './pages/customer/CustomerPersonalInfo';
import CustomerAddress from './pages/customer/CustomerAddress';
import Customer_memberInfo from './pages/customer/Customer_memberInfo';
import CustomerGrouping from './pages/customer/CustomerGrouping';
import Customer_follow_up_create from './pages/customer/Customer_follow_up_create';
import CustomerDocumentationUpload from './pages/customer/CustomerDocumentationUpload';

import CreateProposal from './pages/proposals/CreateProposal';
import CreateRenewal from './pages/proposals/CreateRenewal';
import ProposalListPage from './pages/proposals/ProposalListPage';


import QuotationPage from './pages/quotation/QuotationPage';
import QuotationOverview from './pages/quotation/QuotationOverview';
import QuickQuotationPage from './pages/quotation/quickQuotation/QuickQuotationPage';
import QuickQuotationList from './pages/quotation/quickQuotation/QuickQuotationList';
import QuotationsListPage from './pages/quotation/QuotatonsListPage';

import PageRightAccess from './pages/pageRightAccess/PageRightAccess'
import withAccessControl from './components/withAccessControl';

import Loader from './components/Loader';
import PaymentForm from './pages/proposals/PaymentForm';
import { PersistGate } from 'redux-persist/integration/react';
import { persistor } from './redux/store';
import SalaryForm from './pages/User/Employee/SalaryForm';
import AgentLoans from './pages/loan/agentLoan/AgentLoans';
import EmployeeLoans from './pages/loan/employeeLoan/EmployeeLoans';
import AgentLoansForm from './pages/loan/agentLoan/AgentLoansForm';
import EmployeeLoansForm from './pages/loan/employeeLoan/EmployeeLoansForm';
import AgentEmiDetails from './pages/loan/agentLoan/AgentEmiDetails';
import EmployeeEmiDetails from './pages/loan/employeeLoan/EmployeeEmiDetails';
import EmployeeBankDetails from './pages/User/Employee/Employee_Bank_Details';
import AgentBankDetails from './pages/agent/Agent/agentBankDetails';
import CKYCForm from './pages/proposals/CKYCForm';
import TaskBoard from './pages/User/Tasks/TaskBoard';
import ProposalRollOver from './pages/proposals/ProposalRollOver';
import ProposalMigration from './pages/proposals/ProposalMigration';
import Reports from './pages/reports/Reports';
import GenerateRenewals from './pages/Renewals/GenerateRenewals';
import RenewalsList from './pages/Renewals/RenewalsList';

const ProtectedRoute = ({ isAuthenticated, children }) => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    if (!isAuthenticated && !token) {
        return <Navigate to="/" replace />; // Redirect to login if not authenticated
    }

    return children; // Render protected component
};

// Wrap your components with access control
const ProtectedDashboard = withAccessControl(Dashboard, 'Dashboard', 'CRM Dashboard');

//MASTER PAGES
const ProtectedInsuranceCompanyPage = withAccessControl(InsuranceCompanyPage, 'Master', 'Insurance Company');
const ProtectedInsuranceCompanyForm = withAccessControl(InsuranceCompanyForm, 'Master', 'Insurance Company Form');
const ProtectedRoleListForm = withAccessControl(RoleListForm, 'Master', 'Role List Form');
const ProtectedRoleListPage = withAccessControl(RoleListPage, 'Master', 'Role Management');
const ProtectedAreaListPage = withAccessControl(AreaListPage, 'Master', 'Area Master');
const ProtectedAreaListForm = withAccessControl(AreaListForm, 'Master', 'Area Form');
const ProtectedMainProductPage = withAccessControl(MainProductPage, 'Master', 'Main Product');
const ProtectedMainProductForm = withAccessControl(MainProductForm, 'Master', 'Main Product Form');
const ProtectedInsuranceBranchPage = withAccessControl(InsuranceBranchPage, 'Master', 'Insurance Branch');
const ProtectedInsuranceBranchForm = withAccessControl(InsuranceBranchForm, 'Master', 'Insurance Branch Form');
const ProtectedProductMasterPage = withAccessControl(ProductMasterPage, 'Master', 'Product Master');
const ProtectedProductMasterForm = withAccessControl(ProductMasterForm, 'Master', 'Product Master Form');
const ProtectedImfBranchPage = withAccessControl(ImfBranchPage, 'Master', 'IMF Branch');
const ProtectedImfBranchForm = withAccessControl(ImfBranchForm, 'Master', 'IMF Branch Form');
const ProtectedSubProductPage = withAccessControl(SubProductPage, 'Master', 'Sub Product');
const ProtectedSubProductForm = withAccessControl(SubProductForm, 'Master', 'Sub Product Form');
//const ProtectedCommissionRatePage = withAccessControl(CommissionRatePage, 'Commission', 'Commission Rate');
const ProtectedEndorsmentTypePage = withAccessControl(EndorsmentTypePage, 'Master', 'Endorsement Type');
const ProtectedEndorsmentTypeForm = withAccessControl(EndorsmentTypeForm, 'Master', 'Endorsement Type Form');
const ProtectedDiseaseMasterPage = withAccessControl(DiseaseMasterPage, 'Master', 'Disease Master');
const ProtectedDiseaseMasterForm = withAccessControl(DiseaseMasterForm, 'Master', 'Disease Master Form');
const ProtectedNetworkPage = withAccessControl(NetworkPage, 'Master', 'Network Master');
const ProtectedNetworkForm = withAccessControl(NetworkForm, 'Master', 'Network Form');

const ProtectedEmployee_masterPage = withAccessControl(Employee_masterPage, 'User Management', 'Employee');
const ProtectedEmployee_Personal_Information = withAccessControl(Employee_Personal_Information, 'User Management', 'Employee Personal Information');
const ProtectedEmployeeOverviewPage = withAccessControl(EmployeeOverviewPage, 'User Management', 'Employee Overview');
const ProtectedEmployee_address = withAccessControl(Employee_address, 'User Management', 'Employee Address');
const ProtectedSalaryForm = withAccessControl(SalaryForm, 'User Management', 'Salary Form');

const ProtectedAgentMasterPage = withAccessControl(AgentMasterPage, 'Agent', 'Agent Master');
const ProtectedAgentMasterOverviewPage = withAccessControl(AgentMasterOverviewPage, 'Agent', 'Agent Overview');
const ProtectedAgentPersonalInformation = withAccessControl(AgentPersonalInformation, 'Agent', 'Agent Personal Information');
const ProtectedAgentAddress = withAccessControl(AgentAddress, 'Agent', 'Agent Address');

// Customer Protected Components
const ProtectedCustomerMasterPage = withAccessControl(CustomerMasterPage, 'Customer', 'Customer Master');
const ProtectedCustomerPersonalInfo = withAccessControl(CustomerPersonalInfo, 'Customer', 'Customer Personal Information');
const ProtectedCustomerAddress = withAccessControl(CustomerAddress, 'Customer', 'Customer Address');
const ProtectedCustomer_memberInfo = withAccessControl(Customer_memberInfo, 'Customer', 'Customer Member Information');
const ProtectedCustomerGrouping = withAccessControl(CustomerGrouping, 'Customer', 'Customer Grouping');

// Protect Customer Documentation and Follow-up routes
const ProtectedCustomerDocumentUpload = withAccessControl(CustomerDocumentationUpload, 'Customer', 'Documents');
const ProtectedCustomerFollowUp = withAccessControl(Customer_follow_up_create, 'Customer', 'Follow Up');

// Protect Proposal related routes
const ProtectedCreateRenewal = withAccessControl(CreateRenewal, 'Proposal', 'Renewal Create');
const ProtectedPaymentForm = withAccessControl(PaymentForm, 'Proposal', 'Payment Form');
const ProtectedCKYCForm = withAccessControl(CKYCForm, 'Proposal', 'CKYC Form');
const ProtectedProposalRollOver = withAccessControl(ProposalRollOver, 'Proposal', 'Roll Over');
const ProtectedProposalMigration = withAccessControl(ProposalMigration, 'Proposal', 'Migration');

const ProtectedQuotationsListPage = withAccessControl(QuotationsListPage, 'Quotation', 'Quotation List');
const ProtectedQuotationPage = withAccessControl(QuotationPage, 'Quotation', 'Quotation Create');
const ProtectedQuotationOverview = withAccessControl(QuotationOverview, 'Quotation', 'Quotation Overview');

const ProtectedQuickQuotationPage = withAccessControl(QuickQuotationPage, 'Quick Quotation', 'Quick Quotation Create');
const ProtectedQuickQuotationList = withAccessControl(QuickQuotationList, 'Quick Quotation', 'Quick Quotation Overview');

const ProtectedPageRightAccess = withAccessControl(PageRightAccess, 'Master', 'Page Right Access');
const ProtectedCreateProposal = withAccessControl(CreateProposal, 'Proposal', 'Proposal Create');
const ProtectedProposalListPage = withAccessControl(ProposalListPage, 'Proposal', 'Proposal List');

const ProtectedTaskBoard = withAccessControl(TaskBoard, 'Task Management', 'Task Board');

// Protect Employee Loan routes
const ProtectedEmployeeLoans = withAccessControl(EmployeeLoans, 'Loan', 'Employee Loan');
const ProtectedEmployeeLoansForm = withAccessControl(EmployeeLoansForm, 'Loan', 'Employee Loan Form');
const ProtectedEmployeeEmiDetails = withAccessControl(EmployeeEmiDetails, 'Loan', 'Employee Loan EMI');

// Protect Agent Loan routes
const ProtectedAgentLoans = withAccessControl(AgentLoans, 'Loan', 'Agent Loan');
const ProtectedAgentLoansForm = withAccessControl(AgentLoansForm, 'Loan', 'Agent Loan Form');
const ProtectedAgentEmiDetails = withAccessControl(AgentEmiDetails, 'Loan', 'Agent Loan EMI');

// Protect Renewals routes
const ProtectedGenerateRenewals = withAccessControl(GenerateRenewals, 'Renewals', 'Generate Renewals');
const ProtectedRenewalsList = withAccessControl(RenewalsList, 'Renewals', 'Generate Renewals');

// Protect Reports routes
const ProtectedReports = withAccessControl(Reports, 'Reports', 'Business Report');

// Protect Bank Detail routes
const ProtectedEmployeeBankDetails = withAccessControl(EmployeeBankDetails, 'User Management', 'Employee Bank Details');
const ProtectedAgentBankDetails = withAccessControl(AgentBankDetails, 'Agent', 'Agent Bank Details');

function App() {
    const dispatch = useDispatch();
    const { message, type, isVisible } = useSelector(state => state.toast);
    const navigate = useNavigate();

    useEffect(() => {
        const initializeAuth = async () => {
            const token = getToken();

            if (token) {
                try {
                    const result = await dispatch(validateToken(token)).unwrap();

                    if (result?.token) {
                        const isRemembered = !!localStorage.getItem('token');

                        // Set user session with the validated token
                        setUserSession(result.token, isRemembered);

                        // Update Redux store with user data
                        dispatch({
                            type: 'auth/setUser',
                            payload: result.user
                        });
                    }
                } catch (error) {
                    console.error('❌ Token validation failed:', error);
                    clearUserSession();
                    navigate('/');
                }
            }
        };

        initializeAuth();
    }, [dispatch, navigate]);


    useEffect(() => {
        dispatch(getAllPickLists());
    }, []);

    useEffect(() => {
        if (isVisible) {
            toast[type](message);
            setTimeout(() => {
                dispatch(hideToast());
            }, 3000);
        }
    }, [isVisible, message, type, dispatch]);

    useEffect(() => {
        const token = getToken();

        if (token) {
            let idleTimer;
            const IDLE_TIMEOUT = process.env.REACT_APP_IDLE_TIMEOUT || 600000;

            const handleUserActivity = () => {
                clearTimeout(idleTimer);
                idleTimer = setTimeout(() => {
                    const currentToken = getToken();
                    if (currentToken) {
                        clearUserSession();
                        toast.info('Session expired due to inactivity. Please login again.');
                        navigate('/');
                    }
                }, IDLE_TIMEOUT);
            };

            // Initialize timer
            handleUserActivity();

            // Add event listeners for user activity
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
            events.forEach(event => {
                document.addEventListener(event, handleUserActivity);
            });

            // Cleanup
            return () => {
                clearTimeout(idleTimer);
                events.forEach(event => {
                    document.removeEventListener(event, handleUserActivity);
                });
            };
        }
    }, [navigate]);

    const isLoading = useSelector(state => state.loading.isLoading);

    return (
        <PersistGate loading={<Loader />} persistor={persistor}>
            {isLoading ? (
                <Loader />
            ) : (
                <div className="App">
                    <ToastContainer theme="colored" position="top-center" />
                    <Routes>
                        {/* Public Routes */}
                        <Route path="/" element={<LoginPage />} />
                        <Route path="/forgot-password" element={<ForgotPassword />} />
                        <Route path="/otp-verification" element={<OtpVerification />} />
                        <Route path="/new-password" element={<NewPassword />} />

                        {/* Add Quick Quotation as a public route */}
                        <Route path="/quick-quotation" element={<QuickQuotationPage />} />
                        <Route path="/quick-quotation-overview" element={<QuickQuotationList />} />

                        {/* Protected Dashboard Routes */}
                        <Route
                            path="/dashboard"
                            element={
                                <ProtectedRoute>
                                    <Dashboard />
                                </ProtectedRoute>
                            }
                        >
                            <Route path="crm-dashboard" element={<CrmDashboard />} />

                            {/* Insurance Company Routes */}
                            <Route path="insurance-company" element={<ProtectedInsuranceCompanyPage />} />
                            <Route path="insurance-form" element={<ProtectedInsuranceCompanyForm />} />
                            <Route path="insurance-company/edit/:id" element={<ProtectedInsuranceCompanyForm />} />

                            {/* Role Routes */}
                            <Route path="role-list" element={<ProtectedRoleListPage />} />
                            <Route path="role-list/edit/:id" element={<ProtectedRoleListForm />} />
                            <Route path="role-form" element={<ProtectedRoleListForm />} />

                            {/* Area Routes */}
                            <Route path="area-list" element={<ProtectedAreaListPage />} />
                            <Route path="area-form" element={<ProtectedAreaListForm />} />
                            <Route path="area-form/edit/:id" element={<ProtectedAreaListForm />} />

                            {/* Product Routes */}
                            <Route path="main-product-list" element={<ProtectedMainProductPage />} />
                            <Route path="main-product-form" element={<ProtectedMainProductForm />} />
                            <Route path="main-product-form/edit/:id" element={<ProtectedMainProductForm />} />

                            {/* Insurance Branch Routes */}
                            <Route path="insurance-branch" element={<ProtectedInsuranceBranchPage />} />
                            <Route path="insurance-branch-form" element={<ProtectedInsuranceBranchForm />} />
                            <Route path="insurance-branch/edit/:id" element={<ProtectedInsuranceBranchForm />} />

                            {/* Product Master Routes */}
                            <Route path="product-master" element={<ProtectedProductMasterPage />} />
                            <Route path="product-master-form" element={<ProtectedProductMasterForm />} />
                            <Route path="product-master-form/edit/:id" element={<ProtectedProductMasterForm />} />

                            {/* IMF Branch Routes */}
                            <Route path="imf-branch" element={<ProtectedImfBranchPage />} />
                            <Route path="imf-branch-form" element={<ProtectedImfBranchForm />} />
                            <Route path="imf-branch-form/edit/:id" element={<ProtectedImfBranchForm />} />

                            {/* Sub Product Routes */}
                            <Route path="sub-product" element={<ProtectedSubProductPage />} />
                            <Route path="sub-product-form" element={<ProtectedSubProductForm />} />
                            <Route path="sub-product-form/edit/:id" element={<ProtectedSubProductForm />} />

                            {/* Commission Routes */}
                            <Route path="commission-rate" element={<CommissionRatePage />} />
                            <Route path="commission-rate-list" element={<CommissionRateListPage />} />
                            <Route path="commission-rate-edit-form/:id" element={<CommissionRateEditForm />} />

                            {/* Endorsement Routes */}
                            <Route path="endorsment-type" element={<ProtectedEndorsmentTypePage />} />
                            <Route path="endorsment-type-form" element={<ProtectedEndorsmentTypeForm />} />
                            <Route path="endorsment-type-form/edit/:id" element={<ProtectedEndorsmentTypeForm />} />

                            {/* Disease Master Routes */}
                            <Route path="disease-master" element={<ProtectedDiseaseMasterPage />} />
                            <Route path="disease-master-form" element={<ProtectedDiseaseMasterForm />} />
                            <Route path="disease-master-form/edit/:id" element={<ProtectedDiseaseMasterForm />} />

                            {/* Network Routes */}
                            <Route path="network-list" element={<ProtectedNetworkPage />} />
                            <Route path="network-form" element={<ProtectedNetworkForm />} />
                            <Route path="network-form/edit/:id" element={<ProtectedNetworkForm />} />

                            {/* Employee Routes */}
                            <Route path="employee-master" element={<ProtectedEmployee_masterPage />} />
                            <Route path="employee-personal-information" element={<ProtectedEmployee_Personal_Information />} />
                            <Route path="employee-personal-information/:id" element={<ProtectedEmployee_Personal_Information />} />
                            <Route path="employee-personal-information/edit/:id" element={<ProtectedEmployee_Personal_Information />} />
                            <Route path="employee-master-overview/:id" element={<ProtectedEmployeeOverviewPage />} />
                            <Route path="employee-address" element={<ProtectedEmployee_address />} />
                            <Route path="employee-address/:id" element={<ProtectedEmployee_address />} />
                            <Route path="salary-form/:id" element={<ProtectedSalaryForm />} />
                            <Route path="salary-form/create/:id" element={<ProtectedSalaryForm />} />
                            <Route path="salary-form/view/:id" element={<ProtectedSalaryForm />} />

                            {/* Agent Routes */}
                            <Route path="agent-master" element={<ProtectedAgentMasterPage />} />
                            <Route path="agent-master-overview/:id" element={<ProtectedAgentMasterOverviewPage />} />
                            <Route path="agent-personal-information" element={<ProtectedAgentPersonalInformation />} />
                            <Route path="agent-personal-information/:id" element={<ProtectedAgentPersonalInformation />} />
                            <Route path="agent-address" element={<ProtectedAgentAddress />} />
                            <Route path="agent-address/:id" element={<ProtectedAgentAddress />} />
                            {/* Customer Routes  */}

                            <Route path='customer-master' element={<ProtectedCustomerMasterPage />} />
                            <Route path='customer-personal-information' element={<ProtectedCustomerPersonalInfo />} />
                            <Route path='customer-personal-information/:id' element={<ProtectedCustomerPersonalInfo />} />
                            <Route path='customer-address' element={<ProtectedCustomerAddress />} />
                            <Route path='customer-member-information' element={<ProtectedCustomer_memberInfo />} />
                            <Route path='customer-grouping' element={<ProtectedCustomerGrouping />} />
                            <Route path='customer-follow-up' element={<ProtectedCustomerFollowUp />} />
                            <Route path='customer-address/:id' element={<ProtectedCustomerAddress />} />
                            <Route path='customer-member-information/:id' element={<ProtectedCustomer_memberInfo />} />
                            <Route path='customer-grouping/:id' element={<ProtectedCustomerGrouping />} />
                            <Route path='customer-follow-up/:id' element={<ProtectedCustomerFollowUp />} />
                            <Route path="customer-documentation-upload/:id" element={<ProtectedCustomerDocumentUpload />} />

                            {/* Quotation Routes */}
                            <Route path="quotations" element={<ProtectedQuotationPage />} />
                            <Route path="quotations/:id" element={<ProtectedQuotationPage />} />
                            <Route path="quotation-overview/:id" element={<ProtectedQuotationOverview />} />
                            <Route path="quotation-overview/:tableSource/:id" element={<ProtectedQuotationOverview />} />

                            <Route path='quotations-list' element={<ProtectedQuotationsListPage />} />
                            <Route path="page-right-access" element={<ProtectedPageRightAccess />} />
                            <Route path="payment-form" element={<ProtectedPaymentForm />} />


                            <Route path="quick-quotation" element={<ProtectedQuickQuotationPage />} />
                            <Route path="quick-quotation-overview" element={<ProtectedQuickQuotationList />} />

                            {/*Proposal Routes*/}
                            <Route path='create-proposal' element={<ProtectedCreateProposal />} />
                            <Route path='create-proposal/:id' element={<ProtectedCreateProposal />} />
                            <Route path='edit-proposal/:id' element={<ProtectedCreateProposal />} />
                            <Route path='proposal-renewal' element={<ProtectedCreateRenewal />} />
                            <Route path="proposals" element={<ProtectedProposalListPage />} />
                            <Route path='ckyc-form' element={<ProtectedCKYCForm />} />

                            {/*Agent Loan Routes*/}
                            <Route path='agent-loans' element={<ProtectedAgentLoans />} />
                            <Route path='agent-loan-form' element={<ProtectedAgentLoansForm />} />
                            <Route path='agent-loan-form/:id' element={<ProtectedAgentLoansForm />} />
                            <Route path='agent-loan-form/view/:id' element={<ProtectedAgentLoansForm />} />
                            <Route path='agent-emi-details/:id' element={<ProtectedAgentEmiDetails />} />
                            <Route path='agent-emi-details/view/:id' element={<ProtectedAgentEmiDetails />} />

                            <Route path='employee-bank-details-form/:id' element={<ProtectedEmployeeBankDetails />} />
                            <Route path='employee-bank-details-form/edit/:id' element={<ProtectedEmployeeBankDetails />} />
                            <Route path='/dashboard/employee-bank-details-form/view/:id' element={<ProtectedEmployeeBankDetails />} />

                            <Route path="agent-bank-details-form/:id" element={<ProtectedAgentBankDetails />} />
                            <Route path="agent-bank-details-form/edit/:id" element={<ProtectedAgentBankDetails />} />
                            <Route path="/dashboard/agent-bank-details-form/view/:id" element={<ProtectedAgentBankDetails />} />

                            {/*Employee Loan Routes*/}
                            <Route path='employee-loans' element={<ProtectedEmployeeLoans />} />
                            <Route path='employee-loan-form' element={<ProtectedEmployeeLoansForm />} />
                            <Route path='employee-loan-form/:id' element={<ProtectedEmployeeLoansForm />} />
                            <Route path='employee-loan-form/view/:id' element={<ProtectedEmployeeLoansForm />} />
                            <Route path='employee-emi-details/:id' element={<ProtectedEmployeeEmiDetails />} />
                            <Route path='employee-emi-details/view/:id' element={<ProtectedEmployeeEmiDetails />} />
                            <Route path="task-board" element={<ProtectedTaskBoard />} />

                          {/*   <Route path='employee-loans' element={<EmployeeLoans />} />
                            <Route path='employee-loan-form' element={<EmployeeLoansForm />} />
                            <Route path='employee-loan-form/:id' element={<EmployeeLoansForm />} />
                            <Route path='employee-loan-form/view/:id' element={<EmployeeLoansForm />} />
                            <Route path='employee-emi-details/:id' element={<EmployeeEmiDetails />} />
                            <Route path='employee-emi-details/view/:id' element={<EmployeeEmiDetails />} />
                            <Route path="task-board" element={<TaskBoard />} /> */}
                            {/* Roll Over */}
                            <Route path="proposal-roll-over" element={<ProtectedProposalRollOver />} />
                            <Route path="proposal-roll-over/:id" element={<ProtectedProposalRollOver />} />
                            <Route path="proposal-migration" element={<ProtectedProposalMigration />} />
                            <Route path="proposal-migration/:id" element={<ProtectedProposalMigration />} />
                            <Route path="reports" element={<ProtectedReports />} />
                            {/* Renewals */}
                            <Route path="generate-renewals" element={<ProtectedGenerateRenewals />} />
                            <Route path='renewals-list' element={<ProtectedRenewalsList />} />


                        </Route>

                        {/* Catch-All Route */}
                        <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                </div>
            )}
        </PersistGate>
    );
}

// Add this to sync between tabs
if (typeof window !== 'undefined') {
    window.addEventListener('storage', (e) => {
        if (e.key === 'persist:root') {
            window.location.reload();
        }
    });
}

export default App;
