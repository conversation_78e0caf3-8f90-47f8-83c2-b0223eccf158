import * as React from 'react';
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: '100%',
  width: '100%',
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
});

export default function UploadButtonAsField({ disabled = false, accept = '' }) {
  const handleFileChange = (event) => {
    //   
  };

  return (
    <div>
      <Button
        component="label"
        variant="outlined"
        startIcon={<CloudUploadIcon sx={{ fontSize: 30 }} />}
        disabled={disabled}
        sx={{
          width: '100%',
          height: '56px',
          padding: '16.5px 14px',
          textTransform: 'none',
          justifyContent: 'flex-start',
          fontSize: '16px',
          color: 'grey',
          borderColor: 'grey',
          backgroundColor: disabled ? '#e0e0e0' : 'transparent',
          '&:hover': {
            borderColor: 'grey',
          },
          '& .MuiButton-startIcon': {
            marginRight: '8px',
          },
        }}
      >
        {disabled ? "File upload" : "Upload file"}
        <VisuallyHiddenInput 
          type="file" 
          onChange={handleFileChange} 
          disabled={disabled} 
          accept={accept}
        />
      </Button>
    </div>
  );
}
