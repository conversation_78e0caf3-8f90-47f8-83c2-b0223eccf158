const knex = require('../knexfile');

const cleanupOldLogs = async () => {
    try {
        const daysToKeep = 7; // Retain logs for 30 days
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        const deletedRows = await knex('quotations_logs')
            .where('created_at', '<', cutoffDate)
            .del();

        console.log(`Deleted ${deletedRows} old SOAP logs.`);
    } catch (error) {
        console.error('Error cleaning up old logs:', error);
    }
};

module.exports = { cleanupOldLogs };
