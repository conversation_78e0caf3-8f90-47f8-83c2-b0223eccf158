exports.up = function (knex) {
    return knex.schema.hasTable('endorsment_type').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('endorsment_type', function (table) {
                table.increments('id').primary();
                table.string('endorsment_type', 255).notNullable();
                table.string('endorsment_name', 255).notNullable().unique();
                table.string('endorsment_description', 512).notNullable();
                table.integer('created_by').notNullable().defaultTo(1);
                table.integer('updated_by').notNullable().defaultTo(1);
                table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
                table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
                table.boolean('status').notNullable().defaultTo(true);
            });
        }
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('endorsment_type');
};