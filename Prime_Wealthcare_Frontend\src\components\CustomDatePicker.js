import React from 'react';
import { TextField } from '@mui/material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

const CustomDatePicker = ({ value, onChange, width = '100%', ...props }) => {
  // Convert value to dayjs object if it isn't already
  const dayjsValue = value ? dayjs(value) : null;

  // Handle date change
  const handleDateChange = (newValue) => {
    if (newValue && newValue.isValid()) {
      // Format the date before passing it to the onChange function
      onChange(newValue.format('YYYY-MM-DD'));
    } else {
      onChange(''); // Handle invalid date or reset the value
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        value={dayjsValue} // Ensure dayjs object is passed
        onChange={handleDateChange}
        renderInput={(params) => (
          <TextField {...params} fullWidth style={{ width }} />
        )}
        {...props}
      />
    </LocalizationProvider>
  );
};

export default CustomDatePicker;
