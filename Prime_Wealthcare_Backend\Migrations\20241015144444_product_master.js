exports.up = function (knex) {
  return knex.schema.hasTable('product_master').then(function (exists) {
    if (!exists) {
      return knex.schema.createTable('product_master', function (table) {
        table.increments('id').primary();

        // Foreign key to main_product table
        table.integer('main_product_id').unsigned().notNullable();
        table.foreign('main_product_id').references('id').inTable('main_product').onDelete('CASCADE');

        // Foreign key to insurance_company table
        table.integer('insurance_company_id').unsigned().notNullable();
        table.foreign('insurance_company_id').references('id').inTable('insurance_company').onDelete('CASCADE');

        // Product name
        table.string('product_name', 255).notNullable().unique();

        // Other fields
        table.integer('created_by').notNullable().defaultTo(1);
        table.integer('updated_by').notNullable().defaultTo(1);
        table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
        table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
        table.boolean('status').notNullable().defaultTo(true);
      });
    }
  });
};

exports.down = function (knex) {
  return knex.schema.dropTableIfExists('product_master');
};
