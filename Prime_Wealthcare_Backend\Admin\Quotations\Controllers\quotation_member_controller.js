const QuotationMemberModel = require('../Models/quotation_member_model');

// Get all members for a quotation
const getAllMembersByQuotationId = async (req, res) => {
  const { quotationId } = req.params;
  try {
    const members = await QuotationMemberModel.getAllMembersByQuotationId(quotationId);
    res.status(200).json(members);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching members', error });
  }
};

// Get a single member by ID
const getMemberById = async (req, res) => {
  const { id } = req.params;
  try {
    const member = await QuotationMemberModel.getMemberById(id);
    if (!member) {
      return res.status(404).json({ message: 'Member not found' });
    }
    res.status(200).json(member);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching member', error });
  }
};

// Create a new member
const createMember = async (req, res) => {
  const memberData = req.body;
  try {
    const newMember = await QuotationMemberModel.createMember(memberData);
    res.status(201).json(newMember);
  } catch (error) {
    res.status(500).json({ message: 'Error creating member', error });
  }
};

// Update a member
const updateMember = async (req, res) => {
  const { id } = req.params;
  const updatedData = req.body;
  try {
    const updatedMember = await QuotationMemberModel.updateMember(id, updatedData);
    if (!updatedMember) {
      return res.status(404).json({ message: 'Member not found' });
    }
    res.status(200).json(updatedMember);
  } catch (error) {
    res.status(500).json({ message: 'Error updating member', error });
  }
};

// Delete a member
const deleteMember = async (req, res) => {
  const { id } = req.params;
  try {
    const deleted = await QuotationMemberModel.deleteMember(id);
    if (!deleted) {
      return res.status(404).json({ message: 'Member not found' });
    }
    res.status(200).json({ message: 'Member deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting member', error });
  }
};

module.exports = {
  getAllMembersByQuotationId,
  getMemberById,
  createMember,
  updateMember,
  deleteMember,
};
