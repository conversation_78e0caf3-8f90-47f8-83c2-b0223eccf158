import React from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Button } from '@mui/material';

const calculateAge = (dob) => {
    if (!dob) return '';

    // Parse the date in dd/mm/yyyy format
    const [day, month, year] = dob.split('/');
    const birthDate = new Date(year, month - 1, day); // month is 0-indexed
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    // Adjust age if birthday hasn't occurred this year
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age.toString();
};

const ExportToPDF = ({ data, customerInfo, fileName = 'Quotation.pdf' }) => {
    const logoMapping = {
        'FUTURE GENRALI INDIA INSURANCE CO. LTD.': '/future-generali-logo.png',
        'FUTURE GENRALI INDIA INSURANCE CO. LTD.': '/future-generali-logo.png',
        'Another Company': '/another-company-logo.png',
        // Add more mappings as needed
    };
    const handleExport = async () => {
        try {
            const doc = new jsPDF({ orientation: 'portrait' });
            const pageWidth = doc.internal.pageSize.getWidth();
            const pageHeight = doc.internal.pageSize.getHeight();

            // Load Images
            const mainLogoData = await loadImage('/prime_logo-01.png');
            const selectedCompany = customerInfo.insuranceCompany?.trim() || '';
            const fgLogoPath = logoMapping[selectedCompany] || '/logo_icon.png';
            const fgLogoData = await loadImage(fgLogoPath);

            // Calculate main logo dimensions maintaining aspect ratio
            const logoWidth = 60;
            const mainLogoAspectRatio = mainLogoData.width / mainLogoData.height;
            const mainLogoHeight = logoWidth / mainLogoAspectRatio;

            const fgLogoWidth = 40;
            const fgLogoHeight = 20;

            // Draw Border
            doc.setDrawColor(0, 0, 0);
            doc.rect(5, 5, pageWidth - 10, pageHeight - 10);

            // Main Logo with preserved aspect ratio
            const logoYPosition = 10;
            doc.addImage(
                mainLogoData.dataUrl,
                'PNG',
                (pageWidth - logoWidth) / 2,
                logoYPosition,
                logoWidth,
                mainLogoHeight,
                undefined,
                'FAST' // Use FAST compression
            );

            // Greeting Section
            const greetingYPosition = logoYPosition + mainLogoHeight + 8;
            doc.setFontSize(14);
            doc.text(`Dear ${customerInfo.fullName || 'Customer'}`, pageWidth / 2, greetingYPosition, { align: 'center' });
            //doc.text('Please find below your health insurance quotation for', pageWidth / 2, greetingYPosition + 10, { align: 'center' });
            // Customize greeting based on product type
           const greetingText = customerInfo.productType?.toUpperCase().includes('FG ACCIDENT SURAKSHA') 
            ? 'Please find below your Personal Accident insurance quotation for'
            : 'Please find below your health insurance quotation for';
            doc.text(greetingText, pageWidth / 2, greetingYPosition + 10, { align: 'center' });


            // FG Logo
            const fgLogoYPosition = greetingYPosition + 14;
            doc.addImage(
                fgLogoData.dataUrl,
                'PNG',
                (pageWidth - fgLogoWidth) / 2,
                fgLogoYPosition,
                fgLogoWidth,
                fgLogoHeight,
                undefined,
                'FAST' // Use FAST compression
            );

            // Add Product Info
            const productInfoY = fgLogoYPosition + 30;
            doc.setFontSize(14);
            doc.text(
                ` ${customerInfo.productType || 'Product Type'} ${customerInfo.familyType || 'Family Type'} - ${data[0]?.soap_cover_type || 'Accident Surakash'}`,
                pageWidth / 2,
                productInfoY,
                { align: 'center' }
            );

            let finalY = productInfoY ;

            const isFloater = customerInfo.familyType?.toLowerCase().includes('floater');

            if (isFloater) {
                // Group by sum insured for floater type
                const sumInsuredGroups = groupBySumInsured(data);
                Object.entries(sumInsuredGroups).forEach(([sumInsured, groupData], index) => {

                    const tableStartY = index === 0 ? finalY + 20 : finalY + 15; // Adjust starting Y position for subsequent tables
                    finalY = generateTable(doc, groupData, customerInfo, tableStartY);
                });
            } else {
                // For individual type, pass the entire data array directly
                finalY = generateTable(doc, data, customerInfo, finalY + 15);
            }

            // Add verification message in white background with reduced spacing
            doc.setFontSize(12);
            doc.setTextColor(0, 0, 0); // Black text

            // Add text and hyperlink for brochure and policy wordings
            doc.text('Download Product Brochure and Policy Wordings from the below link:', pageWidth / 2, finalY + 5, {
                align: 'center'
            });
            
            doc.setTextColor(0, 0, 255); // Blue color for link
            doc.textWithLink('https://general.futuregenerali.in/customer-service/downloads', pageWidth / 2, finalY + 10, {
                align: 'center',
                url: 'https://general.futuregenerali.in/customer-service/downloads',
                target: '_blank'
            });

            // Reset text color for verification message
            doc.setTextColor(0, 0, 0);
            doc.text(
                'Above quotation is subject to verification by insurance company',
                pageWidth / 2,
                finalY + 16,
                { align: 'center' }
            );

            // Add the new terms and conditions line
            doc.setFontSize(11);
            doc.setFont(undefined, 'italic'); // Make it italic to stand out
            doc.text(
                '*Premium may vary on basis of age slab',
                pageWidth / 2,
                finalY + 22,
                { align: 'center' }
            );

            // Reset font style for remaining text
            doc.setFont(undefined, 'normal');
            doc.setFontSize(12);
            doc.text(
                'For more information, please contact',
                pageWidth / 2,
                finalY + 28, // Adjusted spacing to accommodate new line
                { align: 'center' }
            );

            // Add contact details in white background with reduced spacing
            doc.setFontSize(14);
            doc.setFont(undefined, 'bold');
            doc.text(
                customerInfo.agentName || 'PIYUSH PANDYA',
                pageWidth / 2,
                finalY + 36,
                { align: 'center' }
            );
            doc.text(
                customerInfo.agentRole || 'Risk Manager',
                pageWidth / 2,
                finalY + 41,
                { align: 'center' }
            );

            // Add contact information with reduced spacing
            doc.setFontSize(12);
            doc.setFont(undefined, 'normal');
            doc.text(
                '+91 9725245005',
                pageWidth / 2,
                finalY + 48,
                { align: 'center' }
            );
            doc.text(
                '<EMAIL>',
                pageWidth / 2,
                finalY + 53, // Reduced spacing between phone and email
                { align: 'center' }
            );

            // Add green background footer with reduced spacing
            const footerHeight = 40;
            // const footerY = finalY + 57; // Adjusted to maintain proper spacing from contact details
            // Calculate footer position dynamically
            const footerY = pageHeight - 45; // Adjust this value to fine-tune the position

            doc.setFillColor(0, 128, 128); // Teal/Green color matching the header
            doc.rect(5, footerY, pageWidth - 10, footerHeight, 'F');

            doc.setFontSize(10);
            doc.setTextColor(255, 255, 255); // White text
            doc.text(
                'Regd: 403/404 Bhavani Skyline, Atabhai Road, Nr. Piyusha Fast Food, Opp. Jogger\'s Park, Bhavnagar - 364002',
                pageWidth / 2,
                footerY + 15,
                { align: 'center' }
            );
            doc.text(
                'Website: www.primewealthcare.com',
                pageWidth / 2,
                footerY + 20,
                { align: 'center' }
            );

            // Save PDF
            doc.save(fileName);
        } catch (error) {
            console.error('Error generating PDF:', error);
        }
    };

    const groupBySumInsured = (data) => {
        return data.reduce((acc, item) => {
            const sumInsured = item.soap_sum_insured;
            if (!acc[sumInsured]) {
                acc[sumInsured] = [];
            }
            acc[sumInsured].push(item);
            return acc;
        }, {});
    };

    const loadImage = (src) => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';

            img.onload = () => {
                const canvas = document.createElement('canvas');
                const width = img.width;
                const height = img.height;

                canvas.width = width;
                canvas.height = height;

                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                try {
                    const dataUrl = canvas.toDataURL('image/png', {
                        quality: 1.0,
                        compressionLevel: 6
                    });

                    // Clean up
                    ctx.clearRect(0, 0, width, height);
                    canvas.width = 0;
                    canvas.height = 0;

                    resolve({
                        dataUrl: dataUrl.replace(/^data:image\/(png|jpg);base64,/, ''),
                        width,
                        height
                    });
                } catch (error) {
                    reject(error);
                } finally {
                    // Ensure cleanup
                    img.onload = null;
                    img.onerror = null;
                }
            };

            img.onerror = (err) => {
                console.error(`Failed to load image from ${src}:`, err);
                if (src !== '/logo_icon.png') {
                    loadImage('/logo_icon.png')
                        .then(resolve)
                        .catch(reject);
                } else {
                    reject(new Error(`Failed to load image from ${src}: ${err.message}`));
                }
            };

            img.src = src;
        });
    };

    const generateTable = (doc, data, customerInfo, tableStartY) => {
        const isPAQuotation = customerInfo.productType?.toLowerCase().includes('pa') || 
                             customerInfo.productType?.toLowerCase().includes('accident');
        
        if (isPAQuotation) {
            const quotationData = data[0];
            const members = quotationData.members || [];
            const responses = quotationData.responses || [];

            // Get page dimensions
            const pageHeight = doc.internal.pageSize.getHeight();
            const pageWidth = doc.internal.pageSize.getWidth();
            const footerHeight = 45;
            const marginBottom = 10;

            // Add border to first page
            doc.setDrawColor(0);
            doc.rect(5, 5, pageWidth - 10, pageHeight - 10);

            // Check if any member has TT_suminsured > 0
            const showTTColumn = members.some(member => parseFloat(member.TT_suminsured || 0) > 0);

            // Headers for sum insured table
            const sumInsuredHeaders = [
                // First header row for "Members Sum Insured" text
                [{
                    content: 'Members Sum Insured',
                    colSpan: showTTColumn ? 5 : 4,
                    styles: { 
                        halign: 'center',
                        fillColor: [255, 255, 255],
                        textColor: [0, 0, 0],
                        fontStyle: 'bold'
                    }
                }],
                showTTColumn 
                    ? ['Relation', 'Accidental death', 'Permanent Partial Disabilement', 'Permanent Total Disablement', 'Temporary Total Disablement']
                    : ['Relation', 'Accidental death', 'Permanent Partial Disabilement', 'Permanent Total Disablement']
            ];
            
            // Create rows for sum insured table
            const sumInsuredRows = members.map(member => {
                const row = [
                    member.relation || 'N/A',
                    member.AD_suminsured?.toLocaleString() || 'N/A',
                    member.PP_suminsured?.toLocaleString() || 'N/A',
                    member.PT_suminsured?.toLocaleString() || 'N/A'
                ];
                if (showTTColumn) {
                    row.push(member.TT_suminsured?.toLocaleString() || 'N/A');
                }
                return row;
            });

            // Generate sum insured table
            autoTable(doc, {
                startY: tableStartY - 5, // Reduced space above first table
                head: sumInsuredHeaders,
                body: sumInsuredRows,
                styles: {
                    halign: 'center',
                    fontSize: 12,
                    cellPadding: 2,
                    lineWidth: 0.5,
                    lineColor: [0, 0, 0]
                },
                headStyles: {
                    fillColor: [82, 138, 126],
                    textColor: [255, 255, 255],
                    fontStyle: 'bold'
                },
                margin: { left: 10, right: 10 },
                theme: 'grid'
            });

            let currentY = doc.lastAutoTable.finalY + 15;

            // Calculate total space needed for all duration tables
            const spacePerDurationTable = 5; // Approximate height for each duration table
            const totalSpaceNeeded = responses.length * spacePerDurationTable;
            const availableSpace = pageHeight - currentY - footerHeight - marginBottom;

            // Generate tables for each duration
            responses.forEach((response, index) => {
                // Only create new page if remaining space is insufficient and it's not the first table
                if (currentY + spacePerDurationTable > pageHeight - footerHeight - marginBottom && index > 0) {
                    doc.addPage();
                    doc.setDrawColor(0);
                    doc.rect(5, 5, pageWidth - 10, pageHeight - 10);
                    currentY = 20;
                }
                
                // Premium details table
                const tableHeaders = [
                    [{
                        content: `Duration - ${response.duration} Year${response.duration > 1 ? 's' : ''}`,
                        colSpan: 4,
                        styles: { 
                            halign: 'center',
                            fillColor: [255, 255, 255],
                            textColor: [0, 0, 0],
                            fontStyle: 'bold'
                        }
                    }],
                    ['Net Premium', 'Family Discount', 'Long Term Discount', 'Total Premium']
                ];

                const premiumRow = [
                    Math.round(parseFloat(response.full_payment_premium || 0)).toLocaleString() || 'N/A',
                    Math.round(parseFloat(response.family_discount_amt || 0)).toLocaleString() || 'N/A',
                    Math.round(parseFloat(response.long_term_discount_amount || 0)).toLocaleString() || 'N/A',
                    Math.round(parseFloat(response.total_full_payment || 0)).toLocaleString() || 'N/A'
                ];

                autoTable(doc, {
                    startY: currentY,
                    head: tableHeaders,
                    body: [premiumRow],
                    styles: {
                        halign: 'center',
                        fontSize: 12,
                        cellPadding: 2,
                        lineWidth: 0.5,
                        lineColor: [0, 0, 0]
                    },
                    headStyles: {
                        fillColor: [82, 138, 126],
                        textColor: [255, 255, 255],
                        fontStyle: 'bold'
                    },
                    didParseCell: function(data) {
                        if (data.row.index === 0 && data.row.section === 'head') {
                            data.cell.styles.fillColor = [255, 255, 255];
                            data.cell.styles.textColor = [0, 0, 0];
                        }
                    },
                    margin: { left: 10, right: 10 },
                    theme: 'grid'
                });

                currentY = doc.lastAutoTable.finalY + 15;
            });

            return currentY;
        } else {
            const isFloater = customerInfo.familyType?.toLowerCase().includes('floater');
            const pageWidth = doc.internal.pageSize.getWidth();
            const pageHeight = doc.internal.pageSize.getHeight();
            const footerHeight = 30;
            const marginBottom = footerHeight + 10; // Space for footer plus some padding

            const addPageBorder = () => {
                doc.setDrawColor(0);
                doc.setLineWidth(0.5);
                doc.rect(5, 5, pageWidth - 10, pageHeight - 10);
            };

            addPageBorder();

            if (isFloater) {
                const sumInsuredGroups = data.reduce((acc, item) => {
                    const sumInsured = item.soap_sum_insured;
                    if (!acc[sumInsured]) acc[sumInsured] = [];
                    acc[sumInsured].push(item);
                    return acc;
                }, {});

                let currentY = tableStartY;

                Object.entries(sumInsuredGroups).forEach(([sumInsured, group], index) => {
                    // Calculate exact space needed for this table
                    const headerRowHeight = 15;
                    const dataRowHeight = 12;
                    const summaryRowsHeight = 36; // 3 summary rows
                    const siHeaderHeight = 20;
                    const tablePadding = 10;

                    const estimatedTableHeight =
                        siHeaderHeight +                    // SI header
                        headerRowHeight +                   // Table header row
                        (group.length * dataRowHeight) +    // Data rows
                        summaryRowsHeight +                 // Summary rows (Basic Premium, GST, Net Premium)
                        tablePadding;                       // Extra padding

                    // Calculate remaining space on current page
                    const remainingSpace = pageHeight - currentY - 25;

                    // Check if table fits in remaining space
                    if (estimatedTableHeight > remainingSpace) {
                        doc.addPage();
                        addPageBorder();
                        currentY = 20; // Reset Y position for new page
                    }

                    // Add SI header
                    doc.setFontSize(14);
                    doc.text(
                        `${customerInfo.productType} ${customerInfo.familyType} - SI ${parseInt(sumInsured.replace(/,/g, '')).toLocaleString()}`,
                        pageWidth / 2,
                        currentY,
                        { align: 'center' }
                    );

                    // Add small spacing after header
                    currentY += 10;

                    // Generate table and update currentY
                    currentY = generateSingleTable(doc, group, customerInfo, currentY, true);

                    // Add spacing between tables if not the last table
                    if (index < Object.entries(sumInsuredGroups).length - 1) {
                        currentY += 20;
                    }
                });

                return currentY;
            } else {
                return generateSingleTable(doc, data, customerInfo, tableStartY, false);
            }
        }
    };

    const generateSingleTable = (doc, data, customerInfo, startY, isFloater) => {
        const tableHeaders = ['Name', 'Age', 'Relation', 'Sum Insured    ', 'Premium'];

        // Sort members (SELF first)
        const sortedData = [...data].sort((a, b) => {
            if (a.relation === 'SELF') return -1;
            if (b.relation === 'SELF') return 1;
            return 0;
        });

        // Format table rows
        const tableRows = sortedData.map((item) => [
            item.member_name || '',
            calculateAge(item.insured_dob) || '',
            item.member_relation || '',
            item.soap_sum_insured ? parseInt(item.soap_sum_insured.toString().replace(/,/g, '')).toLocaleString() : '0',
            item.per_person_premium ? parseFloat(item.per_person_premium.toString().replace(/,/g, '')).toLocaleString() : '0'
        ]);

        // Add duration row
        tableRows.push([
            '', '', '', 'Period Of Cover',
            customerInfo?.duration ? customerInfo.duration.toString() : '1 Year'
        ]);

        // Add totals
        const basicPremium = parseFloat(data[0]?.premium_amount || 0);
        const serviceTax = parseFloat(data[0]?.service_tax || 0);
        const netPremium = parseFloat(data[0]?.premium_with_service_tax || 0);

        tableRows.push([
            '', '', '', 'Basic Premium',
            basicPremium.toLocaleString()
        ]);
        tableRows.push([
            '', '', '', 'GST (18.0%)',
            serviceTax.toLocaleString()
        ]);
        tableRows.push([
            '', '', '', 'Net Premium',
            netPremium.toLocaleString()
        ]);

        // Generate table with modified configuration
        autoTable(doc, {
            startY: startY,
            head: [tableHeaders],
            body: tableRows,
            styles: {
                halign: 'center',
                valign: 'middle',
                fontSize: 12,
                lineColor: [0, 0, 0],
                lineWidth: 0.5,
                fillColor: [255, 255, 255],
                minCellHeight: 10,
                cellPadding: 2
            },
            headStyles: {
                fillColor: [82, 138, 126],
                textColor: [255, 255, 255],
                lineWidth: 0.75,
                fontSize: 12,
                fontStyle: 'bold'
            },
            columnStyles: {
                0: { halign: 'center' },
                1: { halign: 'center' },
                2: { halign: 'center' },
                3: { halign: 'center', cellPadding: { right: 10 } },
                4: { halign: 'center' }
            },
            didParseCell: function (data) {
                if (data.row.raw && data.row.raw[3] === 'Net Premium') {
                    data.cell.styles.fillColor = [128, 0, 0];
                    data.cell.styles.textColor = [255, 255, 255];
                    if (data.column.index === 3 || data.column.index === 4) {
                        data.cell.styles.fontStyle = 'bold';
                    }
                }
            },
            margin: { left: 10, right: 10 },
            tableWidth: 'auto',
            willDrawCell: function (data) {
                // Draw all borders
                data.cell.styles.lineWidth = 0.5;
            }
        });

        return doc.lastAutoTable.finalY;
    };


    return (
        <Button
            variant="outlined"
            onClick={handleExport}
            sx={{
                borderColor: 'green',
                color: 'green',
                '&:hover': {
                    borderColor: 'green',
                }
            }}
        >
            Export to PDF
        </Button>
    );
};

export default ExportToPDF;