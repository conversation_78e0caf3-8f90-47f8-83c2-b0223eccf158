import React, { useEffect, useState } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    Collapse,
    Typography,
    TableHead,
    TableRow,
    IconButton,
    Paper,
    TablePagination,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import ViewIcon from '@mui/icons-material/Visibility';
import FilePreviewModal from '../FilePreviewModal';
import VisibilityIcon from '@mui/icons-material/Visibility';

const DetailsTable = ({
    open = true,
    tableHeadings,
    tableData,
    onEdit,
    onView,
    optionsColumn = {
        edit: true,
        view: true
    },
    noDataMessage = "No data present",
    disabled = false
}) => {
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState({ url: '', name: '' });

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handlePreviewClick = (url, name) => {
        setSelectedFile({ url, name });
        setIsModalOpen(true);
    };

    return (
        <>
            <Collapse in={open} timeout="auto" unmountOnExit>
                <TableContainer component={Paper} sx={{
                    width: '100%',
                    marginInline: 'auto',
                    borderRadius: '1rem',
                    border: '1px solid #e0e0e0',
                    opacity: disabled ? 0.6 : 1,
                    pointerEvents: disabled ? 'none' : 'auto',
                    '& .MuiTableCell-head': {
                        borderBottom: '1px solid #e0e0e0'
                    }
                }}>
                    <Table sx={{ tableLayout: 'auto' }} aria-label="customized table">
                        <TableHead>
                            <TableRow>
                                {optionsColumn && (
                                    <TableCell sx={{ borderBottom: '1px solid #e0e0e0', textAlign: 'center' }}>
                                        <strong>Options</strong>
                                    </TableCell>
                                )}
                                {tableHeadings.map((heading, index) => (
                                    <TableCell key={index} sx={{ borderBottom: '10px solid #e0e0e0', textAlign: 'center' }}>
                                        <strong>{heading.replace(/_/g, ' ')}</strong>
                                    </TableCell>
                                ))}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {Array.isArray(tableData) && tableData.length > 0 ? (
                                tableData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                    .map((row, rowIndex) => (
                                        <TableRow key={rowIndex}>
                                            {optionsColumn && (
                                                <TableCell sx={{ display: 'flex', justifyContent: 'center' }}>
                                                    <>
                                                        {optionsColumn.edit && (
                                                            <IconButton onClick={() => onEdit(row.id)} sx={{ color: 'green' }} disabled={row.status === 0}>
                                                                <EditIcon sx={{ width: '1rem', height: '1rem' }} />
                                                            </IconButton>
                                                        )}
                                                        {optionsColumn.view && (
                                                            <IconButton onClick={() => onView(row.id)} sx={{ color: 'blue' }} disabled={row.status === 0}>
                                                                <ViewIcon sx={{ width: '1rem', height: '1rem' }} />
                                                            </IconButton>
                                                        )}
                                                    </>
                                                </TableCell>
                                            )}
                                            {tableHeadings.map((heading, colIndex) => (
                                                <TableCell
                                                    key={colIndex}
                                                    sx={{
                                                        color: row.status === 0 ? 'red' : 'inherit',
                                                        fontSize: '0.8rem',
                                                        textAlign: 'center'
                                                    }}
                                                >
                                                    {heading === 'Attachment' && row[heading] ? (
                                                        <IconButton
                                                            onClick={() => handlePreviewClick(row[heading], row.file_name || 'File')}
                                                            sx={{
                                                                color: row.status === 0 ? 'red' : 'primary.main',
                                                                padding: 0
                                                            }}
                                                        >
                                                            <VisibilityIcon sx={{ width: '1.2rem', height: '1.2rem' }} />
                                                        </IconButton>
                                                    ) : (
                                                        row[heading] || "N/A"
                                                    )}
                                                </TableCell>
                                            ))}
                                        </TableRow>
                                    ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={tableHeadings.length + (optionsColumn ? 1 : 0)}>
                                        <Typography variant="body1" align="center">
                                            {noDataMessage}
                                        </Typography>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                    <TablePagination
                        rowsPerPageOptions={[1, 2, 5]}
                        component="div"
                        count={Array.isArray(tableData) ? tableData?.length : 0}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={handleChangePage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                </TableContainer>
            </Collapse>

            <FilePreviewModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                fileUrl={selectedFile.url}
                fileName={selectedFile.name}
            />
        </>
    );
};

export default DetailsTable;