import React, { useState } from 'react';
import { Grid, Box, Button, Typography, InputAdornment } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import VpnKeyOutlinedIcon from '@mui/icons-material/VpnKeyOutlined';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import CustomTextField from '../../components/CustomTextField';
import { changePassword, resetPassword } from '../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';

const NewPassword = () => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [newPasswordFocused, setNewPasswordFocused] = useState(false);
  const [confirmPasswordFocused, setConfirmPasswordFocused] = useState(false);
  const [passwordError, setPasswordError] = useState('');

  const navigate = useNavigate();
  const dispatch = useDispatch();

 // const { userId, userType } = useSelector(state => state.auth);
  //const otp = useSelector((state) => state.auth.otpStatus); // This will give you the OTP
  // Access the OTP from the Redux store
  const userId = useSelector((state) => state.auth.userId);
  const userType = useSelector((state) => state.auth.userType);

  const otpCode = useSelector((state) => state.auth.otpCode);  // OTP sent by backend (if available)
  const otpEntered = useSelector((state) => state.auth.otpEntered);  // OTP entered by user


  const validatePassword = (password) => {
    const minLength = /.{8,}/;
    const uppercase = /[A-Z]/;
    const lowercase = /[a-z]/;
    const number = /[0-9]/;
    const specialChar = /[!@#$%^&*(),.?":{}|<>]/;

    if (!minLength.test(password)) {
      return "Password must be at least 8 characters long.";
    }
    if (!uppercase.test(password)) {
      return "Password must contain at least one uppercase letter.";
    }
    if (!lowercase.test(password)) {
      return "Password must contain at least one lowercase letter.";
    }
    if (!number.test(password)) {
      return "Password must contain at least one number.";
    }
    if (!specialChar.test(password)) {
      return "Password must contain at least one special character.";
    }
    return ''; // No errors
  };

  const handleSubmit = async () => {
    const validationError = validatePassword(newPassword);
    if (validationError) {
      setPasswordError(validationError);
      toast.error(validationError);
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    try {
      if (otpEntered) {
        // If OTP exists, handle password reset
        const payload = {
          newPassword,
          confirmPassword,
          otp: otpEntered,            // Send the OTP entered by the user
        };

        const resultAction = await dispatch(resetPassword(payload));

        if (resetPassword.fulfilled.match(resultAction)) {
          toast.success('Password reset successfully');
          navigate('/');  // Redirect to login page after successful reset
        } else {
          toast.error(resultAction.payload || 'Password reset failed');
        }
      } else {
        // Otherwise, handle first-time login password change
        const payload = {
          newPassword,
          confirmPassword,
          userId,
          userType
        };

        const resultAction = await dispatch(changePassword(payload));

        if (changePassword.fulfilled.match(resultAction)) {
          navigate('/'); // Redirect to Login page after successful change
        } else {
          toast.error(resultAction.payload || 'Password change failed');
        }
      }
    } catch (error) {
      toast.error('An error occurred while changing the password');
    }
  };


  const toggleShowNewPassword = () => {
    setShowNewPassword(prev => !prev);
  };

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(prev => !prev);
  };

  return (
    <Grid container style={{ height: '100vh', overflow: 'hidden' }}>
      <Grid item xs={12} md={6} style={{ backgroundImage: `url(/background.png)`, backgroundSize: 'cover', backgroundPosition: 'left' }}>
      </Grid>

      <Grid
        item
        xs={12}
        md={6}
        display="flex"
        alignItems="center"
        justifyContent="center"
        style={{ backgroundColor: '#DDF2ED', padding: '8px', height: '100%' }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: 800,
            padding: 2,
            borderRadius: 2,
            alignItems: 'center',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <img
              src="/logo1.png"
              alt="Logo"
              style={{ width: 250 }}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: 1 }}>
            <img
              src="/illustration.png"
              alt="Illustration"
              style={{ width: '50%' }}
            />
          </Box>

          <Box sx={{ textAlign: 'center', marginTop: 2 }}>
            <Typography variant="h5" gutterBottom>
              New Password
            </Typography>
            <Typography variant="body1" gutterBottom>
              Please enter your new password
            </Typography>
          </Box>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <CustomTextField
              label="Enter New Password"
              type={showNewPassword ? 'text' : 'password'}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              onFocus={() => setNewPasswordFocused(true)}
              onBlur={() => setNewPasswordFocused(false)}
              margin="normal"
              error={Boolean(passwordError)}
              helperText={passwordError}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" onClick={toggleShowNewPassword} style={{ cursor: 'pointer' }}>
                    {showNewPassword ? <VisibilityOff /> : <Visibility />}
                  </InputAdornment>
                ),
              }}
              icon={newPasswordFocused || newPassword ? (showNewPassword ? Visibility : VpnKeyOutlinedIcon) : VpnKeyOutlinedIcon}
              sx={{ width: '100%', maxWidth: '400px' }}
            />

            <CustomTextField
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              onFocus={() => setConfirmPasswordFocused(true)}
              onBlur={() => setConfirmPasswordFocused(false)}
              margin="normal"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" onClick={toggleShowConfirmPassword} style={{ cursor: 'pointer' }}>
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </InputAdornment>
                ),
              }}
              icon={confirmPasswordFocused || confirmPassword ? (showConfirmPassword ? Visibility : VpnKeyOutlinedIcon) : VpnKeyOutlinedIcon}
              sx={{ width: '100%', maxWidth: '400px' }}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', marginTop: 4 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              sx={{ maxWidth: '250px', width: '100%', backgroundColor: '#1A6A62', padding: '12px 0' }}
            >
              Submit
            </Button>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default NewPassword;
