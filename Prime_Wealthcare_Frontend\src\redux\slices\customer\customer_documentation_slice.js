import { createSlice } from '@reduxjs/toolkit';
import {
    getAllCustomerDocuments,
    getCustomerDocumentById,
    createCustomerDocument,
    updateCustomerDocument,
    deleteCustomerDocument,
    getCustomerDocumentsByCustomerId
} from '../../actions/action';
import { toast } from 'react-toastify';

// Initial state for the customer slice
const initialState = {
    customerDocumentation: {},
    loading: false,
    error: null,
};

// Create a slice for customer
const customer_documentation_info_slice = createSlice({
    name: 'customerDocumentationInfo',
    initialState,
    extraReducers: (builder) => {
        // Get All Documents
        builder
            .addCase(getAllCustomerDocuments.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAllCustomerDocuments.fulfilled, (state, action) => {
                state.loading = false;
                state.customerDocumentation = action.payload;
            })
            .addCase(getAllCustomerDocuments.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error(action.payload || 'Failed to fetch documents');
            })

            // Get Document By ID
            .addCase(getCustomerDocumentById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCustomerDocumentById.fulfilled, (state, action) => {
                state.loading = false;
                state.customerDocumentation = action.payload;
            })
            .addCase(getCustomerDocumentById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Get Customer Documents By Customer ID
            .addCase(getCustomerDocumentsByCustomerId.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCustomerDocumentsByCustomerId.fulfilled, (state, action) => {
                state.loading = false;
                state.customerDocumentation = action.payload;
            })
            .addCase(getCustomerDocumentsByCustomerId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Create Document
            .addCase(createCustomerDocument.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createCustomerDocument.fulfilled, (state, action) => {
                state.loading = false;
                state.customerDocumentation = action.payload;
                toast.success('Document created successfully');
            })
            .addCase(createCustomerDocument.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error(action.payload || 'Failed to create document');
            })

            // Update Document
            .addCase(updateCustomerDocument.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateCustomerDocument.fulfilled, (state, action) => {
                state.loading = false;
                state.customerDocumentation = action.payload;
                toast.success('Document updated successfully');
            })
            .addCase(updateCustomerDocument.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error(action.payload || 'Failed to update document');
            })

            // Delete Document
            .addCase(deleteCustomerDocument.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteCustomerDocument.fulfilled, (state, action) => {
                state.loading = false;
                // Remove the deleted document from state
                const id = action.payload;
                if (Array.isArray(state.customerDocumentation)) {
                    state.customerDocumentation = state.customerDocumentation.filter(doc => doc.id !== id);
                } else {
                    delete state.customerDocumentation[id];
                }
                toast.success('Document deleted successfully');
            })
            .addCase(deleteCustomerDocument.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error(action.payload || 'Failed to delete document');
            });
    },
});

// Export the reducer to be included in the store
export default customer_documentation_info_slice.reducer;
