exports.up = function (knex) {
    return knex.schema.hasTable('role_management').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('role_management', function (table) {
                table.increments('id').primary();
                table.string('role_name', 50).notNullable().unique();
                table.string('department_name', 50).notNullable();
                table.integer('created_by').notNullable().defaultTo(1);
                table.integer('updated_by').notNullable().defaultTo(1);
                table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
                table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
                table.boolean('status').notNullable().defaultTo(true);
            });
        }
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('role_management');
};
