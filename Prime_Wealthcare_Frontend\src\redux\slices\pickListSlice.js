import { createSlice } from '@reduxjs/toolkit';
import { getAllPickLists } from '../actions/action';

const initialState = {
    pickLists: [],
    isLoading: false,
    error: null,
    genderOptions: [],
    maritalStatusOptions: [],
    educationOptions: [],
    insuranceTypeOptions: [],
    paymentTypeOptions: [],
    commissionPayableTypeOptions: [],
    commissionSourceOptions: [],
    policyTypeOptions: [],
    rangeTypeOptions: [],
    endorsementTypeOptions: [],
    claimBillOptions: [],
    bloodGroupOptions: [],
    relationOptions: [],
    documentTypeOptions: [],
    coverTypeOptions: [],
    OccupationOptions: [],
    paymentTypes: [],
    durationOptions: [],
    salutationOptions: [],
    loanTypeOptions: [],
    approvalStatusOptions: [],
};

const pickListSlice = createSlice({
    name: 'pickList',
    initialState,
    extraReducers: (builder) => {
        builder.addCase(getAllPickLists.pending, (state) => {
            state.isLoading = true;
        })
            .addCase(getAllPickLists.fulfilled, (state, action) => {
                state.isLoading = false;
                state.pickLists = action.payload;
                // Organize options by type
                state.genderOptions = action.payload.filter(item => item.type_name === 'Gender');
                state.maritalStatusOptions = action.payload.filter(item => item.type_name === 'Marital Status');
                state.educationOptions = action.payload.filter(item => item.type_name === 'Education');
                state.insuranceTypeOptions = action.payload.filter(item => item.type_name === 'Insurance Type');
                state.paymentTypeOptions = action.payload.filter(item => item.type_name === 'Payment Type');
                state.commissionPayableTypeOptions = action.payload.filter(item => item.type_name === 'Commission Payable Type');
                state.commissionSourceOptions = action.payload.filter(item => item.type_name === 'Commission Source');
                state.policyTypeOptions = action.payload.filter(item => item.type_name === 'Policy Type');
                state.rangeTypeOptions = action.payload.filter(item => item.type_name === 'Range Type');
                state.endorsementTypeOptions = action.payload.filter(item => item.type_name === 'Endorsement Type');
                state.claimBillOptions = action.payload.filter(item => item.type_name === 'Claim Bill');
                state.bloodGroupOptions = action.payload.filter(item => item.type_name === 'Blood Group');
                state.relationOptions = action.payload.filter(item => item.type_name === 'Relation');
                state.documentTypeOptions = action.payload.filter(item => item.type_name === 'Document Type');
                state.coverTypeOptions = action.payload.filter(item => item.type_name === 'Cover Type');
                state.OccupationOptions = action.payload.filter(item => item.type_name === 'Future_genrali_Occupation');
                state.paymentTypes = action.payload.filter(item => item.type_name === 'Payment Type');
                state.durationOptions = action.payload.filter(item => item.type_name === 'Duration');
                state.salutationOptions = action.payload.filter(item => item.type_name === 'Future_genrali_Salutation');
                state.loanTypeOptions = action.payload.filter(item => item.type_name === 'Loan_Type');
                state.approvalStatusOptions = action.payload.filter(item => item.type_name === 'Approval_Status');
            })
            .addCase(getAllPickLists.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.error.message;
            })
    }
});

export default pickListSlice.reducer;