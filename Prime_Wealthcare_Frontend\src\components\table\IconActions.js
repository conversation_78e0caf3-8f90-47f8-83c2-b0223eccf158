import React from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined';

const IconActions = ({ onAllClick, onActiveClick, onInactiveClick, onRefreshClick }) => {
  return (
    <Box sx={{ display: 'flex' }}>
      {onAllClick && <Tooltip title="All">
        <IconButton onClick={onAllClick}>
          <img src="/select-all.png" alt="All Icon" width={24} height={24} />
        </IconButton>
      </Tooltip>}
      {onActiveClick && <Tooltip title="Active">
        <IconButton onClick={onActiveClick}>
          <img src="/Vector.png" alt="Active Icon" width={24} height={24} />
        </IconButton>
      </Tooltip>}
      {onInactiveClick && <Tooltip title="Inactive">
        <IconButton onClick={onInactiveClick}>
          <img src="/deactivate-orders.png" alt="Deactivate Icon" width={24} height={24} />
        </IconButton>
      </Tooltip>}
      {onRefreshClick&&<Tooltip title="Refresh">
        <IconButton onClick={onRefreshClick}>
          <RefreshOutlinedIcon />
        </IconButton>
      </Tooltip>}
    </Box>
  );
};

export default IconActions;
