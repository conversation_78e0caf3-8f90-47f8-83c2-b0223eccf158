const PageRights = require('../Models/pageRightsModel');

const createPageRights = async (req, res) => {
    try {
        const { user_id, modules } = req.body;
        
        if (!req.user || !req.user.id) {
            console.error('User information not found in request');
            return res.status(401).json({
                success: false,
                message: 'User information not found'
            });
        }

        if (!user_id || !modules || !Array.isArray(modules)) {
            console.error('Invalid request data:', { user_id, modules });
            return res.status(400).json({
                success: false,
                message: 'Invalid request data'
            });
        }

        // Update the rights
        await PageRights.updateRights(user_id, modules, req.user.id);
        
        res.status(200).json({
            success: true,
            message: 'Page rights updated successfully'
        });
    } catch (error) {
        console.error('Error in createPageRights:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating page rights',
            error: error.message
        });
    }
};

const getRoleRights = async (req, res) => {
    try {
        const { role_id } = req.params;
        const rights = await PageRights.findByRoleId(role_id);
        
        res.status(200).json({
            success: true,
            data: rights
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching role rights',
            error: error.message
        });
    }
};

const checkUserAccess = async (req, res) => {
    try {
        const { user_id, module_name, page_name } = req.body;
        const access = await PageRights.checkUserAccess(user_id, module_name, page_name);
        
        res.status(200).json({
            success: true,
            data: access || {}
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error checking access',
            error: error.message
        });
    }
};

const getUserAccessRights = async (req, res) => {
    try {
        const { user_id } = req.params;
        const rights = await PageRights.findByUserId(user_id);
        
        res.status(200).json({
            success: true,
            data: rights
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching user access rights',
            error: error.message
        });
    }
};
const getAllUsers = async (req, res) => {
    try {
        const users = await PageRights.getAllUsers();
        res.status(200).json({ success: true, data: users });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error fetching users', error: error.message });
    }
};


module.exports = {
    createPageRights,
    getRoleRights,
    checkUserAccess,
    getUserAccessRights,
    getAllUsers
};