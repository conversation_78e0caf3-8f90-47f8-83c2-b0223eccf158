import React, { useEffect, useState } from 'react';
import { Box, Button, ButtonGroup, Container, Grid, IconButton, Typography } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import { renderAvatar, maskDOB, maskMobileNumber, maskEmail } from '../../../utils/Reusable';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import dayjs from 'dayjs';
import { Cancel, Save } from '@mui/icons-material';
import CustomEditableTable from '../../../components/table/CustomEditableTable';
import DetailsDropdown from '../../../components/table/DetailsDropdown';
import { fetchEmployeeById, getEmployeeLoanById, getEmployeeLoanE<PERSON>, updateEmployee<PERSON>mi, updateEmployee<PERSON>oan } from '../../../redux/actions/action';
import { toast } from 'react-toastify';
import { usePermissions } from '../../../hooks/usePermissions';

function EmployeeEmiDetails() {
    const { id } = useParams();
    const isViewMode = useLocation().pathname.includes('view');
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const employeeInformation = useSelector(state => state.employeeInfoReducer.employeeDetail);
    const employeeLoan = useSelector(state => state.employeeLoanReducer.employeeLoanDetails);
    const employeeLoanEmisData = useSelector(state => state.employeeLoanReducer.employeeLoanEmis);
    const { user } = useSelector((state) => state.auth);
    const [employeeLoanEmis, setEmployeeLoanEmis] = useState([]);
    const permissions = usePermissions('Loan', 'Employee Loan EMI');

    useEffect(() => {
        dispatch(getEmployeeLoanById(id)).then(res => {
            dispatch(fetchEmployeeById(res.payload?.employee_id));
            dispatch(getEmployeeLoanEmis(res.payload?.loan_id));
        });
    }, [dispatch, id]);

    useEffect(() => {
        setEmployeeLoanEmis(employeeLoanEmisData);
    }, [employeeLoanEmisData]);

    const handleCancel = () => {
        navigate('/dashboard/employee-loans');
    }

    const handleUpdate = (emiId, field, value) => {
        const emi = employeeLoanEmis?.find(emi => emi.id === emiId);
        if (!value) {
            toast.error('Error updating EMI please select a date and try again');
            return;
        }
        const closingBalance = (calculateOutstandingAmount() - Number(emi?.emi_amount)) || 0;
        const updatedEmi = {
            employee_loan_id: employeeLoan?.loan_id,
            paid_date: value ? dayjs(value).format('YYYY-MM-DD') : null,
            closing_balance: closingBalance,
            paid_amount: employeeLoan?.loan_amount - (closingBalance || employeeLoan?.loan_amount),
            updated_by: user?.userId,
            status: 0
        }
        dispatch(updateEmployeeEmi({ id: emiId, emiData: updatedEmi })).then(() => {
            dispatch(getEmployeeLoanById(id));
            dispatch(getEmployeeLoanEmis(employeeLoan?.loan_id));
        });
    }

    const calculateOutstandingAmount = () => {
        const outstandingAmount = employeeLoan?.loan_amount - employeeLoan.paid_amount;
        return outstandingAmount || employeeLoan?.loan_amount;
    }

    const calculatePaidAmount = () => {
        const totalPaidAmount = employeeLoanEmis?.reduce((sum, emi) => {
            if (emi.status === false) {
                return sum + emi.emi_amount;
            }
        }, 0);
        return totalPaidAmount || 0;
    }

    const columns = [
        { field: 'month', headerName: 'Month', editable: false, inputType: 'text' },
        { field: 'year', headerName: 'Year', editable: false, inputType: 'text' },
        { field: 'emi_amount', headerName: 'EMI Amount', editable: false, inputType: 'text' },
        { field: 'closing_balance', headerName: 'Closing Balance', editable: false, inputType: 'text' },
        { field: 'paid_date', headerName: 'Paid Date', editable: true, inputType: 'date', minDate: employeeLoan?.issue_date },
    ]

    return (
        <Container maxWidth="xxl">
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {/* Module Name and Buttons */}
                <Grid
                    container
                    sx={{
                        backgroundColor: 'white',
                        borderBottom: '2px solid #E0E0E0',
                        padding: '10px 0',
                        display: "flex"
                    }}
                >
                    {/* Header Row */}
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{
                                width: '20px',
                                marginLeft: '20px',
                                backgroundColor: 'green'
                            }}
                        />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Employee Loan EMI" pageName="View" />
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                            <IconButton
                                onClick={handleCancel}
                                sx={{ color: 'red', mx: 0.5 }}
                            >
                                <Cancel />
                            </IconButton>
                        </Box>
                        <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
                            <Button
                                onClick={handleCancel}
                                variant="outlined"
                                size="small"
                                sx={{
                                    maxWidth: '100px',
                                    width: '100%',
                                    mx: 1.5,
                                    color: 'red',
                                    borderColor: 'red',
                                    textTransform: 'none'
                                }}
                            >
                                Cancel
                            </Button>
                        </Box>
                    </Grid>
                </Grid>
                {/* Employee Info Section */}
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem', borderBlock: '1px solid black' }}>
                    <Box mr={2}>
                        {renderAvatar(employeeInformation?.emp_photo, employeeInformation?.employee_full_name)}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                        <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Full Name:</strong> {employeeInformation?.employee_full_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>User ID:</strong> {employeeInformation?.user_id || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Mobile:</strong> {employeeInformation?.official_mobile || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Email:</strong> {employeeInformation?.official_email || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Department:</strong> {employeeInformation?.department_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Role:</strong> {employeeInformation?.role_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Mobile:</strong> {maskMobileNumber(employeeInformation?.personal_mobile) || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Email:</strong> {maskEmail(employeeInformation?.personal_email) || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Joining Date:</strong> {dayjs(employeeInformation?.date_of_joining).format('DD-MM-YYYY') || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Branch Name:</strong> {employeeInformation?.branch_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>City:</strong> {employeeInformation?.branch_city || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Date of Birth:</strong> {maskDOB(employeeInformation?.dob) || 'N/A'}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </Box>

                {/* Loan Details Section */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem 3rem', borderRadius: '4px', mb: 2, display: 'flex', justifyContent: 'space-between' }}>
                        <h2>Loan Details</h2>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ alignItems: 'center', padding: '0 3rem' }}>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Loan Id:</strong> {employeeLoan?.loan_id || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Loan Amount:</strong> {employeeLoan?.loan_amount || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Outstanding:</strong> {calculateOutstandingAmount() || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Paid Amount:</strong> {employeeLoan?.paid_amount || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>EMI:</strong> {employeeLoan?.emi || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Issue Date:</strong> {dayjs(employeeLoan?.issue_date).format('DD-MM-YYYY') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Start Date:</strong> {dayjs(employeeLoan?.start_date).format('DD-MM-YYYY') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>End Date:</strong> {dayjs(employeeLoan?.end_date).format('DD-MM-YYYY') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Tenure:</strong> {employeeLoan?.tenure || 'N/A'}</Typography>
                    </Grid>
                </Grid>

                {/* EMI Details Table */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem 3rem', borderRadius: '4px', display: 'flex', justifyContent: 'space-between' }}>
                        <h2>EMI Details</h2>
                    </Box>
                </Grid>
                <CustomEditableTable
                    isCheckboxRequired={false}
                    data={employeeLoanEmis}
                    columns={columns}
                    onCellEdit={permissions.can_edit && !isViewMode ? handleUpdate : null}
                    updateButtonText={['Paid', 'Unpaid']}
                    isDisabled={isViewMode || !permissions.can_edit}
                />
            </Box>
        </Container>
    );
}

export default EmployeeEmiDetails;
