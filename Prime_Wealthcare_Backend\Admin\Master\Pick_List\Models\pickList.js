const knexConfig = require('../../../../knexfile');
const knex = require('knex')(knexConfig.development);

class PickList {
    static async getAll() {
        return knex('pick_list').where('is_active', true); // Fetch only active records
    }

    static async getByTypeName(typeName) {
        return knex('pick_list').where('type_name', typeName).andWhere('is_active', true);
    }

    static async create(pickListData) {
        return knex('pick_list').insert(pickListData).returning('*');
    }

    static async update(id, pickListData) {
        return knex('pick_list').where({ id }).update(pickListData).returning('*');
    }

    static async delete(id) {
        return knex('pick_list').where({ id }).del();
    }

    static async softDelete(id) {
        return knex('pick_list').where({ id }).update({ is_active: false }).returning('*');
    }

    static async reinstate(id) {
        return knex('pick_list').where({ id }).update({ is_active: true }).returning('*');
    }
}

module.exports = PickList;
