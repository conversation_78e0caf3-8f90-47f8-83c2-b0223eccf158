const express = require('express');
const router = express.Router();
const branchController = require('../Controllers/insuranceBranchController');

router.post('/', branchController.createInsuranceBranch);
router.get('/', branchController.getInsuranceBranches);
router.get('/:id', branchController.getInsuranceBranchId);
router.get('/insurance-coompany/:id', branchController.getInsuranceBranchInsuranceCompanyId);
router.put('/:id', branchController.updateInsuranceBranchById);
router.delete('/:id', branchController.deleteInsuranceBranchById);
router.put('/reinstate/:id', branchController.reinstateInsuranceBranchById);
//router.get('/branch-agency-codes',branchController.fetchAllBranchAndAgencyCodeData);
router.post('/create-branch-and-agency-code',branchController.createBranchAndAgencyCodes);
// Route to get insurance branches by filter (dropdown)
router.get('/criteria/:criteria', branchController.getInsuranceBranchesByCriteria);
// Route to search insurance branches
router.get('/name/:name', branchController.searchInsuranceBranch);




module.exports = router;      
 