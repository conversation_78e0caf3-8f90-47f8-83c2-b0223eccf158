exports.up = function (knex) {
    return knex.schema.createTable('pa_occupation_list', (table) => {
        table.increments('id').primary();           // Auto-incrementing primary key
        table.integer('pick_list_id').unsigned().references('id').inTable('pick_list').onDelete('CASCADE');  // Foreign key referencing pick_list table
        table.integer('risk_class').notNullable(); // Risk class
        table.string('type_name').notNullable();    // Dropdown category (e.g., Gender, Payment Type)
        table.string('api_name').notNullable();     // API reference (e.g., G_Male, G_Female)
        table.string('label_name').notNullable();   // Label to display in the dropdown
        table.boolean('is_active').notNullable().defaultTo(true); // Whether the pick list item is active
        table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
        table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
    });
};


exports.down = function (knex) {
    return knex.schema.dropTableIfExists('pa_occupation_list');
};
