import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify'
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
  fetchInsuranceCompanies, deleteInsuranceCompany, reinstateInsuranceCompany,
  fetchInsuranceCompanyByName, // New action
  fetchInsuranceCompaniesByCriteria, // New action
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';

const InsuranceCompanyPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [selectedOption, setSelectedOption] = useState('none');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const [sortedCompanies, setSortedCompanies] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
  const [filteredCompanies, setFilteredCompanies] = useState(insuranceCompanies || []);

  // Use permissions for this page
  const permissions = usePermissions('Master', 'Insurance Company');

  // No-op function
  const noOp = () => {};

  useEffect(() => {
    dispatch(fetchInsuranceCompanies());
  }, [dispatch]);

  useEffect(() => {
    setFilteredCompanies(insuranceCompanies);
  }, [insuranceCompanies]);

  useEffect(() => {
    if (selectedOption) {
      dispatch(fetchInsuranceCompaniesByCriteria(selectedOption));
    }
  }, [selectedOption, dispatch]);

  useEffect(() => {
    const filterCompaniesByStatus = () => {
      if (statusFilter === 'all') {
        setFilteredCompanies(insuranceCompanies);
      } else {
        setFilteredCompanies(insuranceCompanies.filter(company => company.status === (statusFilter === 'active' ? 1 : 0)));
      }
    };

    filterCompaniesByStatus();
  }, [statusFilter, insuranceCompanies]);

  useEffect(() => {
    if (insuranceCompanies && insuranceCompanies.length > 0) {
      // Sort by 'created_at' in reverse order (newest first)
      const sortedData = [...insuranceCompanies].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      setFilteredCompanies(sortedData);
    }
  }, [insuranceCompanies]);


  const onSearch = (query) => {
    if (query) {
      dispatch(fetchInsuranceCompanyByName(query));
    } else {
      dispatch(fetchInsuranceCompanies());// Display all data when search is empty
    }
  }


  const handleOpenDeletePopup = (item) => {
    setSelectedItem(item);
    setOpenDeletePopup(true);
  };

  const handleCloseDeletePopup = () => {
    setOpenDeletePopup(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    dispatch(deleteInsuranceCompany(selectedItem.id))
      .then(() => {
        dispatch(fetchInsuranceCompanies());
        setOpenDeletePopup(false);
        setOpenSuccessPopup(true);
      })
      .catch(error => {
        console.error("Failed to delete insurance company:", error);
        setOpenDeletePopup(false);
      });
  }

  const handleCloseSuccessPopup = () => {
    setOpenSuccessPopup(false);
  };

  const handleAdd = () => {
    navigate('/dashboard/insurance-form');
  };

  const handleDelete = (id) => {
    handleOpenDeletePopup(insuranceCompanies.find(company => company.id === id));
  };

  const handleReinstate = (id) => {
    dispatch(reinstateInsuranceCompany(id))
      .then(() => {
        dispatch(fetchInsuranceCompanies());
        toast.success('Insurance company reinstated successfully!');

      })
      .catch(error => {
        console.error("Failed to reinstate insurance company:", error);
        toast.error('Failed to reinstate insurance company.');

      });
  };

  const handleEdit = (id) => {
    const companyToEdit = insuranceCompanies.find((company) => company.id === id);
    if (companyToEdit) {
      navigate(`/dashboard/insurance-company/edit/${id}`, { state: { company: companyToEdit } });
    }
  };

  const handleSelectionChange = (id) => {
    setSelectedRows(prevSelected =>
      prevSelected.includes(id)
        ? prevSelected.filter(rowId => rowId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSelectAll = (isSelected) => {
    setSelectedRows(isSelected ? insuranceCompanies.map(company => company.id) : []);
  };

  const handleAllClick = () => setStatusFilter('all');
  const handleActiveClick = () => setStatusFilter('active');
  const handleInactiveClick = () => setStatusFilter('inactive');
  const handleRefreshClick = () => {
    setSelectedOption('none');
    dispatch(fetchInsuranceCompanies());
  }

  const columns = [
    { field: 'id', headerName: 'ID', width: '1%' },
    { field: 'insurance_company_name', headerName: 'Company Name', width: '15%' },
    { field: 'short_name', headerName: 'Short Name', width: '10%' },
    { field: 'ado_code', headerName: 'ADO Code', width: '5%' },
    { field: 'help_line_no', headerName: 'Help Line No.', width: '10%' },
    { field: 'zone_head_name', headerName: 'Zone Head Name', width: '10%' },
    { field: 'insurance_type', headerName: 'Insurance Type', width: '10%' },
    { field: 'city', headerName: 'City', width: '10%' },
    { field: 'state', headerName: 'State', width: '5%' },
    // { field: 'company_logo', headerName: 'Logo', width: 120 },

  ];

  const dataMapping = {
    ID: 'id',
    'Company Name': 'insurance_company_name',
    'Short Name': 'short_name',
    'ADO Code': 'ado_code',
    'Help Line No.': 'help_line_no',
    'Zone Head Name': 'zone_head_name',
    'Insurance Type': 'insurance_type',
    City: 'city',
    State: 'state',
    Status: 'status',
  };

  return (
    <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
      {/*    <Navbar/>  */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/image.png"
              alt="module icon"
              style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
            />
            <ModuleName moduleName="Insurance Company" pageName="List" />
          </Box>
          <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
            {permissions.can_add && (
              <Button
                onClick={handleAdd}
                sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
              >
                New
              </Button>
            )}
            <ExportToPDF
              data={filteredCompanies.map(company => ({
                ...company,
                status: company.status === 1 ? 'Active' : 'Inactive'
              }))}
              headNames={['ID', 'Company Name', 'Short Name', 'ADO Code', 'Help Line No.', 'Zone Head Name', 'Insurance Type', 'City', 'State', 'Status']}
              selectedRows={selectedRows}
              imageUrl="/logo.png"
              watermarkUrl="/gray-logo.png"
              fileName="insurance-companies.pdf"
              dataMapping={dataMapping}
              headerTitle="Insurance Company Report"
            />
          </ButtonGroup>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingBottom: '1rem', ml: 5 }}>
          <DropDown
            label=""
            value={selectedOption}
            onChange={(e) => setSelectedOption(e.target.value)}
            options={[
              { value: 'none', label: 'None' },
              { value: 'newLastWeek', label: 'New Last Week' },
              { value: 'newThisWeek', label: 'New this Week' },
              { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
              { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
              { value: 'editedLastWeek', label: 'Edited Last Week' },
              { value: 'editedThisWeek', label: 'Edited This Week' },
            ]}
          // sx={{ height: '30px', ml: '82px', mt: '-15px' }}
          />
          <Box sx={{ display: 'flex', gap: 2 }}>
            <SearchBar placeholder="Search..."
              onSearch={onSearch} />
            <IconActions
              onAllClick={handleAllClick}
              onActiveClick={handleActiveClick}
              onInactiveClick={handleInactiveClick}
              onRefreshClick={handleRefreshClick}
            />
          </Box>
        </Box>

        <CustomTable
          data={filteredCompanies}
          columns={columns}
          selectedRows={selectedRows}
          onDelete={permissions.can_delete ? handleDelete : null}
          onEdit={permissions.can_edit ? handleEdit : null}
          onReinstate={handleReinstate}
          onSelectionChange={handleSelectionChange}
          onSelectAll={handleSelectAll}
        />
      </Box>

      <DeletePopup
        open={openDeletePopup}
        onClose={handleCloseDeletePopup}
        onConfirm={handleConfirmDelete}
        modulename={selectedItem ? selectedItem.insurance_company_name : ''}

      />

      <SuccessPopup
        open={openSuccessPopup}
        onClose={handleCloseSuccessPopup}
        modulename={selectedItem ? selectedItem.insurance_company_name : ''}

      />
    </Container>
  );
};

export default InsuranceCompanyPage;
