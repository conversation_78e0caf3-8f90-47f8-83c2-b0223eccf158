const express = require('express');
const router = express.Router();
const { sendSOAPRequest } = require('../HealthAbsolute/healthAbsoluteSoapService');
const { v4: uuidv4 } = require('uuid');
const { formatDateToDDMMYYYY, calculateDOBFromAgeBand } = require('../../../Reusable/reusable');

// Add this helper function to calculate policy dates
const calculatePolicyDates = (duration) => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // Start from tomorrow

    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + parseInt(duration)); // Add duration years
    endDate.setDate(endDate.getDate() - 1); // Subtract one day

    return {
        startDate: formatDateToDDMMYYYY(startDate),
        endDate: formatDateToDDMMYYYY(endDate)
    };
}

// Define sum insured options based on cover types
const COVER_TYPE_SUM_INSURED = {
    'CLASSIC': [500000, 1000000],
    'PLATINUM': [1500000, 2000000, 2500000],
    'SIGNATURE': [5000000, 10000000]
};

router.post('/healthabsolutecreate', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Generate numeric UID
        const numericUid = uuidv4();

        // Calculate policy dates based on duration
        const { startDate: policyStartDate, endDate: policyEndDate } = calculatePolicyDates(duration || '1');

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;

        const policyType = family_type === 'individual' ? 'HAI' : 'HAF'; // HAI for individual, HAF for floater

        const soapData = {
            Product: normalizedProductName,
            PolicyHeader: {
                PolicyStartDate: policyStartDate,
                PolicyEndDate: policyEndDate,
                AgentCode: "60000272",
                BranchCode: "10",
                MajorClass: "FHA",
                ContractType: "FHA",
                METHOD: "ENQ",
                PolicyIssueType: "I"
            },
            Uid: numericUid,
            VendorCode: "webagg",
            VendorUserId: "webagg",
            Client: {
                ClientType: "I",
                CreationType: "C",
                Address1: {
                    Pincode: pincode,
                    Country: "IND"
                }
            },
            BeneficiaryDetails: {
                Member: members.map((member, index) => ({
                    MemberId: (index + 1).toString(),
                    InsuredName: `Member ${index + 1}`,
                    InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                    InsuredGender: member.gender || 'M',
                    InsuredOccpn: "SVCM",
                    CoverType: cover_type,
                    SumInsured: member.sumInsured,
                    Relation: member.relation,
                    Height: "170",
                    Weight: "70",
                    Smoking: "N",
                    Tobacco: "N",
                    IsGoodHealth: "Y",
                    IsExistingAbsolutePolicy: "N",
                    NomineeName: "Test Nominee",
                    NomineeRelation: "FATH",
                    NomineeAge: "45"
                }))
            },
            Risk: {
                PolicyType: policyType,
                Duration: duration || "1", // Use provided duration or default to 1
                Installments: "FULL",
                PaymentType: "CC"
            }
        };
 
        const soapResponse = await sendSOAPRequest(soapData);
        
        // Check if the response contains an error
        if (soapResponse?.status === "error" || soapResponse?.policyDetails?.Root?.Status?.[0] === "Fail") {
            const errorMessage = soapResponse?.policyDetails?.Root?.ValidationError?.[0] || 
                                soapResponse?.message || 
                                "Unknown error occurred";
            
            return res.status(400).json({
                status: "error",
                message: errorMessage,
                timestamp: new Date().toISOString()
            });
        }

        // Only attempt to process the response if we have valid data
        const outputRes = soapResponse?.policyDetails?.Root?.Policy?.[0]?.OutputRes?.[0];
        
        // If outputRes is undefined, something is wrong with the response structure
        if (!outputRes) {
            return res.status(400).json({
                status: "error",
                message: "Invalid response structure from insurance provider",
                timestamp: new Date().toISOString(),
                rawResponse: soapResponse
            });
        }

        const memberResults = soapResponse?.policyDetails?.Root?.Policy[0]?.InputParameters?.[0]?.BeneficiaryDetails[0]?.Member.map((member) => ({
            memberId: member.MemberId[0],
            sumInsured: member.SumInsured[0],
            basePremium: member.PerPersonPremium[0],
            coverType: member.CoverType[0],
            relation: member.Relation[0],
        })) || [];

        const outputResponseData = {
            premiumAmt: outputRes.PremiumAmt[0],
            serviceTax: outputRes.ServiceTax[0],
            premWithServiceTax: outputRes.PremWithServTax[0],
        };

        const formattedResponse = {
            status: 'success',
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName,
                company_name: company_name,
                duration: duration
            },
            results: {
                premiums: memberResults,
                outputResponse: outputResponseData
            },
            rawResponse: soapResponse
        };

        res.json(formattedResponse);
    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ 
            status: "error", 
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// New route for healthabsolute-floater-options
router.post('/healthabsolute-floater-options', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;
        // Validate input
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Validate cover type
        if (!COVER_TYPE_SUM_INSURED[cover_type]) {
            console.error(`Invalid cover type: ${cover_type}`);
            throw new Error(`Invalid cover type: ${cover_type}`);
        }

        // Temporary storage for results
        const tempResults = [];

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;

        // Iterate through sum insured options
        const sumInsuredOptions = COVER_TYPE_SUM_INSURED[cover_type];

        for (const sumInsured of sumInsuredOptions) {
            // Generate numeric UID
            const numericUid = uuidv4();

            // Calculate dates
            const startDate = new Date();
            startDate.setDate(startDate.getDate() + 1);
            const endDate = new Date(startDate);
            endDate.setFullYear(endDate.getFullYear() + 1);
            endDate.setDate(endDate.getDate() - 1);

            const policyStartDate = formatDateToDDMMYYYY(startDate);
            const policyEndDate = formatDateToDDMMYYYY(endDate);


            // Prepare SOAP data with current sum insured
            const soapData = {
                Product: normalizedProductName,
                PolicyHeader: {
                    PolicyStartDate: policyStartDate,
                    PolicyEndDate: policyEndDate,
                    AgentCode: "60000272",
                    BranchCode: "10",
                    MajorClass: "FHA",
                    ContractType: "FHA",
                    METHOD: "ENQ",
                    PolicyIssueType: "I"
                },
                Uid: numericUid,
                VendorCode: "webagg",
                VendorUserId: "webagg",
                Client: {
                    ClientType: "I",
                    CreationType: "C",
                    Address1: {
                        Pincode: pincode,
                        Country: "IND"
                    }
                },
                BeneficiaryDetails: {
                    Member: members.map((member, index) => ({
                        MemberId: (index + 1).toString(),
                        InsuredName: `Member ${index + 1}`,
                        InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                        InsuredGender: member.gender || 'M',
                        InsuredOccpn: "SVCM",
                        CoverType: cover_type,
                        SumInsured: sumInsured,
                        Relation: member.relation,
                        Height: "170",
                        Weight: "70",
                        Smoking: "N",
                        Tobacco: "N",
                        IsGoodHealth: "Y",
                        IsExistingAbsolutePolicy: "N",
                        NomineeName: "Test Nominee",
                        NomineeRelation: "FATH",
                        NomineeAge: "45"
                    }))
                },
                Risk: {
                    PolicyType: family_type === 'individual' ? 'HAI' : 'HAF', // HAI for individual, HAF for floater
                    Duration: duration || "1",
                    Installments: "FULL",
                    PaymentType: "CC"
                }
            };

            try {
                // Call the SOAP service
                const soapResponse = await sendSOAPRequest(soapData);

                // Extract relevant data from the SOAP response
                //const outputRes = soapResponse?.policyDetails?.Root?.Policy[0]?.OutputRes[0]; // Access OutputRes
                console.log(soapResponse, "soap response float ");
                  // Check if the response contains an error
        if (soapResponse?.status === "error" || soapResponse?.policyDetails?.Root?.Status?.[0] === "Fail") {
            const errorMessage = soapResponse?.policyDetails?.Root?.ValidationError?.[0] || 
                                soapResponse?.message || 
                                "Unknown error occurred";
            
            return res.status(400).json({
                status: "error",
                message: errorMessage,
                timestamp: new Date().toISOString()
            });
        }

        // Only attempt to process the response if we have valid data
        const outputRes = soapResponse?.policyDetails?.Root?.Policy?.[0]?.OutputRes?.[0];
        
        // If outputRes is undefined, something is wrong with the response structure
        if (!outputRes) {
            return res.status(400).json({
                status: "error",
                message: "Invalid response structure from insurance provider",
                timestamp: new Date().toISOString(),
                rawResponse: soapResponse
            });
        }

                
                const memberResults = soapResponse?.policyDetails?.Root?.Policy[0]?.InputParameters?.[0]?.BeneficiaryDetails[0]?.Member.map((member) => ({
                    memberId: member.MemberId[0],
                    sumInsured: member.SumInsured[0],
                    basePremium: member.PerPersonPremium[0],
                    coverType: member.CoverType[0],
                    relation: member.Relation[0],
                })) || [];

                const outputResponseData = {
                    premiumAmt: outputRes.PremiumAmt[0],
                    serviceTax: outputRes.ServiceTax[0],
                    premWithServiceTax: outputRes.PremWithServTax[0],
                };

                // Push the results into tempResults
                tempResults.push({
                    sumInsured: sumInsured,
                    requestId: numericUid,
                    premiums: memberResults,
                    outputResponse: outputResponseData,
                    rawResponse: soapResponse
                });
            } catch (soapError) {
                console.error(`Error for sum insured ${sumInsured}:`, soapError);
                tempResults.push({
                    sumInsured: sumInsured,
                    error: soapError.message || 'Unknown error'
                });
            }
        }

        // Respond with the temporary results
        res.json({
            status: 'success',
            coverType: cover_type,
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                company_name: company_name,
                product: normalizedProductName, 
                duration:duration

            },
            results: tempResults // Return all results after processing
        });

    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ 
            status: "error", 
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;