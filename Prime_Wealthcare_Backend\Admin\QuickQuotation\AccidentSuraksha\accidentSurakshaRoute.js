const express = require('express');
const router = express.Router();
const { sendSOAPRequest } = require('./accidentSurakshaSOAP');
const { v4: uuidv4 } = require('uuid'); 
const { formatDateToDDMMYYYY, calculateDOBFromAgeBand, calculatePolicyDates } = require('../../../Reusable/reusable');
const knexConfig = require('../../../knexfile');
const knex = require('knex')(knexConfig.development);

// Add this function before the router.post
const getCoverName = (code) => {
    const coverNames = {
        'RF': 'Repatriation and Funeral Expenses',
        'AA': 'Adaptation Allowance',
        'CS': 'Child Education Support',
        'FT': 'Family Transportation Allowance',
        'HC': 'Hospital Cash Allowance',
        'LP': 'Loan Protecter',
        'LS': 'Life Support Benefit',
        'ME': 'Accidental Hospitalisation',
        'AM': 'Accidental Medical Expenses',
        'BB': 'Broken Bones'
    };
    return coverNames[code] || '';
};

// Update the getOccupationDetails function
const getOccupationDetails = async (occupationId) => {
    try {
        // console.log('Fetching occupation for ID:', occupationId);
        const occupation = await knex('pa_occupation_list')
            .where({
                id: occupationId,
                is_active: true
            })
            .first();
        
        // console.log('Found occupation:', occupation); // Debug log
        
        if (!occupation) {
            console.log('No occupation found for ID:', occupationId);
            return null;
        }

        return {
            id: occupation.id,
            api_name: occupation.api_name,
            label_name: occupation.label_name,
            risk_class: occupation.risk_class
        };
    } catch (error) {
        console.error('Error fetching occupation:', error);
        throw new Error('Failed to fetch occupation details');
    }
};

router.post('/accidentsurakshacreate', async (req, res) => {
    try {
        // Log the incoming request data
        console.log('Request Body:', JSON.stringify(req.body, null, 2));
        // console.log('Members Data:', JSON.stringify(req.body.members, null, 2));

        // Validate required fields
        const requiredFields = ['company_name', 'product_master_name', 'duration', 'pincode'];
        const missingFields = requiredFields.filter(field => !req.body[field]);
        
        if (missingFields.length > 0) {
            // console.log('Missing Required Fields:', missingFields);
            return res.status(400).json({ 
                error: `Missing required fields: ${missingFields.join(', ')}` 
            });
        }

        // Validate members data
        if (!req.body.members || !Array.isArray(req.body.members) || req.body.members.length === 0) {
            console.log('Invalid or Missing Members Data');
            return res.status(400).json({ 
                error: 'Members data is required and must be an array' 
            });
        }

        // Validate each member's required fields
        const requiredMemberFields = [
            'occupation',
            'annual_income',
            'sumInsured_AD',
            'sumInsured_PT',
            'sumInsured_PP',
            'sumInsured_TTD',
            'relation',
            'gender',
            'ageBand'
        ];

        for (let i = 0; i < req.body.members.length; i++) {
            const member = req.body.members[i];
            const missingMemberFields = requiredMemberFields.filter(field => !member[field]);
            
            if (missingMemberFields.length > 0) {
                console.log(`Missing Fields for Member ${i + 1}:`, missingMemberFields);
                return res.status(400).json({ 
                    error: `Missing required fields for member ${i + 1}: ${missingMemberFields.join(', ')}` 
                });
            }
        }

        // Add validation for additional covers if enabled
        if (req.body.additional_covers) {
            // Validate required covers
            const requiredCovers = ['repatriation_funeral_expenses'];
            const missingCovers = requiredCovers.filter(cover => 
                !req.body.additional_covers[cover] || 
                req.body.additional_covers[cover] !== 'yes'
            );
            
            if (missingCovers.length > 0) {
                return res.status(400).json({
                    error: 'Repatriation and Funeral Expenses cover is mandatory'
                });
            }
        }

        // Generate numeric UID
        const numericUid = uuidv4();

        // Calculate policy dates based on duration
        //const policyDates = calculatePolicyDates(req.body.duration || '1');

        // Normalize the product name
        const productMapping = {
            'FG ACCIDENT SURAKSHA': 'AccidentSuraksha'
        };

        const normalizedProductName = productMapping[req.body.product_master_name] || req.body.product_master_name;

        // Map frontend cover names to SOAP API cover codes
        const coverCodeMapping = {
            repatriation_funeral_expenses: 'RF',
            adaptation_allowance: 'AA',
            child_education_support: 'CS',
            family_transportation: 'FT',
            hospital_cash: 'HC',
            loan_protector: 'LP',
            life_support_benefit: 'LS',
            accidental_hospitalization: 'ME',
            accidental_medical_expenses: 'AM',
            broken_bones: 'BB'
        };

        // Convert occupation IDs to details for each member
        const membersWithOccupationDetails = await Promise.all(
            req.body.members.map(async (member) => {
                const occupationDetails = await getOccupationDetails(member.occupation);
                console.log('Member occupation details:', occupationDetails); // Debug log
                
                if (!occupationDetails) {
                    throw new Error(`Invalid occupation ID: ${member.occupation}`);
                }

                return {
                    ...member,
                    occupation: {
                        id: member.occupation,
                        api_name: occupationDetails.api_name,
                        label_name: occupationDetails.label_name,
                        risk: occupationDetails.risk_class
                    }
                };
            })
        );

        // Update the calculateAdditionalCoverSumInsured function
        const calculateAdditionalCoverSumInsured = (member, coverType, members) => {
            const selfMember = members.find(m => m.relation === 'SELF');
            
            // Determine member type and percentage
            const isHousewifeSpouse = member.relation === 'SPOUSE' && 
                member.occupation?.label_name?.toLowerCase() === 'housewife';
            const isChild = member.relation === 'SON' || member.relation === 'DAUGHTER';
            
            let percentageOfSelf = 100;
            if (isHousewifeSpouse) {
                percentageOfSelf = 50; // 50% for housewife spouse
            } else if (isChild) {
                percentageOfSelf = 25; // 25% for children
            }

            // Use self member's values for calculations
            const baseMember = selfMember; // Always use self member as base
            const principalSumInsured = Number(baseMember.sumInsured_AD) * (percentageOfSelf / 100);
            // console.log("principalSumInsured",principalSumInsured);
            const ptdSumInsured = Number(baseMember.sumInsured_PT) * (percentageOfSelf / 100);
            // console.log("ptdSumInsured",ptdSumInsured);
            
            const getRiskClass = (occupation) => {
                if (member.relation !== 'SELF' && isHousewifeSpouse) {
                    return 'Class II';
                }
                return occupation?.risk === 1 ? 'Class I' : 'Class II';
            };

            const riskClass = getRiskClass(member.occupation);

            switch (coverType) {
                case 'adaptation_allowance':
                    // 10% of PTD Sum Insured, max 50,000
                    return Math.min(ptdSumInsured * 0.10, 50000);

                case 'child_education_support':
                    // Only applicable if member has children
                    if (!isChild && !members.some(m => m.relation === 'SON' || m.relation === 'DAUGHTER')) {
                        return 0;
                    }
                    // 1% of Principal Sum Insured, max 10,000 per month for 48 months
                    return Math.min(principalSumInsured * 0.01, 10000);

                case 'family_transportation':
                    // 10% of Principal Sum Insured, max 50,000
                    return Math.min(principalSumInsured * 0.10, 50000);

                case 'hospital_cash':
                    // Class I: 2000/day, Class II: 1500/day, max 30 days
                    const dailyAmount = riskClass === 'Class I' ? 2000 : 1500;
                    return dailyAmount ;

                case 'loan_protector':
                    // Class I: 2% up to 20,000/month, Class II: 2% up to 15,000/month, for 12 months
                    const monthlyMax = riskClass === 'Class I' ? 20000 : 15000;
                    return Math.min(principalSumInsured * 0.02, monthlyMax) ;

                case 'life_support_benefit':
                    // 1% of PTD Sum Insured, max 10,000 per month for 48 months
                    return Math.min(ptdSumInsured * 0.01, 10000) * 48;

                case 'accidental_hospitalization':
                    // 25% of Principal Sum Insured, max 10 Lakhs
                    return Math.min(principalSumInsured * 0.25, 1000000);

                case 'accidental_medical_expenses':
                    // 20% loading on total premium of primary covers
                    return 0; // This will be handled differently as it's premium-based

                case 'repatriation_funeral_expenses':
                    // 1% of Principal Sum Insured, max 12,500
                    return Math.min(principalSumInsured * 0.01, 12500);

                case 'broken_bones':
                    // Calculate based on monthly income with class-based caps
                    const monthlyIncome = isHousewifeSpouse ? 
                        (Number(selfMember.annual_income) / 24) : // Half of self's monthly income for housewife
                        isChild ? 
                            (Number(selfMember.annual_income) / 48) : // Quarter of self's monthly income for child
                            (Number(member.annual_income) / 12); // Normal calculation for others
                    
                    const maxAmount = riskClass === 'Class I' ? 1500000 : 1000000;
                    return Math.min(monthlyIncome * 24, maxAmount);

                default:
                    return 0;
            }
        };
        // console.log(membersWithOccupationDetails,"membersWithOccupationDetails");

        // Prepare data for SOAP request with occupation details
        const soapData = {
            Product: normalizedProductName,
            
            Uid: numericUid,
            Client: {
                Address1: {
                    Pincode: req.body.pincode,
                    Country: "IND"
                }
            },
            BeneficiaryDetails: {
                Member: membersWithOccupationDetails.map((member, index) => {
                    const isChild = member.relation === 'SON' || member.relation === 'DAUGHTER';
                    
                    if (!member.occupation?.api_name) {
                        console.error('Missing occupation API name for member:', member);
                    }

                    return {
                        MemberId: (index + 1).toString(),
                        InsuredName: `Member ${index + 1}`,
                        InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                        InsuredGender: member.gender || 'M',
                        InsuredOccpn: member.occupation?.api_name,
                        SumInsured_AD: member.sumInsured_AD,
                        SumInsured_PT: member.sumInsured_PT,
                        SumInsured_PP: member.sumInsured_PP,
                        SumInsured_TTD: member.sumInsured_TTD,
                        AnnualIncome: member.annual_income,
                        Relation: member.relation,
                        AdditionalCover: req.body.additional_covers ? 
                            Object.entries(req.body.additional_covers)
                                .filter(([coverType, value]) => {
                                    // Skip Child Education Support and Loan Protector for children
                                    if (isChild && (
                                        coverType === 'child_education_support' || 
                                        coverType === 'loan_protector'
                                    )) {
                                        return false;
                                    }
                                    return value === 'yes';
                                })
                                .map(([coverType, _]) => ({
                                    coverCode: coverCodeMapping[coverType],
                                    coverName: getCoverName(coverCodeMapping[coverType]),
                                    sumInsured: calculateAdditionalCoverSumInsured(
                                        member, 
                                        coverType, 
                                        membersWithOccupationDetails
                                    ),
                                    enabled: true
                                })) : []
                    };
                })
            },
            Risk: {
                Duration: req.body.duration || "1",
                Installments: "FULL",
                PaymentType: "CC"
            },
            AdditionalCovers: req.body.additional_covers ? 
                Object.entries(req.body.additional_covers)
                    .filter(([_, value]) => value === 'yes')
                    .map(([cover, _]) => ({
                        coverCode: coverCodeMapping[cover],
                        enabled: true
                    })) : []
        };
        console.log('Final SOAP data:', JSON.stringify(soapData, null, 2)); // Debug log


        // Send SOAP request
        const soapResponse = await sendSOAPRequest(soapData);

        // Get the Premium details from the correct path in the response
        const premiumDetails = soapResponse?.data?.premium || {};
        const emiDetails = soapResponse?.data?.emiDetails || [];

        // Update member results to include occupation details
        const memberResults = membersWithOccupationDetails.map((member, index) => ({
            memberId: (index + 1).toString(),
            occupation: {
                id: member.occupation?.id,
                name: member.occupation?.label_name,
                risk_class: member.occupation?.risk
            },
            sumInsured_AD: member.sumInsured_AD,
            sumInsured_PT: member.sumInsured_PT,
            sumInsured_PP: member.sumInsured_PP,
            sumInsured_TTD: member.sumInsured_TTD,
            annualIncome: member.annual_income,
            relation: member.relation,
            gender: member.gender,
            ageBand: member.ageBand
        }));

        const formattedResponse = {
            status: 'success',
            policyDetails: {
                policyType: 'Individual', // Always individual for Accident Suraksha
                product: 'AccidentSuraksha',
                company_name: req.body.company_name,
                duration: req.body.duration,
            },
            results: {
                premiums: memberResults,
                outputResponse: {
                    premiumAmt: premiumDetails.totalPremium || 0,
                    serviceTax: premiumDetails.serviceTax || 0,
                    premWithServiceTax: premiumDetails.premiumWithServiceTax || 0,
                    discountAmount: premiumDetails.discounts?.amount || 0,
                    discountPercentage: premiumDetails.discounts?.percentage || 0
                },
                emiDetails: emiDetails
            }
        };

        res.json(formattedResponse);
    } catch (error) {
        console.error('=== Error in Accident Suraksha Quotation ===');
        console.error('Error:', error);
        res.status(500).json({ 
            error: error.message || 'Internal server error',
            details: error.stack
        });
    }
});

module.exports = router; 