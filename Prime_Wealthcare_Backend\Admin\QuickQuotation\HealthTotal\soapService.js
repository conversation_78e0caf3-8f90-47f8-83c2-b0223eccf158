const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { generateNomineeDetailsXML } = require('../../../Reusable/xmlComponents');

const SOAP_API_URL = process.env.SOAP_API_URL; // Use the URL from the .env file
const SOAP_ACTION = process.env.SOAP_ACTION; // Use the SOAP Action from the .env file
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;

const createSoapEnvelope = (data) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
  <soapenv:Header/>
  <soapenv:Body>
    <tem:CreatePolicy>
      <tem:Product>HealthTotal</tem:Product>
      <tem:XML>
        <![CDATA[<Root>
          <Uid>${data.Uid}</Uid>
  <VendorCode>${VENDOR_CODE}</VendorCode>
  <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
          <SentToOutSourcePrint></SentToOutSourcePrint>
          <WinNo />
          <ApplicationNo />
          <PolicyHeader>
            <PolicyStartDate>${data.PolicyHeader.PolicyStartDate}</PolicyStartDate>
            <PolicyEndDate>${data.PolicyHeader.PolicyEndDate}</PolicyEndDate>
            <AgentCode>${process.env.AGENT_CODE}</AgentCode>
    <BranchCode>${process.env.BRANCH_CODE}</BranchCode>
            <MajorClass>HTO</MajorClass>
            <ContractType>HTO</ContractType>
            <METHOD>ENQ</METHOD>
            <PolicyIssueType>I</PolicyIssueType>
            <PolicyNo />
            <ClientID></ClientID>
            <ReceiptNo />
          </PolicyHeader>
          <POS_MISP>
            <Type />
            <PanNo />
          </POS_MISP>
          <Client>
            <ClientCategory />
            <ClientType>I</ClientType>
            <CreationType></CreationType>
            <Salutation></Salutation>
            <FirstName></FirstName>
            <LastName></LastName>
            <DOB></DOB>
            <Gender>M</Gender>
            <MaritalStatus></MaritalStatus>
            <Occupation></Occupation>
            <PANNo/>
            <GSTIN />
            <AadharNo />
            <CKYCNo></CKYCNo>
            <CKYCRefNo/>
            <EIANo />
            <Address1>
              <AddrLine1>Test Address</AddrLine1>
              <AddrLine2></AddrLine2>
              <AddrLine3 />
              <Landmark />
              <Pincode>${data.Client.Address1.Pincode}</Pincode>
              <City></City>
              <State></State>
              <Country>IND</Country>
              <AddressType>R</AddressType>
              <HomeTelNo />
              <OfficeTelNo />
              <FAXNO />
              <MobileNo></MobileNo>
              <EmailAddr></EmailAddr>
            </Address1>
            <Address2>
              <AddrLine1>Thane</AddrLine1>
              <AddrLine2 />
              <AddrLine3 />
              <Landmark />
              <Pincode>400605</Pincode>
              <City>Thane</City>
              <State>Maharashtra</State>
              <Country>IND</Country>
              <AddressType>P</AddressType>
              <HomeTelNo />
              <OfficeTelNo />
              <FAXNO />
              <MobileNo />
              <EmailAddr />
            </Address2>
            <VIPFlag>N</VIPFlag>
            <VIPCategory />
          </Client>
          <Receipt>
            <UniqueTranKey></UniqueTranKey>
            <CheckType />
            <BSBCode />
            <TransactionDate></TransactionDate>
            <ReceiptType>IVR</ReceiptType>
            <Amount></Amount> 
            <TCSAmount />
            <TranRefNo></TranRefNo>
            <TranRefNoDate></TranRefNoDate>
          </Receipt>
          <Risk>
            <PolicyType>${data.Risk.PolicyType}</PolicyType>
            <Duration>${data.Risk.Duration}</Duration>
            <Installments>FULL</Installments>
            <PaymentType>CC</PaymentType>
            <IsFgEmployee>N</IsFgEmployee>
            <BranchReferenceID />
            <FGBankBranchStaffID />
            <BankStaffID />
            <BankCustomerID />
            <BancaChannel />
            <PartnerRefNo />
            <PayorID />
            <PayerName />
            <BeneficiaryDetails>
              ${data.BeneficiaryDetails.Member.map(member => `
              <Member>
                <MemberId>${member.MemberId}</MemberId>
                <AbhaNo />
                <InsuredName></InsuredName>
                <InsuredDob>${member.InsuredDob}</InsuredDob>
                <InsuredGender>M</InsuredGender>
                <InsuredOccpn>SVCM</InsuredOccpn>
                <CoverType>${member.CoverType}</CoverType>
                <SumInsured>${member.SumInsured}</SumInsured>
                <DeductibleDiscount />
                <Relation>${member.Relation}</Relation>
                <NomineeName>Test Nominee</NomineeName>
                <NomineeRelation>Mother</NomineeRelation>
                <AnualIncome />
                <Height>155</Height>
                <Weight>35</Weight>
                <NomineeAge>45</NomineeAge>
                <AppointeeName />
                <AptRelWithominee />
                <MedicalLoading>0</MedicalLoading>
                <PreExstDisease>N</PreExstDisease>
                <DiseaseMedicalHistoryList>
                  <DiseaseMedicalHistory>
                    <PreExistingDiseaseCode />
                    <MedicalHistoryDetail />
                  </DiseaseMedicalHistory>
                </DiseaseMedicalHistoryList>
              ${generateNomineeDetailsXML({member}, member.MemberId - 1, true)}
              </Member>`).join('')}
            </BeneficiaryDetails>
          </Risk>
        </Root>]]>
      </tem:XML>
    </tem:CreatePolicy>
  </soapenv:Body>
</soapenv:Envelope>`;

const sendSOAPRequest = async (data) => {
  try {

    const modifiedData = {
      ...data,
      Product: data.Product, // Ensure Product is passed      VendorCode: 'webagg',
      VendorUserId: 'webagg',
      PolicyHeader: {
        ...data.PolicyHeader,
        AgentCode: '60048599',
        BranchCode: '51',
        MajorClass: 'HTO',
        ContractType: 'HTO'
      },
      Risk: {
        ...data.Risk,
        PolicyType: data.Risk.PolicyType // Ensure PolicyType is passed
      }
    };

    const soapEnvelope = createSoapEnvelope(modifiedData);

    const headers = {
      "Content-Type": "text/xml; charset=utf-8",
      SOAPAction: SOAP_ACTION,
    };

    const response = await axios.post(SOAP_API_URL, soapEnvelope, { headers });
    console.log('SOAP Response:', response.data);
    const parsedResponse = await parseStringPromise(response.data);

    // Extract the relevant data from the response
    const createPolicyResult = parsedResponse['s:Envelope']['s:Body'][0]['CreatePolicyResponse'][0]['CreatePolicyResult'][0];

    // Parse the CDATA section if it exists
    let policyDetails;
    try {
      if (createPolicyResult) {
        policyDetails = await parseStringPromise(createPolicyResult);
      }
    } catch (parseError) {
      console.error('Error parsing policy details:', parseError);
    }

    if (policyDetails && policyDetails.Root && policyDetails.Root.Status && policyDetails.Root.Status[0] === "Fail") {
      const errorMessage = policyDetails.Root.ValidationError ? policyDetails.Root.ValidationError[0] : "Unknown error";
      console.log('Policy creation failedddddddddddd:', errorMessage);
      return {
        status: 'error',
        message: errorMessage,
        timestamp: new Date().toISOString()
      };
    }

    return {
      status: 'success',
      //rawResponse: parsedResponse,
      policyDetails: policyDetails || null,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('SOAP Request Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });

    // throw new Error(`SOAP Request Failed: ${error.message}`);
    return {
      status: 'error',
      message: `SOAP Request Failed: ${error.message}`,
      timestamp: new Date().toISOString()
    };
  }
};

module.exports = { sendSOAPRequest };
