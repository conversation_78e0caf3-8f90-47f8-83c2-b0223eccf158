const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new role
const create = async (data) => {
    try {
        const [result] = await db('role_management').insert({
            role_name: data.role_name,
            department_name: data.department_name,
            created_by: data.created_by,
            updated_by: data.updated_by
        });
        return result;
    } catch (error) {
        console.error("Error creating role:", error);
        throw error;
    }
};

// Find all roles
const findAll = async () => {
    try {
        return await db('role_management').select('*');
    } catch (error) {
        throw error;
    }
};

// Find role by ID983931608686
const findById = async (id) => {
    try {
        const role = await db('role_management').where({ id }).first();
        return role;
    } catch (error) {
        throw error;
    }
};

// Find diseases by disease name (partial match)
const findByName = async (name) => {
    try {
        const roles = await db('role_management')
            .where(function () {
                this.where('role_name', 'like', `%${name}%`)
                    .orWhere('department_name', 'like', `%${name}%`);
            });
        return roles;
    } catch (error) {
        console.error("Error finding diseases by name:", error);
        throw error;
    }
};


// Update role by ID
const updateById = async (id, data) => {
    try {

        const result = await db('role_management').where({ id }).update({
            role_name: data.role_name,
            department_name: data.department_name,
            updated_by: data.updated_by,
            updated_at: getCurrentTimestamp()
        });
        return result;
    } catch (error) {
        throw error;
    }
};

// Delete role by ID
const deleteById = async (id) => {
    try {
        const result = await db('role_management').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

// Reinstate role by ID
const reinstate = async (id) => {
    try {
        const result = await db('role_management').where({ id }).update({ status: 1, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

const newLastWeek = async () => {
    try {
        return await db('role_management')
            .where('created_at', '<', db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'));
    } catch (error) {
        throw error;
    }
};

// New diseases created this week
const newThisWeek = async () => {
    try {
        const query = db('role_management')
            .whereBetween('created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ]);



        return await query;
    } catch (error) {
        throw error;
    }
};

// Deactivated diseases updated this week
const deactivatedThisWeek = async () => {
    try {
        return await db('role_management')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);
    } catch (error) {
        throw error;
    }
};

// Deactivated diseases updated last week
const deactivatedLastWeek = async () => {
    try {
        const query = db('role_management')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited diseases updated this week
const editedThisWeek = async () => {
    try {
        const query = db('role_management')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited diseases updated last week
const editedLastWeek = async () => {
    try {
        return await db('role_management')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findAll,
    findById,
    findByName,
    updateById,
    deleteById,
    reinstate,
    newLastWeek,
    newThisWeek,
    deactivatedThisWeek,
    deactivatedLastWeek,
    editedThisWeek,
    editedLastWeek

};
