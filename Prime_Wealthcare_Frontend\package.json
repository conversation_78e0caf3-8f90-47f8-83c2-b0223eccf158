{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.16.7", "@mui/x-data-grid": "^7.14.0", "@mui/x-date-pickers": "^7.17.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.5", "chart.js": "^4.4.8", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "i": "^0.3.7", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.3", "jwt-decode": "^4.0.0", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-datepicker": "^7.4.0", "react-dom": "^18.3.1", "react-multi-carousel": "^2.8.6", "react-redux": "^9.1.2", "react-redux-loading-bar": "^5.0.8", "react-router-dom": "^6.26.1", "react-scripts": "5.0.1", "react-toastify": "^10.0.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sass": "^1.77.8", "toast": "^0.5.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}