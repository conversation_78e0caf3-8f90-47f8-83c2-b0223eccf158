import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import CustomTextField from "../../../components/CustomTextField";
import Box from "@mui/material/Box";
import { useDispatch, useSelector } from "react-redux";
import ModuleName from "../../../components/table/ModuleName";
import Dropdown from "../../../components/table/DropDown";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CustomFileUpload from "../../../components/CustomFileUpload";
import CustomSection from "../../../components/CustomSection";
import dayjs from "dayjs";
import { createAgentDetails, fetchAllImfBranches, getAllRoles, updateAgentDetails, getAgentByIdForEdit, getAllAgentDetails, fetchEmployeeData } from "../../../redux/actions/action";
import { Radio, RadioGroup, FormControlLabel, FormControl, FormLabel, Typography, Divider, Icon, IconButton } from "@mui/material";
import { Add, AddIcCallOutlined, Cancel, Save } from "@mui/icons-material";

function CreateProposal() {

    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const location = useLocation();



    const minAgeDate = dayjs().subtract(18, "year");
    const [isViewMode, setIsViewMode] = useState(false);
    // const isViewMode = agentDetails?.status === 0;
    // const isEditMode = id && agentDetails?.status === 1;
    const [isEditMode, setIsEditMode] = useState(false);
    const [proposalType, setProposalType] = useState('New');

    const handleChange = (e) => {
    }

    const handleFileSelect = () => {
    }

    const handleDateChange = (name, date) => {
    }

    const handleSearchDuplicate = (e) => {
    }

    const handleSave = () => {
    }

    const handleCancel = () => {
        navigate('/dashboard/crm-dashboard')
    }

    const handleProposalTypeChange = (event) => {
        setProposalType(event.target.value);
    };

    return (
        <Box sx={{
            padding: { xs: '0 5px 5px', md: '0 40px 40px' },
            width: '100%'
        }}>
            <form encType="multipart/form-data" style={{ width: '100%' }}>
                <Grid container style={{ display: "flex" }}>
                    {/* Header Row */}
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Proposal" pageName={id ? (isViewMode ? "View" : "Edit") : "Create"} />
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                            <IconButton onClick={handleSave} sx={{ color: 'green', mx: 0.5 }}>
                                <Save />
                            </IconButton>
                            <IconButton onClick={handleCancel} sx={{ color: 'red', mx: 0.5 }}>
                                <Cancel />
                            </IconButton>
                        </Box>
                        <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
                            <Button onClick={handleSave} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', textTransform: 'none' }}>
                                Save
                            </Button>
                            <Button onClick={handleCancel} variant="outlined" size="small" sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', textTransform: 'none' }} >
                                Cancel
                            </Button>
                        </Box>
                    </Grid>
                    {/* <Grid container >
                        <CustomSection titles={['Overview', 'Personal Details', 'Address']} page='proposal' />
                    </Grid> */}
                </Grid>
                {/* Proposal Type */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Proposal Type
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Radio Buttons */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' }
                    }}>
                        <FormControl component="fieldset" sx={{ width: '100%' }}>
                            <RadioGroup
                                row
                                aria-label="proposal-type"
                                name="proposal-type"
                                value={proposalType}
                                onChange={handleProposalTypeChange}
                                sx={{
                                    display: 'flex',
                                    flexDirection: { xs: 'column', sm: 'row' },
                                    gap: { xs: '0.5rem', sm: '1rem' },
                                    '& .MuiFormControlLabel-root': {
                                        flex: { xs: '1 1 100%', sm: '1 1 auto' }
                                    }
                                }}
                            >
                                <FormControlLabel
                                    value="New"
                                    control={<Radio sx={{
                                        color: '#528A7E',
                                        '&.Mui-checked': { color: '#528A7E' }
                                    }} />}
                                    label="New"
                                />
                                <FormControlLabel
                                    value="Renewal"
                                    control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                    label="Renewal"
                                    onClick={() => {
                                        setProposalType('Renewal'); // Set the proposal type to Renewal
                                        navigate('/dashboard/proposal-renewal'); // Navigate to the renewal page
                                    }}
                                />
                                <FormControlLabel
                                    value="Roll Over"
                                    control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                    label="Roll Over"
                                />
                                <FormControlLabel
                                    value="Migration"
                                    control={<Radio sx={{ color: '#528A7E', '&.Mui-checked': { color: '#528A7E' } }} />}
                                    label="Migration"
                                />
                            </RadioGroup>
                        </FormControl>
                    </Box>
                </Box>
                {/* Proposal Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Proposal Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="quotation_number"
                                label="Quotation Number"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="created_at"
                                label="Created At"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
                {/* Agent Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Agent Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="agent_code"
                                label="Agent Code"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="agent_name"
                                label="Agent Name"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="branch_name"
                                label="Branch Name"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
                {/* Insurance Company Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Insurance Company Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Insurance Company Name"
                                name="insurance_company_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Insurance Company Branch"
                                name="insurance_company_branch_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Branch Agent Code"
                                name="branch_agent_code"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Product Type"
                                name="product_type_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Product Name"
                                name="product_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Sub Product Name"
                                name="sub_product_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Member Type"
                                name="member_type_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
                {/* Personal Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Personal Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Customer Name"
                                name="customer_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={2} sm={1} md={1} lg={1} style={{ display: 'flex', alignItems: 'center' }}>
                            <Button
                                onClick={handleSave}
                                variant="outlined"
                                size="small"
                                sx={{
                                    minWidth: 0,
                                    padding: { xs: '8px', sm: '6px 16px' },
                                    color: 'green',
                                    borderColor: 'green',
                                    '& .MuiButton-startIcon': {
                                        margin: 0
                                    }
                                }}
                            >
                                <Save />
                            </Button>
                        </Grid>
                    </Grid>
                </Box>
                {/* Member Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Member Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="first_name"
                                label="First Name"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="middle_name"
                                label="Middle Name"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="last_name"
                                label="Last Name"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <FormControl fullWidth required error={'put something here'}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="DOB"
                                        // value={formData.marriage_date ? dayjs(formData.marriage_date) : null}
                                        onChange={(date) => handleDateChange('dob', date)}
                                        // maxDate={minAgeDate}
                                        disabled={isViewMode}
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                //required:true,
                                                // error: Boolean(formErrors.marriage_date),
                                                // helperText: formErrors.marriage_date || 'Must be at least 18 years old',
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Relation"
                                name="relation_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Marital Status"
                                name="marital_status_id"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <FormControl fullWidth required error={'put something here'}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="Marriage Date"
                                        // value={formData.marriage_date ? dayjs(formData.marriage_date) : null}
                                        onChange={(date) => handleDateChange('marriage_date', date)}
                                        // maxDate={minAgeDate}
                                        disabled={isViewMode}
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                //required:true,
                                                // error: Boolean(formErrors.marriage_date),
                                                // helperText: formErrors.marriage_date || 'Must be at least 18 years old',
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="mobile_number"
                                label="Mobile Number"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="email_id"
                                label="Email Id"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="sum_insured"
                                label="Sum Insured"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
                {/* Proposal Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Proposal Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="sum_insured"
                                label="Sum Insured"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="net_premium"
                                label="Net Premium"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="gst_amount"
                                label="GST Amount"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="gst_percentage"
                                label="GST Percentage"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="total_premium"
                                label="Total Premium"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <FormControl fullWidth required error={'put something here'}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="Issue Date"
                                        // value={formData.marriage_date ? dayjs(formData.marriage_date) : null}
                                        onChange={(date) => handleDateChange('issue_date', date)}
                                        // maxDate={minAgeDate}
                                        disabled={isViewMode}
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                //required:true,
                                                // error: Boolean(formErrors.marriage_date),
                                                // helperText: formErrors.marriage_date || 'Must be at least 18 years old',
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="policy_number"
                                label="Policy Number"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <FormControl fullWidth required error={'put something here'}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="Start Date"
                                        // value={formData.marriage_date ? dayjs(formData.marriage_date) : null}
                                        onChange={(date) => handleDateChange('start_date', date)}
                                        // maxDate={minAgeDate}
                                        disabled={isViewMode}
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                //required:true,
                                                // error: Boolean(formErrors.marriage_date),
                                                // helperText: formErrors.marriage_date || 'Must be at least 18 years old',
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Tenure"
                                name="tenure"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <FormControl fullWidth required error={'put something here'}>
                                <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        label="End Date"
                                        // value={formData.marriage_date ? dayjs(formData.marriage_date) : null}
                                        onChange={(date) => handleDateChange('end_date', date)}
                                        // maxDate={minAgeDate}
                                        disabled={isViewMode}
                                        slotProps={{
                                            textField: {
                                                fullWidth: true,
                                                //required:true,
                                                // error: Boolean(formErrors.marriage_date),
                                                // helperText: formErrors.marriage_date || 'Must be at least 18 years old',
                                                sx: {
                                                    '& .MuiOutlinedInput-root': {
                                                        '&::before': {
                                                            content: '""',
                                                            position: 'absolute',
                                                            left: 0,
                                                            top: 0,
                                                            bottom: 0,
                                                            width: '3px',
                                                            backgroundColor: 'red',
                                                            zIndex: 1,
                                                        }
                                                    },
                                                }
                                            }
                                        }}
                                    />
                                </LocalizationProvider>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomFileUpload
                                name="policy_pdf"
                                section_name="Upload Policy Pdf"
                                accept=".pdf,.jpeg,.png,.jpg"
                                label={"Upload Policy Pdf"}
                                onFileSelect={handleFileSelect}
                                insertedFile={null}
                                helperText={''}
                                error={''}
                                isRequired
                                disabled={isViewMode}
                                sx={{
                                    '& .MuiFormHelperText-root': {
                                        display: 'block',
                                        visibility: 'visible'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
                {/* Payment Details */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Payment Details
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <Dropdown
                                label="Select Payment Type"
                                name="payment_type"
                                options={[]}
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                required
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="cash_amount"
                                label="Cash Amount"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="received_date"
                                label="Received Date"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="cheque_number"
                                label="Cheque Number"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="bank_name"
                                label="Bank Name"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="cheque_date"
                                label="Cheque Date"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
                {/* Remarks */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            Remarks
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid item xs={12}>
                        <CustomTextField
                            label="Remarks"
                            multiline
                            rows={3}
                            fullWidth
                            name="remarks"
                            value={''}
                            onChange={handleChange}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    width: '100%'
                                }
                            }}
                        />
                    </Grid>
                </Box>
                {/* User Info */}
                <Box sx={{
                    width: '100%',
                    borderTop: '2px solid #E0E0E0',
                    borderBottom: '2px solid #E0E0E0',
                    padding: { xs: '0.5rem', sm: '1rem' },
                    marginTop: { xs: 2, sm: 3 }
                }}>
                    {/* Section Header */}
                    <Box sx={{
                        width: '100%',
                        padding: { xs: "10px", sm: "15px" },
                        borderRadius: "4px",
                    }}>
                        <Typography
                            variant="h5"
                            sx={{
                                fontSize: { xs: "18px", sm: "20px", md: "22px" },
                                fontWeight: "700",
                                color: '#4C5157',
                            }}
                        >
                            User Info
                        </Typography>
                    </Box>
                    <Divider />

                    {/* Form Fields */}
                    <Grid container spacing={2} sx={{
                        width: '100%',
                        padding: { xs: '1rem', sm: '1.5rem' },
                        '& .MuiGrid-item': {
                            width: '100%'
                        }
                    }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="created_by"
                                label="Created By"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="created_at"
                                label="Created At"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="updated_by"
                                label="Updated By"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <CustomTextField
                                name="updated_at"
                                label="Updated At"
                                value={''}
                                onChange={handleChange}
                                fullWidth
                                helperText={''}
                                isRequired
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        width: '100%'
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </Box>
            </form>
        </Box >
    )
}

export default CreateProposal;