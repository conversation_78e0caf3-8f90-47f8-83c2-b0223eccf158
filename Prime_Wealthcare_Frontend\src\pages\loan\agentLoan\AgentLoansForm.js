import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { currentFinancialYear, formatDate, trimFormData } from '../../../utils/Reusable';
import dayjs from 'dayjs';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { FormControl, Typography } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import Dropdown from '../../../components/table/DropDown';
import { createAgentLoan, getAgentLoanById, getAllAgentDetails, getAllPickLists, getAgentLoanEmis, updateAgentLoan } from '../../../redux/actions/action';
import { clearAgentLoanDetails } from '../../../redux/slices/loans/agentLoanSlice';

function AgentLoansForm() {

    const { id } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();

    const isCreateMode = !id;
    const [isViewMode, setIsViewMode] = useState(location.pathname.includes('view'));

    const { user } = useSelector((state) => state.auth);
    const agentLoan = useSelector(state => state.agentLoanReducer.agentLoanDetails);
    const agents = useSelector(state => state.agentReducer.agents);
    const loanOptions = useSelector(state => state.pickListReducer.loanTypeOptions);
    const approvalOptions = useSelector(state => state.pickListReducer.approvalStatusOptions);

    const [formData, setFormData] = useState({
        agent_id: '',
        loan_type: '',
        loan_amount: '',
        tenure: '',
        emi: '',
        issue_date: '',
        start_date: '',
        end_date: '',
        remarks: '',
        admin_approval: '',
    });

    const [errors, setErrors] = useState(formData);

    useEffect(() => {
        resetFormData();
        dispatch(clearAgentLoanDetails());
        dispatch(getAllAgentDetails());
        dispatch(getAllPickLists());
        if (id && !isCreateMode) {
            dispatch(getAgentLoanById(id));
        }
    }, []);

    useEffect(() => {
        if (isCreateMode) {
            setFormData(prevFormData => ({
                ...prevFormData,
                admin_approval: 'PENDING'
            }));
        }
    }, [approvalOptions])

    useEffect(() => {
        if (agentLoan && id) {
            setFormData(prevFormData => ({
                ...prevFormData,
                agent_id: agentLoan.agent_id || '',
                loan_type: Number(agentLoan.loan_type) || '',
                loan_amount: Number(agentLoan.loan_amount) || '',
                tenure: Number(agentLoan.tenure) || '',
                emi: Number(agentLoan.emi) || '',
                issue_date: agentLoan.issue_date ? dayjs(agentLoan.issue_date) : '',
                start_date: agentLoan.start_date ? dayjs(agentLoan.start_date) : '',
                end_date: agentLoan.end_date ? dayjs(agentLoan.end_date) : '',
                remarks: agentLoan.remarks || '',
                admin_approval: agentLoan.admin_approval || '',
            }));
            if (agentLoan.admin_approval === 'REJECTED' || agentLoan.admin_approval === 'APPROVED') {
                setIsViewMode(true);
            }
        }
    }, [agentLoan, id]);

    const resetFormData = () => {
        setFormData(prevFormData => ({
            ...prevFormData,
            agent_id: '',
            loan_type: '',
            loan_amount: '',
            tenure: '',
            emi: '',
            issue_date: '',
            start_date: '',
            end_date: '',
            remarks: '',
            admin_approval: '',
        }));
    };

    const validate = () => {
        let tempErrors = {};
        if (!formData.agent_id) {
            tempErrors.agent_id = 'Agent is required';
        }
        if (!formData.loan_type) {
            tempErrors.loan_type = 'Loan Type is required';
        }
        if (!formData.loan_amount) {
            tempErrors.loan_amount = 'Loan Amount is required';
        }
        if (!formData.tenure) {
            tempErrors.tenure = 'Tenure is required';
        }
        if (!formData.admin_approval) {
            tempErrors.admin_approval = 'Admin Approval is required'
        }
        if (formData.admin_approval === 'APPROVED') {
            if (!formData.issue_date) {
                tempErrors.issue_date = 'Issue Date is required';
            }
            if (!formData.start_date) {
                tempErrors.start_date = 'Start Date is required';
            }
            if (!formData.end_date) {
                tempErrors.end_date = 'End Date is required';
            }
        }
        setErrors(tempErrors);

        // Return true if there are no errors
        return Object.keys(tempErrors).length === 0;
    };

    const handleDateChange = (name, date) => {
        if (name === 'start_date') {
            if (!date) {
                // If date is null/undefined, clear the start_date
                setFormData(prev => ({
                    ...prev,
                    start_date: null,
                    end_date: null
                }));
                return;
            }

            if (formData.tenure) {
                // Calculate end date based on tenure (1 year minus 1 day)
                const endDate = dayjs(date).add(Number(formData.tenure), 'month').subtract(1, 'day');
                setFormData(prev => ({
                    ...prev,
                    start_date: date,
                    end_date: endDate
                }));
                setErrors(prev => ({
                    ...prev,
                    start_date: '',
                    end_date: ''
                }));
            } else {
                setFormData(prev => ({
                    ...prev,
                    start_date: date
                }));
                setErrors(prev => ({
                    ...prev,
                    start_date: ''
                }));
            }
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: date
            }));
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;

        if (value === ' ') {
            setErrors({
                ...errors,
                [name]: 'Do not start with a whitespace character'
            })
            return;
        }
        let additional_fields = {};
        let additional_field_errors = {};
        if ((['loan_amount', 'tenure']).includes(name)) {
            if (!/^\d*?\d*$/.test(value) && value !== '') {
                setErrors({
                    ...errors,
                    [name]: 'Only numbers are allowed'
                });
                return;
            }
            // if (formData.loan_amount !== '' && formData.tenure !== '') {
            const loan_amount = name === 'loan_amount' ? value : formData?.loan_amount || 0;
            const tenure = name === 'tenure' ? value : formData?.tenure || 0;
            const emi = Math.round(loan_amount / tenure);

            if (loan_amount !== '' && tenure !== '') {
                additional_fields = {
                    ...additional_fields,
                    emi: emi === Infinity ? '' : emi
                }
                if (additional_fields.emi >= 0) {
                    additional_field_errors = {
                        ...additional_field_errors,
                        emi: ''
                    }
                    // }
                }
            }
        }
        if (name === 'tenure' && formData.start_date !== '') {
            additional_fields = {
                ...additional_fields,
                end_date: dayjs(formData.start_date).add(value, 'month').subtract(1, 'day')
            }
        }

        const data = typeof value === 'string' ? value.toUpperCase().replace(/\s{2,}$/, ' ') : value;

        setFormData({
            ...formData,
            [name]: data,
            ...additional_fields
        });

        setErrors({
            ...errors,
            [name]: false,
            ...additional_field_errors
        })
    }


    const handleAgentLoanCreationAndUpdate = () => {
        const isValid = validate();
        if (!isValid) return; // Stop if the form is invalid

        const { issue_date, start_date, end_date, ...rest } = formData;

        const data = {
            ...isCreateMode ? {
                agent_id: Number(formData.agent_id),
                paid_amount: 0,
                created_by: user.userId,
                updated_by: user.userId,
            } : {
                ...(formData.admin_approval === 'APPROVED' && {
                    issue_date: dayjs(issue_date).format('YYYY-MM-DD'),
                    start_date: dayjs(start_date).format('YYYY-MM-DD'),
                    end_date: dayjs(end_date).format('YYYY-MM-DD'),
                }),
                updated_by: user.userId,
            },
            ...rest
        };
        const filteredData = trimFormData(data);

        if (!isCreateMode) {
            dispatch(updateAgentLoan({ id, loanData: filteredData }));
            resetFormData();
            handleCancel();
        }
        else {
            dispatch(createAgentLoan(filteredData));
            handleCancel();
        }
    };

    const handleCancel = () => {
        navigate(`/dashboard/agent-loans`);
    };

    const handleEmiDetails = () => {
        const viewMode = location.pathname.includes('view');
        navigate(`/dashboard/agent-emi-details/${viewMode ? `view/${id}` : `${id}`}`);
    }

    return (
        <form encType="multipart/form-data" style={{ width: '100%' }}>
            <Grid container spacing={2}>
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }} />

                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ModuleName moduleName="Agent Loan" pageName={id ? agentLoan?.status === 0 ? "View" : "Edit" : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!isCreateMode ? !isViewMode ? (

                        <>
                            {(agentLoan?.status === 1 || !isCreateMode) && <Button
                                variant="outlined"
                                size="small"
                                sx={{
                                    maxWidth: '120px', // Increase width if needed
                                    width: '120px', // Set a fixed width
                                    mx: 0.5,
                                    color: 'green',
                                    borderColor: 'green',
                                    mt: 3,
                                    textTransform: 'none',
                                    fontSize: '0.875rem', // Adjust font size if needed
                                    whiteSpace: 'nowrap' // Prevent text from wrapping
                                }}
                                onClick={handleAgentLoanCreationAndUpdate}
                            >
                                Update & New
                            </Button>}
                            {(agentLoan?.status === 1 || !id) && <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleAgentLoanCreationAndUpdate}
                            >
                                Update
                            </Button>}
                        </>
                    ) : null : (
                        // Buttons for Create Mode
                        <>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleAgentLoanCreationAndUpdate}
                            >
                                Save & New
                            </Button>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleAgentLoanCreationAndUpdate}
                            >
                                Save
                            </Button>
                        </>
                    )}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem 3rem', borderRadius: '4px', mb: 2, display: 'flex', justifyContent: 'space-between' }}>
                        <h2>Agent Loan Details</h2>
                        {id && agentLoan?.admin_approval === 'APPROVED' &&
                            <Typography variant="small"
                                sx={{
                                    color: '#528a7e',
                                    textTransform: 'none',
                                    mx: 1,
                                    cursor: 'pointer'
                                }}
                                onClick={() => handleEmiDetails()}
                            >
                                View EMI Details
                            </Typography>
                        }
                    </Box>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Dropdown
                            name="agent_id"
                            label="Agent Name / Agent Code"
                            options={agents?.filter(agent => agent.status === 1).map(agent => {
                                return {
                                    label: `${agent.agent_id}(${agent.full_name})`,
                                    value: agent.id
                                }
                            })}
                            value={formData.agent_id}
                            onChange={(e) => handleChange(e)}
                            helperText={errors.agent_id}
                            fullWidth
                            required
                            disabled={agentLoan?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Dropdown
                            name="loan_type"
                            label="Loan Type"
                            options={loanOptions
                                .filter(option => {
                                    const label_name = option.label_name?.toLowerCase() || '';
                                    return !label_name.includes('salary');
                                }).map((option) => ({
                                    value: option.id,
                                    label: option.label_name
                                }))}
                            value={formData.loan_type}
                            onChange={(e) => handleChange(e)}
                            helperText={errors.loan_type}
                            fullWidth
                            disabled={agentLoan?.status === 0 || isViewMode}
                            required
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="loan_amount"
                            label="Loan Amount"
                            value={formData.loan_amount}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.loan_amount}
                            isRequired
                            isDisabled={agentLoan?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="tenure"
                            label="Tenure (Months)"
                            value={formData.tenure}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.tenure}
                            isRequired
                            isDisabled={agentLoan?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="emi"
                            label="EMI"
                            value={formData.emi}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.emi}
                            isRequired
                            isDisabled
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <FormControl fullWidth>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    name='issue_date'
                                    label="Issue Date"
                                    onChange={(newDate) => handleDateChange('issue_date', newDate)}
                                    value={formData?.issue_date ? dayjs(formData.issue_date) : null}
                                    format="DD/MM/YYYY"
                                    disabled={isCreateMode || agentLoan?.status === 0 || isViewMode || formData?.admin_approval !== 'APPROVED'}
                                    slotProps={formData.admin_approval === 'APPROVED' && {
                                        textField: {
                                            fullWidth: true,
                                            error: Boolean(errors.issue_date),
                                            helperText: errors.issue_date,
                                            sx: {
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <FormControl fullWidth>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    name='start_date'
                                    label="Start Date"
                                    onChange={(newDate) => handleDateChange('start_date', newDate)}
                                    minDate={dayjs(formData?.issue_date)}
                                    value={formData?.start_date ? dayjs(formData.start_date) : null}
                                    format="DD/MM/YYYY"
                                    disabled={isCreateMode || agentLoan?.status === 0 || isViewMode || formData?.issue_date === ''}
                                    slotProps={formData.admin_approval === 'APPROVED' && {
                                        textField: {
                                            fullWidth: true,
                                            error: Boolean(errors.start_date),
                                            helperText: errors.start_date,
                                            sx: {
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <FormControl fullWidth>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    name='end_date'
                                    label="End Date"
                                    value={formData?.end_date ? dayjs(formData.end_date) : null}
                                    format="DD/MM/YYYY"
                                    disabled
                                    slotProps={formData.admin_approval === 'APPROVED' && {
                                        textField: {
                                            fullWidth: true,
                                            error: Boolean(errors.end_date),
                                            helperText: errors.end_date,
                                            sx: {
                                                '& .MuiOutlinedInput-root': {
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: 0,
                                                        bottom: 0,
                                                        width: '3px',
                                                        backgroundColor: 'red',
                                                        zIndex: 1,
                                                    }
                                                },
                                            }
                                        }
                                    }}
                                />
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Dropdown
                            name="admin_approval"
                            label="Admin Approval"
                            options={approvalOptions.map((option) => ({
                                value: option.label_name,
                                label: option.label_name
                            }))}
                            value={formData.admin_approval}
                            onChange={(e) => handleChange(e)}
                            helperText={errors.admin_approval}
                            fullWidth
                            required
                            disabled={isCreateMode || agentLoan?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12}
                        sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            padding: { xs: '1rem', sm: '1.5rem' },
                        }}>
                        <CustomTextField
                            label="Remarks"
                            multiline
                            rows={3}
                            fullWidth
                            disabled={id}
                            name="remarks"
                            value={formData.remarks}
                            onChange={handleChange}
                            sx={{
                                width: '80%',
                            }}
                        />
                    </Grid>
                </Box>
            </Grid>
        </form>
    );
}

export default AgentLoansForm;