const crypto = require('crypto');
const { create, createPayment, updatePayment, checkIfSuccess, getByProposalNumber } = require('../Models/paymentModel');

const generateChecksum = (data) => {
    const concatenatedString = data.join('|') + '|'; // Add trailing '|'
    return crypto.createHash('sha256').update(concatenatedString, 'utf8').digest('hex');
};


const createPaymentDetails = async (req, res) => {
    try {
        const data = req.body;
        const result = await create(data);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};

const updatePaymentDetails = async (req, res) => {
    try {
        const result = await updatePayment(req.params.id, req.body);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};

const checkSuccess = async (req, res) => {
    try {
        const result = await checkIfSuccess(req.params.proposal_number);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
}

const submitPayment = async (req, res) => {
    try {
        const {
            TransactionID, PaymentOption, ResponseURL,
            ProposalNumber, PremiumAmount, UserIdentifier,
            UserId, FirstName, LastName, Mobile, Email, Vendor
        } = req.body;

        // Step 1: Generate checksum
        const checksumData = [
            TransactionID, PaymentOption, ResponseURL,
            ProposalNumber, PremiumAmount, UserIdentifier,
            UserId, FirstName, LastName, Mobile, Email, Vendor
        ];
        const CheckSum = generateChecksum(checksumData);

        // Step 2: Save to database
        const paymentData = {
            TransactionID, PaymentOption, ResponseURL,
            ProposalNumber, PremiumAmount, UserIdentifier,
            UserId, FirstName, LastName, Mobile, Email, Vendor, CheckSum
        };
        await createPayment(paymentData);

        // Step 3: Forward the request to the payment gateway
        const paymentGatewayURL = "https://fgnluat.fggeneral.in/Ecom_UAT/WEBAPPLN/UI/Common/WebAggPayNew.aspx";
        res.json({
            success: true,
            message: 'Checksum generated and payment data submitted',
            data: { ...paymentData, actionURL: paymentGatewayURL },
        });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};

const updatePaymentStatus = async (req, res) => {
    try {
        const result = await updatePayment(req.params.id, req.body);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};

const getPaymentByProposalNumber = async (req, res) => {
    try {
        const result = await getByProposalNumber(req.params.proposal_number);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};

module.exports = {
    submitPayment,
    createPaymentDetails,
    updatePaymentDetails,
    updatePaymentStatus,
    checkSuccess,
    getPaymentByProposalNumber
};
