const express = require('express');
const productMasterController = require('../Controllers/productMasterController');
const router = express.Router();

// Route to get all products
router.get('/', productMasterController.getAllProducts);

// Route to get a product by ID
router.get('/:id', productMasterController.getProductById);

// Route to create a new product
router.post('/', productMasterController.createProduct);

// Route to update a product by ID
router.put('/:id', productMasterController.updateProduct);

// Route to delete (soft delete) a product by ID
router.delete('/:id', productMasterController.deleteProduct);

// Route to reinstate a product by ID
router.put('/reinstate/:id', productMasterController.reinstateProduct);

// Route to get products by name
router.get('/name/:name', productMasterController.getMasterProductByName)

// Route to get products by criteria
router.get('/criteria/:criteria', productMasterController.getMasterProductsByCriteria);

// Route to get product by main product and insurance company
router.get('/getMasterProductByMainProductAndInsuranceCompany/:mainProductId/:insuranceCompanyId', productMasterController.getMasterProductByMainProductAndInsuranceCompany);

module.exports = router;
