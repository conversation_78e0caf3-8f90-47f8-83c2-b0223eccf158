import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
  fetchInsuranceCompanyBranches,
  softDeleteInsuranceCompanyBranch, fetchInsuranceBranchesByCriteria,
  reinstateInsuranceCompanyBranch, searchInsuranceBranches, fetchImfAgencyCodes
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';

const InsuranceBranchPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [selectedOption, setSelectedOption] = useState('none');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const [sortedBranches, setSortedBranches] = useState([]);
  const [isAgencyCodeEditable, setIsAgencyCodeEditable] = useState(false); // New state for agency code editability

  const insuranceBranches = useSelector(state => state.insuranceBranchReducer.branches);
  const agencyCodes = useSelector(state => state.imfAgencyCodeReducer.agencyCodes);
  const permissions = usePermissions('master', 'Insurance Branch');

  useEffect(() => {
    dispatch(fetchInsuranceCompanyBranches());
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchImfAgencyCodes());
  }, [dispatch]);


  useEffect(() => {
    const fetchBranches = () => {
      dispatch(fetchInsuranceBranchesByCriteria(selectedOption));
    };
    fetchBranches();
  }, [selectedOption, dispatch]);

  // Filter branches by status
  useEffect(() => {
    const filterBranchesByStatus = () => {
      if (statusFilter === 'all') {
        setSortedBranches(insuranceBranches);
      } else if (statusFilter === 'none') {
        dispatch(fetchInsuranceCompanyBranches());
      } else {
        setSortedBranches(insuranceBranches.filter(branch => branch.status === (statusFilter === 'active' ? 1 : 0)));
      }
    }

    filterBranchesByStatus();
  }, [statusFilter, insuranceBranches, dispatch])

  useEffect(() => {
    const sortAndFilterBranches = async () => {
      let filteredBranches = [...insuranceBranches]

      // Status filter
      if (statusFilter !== 'all') {
        filteredBranches = filteredBranches.filter(branch => branch.status === (statusFilter === 'active' ? 1 : 0));
      }

      setSortedBranches(filteredBranches);
    };

    sortAndFilterBranches();
  }, [selectedOption, insuranceBranches, statusFilter, dispatch]);

  /*  useEffect(() => {
     if ( insuranceBranches  &&  insuranceBranches .length > 0) {
       // Sort by 'created_at' in reverse order (newest first)
       const sortedData = [... insuranceBranches ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
       setSortedBranches(sortedData);
     }
   }, [ insuranceBranches ]); */


  const handleSearch = (query) => {
    if (query === '') {
      dispatch(fetchInsuranceCompanyBranches());
    } else {
      dispatch(searchInsuranceBranches(query));
    }
  }

  const handleOpenDeletePopup = (item) => {
    setSelectedItem(item);
    setOpenDeletePopup(true);
  };

  const handleCloseDeletePopup = () => {
    setOpenDeletePopup(false);
    setSelectedItem(null);
  };

  const handleConfirmDelete = () => {
    dispatch(softDeleteInsuranceCompanyBranch(selectedItem.id))
      .then(() => {
        dispatch(fetchInsuranceCompanyBranches());
        dispatch(fetchImfAgencyCodes());
        setOpenDeletePopup(false);
        setOpenSuccessPopup(true);
      })
      .catch(error => {
        console.error("Failed to delete insurance branch:", error);
        setOpenDeletePopup(false);
      });
  };

  const handleCloseSuccessPopup = () => {
    setOpenSuccessPopup(false);
  };

  const handleAdd = () => {
    navigate('/dashboard/insurance-branch-form');
  };

  const handleDelete = (id) => {
    handleOpenDeletePopup(insuranceBranches.find(branch => branch.id === id));
  };

  /* const handleReinstate = (id) => {
    dispatch(reinstateInsuranceCompanyBranch(id))
      .then(() => {
        setIsAgencyCodeEditable(true); // Enable agency code fields
        // Update the sortedBranches to set date fields to "N/A"
      setSortedBranches(prevBranches => 
        prevBranches.map(branch => 
          branch.id === id 
            ? { ...branch, license_valid_from: 'N/A', license_valid_till: 'N/A' } // Set dates to "N/A"
            : branch
        )
      );
      })
      .catch(error => {
        console.error("Failed to reinstate insurance branch:", error);
      });
  }; */


  const handleReinstate = (id) => {
    // Find the branch to reinstate
    const branchToReinstate = insuranceBranches.find((branch) => branch.id === id);
  
    if (!branchToReinstate) {
      console.error("Branch not found for ID:", id);
      return;
    }
  
    // Check if the agency code exists for the branch
    const relatedAgencyCode = agencyCodes.find((agencyCode) =>
      agencyCode.insurance_company_id === branchToReinstate.insurance_company_id &&
      agencyCode.insurance_co_branch_name === branchToReinstate.insurance_co_branch_name
    );
  
    if (!relatedAgencyCode) {
      // Show an alert if no agency code exists
      // alert("To reinstate, you must add the data for the agency code.");
      navigate(`/dashboard/insurance-branch/edit/${id}`, {
        state: {
          branch: branchToReinstate,
          agencyCode: null, // No agency code available
        },
      });
      return;
    }
  
    // If agency code exists, proceed with reinstatement
    dispatch(reinstateInsuranceCompanyBranch(id))
      .then(() => {
        setIsAgencyCodeEditable(true); // Enable agency code fields
        // Update the sortedBranches to set date fields to "N/A"
        setSortedBranches((prevBranches) =>
          prevBranches.map((branch) =>
            branch.id === id
              ? { ...branch, license_valid_from: "N/A", license_valid_till: "N/A" } // Set dates to "N/A"
              : branch
          )
        );
      })
      .catch((error) => {
        console.error("Failed to reinstate insurance branch:", error);
      });
  };
  

  const handleEdit = (id) => {
    // Find the branch by its ID
    const branchToEdit = insuranceBranches.find((branch) => branch.id === id);

    if (branchToEdit) {
      // Find the related agency code based on the branch details
      const agencyCodeToEdit = agencyCodes.find((agencyCode) =>
        agencyCode.insurance_company_id === branchToEdit.insurance_company_id &&
        agencyCode.insurance_co_branch_name === branchToEdit.insurance_co_branch_name
      );

      // Navigate to the edit page with both branch and agency code (agency code disabled)
      navigate(`/dashboard/insurance-branch/edit/${id}`, {
        state: {
          branch: branchToEdit,
          agencyCode: agencyCodeToEdit , // Pass agency code or null if not found
          allowEditAgencyCode: true, // Allow editing the agency code
                    // isAgencyCodeDisabled: false      // Enable agency code fields
        }
      });
    } else {
      console.error('Branch not found for ID:', id);
    }
  };

  const handleSelectionChange = (id) => {
    setSelectedRows(prevSelected =>
      prevSelected.includes(id)
        ? prevSelected.filter(rowId => rowId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSelectAll = (isSelected) => {
    setSelectedRows(isSelected ? insuranceBranches.map(branch => branch.id) : []);
  };

  const handleAllClick = () => setStatusFilter('all');
  const handleActiveClick = () => setStatusFilter('active');
  const handleInactiveClick = () => setStatusFilter('inactive');
  const handleRefreshClick = () => {
    setSelectedOption('none');
    dispatch(fetchInsuranceCompanyBranches());
  }

  const formatDate = (dateString) => {
    if (!dateString) return ''; // Handle cases where dateString is null or undefined

    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.error("Invalid date:", dateString); // Log invalid dates for debugging
      return ''; // Return an empty string for invalid dates
    }

    return date.toLocaleDateString('en-GB');  // Format date as dd/mm/yyyy
  };


  const columns = [
    { field: 'id', headerName: 'ID', width: '5%' },
    { field: 'insurance_company_name', headerName: 'Company Name', width: '15%' },
    { field: 'insurance_co_branch_name', headerName: 'Ins.Co.Branch', width: '10%' },
    { field: 'insurance_type', headerName: 'Insurance Type', width: '10%' },
    { field: 'imf_code', headerName: 'IMF Code', width: '10%' },
    { field: 'branch_code', headerName: 'Branch Code', width: '10%' },
    { field: 'status', headerName: 'Status', width: '10%' },
   /*  { field: 'license_no', headerName: 'License No', width: '10%' },
    {
      field: 'license_valid_from',
      headerName: 'Valid From',
      width: '10%',
      valueGetter: (params) =>
        formatDate(params.value), // Format the date
    },
    {
      field: 'license_valid_till',
      headerName: 'Valid To',
      width: '10%',
      valueGetter: (params) => formatDate(params.value), // Format the date
    }, */
  ];

  const dataMapping = {
    ID: 'id',
    'Company Name': 'insurance_company_name',
    'Ins.Co.Branch': 'insurance_co_branch_name',
    'Insurance Type': 'insurance_type',
    'IMF Code': 'imf_code',
    'Branch Code': 'branch_code',
    'Status': 'status',
  };

  return (
    <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
      {/*  <Navbar /> */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>

            <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />

            <ModuleName moduleName="Insurance Branch" pageName="List" />
          </Box>
          <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
          {permissions.can_add && (
            <Button
              onClick={handleAdd}
              sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
            >
              New
            </Button>
          )}
            <ExportToPDF
              data={sortedBranches.map(branch => ({
                ...branch,
                status: branch.status === 1 ? 'Active' : 'Inactive'
              }))}
              headNames={['ID', 'Company Name', 'Ins.Co.Branch', 'Insurance Type', 'IMF Code','Branch Code', 'Status']}
              selectedRows={selectedRows}
              imageUrl="/logo.png"
              watermarkUrl="/gray-logo.png"
              fileName="insurance-branches.pdf"
              dataMapping={dataMapping}
              headerTitle="Insurance Branch Report"
            />
          </ButtonGroup>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, ml: 5 }}>
          <DropDown
            label=""
            value={selectedOption}
            onChange={(e) => setSelectedOption(e.target.value)}
            options={[
              { value: 'none', label: 'None' },
              { value: 'newLastWeek', label: 'New Last Week' },
              { value: 'newThisWeek', label: 'New this Week' },
              { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
              { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
              { value: 'editedLastWeek', label: 'Edited Last Week' },
              { value: 'editedThisWeek', label: 'Edited This Week' },
            ]}

          />

          <Box sx={{ display: 'flex', gap: 2 }}>

            <SearchBar placeholder="Search..." onSearch={handleSearch} />

            <IconActions
              onAllClick={handleAllClick}
              onActiveClick={handleActiveClick}
              onInactiveClick={handleInactiveClick}
              onRefreshClick={handleRefreshClick}
            />
          </Box>
        </Box>

        <CustomTable
          data={sortedBranches}
          //columns={columns}
          columns={columns.map(column => ({
            ...column,
            editable: column.field === 'agency_code' ? isAgencyCodeEditable : false // Make agency code editable
          }))} 
          selectedRows={selectedRows}
          onDelete={permissions.can_delete ? handleDelete : null}
          onEdit={permissions.can_edit ? handleEdit : null}
          onReinstate={handleReinstate}
          onSelectionChange={handleSelectionChange}
          onSelectAll={handleSelectAll}
          isViewEnabled={false}
        />
      </Box>

      <DeletePopup
        open={openDeletePopup}
        onClose={handleCloseDeletePopup}
        onConfirm={handleConfirmDelete}
        modulename={selectedItem ? selectedItem.insurance_co_branch_name : ''}
      />

      <SuccessPopup
        open={openSuccessPopup}
        onClose={handleCloseSuccessPopup}
        modulename={selectedItem ? selectedItem.insurance_co_branch_name : ''}
      />


    </Container>
  );
};

export default InsuranceBranchPage;
