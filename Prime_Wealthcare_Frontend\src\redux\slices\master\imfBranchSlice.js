import { createSlice } from '@reduxjs/toolkit';
import {
    fetchAllImfBranches, fetchImfBranchById, createImfBranch,
    updateImfBranch, softDeleteImfBranch, reinstateImfBranch, fetchIMFBranchesByCriteria,
    searchIMFBranches
} from '../../actions/action'

const imfBranchSlice = createSlice({
    name: 'imfBranch',
    initialState: {
        data: [],
        currentBranch: null,
        status: 'idle',
        error: null,
        loading: false
    },
    reducers: {
        clearImfBranch: (state) => {
            state.data = [];
        }
    },
    extraReducers: (builder) => {
        builder
            // Fetch all IMF branches
            .addCase(fetchAllImfBranches.pending, (state) => {
                state.status = 'loading';
                state.loading = true;
            })
            .addCase(fetchAllImfBranches.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.data = action.payload;
                state.loading = false;
            })
            .addCase(fetchAllImfBranches.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                state.loading = false;
            })

            // Fetch IMF branch by ID
            .addCase(fetchImfBranchById.pending, (state) => {
                state.status = 'loading';
                state.loading = true;
            })
            .addCase(fetchImfBranchById.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.currentBranch = action.payload;
                state.loading = false;
            })
            .addCase(fetchImfBranchById.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                state.loading = false;
            })

            // Create IMF branch
            .addCase(createImfBranch.fulfilled, (state, action) => {
                state.data.push(action.payload);
                state.loading = false;
            })
            .addCase(createImfBranch.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })

            // Update IMF branch
            .addCase(updateImfBranch.fulfilled, (state, action) => {
                const index = state.data.findIndex(branch => branch.id === action.payload.id);
                if (index !== -1) {
                    state.data[index] = action.payload;
                    state.loading = false;
                }
            })
            .addCase(updateImfBranch.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })

            // Soft delete IMF branch
            .addCase(softDeleteImfBranch.fulfilled, (state, action) => {
                const index = state.data.findIndex(branch => branch.id === action.payload.id);
                if (index !== -1) {
                    state.data.splice(index, 1);
                    state.loading = false;
                }
            })
            .addCase(softDeleteImfBranch.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })

            // Reinstate IMF branch
            .addCase(reinstateImfBranch.fulfilled, (state, action) => {
                const index = state.data.findIndex(branch => branch.id === action.payload.id);
                if (index !== -1) {
                    state.data[index] = action.payload;
                    state.loading = false;
                }
            })
            .addCase(reinstateImfBranch.rejected, (state, action) => {
                state.error = action.payload;
                state.loading = false;
            })

            .addCase(fetchIMFBranchesByCriteria.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchIMFBranchesByCriteria.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchIMFBranchesByCriteria.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })

            // Search IMF branches by name

            .addCase(searchIMFBranches.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(searchIMFBranches.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(searchIMFBranches.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
    }
});

export const { clearImfBranch } = imfBranchSlice.actions;
export default imfBranchSlice.reducer;    