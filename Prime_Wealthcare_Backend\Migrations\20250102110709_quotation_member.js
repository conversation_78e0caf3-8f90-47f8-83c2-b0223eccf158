exports.up = function (knex) {
    return knex.schema.hasTable('quotation_member').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('quotation_member', (table) => {
                table.increments('id').primary(); // Auto-increment primary key
                table.integer('quotation_id').unsigned().nullable();
                table.foreign('quotation_id').references('quotation_id').inTable('quotations');
                table.integer('member_id_no').notNullable(); // Member ID number
                table.integer('member_id').nullable();
                table.string('insuredName', 50).notNullable(); // Insured name
                table.date('insuredDob').notNullable(); // Insured DOB
                table.integer('sub_product_id').unsigned().nullable().references('id').inTable('sub_product');
                table.integer('sumInsured').notNullable(); // Sum insured
                table.integer('deductableDiscount').notNullable(); // Deductible discount
                table.string('relation').notNullable(); // Relation
                table.string('status', 50).notNullable(); // Status
                table.string('Created_by').notNullable(); // Created by
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now()); // Created timestamp
                table.integer('Updated_by').nullable(); // Updated by
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now()); // Updated timestamp
            });
        }
    })

};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('quotation_member');
};
