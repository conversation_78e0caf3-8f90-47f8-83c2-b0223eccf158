exports.up = function (knex) {
    return knex.schema
        .createTable('sub_product', function (table) {
            table.increments('id').primary();

            // Foreign keys
            table.integer('main_product_id').unsigned().notNullable();
            table.foreign('main_product_id').references('id').inTable('main_product').onDelete('CASCADE');
            table.integer('insurance_company_id').unsigned().notNullable();
            table.foreign('insurance_company_id').references('id').inTable('insurance_company').onDelete('CASCADE');
            table.integer('product_master_id').unsigned().notNullable();
            table.foreign('product_master_id').references('id').inTable('product_master').onDelete('CASCADE');

            // Other fields
            table.string('sub_product_name', 255).notNullable();
            table.integer('co_pay').unsigned();
            table.integer('child_separation_age').unsigned().notNullable();

            // Unique combination of foreign keys
            table.unique(['main_product_id', 'insurance_company_id', 'product_master_id', 'sub_product_name'], 'unique_product_combination');

            // Hospitalization days
            table.integer('pre_hospitalization_days').unsigned();
            table.integer('post_hospitalization_days').unsigned();

            // Timestamps and status
            table.integer('created_by').notNullable().defaultTo(1);
            table.integer('updated_by').notNullable().defaultTo(1);
            table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
            table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
            table.boolean('status').notNullable().defaultTo(true);

            // Constraints
            table.check('co_pay >= 0 AND co_pay <= 99');
            table.check('child_separation_age >= 18 AND child_separation_age <= 30');
            table.check('pre_hospitalization_days >= 0 AND pre_hospitalization_days <= 99');
            table.check('post_hospitalization_days >= 0 AND post_hospitalization_days <= 99');
        })
        .createTable('sub_product_age_sum', function (table) {
            table.increments('id').primary();

            // Foreign key to sub_product
            table.integer('sub_product_id').unsigned().notNullable();
            table.foreign('sub_product_id').references('id').inTable('sub_product').onDelete('CASCADE');

            // Age and Sum Insured Details
            table.integer('min_age').unsigned().notNullable();
            table.integer('max_age').unsigned().notNullable();
            table.integer('sum_insured').unsigned().notNullable();

            // Constraints
            table.check('min_age >= 0 AND min_age <= 100');
            table.check('max_age >= 0 AND max_age <= 100');
            table.check('max_age >= min_age');
            table.check('sum_insured >= 50000 AND sum_insured <= 10000000 AND sum_insured % 50000 = 0');
        })
        .createTable('sub_product_rider', function (table) {
            table.increments('id').primary();

            // Foreign key to sub_product
            table.integer('sub_product_id').unsigned().notNullable();
            table.foreign('sub_product_id').references('id').inTable('sub_product').onDelete('CASCADE');

            // Sum Insured Rider Details
            table.string('rider_details', 512).notNullable();
        });
};

exports.down = function (knex) {
    return knex.schema
        .dropTable('sub_product_rider')
        .dropTable('sub_product_age_sum')
        .dropTable('sub_product');
};
