import React, { useRef, useState, useEffect } from 'react';
import { 
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title, 
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { Box, Paper, Typography, Button, Grid, Divider, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';

// Register Chart.js components needed for bar charts only
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const ReportCharts = ({ reportData, page = 0, rowsPerPage = 10, agents = [] }) => {
  const chartRef = useRef(null);
  const chartInstanceRef = useRef(null);
  const [exporting, setExporting] = useState(false);
  const [primaryGroupField, setPrimaryGroupField] = useState('');
  const [stackGroupField, setStackGroupField] = useState('');
  const [availableFields, setAvailableFields] = useState([]);
  const [selectedFields, setSelectedFields] = useState([]);
  
  // Determine available fields and set defaults based on Group By selections
  useEffect(() => {
    if (reportData?.columns) {
      // Get non-metric fields that can be used for grouping
      const fields = reportData.columns
        .filter(col => !['total_count', 'total_net_premium', 'total_gst', 'total_gross_premium'].includes(col.field))
        .map(col => ({ field: col.field, label: col.headerName }));
      
      setAvailableFields(fields);
      
      // Determine which fields were selected in the Group By section
      // This info should be passed from the Reports component
      if (reportData.groupByFields && Array.isArray(reportData.groupByFields) && reportData.groupByFields.length > 0) {
        setSelectedFields(reportData.groupByFields);
        
        // Set primary group (X axis) to the first selected field
        if (!primaryGroupField && reportData.groupByFields.length > 0) {
          setPrimaryGroupField(reportData.groupByFields[0]);
        }
        
        // Set stack group to the second selected field if available
        if (!stackGroupField && reportData.groupByFields.length > 1) {
          setStackGroupField(reportData.groupByFields[1]);
        }
      } else {
        // Fallback if no group by fields were explicitly passed
        // Just use the first available field
        if (fields.length > 0 && !primaryGroupField) {
          setPrimaryGroupField(fields[0].field);
        }
      }
    }
  }, [reportData]);

  // Function to export chart as image
  const handleExportToImage = () => {
    try {
      setExporting(true);
      
      // Get chart instance from ref
      const chartInstance = chartInstanceRef.current;
      
      if (!chartInstance) {
        throw new Error('Chart instance not available');
      }
      
      // Get the canvas from chart instance
      const originalCanvas = chartInstance.canvas;
      
      if (!originalCanvas) {
        throw new Error('Canvas element not found');
      }
      
      // Create a new canvas with white background
      const exportCanvas = document.createElement('canvas');
      exportCanvas.width = originalCanvas.width;
      exportCanvas.height = originalCanvas.height;
      
      // Get the context of the new canvas
      const ctx = exportCanvas.getContext('2d');
      
      // Fill with white background
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);
      
      // Draw the chart on the white background
      ctx.drawImage(originalCanvas, 0, 0);
      
      // Create link for download
      const link = document.createElement('a');
      link.download = `PrimeWealthcare_GrossPremium_${new Date().toISOString().split('T')[0]}.png`;
      
      // Convert canvas to data URL
      link.href = exportCanvas.toDataURL('image/png', 1.0);
      link.click();
      
    } catch (error) {
      console.error('Error exporting chart to image:', error);
      alert('Failed to export chart as image. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  // Map IDs to display values (agents, branches, etc.)
  const enrichData = (rows) => {
    if (!rows || rows.length === 0) return rows;
    
    return rows.map(row => {
      const newRow = { ...row };
      
      // Map agent IDs to their display names
      if (newRow.agent_id !== undefined && agents && agents.length > 0) {
        const agent = agents.find(a => a.id === parseInt(newRow.agent_id));
        if (agent) {
          newRow._display_agent_id = `${agent.agent_id} - ${agent.full_name}`;
          if (!newRow.agent_name && agent.full_name) {
            newRow.agent_name = agent.full_name;
          }
        }
      }
      
      return newRow;
    });
  };

  // Get the paginated and enriched rows
  const enrichedRows = reportData?.rows 
    ? enrichData(reportData.rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)) 
    : [];

  // Helper to get field display value
  const getFieldDisplayValue = (field, row) => {
    // For agent field, use the display name
    if (field === 'agent_id' && row._display_agent_id) {
      return row._display_agent_id;
    }
    
    // For insurance company, use short name if available
    if (field === 'insurance_company_name' && row.insurance_company_short_name) {
      return `${row.insurance_company_short_name} `;
    }
    
    // Return the field value or "Unknown" if not present
    return row[field] || 'Unknown';
  };

  // Get field display label
  const getFieldDisplayLabel = (fieldName) => {
    const field = availableFields.find(f => f.field === fieldName);
    return field ? field.label : fieldName;
  };

  // Prepare data for chart with stacking based on selected fields
  const prepareChartData = () => {
    if (!enrichedRows || enrichedRows.length === 0 || !primaryGroupField) {
      return { labels: [], datasets: [] };
    }
    
    // Create labels with short names for insurance companies
    const labels = enrichedRows.map(row => {
      // If displaying insurance company and short name is available
      if (primaryGroupField === 'insurance_company_name' && row.insurance_company_short_name) {
        return `${row.insurance_company_short_name} - ${row.insurance_company_name}`;
      }
      
      // For agent and other fields, use existing logic
      if (primaryGroupField === 'agent_id' && row._display_agent_id) {
        return row._display_agent_id;
      }
      
      return row[primaryGroupField] || 'Unknown';
    });
    
    // If we have a stacking field, group data by both primary and stack fields
    if (stackGroupField) {
      // First group by primary field
      const groupedByPrimary = {};
      
      // Collect all unique stack field values
      const stackValues = new Set();
      
      // Group the data
      enrichedRows.forEach(row => {
        const primaryValue = getFieldDisplayValue(primaryGroupField, row);
        const stackValue = getFieldDisplayValue(stackGroupField, row);
        
        // Add to unique stack values
        stackValues.add(stackValue);
        
        if (!groupedByPrimary[primaryValue]) {
          groupedByPrimary[primaryValue] = {};
        }
        
        if (!groupedByPrimary[primaryValue][stackValue]) {
          groupedByPrimary[primaryValue][stackValue] = {
            grossPremium: 0,
            count: 0
          };
        }
        
        groupedByPrimary[primaryValue][stackValue].grossPremium += parseFloat(row.total_gross_premium || 0);
        groupedByPrimary[primaryValue][stackValue].count += parseInt(row.total_count || 0);
      });
      
      // Convert to chart format
      const primaryLabels = Object.keys(groupedByPrimary);
      const stackLabelsArray = Array.from(stackValues);
      
      // Create datasets - one for each stack value
      const datasets = stackLabelsArray.map((stackValue, index) => {
        const colors = [
          { bg: 'rgba(54, 162, 235, 0.6)', border: 'rgba(54, 162, 235, 1)' },
          { bg: 'rgba(255, 206, 86, 0.6)', border: 'rgba(255, 206, 86, 1)' },
          { bg: 'rgba(75, 192, 192, 0.6)', border: 'rgba(75, 192, 192, 1)' },
          { bg: 'rgba(153, 102, 255, 0.6)', border: 'rgba(153, 102, 255, 1)' },
          { bg: 'rgba(255, 99, 132, 0.6)', border: 'rgba(255, 99, 132, 1)' },
          { bg: 'rgba(255, 159, 64, 0.6)', border: 'rgba(255, 159, 64, 1)' },
        ];
        
        const colorIndex = index % colors.length;
        
        return {
          label: `${stackValue}`,
          data: primaryLabels.map(primaryLabel => {
            if (groupedByPrimary[primaryLabel][stackValue]) {
              return groupedByPrimary[primaryLabel][stackValue].grossPremium;
            }
            return 0;
          }),
          backgroundColor: colors[colorIndex].bg,
          borderColor: colors[colorIndex].border,
          borderWidth: 1,
          stack: 'stack1'
        };
      });
      
      // Add counts and totals for displaying above bars
      const totalsByPrimary = primaryLabels.map(primaryLabel => {
        let total = 0;
        let count = 0;
        
        stackLabelsArray.forEach(stackValue => {
          if (groupedByPrimary[primaryLabel][stackValue]) {
            total += groupedByPrimary[primaryLabel][stackValue].grossPremium;
            count += groupedByPrimary[primaryLabel][stackValue].count;
          }
        });
        
        return { total, count };
      });
      
      return {
        labels: primaryLabels,
        datasets,
        counts: totalsByPrimary.map(t => t.count),
        totals: totalsByPrimary.map(t => t.total)
      };
      
    } else {
      // Simple grouping by primary field only
      const groupedData = {};
      
      enrichedRows.forEach(row => {
        const primaryValue = getFieldDisplayValue(primaryGroupField, row);
        
        if (!groupedData[primaryValue]) {
          groupedData[primaryValue] = {
            grossPremium: 0,
            count: 0
          };
        }
        
        groupedData[primaryValue].grossPremium += parseFloat(row.total_gross_premium || 0);
        groupedData[primaryValue].count += parseInt(row.total_count || 0);
      });
      
      // Convert to chart format
      const labels = Object.keys(groupedData);
      
      const datasets = [{
        label: 'Gross Premium',
        data: labels.map(label => groupedData[label].grossPremium),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      }];
      
      return {
        labels,
        datasets,
        counts: labels.map(label => groupedData[label].count),
        totals: labels.map(label => groupedData[label].grossPremium)
      };
    }
  };

  // Create chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'x',
    scales: {
      x: {
        stacked: true,
        ticks: {
          maxRotation: 90,
          minRotation: 45,
          autoSkip: false
        },
        grid: {
          display: false
        },
        title: {
          display: true,
          text: getFieldDisplayLabel(primaryGroupField),
          font: { weight: 'bold' }
        }
      },
      y: {
        stacked: true,
        beginAtZero: true,
        title: {
          display: true,
          text: 'Gross Premium Amount (₹)',
          font: { weight: 'bold' }
        },
        ticks: {
          callback: function(value) {
            return '₹' + value.toLocaleString('en-IN');
          }
        }
      }
    },
    plugins: {
      title: {
        display: true,
        text: stackGroupField ? 
          `Gross Premium by ${getFieldDisplayLabel(primaryGroupField)} and ${getFieldDisplayLabel(stackGroupField)}` : 
          `Gross Premium by ${getFieldDisplayLabel(primaryGroupField)}`,
        font: { size: 16, weight: 'bold' }
      },
      legend: {
        position: 'top',
        title: {
          display: stackGroupField ? true : false,
          text: stackGroupField ? getFieldDisplayLabel(stackGroupField) : '',
          font: { weight: 'bold' }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            const value = context.raw || 0;
            return `${label}: ₹${value.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
          },
          afterBody: function(context) {
            const index = context[0].dataIndex;
            const chartData = prepareChartData();
            return [
              `Total Premium: ₹${chartData.totals[index].toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
              `Policy Count: ${chartData.counts[index]}`
            ];
          }
        }
      }
    }
  };

  // Plugin to display policy counts and totals above bars
  const counterPlugin = {
    id: 'counterPlugin',
    afterDatasetsDraw(chart) {
      const { ctx, data, chartArea, scales } = chart;
      
      // Get the chart data with counts
      const chartData = prepareChartData();
      
      ctx.save();
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'center';
      
      // Draw totals above each bar group
      chartData.labels.forEach((label, index) => {
        const xPos = scales.x.getPixelForValue(index);
        const yPos = chartArea.top - 15;
        
        // Gross premium total
        ctx.fillStyle = '#4bc0c0';
        ctx.fillText(
          `₹${chartData.totals[index].toLocaleString('en-IN', { maximumFractionDigits: 0 })}`, 
          xPos, 
          yPos
        );
        
        // Policy count
        ctx.fillStyle = '#9966ff';
        ctx.fillText(
          `${chartData.counts[index]} policies`, 
          xPos, 
          yPos - 15
        );
      });
      
      ctx.restore();
    }
  };

  // If no data, show a placeholder
  if (!reportData || !reportData.rows || reportData.rows.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1" color="textSecondary">
          No data available for visualization
        </Typography>
      </Paper>
    );
  }

  // Chart data
  const chartData = prepareChartData();

  // Determine if we have more than 2 grouping fields to show dropdown options
  const hasMultipleGroupByOptions = selectedFields.length > 2;

  return (
    <Paper sx={{ p: 3, mb: 3 }} ref={chartRef}>
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, justifyContent: 'space-between', mb: 2, gap: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Gross Premium Distribution
        </Typography>
        
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
          {/* X-Axis field selection dropdown */}
          {hasMultipleGroupByOptions && (
            <FormControl size="small" sx={{ minWidth: 180 }}>
              <InputLabel>X-Axis</InputLabel>
              <Select
                value={primaryGroupField}
                label="X-Axis"
                onChange={(e) => setPrimaryGroupField(e.target.value)}
              >
                {selectedFields.map(fieldName => {
                  const field = availableFields.find(f => f.field === fieldName);
                  return (
                    <MenuItem key={fieldName} value={fieldName}>
                      {field ? field.label : fieldName}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          )}
          
          {/* Stacking field selection dropdown - only if we have more than 1 group by field */}
          {selectedFields.length > 1 && (
            <FormControl size="small" sx={{ minWidth: 180 }}>
              <InputLabel>Stack By</InputLabel>
              <Select
                value={stackGroupField}
                label="Stack By"
                onChange={(e) => setStackGroupField(e.target.value)}
              >
                <MenuItem value="">None</MenuItem>
                {selectedFields
                  .filter(field => field !== primaryGroupField)
                  .map(fieldName => {
                    const field = availableFields.find(f => f.field === fieldName);
                    return (
                      <MenuItem key={fieldName} value={fieldName}>
                        {field ? field.label : fieldName}
                      </MenuItem>
                    );
                  })
                }
              </Select>
            </FormControl>
          )}
          
          <Button 
            variant="outlined" 
            startIcon={<ImageIcon />} 
            onClick={handleExportToImage}
            className="export-button"
            disabled={exporting}
          >
            {exporting ? 'Exporting...' : 'Export as Image'}
          </Button>
        </Box>
      </Box>
      <Divider sx={{ mb: 3 }} />
      
      <Box sx={{ height: 450, mb: 3, overflowX: chartData.labels.length > 8 ? 'auto' : 'hidden' }}>
        <Bar 
          data={chartData} 
          options={chartOptions} 
          plugins={[counterPlugin]}
          ref={(reference) => {
            if (reference) {
              chartInstanceRef.current = reference;
            }
          }}
        />
      </Box>
      
      <Grid container spacing={2} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Paper elevation={1} sx={{ p: 2, bgcolor: '#f9f9f9' }}>
            <Typography variant="subtitle2" color="textSecondary">
              Total Policies:
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#9966ff' }}>
              {reportData.summary.total_policies.toLocaleString('en-IN')}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Paper elevation={1} sx={{ p: 2, bgcolor: '#f9f9f9' }}>
            <Typography variant="subtitle2" color="textSecondary">
              Total Net Premium:
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#36a2eb' }}>
              ₹{reportData.summary.total_net_premium.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Paper elevation={1} sx={{ p: 2, bgcolor: '#f9f9f9' }}>
            <Typography variant="subtitle2" color="textSecondary">
              Total Gross Premium:
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#4bc0c0' }}>
              ₹{reportData.summary.total_gross_premium.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ReportCharts;