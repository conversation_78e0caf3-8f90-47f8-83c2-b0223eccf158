const express = require('express');
const router = express.Router();
const QuotationController = require('../Controllers/quotations_controller'); // Adjust path based on your structure
const { createQuotationWithMembers } = require('../Models/quotations_model');
const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);


// Define routes for quotations
router.post('/quotations-with-members', createQuotationWithMembers);

router.get('/quotationResponse', QuotationController.getQuotationResponse)
router.get('/quotationResponse/:id', QuotationController.getQuotationResponseById)

// Get all quotations
router.get('/', QuotationController.getAllQuotations);

//Get all quotations based on user_id
router.get('/user/:userId', QuotationController.getAllQuotationsByUserId);

// Get a single quotation by ID
router.get('/:id', QuotationController.getQuotationById);

// Get a single quotation by quotation number
router.get('/quotation-number/:quotationNumber', QuotationController.getQuotationByQuotationNumber);

// Create a new quotation
router.post('/', QuotationController.createQuotation);

// Update a quotation
router.put('/:quotation-number', QuotationController.updateQuotation);

// Add new route for getting logs by customer name
router.get('/logs/customer/:customerName', async (req, res) => {
    try {
        const { customerName } = req.params;
        
        // Remove spaces and convert to uppercase for consistent searching
        const searchName = customerName.replace(/\s+/g, '').toUpperCase();

        // Get all quotation logs
        const logs = await db('quotations_logs')
            .whereRaw('REPLACE(UPPER(customer_name), " ", "") LIKE ?', [`%${searchName}%`])
            .select(
                'id',
                'quotation_number',
                'request_body',
                'response_body',
                'status',
                'error_message',
                'created_by',
                'customer_name',
                'insurance_company',
                'product_name',
                'created_at'
            )
            .orderBy('created_at', 'desc');

        if (!logs.length) {
            return res.status(404).json({
                success: false,
                message: `No quotation logs found for customer: ${customerName}`
            });
        }

        // Get unique quotation numbers for additional details
        const quotationNumbers = [...new Set(logs.map(log => log.quotation_number))];
        
        // Get quotation details
        const quotationDetails = await db('quotations')
            .whereIn('quotation_number', quotationNumbers)
            .select(
                'quotation_number',
                'status as quotation_status',
                'product',
                'insurance_company',
                'main_product',
                'policyType',
                'customer_id',
                'agent_id',
                'Created_by',
                'created_at as quotation_created_at'
            );

        // Group logs by status
        const logsByStatus = logs.reduce((acc, log) => {
            acc[log.status] = acc[log.status] || [];
            acc[log.status].push(log);
            return acc;
        }, {});

        res.json({
            success: true,
            data: {
                logs: logs.map(log => ({
                    ...log,
                    quotation_details: quotationDetails.find(q => 
                        q.quotation_number === log.quotation_number
                    ) || null
                })),
                summary: {
                    total_logs: logs.length,
                    status_breakdown: Object.keys(logsByStatus).map(status => ({
                        status,
                        count: logsByStatus[status].length
                    })),
                    unique_quotations: quotationNumbers.length
                }
            }
        });

    } catch (error) {
        console.error('Error fetching quotation logs:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching quotation logs',
            error: error.message
        });
    }
});

module.exports = router;
