import { createSlice } from '@reduxjs/toolkit';
import {
    getAllAgentAddresses,
    getAgentAddressById,
    createAgentAddress,
    updateAgentAddress,
    deleteAgentAddress,
    getAllAgentDetails,
    getAgentById,
    createAgentDetails,
    updateAgentDetails,
    deleteAgentDetails,
    reinstateAgentDetails,
    getAgentsByCriteria,
    getAgentsBySearch,
    getAgentByIdForEdit,
    getAgentByBranchName,
} from '../../actions/action'; // Adjust the path if necessary
import { toast } from 'react-toastify';

const agentMasterSlice = createSlice({
    name: 'agents',
    initialState: {
        agents: [],
        agent: null,
        addresses: [], // New state for addresses
        currentAddress: null, // New state for current address
        loading: false,
        error: null,
    },
    extraReducers: (builder) => {
        builder
            // Get All Agent Addresses
            .addCase(getAllAgentAddresses.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllAgentAddresses.fulfilled, (state, action) => {
                state.loading = false;
                state.addresses = action.payload;
            })
            .addCase(getAllAgentAddresses.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent addresses');
            })

            // Get Agent By Branch Name
            .addCase(getAgentByBranchName.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentByBranchName.fulfilled, (state, action) => {
                state.loading = false;
                state.agents = action.payload;
            })
            .addCase(getAgentByBranchName.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agents');
            })

            // Get Agent Address By ID
            .addCase(getAgentAddressById.pending, (state) => {
                state.loading = true;
                state.currentAddress = null;
            })
            .addCase(getAgentAddressById.fulfilled, (state, action) => {
                state.loading = false;
                if (action.payload === null || action.payload === undefined || action.payload === '') {
                    state.currentAddress = null;
                } else {
                    state.currentAddress = action.payload;
                }
            })
            .addCase(getAgentAddressById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent address');
            })

            // Create Agent Address
            .addCase(createAgentAddress.pending, (state) => {
                state.loading = true;
            })
            .addCase(createAgentAddress.fulfilled, (state, action) => {
                state.loading = false;
                state.addresses.push(action.payload);
                toast.success('Agent address created successfully');
            })
            .addCase(createAgentAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to create agent address');
            })

            // Update Agent Address
            .addCase(updateAgentAddress.pending, (state) => {
                state.loading = true;
            })
            .addCase(updateAgentAddress.fulfilled, (state, action) => {
                const index = state.addresses.findIndex(address => address.id === action.payload.id);
                if (index !== -1) {
                    state.addresses[index] = action.payload;
                }
                state.loading = false;
                toast.success('Agent address updated successfully');
            })
            .addCase(updateAgentAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to update agent address');
            })

            // Delete Agent Address
            .addCase(deleteAgentAddress.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteAgentAddress.fulfilled, (state, action) => {
                state.addresses = state.addresses.filter(address => address.id !== action.payload);
                state.loading = false;
                toast.success('Agent address deleted successfully');
            })
            .addCase(deleteAgentAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to delete agent address');
            })

            // Get All Agent Details
            .addCase(getAllAgentDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllAgentDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.agents = action.payload;
            })
            .addCase(getAllAgentDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent details');
            })

            // Get Agent By ID
            .addCase(getAgentById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentById.fulfilled, (state, action) => {
                state.loading = false;
                state.agent = action.payload;
            })
            .addCase(getAgentById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent details');
            })

            // Get Agent By ID For Edit
            .addCase(getAgentByIdForEdit.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentByIdForEdit.fulfilled, (state, action) => {
                state.loading = false;
                state.agent = action.payload;
            })
            .addCase(getAgentByIdForEdit.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agent details');
            })

            // Get Agents By Search
            .addCase(getAgentsBySearch.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentsBySearch.fulfilled, (state, action) => {
                state.loading = false;
                state.agents = action.payload;
            })
            .addCase(getAgentsBySearch.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch agents');
            })

            // Create Agent Details
            .addCase(createAgentDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(createAgentDetails.fulfilled, (state, action) => {
                state.loading = false;
                state.agents.push(action.payload);
                toast.success('Agent details created successfully');
            })
            .addCase(createAgentDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to create agent details');
            })

            // Update Agent Details
            .addCase(updateAgentDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(updateAgentDetails.fulfilled, (state, action) => {
                const index = state.agents.findIndex(agent => agent.id === action.payload.id);
                if (index !== -1) {
                    state.agents[index] = action.payload;
                }
                state.loading = false;
                toast.success('Agent details updated successfully');
            })
            .addCase(updateAgentDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to update agent details');
            })

            // Delete Agent Details
            .addCase(deleteAgentDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(deleteAgentDetails.fulfilled, (state, action) => {
                state.agents = state.agents.filter(agent => agent.id !== action.payload);
                state.loading = false;
                toast.success('Agent details deleted successfully');
            })
            .addCase(deleteAgentDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to delete agent details');
            })

            // Reinstate Agent Details
            .addCase(reinstateAgentDetails.pending, (state) => {
                state.loading = true;
            })
            .addCase(reinstateAgentDetails.fulfilled, (state, action) => {
                const index = state.agents.findIndex(agent => agent.id === action.payload.id);
                if (index !== -1) {
                    state.agents[index] = action.payload;
                }
                state.loading = false;
                toast.success('Agent details reinstated successfully');
            })
            .addCase(reinstateAgentDetails.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to reinstate agent details');
            })

            // Filter Agents by Criteria
            .addCase(getAgentsByCriteria.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAgentsByCriteria.fulfilled, (state, action) => {
                state.loading = false;
                state.agents = action.payload;
            })
            .addCase(getAgentsByCriteria.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to filter agents');
            });
    },
});

export default agentMasterSlice.reducer;
