exports.up = function (knex) {
    return knex.schema.hasTable('customer_address').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('customer_address', function (table) {
                table.increments('id').primary();  // INT AUTO_INCREMENT PRIMARY KEY

                // Foreign Key to the Agents table
                table.integer('customer_id').unsigned().notNullable();

                table.foreign('customer_id').references('id').inTable('customer_personal_info');

                // Current Address Fields
                table.string('current_apartment_no', 50).notNullable();  // VARCHAR(50)
                table.string('current_apartment_name', 100).notNullable();  // VARCHAR(100)
                table.string('current_address_line1', 255).notNullable();  // VARCHAR(255)
                table.string('current_address_line2', 255).nullable();  // VARCHAR(255) - Nullable
                table.integer('current_pincode', 255).notNullable();
                table.string('current_city', 255).notNullable();
                table.string('current_pincode_city').references('pincode_city').inTable('locations').onDelete('CASCADE');
                table.string('current_state', 255).notNullable();

                table.integer('current_area').unsigned().nullable()
                    .references('id').inTable('areas') // Foreign key to `areas`
                    .onDelete('CASCADE');

                // Permanent Address Fields
                table.string('permanent_apartment_no', 50).notNullable();  // VARCHAR(50)
                table.string('permanent_apartment_name', 100).notNullable();  // VARCHAR(100)
                table.string('permanent_address_line1', 255).notNullable();  // VARCHAR(255)
                table.string('permanent_address_line2', 255).nullable();  // VARCHAR(255) - Nullable
                table.integer('permanent_pincode', 255).notNullable();
                table.string('permanent_city', 255).notNullable();
                table.string('permanent_pincode_city').references('pincode_city').inTable('locations').onDelete('CASCADE');
                table.string('permanent_state', 255).notNullable();

                table.integer('permanent_area').unsigned().nullable()
                    .references('id').inTable('areas') // Foreign key to `areas`
                    .onDelete('CASCADE');

                table.string('used_address', 100).notNullable();  // VARCHAR(100)
                table.string('use_current_as_permanent', 50).nullable();

                // Common Fields
                table.boolean('status').notNullable().defaultTo(true);  // Status Field
                table.timestamp('created_at').defaultTo(knex.fn.now());
                table.timestamp('updated_at').defaultTo(knex.fn.now());
            });
        };
    });
};
exports.down = function (knex) {
    return knex.schema.dropTable('customer_address');
};
