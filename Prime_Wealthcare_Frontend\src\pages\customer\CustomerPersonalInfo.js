import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { CircularProgress, FormControl, FormControlLabel, FormHelperText, Radio, RadioGroup, Typography } from '@mui/material';
import CustomTextField from '../../components/CustomTextField';
import CustomCheckbox from '../../components/CheckboxWithLabel';
import CustomSection from '../../components/CustomSection';
import Dropdown from '../../components/table/DropDown';
import ModuleName from '../../components/table/ModuleName';
import { createCustomerInfo, getAllAgentDetails, getCustomerById, updateCustomer, getAllCustomer, createCustomerGrouping, getAllGroups, updateGrouping, getAllPickLists } from '../../redux/actions/action'
import EditIcon from '@mui/icons-material/Edit';
import IconButton from '@mui/material/IconButton';
import { toast } from 'react-toastify';
import { Autocomplete, TextField } from '@mui/material';
import CustomFileUpload from '../../components/CustomFileUpload';

const CustomerPersonalInfo = () => {

    const { user } = useSelector((state) => state.auth);
    const userId = user?.userId; // Access the user_id field from the user object
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { id } = useParams();
    const [isEditing, setIsEditing] = useState(false); // State to manage edit mode
    const [groupCode, setGroupCode] = useState(''); // Initialize with a default value
    // Add state for form fields
    const [loading, setLoading] = useState(false); // Add loading state
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [formData, setFormData] = useState({
        customer_category: '',
        company_name: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        occupation: '',
        height: '',
        weight: '',
        isSmoking: '',
        isTobacco: '',
        preExistingDisease: '',
        mobile: '',
        whatsApp_number: '',
        email: '',
        pan_number: '',
        aadhar_number: '',
        head_name: '',
        group_code: '',
        comment_history: '',
        assigned_to: userId,
        agent_id: null,
        created_at: new Date().toISOString(),
        smokingPerDay: '',
        tobaccoPerDay: '',
        diseaseDetails: '',
        discarge_summaryFile: null,
    });
    const handleEditToggle = () => {
        setIsEditing(prev => !prev); // Toggle edit mode
    };
    // Add state for checkbox
    const [createNew, setCreateNew] = useState(false);
    useEffect(() => {
        if (!id) {
            setCreateNew(false); // Ensure checkbox is unchecked when on a new page
        }
    }, [id]); // Whenever the page loads (based on customer ID)
    const allCustomers = useSelector((state) => state.customerReducer.customer); // Assuming this is the array of customers

    const customer = useSelector(state => state.customerReducer.customerDetails);
    const agents = useSelector(state => state.agentReducer.agents);
    const existingGroups = useSelector(state => state.customerGroupingReducer.groups); // Assuming you have a reducer for groups
    const occupationData = useSelector((state) => state.pickListReducer.OccupationOptions);

    useEffect(() => {
        if (id) {
            dispatch(getCustomerById(id));
            setIsEditing(false); // Set edit mode to true if id exists
        } else {
            setIsEditing(true); // Set edit mode to true for new customer
        }
    }, [dispatch, id]);
    useEffect(() => {
        if (customer && id) {
            setCustomerType(customer.customer_category || 'individual'); // Set customerType based on fetched customer data
            setCreateNew(customer.head_name ? true : false);
        }
    }, [customer]);
    useEffect(() => {
        dispatch(getAllAgentDetails());
        dispatch(getAllCustomer());
        dispatch(getAllGroups());
        dispatch(getAllPickLists());
    }, [dispatch]);


    // const agentOptions = agents.map((agent) => ({
    //     value: agent.id, // Use agent_id as the value
    //     label: agent.agent_id
    // }))
    const agentOptions = agents
        .filter(agent => agent.status === 1)
        .map(agent => ({
            value: agent.id,
            label: `${agent.agent_id} (${agent.full_name})` // Format: "RM-0001 (John Doe)"
        }));

    const occupationOptions = occupationData.map((occupation) => ({
        value: occupation.id,
        label: occupation.label_name
    }))
    // Add useEffect to set form data from customer data
    useEffect(() => {
        if (id) {
            setFormData(prevState => ({
                ...prevState,
                customer_category: customer?.customer_category || '',
                company_name: customer?.company_name || '',
                first_name: customer?.first_name || '',
                last_name: customer?.last_name || '',
                middle_name: customer?.middle_name || '',
                mobile: customer?.mobile || '',
                whatsApp_number: customer?.whatsApp_number || '',
                email: customer?.email || '',
                pan_number: customer?.pan_number || '',
                aadhar_number: customer?.aadhar_number || '',
                //head_name: `${customer?.first_name || ''} ${customer?.last_name || ''}`.trim(), // Set head_name from first and last name
                head_name: customer?.head_name ? (customer?.customer_category === 'corporate' ? customer?.company_name : `${customer?.first_name || ''} ${customer?.last_name || ''}`.trim()) : '', // Set head_name based on customer category only if head_name is true
                // group_code: customer?.head_name ? (existingGroups.length > 0 ? existingGroups[0].group_code : '') : '', // Set default group_code from existing groups only if head_name is true comment_history: customer?.comment_history || '',
                group_code: customer?.group_code || '',
                assigned_to: userId,
                agent_id: customer?.agent_id,
                created_at: customer?.created_at || new Date().toISOString(),
                weight: customer?.weight || '',
                height: customer?.height || '',
                occupation: customer?.occupation || '',
                // occupation: customer?.occupation ? occupationOptions.find(option => option.value === customer?.occupation)
                //     : '', // Set occupation from customer data using occupationOptions
                isSmoking: customer?.isSmoking || '',
                isTobacco: customer?.isTobacco || '',
                preExistingDisease: customer?.preExistingDisease || '',
                smokingPerDay: customer?.smokingPerDay || '',
                tobaccoPerDay: customer?.tobaccoPerDay || '',
                diseaseDetails: customer?.diseaseDetails || '',
                discarge_summaryFile: customer?.discarge_summaryFile || null,
            }));
        }
    }, [customer, existingGroups]);

    //const customers = useSelector(state => state.customerReducer.customer); // Assuming you have a customers state


    // useEffect(() => {
    //     dispatch(getAllCustomer()); // Fetch all customers when the component mounts
    // }, [dispatch]);

    const formatDate = (date) => {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    };
    const formatAadharNumber = (input) => {
        // Remove non-numeric characters
        const numericValue = input.replace(/[^0-9]/g, "");
        // Limit to 12 digits
        const limitedValue = numericValue.slice(0, 12);
        // Add hyphens after every 4 digits
        return limitedValue.replace(/(\d{4})(?=\d)/g, "$1-");
    };

    // Handle customer type change
    const handleCustomerTypeChange = (event) => {
        const selectedType = event.target.value;
        setCustomerType(selectedType);

        // Update group code based on selected customer type
        let newGroupCode;
        if (selectedType === 'individual') {
            newGroupCode = `${formData.first_name.slice(0, 4).toUpperCase()}${formData.mobile.slice(-4)}`; // First 4 letters of first name + last 4 digits of mobile
        } else if (selectedType === 'corporate') {
            newGroupCode = `${formData.company_name.slice(0, 4).toUpperCase()}${formData.pan_number.slice(-5, -1)}`; // First 4 letters of company name + last 4 digits of PAN
        }
        setGroupCode(newGroupCode); // Update group code

        // Update head name based on selected customer type
        if (createNew) {
            const headName = selectedType === 'corporate' ? formData.company_name : `${formData.first_name} ${formData.last_name}`.trim();
            setFormData(prev => ({
                ...prev,
                head_name: headName, // Set head_name to the full name
                group_code: newGroupCode
            }));
        }
    };
    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value: originalValue } = e.target; // Rename value to originalValue
        let value = originalValue; // Create a new variable for modified value

        // Add validation for smoking and tobacco consumption to allow only alphanumeric characters
        if (name === 'smokingPerDay' || name === 'tobaccoPerDay') {
            // Allow only letters and numbers, remove any other characters
            const alphanumericValue = value.replace(/[^a-zA-Z0-9\s]/g, '');
            if (alphanumericValue !== value) {
                return; // Prevent input of symbols
            }
        }

        // Add height validation to only allow integers
        if (name === 'height') {
            // Remove any non-numeric characters and decimal points
            const numericValue = value.replace(/[^0-9]/g, '');
            if (numericValue !== value) {
                return; // Prevent non-integer input
            }
        }
        if (name === 'weight') {
            // Remove any non-numeric characters and decimal points
            const numericValue = value.replace(/[^0-9]/g, '');
            if (numericValue !== value) {
                return; // Prevent non-integer input
            }
        }
        // Convert first name and last name to uppercase
        if (name === 'first_name' || name === 'last_name' || name === 'middle_name') {
            value = value.toUpperCase(); // Convert to uppercase
        }

        // Allow only numbers for mobile input and restrict to 10 digits
        if (name === 'mobile') {
            if (!/^[0-9]*$/.test(value) || value.length > 10) {
                return; // Prevent non-numeric input or exceeding length
            }
        }
        if (name === 'whatsApp_number') {
            if (!/^[0-9]*$/.test(value) || value.length > 10) {
                return; // Prevent non-numeric input or exceeding length
            }
        }
        // Convert email to lowercase
        if (name === 'email') {
            value = value.toLowerCase().trim(); // Convert to lowercase
        }
        if (name === 'pan_number') {
            value = value.toUpperCase().trim();
            if (value.length > 10) {
                return;
            }
        }
        // Format Aadhaar number
        if (name === 'aadhar_number') {
            value = formatAadharNumber(value); // Format the Aadhaar number
        }

        // Clear errors when user types
        if (name === 'smokingPerDay' && value) {
            setErrors(prevErrors => ({ ...prevErrors, smokingPerDay: undefined }));
        }
        if (name === 'tobaccoPerDay' && value) {
            setErrors(prevErrors => ({ ...prevErrors, tobaccoPerDay: undefined }));
        }
        if (name === 'diseaseDetails' && value) {
            setErrors(prevErrors => ({ ...prevErrors, diseaseDetails: undefined }));
        }


        // Add validation for diseaseDetails
        if (name === 'diseaseDetails') {
            // Allow letters, numbers, spaces, parentheses, hyphens, dots, and common separators
            const validValue = value.replace(/[^a-zA-Z0-9\s,.\-()]/g, '');

            // Define a regular expression for valid disease detail formats
            const isValidInput = /^(\d+\s*-\s*[a-zA-Z0-9\s,.\-()]+|[a-zA-Z0-9\s,.\-()]+)$/;

            if (value && !isValidInput.test(validValue)) {
                setErrors(prevErrors => ({
                    ...prevErrors,
                    diseaseDetails: 'Please enter a valid medical condition format like "1-Sugar" or "Diabetes Type-2"'
                }));
                return;
            }

            value = validValue;
        }

        setFormData(prevState => {
            const newState = {
                ...prevState,
                [name]: value
            };


            // Clear the corresponding error message when the user types in the field
            if (name === 'first_name' && value) {
                setErrors(prevErrors => ({ ...prevErrors, first_name: undefined })); // Clear First Name error
            }
            if (name === 'last_name' && value) {
                setErrors(prevErrors => ({ ...prevErrors, last_name: undefined })); // Clear Last Name error
            }
            if (name === 'mobile' && value) {
                setErrors(prevErrors => ({ ...prevErrors, mobile: undefined })); // Clear Mobile error
            }
            if (name === 'whatsApp_number' && value) {
                setErrors(prevErrors => ({ ...prevErrors, whatsApp_number: undefined })); // Clear WhatsApp error
            }
            if (name === 'email' && value) {
                setErrors(prevErrors => ({ ...prevErrors, email: undefined })); // Clear Email error
            }
            if (name === 'aadhar_number' && value) {
                setErrors(prevErrors => ({ ...prevErrors, aadhar_number: undefined })); // Clear Aadhaar error
            }
            if (name === 'pan_number' && value) {
                setErrors(prevErrors => ({ ...prevErrors, pan_number: undefined })); // Clear PAN error
            }
            if (name === 'occupation' && value) {
                setErrors(prevErrors => ({ ...prevErrors, occupation: undefined })); // Clear occupation error
            }
            if (name === 'weight' && value) {
                setErrors(prevErrors => ({ ...prevErrors, weight: undefined })); // Clear Weight error
            }
            if (name === 'height' && value) {
                setErrors(prevErrors => ({ ...prevErrors, height: undefined })); // Clear Height error
            }
            if (name === 'isSmoking' && value) {
                setErrors(prevErrors => ({ ...prevErrors, isSmoking: undefined })); // Clear
            }
            if (name === 'isTobacco' && value) {
                setErrors(prevErrors => ({ ...prevErrors, isTobacco: undefined })); //
            }
            if (name === 'preExistingDisease' && value) {
                setErrors(prevErrors => ({ ...prevErrors, preExistingDisease: undefined }));
            }
            // If createNew is checked and first_name or last_name changes, update head_name
            if (createNew && (name === 'first_name' || name === 'last_name')) {
                const fullName = `${name === 'first_name' ? value : newState.first_name} ${name === 'last_name' ? value : newState.last_name}`.trim();
                newState.head_name = fullName;
            }
            // Clear errors for Head Name and Group Code if they are being modified
            if (name === 'head_name' && value) {
                setErrors(prevErrors => ({ ...prevErrors, head_name: undefined })); // Clear Head Name error
            }
            if (name === 'group_code' && value) {
                setErrors(prevErrors => ({ ...prevErrors, group_code: undefined })); // Clear Group Code error
            }
            if (name === 'agent_id' && value) {
                setErrors(prevErrors => ({ ...prevErrors, agent_id: undefined })); // Clear Agent
            }
            if (name === 'occupation' && value) {
                setErrors(prevErrors => ({ ...prevErrors, occupation: undefined })); // Clear occupation error
            }
            // Clear the corresponding error message when the user types in the field

            return newState;
        });
    };

    // Add state for group code


    // Fetch existing groups on component mount
    useEffect(() => {
        if (id) {
            dispatch(getAllGroups());
        } // Fetch all groups from the database
    }, [dispatch]);

    // Add state for customer type
    const [customerType, setCustomerType] = useState('individual'); // Default to individual

    // Update group code logic based on customer type
    useEffect(() => {
        if (existingGroups && existingGroups.length > 0) {
            const groupCodes = existingGroups
                .map(group => group.group_code) // Extract group codes
                .filter(code => code !== undefined); // Filter out undefined codes

            if (groupCodes.length > 0) {
                // Generate group code based on customer type
                let newGroupCode;
                if (customerType === 'individual') {
                    newGroupCode = `${formData.first_name.slice(0, 4).toUpperCase()}${formData.mobile.slice(-4)}`; // First 4 letters of first name + last 4 digits of mobile
                } else if (customerType === 'corporate') {
                    newGroupCode = `${formData.company_name.slice(0, 4).toUpperCase()}${formData.pan_number.slice(-5, -1)}`; // First 4 letters of first name + last 4 digits of PAN
                }
                setGroupCode(newGroupCode); // Update group code
            }
        } else {
            // Generate group code for the first customer if no existing groups
            if (customerType === 'individual') {
                const newGroupCode = `${formData.first_name.slice(0, 4).toUpperCase()}${formData.mobile.slice(-4)}`;
                setGroupCode(newGroupCode);
            } else if (customerType === 'corporate') {
                const newGroupCode = `${formData.company_name.slice(0, 4).toUpperCase()}${formData.pan_number.slice(-5, -1)}`;
                setGroupCode(newGroupCode);
            }
        }
    }, [existingGroups, formData.first_name, formData.mobile, formData.pan_number, customerType]);

    // Add checkbox handler
    const handleCreateNewChange = (checked) => {
        setCreateNew(checked);
        if (checked) {
            // Set head_name to the full name when checkbox is checked
            const headName = customerType === 'corporate' ? formData.company_name : `${formData.first_name} ${formData.last_name}`.trim();
            //const fullName = `${formData.first_name} ${formData.last_name}`.trim();
            const newGroupCode = customerType === 'individual' ? `${formData.first_name.slice(0, 4).toUpperCase()}${formData.mobile.slice(-4)}` : `${formData.company_name.slice(0, 4).toUpperCase()}${formData.pan_number.slice(-5, -1)}`;
            setFormData(prev => ({
                ...prev,
                head_name: headName, // Set head_name to the full name
                group_code: newGroupCode // Assign the new group code
            }));
        } else {
            // Clear head_name when checkbox is unchecked
            setFormData(prev => ({
                ...prev,
                head_name: '', // Clear head_name
                group_code: '' // Clear group_code when checkbox is unchecked
            }));
        }
        setErrors(prevErrors => ({
            ...prevErrors,
            head_name: undefined,
            group_code: undefined
        }));
    };

    // Add state for error messages
    const [errors, setErrors] = useState({});

    // Validation function
    const validateForm = () => {
        const newErrors = {};
        if (customerType === 'individual') {
            if (!formData.first_name) newErrors.first_name = 'First Name is required';
            if (!formData.last_name) newErrors.last_name = 'Last Name is required';
            if (!formData.aadhar_number) {
                newErrors.aadhar_number = "Aadhaar Number is required.";
            } else if (!/^\d{12}$/.test(formData.aadhar_number.replace(/-/g, ""))) { // Remove hyphens for validation
                newErrors.aadhar_number = "Invalid Aadhaar Number. It should be a 12-digit numeric value.";
            }
            if (!formData.occupation) newErrors.occupation = "occupation is required";
            if (!formData.weight) newErrors.weight = "Weight is required";
            if (!formData.height) newErrors.height = "Height is required";
            if (!formData.isSmoking) newErrors.isSmoking = "Is Smoking is required";
            if (!formData.isTobacco) newErrors.isTobacco = "Is Tobacco is required";
            if (!formData.preExistingDisease) newErrors.preExistingDisease = "Pre Existing Disease is required";
            if (!formData.agent_id) newErrors.agent_id = "Agent ID is required";
            // Add validation for smoking details
            if (formData.isSmoking === 'Y' && !formData.smokingPerDay) {
                newErrors.smokingPerDay = 'Please specify smoking consumption per day';
            }

            // Add validation for tobacco details
            if (formData.isTobacco === 'Y' && !formData.tobaccoPerDay) {
                newErrors.tobaccoPerDay = 'Please specify tobacco consumption per day';
            }

            // Add validation for disease details
            if (formData.preExistingDisease === 'Y') {
                if (!formData.diseaseDetails) {
                    newErrors.diseaseDetails = 'Please specify the disease details';
                }
                // if (!formData.prescriptionFile) {
                //     newErrors.prescriptionFile = 'Please upload prescription file';
                // }
            }
        }

        if (!formData.mobile) {
            newErrors.mobile = 'Mobile is required';
        } else if (!/^[789]\d{9}$/.test(formData.mobile)) { // Validate mobile number
            newErrors.mobile = 'Mobile number must start with 7, 8, or 9 and be 10 digits long';
        }
        if (!formData.email) {
            newErrors.email = 'Email is required';
        } else if (!/^[a-z0-9._]+@[a-z0-9-]+(\.[a-z]{2,})+$/.test(formData.email)) {
            newErrors.email = 'Invalid email format. Please enter a valid email address.';
        }
        if (!formData.whatsApp_number) {
            newErrors.whatsApp_number = 'whatsApp Number is required';
        } else if (!/^[789]\d{9}$/.test(formData.whatsApp_number)) { // Validate mobile number
            newErrors.whatsApp_number = 'WhatsApp number must start with 7, 8, or 9 and be 10 digits long';
        }

        if (!formData.pan_number) {
            newErrors.pan_number = "PAN Number is required.";
        } else if (formData.pan_number.length !== 10) {
            newErrors.pan_number = "PAN Number must be exactly 10 characters long.";
        } else if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(formData.pan_number)) {
            newErrors.pan_number = "Invalid PAN Number. Format should be 5 letters, 4 digits, 1 letter (e.g., **********).";
        }
        if (customerType === 'corporate') {
            if (!formData.company_name) {
                newErrors.company_name = 'Company Name is required'; // Add error for company name
            }
            // Check for Head Name and Group Code
            if (!formData.head_name) {
                newErrors.head_name = 'Head Name is required for corporate customers';
            }
            if (!formData.group_code) {
                newErrors.group_code = 'Group Code is required for corporate customers';
            }
            if (formData.aadhar_number && !/^\d{12}$/.test(formData.aadhar_number.replace(/-/g, ""))) { // Remove hyphens for validation
                newErrors.aadhar_number = "Invalid Aadhaar Number. It should be a 12-digit numeric value.";
            }
        }

        setErrors(newErrors);
        if (Object.keys(newErrors).length !== 0) {
            setIsSubmitting(false);
        }
        return Object.keys(newErrors).length === 0; // Return true if no errors
    };

    // Handle save
    const handleSave = async () => {
        setIsSubmitting(true);
        if (!validateForm()) {
            setIsSubmitting(false);
            return; // Val  idate before saving
        }
        setIsSubmitting(true);
        const { customer_category, first_name, middle_name, last_name, mobile, whatsApp_number, email, pan_number, aadhar_number, comment_history, assigned_to, agent_id, isSmoking, isTobacco, preExistingDisease, weight, height, occupation } = formData;

        // Check for uniqueness only if creating a new customer
        let newErrors = {};
        if (!id) { // Only perform uniqueness checks if there is no id (i.e., creating a new customer)

            allCustomers.forEach(customer => {
                if (customer.mobile === mobile) {
                    newErrors.mobile = 'Mobile number already exists.';
                }
                if (customer.whatsApp_number === whatsApp_number) {
                    newErrors.whatsApp_number = 'WhatsApp number already exists.';
                }
                if (customer.email === email) {
                    newErrors.email = 'Email already exists.';
                }
                if (customer.pan_number === pan_number) { // Ensure correct variable name
                    newErrors.pan_number = 'PAN number already exists';
                }
                // Add uniqueness check for company name
                // if (customer.company_name === formData.company_name) {
                //     newErrors.company_name = 'Company name already exists.'; // New error for company name
                // }
            });

            // Log the errors found
            if (Object.keys(newErrors).length > 0) {
                setIsSubmitting(false);
            }
        }

        // // Set errors if any
        // if (Object.keys(newErrors).length > 0) {
        //     setErrors(prev => ({ ...prev, ...newErrors }));
        //     return; // Exit if there are errors
        // }

        // Proceed with saving the customer data
        const head_name = createNew; // This will be true or false based on the checkbox

        const customerData = {
            customer_category,
            first_name: customerType === 'corporate' ? null : first_name,
            middle_name: customerType === 'corporate' ? null : middle_name,
            last_name: customerType === 'corporate' ? null : last_name,
            mobile,
            whatsApp_number,
            email,
            aadhar_number,
            pan_number,
            head_name: createNew, // This will be true or false based on the checkbox
            comment_history,
            assigned_to,
            agent_id,
            customer_category: customerType,
            group_code: createNew ? groupCode : '', // Use groupCode state
            company_name: customerType === 'individual' ? null : formData.company_name, // Set company_name to null if individual
            occupation: customerType === 'individual' ? (formData.occupation || null) : null,
            height,
            weight,
            isTobacco,
            isSmoking,
            preExistingDisease,
            smokingPerDay: formData.smokingPerDay || null,
            tobaccoPerDay: formData.tobaccoPerDay || null,
            diseaseDetails: formData.diseaseDetails || null,
            discarge_summaryFile: formData.discarge_summaryFile,
        };
        try {
            let customerId;
            let response; // Declare response properly
            if (id) {
                // Update existing customer
                response = await dispatch(updateCustomer({ id: id, data: customerData })).unwrap();

                customerId = response?.id; // Check if response contains an ID
                toast.success('Customer updated successfully!');

                if (createNew) {
                    const groupingData = {
                        head_name,
                        customer_id: customerId,
                        group_code: groupCode, // Include group code
                    };
                    await dispatch(updateGrouping({ id: id, data: groupingData })).unwrap();
                }
            } else {
                // Create new customer
                response = await dispatch(createCustomerInfo(customerData)).unwrap();

                customerId = response?.id; // Check if response contains an ID

                if (createNew) {
                    const groupingData = {
                        head_name,
                        customer_id: customerId,
                        group_code: groupCode,
                    };
                    await dispatch(createCustomerGrouping(groupingData)).unwrap();
                }
            }
            // Redirect based on response data
            if (customerId) {
                // const { customer_category, customer_id } = response;
                if (customerData.customer_category === 'individual') {
                    navigate(`/dashboard/customer-member-information/${customerId}`);
                } else if (customerData.customer_category === 'corporate') {
                    navigate(`/dashboard/customer-address/${customerId}`);
                }

            } else if (id) {
                if (customerData.customer_category === 'individual') {
                    navigate(`/dashboard/customer-member-information/${id}`);
                } else if (customerData.customer_category === 'corporate') {
                    navigate(`/dashboard/customer-address/${id}`);
                }
            } else {
                console.error('Customer creation/update failed. No customer ID returned.');
            }
        } catch (error) {
            console.error('Error creating/updating customer:', error);
        } finally {
            setIsSubmitting(false);
            setCreateNew(false);
        }

    };

    const handleFileSelect = (file) => {
        if (file.image.size > 5 * 1024 * 1024) {
            setErrors(prevErrors => ({
                ...prevErrors,
                discharge_summaryFile: 'File size must not exceed 5MB'
            }));
            return;
        }

        try {
            // Map section_name to form data field names
            const fieldMapping = {
                'discharge_summary': 'discharge_summaryFile'
            };

            const formField = fieldMapping[file.section_name];

            if (formField) {
                setFormData(prevData => ({
                    ...prevData,
                    [formField]: file.image
                }));

                // Clear error if file is valid
                setErrors(prevErrors => ({
                    ...prevErrors,
                    [formField]: null
                }));
            }

        } catch (error) {
            console.error('File upload error:', error);
            toast.error('Failed to upload file');
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/customer-Master');
        setCreateNew(false);
    };

    return (
        <Box sx={{
            paddingLeft: { xs: '20px', md: '40px' },
            paddingRight: { xs: '20px', md: '40px' },
            paddingBottom: '40px'
        }}>
            {(loading || isSubmitting) && (
                <Box
                    sx={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(255, 255, 255, 0.8)', // Semi-transparent background
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1000 // Ensure it is above other content
                    }}
                    aria-live="polite" // Accessibility feature
                >
                    <Box sx={{ textAlign: 'center' }}>
                        <CircularProgress /> {/* Show loading spinner */}
                        <Typography variant="h6" sx={{ marginTop: 2 }}>
                            {isSubmitting ? "Submitting..." : "Loading..."} {/* Custom message */}
                        </Typography>
                    </Box>
                </Box>
            )}
            <form encType="multipart/form-data">
                <Grid container spacing={2} style={{ display: 'flex', alignItems: 'center' }}>
                    {/* Header Row */}
                    <Grid item xs={12} sx={{
                        position: 'sticky',
                        top: { xs: '140px', sm: '140px', md: '164px', lg: '164px' },
                        zIndex: 101,
                        backgroundColor: 'white',
                        // borderBottom: '2px solid #E0E0E0',
                        //  padding: '10px 0',
                        display: "flex"
                    }}>
                        <Grid item xs={12} md={8} style={{ display: 'flex', alignItems: 'center' }}>
                            <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <ModuleName moduleName="Customer" pageName={"Create"} />
                            </Box>
                        </Grid>


                        <Grid item xs={12} md={4} sx={{
                            display: 'flex',
                            justifyContent: { xs: 'center', md: 'flex-end' },
                            flexWrap: { xs: 'wrap', md: 'nowrap' },
                            gap: { xs: 1, md: 0 }
                        }}>

                            <Button variant="outlined" size="small"
                                sx={{
                                    minWidth: { xs: '20%', md: '100px' },
                                    mx: { xs: 0.5, md: 0.5 },
                                    color: 'green',
                                    borderColor: 'green',
                                    mt: 3,
                                    textTransform: 'none'
                                }}
                                onClick={handleSave}
                            >
                                Save & Next
                            </Button>
                            <Button variant="outlined" size="small" sx={{
                                minWidth: { xs: '20%', md: '100px' },
                                mx: { xs: 0.5, md: 0.5 },
                                color: 'red',
                                borderColor: 'red',
                                mt: 3,
                                textTransform: 'none'
                            }}
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </Grid>
                    </Grid>
                    <Grid container >
                        <CustomSection titles={['Overview', 'Personal Details', 'Member Information', 'Address', 'Grouping']} page='customer' customerType={formData.customer_category} />
                    </Grid>

                    <Grid item xs={12}>
                        <Box sx={{
                            //backgroundColor: '#f0f0f0',
                            display: 'flex', alignItems: 'center',
                            padding: '10px',
                            borderRadius: '4px',
                            height: '60px',
                            fontSize: '18px',
                            fontStyle: 'normal',
                            fontWeight: '700',
                            lineHeight: '27px',
                            color: '#4C5157'
                        }}>
                            <h5>Personal Details</h5>
                            <IconButton sx={{ marginLeft: '8px', color: 'green' }} onClick={handleEditToggle}>
                                <EditIcon />
                            </IconButton>
                            <div style={{ height: '100vh', display: 'flex', alignItems: 'center' }}>

                            </div>
                        </Box>
                        <hr />
                    </Grid>
                    {/* Form Fields - Adjust column widths */}
                    <Grid item xs={12}>
                        <h5>Customer Type</h5>
                        <FormControlLabel
                            control={
                                <Radio
                                    checked={customerType === 'individual'}
                                    onChange={handleCustomerTypeChange}
                                    value="individual"
                                    disabled={!isEditing}
                                    name="customerType"
                                />
                            }
                            label="Individual"
                        />
                        <FormControlLabel
                            control={
                                <Radio
                                    checked={customerType === 'corporate'}
                                    onChange={handleCustomerTypeChange}
                                    value="corporate"
                                    name="customerType"
                                    disabled={!isEditing}
                                />
                            }
                            label="Corporate"
                        />
                    </Grid>
                    {customerType === 'corporate' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Company Name"
                                name="company_name"
                                value={formData.company_name}
                                onChange={handleInputChange}
                                fullWidth
                                disabled={!isEditing}
                                error={!!errors.company_name} // Show error state
                                helperText={errors.company_name} // Show error message

                                sx={{

                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>
                    )}
                    {customerType === 'individual' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="First Name"
                                name="first_name"
                                value={formData.first_name}
                                onChange={handleInputChange}
                                fullWidth
                                disabled={!isEditing}
                                error={!!errors.first_name} // Show error state
                                helperText={errors.first_name} // Show error message
                                sx={{

                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>
                    )}
                    {customerType === 'individual' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Middle Name"
                                name="middle_name"
                                value={formData.middle_name}
                                onChange={handleInputChange}
                                fullWidth
                                disabled={!isEditing}
                                error={!!errors.middle_name} // Show error state
                                helperText={errors.middle_name} // Show error message

                            />
                        </Grid>
                    )}
                    {customerType === 'individual' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Last Name"
                                name="last_name"
                                disabled={!isEditing}
                                value={formData.last_name}
                                onChange={handleInputChange}
                                fullWidth
                                isRequired
                                error={!!errors.last_name} // Show error state
                                helperText={errors.last_name} // Show error message

                            />
                        </Grid>
                    )}
                    {customerType === 'individual' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <Autocomplete
                                freeSolo
                                options={occupationOptions} // Array of { value: id, label: label_name }
                                getOptionLabel={(option) =>
                                    typeof option === 'string' ? option : option.label || ''
                                } // Handles both free-text and predefined options
                                value={
                                    occupationOptions.find((option) => option.value === formData.occupation) || null
                                } // Match the ID in formData
                                onChange={(event, newValue) => {
                                    handleInputChange({
                                        target: {
                                            name: 'occupation',
                                            value: newValue?.value || '', // Save the ID (or empty string if cleared)
                                        },
                                    });
                                }}
                                onInputChange={(event, newInputValue) => {
                                    if (newInputValue && !isEditing) return; // Prevent changes if not in edit mode
                                    handleInputChange({
                                        target: {
                                            name: 'occupation',
                                            value: newInputValue, // Save free-text input if `freeSolo` is enabled
                                        },
                                    });
                                }}
                                disabled={!isEditing}
                                required
                                renderInput={(params) => (
                                    <CustomTextField
                                        {...params}
                                        label="Occupation"
                                        helperText={errors.occupation}
                                        error={!!errors.occupation}
                                        fullWidth
                                        isRequired
                                    />
                                )}
                            />
                        </Grid>


                    )}
                    <Grid item xs={12} sm={6} md={3}>

                        <CustomTextField
                            label="Mobile"
                            name="mobile"
                            disabled={!isEditing}
                            value={formData.mobile}
                            onChange={handleInputChange}
                            type="tel"
                            applyPrefix
                            error={!!errors.mobile} // Show error state
                            helperText={errors.mobile} // Show error message
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                        {/* {error.personal_mobile && <FormHelperText error>{error.personal_mobile}</FormHelperText>} */}

                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="Whatsapp Number"
                            name="whatsApp_number"
                            disabled={!isEditing}
                            value={formData.whatsApp_number}
                            onChange={handleInputChange}
                            error={!!errors.whatsApp_number} // Show error state
                            helperText={errors.whatsApp_number} // Show error message
                            type="tel"
                            applyPrefix
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px', // Width of the red line
                                        backgroundColor: 'red', // Color of the line
                                    },
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label=" Email "
                            name="email"
                            disabled={!isEditing}
                            value={formData.email}
                            error={!!errors.email} // Show error state
                            helperText={errors.email} // Show error message
                            onChange={handleInputChange}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="Aadhaar Number (1234-5678-9012)"
                            name="aadhar_number"
                            value={formData.aadhar_number} // Use formData for editing
                            onChange={handleInputChange}
                            error={Boolean(errors.aadhar_number)} // Set error state
                            helperText={errors.aadhar_number} // Display helper text
                            disabled={id || !isEditing} // Disable based on edit mode

                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                        {/* {error.aadhar_number && <FormHelperText error>{error.aadhar_number}</FormHelperText>} */}

                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="PAN Number (**********)"
                            name="pan_number"
                            value={formData.pan_number} // Use formData for editing
                            onChange={handleInputChange}
                            disabled={id || !isEditing} // Disable based on edit mode

                            error={Boolean(errors.pan_number)} // Set error state
                            helperText={errors.pan_number} // Display helper text
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />

                        {/* {error.pan_number && <FormHelperText error>{error.pan_number}</FormHelperText>} */}
                    </Grid>
                    {/* add height FIELD */}
                    {customerType === 'individual' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Height (in cm)"
                                name="height"
                                value={formData.height} // Use formData for editing
                                onChange={handleInputChange}
                                disabled={!isEditing} // Disable based on edit mode
                                error={Boolean(errors.height)} // Set error state
                                helperText={errors.height} // Display helper text
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>
                    )}
                    {/* WEIGT FIELD */}
                    {customerType === 'individual' && (
                        <Grid item xs={12} sm={6} md={3}>
                            <CustomTextField
                                label="Weight (in kg)"
                                name="weight"
                                value={formData.weight} // Use formData for editing
                                onChange={handleInputChange}
                                disabled={!isEditing} // Disable based on edit mode
                                error={Boolean(errors.weight)} // Set error state
                                helperText={errors.weight} // Display helper text
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}

                            />
                        </Grid>
                    )}
                    {/* RADIO BUTTONS FOR IS SMOKING YES OR NO */}
                    {/* ADD COLOR TO H5 TAG */}
                    {customerType === 'individual' && (
                        <Grid item xs={12} container spacing={2} alignItems="center">
                            <Grid item xs={12} sm={3}>
                                <Typography variant="h6" color="textSecondary">
                                    Is Smoking?
                                </Typography>
                                <FormControl component="fieldset" error={Boolean(errors.isSmoking)} disabled={!isEditing}>
                                    <RadioGroup
                                        row
                                        name="isSmoking"
                                        value={formData.isSmoking}
                                        onChange={(e) => {
                                            handleInputChange(e);
                                            setFormData((prev) => ({
                                                ...prev,
                                                isSmoking: e.target.value,
                                                smokingPerDay: e.target.value === "Y" ? formData.smokingPerDay || "" : "",
                                            }));
                                        }}
                                    >
                                        <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                        <FormControlLabel value="N" control={<Radio />} label="No" />
                                    </RadioGroup>
                                    {errors.isSmoking && (
                                        <FormHelperText>{errors.isSmoking}</FormHelperText>
                                    )}
                                </FormControl>
                            </Grid>
                            {formData.isSmoking === "Y" && (
                                <Grid item xs={12} md={4} sm={3}>
                                    <CustomTextField
                                        label="How many cigarettes consumed per day?"
                                        name="smokingPerDay"
                                        value={formData.smokingPerDay || ""}
                                        onChange={handleInputChange}
                                        fullWidth
                                        error={Boolean(errors.smokingPerDay)}
                                        helperText={errors.smokingPerDay}
                                        disabled={!isEditing}
                                        isRequired
                                        type="text"
                                    // inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }} // Add numeric input mode

                                    />
                                </Grid>
                            )}
                        </Grid>
                    )}
                    {/* ADD RADIO BUTTONS FOR IS TOBACO YES OR NO */}
                    {customerType === 'individual' && (
                        <Grid item xs={12} container spacing={2} alignItems="center">
                            <Grid item xs={12} sm={3}>
                                <Typography variant="h6" color="textSecondary">
                                    Is Tobacco?
                                </Typography>
                                <FormControl component="fieldset" error={Boolean(errors.isTobacco)} disabled={!isEditing}>
                                    <RadioGroup
                                        row
                                        name="isTobacco"
                                        value={formData.isTobacco}
                                        onChange={(e) => {
                                            handleInputChange(e);
                                            setFormData((prev) => ({
                                                ...prev,
                                                isTobacco: e.target.value,
                                                tobaccoPerDay: e.target.value === "Y" ? formData.tobaccoPerDay || "" : "",
                                            }));
                                        }}
                                    >
                                        <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                        <FormControlLabel value="N" control={<Radio />} label="No" />
                                    </RadioGroup>
                                    {errors.isTobacco && (
                                        <FormHelperText>{errors.isTobacco}</FormHelperText>
                                    )}
                                </FormControl>
                            </Grid>
                            {formData.isTobacco === "Y" && (
                                <Grid item xs={12} sm={3} md={4}>
                                    <CustomTextField
                                        label="How much tobacco consumed per day?"
                                        name="tobaccoPerDay"
                                        value={formData.tobaccoPerDay || ""}
                                        onChange={handleInputChange}
                                        fullWidth
                                        error={Boolean(errors.tobaccoPerDay)}
                                        helperText={errors.tobaccoPerDay}
                                        disabled={!isEditing}
                                        isRequired
                                        type="text"
                                    //  inputProps={{ inputMode: 'numeric', pattern: '[0-9]*' }} // Add numeric input mode

                                    />
                                </Grid>
                            )}
                        </Grid>
                    )}
                    {/* Pre Existing Disease radio buttons yes or no */}
                    {customerType === 'individual' && (
                        <Grid item xs={12} container spacing={2} alignItems="center">
                            <Grid item xs={12} sm={3}>
                                <Typography variant="h6" color="textSecondary">
                                    Pre-Existing Disease?
                                </Typography>
                                <FormControl component="fieldset" error={Boolean(errors.preExistingDisease)} disabled={!isEditing}>
                                    <RadioGroup
                                        row
                                        name="preExistingDisease"
                                        value={formData.preExistingDisease}
                                        onChange={(e) => {
                                            handleInputChange(e);
                                            setFormData((prev) => ({
                                                ...prev,
                                                preExistingDisease: e.target.value,
                                                diseaseDetails: e.target.value === "Y" ? formData.diseaseDetails || "" : "",
                                                prescriptionFile: e.target.value === "Y" ? formData.prescriptionFile || null : null,
                                            }));
                                        }}
                                    >
                                        <FormControlLabel value="Y" control={<Radio />} label="Yes" />
                                        <FormControlLabel value="N" control={<Radio />} label="No" />
                                    </RadioGroup>
                                    {errors.preExistingDisease && (
                                        <FormHelperText>{errors.preExistingDisease}</FormHelperText>
                                    )}
                                </FormControl>
                            </Grid>
                            {formData.preExistingDisease === "Y" && (
                                <>
                                    <Grid item xs={12} sm={3} md={4}>
                                        <CustomTextField
                                            label="Pre-Existing Diseases"
                                            name="diseaseDetails"
                                            fullWidth
                                            multiline
                                            rows={3}
                                            value={formData.diseaseDetails || ""}
                                            onChange={handleInputChange}
                                            isRequired
                                            error={Boolean(errors.diseaseDetails)}
                                            helperText={errors.diseaseDetails}
                                            disabled={!isEditing}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={3}>
                                        <CustomFileUpload
                                            name="discarge_summary"
                                            section_name="discarge_summary"
                                            accept=".pdf,.jpeg,.png,.jpg"
                                            label={"Upload Discharge Summary"}
                                            onFileSelect={handleFileSelect}
                                            insertedFile={!formData.discarge_summaryFile ? null : {
                                                section_name: 'discarge_summary',
                                                name: 'Discharge Summary',
                                                url: formData.discarge_summaryFile
                                            }}
                                            error={errors.discarge_summaryFile}
                                            helperText={errors.discarge_summaryFile || "Upload in Pdf, Jpeg, Png, Jpg format"}
                                            disabled={!isEditing}
                                            sx={{
                                                '& .MuiFormHelperText-root': {
                                                    display: 'block',
                                                    visibility: 'visible'
                                                }
                                            }}
                                        />
                                    </Grid>
                                </>
                            )}
                        </Grid>
                    )}
                    <Grid item xs={12}>
                        <hr />
                        <Box sx={{
                            padding: { xs: '5px', md: '10px' },
                            height: { xs: '50px', md: '60px' },
                            fontSize: { xs: '16px', md: '18px' },
                            // ... rest of the styling
                        }}>
                            <h5>Grouping Name      <FormControlLabel
                                control={
                                    <CustomCheckbox
                                        checked={createNew}
                                        onChange={handleCreateNewChange}
                                        disabled={!isEditing}
                                    />
                                }
                                label="Create New"
                                labelPlacement="end"
                                sx={{ paddingLeft: "10px" }}
                            /> </h5>

                        </Box>
                        <hr />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="Head Name"
                            disabled
                            name="head_name"
                            value={formData.head_name}
                            onChange={handleInputChange}
                            error={!!errors.head_name} // Show error state
                            helperText={errors.head_name} // Show error message
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <CustomTextField
                            label="Group Code"
                            disabled
                            name="group_code"
                            // disabled={!isEditing}
                            value={formData.group_code}
                            onChange={handleInputChange}
                            error={!!errors.group_code} // Show error state
                            helperText={errors.group_code} // Show error message
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        left: 0,
                                        top: 0,
                                        bottom: 0,
                                        width: '3px',
                                        backgroundColor: 'red',
                                        zIndex: 1,
                                    }
                                },
                            }}
                        />
                    </Grid>

                    <Grid item xs={12}>
                        <Box sx={{
                            padding: { xs: '5px', md: '10px' },
                            height: { xs: '50px', md: '60px' },
                            fontSize: { xs: '16px', md: '18px' },
                            // ... rest of the styling
                        }}>
                            <h5>Detailed Summary </h5>
                        </Box>
                        <hr />
                    </Grid>
                    <Grid item xs={12} md={11}>
                        <CustomTextField
                            label="Comments History"
                            multiline
                            rows={3}
                            fullWidth
                            disabled={!isEditing}
                            name="comment_history"
                            value={formData.comment_history}
                            onChange={handleInputChange}
                        />
                    </Grid>

                    <Grid item xs={12}>
                        <Box sx={{
                            padding: { xs: '5px', md: '10px' },
                            height: { xs: '50px', md: '60px' },
                            fontSize: { xs: '16px', md: '18px' },
                            // ... rest of the styling
                        }}>
                            <h5>Created Info </h5>
                        </Box>
                        <hr />
                    </Grid>

                    <Grid item xs={12} container spacing={2} justifyContent="space-between">
                        {/* Left Aligned TextField */}
                        <Grid item xs={3}>
                            <CustomTextField
                                label="Assigned To"
                                disabled
                                name="assigned_to"
                                value={formData.assigned_to}
                                onChange={handleInputChange}
                                error={!!errors.assigned_to} // Show error state
                                helperText={errors.assigned_to} // Show error message
                                fullWidth
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>
                        <Grid item xs={3}>

                            <Dropdown
                                label="Select Agent"
                                name="agent_id"
                                value={formData.agent_id}
                                onChange={handleInputChange}
                                options={agentOptions}
                                fullWidth
                                disabled={!isEditing}
                                error={!!errors.agent_id} // Show error state
                                helperText={errors.agent_id} // Show error message
                                // disabled={userId?.startsWith('RM-') || !isEditing}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    }
                                }}
                            />

                        </Grid>

                        {/* Right Aligned TextField */}
                        <Grid item xs={3}>
                            <CustomTextField
                                label="Created At"
                                disabled
                                value={formatDate(formData.created_at)}
                                name="created_at"
                                fullWidth
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&::before': {
                                            content: '""',
                                            position: 'absolute',
                                            left: 0,
                                            top: 0,
                                            bottom: 0,
                                            width: '3px',
                                            backgroundColor: 'red',
                                            zIndex: 1,
                                        }
                                    },
                                }}
                            />
                        </Grid>
                    </Grid>

                </Grid>
            </form>
        </Box >
    );
};

export default CustomerPersonalInfo;
