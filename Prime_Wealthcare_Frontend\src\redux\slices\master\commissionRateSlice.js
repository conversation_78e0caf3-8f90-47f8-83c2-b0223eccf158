import { createSlice } from '@reduxjs/toolkit';
import {
  fetchCommissionRates,
  fetchCommissionRateById,
  createCommissionRate,
  updateCommissionRate,
  deleteCommissionRate,
  reinstateCommissionRate,
  fetchCommissionRatesByCompany,
  updateMultipleCommissionRates,searchCommissionRate,getCommissionRateByCriteria
} from '../../actions/action';

const commissionRateSlice = createSlice({
  name: 'commissionRate',
  initialState: {
    commissionRates: [],
    commissionRateDetails: null,
    commissionSource: [],
    commissionType: [],
    policyType: [],
    loading: false,
    error: null
  },
  reducers: {},
  extraReducers: (builder) => {
    // Fetch all commission rates
    builder.addCase(fetchCommissionRates.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(fetchCommissionRates.fulfilled, (state, action) => {
      state.loading = false;
      state.commissionRates = action.payload;
    });
    builder.addCase(fetchCommissionRates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    });

    // Fetch commission rate by ID
    builder.addCase(fetchCommissionRateById.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(fetchCommissionRateById.fulfilled, (state, action) => {
      state.loading = false;
      state.commissionRateDetails = action.payload;
    });
    builder.addCase(fetchCommissionRateById.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    });

    // Create new commission rate
    builder.addCase(createCommissionRate.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(createCommissionRate.fulfilled, (state, action) => {
      state.loading = false;
      state.commissionRates.push(action.payload);
    });
    builder.addCase(createCommissionRate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })

     // Update a single commission rate
     .addCase(updateCommissionRate.pending, (state) => {
      state.loading = true;
      state.error = null;
      state.successMessage = null;
    })
    .addCase(updateCommissionRate.fulfilled, (state, action) => {
      state.loading = false;
      const updatedRate = action.payload;
      // Find and update the specific commission rate in the list
      const index = state.commissionRates.findIndex(rate => rate.id === updatedRate.id);
      if (index !== -1) {
        state.commissionRates[index] = updatedRate;
      }
      state.successMessage = 'Commission rate updated successfully';
    })
    .addCase(updateCommissionRate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload || 'Error updating commission rate';
    })

    // Delete commission rate by ID
    builder.addCase(deleteCommissionRate.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(deleteCommissionRate.fulfilled, (state, action) => {
      state.loading = false;
      state.commissionRates = state.commissionRates.filter(rate => rate.id !== action.meta.arg);
    });
    builder.addCase(deleteCommissionRate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    });

    // Reinstate commission rate by ID
    builder.addCase(reinstateCommissionRate.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(reinstateCommissionRate.fulfilled, (state, action) => {
      state.loading = false;
      state.commissionRates.push(action.payload);
    });
    builder.addCase(reinstateCommissionRate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    });
     builder
      .addCase(fetchCommissionRatesByCompany.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCommissionRatesByCompany.fulfilled, (state, action) => {
        state.loading = false;
        state.commissionRates = action.payload;
      })
      .addCase(fetchCommissionRatesByCompany.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      .addCase(updateMultipleCommissionRates.pending, (state) => {
      state.loading = true;
      state.error = null;
  })
  .addCase(updateMultipleCommissionRates.fulfilled, (state, action) => {
      state.loading = false;
      const updatedRates = action.payload;
  
      // Ensure updatedRates is an array
      if (Array.isArray(updatedRates) && Array.isArray(state.commissionRates)) {
          state.commissionRates = state.commissionRates.map((rate) => {
              const updatedRate = updatedRates.find((r) => r.id === rate.id);
              // Merge updated values into the existing commission rates
              return updatedRate ? { ...rate, ...updatedRate } : rate;
          });
      } else {
          console.error('Expected updatedRates to be an array:', updatedRates);
      }
  })
  .addCase(updateMultipleCommissionRates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload || 'Error updating multiple commission rates';
  });
  
 // Handle searchCommissionRate action
 builder
 .addCase(searchCommissionRate.pending, (state) => {
   state.loading = true;
   state.error = null;
 })
 .addCase(searchCommissionRate.fulfilled, (state, action) => {
   state.loading = false;
   state.commissionRates = action.payload;
 })
 .addCase(searchCommissionRate.rejected, (state, action) => {
   state.loading = false;
   state.error = action.payload;
 });

// Handle getCommissionRateByCriteria action
builder
 .addCase(getCommissionRateByCriteria.pending, (state) => {
   state.loading = true;
   state.error = null;
 })
 .addCase(getCommissionRateByCriteria.fulfilled, (state, action) => {
   state.loading = false;
   state.commissionRates = action.payload;
 })
 .addCase(getCommissionRateByCriteria.rejected, (state, action) => {
   state.loading = false;
   state.error = action.payload;
 });
},
});

export default commissionRateSlice.reducer;
