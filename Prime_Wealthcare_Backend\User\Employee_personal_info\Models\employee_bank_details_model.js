const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');


const create = async (data) => {
    try {
        return await db('employee_bank_details').insert(data);
    } catch (error) {
        console.error('Error inserting Employee BankDetails :', error);
        throw error;
    }
};

const getAll = async () => {
    try {
        const Employees = await db('employee_bank_details').select('*');
        return Employees;
    } catch (error) {
        console.error('Error retrieving Employee BankDetails:', error);
        throw error;
    }
};

const findById = async (id) => {
    try {
        const Employee = await db('employee_bank_details').where({ 'employee_id': id }).first();
        return Employee;
    } catch (error) {
        throw error;
    }
};
const findByEmployeeId = async (id) => {
    try {
        const Employee = await db('employee_bank_details')
            .join('bank_list', 'employee_bank_details.bank_name', '=', 'bank_list.id')
            .join('employee_personal_info', 'employee_bank_details.employee_id', '=', 'employee_personal_info.id')
            .where('employee_bank_details.employee_id', id)
            .select('employee_bank_details.*', 'bank_list.label_name as bank_name', 'employee_personal_info.user_id', 'employee_personal_info.employee_full_name as employee_name');

        Employee.forEach(emp => {
            emp.created_at = formatDate(emp.created_at);
            emp.updated_at = formatDate(emp.updated_at);
            emp.status = emp.status ? 'Active' : 'Inactive'; // Convert boolean status to string

        });

        return Employee;
    } catch (error) {
        throw error;
    }
};

// Utility function to format date
const formatDate = (date) => {
    const d = new Date(date);
    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
};

// Update Employee by ID
const update = async (id, EmployeeData) => {
    if (!id) throw new Error("Employee BankDetails ID is required");
    try {
        EmployeeData.updated_at = getCurrentTimestamp();

        const result = await db('employee_bank_details').where('employee_id', id).update(EmployeeData);
        if (result) {

        } else {
            console.error(`No Employee BankDetails found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating Employee BankDetails with ID: ${id}`, error);
        throw error;
    }
};
// Delete role by ID
const deleteById = async (id) => {
    try {
        const result = await db('employee_bank_details').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};
const deleteFirstBankById = async (id) => {
    try {
        // Check if second bank details exist
        const bankDetails = await db('employee_bank_details')
            .where('employee_id', id)
            .first();

        if (bankDetails.account_holder_name_2) {
            // If second bank exists, only clear first bank details
            const result = await db('employee_bank_details')
                .where('employee_id', id)
                .update({
                    account_holder_name: null,
                    bank_name: null,
                    branch_name: null,
                    account_number: null,
                    IFSC_code: null,
                    account_Type: null,
                    canceled_cheque: null,
                    is_active_bank: 'second', // Set active bank to second if first is deleted
                    updated_at: getCurrentTimestamp()
                });
            return result;
        } else {
            // If no second bank, delete the entire record
            const result = await db('employee_bank_details')
                .where('employee_id', id)
                .del();
            return result;
        }
    } catch (error) {
        throw error;
    }
};

const deleteSecondBankById = async (id) => {
    try {
        // Check if first bank details exist
        const bankDetails = await db('employee_bank_details')
            .where('employee_id', id)
            .first();
        if (bankDetails.account_holder_name) {
            // If first bank exists, only clear second bank details
            const result = await db('employee_bank_details')
                .where('employee_id', id)
                .update({
                    account_holder_name_2: null,
                    bank_name_2: null,
                    branch_name_2: null,
                    account_number_2: null,
                    IFSC_code_2: null,
                    account_Type_2: null,
                    canceled_cheque_2: null,
                    is_active_bank: 'first', // Set active bank to first if second is deleted
                    updated_at: getCurrentTimestamp()
                });
            return result;
        } else {
            // If no first bank, delete the entire record
            const result = await db('employee_bank_details')
                .where('employee_id', id)
                .del();
            return result;
        }
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    getAll,
    findById,
    update,
    deleteById,
    findByEmployeeId,
    deleteFirstBankById,
    deleteSecondBankById
}