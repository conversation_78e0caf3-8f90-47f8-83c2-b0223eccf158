const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Base directory for uploads
//const baseUploadDir = path.join(__dirname, '..', '../../../Prime_Wealthcare_Frontend/public/uploads/employee');
const uploadDir = process.env.UPLOAD_DIR;
// Configure storage settings
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Extract user ID from the request
    const userId = req.body.user_id;

    // Create a subdirectory for the user
    const userUploadDir = path.join(uploadDir, 'employees', userId);

    // Check if the directory exists; if not, create it
    if (!fs.existsSync(userUploadDir)) {
      fs.mkdirSync(userUploadDir, { recursive: true });
    }

    // Pass the directory to multer
    cb(null, userUploadDir);
  },
  filename: (req, file, cb) => {
    // Generate a unique file name
    const uniqueName = `${Date.now()}-${file.originalname}`;
    cb(null, uniqueName);
  },
});

// Create the multer instance
const upload = multer({ storage });

module.exports = upload;
