const express = require('express');
const { login, logout } = require('../Controllers/loginController');
const { sendOTP, verifyOTP, resetPassword } = require('../Controllers/otpController');
const { changePassword } = require('../Controllers/changePasswordController');
const { validateToken } = require('../Controllers/loginController');
const authenticateToken = require('../Middleware/authMiddleware')
const router = express.Router();

// Login route
router.post('/login', login);

router.post('/forgot-password', sendOTP); // Send OTP to email

router.post('/verify-otp', verifyOTP); // Verify OTP before resetting password

// Change password route (protected by JWT token)
router.post('/change-password', changePassword);

router.post('/reset-password', resetPassword);

router.get('/validate-token', authenticateToken, validateToken);

router.post('/logout', logout)

module.exports = router; 
