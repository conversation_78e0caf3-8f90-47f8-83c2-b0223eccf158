// Popup.jsx

import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Typography
} from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import DeleteIcon from '@mui/icons-material/Delete';
import DropDown from '../../../components/table/DropDown'; // Ensure this path is correct
import { updateCommissionRate } from '../../../redux/actions/action'; // Ensure this path is correct
import { markRowAsSaved } from '../../../redux/slices/master/savedRowsSlice'
import 'dayjs/locale/en'; // Import locale if needed
import './Pop.css'; // Import your custom CSS
import dayjs from 'dayjs';
import { toast } from 'react-toastify';
import CustomTextField from '../../../components/CustomTextField';


const generateOptions = (start, end, step) => {
  const options = [];
  for (let i = start; i <= end; i += step) {
    options.push({ value: i, label: i.toLocaleString() });
  }
  return options;
};

const Popup = ({ open, onClose, existingId, rowData = {}, onSaveSuccess }) => {
  const dispatch = useDispatch();

  const [selectedOption, setSelectedOption] = useState('');
  const [formData, setFormData] = useState({
    // First Set of Fields
    rangeFrom: '',
    rangeTo: '',
    extraPercent: '',
    effectiveFrom: null,
    effectiveTo: null,
    // Second Set of Fields
    rangeFrom2: '',
    rangeTo2: '',
    extraPercent2: '',
    effectiveFrom2: null,
    effectiveTo2: null,
    calculatedOn: '',
    showSecondSet: false, // Flag to show second set
  });

  const [errors, setErrors] = useState({
    // First Set of Fields
    rangeFrom: '',
    rangeTo: '',
    extraPercent: '',
    effectiveFrom: '',
    effectiveTo: '',
    // Second Set of Fields
    rangeFrom2: '',
    rangeTo2: '',
    extraPercent2: '',
    effectiveFrom2: '',
    effectiveTo2: '',
    selectedOption: '', // Add error state for radio button
  });

  useEffect(() => {

    if (rowData && rowData.id) {
      // Populate form data with values from the row for editing
      setFormData({
        rangeFrom: rowData.range_from || '',
        rangeTo: rowData.range_to || '',
        extraPercent: rowData.extra_percentage || '',
        effectiveFrom: rowData.effective_from ? dayjs(rowData.effective_from) : null, // Keep dayjs object
        effectiveTo: rowData.effective_to ? dayjs(rowData.effective_to) : null, // Keep dayjs object
        rangeFrom2: rowData.range_from2 || '',
        rangeTo2: rowData.range_to2 || '',
        extraPercent2: rowData.extra_percentage2 || '',
        effectiveFrom2: rowData.effective_from2 ? dayjs(rowData.effective_from2) : null, // Keep dayjs object
        effectiveTo2: rowData.effective_to2 ? dayjs(rowData.effective_to2) : null, // Keep dayjs object
        calculatedOn: rowData.calculated_on || '',
        showSecondSet: !!rowData.range_from2 || !!rowData.range_to2 || !!rowData.extra_percentage2 || !!rowData.effective_from2 || !!rowData.effective_to2,
      });

      // Set selectedOption based on rowData.calculated_on
      setSelectedOption(rowData.calculated_on || '');
    } else if (existingId) {
      // Reset form for creating new entry
      setFormData({
        rangeFrom: '',
        rangeTo: '',
        extraPercent: '',
        effectiveFrom: null,
        effectiveTo: null,
        rangeFrom2: '',
        rangeTo2: '',
        extraPercent2: '',
        effectiveFrom2: null,
        effectiveTo2: null,
        showSecondSet: false,
      });

      // Reset selectedOption
      setSelectedOption('');
    }

    // Reset errors
    setErrors({
      rangeFrom: '',
      rangeTo: '',
      extraPercent: '',
      effectiveFrom: '',
      effectiveTo: '',
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: '',
      effectiveTo2: '',
      selectedOption: '', // Error for radio button
    });

    // Optional: Reset selected option if needed
    if (!rowData || !rowData.id) {
      setSelectedOption('');
    }
  }, [rowData, existingId]);

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
    // Clear the error for selectedOption
    setErrors((prevErrors) => ({
      ...prevErrors,
      selectedOption: '',
    }));
  };

  const handleInputChange = (field, value) => {
    setFormData((prevData) => ({
      ...prevData,
      [field]: value,
    }));

    // Clear the error for the field being updated
    setErrors((prevErrors) => ({
      ...prevErrors,
      [field]: '',
    }));
  };

  const handleAddMore = () => {
    setFormData((prevData) => ({
      ...prevData,
      showSecondSet: true,
    }));
  };

  const handleDeleteSecondSet = () => {
    setFormData((prevData) => ({
      ...prevData,
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: null,
      effectiveTo2: null,
      showSecondSet: false,
    }));
    // Clear errors related to the second set
    setErrors((prevErrors) => ({
      ...prevErrors,
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: '',
      effectiveTo2: '',
    }));
  };

  const validateForm = () => {
    let valid = true;
    let newErrors = {
      rangeFrom: '',
      rangeTo: '',
      extraPercent: '',
      effectiveFrom: '',
      effectiveTo: '',
      rangeFrom2: '',
      rangeTo2: '',
      extraPercent2: '',
      effectiveFrom2: '',
      effectiveTo2: '',
      selectedOption: '', // Add error state for radio button
    };

    // First Set Validation: All fields are required
    if (!formData.rangeFrom) {
      newErrors.rangeFrom = '"Range From" is required.';
      valid = false;
    }
    if (!formData.rangeTo) {
      newErrors.rangeTo = '"Range To" is required.';
      valid = false;
    }
    if (!formData.extraPercent) {
      newErrors.extraPercent = '"Extra %" is required.';
      valid = false;
    }
    if (!formData.effectiveFrom) {
      newErrors.effectiveFrom = '"Effective From" date is required.';
      valid = false;
    }
    if (!formData.effectiveTo) {
      newErrors.effectiveTo = '"Effective To" date is required.';
      valid = false;
    }

    // Range Validation for First Set
    if (formData.rangeFrom && formData.rangeTo) {
      if (Number(formData.rangeFrom) >= Number(formData.rangeTo)) {
        newErrors.rangeFrom = '"Range From" should be less than "Range To".';
        newErrors.rangeTo = '"Range To" should be greater than "Range From".';
        valid = false;
      }
    }

    // Date Validation for First Set
    if (formData.effectiveFrom && formData.effectiveTo) {
      if (!dayjs(formData.effectiveFrom).isBefore(dayjs(formData.effectiveTo))) {
        newErrors.effectiveFrom = '"Effective From" should be before "Effective To".';
        newErrors.effectiveTo = '"Effective To" should be after "Effective From".';
        valid = false;
      }
    }

    // Second Set Conditional Validation
    const secondSetFilled =
      formData.rangeFrom2 ||
      formData.rangeTo2 ||
      formData.extraPercent2 ||
      formData.effectiveFrom2 ||
      formData.effectiveTo2;

    if (secondSetFilled) {
      // All fields in the second set become required
      if (!formData.rangeFrom2) {
        newErrors.rangeFrom2 = '"Range From 2" is required.';
        valid = false;
      }
      if (!formData.rangeTo2) {
        newErrors.rangeTo2 = '"Range To 2" is required.';
        valid = false;
      }
      if (!formData.extraPercent2) {
        newErrors.extraPercent2 = '"Extra % 2" is required.';
        valid = false;
      }
      if (!formData.effectiveFrom2) {
        newErrors.effectiveFrom2 = '"Effective From 2" date is required.';
        valid = false;
      }
      if (!formData.effectiveTo2) {
        newErrors.effectiveTo2 = '"Effective To 2" date is required.';
        valid = false;
      }

      // Range Validation for Second Set
      if (formData.rangeFrom2 && formData.rangeTo2) {
        if (Number(formData.rangeFrom2) >= Number(formData.rangeTo2)) {
          newErrors.rangeFrom2 = '"Range From 2" should be less than "Range To 2".';
          newErrors.rangeTo2 = '"Range To 2" should be greater than "Range From 2".';
          valid = false;
        }
      }

      // Date Validation for Second Set
      if (formData.effectiveFrom2 && formData.effectiveTo2) {
        if (!dayjs(formData.effectiveFrom2).isBefore(dayjs(formData.effectiveTo2))) {
          newErrors.effectiveFrom2 = '"Effective From 2" should be before "Effective To 2".';
          newErrors.effectiveTo2 = '"Effective To 2" should be after "Effective From 2".';
          valid = false;
        }
      }
    }

    if (!selectedOption) {
      newErrors.selectedOption = 'Please select an option.'; // Set error message
      valid = false; // Mark the form as invalid
    }

    setErrors(newErrors);
    return valid;
  };

  const handleSubmit = async () => {

    if (!existingId) {
      console.error("ID is undefined. Cannot update commission rates.");
      toast.error("Cannot update commission rates. ID is undefined.");
      return;
    }

    if (!validateForm()) {
      return; // Prevent submission if validation fails
    }

    // Convert the form data to the correct format for the backend
    const effectiveFrom = formData.effectiveFrom
      ? dayjs(formData.effectiveFrom).format('YYYY-MM-DD')
      : null;
    const effectiveTo = formData.effectiveTo
      ? dayjs(formData.effectiveTo).format('YYYY-MM-DD')
      : null;
    const effectiveFrom2 = formData.effectiveFrom2
      ? dayjs(formData.effectiveFrom2).format('YYYY-MM-DD')
      : null;
    const effectiveTo2 = formData.effectiveTo2
      ? dayjs(formData.effectiveTo2).format('YYYY-MM-DD')
      : null;

    const commissionRateData = {
      range_from: formData.rangeFrom,
      range_to: formData.rangeTo,
      extra_percentage: formData.extraPercent,
      effective_from: effectiveFrom,
      effective_to: effectiveTo,
      range_from2: formData.rangeFrom2,
      range_to2: formData.rangeTo2,
      extra_percentage2: formData.extraPercent2,
      effective_from2: effectiveFrom2,
      effective_to2: effectiveTo2,
      calculated_on: selectedOption, // Ensure selectedOption is defined
    };


    try {
      // Dispatch the update action with both ID and data
      await dispatch(updateCommissionRate({ id: existingId, updateData: commissionRateData }));

      // Dispatch the action to mark the row as saved
      dispatch(markRowAsSaved(existingId));

      // Notify the parent about the successful save
      onSaveSuccess(); // This is a callback function passed from the parent
      onClose(); // Close the dialog on success

      toast.success("Commission rate saved successfully.");
    } catch (error) {
      console.error("Error saving commission rates:", error);
      // Optionally, set a general error message here
      toast.error("Failed to save commission rate. Please try again.");
    }
  };

  const fieldsDisabled = !selectedOption; // Disable fields if no radio button is selected


  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle>
          <Grid container alignItems="center">
            <Grid item xs>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: '#f5f5f5',
                  padding: '10px',
                  borderRadius: '5px',
                  border: '1px solid #ccc',
                }}
              >
                <span style={{ marginRight: 'auto' }}>Add Extra%</span>
                <RadioGroup row value={selectedOption} onChange={handleOptionChange}>
                  <FormControlLabel value="sum_insured" control={<Radio />} label="Sum Insured" />
                  <FormControlLabel value="premium" control={<Radio />} label="Premium" />
                </RadioGroup>
                {errors.selectedOption && ( // Display error message for radio buttons
                  <Typography variant="caption" color="error">
                    {errors.selectedOption}
                  </Typography>
                )}
              </div>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent style={{ overflowY: 'auto' }}>
          <Grid container spacing={2} style={{ marginBottom: '20px', marginTop: '3px' }}>
            {/* First Set of Fields */}
            <Grid item xs={12} sm={6} md={3}>
              <DropDown
                label="Range From"
                name="rangeFrom"
                options={generateOptions(50000, 1000000, 50000)}
                value={formData.rangeFrom}
                onChange={(e) => handleInputChange('rangeFrom', e.target.value)}
                fullWidth
                required
                error={Boolean(errors.rangeFrom)}
                helperText={errors.rangeFrom}
                disabled={fieldsDisabled} // Disable based on radio selection

              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <DropDown
                label="Range To"
                name="rangeTo"
                options={generateOptions(50000, 1000000, 50000)}
                value={formData.rangeTo}
                onChange={(e) => handleInputChange('rangeTo', e.target.value)}
                fullWidth
                required
                error={Boolean(errors.rangeTo)}
                helperText={errors.rangeTo}
                disabled={fieldsDisabled} // Disable based on radio selection

              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <CustomTextField
                label="Extra %"
                value={formData.extraPercent}
                onChange={(e) => handleInputChange('extraPercent', e.target.value)}
                fullWidth
                isRequired
                error={Boolean(errors.extraPercent)}
                helperText={errors.extraPercent}
                disabled={fieldsDisabled}

              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="Effective From"
                value={formData.effectiveFrom}
                disabled={fieldsDisabled} // Disable based on radio selection

                onChange={(newValue) => handleInputChange('effectiveFrom', newValue)}
                renderInput={(params) => (
                  <TextField
                  {...params}
        fullWidth
        required
        error={Boolean(errors.effectiveFrom)}
        helperText={errors.effectiveFrom || "Required field"} // Show required text
        InputProps={{
          style: {
            border: errors.effectiveFrom ? '1px solid red' : '1px solid #ccc', // Red border if there's an error
          },
        }}
                  />
                )}
                format="DD/MM/YYYY" // Ensure the date format is dd/MM/yyyy
                className="date-picker"
                PopperProps={{
                  modifiers: [
                    {
                      name: 'offset',
                      options: {
                        offset: [0, 10],
                      },
                    },
                  ],
                }}
                calendarClassName="custom-calendar"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="Effective To"
                value={formData.effectiveTo}
                disabled={fieldsDisabled} // Disable based on radio selection
                minDate={formData.effectiveFrom} // Disable dates before 'Effective From'

                onChange={(newValue) => handleInputChange('effectiveTo', newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    required
                    error={Boolean(errors.effectiveTo)}
                    helperText={errors.effectiveTo}
                    disabled={fieldsDisabled}

                  />
                )}
                format="DD/MM/YYYY"
                className="date-picker"
                PopperProps={{
                  modifiers: [
                    {
                      name: 'offset',
                      options: {
                        offset: [0, 10],
                      },
                    },
                  ],
                }}
                calendarClassName="custom-calendar"
              />
            </Grid>
          </Grid>

          {/* Second Set of Fields */}
          {formData.showSecondSet && (
            <Grid container spacing={2} style={{ marginBottom: '20px', marginTop: '5px' }}>
              <Grid item xs={12} sm={6} md={3}>
                <DropDown
                  label="Range From"
                  name="rangeFrom2"
                  options={generateOptions(50000, 1000000, 50000)}
                  value={formData.rangeFrom2}
                  onChange={(e) => handleInputChange('rangeFrom2', e.target.value)}
                  disabled={fieldsDisabled} // Disable based on radio selection
                  fullWidth
                  required={Boolean(
                    formData.rangeFrom2 ||
                    formData.rangeTo2 ||
                    formData.extraPercent2 ||
                    formData.effectiveFrom2 ||
                    formData.effectiveTo2
                  )}
                  error={Boolean(errors.rangeFrom2)}
                  helperText={errors.rangeFrom2}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      position: 'relative',
                      '&:before': {
                        content: '""',
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '3px',
                        backgroundColor: 'red',
                      },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DropDown
                  label="Range To"
                  name="rangeTo2"
                  options={generateOptions(50000, 1000000, 50000)}
                  value={formData.rangeTo2}
                  onChange={(e) => handleInputChange('rangeTo2', e.target.value)}
                  disabled={fieldsDisabled} // Disable based on radio selection
                  fullWidth
                  required={Boolean(
                    formData.rangeFrom2 ||
                    formData.rangeTo2 ||
                    formData.extraPercent2 ||
                    formData.effectiveFrom2 ||
                    formData.effectiveTo2
                  )}
                  error={Boolean(errors.rangeTo2)}
                  helperText={errors.rangeTo2}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <CustomTextField
                  label="Extra %"
                  value={formData.extraPercent2}
                  onChange={(e) => handleInputChange('extraPercent2', e.target.value)}
                  fullWidth
                  disabled={fieldsDisabled} // Disable based on radio selection
                  isRequired
                  required={Boolean(
                    formData.rangeFrom2 ||
                    formData.rangeTo2 ||
                    formData.extraPercent2 ||
                    formData.effectiveFrom2 ||
                    formData.effectiveTo2
                  )}
                  error={Boolean(errors.extraPercent2)}
                  helperText={errors.extraPercent2}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Effective From "
                  value={formData.effectiveFrom2}
                  onChange={(newValue) => handleInputChange('effectiveFrom2', newValue)}
                  disabled={fieldsDisabled} // Disable based on radio selection

                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      required={Boolean(
                        formData.rangeFrom2 ||
                        formData.rangeTo2 ||
                        formData.extraPercent2 ||
                        formData.effectiveFrom2 ||
                        formData.effectiveTo2
                      )}
                      error={Boolean(errors.effectiveFrom2)}
                      helperText={errors.effectiveFrom2}
                    />
                  )}
                  format="DD/MM/YYYY"
                  className="date-picker"
                  PopperProps={{
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 10],
                        },
                      },
                    ],
                  }}
                  calendarClassName="custom-calendar"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Effective To"
                  value={formData.effectiveTo2}
                  disabled={fieldsDisabled} // Disable based on radio selection
                  minDate={formData.effectiveFrom2} // Disable dates before 'Effective From'
                  onChange={(newValue) => handleInputChange('effectiveTo2', newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      required={Boolean(
                        formData.rangeFrom2 ||
                        formData.rangeTo2 ||
                        formData.extraPercent2 ||
                        formData.effectiveFrom2 ||
                        formData.effectiveTo2
                      )}
                      error={Boolean(errors.effectiveTo2)}
                      helperText={errors.effectiveTo2}
                      disabled={fieldsDisabled} // Disable based on radio selection

                    />
                  )}
                  format="DD/MM/YYYY"
                  className="date-picker"
                  PopperProps={{
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, 10],
                        },
                      },
                    ],
                  }}
                  calendarClassName="custom-calendar"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          {!formData.showSecondSet ? (
            <Button onClick={handleAddMore} color="primary" variant="contained">
              Add More
            </Button>
          ) : (
            <Button
              onClick={handleDeleteSecondSet}
              color="secondary"
              variant="outlined"
              startIcon={<DeleteIcon />}
            >
              Delete
            </Button>
          )}
          <Button onClick={handleSubmit} color="primary" variant="contained">
            Save
          </Button>
          <Button onClick={onClose} color="secondary" variant="outlined">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default Popup;
