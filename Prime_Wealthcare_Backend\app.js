const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const { checkAndCreateDatabase } = require('./database'); // Import the function
const { sendErrorEmail } = require('./services/emailService'); // Import the error email function
const path = require('path');
const fs = require('fs');
const { initCronJobs } = require('./services/cronService'); // Import the cron service

const locationsRoutes = require('./Admin/Master/Area_Management/Routes/locationsRoutes');
const subAreasRoutes = require('./Admin/Master/Area_Management/Routes/areasRoutes');
const roleRoutes = require('./Admin/Master/Role_Management/Routes/roleRoute');
const mainProductRoutes = require('./Admin/Master/Main_Product/Routes/mainProductRoute');
const insuranceBranchRoutes = require('./Admin/Master/Insurance_Branch/Routes/insuranceBranchRoutes')
const insuranceCompanyRoutes = require('./Admin/Master/Insurance_Company/Routes/insuranceCompanyRoutes')
const imfAgencyCodeRoutes = require('./Admin/Master/IMF_Agency_Code/Routes/imfAgencyCodeRoutes')
const insuranceTypeRoutes = require('./Admin/Master/Insurance_Type/Routes/insuranceTypeRoutes')
const productMasterRoutes = require('./Admin/Master/Product_Master/Routes/productMasterRoutes')
const imfBranchRoutes = require('./Admin/Master/Imf_Branch/Routes/imfBranchRoutes')
const subProductRoutes = require('./Admin/Master/Sub_Product/Routes/subProductRoutes');
const subProductRiderRoutes = require('./Admin/Master/Sub_Product/Routes/subProductRiderRoutes')
const subProductAgeSumRoutes = require('./Admin/Master/Sub_Product/Routes/subProductAgeSumRoutes')
const commissionRateRoutes = require('./Admin/Master/Commission_Rate/Routes/commissionRateRoutes')
const pickListRoutes = require('./Admin/Master/Pick_List/Routes/pickListRoutes')
const PA_occupation_listRoutes = require('./Admin/Master/PA_occupation_list/Routes/PA_occupation_ListRoutes')
const endorsmentRoutes = require('./Admin/Master/Endorsment_Type/Routes/endorsmentTypeRoutes')
const diseaseMasterRoutes = require('./Admin/Master/Disease_Master/Routes/diseaseMasterRoutes')
const networkRoutes = require('./Admin/Master/Network/Routes/networkRoute')
const employeeRoutes = require('./User/Employee_personal_info/Routes/employee_Persanol_Info_Routes')

const agentAddressRoutes = require('./Admin/Agent/Routes/agentAddressRoutes')
const agentDetailsRoutes = require('./Admin/Agent/Routes/agentDetailsRoutes');

const customerRoutes = require('./Admin/Customer/Routes/customer_personal_info_routes');
const customerMemberRoutes = require('./Admin/Customer/Routes/customer_member_info_reote');
const customerGrouping = require('./Admin/Customer/Routes/customer_grouping_routes');
const customerAddress = require('./Admin/Customer/Routes/customer_address_route');
const customerDocumentation = require('./Admin/Customer/Routes/customer_documentation_upload');
const authRoutes = require('./Login/Routes/authRoutes');
const reusableRoutes = require('./Reusable/Routes/reusableRoute');

const healthTotalRouter = require('./Admin/QuickQuotation/HealthTotal/healthTotalRoute')
const advantageTopupRouter = require('./Admin/QuickQuotation/AdvantageTopup/advantageTopupRoute');
const healthAbsoluteRoute = require('./Admin/QuickQuotation/HealthAbsolute/healthAbsoluteRoute');
const healthSuraksha = require('./Admin/QuickQuotation/Health Suraksha/healthSuraksha');
const varishtaBima = require('./Admin/QuickQuotation/VarishtaBima/varishtaBimaRoute')
const accidentSuraksha = require('./Admin/QuickQuotation/AccidentSuraksha/accidentSurakshaRoute');

const quotationRoute = require('./Admin/Quotations/Routes/quotation_routes');
const quotationMemberRoute = require('./Admin/Quotations/Routes/quotation_member_routes')
const employeeAddressRoutes = require('./User/Employee_personal_info/Routes/employee_address_route')
const employeeSalary = require('./User/Employee_personal_info/Routes/employee_salary_route')

const proposalRoutes = require('./Admin/Proposal/Routes/proposalRoutes');
const paymentRoutes = require('./Admin/Payment/Routes/paymentRoutes');

const PA_quotationRoute = require('./Admin/Quotations/Routes/PA_quotation_routes');
const pageRightsRoutes = require('./Admin/PageRights/Routes/pageRightsRoutes');
const agentBankRoutes = require('./Admin/Agent/Routes/agentBankDetailsRoutes');
const employeeBankRoutes = require('./User/Employee_personal_info/Routes/employee_bank_details_route')
const bankList = require('./Admin/Master/Bank_List/Route/bankListRoute');
const agentLoanRoutes = require('./Admin/Loan/Routes/agentLoanRoutes');
const employeeLoanRoutes = require('./Admin/Loan/Routes/employeeLoanRoutes');
const TaskRoute = require('./Admin/Tasks/Routes/task_route');
const { router: uploadRouter } = require('./Admin/Agent/Middleware/upload');
const rolloverMigrationRoutes = require('./Admin/Proposal/Routes/rolloverMigrationRoutes');

const dashboard = require('./Admin/Dashboard/Routes/dashboardRoutes');
const reportRoutes = require('./Admin/Reports/Routes/reportRoutes');

const renewalsMapping = require('./Admin/Renewals/Routes/Renewal_Route');

dotenv.config();

const app = express();
const host = process.env.HOST;
const port = process.env.PORT;
const corsOriginHost = process.env.CORS_ORIGIN_HOST;
const corsOriginPort = process.env.CORS_ORIGIN_PORT;
const corsOrigin = corsOriginPort ? `${corsOriginHost}:${corsOriginPort}` : corsOriginHost;

app.use(express.json());
app.use(cors({ credentials: true, origin: corsOrigin }));

// app.use(uploadRouter);
app.get('/', (req, res) => {
  res.send('API is working!');
});
app.use('/locations', locationsRoutes);
app.use('/areas', subAreasRoutes);
app.use('/roles', roleRoutes);
app.use('/main-product', mainProductRoutes)
app.use('/insurance-companies', insuranceCompanyRoutes);
app.use('/insurance-company-branches', insuranceBranchRoutes)
app.use('/insurance-types', insuranceTypeRoutes);
app.use('/imf-agency-code', imfAgencyCodeRoutes)
app.use('/product-master', productMasterRoutes)
app.use('/imf-branch', imfBranchRoutes)
app.use('/sub-product', subProductRoutes);
app.use('/sub-product-age-sum', subProductAgeSumRoutes);
app.use('/sub-product-rider-details', subProductRiderRoutes);
app.use('/commission-rates', commissionRateRoutes);
app.use('/pick-list', pickListRoutes);
app.use('/PA-occupation-list', PA_occupation_listRoutes);
app.use('/endorsment-type', endorsmentRoutes);
app.use('/disease-master', diseaseMasterRoutes);
app.use('/network', networkRoutes);
app.use('/agent-address', agentAddressRoutes);
app.use('/agent-details', agentDetailsRoutes);

app.use('/employee_info', employeeRoutes);
app.use('/employee_address', employeeAddressRoutes);
app.use('/employee_salary', employeeSalary);
app.use('/customer_info', customerRoutes);
app.use('/customer_member_info', customerMemberRoutes);

app.use('/customer_grouping', customerGrouping);
app.use('/customer_address', customerAddress);
app.use('/customer_documentation', customerDocumentation);
app.use('/quick_quotations_health_total', healthTotalRouter);
app.use('/quick_quotations_advantage_topup', advantageTopupRouter);
app.use('/quick_quotations_health_absolute', healthAbsoluteRoute);
app.use('/quick_quotations_health_suraksha', healthSuraksha)
app.use('/quick_quotations_varishta_bima', varishtaBima)
app.use('/quick_quotations_accident_suraksha', accidentSuraksha)

app.use('/reusable', reusableRoutes);
app.use('/auth', authRoutes); // Add this line to handle login/change password routes

app.use('/quotations', quotationRoute);
app.use('/quotation_member', quotationMemberRoute)

app.use('/proposals', proposalRoutes);
app.use('/payment', paymentRoutes);

app.use('/pa_quotation', PA_quotationRoute);
app.use('/page-rights', pageRightsRoutes);

app.use('/agent-bank', agentBankRoutes);
app.use('/employee_bank', employeeBankRoutes);
app.use('/bank_list', bankList)

app.use('/agent_loan', agentLoanRoutes);
app.use('/employee_loan', employeeLoanRoutes);
app.use('/task', TaskRoute);
app.use('/rollover-migrations', rolloverMigrationRoutes)

app.use('/dashboard_charts', dashboard);
app.use('/reports', reportRoutes);

app.use('/renewals_mapping', renewalsMapping);
// Example route that could throw an error
app.get('/error', (req, res) => {
  throw new Error('This is a test error'); // This error will be caught by the error handler below
});

// Global error handling middleware
app.use(async (err, req, res, next) => {
  console.error('Global Error Handler:', err.message); // Log the error for debugging

  // Send email notification for the error
  try {
    await sendErrorEmail(err); // Call the email service to notify about the error
  } catch (emailError) {
    console.error('Failed to send error notification email:', emailError);
  }

  // Send a generic error response to the client
  res.status(500).json({ message: 'Something went wrong. The error has been reported.' });
});


app.listen(port, async () => {
  await checkAndCreateDatabase(); // Call the function here
  initCronJobs(); // Initialize cron jobs
  console.log(`Server is running on port ${port}`);
});
