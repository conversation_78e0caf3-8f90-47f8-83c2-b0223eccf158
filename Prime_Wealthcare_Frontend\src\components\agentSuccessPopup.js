import React, { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>, <PERSON>alogActions, <PERSON>alogContent, <PERSON>alogTitle,
    Button, Grid
} from '@mui/material';
import { getAllAgentDetails, transferBusiness, updateAgentDetails } from '../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import Dropdown from './table/DropDown';

const SuccessPopup = ({ open, onClose, modulename, deleted, deletedAgentId }) => {
    const dispatch = useDispatch();
    const agents = useSelector(state => state.agentReducer.agents);
    const [selectedAgent, setSelectedAgent] = useState(null);
    const [error, setError] = useState(false);

    useEffect(() => {
        if (open) {
            dispatch(getAllAgentDetails());
            setSelectedAgent(null); // reset on open
            setError(false);
        }
    }, [dispatch, open]);

    const agentOptions = agents
        .filter(agent => agent.status === 1)
        .map(agent => ({
            value: agent.id,
            label: `${agent.agent_id} (${agent.full_name})`
        }));

    const handleConfirm = async () => {
        if (!selectedAgent) {
            setError(true);
            return;
        }
        try {
            dispatch(transferBusiness({ id: deletedAgentId, agent_id: selectedAgent }));
            //  await updateAgentDetails({ id: deletedAgentId, formData: selectedAgent }); // send to backend
            onClose(selectedAgent); // optionally close the dialog with selected agent
        } catch (err) {
            console.error("Business transfer failed:", err);
            // Optionally show error message to the user
        }
        // You can pass the selected agent back here
        onClose(selectedAgent);
    };

    const handleClose = (event, reason) => {
        // Prevent closing via backdrop or escape if no agent is selected
        if (!selectedAgent && (reason === 'backdropClick' || reason === 'escapeKeyDown')) {
            return;
        }
        onClose();
    };

    return (
        <Dialog open={open} onClose={handleClose}>
            <DialogTitle>{modulename} {deleted || 'deactivated'} Successfully!</DialogTitle>
            <DialogContent>
                <p style={{ paddingBottom: 10 }}>Select the Agent to transfer the business</p>
                <Grid item xs={12}>
                    <Dropdown
                        label="Select Agent"
                        name="agent_id"
                        value={selectedAgent}
                        onChange={(e) => {
                            setSelectedAgent(e.target.value);
                            setError(false);
                        }}
                        options={agentOptions}
                        error={error}
                        helperText={error ? 'Agent selection is required' : ''}
                        fullWidth
                    />
                </Grid>
            </DialogContent>
            <DialogActions style={{ justifyContent: 'center', height: '100px' }}>
                <Button
                    onClick={handleConfirm}
                    color="primary"
                    sx={{
                        backgroundColor: '#528A7E',
                        color: 'white',
                        '&:hover': { backgroundColor: '#3f6e5e' }
                    }}
                >
                    Confirm
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default SuccessPopup;
