const express = require('express');
const AgentBankDetailsController = require('../Controllers/agentBankDetailsController');
const router = express.Router();

// Route to create a new Agent info
router.post('/', AgentBankDetailsController.createAgentBankDetails);

router.get('/agent_id/:id', AgentBankDetailsController.getAgentBankDetailsByAgentId);

router.get('/', AgentBankDetailsController.getAgentBankDetails);

router.get('/:id', AgentBankDetailsController.getAgentBankDetailsById);

router.put('/:id', AgentBankDetailsController.updateAgentBankDetails);

router.delete('/:id', AgentBankDetailsController.deleteAgentBankDetails);

router.delete('/first_bank/:id', AgentBankDetailsController.deleteFirstBankById);

router.delete('/second_bank/:id', AgentBankDetailsController.deleteSecondBankById);

module.exports = router;
