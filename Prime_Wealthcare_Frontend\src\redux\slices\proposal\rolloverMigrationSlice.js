import { createSlice } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import { 
  createRolloverMigration,
  getRolloverMigrationById,
  getAllRolloverMigrations,
  updateRolloverMigration
} from '../../actions/action';

const initialState = {
  loading: false,
  rolloverMigration: null,
  error: null,
  rolloverMigrationList: []
};

const rolloverMigrationSlice = createSlice({
  name: 'rolloverMigration',
  initialState,
  reducers: {
    clearRolloverMigrationData: (state) => {
      state.rolloverMigration = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Create Rollover Migration
      .addCase(createRolloverMigration.pending, (state) => {
        state.loading = true;
      })
      .addCase(createRolloverMigration.fulfilled, (state, action) => {
        state.loading = false;
        //toast.success('Rollover migration created successfully');
      })
      .addCase(createRolloverMigration.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
        //toast.error('Failed to create rollover migration');
      })
      
      // Get Rollover Migration by ID
      .addCase(getRolloverMigrationById.pending, (state) => {
        state.loading = true;
      })
      .addCase(getRolloverMigrationById.fulfilled, (state, action) => {
        state.rolloverMigration = action.payload.data;
        state.loading = false;
      })
      .addCase(getRolloverMigrationById.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
        toast.error('Failed to fetch rollover migration');
      })
      
      // Get All Rollover Migrations
      .addCase(getAllRolloverMigrations.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllRolloverMigrations.fulfilled, (state, action) => {
        state.rolloverMigrationList = action.payload.data;
        state.loading = false;
      })
      .addCase(getAllRolloverMigrations.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
        toast.error('Failed to fetch rollover migrations');
      })
      
      // Update Rollover Migration
      .addCase(updateRolloverMigration.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateRolloverMigration.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(updateRolloverMigration.rejected, (state, action) => {
        state.error = action.payload;
        state.loading = false;
      })
  }
});

export const { clearRolloverMigrationData } = rolloverMigrationSlice.actions;

export default rolloverMigrationSlice.reducer;