const Task = require('../Models/task_model');

// create new task
exports.createTask = async (req, res) => {
    try {
        const task = await Task.create(req.body);
        res.status(201).json({ message: "Task created successfully", task });
    } catch (error) {
        res.status(500).json({ message: "Error creating task", error });
    }
}

// get all tasks
exports.getAllTasks = async (req, res) => {
    try {
        const tasks = await Task.getAll();
        //.sort({ createdAt: -1 });
        res.status(200).json(tasks);
    } catch (error) {
        res.status(500).json({ message: "Error fetching tasks", error });
    }
}
// get by id 
exports.getTaskById = async (req, res) => {
    try {
        const taskId = req.params.id;
        const task = await Task.getById(taskId);
        res.status(200).json(task);
    } catch (error) {
        res.status(404).json({ message: "Task not found", error });
    }
}
//find by AssignedTo
exports.getTaskByAssignedTo = async (req, res) => {
    try {
        const assignedTo = req.params.id; // Make sure this matches your route parameter name
        const task = await Task.getByAssignedTo(assignedTo); // Pass as a direct argument
        res.status(200).json(task);
    } catch (error) {
        res.status(404).json({ message: "Task not found", error });
    }
};
// get by assigned by
exports.getTaskByAssignedBy = async (req, res) => {
    try {
        const assignedBy = req.params.id;
        const task = await Task.getByAssignedBy(assignedBy);
        res.status(200).json(task);
    } catch (error) {
        res.status(404).json({ message: "Task not found", error });
    }
}

//update
exports.updateTask = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const task = await Task.update(data, id);

        res.status(200).json(task);
    } catch (error) {
        res.status(404).json({ message: "Task not found", error });
    }
}

// delete by id 
exports.deleteTaskById = async (req, res) => {
    try {
        const taskId = req.params.id;
        await Task.deleteById(taskId);
        res.status(200).json({ message: "Task deleted successfully" });
    } catch (error) {
        res.status(404).json({ message: "Task not found", error });
    }
}


// Comments
exports.createComment = async (req, res) => {
    try {
        const taskId = req.params.id;
        const comment = req.body;
        const task = await Task.addComment(taskId, comment);
        res.status(200).json(task);
    } catch (error) {
        res.status(500).json({ message: "Error creating comment", error });
    }
}

exports.addCommentReply = async (req, res) => {
    try {
        //   const taskId = req.params.id;
        const commentId = req.params.id;
        const reply = req.body;
        const task = await Task.addCommentReply(commentId, reply);
        res.status(200).json(task);
    } catch (error) {
        res.status(500).json({ message: "Error creating comment reply", error });
    }
}

//update task Notifications
exports.updateTaskNotification = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const task = await Task.updateNotification(id, data);
        res.status(200).json(task);
    } catch (error) {
        res.status(500).json({ message: "Error updating task notification", error });
    }
}

//  update Comment Notification
exports.updateCommentNotification = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        // const userId = req.params.userId;
        const task = await Task.updateCommentNotification(id, data);
        res.status(200).json(task);
    } catch (error) {
        res.status(500).json({ message: "Error updating comment notification", error });
    }
}

// get all comment replies by comment id
exports.getAllCommentReplies = async (req, res) => {
    try {
        const taskId = req.params.id;
        const replies = await Task.getAllCommentReplies(taskId);
        res.status(200).json(replies);
    } catch (error) {
        res.status(500).json({ message: "Error fetching comment replies", error });
    }
}

exports.getTaskNotifications = async (req, res) => {
    try {
        const userId = req.params.id;
        const notifications = await Task.getTaskNotifications(userId);
        res.status(200).json(notifications);
    } catch (error) {
        res.status(500).json({ message: "Error fetching task notifications", error });
    }
}

exports.getCommentNotifications = async (req, res) => {
    try {
        const userId = req.params.id;
        const notifications = await Task.getCommentNotifications(userId);
        res.status(200).json(notifications);
    } catch (error) {
        res.status(500).json({ message: "Error fetching comment notifications", error });
    }
}

//get all task notifications
exports.getAllTaskNotifications = async (req, res) => {
    try {
        const notifications = await Task.getAllTaskNotifications();
        res.status(200).json(notifications);
    } catch (error) {
        res.status(500).json({ message: "Error fetching task notifications", error });
    }
}
//get all comment notifications
exports.getAllCommentNotifications = async (req, res) => {
    try {
        const notifications = await Task.getAllCommentNotifications();
        res.status(200).json(notifications);
    } catch (error) {
        res.status(500).json({ message: "Error fetching comment notifications", error });
    }
}

// get task details and its commennts and replies  by u(created_by)
exports.getTaskDetailsByUserId = async (req, res) => {
    try {
        const id = req.params.userId;
        const tasks = await Task.getTaskDetails(id);
        res.status(200).json(tasks);
    } catch (error) {
        res.status(500).json({ message: "Error fetching task details", error });
    }
}
