const express = require('express');
const router = express.Router();
const reportController = require('../Controllers/reportController');
//const authenticateToken = require('../../../Login/Middleware/authMiddleware');

// Generate report based on filters
router.post('/generate',  reportController.generateReport);

// Get available financial years
router.get('/financial-years', reportController.getFinancialYears);

module.exports = router;