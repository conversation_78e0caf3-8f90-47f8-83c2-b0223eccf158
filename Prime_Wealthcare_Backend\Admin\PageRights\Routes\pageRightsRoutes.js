const express = require('express');
const router = express.Router();
const { createPageRights, getRoleRights, checkUserAccess, getUserAccessRights, getAllUsers } = require('../Controllers/pageRightsController');
const authenticateToken = require('../../../Login/Middleware/authMiddleware');  // Updated path

// Define routes
router.post('/create', authenticateToken, createPageRights);
router.get('/role/:role_id', authenticateToken, getRoleRights);
router.post('/check-access', authenticateToken, checkUserAccess);
router.get('/user/:user_id', authenticateToken, getUserAccessRights); // New route
router.get('/users', authenticateToken, getAllUsers);

module.exports = router;