const express = require('express');
const router = express.Router();
const { sendSOAPRequest } = require('../VarishtaBima/varishtaBimaSoapService');
const { v4: uuidv4 } = require('uuid');

const calculateDOBFromAgeBand = (ageBand) => {
    const currentYear = new Date().getFullYear();
    const [minAge, maxAge] = ageBand.split('-').map(Number);
    const averageAge = maxAge ? (minAge + maxAge) / 2 : minAge;
    const birthYear = currentYear - averageAge;
    return new Date(birthYear, 0, 1).toISOString().split('T')[0];
};

const formatDateToDDMMYYYY = (date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};

const COVER_TYPE_SUM_INSURED = {
    'VARISHTA BIMA': [500000, 750000, 1000000],
  };



// Add this helper function at the top
const calculatePolicyDates = (duration) => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // Start from tomorrow
    
    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + parseInt(duration)); // Add duration years
    endDate.setDate(endDate.getDate() - 1); // Subtract one day
    
    return {
        startDate: formatDateToDDMMYYYY(startDate),
        endDate: formatDateToDDMMYYYY(endDate)
    };
};

const mapRelation = (frontendRelation) => {
    const relationMapping = {
        'SELF': 'SELF',
        'SPOUSE': 'SPOU',
        'SON': 'SON',
        'DAUGHTER': 'DAUG',
        
        // Add more mappings as needed
    };
    return relationMapping[frontendRelation] || frontendRelation;
};

router.post('/healthvarishtabima-floater-options', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration, copay } = req.body;

        // Validate input
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Validate cover type
        if (!COVER_TYPE_SUM_INSURED[cover_type]) {
            console.error(`Invalid cover type: ${cover_type}`);
            throw new Error(`Invalid cover type: ${cover_type}`);
        }

        // Temporary storage for results
        const tempResults = [];

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;
        
        // Iterate through sum insured options
        const sumInsuredOptions = COVER_TYPE_SUM_INSURED[cover_type];

        // Calculate policy dates based on duration
        const policyDates = calculatePolicyDates(duration || '1');

        for (const sumInsured of sumInsuredOptions) {

            // Generate numeric UID
            const numericUid = uuidv4();

            const soapData = {
                Product: normalizedProductName,
                PolicyHeader: {
                    PolicyStartDate: policyDates.startDate,
                    PolicyEndDate: policyDates.endDate,
                    AgentCode: "60000272",
                    BranchCode: "10",
                    MajorClass: "FHA",
                    ContractType: "FHA",
                    METHOD: "ENQ",
                    PolicyIssueType: "I"
                },
                Uid: numericUid,
                VendorCode: "webagg",
                VendorUserId: "webagg",
                Client: {
                    ClientType: "I",
                    CreationType: "C",
                    Address1: {
                        Pincode: pincode,
                        Country: "IND"
                    }
                },
                BeneficiaryDetails: {
                    Member: members.map((member, index) => {
                        const insuredDob = formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand)));
                        return {
                            MemberId: (index + 1).toString(),
                            InsuredName: `Member ${index + 1}`,
                            InsuredDob: insuredDob,
                            InsuredGender: member.gender || 'M',
                            InsuredOccpn: "SVCM",
                            CoverType: cover_type,
                            SumInsured: sumInsured,
                            Relation: mapRelation(member.relation),
                            Height: "170",
                            Weight: "70",
                            NomineeName: "Test Nominee",
                            NomineeRelation: "FATH",
                            NomineeAge: "45"
                        };
                    })
                },
                Risk: {
                    PolicyType: family_type === 'individual' ? 'VBI' : 'VBF',
                    Duration: duration || "1",
                    Installments: "FULL",
                    PaymentType: "CC",
                    CoPay: copay === 'yes' ? 'Y' : 'N'
                }
            };

            try {
                // Call the floater SOAP service
                const soapResponse = await sendSOAPRequest(soapData);
                
                const policyDetails = soapResponse?.policyDetails?.Root?.Policy?.[0];
                const outputRes = policyDetails?.OutputRes?.[0];
                const memberDetails = policyDetails?.InputParameters?.[0]?.BeneficiaryDetails?.[0]?.Member;
        
                // Map member results with correct paths
                const memberResults = memberDetails?.map((member) => ({
                    memberId: member.MemberId?.[0],
                    sumInsured: member.SumInsured?.[0],
                    basePremium: member.BeneBasePremium?.[0], // Updated from PerPrsnPremium
                    perPersonPremium: member.PerPrsnPremium?.[0],
                    coverType: cover_type, // Using the input cover_type
                    relation: member.Relation?.[0],
                })) || [];
        
                // Update output response data extraction
                const outputResponseData = {
                    premiumAmt: outputRes?.PremiumAmt?.[0] || '0',
                    serviceTax: outputRes?.ServiceTax?.[0] || '0',
                    premWithServiceTax: outputRes?.PremWithServTax?.[0] || '0',
                    basePremium: outputRes?.BasePremium?.[0] || '0',
                    familyDiscount: outputRes?.FamilyDiscount?.[0] || '0',
                    coPayAmount: outputRes?.CoPayAmount?.[0] || '0',
                    coPayPercentage: outputRes?.CopayPercentage?.[0] || '0'
                };
                
                // Push the results into tempResults
                tempResults.push({
                    sumInsured: sumInsured,
                    requestId: numericUid,
                    premiums: memberResults,
                    outputResponse: outputResponseData, // Store output response fields here
                    rawResponse: soapResponse
                });
            } catch (soapError) {
                console.error(`Error for sum insured ${sumInsured}:`, soapError);
                tempResults.push({
                    sumInsured: sumInsured,
                    error: soapError.message || 'Unknown error'
                });
            }
        }
        // Respond with the temporary results
        res.json({
            status: 'success',
            coverType: cover_type,
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName, // Added product to the response
                company_name: company_name,
                duration:duration
            },
            results: tempResults // Return all results after processing
        });

    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ error: error.message });
    }
});

router.post('/healthvaristabimacreate', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration, copay } = req.body;

        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Generate numeric UID
        const numericUid = uuidv4();

        // Calculate policy dates based on duration
        const policyDates = calculatePolicyDates(duration || '1');

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
            // Add other mappings as needed
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name; // Default to the original name if not found

        // Map PolicyType based on family_type
        const policyType = family_type === 'individual' ? 'VBI' : 'VBF'; // HTI for individual, HTF for floater

        const soapData = {
            Product: normalizedProductName, // Use the normalized product name
            PolicyHeader: {
                PolicyStartDate: policyDates.startDate,
                PolicyEndDate: policyDates.endDate,
               
            },
            Uid: numericUid,
            VendorCode: "webagg",
            VendorUserId: "webagg",
            Client: {
                ClientType: "I",
                CreationType: "C",
                Address1: {
                    Pincode: pincode,
                    Country: "IND"
                }
            },
            BeneficiaryDetails: {
                Member: members.map((member, index) => ({
                    MemberId: (index + 1).toString(),
                    InsuredName: `Member ${index + 1}`,
                    InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                    InsuredGender: member.gender || 'M',
                    InsuredOccpn: "SVCM",
                    CoverType: cover_type,
                    SumInsured: member.sumInsured,
                    Relation: mapRelation(member.relation),
                    Height: "170",
                    Weight: "70",
                    NomineeName: "Test Nominee",
                    NomineeRelation: "FATH",
                    NomineeAge: "45"
                }))
            },
            Risk: {
                PolicyType: policyType,
                Duration: duration || "1",
                Installments: "FULL",
                PaymentType: "CC",
                CoPay: copay === 'yes' ? 'Y' : 'N'
            }
        };


        const soapResponse = await sendSOAPRequest(soapData);
        // Safely extract data from the SOAP response
        const policyDetails = soapResponse?.policyDetails?.Root?.Policy?.[0];
        const outputRes = policyDetails?.OutputRes?.[0];
        const memberDetails = policyDetails?.InputParameters?.[0]?.BeneficiaryDetails?.[0]?.Member;

        // Map member results with correct paths
        const memberResults = memberDetails?.map((member) => ({
            memberId: member.MemberId?.[0],
            sumInsured: member.SumInsured?.[0],
            basePremium: member.BeneBasePremium?.[0], // Updated from PerPrsnPremium
            perPersonPremium: member.PerPrsnPremium?.[0],
            coverType: cover_type, // Using the input cover_type
            relation: member.Relation?.[0],
        })) || [];

        // Update output response data extraction
        const outputResponseData = {
            premiumAmt: outputRes?.PremiumAmt?.[0] || '0',
            serviceTax: outputRes?.ServiceTax?.[0] || '0',
            premWithServiceTax: outputRes?.PremWithServTax?.[0] || '0',
            basePremium: outputRes?.BasePremium?.[0] || '0',
            familyDiscount: outputRes?.FamilyDiscount?.[0] || '0',
            coPayAmount: outputRes?.CoPayAmount?.[0] || '0',
            coPayPercentage: outputRes?.CopayPercentage?.[0] || '0'
        };
        const formattedResponse = {
            status: 'success',
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName,
                company_name: company_name,
                duration: duration
            },
            results: {
                premiums: memberResults,
                outputResponse: outputResponseData
            },
            rawResponse: soapResponse
        };
        

        res.json(formattedResponse);
    } catch (error) {
        console.error('Full Error Stack:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
