import React, { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom'
import InfoPopup from '../../../components/InfoPopup';
import { Box, Button, Checkbox, FormControlLabel, Grid } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import DropDown from '../../../components/table/DropDown';
import CustomTextField from '../../../components/CustomTextField';
import { toast } from 'react-toastify';
import { createRidersDetails, createSubProduct, createSumInsured, deleteRiderDetails, deleteSumInsured, fetchInsuranceCompanies, getAllMasterProducts, getAllProducts, getAllSubProducts, getMasterProductByMainProductAndInsuranceCompany, getSubProductById, updateRiderDetails, updateSubProduct, updateSumInsured } from '../../../redux/actions/action';
import { trimFormData } from '../../../utils/Reusable';

// Custom hook to detect clicks outside of a component
function useOutsideClick(ref, callback) {
    useEffect(() => {
        function handleClickOutside(event) {
            if (ref.current && !ref.current.contains(event.target)) {
                callback();
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [ref, callback]);
}

function SubProductForm() {
    // Data initialization
    const { id } = useParams() || '';
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const mainProducts = useSelector(state => state.mainProductReducer.products);
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
    const masterProducts = useSelector(state => state.productMasterReducer.products);
    const subProduct = useSelector(state => state.subProductReducer.subProduct);
    const sumInsured = useSelector(state => state.subProductReducer.sumInsured);
    const riderDetails = useSelector(state => state.subProductReducer.riderDetails);
    const initialFormData = {
        main_product_id: '',
        insurance_company_id: '',
        product_master_id: '',
        sub_product_name: '',
        co_pay: '0',
        child_separation_age: '',
        pre_hospitalization_days: '',
        post_hospitalization_days: '',
    };
    const [formData, setFormData] = useState(initialFormData);
    const [openInfoPopup, setOpenInfoPopup] = useState(false);
    const [infoPopupMessage, setInfoPopupMessage] = useState('');
    const [validationErrors, setValidationErrors] = useState({});
    const [isSubProductAdded, setIsSubProductAdded] = useState(false); // Track if a sub-product is added
    const [subProductNameFocus, setSubProductNameFocus] = useState(false);
    const [sectionsVisibility, setSectionsVisibility] = useState({
        hospitalizationDays: false,
        sumInsuredCriteria: false,
        riderDetails: false
    })
    const [sumInsuredRows, setSumInsuredRows] = useState([{
        min_age: '',
        max_age: '',
        sum_insured: ''
    }]);
    const [riderRows, setRiderRows] = useState([{
        rider_details: '',
    }])
    const [currentSection, setCurrentSection] = useState('SubProduct');
    const [isEditingHospitalization, setIsEditingHospitalization] = useState();
    const [sumInsuredEditIndex, setSumInsuredEditIndex] = useState(null);
    const [editRiderIndex, setEditRiderIndex] = useState(null);
    const [saveAndNew, setSaveAndNew] = useState(false);
    const sumInsuredRef = useRef(null);

    // Use Effects
    useEffect(() => {
        dispatch(getAllProducts());
        dispatch(fetchInsuranceCompanies());
        if (id) {
            dispatch(getSubProductById(id));
        } else {
            setFormData(initialFormData);
            setSectionsVisibility({
                hospitalizationDays: false,
                sumInsuredCriteria: false,
                riderDetails: false
            });
        }
    }, [id, dispatch])

    useEffect(() => {
        if (subProduct && id) {
            setFormData({
                main_product_id: subProduct.main_product_id || '',
                insurance_company_id: subProduct.insurance_company_id || '',
                product_master_id: subProduct.product_master_id || '',
                sub_product_name: subProduct.sub_product_name || '',
                co_pay: subProduct.co_pay === null ? null : subProduct.co_pay === 0 ? 0 : subProduct.co_pay || '',
                child_separation_age: subProduct.child_separation_age || '',
                pre_hospitalization_days: subProduct.pre_hospitalization_days || '',
                post_hospitalization_days: subProduct.post_hospitalization_days || '',
            });
            if (subProduct.pre_hospitalization_days && subProduct.post_hospitalization_days) {
                setIsEditingHospitalization(false);
            }
            if (!subProduct.pre_hospitalization_days && !subProduct.post_hospitalization_days) {
                setIsEditingHospitalization(true);
            }
            // Set section visibility based on existing data
            setSectionsVisibility({
                hospitalizationDays: (subProduct.pre_hospitalization_days || subProduct.post_hospitalization_days),
                sumInsuredCriteria: (sumInsured[0]?.min_age || sumInsured[0]?.max_age || sumInsured[0]?.sum_insured || sumInsuredRows[0].min_age !== '' || sumInsuredRows[0].max_age !== '' || sumInsuredRows[0].sum_insured !== ''),
                riderDetails: (riderDetails[0]?.rider_details || riderRows[0].rider_details !== ''),
            });
        }
    }, [subProduct, id]);

    useEffect(() => {
        // Ensure at least one row for sub_product_rider_details
        let initialRiderRows = riderDetails && riderDetails.length > 0
            ? riderDetails
            : [{ rider_details: '' }];
        if (riderDetails && riderDetails.length > 0 && !(subProduct?.status === 0)) {
            initialRiderRows = [...initialRiderRows, { rider_details: '' }];
        }
        if (riderRows[riderRows.length - 1].rider_details !== '') {
            return;
        }
        setRiderRows(initialRiderRows);
    }, [riderDetails, id])

    useEffect(() => {
        if (formData.main_product_id && formData.insurance_company_id) {
            dispatch(getMasterProductByMainProductAndInsuranceCompany({ mainProductId: formData.main_product_id, insuranceCompanyId: formData.insurance_company_id }));
        }
    }, [formData.main_product_id, formData.insurance_company_id]);

    useEffect(() => {
        // Ensure at least one row for sub_product_age_sum
        let initialSumInsuredRows = sumInsured && sumInsured.length > 0
            ? sumInsured
            : [{ min_age: '', max_age: '', sum_insured: '' }];
        if (sumInsured && sumInsured.length > 0 && !(subProduct?.status === 0)) {
            initialSumInsuredRows = [...initialSumInsuredRows, { min_age: '', max_age: '', sum_insured: '' }];
        }
        if (sumInsuredRows[sumInsuredRows.length - 1].min_age !== '' || sumInsuredRows[sumInsuredRows.length - 1].max_age !== '' || sumInsuredRows[sumInsuredRows.length - 1].sum_insured !== '') {
            return;
        }
        setSumInsuredRows(initialSumInsuredRows);
    }, [sumInsured, id])

    useOutsideClick(sumInsuredRef, () => {
        if (sumInsuredEditIndex !== null && sumInsuredRows[sumInsuredEditIndex]) {
            const value = sumInsuredRows[sumInsuredEditIndex].sum_insured;
            const roundedValue = Math.round(value / 50000) * 50000;
            handleSumInsuredChange(sumInsuredEditIndex, 'sum_insured', roundedValue);
        }
    });

    // Reusable Functions 
    const generateOptions = (start, end, step) => {
        let options = [];
        for (let i = start; i <= end; i += step) {
            options.push({ label: i.toString(), value: i });
        }
        return options;
    };
    const isSubProductInformationFilled = () => {
        const flag = !!formData.main_product_id && !!formData.insurance_company_id && !!formData.product_master_id && !!formData.sub_product_name && !!formData.child_separation_age;
        return flag;
    }
    // Handling Internal Changes
    const handleCheckboxChange = (section) => {
        if (isSubProductInformationFilled()) {
            if (!id) {
                setInfoPopupMessage('This will save the sub product information')
                setOpenInfoPopup(true);
            }
            setSectionsVisibility(prevData => ({
                ...prevData,
                [section]: !prevData[section]
            }));
        } else {
            setInfoPopupMessage('Please fill in all required fields before proceeding.');
            setOpenInfoPopup(true);
        }
    }
    const handleCloseInfoPopup = () => {
        setOpenInfoPopup(false);
    }
    const handleSumInsuredEditClick = (index) => {
        if (sumInsuredEditIndex === index) {
            setSumInsuredEditIndex(null);
            handleSumInsuredUpdate(index);
        } else {
            setSumInsuredEditIndex(index);
        }
    }
    const handleRiderDetailEditClick = (index) => {
        if (editRiderIndex === index) {
            setEditRiderIndex(null);
            handleRiderDetailsUpdate(index);
        } else {
            setEditRiderIndex(index);
        }
    }
    const handleInfoPopupConfirm = async () => {
        // dispatch(handleCreatingSubProduct());
        if (isSubProductInformationFilled()) {
            let newSubProductId = id
            if (currentSection === 'sumInsuredCriteria') {
                handleSumInsuredAdd(sumInsuredRows.length - 1);
            } else if (currentSection === 'riderDetails') {
                handleRiderDetailAdd(riderRows.length - 1);
            } else if (currentSection === 'SubProduct') {
                newSubProductId = await handleCreatingSubProduct();


                if (typeof newSubProductId.payload === 'number') {
                    navigate(`/dashboard/sub-product-form/edit/${newSubProductId.payload}`)
                }
            }
            dispatch(getSubProductById(newSubProductId));
        } else {
            toast.error('Fill all the sub product information');
            setSectionsVisibility({
                hospitalizationDays: false,
                sumInsuredCriteria: false,
                riderDetails: false,
            })
        }
        // navigate(`/dashboard/sub-product-form/edit/${id}`)
        setOpenInfoPopup(false);
    }
    const validate = () => {
        const errors = {};
        if (!formData.main_product_id) {
            errors.main_product_id = 'Please select a product';
        }
        if (!formData.insurance_company_id) {
            errors.insurance_company_id = 'Please select an insurance company';
        }
        if (!formData.product_master_id) {
            errors.product_master_id = 'Please select a product master';
        }
        if (!formData.sub_product_name) {
            errors.sub_product_name = 'Sub product name is required';
        }
        if (formData.co_pay) {
            if (formData.co_pay < 0 || formData.co_pay > 99) {
                errors.co_pay = 'Co-pay must be between 0 and 99';
            }
        }
        if (!formData.child_separation_age) {
            errors.child_separation_age = 'Please select child separation age';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    }
    const handleSaveAndNew = () => {
        if (!validate()) {
            return;
        }
        handleCreatingSubProduct();
        setFormData({
            ...setFormData,
            main_product_id: '',
            insurance_company_id: '',
            product_master_id: '',
            sub_product_name: '',
            co_pay: null,
            child_separation_age: '',
            pre_hospitalization_days: '',
            post_hospitalization_days: '',
        })
    }
    const handleSave = () => {
        if (!validate()) {
            return;
        }
        if (id) {
            handleUpdatingSubProduct();
        } else {
            handleCreatingSubProduct()
        }
        navigate('/dashboard/sub-product')
    }
    const handleCancel = () => {
        setFormData({});
        setRiderRows([]);
        setSumInsuredRows([]);

        navigate('/dashboard/sub-product');
    }
    const handleChange = (e) => {
        const { name, value } = e.target;

        if (value === ' ') {
            setValidationErrors({
                ...validationErrors,
                [name]: 'Do not start with a whitespace character'
            })
            return;
        }
        if (name === 'co_pay') {
            if (value.length > 2 || Number(value) < 0) {
                return;
            }
        }
        const data = name === 'sub_product_name' ? value.toUpperCase().replace(/\s{2,}$/, ' ') : name === 'co_pay' ? Number(value) || 0 : value;
        setFormData({
            ...formData,
            [name]: data
        })
        setValidationErrors({
            ...validationErrors,
            [name]: false
        });
        if (!id) {
            const { main_product_id, insurance_company_id, product_master_id, sub_product_name } = formData;
            if (main_product_id && insurance_company_id && product_master_id && sub_product_name && !subProductNameFocus) {
                dispatch(getAllSubProducts()).then(action => {
                    const isDuplicate = action.payload.find(item => item.insurance_company_id === insurance_company_id && item.main_product_id === main_product_id && item.product_master_id === product_master_id && item.sub_product_name === sub_product_name);
                    if (isDuplicate) {
                        toast.error('Sub product combination already exists');
                        setFormData({
                            ...formData,
                            sub_product_name: '',
                        })
                    }
                })
            }
        }
    }
    const handleHospitalizationSave = () => {
        const errors = {};
        if (isEditingHospitalization) {
            if (formData.pre_hospitalization_days === null && formData.pre_hospitalization_days < 0) {
                errors.pre_hospitalization_days = 'Pre-hospitalization days are required';
            }
            if (formData.post_hospitalization_days === null) {
                errors.post_hospitalization_days = 'Post-hospitalization days are required';
            }
        }
        setValidationErrors(errors);
        if (Object.keys(errors).length === 0) {
            if (isEditingHospitalization) {
                handleUpdatingSubProduct();
            }
            setIsEditingHospitalization(!isEditingHospitalization);
        }
    }
    const handleSumInsuredChange = (index, name, value) => {
        // Check if the value is a number
        if (name === 'sum_insured') {
            if (/[^0-9]/.test(value)) {
                setValidationErrors({
                    ...validationErrors,
                    sumInsuredIndex: index,
                    sum_insured: 'Sum Insured must be a number'
                });
                return;
            }
        }

        // Cap the sum insured at 2 crore (20,000,000)
        const cappedValue = name === 'sum_insured' && value > 20000000 ? 20000000 : value;

        const updatedRows = sumInsuredRows.map((row, idx) =>
            idx === index ? { ...row, [name]: cappedValue } : row
        );

        setValidationErrors({
            ...validationErrors,
            sumInsuredIndex: '',
            min_age: false,
            max_age: false,
            sum_insured: false
        });

        setSumInsuredRows(updatedRows);
    };
    const handleRiderDetailChange = (index, value) => {
        const updatedRows = [...riderRows];
        const updatedValidationErrors = { ...validationErrors };

        if (value === ' ') {
            updatedValidationErrors[index] = {
                rider_details: 'Do not start with a whitespace character'
            };
            setValidationErrors(updatedValidationErrors);
            return;
        }

        const data = value.toUpperCase().replace(/\s{2,}$/, ' ');
        updatedRows[index] = { ...updatedRows[index], rider_details: data };

        // Clear the validation error for this specific row if the value is valid
        if (updatedValidationErrors[index]) {
            delete updatedValidationErrors[index];
        }
        setRiderRows(updatedRows);
        setValidationErrors(updatedValidationErrors);
    };

    const handleSumInsuredAdd = (index) => {
        const chosenRow = sumInsuredRows[index];
        const isDuplicate = sumInsured.find(item =>
            item.min_age === chosenRow.min_age &&
            item.max_age === chosenRow.max_age &&
            item.sum_insured === chosenRow.sum_insured
        );
        if (isDuplicate) {
            toast.error('Data already exists')
            setSumInsuredRows(prevData => {
                const newData = [...prevData];
                const lastIndex = newData.length - 1;
                newData[lastIndex] = {
                    ...newData[lastIndex],
                    min_age: '',
                    max_age: '',
                    sum_insured: ''
                };
                return newData;
            });
            return;
        }
        const newSumInsuredRow = {
            ...chosenRow,
            sub_product_id: id
        };
        handleCreatingSumInsured(newSumInsuredRow);
        setSumInsuredRows([...sumInsuredRows, { min_age: '', max_age: '', sum_insured: '' }]);
    };

    const handleRiderDetailAdd = (index) => {
        const chosenRow = riderRows[index];
        const chosenRowRiderDetails = chosenRow.rider_details.toString().trim();

        const isDuplicate = riderDetails.some(item => {
            const itemRiderDetails = item.rider_details.toString().trim();
            if (itemRiderDetails === chosenRowRiderDetails) {
                return true;
            }
            return false;
        });
        if (isDuplicate) {
            toast.error('Data already exists');
            setRiderRows(prevData => {
                const newData = [...prevData];
                const lastIndex = newData.length - 1;
                newData[lastIndex] = {
                    rider_details: ''
                };
                return newData;
            });
            return;
        }
        const newRiderRow = {
            ...chosenRow,
            sub_product_id: id
        };
        handleCreatingRiderDetails(newRiderRow);
        setRiderRows([...riderRows, { rider_details: '' }]);
    }
    const handleSumInsuredUpdate = (index) => {
        dispatch(updateSumInsured({ id: sumInsuredRows[index].id, sumInsuredData: sumInsuredRows[index] }))
    };
    const handleRiderDetailsUpdate = (index) => {
        const updatedData = riderRows[index].rider_details.trim();
        const updatedRows = [...riderRows];
        updatedRows[index] = {
            ...updatedRows[index],
            rider_details: updatedData
        };
        setRiderRows(updatedRows);
        dispatch(updateRiderDetails({ id: riderRows[index].id, riderData: updatedRows[index] }));
    };

    const handleDeleteSumInsured = (sumInsuredId) => {
        dispatch(deleteSumInsured(sumInsuredId))
        sumInsuredRows.filter(row => row.id === sumInsuredId);
    };
    const handleRiderDetailDelete = (riderDetailId) => {
        dispatch(deleteRiderDetails(riderDetailId))

    };

    //Handling requests to backend
    const handleCreatingSubProduct = async () => {
        const id = await dispatch(createSubProduct(trimFormData(formData)))
        setIsSubProductAdded(true); // Mark that a sub-product has been added
        return id;
    };
    const handleCreatingSumInsured = (newSumInsuredRow) => {
        dispatch(createSumInsured(newSumInsuredRow))
    };
    const handleCreatingRiderDetails = (newRiderRows) => {
        dispatch(createRidersDetails(trimFormData(newRiderRows)))
    };
    const handleUpdatingSubProduct = () => {
        dispatch(updateSubProduct({ id, data: trimFormData(formData) })).then(() => {
            dispatch(getSubProductById(id));
        })
    };
    return (
        <form>
            <Grid container spacing={2}>
                {/* Image and Module for the form */}
                <Grid item xs={8} sx={{ display: 'flex', alignItems: 'center' }}>
                    <img
                        src='/image.png'
                        alt='module icon'
                        style={{
                            width: '20px',
                            padding: '10px,30px,10px,30px',
                            marginLeft: '20px', backgroundColor: 'green'
                        }}
                    />
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center'
                        }}
                    >
                        <ModuleName
                            moduleName="Sub Product"
                            pageName={id ? subProduct?.status === 0 ? "View" : "Edit" : "Create"}
                        />
                    </Box>
                </Grid>

                {/* Main Buttons */}
                <Grid item xs={4} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!id && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{
                                maxWidth: '120px',
                                width: '120px',
                                mx: 0.5,
                                color: 'green',
                                borderColor: 'green',
                                mt: 3,
                                textTransform: 'none'
                            }}
                            onClick={handleSaveAndNew}
                        >
                            Save &amp; New
                        </Button>
                    )}
                    {(!id || subProduct?.status === 1) && <Button
                        variant="outlined"
                        size="small"
                        sx={{
                            maxWidth: '120px',
                            width: '120px',
                            mx: 0.5,
                            color: 'green',
                            borderColor: 'green',
                            mt: 3,
                            textTransform: 'none'
                        }}
                        onClick={handleSave}
                    >
                        Save
                    </Button>}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{
                            maxWidth: '120px',
                            width: '120px',
                            mx: 0.5,
                            color: 'red',
                            borderColor: 'red',
                            mt: 3,
                            textTransform: 'none'
                        }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>

                {/* Sub Product Information */}
                <Grid item xs={12} >
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            backgroundColor: '#f0f0f0',
                            padding: '15px',
                            borderRadius: '4px',
                            height: '60px'
                        }}
                    >
                        <h2>Sub Product Information</h2>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ margin: '1rem 2rem' }}>
                    <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                        <DropDown
                            label="Insurance Company"
                            name="insurance_company_id"
                            options={insuranceCompanies.filter(company => company.status === 1).map(company => ({ label: company.insurance_company_name, value: company.id }))}
                            value={formData.insurance_company_id}
                            onChange={handleChange}
                            fullWidth
                            required
                            disabled={id ? true : false}
                            helperText={validationErrors.insurance_company_id || ''}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                        <DropDown
                            label="Main Product"
                            name="main_product_id"
                            options={mainProducts.filter(product => product.status === 1).map(product => ({ label: product.main_product, value: product.id }))}
                            value={formData.main_product_id}
                            onChange={handleChange}
                            fullWidth
                            required
                            disabled={id ? true : false}
                            helperText={validationErrors.main_product_id || ''}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                        <DropDown
                            label="Product Master"
                            name="product_master_id"
                            options={masterProducts.filter(product => product.status === 1).map(product => ({ label: product.product_name, value: product.id }))}
                            value={formData.product_master_id}
                            onChange={handleChange}
                            fullWidth
                            required
                            disabled={id ? true : false}
                            helperText={validationErrors.product_master_id || ''}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                        <CustomTextField
                            label="Sub Product Name"
                            name="sub_product_name"
                            value={formData.sub_product_name}
                            onChange={handleChange}
                            onFocus={() => setSubProductNameFocus(true)}
                            onBlur={() => setSubProductNameFocus(false)}
                            fullWidth
                            helperText={validationErrors.sub_product_name || ''}
                            isDisabled={id ? true : false}
                            isRequired
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                        <CustomTextField
                            label="Co-Pay"
                            name="co_pay"
                            value={formData.co_pay}
                            onChange={handleChange}
                            fullWidth
                            helperText={validationErrors.co_pay}
                            isDisabled={subProduct?.status === 0}
                            shrink={true}
                        />
                    </Grid>
                    <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                        <DropDown
                            label="Child Separation Age"
                            name="child_separation_age"
                            options={generateOptions(18, 30, 1)}
                            value={formData.child_separation_age}
                            onChange={handleChange}
                            fullWidth
                            required
                            helperText={validationErrors.child_separation_age || ''}
                            disabled={subProduct?.status === 0}
                        />
                    </Grid>
                </Grid>

                {/* Hospitalization Days */}
                <Grid item xs={12} >
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            backgroundColor: '#f0f0f0',
                            padding: '15px',
                            borderRadius: '4px',
                            height: '60px'
                        }}
                    >
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={sectionsVisibility.hospitalizationDays ? true : false}
                                    onChange={() => handleCheckboxChange('hospitalizationDays')}
                                    color="primary"
                                />
                            }
                            label={<h2>Hospitalization Days</h2>}
                        />
                    </Box>
                </Grid>
                {
                    sectionsVisibility.hospitalizationDays &&
                    <Grid container spacing={2} sx={{ margin: '1rem 2rem' }}>
                        <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                            <DropDown
                                label="Pre Hospitalization Days"
                                name="pre_hospitalization_days"
                                options={generateOptions(30, 60, 30)}
                                value={formData.pre_hospitalization_days}
                                onChange={handleChange}
                                fullWidth
                                required
                                disabled={!isEditingHospitalization || subProduct?.status === 0}
                                helperText={validationErrors.pre_hospitalization_days}
                            />
                        </Grid>
                        <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                            <DropDown
                                label="Post Hospitalization Days"
                                name="post_hospitalization_days"
                                options={[{ label: '15', value: 15 }, { label: '30', value: 30 }, { label: '45', value: 45 }, { label: '75', value: 75 }, { label: '90', value: 90 }]}
                                value={formData.post_hospitalization_days}
                                onChange={handleChange}
                                fullWidth
                                required
                                disabled={!isEditingHospitalization || subProduct?.status === 0}
                                helperText={validationErrors.post_hospitalization_days}
                            />
                        </Grid>
                        <Grid item xs={3} sx={{ display: 'flex', alignItems: 'center' }}>
                            {subProduct?.status === 1 && <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', textTransform: 'none' }}
                                onClick={() => handleHospitalizationSave()}
                            >
                                {isEditingHospitalization ? 'Save' : 'Edit'}
                            </Button>}
                        </Grid>
                    </Grid>
                }

                {/* Sum Insured Age Band Criteria*/}
                <Grid item xs={12} >
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            backgroundColor: '#f0f0f0',
                            padding: '15px',
                            borderRadius: '4px',
                            height: '60px'
                        }}
                    >
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={sectionsVisibility.sumInsuredCriteria ? true : false}
                                    onChange={() => handleCheckboxChange('sumInsuredCriteria')}
                                    color="primary"
                                />
                            }
                            label={<h2>Sum Insured Age Band Criteria</h2>}
                        />
                    </Box>
                </Grid>
                {sectionsVisibility.sumInsuredCriteria && sumInsuredRows.map((row, index) => (
                    <Grid
                        container
                        spacing={2}
                        key={index}
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            flexWrap: 'nowrap',
                            margin: '1rem 2rem 0 2rem',
                        }}
                    >
                        {/* Min Age Dropdown */}
                        <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                            <DropDown
                                label="Min Age"
                                name="min_age"
                                options={generateOptions(0, row.max_age === 0 ? 0 : row.max_age || 100, 1)}
                                value={row.min_age}
                                onChange={(e) => handleSumInsuredChange(index, 'min_age', e.target.value)}
                                fullWidth
                                required
                                disabled={(sumInsuredEditIndex !== index && index !== sumInsuredRows.length - 1) || (subProduct?.status === 0)}
                                helperText={validationErrors?.sumInsuredIndex === index ? validationErrors.min_age : ''}
                            />
                        </Grid>
                        {/* Max Age Dropdown */}
                        <Grid item xs={3} sx={{ margin: '0, 30px' }}>
                            <DropDown
                                label="Max Age"
                                name="max_age"
                                options={generateOptions(row.min_age || 0, 100, 1)}
                                value={row.max_age}
                                onChange={(e) => handleSumInsuredChange(index, 'max_age', e.target.value)}
                                fullWidth
                                required
                                disabled={(sumInsuredEditIndex !== index && index !== sumInsuredRows.length - 1) || (subProduct?.status === 0)}
                                helperText={validationErrors?.sumInsuredIndex === index ? validationErrors.max_age : ''}
                            />
                        </Grid>
                        {/* Sum Insured Dropdown */}
                        <Grid item xs={3} sx={{ margin: '0, 30px' }} ref={sumInsuredRef}>
                            <CustomTextField
                                label="Sum Insured"
                                name="sum_insured"
                                value={row.sum_insured}
                                onChange={(e) => handleSumInsuredChange(index, 'sum_insured', e.target.value)}
                                onBlur={(e) => {
                                    const value = parseInt(e.target.value, 10);
                                    const roundedValue = value < 50000 ? 50000 : Math.floor(value / 50000) * 50000;
                                    handleSumInsuredChange(index, 'sum_insured', roundedValue);
                                }}
                                fullWidth
                                isRequired
                                disabled={(sumInsuredEditIndex !== index && index !== sumInsuredRows.length - 1) || (subProduct?.status === 0)}
                                helperText={validationErrors?.sumInsuredIndex === index ? validationErrors?.sum_insured : ''}
                            />
                        </Grid>
                        {/* Action Buttons */}
                        {subProduct?.status === 1 &&
                            <Grid item xs={3} sx={{ display: 'flex', alignItems: 'center' }}>
                                {index === sumInsuredRows.length - 1 && (
                                    <Button
                                        variant="outlined"
                                        size="small"
                                        sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', textTransform: 'none' }}
                                        onClick={() => handleSumInsuredAdd(index)}
                                        disabled={!(row.min_age !== '' && row.max_age !== '' && row.sum_insured !== '')}
                                    >
                                        Add
                                    </Button>
                                )}
                                {/* Edit/Save button */}
                                {index !== sumInsuredRows.length - 1 && (
                                    <Button
                                        variant="outlined"
                                        size="small"
                                        sx={{
                                            maxWidth: '100px',
                                            width: '100%',
                                            mx: 0.5,
                                            color: sumInsuredEditIndex === index ? 'blue' : 'green',
                                            borderColor: sumInsuredEditIndex === index ? 'blue' : 'green',
                                            textTransform: 'none',
                                        }}
                                        onClick={() => handleSumInsuredEditClick(index)}
                                    >
                                        {sumInsuredEditIndex === index ? 'Save' : 'Edit'}
                                    </Button>
                                )}
                                {/* Delete Button */}
                                {index !== sumInsuredRows.length - 1 && (
                                    <Button
                                        variant="outlined"
                                        size="small"
                                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', textTransform: 'none' }}
                                        onClick={() => handleDeleteSumInsured(row.id)}
                                    >
                                        Delete
                                    </Button>
                                )}
                            </Grid>}
                    </Grid>
                ))}


                {/* Rider Details*/}
                <Grid item xs={12} >
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            backgroundColor: '#f0f0f0',
                            padding: '15px',
                            borderRadius: '4px',
                            height: '60px'
                        }}
                    >
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={sectionsVisibility.riderDetails ? true : false}
                                    onChange={() => handleCheckboxChange('riderDetails')}
                                    color="primary"
                                />
                            }
                            label={<h2>Rider Details</h2>}
                        />
                    </Box>
                </Grid>

                <Grid container spacing={2} sx={{ paddingBottom: '1rem', width: '100%' }}>
                    {sectionsVisibility.riderDetails && riderRows.map((row, index) => (
                        <Grid
                            container
                            spacing={2}
                            key={index}
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                flexWrap: 'nowrap',
                                margin: '1rem 2rem 0 2rem',
                            }}
                        >
                            {/* Rider Details TextField */}
                            <Grid item xs={8}>
                                <CustomTextField
                                    label="Rider Details"
                                    name="rider_details"
                                    value={row.rider_details}
                                    onChange={(e) => handleRiderDetailChange(index, e.target.value)}
                                    fullWidth
                                    disabled={(editRiderIndex !== index && index !== riderRows.length - 1) || (subProduct?.status === 0)}
                                    isRequired
                                    helperText={validationErrors[index] ? validationErrors[index].rider_details : ''}
                                />
                            </Grid>
                            {/* Action Buttons */}
                            {subProduct?.status === 1 &&
                                <Grid item xs={4} sx={{ display: 'flex', alignItems: 'center' }}>
                                    {index === riderRows.length - 1 && (
                                        <Button
                                            variant="outlined"
                                            size="small"
                                            sx={{
                                                width: '100px',
                                                mx: 0.5,
                                                color: 'green',
                                                borderColor: 'green',
                                                textTransform: 'none',
                                            }}
                                            disabled={!row.rider_details}
                                            onClick={() => handleRiderDetailAdd(index)}
                                        >
                                            Add
                                        </Button>
                                    )}
                                    {index !== riderRows.length - 1 && (
                                        <Button
                                            variant="outlined"
                                            size="small"
                                            sx={{
                                                width: '100px',
                                                mx: 0.5,
                                                color: editRiderIndex === index ? 'blue' : 'green',
                                                borderColor: editRiderIndex === index ? 'blue' : 'green',
                                                textTransform: 'none',
                                            }}
                                            onClick={() => handleRiderDetailEditClick(index)}
                                        >
                                            {editRiderIndex === index ? 'Save' : 'Edit'}
                                        </Button>
                                    )}
                                    {index !== riderRows.length - 1 && (
                                        <Button
                                            variant="outlined"
                                            size="small"
                                            sx={{
                                                width: '100px',
                                                mx: 1.5,
                                                color: 'red',
                                                borderColor: 'red',
                                                textTransform: 'none',
                                            }}
                                            onClick={() => handleRiderDetailDelete(row.id)}
                                        >
                                            Delete
                                        </Button>
                                    )}
                                </Grid>
                            }
                        </Grid>
                    ))}

                </Grid>
            </Grid>
            <InfoPopup
                open={openInfoPopup}
                onClose={handleCloseInfoPopup}
                onConfirm={handleInfoPopupConfirm}
                currentSection={currentSection}
                message={infoPopupMessage}
            />
        </form >
    )
}

export default SubProductForm