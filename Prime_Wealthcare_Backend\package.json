{"name": "my_backend", "version": "1.0.0", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js", "frontend": "cd ../Prime_Wealthcare_Frontend && npm start", "dev": "concurrently \"npm start\" \"npm run frontend\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.7.8", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.21.1", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "moment": "^2.30.1", "ms": "^2.1.3", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "nodemon": "^3.1.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.3", "xml2js": "^0.6.2"}, "devDependencies": {"concurrently": "^9.1.2"}}