const path = require('path');
const fs = require('fs');
const yaml = require('js-yaml');
require('dotenv').config();

// Load YAML configuration
let config;
const configPath = path.join(__dirname, 'config.yml'); // Use absolute path

try {
  config = yaml.load(fs.readFileSync(configPath, 'utf8'));
} catch (e) {
  console.error('Error reading YAML file:', e);
}

// Integrate environment variables
if (config && config.development) {
  config.development.connection.host = process.env.DB_HOST;
  config.development.connection.user = process.env.DB_USER;
  config.development.connection.password = process.env.DB_PASSWORD;
  config.development.connection.database = process.env.DB_NAME;
} else {
  console.error('Configuration is missing or invalid');
}

module.exports = config;

