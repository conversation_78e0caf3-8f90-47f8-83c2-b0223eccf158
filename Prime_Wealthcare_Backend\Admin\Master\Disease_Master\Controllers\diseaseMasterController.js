const DiseaseMaster = require('../Model/diseaseMaster');

// Get all diseases
exports.getAllDiseases = async (req, res, next) => {
    try {
        const data = await DiseaseMaster.findAll();
        res.status(200).json(data);
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Get disease by ID
exports.getDiseaseById = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = await DiseaseMaster.findById(id);
        if (!data) {
            return res.status(404).json({ message: 'Disease not found' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Get disease by name
exports.getDiseaseByName = async (req, res, next) => {
    try {
        const name = req.params.name;
        const data = await DiseaseMaster.findByName(name);
        res.status(200).json(data);
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Create a new disease
exports.createDisease = async (req, res, next) => {
    try {
        const data = req.body;
        const result = await DiseaseMaster.create(data);
        res.status(201).json(result);
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Update disease by ID
exports.updateDisease = async (req, res, next) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const result = await DiseaseMaster.updateById(id, data);
        if (result) {
            res.status(200).json({ message: 'Disease updated successfully' });
        } else {
            res.status(404).json({ message: 'Disease not found' });
        }
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Delete (deactivate) disease by ID
exports.deleteDisease = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await DiseaseMaster.deleteById(id);
        if (result) {
            res.status(200).json({ message: 'Disease deactivated successfully' });
        } else {
            res.status(404).json({ message: 'Disease not found' });
        }
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Reinstate a disease by ID
exports.reinstateDisease = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await DiseaseMaster.reinstate(id);
        if (result) {
            res.status(200).json({ message: 'Disease reinstated successfully' });
        } else {
            res.status(404).json({ message: 'Disease not found' });
        }
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};

// Get diseases by specific criteria (new, deactivated, edited)
exports.getDiseasesByCriteria = async (req, res, next) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await DiseaseMaster.findAll();
                break;
            case 'newLastWeek':
                data = await DiseaseMaster.newLastWeek();
                break;
            case 'newThisWeek':
                data = await DiseaseMaster.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await DiseaseMaster.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await DiseaseMaster.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await DiseaseMaster.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await DiseaseMaster.editedLastWeek();
                break;
            case 'allActive':
                data = await DiseaseMaster.allActive();
                break;
            case 'allInactive':
                data = await DiseaseMaster.allInactive();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }
        res.status(200).json(data);
    } catch (error) {
        next(error); // Pass the error to the global error handler
    }
};
