import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import DropDown from '../../../components/table/DropDown';
import { toast } from 'react-toastify';
import { useLocation } from 'react-router-dom';
import {
  fetchImfBranchById,
  createImfBranch,
  updateImfBranch,
  getLocationByPincode, fetchAllImfBranches, getAreasByPincodeAndCity
} from '../../../redux/actions/action';


const ImfBranchForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();

  const { data } = useSelector(state => state.imfBranchReducer || {});
  const locationData = useSelector(state => state.areaManagementReducer.locations);

  const [cityOptions, setCityOptions] = useState([]);
  const [areaOptions, setAreaOptions] = useState([]);

  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    branch_name: "",
    branch_code: "",
    email: "",
    branch_manager_name: "",
    branch_manager_number: "",
    help_line_number: "",
    address_line1: "",
    address_line2: "",
    pincode: "",
    state: "",
    city: "",
    area: null, // Initialize as null // Add area_id to the form data
  });

  useEffect(() => {
    if (id) {
      dispatch(fetchImfBranchById(id));
    }
  }, [id, dispatch]);

  const isEditMode = !!id;
  const currentBranch = useSelector(state => state.imfBranchReducer.currentBranch); // Declare outside the useEffect

  useEffect(() => {

  }, [formData])

  useEffect(() => {
    if (isEditMode) {
      if (currentBranch) {

        setFormData({
          branch_name: currentBranch.branch_name || "",
          branch_code: currentBranch.branch_code || "",
          email: currentBranch.email || "",
          branch_manager_name: currentBranch.branch_manager_name || "",
          branch_manager_number: currentBranch.branch_manager_number || "",
          help_line_number: currentBranch.help_line_number || "",
          address_line1: currentBranch.address_line1 || "",
          address_line2: currentBranch.address_line2 || "",
          pincode: currentBranch.pincode || "",
          area: currentBranch.area || "",
          city: currentBranch.city || "",
          state: currentBranch.state || "",
        });

        dispatch(getLocationByPincode(currentBranch.pincode));
      } else {
        dispatch(fetchImfBranchById(id));
      }
    }
  }, [id, currentBranch, dispatch, isEditMode]); // Removed 'data' from dependencies as it's not used

  useEffect(() => {
    if (String(formData.pincode).length === 6) {
      dispatch(getLocationByPincode(formData.pincode)).then((action) => {
        if (!action.payload || action.payload.length === 0) {
          setErrors(prevErrors => ({ ...prevErrors, pincode: 'This pincode does not exist' }));
          setFormData(prevFormData => ({
            ...prevFormData,
            area: '',
            city: '',
            state: ''
          }));
        } else {
          const uniqueCities = [...new Set(action.payload.map(location => location.city))];
          const data = uniqueCities.map(city => ({ label: city, value: city }));
          setCityOptions(data);
          setFormData(prevFormData => ({
            ...prevFormData,
            state: currentBranch?.state || action.payload[0].state,
            city: currentBranch?.city
          }));
        }
      });
    }
  }, [formData.pincode, dispatch]);

  useEffect(() => {
    if (formData.city) {
      dispatch(getAreasByPincodeAndCity({ pincode: formData.pincode, city: formData.city })).then((action) => {
        if (action.payload && action.payload.length > 0) {
          const data = action.payload?.map(area => ({ label: area.area, value: area.id }));
          setAreaOptions(data);
          if (data.length === 1) {
            setFormData(prevFormData => ({
              ...prevFormData,
              area: currentBranch?.area || data[0].value
            }));
          }
        } else {
          setAreaOptions([]);
        }
      });
    }
  }, [formData.city, dispatch]);


  // useEffect(() => {
  //   if (locationData && formData.pincode) {
  //     setFormData(prevFormData => ({
  //       ...prevFormData,
  //       area: locationData.area || '',
  //       city: locationData.city || '',
  //       state: locationData.state || ''
  //     }));
  //   }
  // }, [locationData, formData.pincode]);

  const toCamelCase = (str) => {
    return str
      .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()); // Capitalize first letter of each word
  };


  const handleChange = (e) => {
    const { name, value } = e.target;


    let formattedValue = value;

    if (name === 'pincode' && value.length > 6) {
      return;
    }

    // Format based on field name
    if (name !== 'address_line1' && name !== 'address_line2' && name !== 'email' && name !== 'area') {
      formattedValue = formattedValue.toUpperCase();
    }

    // Ensure email is in lowercase
    if (name === 'email') {
      formattedValue = formattedValue.toLowerCase();
    }

    // Validate and format address fields to camel case
    if (name.startsWith('address')) {
      const regex = /^[a-zA-Z0-9,.\-\s#/&]+$/;
      if (!regex.test(value) && value !== '') {
        setErrors(prevErrors => ({
          ...prevErrors,
          [name]: 'Address must only contain letters, numbers, spaces, and special characters (,.-/#&)'
        }));
        return;
      }
      formattedValue = toCamelCase(formattedValue);
    }

    // Limit mobile number to 10 digits and prevent characters
    if (name === 'branch_manager_number' || name === 'help_line_number') {
      formattedValue = formattedValue.replace(/[^0-9]/g, '');
      if (formattedValue.length > 10) {
        formattedValue = formattedValue.slice(0, 10);
      }
    }

    // Update the form data
    setFormData({
      ...formData,
      [name]: formattedValue,
    });

    // Clear any previous error for the field
    setErrors(prevErrors => {
      const { [name]: removedError, ...rest } = prevErrors;
      return rest;
    });

    // Handle pincode specific logic
    if (formattedValue.length === 6) {
      dispatch(getLocationByPincode(formattedValue)).then((action) => {
        if (getLocationByPincode.rejected.match(action)) {
          // Handle errors when pincode doesn't exist
          setErrors((prevErrors) => ({
            ...prevErrors,
            pincode: 'Pincode does not exist',
          }));
          setFormData(prevData => ({
            ...prevData,
            area: '',
            city: '',
            state: '',
          }));
        } else {
          setErrors(prevErrors => {
            const { pincode, ...rest } = prevErrors;
            return rest;
          });
        }
      });
    }
  };


  const handleValidation = () => {
    let validationErrors = {};

    // Validate Branch Name
    if (!formData.branch_name || formData.branch_name.trim().length < 3 || formData.branch_name.trim().length > 100) {
      validationErrors.branch_name = 'Branch Name is required and must be between 3 and 100 characters.';
    }

    // Validate Branch Code
    if (!formData.branch_code || formData.branch_code.trim().length < 3 || formData.branch_code.trim().length > 10) {
      validationErrors.branch_code = 'Branch Code is required and must be between 3 and 10 characters.';
    }

    // Check Combination of Branch Name and Branch Code Uniqueness (only in create mode)
    if (!isEditMode && formData.branch_name && formData.branch_code) {
      const currentCombination = {
        name: formData.branch_name.toLowerCase().trim(),
        code: formData.branch_code.toLowerCase().trim(),
      };

      const existingCombinations = data.map(branch => ({
        name: (branch.branch_name || '').toLowerCase().trim(),
        code: (branch.branch_code || '').toLowerCase().trim(),
      }));

      const isCombinationExists = existingCombinations.some(combo => combo.name === currentCombination.name && combo.code === currentCombination.code);

      if (isCombinationExists) {
        validationErrors.branch_name = 'The combination of Branch Name and Branch Code must be unique.';
        validationErrors.branch_code = 'The combination of Branch Name and Branch Code must be unique.';
      }
    }
    // Validate Helpline Number
    if (formData.help_line_number && !/^\d{10,12}$/.test(formData.help_line_number)) {
      validationErrors.help_line_number = 'Please enter a valid 10-12 digit helpline number';
    }

    // Validate Email
    if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      validationErrors.email = 'Please enter a valid email address.';
    }

    // Validate Branch Manager Name
    if (!formData.branch_manager_name) {
      validationErrors.branch_manager_name = 'Please select a Branch Manager.';
    }

    // Validate Branch Manager Number
    if (!/^[0-9]{10}$/.test(formData.branch_manager_number)) {
      validationErrors.branch_manager_number = 'Please enter a valid 10-digit Branch Manager Number.';
    }

    // Validate Pincode
    if (!/^[0-9]{6}$/.test(formData.pincode)) {
      validationErrors.pincode = 'Please enter a valid 6-digit Pincode.';
    }

    if(!formData.city){
      validationErrors.city = 'Please select a City.';
    }

    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0; // Return true if no errors
  };

  const handleBranchCreationAndUpdate = async () => {
    const isValid = handleValidation();
    if (!isValid) return; // Stop if the form is invalid
    if (formData.area === '') {
      setFormData({
        ...formData,
        area: null
      })
    }
    try {
      let response;
      if (isEditMode) {
        response = await dispatch(updateImfBranch({ id, updatedBranch: formData }));
        if (!response.error) {
          toast.success('Branch updated successfully!');
        }
      } else {
        response = await dispatch(createImfBranch(formData));
        if (!response.error) {
          toast.success('Branch created successfully!');
        }
      }

      if (response.error) {
        throw new Error(response.error.message);
      }

      return true; // Indicate success
    } catch (error) {
      console.error("Error saving branch:", error);
      toast.error("Failed to save branch: " + error.message);
      return false; // Indicate failure
    }
  };


  const handleSave = async () => {
    const success = await handleBranchCreationAndUpdate();
    if (success) {
      navigate('/dashboard/imf-branch'); // Redirect after successful save
    }
  };

  const handleSaveAndNew = async () => {

    const validate = handleValidation();
    if (!validate) return;
    await handleBranchCreationAndUpdate().then(() => {
      dispatch(fetchAllImfBranches());
    })
    if (validate) {
      // Reset form fields for a new entry after successful save
      setFormData({
        branch_name: "",
        branch_code: "",
        email: "",
        branch_manager_name: "",
        branch_manager_number: "",
        help_line_number: "",
        address_line1: "",
        address_line2: "",
        pincode: "",
        area: "",
        city: "",
        state: "",
      });
      // Optionally, navigate to the branch form if it's a separate route
      navigate('/dashboard/imf-branch-form');
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/imf-branch'); // Redirect to list or home page
  };

  return (
    <>
      <form>
        <Grid container spacing={2}>
          {/* <Navbar /> */}
          <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
            <img src="/image.png" alt="module icon" style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }} />

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ModuleName moduleName="Imf Branch" pageName={id ? currentBranch?.status === 0 ? "View" : "Edit" : "Create"} />          </Box>
          </Grid>
          <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
            {id ? (

              <>
                {(currentBranch?.status === 1 || !id) && <Button
                  variant="outlined"
                  size="small"
                  sx={{
                    maxWidth: '120px', // Increase width if needed
                    width: '120px', // Set a fixed width
                    mx: 0.5,
                    color: 'green',
                    borderColor: 'green',
                    mt: 3,
                    textTransform: 'none',
                    fontSize: '0.875rem', // Adjust font size if needed
                    whiteSpace: 'nowrap' // Prevent text from wrapping
                  }}
                  onClick={handleSaveAndNew}
                >
                  Update & New
                </Button>}
                {(currentBranch?.status === 1 || !id) && <Button
                  variant="outlined"
                  size="small"
                  sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                  onClick={handleSave}
                >
                  Update
                </Button>}
              </>
            ) : (
              // Buttons for Create Mode
              <>
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                  onClick={handleSaveAndNew}
                >
                  Save & New
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                  onClick={handleSave}
                >
                  Save
                </Button>
              </>
            )}
            <Button
              variant="outlined"
              size="small"
              sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
              onClick={handleCancel}
            >
              Cancel
            </Button>
          </Grid>

          <Grid item xs={12}>
            <Box
              sx={{
                backgroundColor: '#f0f0f0',
                padding: '14px',
                borderRadius: '4px',
                //  mb: 2,
                height: '60px',
              }}
            >
              <h2>IMF Branch Information</h2>
            </Box>
          </Grid>

          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Branch Name"
              name="branch_name"
              value={formData.branch_name}
              onChange={handleChange}
              minLength={3}
              maxLength={100}
              disabled={isEditMode} // Disable in edit mode
              style={isEditMode ? { backgroundColor: 'lightgray' } : {}}
              error={!!errors.branch_name} // Show error if there's an error for this field
              helperText={errors.branch_name} // Display the error message
              isRequired
            />
          </Grid>

          <Grid item xs={3}>
            <CustomTextField
              label="Branch Code"
              name="branch_code"
              value={formData.branch_code}
              onChange={handleChange}

              minLength={3}
              maxLength={10}
              disabled={isEditMode} // Disable in edit mode
              style={isEditMode ? { backgroundColor: 'lightgray' } : {}}
              error={!!errors.branch_code} // Show error if there's an error for this field
              helperText={errors.branch_code} // Display the error message
              isRequired
            />

          </Grid>

          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <CustomTextField
              label="Help Line Number"
              name="help_line_number"
              value={formData.help_line_number}
              onChange={handleChange}
              disabled={id && currentBranch?.status === 0}
              type="tel"
              pattern="[0-9]*"
              error={!!errors.help_line_number}
              helperText={errors.help_line_number}
              inputProps={{
                maxLength: 12,
                onInput: (e) => {
                  e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 12);
                }
              }}
            />


          </Grid>

          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              type="email"
              maxLength={150}
              disabled={id && currentBranch?.status === 0}
              error={!!errors.email} // Show error if there's an error for this field
              helperText={errors.email} // Display the error message

              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={3}>
            <CustomTextField
              label="Branch Manager Name"
              name="branch_manager_name"
              value={formData.branch_manager_name}
              onChange={handleChange}
              disabled={id && currentBranch?.status === 0}
              isRequired
              error={!!errors.branch_manager_name} // Show error if there's an error for this field
              helperText={errors.branch_manager_name}
            />

          </Grid>

          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <CustomTextField
              label="Branch Manager Number"
              name="branch_manager_number"
              value={formData.branch_manager_number}
              onChange={handleChange}
              disabled={id && currentBranch?.status === 0}
              type="tel"
              pattern="[0-9]{10}"
              applyPrefix
              error={!!errors.branch_manager_number} // Show error if there's an error for this field
              helperText={errors.branch_manager_number} // Display the error message
              isRequired
            />
          </Grid>

          <Grid item xs={12}>
            <Box
              sx={{
                backgroundColor: '#f0f0f0',
                padding: '15px',
                borderRadius: '4px',
                height: '60px',
              }}
            >
              <h2>Address Information</h2>
            </Box>
          </Grid>

          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="Address Line 1"
              name="address_line1"
              value={formData.address_line1}
              onChange={handleChange}
              disabled={id && currentBranch?.status === 0}
              error={!!errors.address_line1}
              helperText={errors.address_line1}
            />
            {/*           {errors.address_line1 && <p className="error">{errors.address_line1}</p>}
 */}        </Grid>

          <Grid item xs={3}>
            <CustomTextField
              label="Address Line 2"
              name="address_line2"
              value={formData.address_line2}
              onChange={handleChange}
              disabled={id && currentBranch?.status === 0}
              error={!!errors.address_line2}
              helperText={errors.address_line2}
            />
          </Grid>

          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <CustomTextField
              label="Pincode"
              name="pincode"
              value={formData.pincode}
              pattern="[0-9]{6}"
              onChange={handleChange}
              error={!!errors.pincode}
              disabled={id && currentBranch?.status === 0}
              helperText={errors.pincode}
              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />
          </Grid>

          <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
            <CustomTextField
              label="State"
              name="state"
              value={formData.state}
              disabled // Disable editing


              sx={{
                '& .MuiOutlinedInput-root': {
                  position: 'relative',
                  '&:before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    backgroundColor: 'red',
                  },
                },
              }}
            />

          </Grid>

          <Grid item xs={3}>
            <DropDown
              label="City"
              name="city"
              options={cityOptions}
              value={formData.city}
              onChange={handleChange}
              fullWidth
              disabled={id && currentBranch?.status === 0}
              required
              helperText={errors.city}
            />

          </Grid>

          <Grid item xs={3} style={{ margin: '0 30px' }}>
            <DropDown
              label="Area"
              name="area"
              options={areaOptions}
              value={formData.area}
              onChange={handleChange}
              fullWidth
              disabled={id && currentBranch?.status === 0}

            />
          </Grid>


        </Grid>
      </form>
    </>
  );

};

export default ImfBranchForm;