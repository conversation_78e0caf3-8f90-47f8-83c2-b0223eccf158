const express = require('express');
const subProductController = require('../Controllers/subProductControllers');
const router = express.Router();

// Route to get all roles
router.get('/', subProductController.getAll);

// Route to get a sub product by ID
router.get('/:id', subProductController.getById);

// Route to get sub products by product details
router.get('/product-details/:mainProductId/:insuranceCompanyId/:productMasterId', subProductController.getByProductDetails);

// Route to create a new sub products
router.post('/', subProductController.create);

// Route to update a sub products by ID
router.put('/:id', subProductController.update);

// Route to delete a sub products by ID
router.delete('/:id', subProductController.delete);

// Route to reinstate a sub products by ID
router.put('/reinstate/:id', subProductController.reinstate);

// Route to get sub products by name
router.get('/name/:name', subProductController.getByName)

// Route to get sub products by criteria
router.get('/criteria/:criteria', subProductController.getByCriteria);

module.exports = router;
