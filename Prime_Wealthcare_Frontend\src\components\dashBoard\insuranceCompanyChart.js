import React, { useEffect, useRef } from 'react';
import { Chart } from 'chart.js/auto';
import { formatIndianValue } from '../../utils/Reusable';

const InsuranceCompanyChart = ({ chartData, chartType, title, height = '500px' }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    // Only proceed if we have data
    if (!chartData) return;

    // Create chart based on chart type
    const createChart = () => {
      if (chartRef.current) {
        if (chartInstance.current) {
          chartInstance.current.destroy();
        }

        const ctx = chartRef.current.getContext('2d');

        if (chartType === 'stacked-bar') {
          // Create stacked bar chart
          chartInstance.current = new Chart(ctx, {
            type: 'bar',
            data: {
              labels: chartData.companies,
              datasets: [
                {
                  label: 'NEW',
                  data: chartData.data.NEW,
                  backgroundColor: 'rgba(0, 200, 170, 0.85)',
                  borderWidth: 1,
                  borderColor: 'rgba(0, 200, 170, 1)',
                  borderRadius: 4,
                  premium: chartData.premium?.NEW || []
                },
                {
                  label: 'RENEWAL',
                  data: chartData.data.RENEWAL,
                  backgroundColor: 'rgba(54, 135, 255, 0.85)',
                  borderWidth: 1,
                  borderColor: 'rgba(54, 135, 255, 1)',
                  borderRadius: 4,
                  premium: chartData.premium?.RENEWAL || []
                },
                {
                  label: 'ROLLOVER',
                  data: chartData.data.ROLLOVER,
                  backgroundColor: 'rgba(255, 184, 0, 0.85)',
                  borderWidth: 1,
                  borderColor: 'rgba(255, 184, 0, 1)',
                  borderRadius: 4,
                  premium: chartData.premium?.ROLLOVER || []
                },
                {
                  label: 'MIGRATION',
                  data: chartData.data.MIGRATION,
                  backgroundColor: 'rgba(243, 124, 124, 0.85)',
                  borderWidth: 1,
                  borderColor: 'rgba(243, 124, 124, 1)',
                  borderRadius: 4,
                  premium: chartData.premium?.MIGRATION || []
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              animation: {
                duration: 800,
                easing: 'easeOutQuart'
              },
              scales: {
                x: {
                  stacked: true,
                  title: {
                    display: true,
                    text: 'Insurance Companies',
                    font: {
                      size: 14,
                      weight: 'bold'
                    },
                    color: '#333'
                  },
                  grid: {
                    display: false,
                  }
                },
                y: {
                  stacked: true,
                  title: {
                    display: true,
                    text: 'Number of Proposals',
                    font: {
                      size: 14,
                      weight: 'bold'
                    },
                    color: '#333'
                  },
                  grid: {
                    color: 'rgba(0, 0, 0, 0.05)'  // Lighter grid lines
                  },
                  ticks: {
                    precision: 0
                  }
                }
              },
              plugins: {
                title: {
                  display: true,
                  text: title || 'Proposals by Insurance Company',
                  font: {
                    size: 18,
                    weight: 'bold'
                  },
                  padding: {
                    top: 10,
                    bottom: 30
                  },
                  color: '#333'
                },
                legend: {
                  position: 'bottom',
                  labels: {
                    usePointStyle: true,  // Use point style for a more modern look
                    padding: 15
                  }
                },
                tooltip: {
                  titleFont: {
                    size: 14
                  },
                  bodyFont: {
                    size: 13
                  },
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  callbacks: {
                    label: function (context) {
                      const label = context.dataset.label || '';
                      const value = context.parsed.y || 0;
                      const premium = context.dataset.premium?.[context.dataIndex] || 0;

                      return [
                        `${label}: ${value} proposals`,
                        `Premium: ${formatIndianValue(premium)}`
                      ];
                    }
                  }
                }
              }
            },
            plugins: [{
              id: 'valueLabels',
              afterDatasetsDraw(chart) {
                const { ctx, data } = chart;
                const isMobileView = window.innerWidth <= 768;

                // Calculate stacked totals for each company
                const stackTotals = {};
                const premiumTotals = {};

                data.datasets.forEach((dataset, datasetIndex) => {
                  const meta = chart.getDatasetMeta(datasetIndex);

                  meta.data.forEach((bar, index) => {
                    const value = dataset.data[index] || 0;
                    if (!stackTotals[index]) stackTotals[index] = 0;
                    stackTotals[index] += value;

                    // Premium totals
                    const premium = dataset.premium?.[index] || 0;
                    if (!premiumTotals[index]) premiumTotals[index] = 0;
                    premiumTotals[index] += premium;
                  });
                });

                // Display total premium at the top of each stack
                Object.keys(stackTotals).forEach((index, arrayIndex) => {
                  const stackTotal = stackTotals[index];
                  const premiumTotal = premiumTotals[index] || 0;

                  // Find the topmost visible bar for this stack
                  let topBar = null;
                  for (let i = data.datasets.length - 1; i >= 0; i--) {
                    const meta = chart.getDatasetMeta(i);
                    if (!meta.hidden && meta.data[index] && data.datasets[i].data[index] > 0) {
                      topBar = meta.data[index];
                      break;
                    }
                  }

                  if (topBar && stackTotal > 0) {
                    const { x } = topBar.getCenterPoint();
                    const y = topBar.getProps(['y']).y;

                    ctx.save();
                    ctx.textAlign = 'center';

                    // Adjust font size and position for mobile
                    ctx.font = isMobileView ? 'bold 10px Arial' : 'bold 11px Arial';
                    ctx.fillStyle = '#008000';

                    const formattedAmount = formatIndianValue(premiumTotal, isMobileView);

                    // Adjust vertical spacing and position for mobile
                    let yOffset;
                    if (isMobileView) {
                      yOffset = arrayIndex % 2 === 0 ? -25 : -10;
                    } else {
                      yOffset = -10;
                    }

                    ctx.fillText(formattedAmount, x, y + yOffset);
                    ctx.restore();
                  }
                });

                // Display individual bar values (optional)
                chart.data.datasets.forEach((dataset, datasetIndex) => {
                  const meta = chart.getDatasetMeta(datasetIndex);
                  if (!meta.hidden) {
                    meta.data.forEach((element, index) => {
                      const value = dataset.data[index];
                      if (value > (isMobileView ? 2 : 3)) { // Only show if value is greater than threshold
                        const position = element.getCenterPoint();
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillStyle = '#fff';
                        ctx.font = isMobileView ? '9px Arial' : 'bold 11px Arial';
                        ctx.fillText(value, position.x, position.y);
                      }
                    });
                  }
                });
              }
            }]
          });
        }
      }
    };

    createChart();

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
        chartInstance.current = null;
      }
    };
  }, [chartData, chartType, title]);

  return (
    <div style={{ height, width: '100%' }}>
      {!chartData && (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          background: '#f5f5f5',
          borderRadius: '8px'
        }}>
          <p style={{ color: '#666' }}>No data available</p>
        </div>
      )}
      <canvas ref={chartRef}></canvas>
    </div>
  );
};

export default InsuranceCompanyChart;