import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import Dropdown from '../../../components/table/DropDown';
import { fetchAllImfBranches, fetchInsuranceCompanies } from '../../../redux/actions/action';
import { fetchInsuranceTypes } from '../../../redux/actions/action';
import { updateImfAgencyCode, createImfAgencyCode, updateInsuranceCompanyBranch, fetchInsuranceCompanyBranches, createBranchAndAgencyCode } from '../../../redux/actions/action';
import { fetchImfAgencyCodes } from '../../../redux/actions/action';
import { toast } from 'react-toastify';
import dayjs from 'dayjs'; // <-- Import dayjs
import customParseFormat from 'dayjs/plugin/customParseFormat'; // <-- Import customParseFormat plugin
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers'; // <-- Import DatePicker and LocalizationProvider
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'; // <-- Import AdapterDayjs

dayjs.extend(customParseFormat); // <-- Extend dayjs with customParseFormat


function InsuranceBranchForm() {

  const location = useLocation(); // Capture the location object
  const { agencyCode, branch } = location.state || {}; // Destructure safely from state, fallback to empty object

  const id = branch?.id || agencyCode?.id; // Determine ID based on branch or agencyCode

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const agencyCodes = useSelector(state => state.imfAgencyCodeReducer.agencyCodes);
  const insuranceTypes = useSelector((state) => state.insuranceTypeReducer.insuranceTypes);
  const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
  const insuranceBranches = useSelector(state => state.insuranceBranchReducer.branches);
  const imfBranches = useSelector(state => state.imfBranchReducer.data);


  const isEditMode = id;

  // State management for insurance branch 
  const [branchFormData, setBranchFormData] = useState({
    insurance_company_id: "",
    insurance_co_branch_name: "",
    email_id: "",
    branch_manager_name: "",
    branch_manager_number: "",
    insurance_type: "",
    assistant_branch_manager_name: "",
    assistant_branch_manager_no: "",
    sales_manager_name: "",
    sales_manager_no: "",
    company_percentage: null,
    imf_code: "",
    branch_code: ""
  });

  // State management for agency code form
  const [agencyCodeFormData, setAgencyCodeFormData] = useState({
    agency_code: '',
    agent_name: '',
    license_no: '',
    license_valid_from: '',
    license_valid_till: '',
    imf_branch_id: '',
  });

  const [loading, setLoading] = useState(false); // Add loading state

  useEffect(() => {
    dispatch(fetchInsuranceTypes());
    dispatch(fetchInsuranceCompanies());
    dispatch(fetchAllImfBranches())
    // Dispatch actions to fetch only current user's agency codes and branches
    if (branchFormData.insurance_company_id && branchFormData.insurance_co_branch_name) {
      dispatch(fetchInsuranceCompanyBranches({
        insurance_company_id: branchFormData.insurance_company_id,
        insurance_co_branch_name: branchFormData.insurance_co_branch_name
      }));
      dispatch(fetchImfAgencyCodes({
        insurance_company_id: branchFormData.insurance_company_id,
        insurance_co_branch_name: branchFormData.insurance_co_branch_name
      }));
    }
  }, [dispatch, branchFormData.insurance_company_id, branchFormData.insurance_co_branch_name]);

  // Initialize form data based on edit or create mode
  useEffect(() => {
    // Check if we are in edit mode and data exists for branch or agencyCode
    if (isEditMode) {
      if (branch) {
        setBranchFormData({
          insurance_company_id: branch.insurance_company_id || "",
          insurance_co_branch_name: branch.insurance_co_branch_name || "",
          email_id: branch.email_id || "",
          branch_manager_name: branch.branch_manager_name || "",
          branch_manager_number: branch.branch_manager_number || "",
          insurance_type: branch.insurance_type || "",
          assistant_branch_manager_name: branch.assistant_branch_manager_name || "",
          assistant_branch_manager_no: branch.assistant_branch_manager_no || "",
          sales_manager_name: branch.sales_manager_name || "",
          sales_manager_no: branch.sales_manager_no || "",
          company_percentage: branch.company_percentage || null,
          imf_code: branch.imf_code || "",
          branch_code: branch.branch_code || ""
        });
      }

      if (agencyCode) {
        setAgencyCodeFormData({
          agency_code: agencyCode.agency_code || "",
          agent_name: agencyCode.agent_name || "",
         /*  license_no: agencyCode.license_no || "",
          license_valid_from: agencyCode.license_valid_from
            ? dayjs(agencyCode.license_valid_from, 'DD/MM/YYYY').format('DD/MM/YYYY')
            : "",
          license_valid_till: agencyCode.license_valid_till
            ? dayjs(agencyCode.license_valid_till, 'DD/MM/YYYY').format('DD/MM/YYYY')
            : "", */
          imf_branch_id: agencyCode.imf_branch_id || ""
        });
      }
    }
  }, [branch, isEditMode, agencyCode]);


  const handleBranchChange = (e) => {
    const { name, value } = e.target;

    if (name === 'email_id') {
      // Handle email to lowercase
      setBranchFormData({
        ...branchFormData,
        [name]: value.toLowerCase(),
      });
    } else if (name === 'insurance_company_id') {
      // Find the selected insurance company
      const selectedCompany = insuranceCompanies.find(
        (company) => company.id === value
      );

      // Auto-fill insurance_type based on the selected company
      const autoFilledInsuranceType = selectedCompany ? selectedCompany.insurance_type : '';

      setBranchFormData({
        ...branchFormData,
        [name]: value, // Update insurance_company_id
        insurance_type: autoFilledInsuranceType, // Auto-fill insurance_type
      });
    } else if (
      name === 'branch_manager_number' ||
      name === 'assistant_branch_manager_no' ||
      name === 'sales_manager_no'
    ) {
      // Handle mobile numbers (only 10 digits)
      const formattedNumber = value.replace(/[^0-9]/g, '').slice(0, 10);
      setBranchFormData({
        ...branchFormData,
        [name]: formattedNumber,
      });
    } else if (
      name === 'assistant_branch_manager_name' ||
      name === 'sales_manager_name'
    ) {
      // Allow only letters and spaces, convert to uppercase
      const formattedName = value.replace(/[^a-zA-Z\s]/g, '').toUpperCase();
      setBranchFormData({
        ...branchFormData,
        [name]: formattedName,
      });
    } else if (name === 'company_percentage') {
      // Allow numbers with up to 2 decimal places and restrict to a maximum of 99.99
      const formattedPercentage = value.replace(/[^0-9.]/g, ''); // Remove invalid characters
      const isValidPercentage = /^\d{0,2}(\.\d{0,2})?$/.test(formattedPercentage); // Validate format
      const numericValue = parseFloat(formattedPercentage);
      setBranchFormData({
        ...branchFormData,
        [name]: value === '' ? '' : (isValidPercentage && numericValue <= 99.99 ? formattedPercentage : branchFormData[name]),
      });
    } else if (name === 'insurance_co_branch_name') {
      // Allow hyphens in the branch name
      const formattedBranchName = value.replace(/[^a-zA-Z0-9\s-]/g, '').toUpperCase();
      setBranchFormData({
        ...branchFormData,
        [name]: formattedBranchName,
      });
    } else if (name === 'imf_code' ) {
      // Allow only numbers
      const formattedValue = value.replace(/[^0-9]/g, '');
      setBranchFormData({
        ...branchFormData,
        [name]: formattedValue,
      });
    } else {
      // For all other fields, capitalize the value
      setBranchFormData({
        ...branchFormData,
        [name]: value.toUpperCase(),
      });
    }

    // Clear error messages for the field being edited
    setValidationErrors({
      ...validationErrors,
      [name]: "",
    });
  };

  const [validationErrors, setValidationErrors] = useState({
    insurance_company_id: "",
    insurance_co_branch_name: "",
    email_id: "",
    branch_manager_name: "",
    branch_manager_number: "",
    insurance_type: "",
    assistant_branch_manager_name: "",
    assistant_branch_manager_no: "",
    sales_manager_name: "",
    sales_manager_no: "",
    company_percentage: null,
    agency_code: "",
    agent_name: "",
    license_no: "",
    license_valid_from: "",
    license_valid_till: "",
    imf_branch_id: "",
    branch_code: "",
    imf_code: ""
  });

  // Validation function
  const validateForm = () => {
    let errors = {};

    // Insurance Branch Form Validations
    if (!branchFormData.insurance_company_id) errors.insurance_company_id = "Insurance company is required.";

    if (!branchFormData.insurance_co_branch_name || !/^[a-zA-Z0-9\s-]+$/.test(branchFormData.insurance_co_branch_name))
      errors.insurance_co_branch_name = "Branch name is required and must be alphanumeric with hyphens.";

    if (!branchFormData.imf_code || !/^[0-9]+$/.test(branchFormData.imf_code))
      errors.imf_code = "IMF code is required and must be numeric.";

    if (!branchFormData.branch_code || !/^[a-zA-Z0-9\s]+$/.test(branchFormData.branch_code))
      errors.branch_code = "Branch code is required and must be Alphanumeric.";

    if (branchFormData.email_id && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(branchFormData.email_id))
      errors.email_id = "Invalid email address.";

    if (branchFormData.branch_manager_name && !/^[a-zA-Z\s]+$/.test(branchFormData.branch_manager_name))
      errors.branch_manager_name = "Branch manager name is required and must contain only letters.";

    if (branchFormData.assistant_branch_manager_name && !/^[a-zA-Z\s]+$/.test(branchFormData.assistant_branch_manager_name.trim())) {
      errors.assistant_branch_manager_name = "Assistant branch manager must contain only letters.";
    }

    if (
      branchFormData.sales_manager_name &&
      !/^[a-zA-Z\s]+$/.test(branchFormData.sales_manager_name.trim())
    ) {
      errors.sales_manager_name = "Sales manager must contain only letters.";
    }

    if (
      branchFormData.company_percentage !== null &&
      !/^\d{1,4}(\.\d{1,2})?$/.test(branchFormData.company_percentage.toString().trim())
    ) {
      errors.company_percentage = "Company percentage must be a number up to 4 digits with up to 2 decimal places.";
    }

    if (branchFormData.branch_manager_number && !/^\d{10}$/.test(branchFormData.branch_manager_number))
      errors.branch_manager_number = "Branch manager number must be 10 digits.";

    if (branchFormData.sales_manager_no && !/^\d{10}$/.test(branchFormData.sales_manager_no))
      errors.sales_manager_no = "Sales manager number must be 10 digits.";

    // Agency Code Form Validations
    if (agencyCodeFormData.agency_code && !/^[a-zA-Z0-9\s]+$/.test(agencyCodeFormData.agency_code))
      errors.agency_code = "Agency code is required and must be alphanumeric.";

    if (!agencyCodeFormData.agent_name || !/^[A-Z0-9()\s\-]+$/.test(agencyCodeFormData.agent_name))
      errors.agent_name = "Agent name is required and must contain uppercase letters, numbers, brackets, hyphens, and spaces.";

    if (agencyCodeFormData.license_no && !/^[a-zA-Z0-9\s]+$/.test(agencyCodeFormData.license_no))
      errors.license_no = "License number is required and must be alphanumeric.";

    if (agencyCodeFormData.license_valid_from) errors.license_valid_from = "License valid from date is required.";
    if (agencyCodeFormData.license_valid_till) errors.license_valid_till = "License valid till date is required.";

    // Additional Validation: Valid From should be less than Valid To
    if (agencyCodeFormData.license_valid_from && agencyCodeFormData.license_valid_till) {
      const validFrom = dayjs(agencyCodeFormData.license_valid_from, 'DD/MM/YYYY');
      const validTo = dayjs(agencyCodeFormData.license_valid_till, 'DD/MM/YYYY');

      if (!validFrom.isValid()) {
        errors.license_valid_from = "License valid from date is invalid.";
      }

      if (!validTo.isValid()) {
        errors.license_valid_till = "License valid till date is invalid.";
      }

      if (validFrom.isValid() && validTo.isValid() && validFrom.isAfter(validTo)) {
        errors.license_valid_from = '"License Valid From" must be earlier than "License Valid Till".';
        errors.license_valid_till = '"License Valid Till" must be later than "License Valid From".';
      }

    }
    if (!agencyCodeFormData.imf_branch_id) {
      errors.imf_branch_id = "IMFBranch is required.";
    }



    setValidationErrors(errors);

    // Return true if no errors
    return Object.keys(errors).length === 0;
  }

  const handleSaveAndNew = () => {
    setLoading(true); // Start loading
    if (!validateForm()) {
      setLoading(false); // Stop loading if validation fails
      return;
    }

    // Convert the date fields to 'YYYY-MM-DD' format before sending to backend
    const formattedAgencyCodeData = {
      ...agencyCodeFormData,
      license_valid_from: agencyCodeFormData.license_valid_from
        ? dayjs(agencyCodeFormData.license_valid_from, 'DD/MM/YYYY').format('YYYY-MM-DD')
        : null,
      license_valid_till: agencyCodeFormData.license_valid_till
        ? dayjs(agencyCodeFormData.license_valid_till, 'DD/MM/YYYY').format('YYYY-MM-DD')
        : null,
    };

    const resetForm = () => {
      setBranchFormData({
        insurance_company_id: '',
        insurance_co_branch_name: '',
        email_id: "",
        branch_manager_name: "",
        branch_manager_number: "",
        insurance_type: "",
        assistant_branch_manager_name: "",
        assistant_branch_manager_no: "",
        sales_manager_name: "",
        sales_manager_no: "",
        company_percentage: null,
        imf_code: "",
        branch_code: ""
      });
      setAgencyCodeFormData({
        agency_code: "",
        agent_name: "",
        license_no: "",
        license_valid_from: "",
        license_valid_till: "",
        imf_branch_id: ""
      })
    };

    if (isEditMode) { // In edit mode, update both the branch and agency code
      dispatch(updateInsuranceCompanyBranch({ id, branchData: branchFormData }))
        .then(() => dispatch(updateImfAgencyCode({ id, agencyCodeData: agencyCodeFormData })))
        .then(() => {
          toast.success("Branch and Agency Code updated successfully!");
          resetForm(); // Clear form fields after update
          navigate('/dashboard/insurance-branch');
        })
        .catch((error) => {
          console.error('Error updating branch and agency code:', error);
        });
    } else { // Create mode
      if (branchFormData.insurance_company_id && branchFormData.insurance_co_branch_name) {
        const existingBranch = insuranceBranches.find(branch =>
          branch.insurance_company_id === branchFormData.insurance_company_id &&
          branch.insurance_co_branch_name === branchFormData.insurance_co_branch_name
        );
       /*  const existingAgencyCode = agencyCodes.find(agencyCode =>
          agencyCode.agency_code === agencyCodeFormData.agency_code
        ); */
        if (existingBranch && existingBranch.id !== id) {
          toast.error('Branch with this company and branch name already exists.');
          setValidationErrors(prevErrors => ({
            ...prevErrors,
            insurance_company_id: "Branch with this company and branch name already exists.",
            insurance_co_branch_name: "Branch with this company and branch name already exists."
          }));
          return;
        }
        /* if (existingAgencyCode && existingAgencyCode.id !== id) {
          toast.error('Agency Code already exists.');
          return;
        } */
        const combinedData = {
          branchData: branchFormData,
          agencyCodeData: formattedAgencyCodeData
        };
        dispatch(createBranchAndAgencyCode(combinedData))
          .then(() => {
            toast.success("Branch and Agency Code created successfully!");
            resetForm(); // Clear form fields after creation
            navigate('/dashboard/insurance-branch-form'); // Redirect to new form
            setLoading(false); // Stop loading
          })
          .catch(error => {
            console.error('Error saving branch and agency code:', error);
            setLoading(false); // Stop loading
          });
      } else {
        console.error('Insurance company and branch name are required');
      }
    }
  };

  const handleBranchFormSave = async () => {
    setLoading(true); // Start loading
    // await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate a 2 seconds delay
    if (!validateForm()) {
      setLoading(false); // Stop loading if validation fails
      return;
    }



    // Convert the date fields to 'YYYY-MM-DD' format before sending to backend
    const formattedAgencyCodeData = {
      ...agencyCodeFormData,
      id: isEditMode ? agencyCodeFormData?.id : undefined, // Only send `id` in edit mode

      license_valid_from: agencyCodeFormData.license_valid_from
        ? dayjs(agencyCodeFormData.license_valid_from, 'DD/MM/YYYY').format('YYYY-MM-DD')
        : null,
      license_valid_till: agencyCodeFormData.license_valid_till
        ? dayjs(agencyCodeFormData.license_valid_till, 'DD/MM/YYYY').format('YYYY-MM-DD')
        : null,
    };

    const formattedAgencyCode = {
      // id: isEditMode ? agencyCodeFormData?.id : undefined, // Only send `id` in edit mode

      id: branch ? branch.id : null,// Reinstating the branch's ID
      insurance_company_id: branchFormData.insurance_company_id, // Adding insurance company ID
      insurance_co_branch_name: branchFormData.insurance_co_branch_name, // Adding insurance company branch name
      agency_code: agencyCodeFormData.agency_code,
      agent_name: agencyCodeFormData.agent_name,
      license_no: agencyCodeFormData.license_no,
      license_valid_from: agencyCodeFormData.license_valid_from,
      license_valid_till: agencyCodeFormData.license_valid_till,
      imf_branch_id: agencyCodeFormData.imf_branch_id,
    };



    const branchId = branch?.id || null; // Safeguard branch.id



    if (isEditMode) {
      // If branch status === 0
      if (branch?.status === 0) {
        // Update the branch first

        dispatch(updateInsuranceCompanyBranch({ id, branchData: branchFormData }))
          .then(res => {


            // Create a new agency code for the branch (this will use the branch's ID)
            return dispatch(createImfAgencyCode({ agencyCodeData: formattedAgencyCode }));
          })
          .then(() => {
            // Reinstate the branch by updating its status to 1
            return dispatch(updateInsuranceCompanyBranch({ id, branchData: { ...branchFormData, status: 1 } }));
          })
          .then(() => {
            toast.success('Branch reinstated and new Agency Code created successfully!');
            setLoading(false); // Stop loading
            navigate('/dashboard/insurance-branch');
          })
          .catch((error) => {
            console.error('Error updating branch and creating agency code:', error);
            setLoading(false); // Stop loading
          });
      } else {
        // For branches with status !== 0, update both branch and agency code as usual
        // In edit mode, update both the branch and agency code
        dispatch(updateInsuranceCompanyBranch({ id, branchData: branchFormData }))
          .then(() => {
            setLoading(false); // Stop loading
            return dispatch(updateImfAgencyCode({ id, agencyCodeData: agencyCodeFormData }));
          })

          .then(() => {
            toast.success('Branch and Agency Code updated successfully!');
            setLoading(false); // Stop loading
            navigate('/dashboard/insurance-branch');
          })
          .catch((error) => {
            console.error('Error updating branch and agency code:', error);
            setLoading(false); // Stop loading
          });
      }
    } else {
      // Create mode logic
      if (branchFormData.insurance_company_id && branchFormData.insurance_co_branch_name) {

        const existingBranch = insuranceBranches.find(branch =>
          branch.insurance_company_id === branchFormData.insurance_company_id &&
          branch.insurance_co_branch_name === branchFormData.insurance_co_branch_name
        );

        /* const existingAgencyCode = agencyCodes.find(agencyCode =>
          agencyCode.agency_code === agencyCodeFormData.agency_code
        );
 */
        if (existingBranch && existingBranch.id !== id) {
          toast.error('Branch with this company and branch name already exists.');
          setValidationErrors(prevErrors => ({
            ...prevErrors,
            insurance_company_id: "Branch with this company and branch name already exists.",
            insurance_co_branch_name: "Branch with this company and branch name already exists."
          }));
          setLoading(false); // Stop loading
          return;
        }

       /*  if (existingAgencyCode && existingAgencyCode.id !== id) {
          toast.error('Agency Code already exists.');
          setLoading(false); // Stop loading
          return;
        } */
        const combinedData = {
          branchData: branchFormData,
          agencyCodeData: formattedAgencyCodeData
        };
        dispatch(createBranchAndAgencyCode(combinedData))

          .then(() => {
            toast.success("Branch and Agency Code created successfully!");
            setLoading(false); // Stop loading
            navigate('/dashboard/insurance-branch');
            if (id) {
              navigate('/dashboard/insurance-branch');
            }
          })
          .catch(error => {
            console.error('Error saving branch and agency code:', error);
            setLoading(false); // Stop loading
          });
      } else {
        console.error('Insurance company and branch name are required');
        setLoading(false); // Stop loading
      }
    }
  };

  // Handle change for agency code data with validation for dates
  const handleAgencyCodeChange = (event) => {
    const { name, value } = event.target;
    // If changing license_valid_from or license_valid_till, perform additional validation
    if (name === 'license_valid_from' || name === 'license_valid_till') {
      // Temporarily update the state to access the latest values
      const updatedAgencyCodeFormData = {
        ...agencyCodeFormData,
        [name]: value,
      };

      // Parse dates using dayjs
      const validFrom = dayjs(updatedAgencyCodeFormData.license_valid_from, 'DD/MM/YYYY');
      const validTo = dayjs(updatedAgencyCodeFormData.license_valid_till, 'DD/MM/YYYY');

      if (validFrom.isValid() && validTo.isValid()) {
        if (validFrom.isAfter(validTo)) {
          setValidationErrors(prevErrors => ({
            ...prevErrors,
            license_valid_from: '"License Valid From" must be earlier than "License Valid Till".',
            license_valid_till: '"License Valid Till" must be later than "License Valid From".'
          }));
        } else {
          setValidationErrors(prevErrors => ({
            ...prevErrors,
            license_valid_from: '',
            license_valid_till: ''
          }));
        }
      }
    }

    if (name === 'email_id') {
      // Handle email to lowercase
      setAgencyCodeFormData({
        ...agencyCodeFormData,
        [name]: value.toLowerCase(),
      });
    } else if (name === 'agency_code' || name === 'agent_name') {
      // Handle agency code and agent name to uppercase
      setAgencyCodeFormData({
        ...agencyCodeFormData,
        [name]: value.toUpperCase(),
      });
    } else if (
      name === 'license_valid_from' ||
      name === 'license_valid_till'
    ) {
      // Handle date fields directly without modification
      setAgencyCodeFormData({
        ...agencyCodeFormData,
        [name]: value,
      });
    } else if (
      name === 'license_no'
    ) {
      // Handle license number to uppercase
      setAgencyCodeFormData({
        ...agencyCodeFormData,
        [name]: value.toUpperCase(),
      });
    } else if (name === 'agent_name') {
      // Allow uppercase letters, numbers, brackets, hyphens, and spaces in agent name
      const formattedAgentName = value.replace(/[^A-Z0-9()\s\-]/g, '').toUpperCase();
      setAgencyCodeFormData({
        ...agencyCodeFormData,
        [name]: formattedAgentName,
      });
    } else {
      // For all other fields, just pass through the value
      setAgencyCodeFormData({
        ...agencyCodeFormData,
        [name]: value,
      });
    }

    // Remove error message when user starts typing
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: "",
      });
    }
  };


  const handleCancel = () => {
    navigate('/dashboard/insurance-branch'); // Redirect to list or home page
  };

  return (
    <><form>
      <Grid container spacing={2}>
        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
          <img src="/image.png" alt="module icon" style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }} />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ModuleName moduleName="Insurance Branch" pageName={id ? branch?.status === 0 ? "View" : "Edit" : "Create"} /> </Box>
        </Grid>
        <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
          {id ? (

            <>
              <Button
                variant="outlined"
                size="small"
                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                onClick={handleBranchFormSave}
              >
                Update
              </Button>
            </>
          ) : (
            // Buttons for Create Mode
            <>
              <Button
                variant="outlined"
                size="small"
                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                onClick={handleSaveAndNew}
              >
                Save & New
              </Button>
              <Button
                variant="outlined"
                size="small"
                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                onClick={handleBranchFormSave}
              >
                Save
              </Button>
            </>
          )}
          <Button
            variant="outlined"
            size="small"
            sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </Grid>
        <Grid item xs={12}>
          <Box
            sx={{
              backgroundColor: '#f0f0f0',
              padding: '14px',
              borderRadius: '4px',
              height: '60px',
            }}
          >
            <h2>Insurance Branch Information</h2>
          </Box>
        </Grid>

        <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
          <Dropdown
            label="Insurance Company"
            name="insurance_company_id"
            // options={insuranceCompanies.map(company => ({ label: company.insurance_company_name, value: company.id }))}
            options={
              insuranceCompanies
                .filter(company => company.status !== 0) // Only include companies with status other than 0
                .map(company => ({ label: company.insurance_company_name, value: company.id }))
            }
            value={branchFormData.insurance_company_id}
            onChange={handleBranchChange} // Use the generic handler
            fullWidth
            disabled={isEditMode}
            error={!!validationErrors.insurance_company_id}
            helperText={validationErrors.insurance_company_id}
            isRequired
          />
        </Grid>
        <Grid item xs={3}  >
          <CustomTextField
            label="Branch Name"
            name="insurance_co_branch_name"
            value={branchFormData.insurance_co_branch_name}
            onChange={handleBranchChange}
            error={!!validationErrors.insurance_co_branch_name}
            helperText={validationErrors.insurance_co_branch_name}
            disabled={isEditMode}
            isRequired
          />
        </Grid>
        <Grid item xs={3} style={{ margin: '0 30px' }}>
          <Dropdown
            label="Insurance Type"
            name="insurance_type"
            options={[
              { label: 'None', value: '' },
              ...insuranceTypes.map(type => ({ label: type.type_name, value: type.type_name })),
            ]}
            value={branchFormData.insurance_type}
            onChange={handleBranchChange}
            fullWidth
            disabled
            required
          />
        </Grid>
        <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
          <CustomTextField
            label="IMF Code"
            name="imf_code"
            value={branchFormData.imf_code}
            onChange={handleBranchChange}
            error={!!validationErrors.imf_code}
            helperText={validationErrors.imf_code}
            isRequired
          />
        </Grid>
        <Grid item xs={3} >
          <CustomTextField
            label="Branch Code"
            name="branch_code"
            value={branchFormData.branch_code}
            onChange={handleBranchChange}
            error={!!validationErrors.branch_code}
            helperText={validationErrors.branch_code}
            isRequired
          />
        </Grid>
        <Grid item xs={3} style={{ margin: '0 30px' }}>
          <CustomTextField
            label="Email Id"
            name="email_id"
            value={branchFormData.email_id || ''}
            onChange={handleBranchChange}
            error={!!validationErrors.email_id}
            helperText={validationErrors.email_id}
          />

        </Grid>

        <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
          <CustomTextField
            label="Branch Manager Name"
            name="branch_manager_name"
            value={branchFormData.branch_manager_name}
            onChange={handleBranchChange}
            error={!!validationErrors.branch_manager_name}
            helperText={validationErrors.branch_manager_name}
          />
        </Grid>
        <Grid item xs={3} >
          <CustomTextField
            label="Branch Manager Number"
            name="branch_manager_number"
            value={branchFormData.branch_manager_number}
            onChange={handleBranchChange}
            error={!!validationErrors.branch_manager_number}
            helperText={validationErrors.branch_manager_number}
          />
        </Grid>

        <Grid item xs={3} style={{ margin: '0 30px' }}>
          <CustomTextField
            label="Assistant Branch Manager Name"
            name="assistant_branch_manager_name"
            value={branchFormData.assistant_branch_manager_name}
            onChange={handleBranchChange}
            error={!!validationErrors.assistant_branch_manager_name}
            helperText={validationErrors.assistant_branch_manager_name}
          />
        </Grid>

        <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
          <CustomTextField
            label="Assistant Branch Manager Number"
            name="assistant_branch_manager_no"
            value={branchFormData.assistant_branch_manager_no}
            onChange={handleBranchChange}
            error={!!validationErrors.assistant_branch_manager_no}
            helperText={validationErrors.assistant_branch_manager_no}

          />
        </Grid>

        <Grid item xs={3} >
          <CustomTextField
            label="Sales Manager Name"
            name="sales_manager_name"
            value={branchFormData.sales_manager_name}
            onChange={handleBranchChange}
            error={!!validationErrors.sales_manager_name}
            helperText={validationErrors.sales_manager_name}
          />
        </Grid>

        <Grid item xs={3} style={{ margin: '0 30px' }}>
          <CustomTextField
            label="Sales Manager Number"
            name="sales_manager_no"
            value={branchFormData.sales_manager_no}
            onChange={handleBranchChange}
            error={!!validationErrors.sales_manager_no}
            helperText={validationErrors.sales_manager_no}
          />
        </Grid>

        <Grid item xs={3} style={{ marginLeft: '500px', marginRight: '30px' }}>
          <CustomTextField
            label="Company Percentage"
            name="company_percentage"
            value={branchFormData.company_percentage}
            onChange={handleBranchChange}
            error={!!validationErrors.company_percentage}
            helperText={validationErrors.company_percentage}
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2, display: 'flex', justifyContent: 'space-between' }}>
            <h2>Agency Code Information</h2>

          </Box>
        </Grid>

        <Grid item xs={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
          <CustomTextField
            label="IMD Child Code"
            name="agency_code"
            value={agencyCodeFormData.agency_code}
            onChange={handleAgencyCodeChange}
            error={!!validationErrors.agency_code}
            helperText={validationErrors.agency_code}
          />
        </Grid>

        <Grid item xs={3}>
          <CustomTextField
            label="IMD Name"
            name="agent_name"
            value={agencyCodeFormData.agent_name}
            onChange={handleAgencyCodeChange}
            error={!!validationErrors.agent_name}
            helperText={validationErrors.agent_name}
            isRequired
          />
        </Grid>

        <Grid item xs={3} sx={{  marginBottom: '50px'}}>
        <Dropdown
            label="IMF Branch Name"
            name="imf_branch_id"
            value={agencyCodeFormData.imf_branch_id}
            //options={imfBranches.map(branch => ({ label: branch.branch_name, value: branch.id }))}
            options={
              imfBranches
                .filter(branch => branch.status !== 0) // Only include branches with status other than 0 (active)
                .map(branch => ({ label: branch.branch_name, value: branch.id }))
            }

            onChange={handleAgencyCodeChange}
            fullWidth
            error={Boolean(validationErrors.imf_branch_id)}
            helperText={validationErrors.imf_branch_id}
            required
            sx={{
              '& .MuiOutlinedInput-root': {
                position: 'relative',
                '&:before': {
                  content: '""',
                  position: 'absolute',

                },
              },
            }}
          />
          
        </Grid>


       {/*  <Grid item xs={12} sm={6} md={3} style={{ marginLeft: '150px', marginRight: '30px' }}>
        <CustomTextField
            label="License No."
            name="license_no"
            value={agencyCodeFormData.license_no}
            onChange={handleAgencyCodeChange}
            error={!!validationErrors.license_no}
            helperText={validationErrors.license_no}
          />
          
        </Grid>

        <Grid item xs={3} >
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label="License Valid From"
              name="license_valid_from"
              value={agencyCodeFormData.license_valid_from ? dayjs(agencyCodeFormData.license_valid_from, 'DD/MM/YYYY') : null}
              onChange={(newValue) => {
                const formattedDate = newValue ? newValue.format('DD/MM/YYYY') : '';
                handleAgencyCodeChange({ target: { name: 'license_valid_from', value: formattedDate || null } });
              }}
              /* slotProps={{
                textField: {
                  fullWidth: true,
                  error: Boolean(validationErrors.license_valid_from),
                  helperText: validationErrors.license_valid_from,
                  sx: {
                    '& .MuiOutlinedInput-root': {
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '3px',
                        backgroundColor: 'red', // Red line on the left
                        zIndex: 1,
                      },
                    },
                  },
                },
              }} */
            /*   format="DD/MM/YYYY"
              sx={{ width: '100%' }} // Ensures full width
            />
          </LocalizationProvider>
        </Grid>

        <Grid item xs={3} style={{ margin: '0 1px' }}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label="License Valid Till"
              name="license_valid_till"
              value={agencyCodeFormData.license_valid_till ? dayjs(agencyCodeFormData.license_valid_till, 'DD/MM/YYYY') : null}
              minDate={agencyCodeFormData.license_valid_from ? dayjs(agencyCodeFormData.license_valid_from, 'DD/MM/YYYY') : null}
              onChange={(newValue) => {
                const formattedDate = newValue ? newValue.format('DD/MM/YYYY') : '';
                handleAgencyCodeChange({ target: { name: 'license_valid_till', value: formattedDate || null } });
              }} */
              /*  slotProps={{
                 textField: {
                   fullWidth: true,
                   error: Boolean(validationErrors.license_valid_till),
                   helperText: validationErrors.license_valid_till,
                   sx: {
                     '& .MuiOutlinedInput-root': {
                       position: 'relative',
                       '&::before': {
                         content: '""',
                         position: 'absolute',
                         left: 0,
                         top: 0,
                         bottom: 0,
                         width: '3px',
                         backgroundColor: 'red', // Red line on the left
                         zIndex: 1,
                       },
                     },
                   },
                 },
               }} */
             /*  format="DD/MM/YYYY"
              sx={{ width: '100%' }} // Ensures full width
            />
          </LocalizationProvider>
          
        </Grid> */} 
      </Grid>

    </form>

    </>
  );
};

export default InsuranceBranchForm;