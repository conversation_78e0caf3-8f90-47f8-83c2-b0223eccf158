import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
    deleteDisease,
    getAllDiseases,
    reinstateDisease,
    getDiseaseByName,
    getFilterDiseaseData,
    getFilterRoles,
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';


function DiseaseTypePage() {
    const [selectedOption, setSelectedOption] = useState('none');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const diseases = useSelector(state => state.diseaseMasterReducer.diseases);
    const [sortedDiseases, setSortedDiseases] = useState(diseases || []);
    const  permissions  = usePermissions('Master', 'Disease Master');

    useEffect(() => {
        dispatch(getAllDiseases());
    }, [dispatch])
    useEffect(() => {

        dispatch(getFilterDiseaseData(selectedOption));
    }, [selectedOption, dispatch]);
    useEffect(() => {

        if (statusFilter === 'all') {
            setSortedDiseases(diseases);
        } else if (statusFilter === 'none') {
            dispatch(getAllDiseases());
        } else {
            setSortedDiseases(diseases.filter(disease => disease.status === (statusFilter === 'active' ? 1 : 0)));
        }
    }, [statusFilter, diseases]);

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleConfirmDelete = () => {
        dispatch(deleteDisease(selectedItem.id))
            .then(() => {
                dispatch(getAllDiseases());
                setOpenDeletePopup(false);
                setOpenSuccessPopup(true);
            })
            .catch(error => {
                console.error('Failed to delete disease:', error);
                setOpenDeletePopup(false);
            });
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/disease-master-form');
    };

    const handleDelete = (id) => {
        handleOpenDeletePopup(diseases.find(disease => disease.id === id));
    };

    const handleReinstate = (id) => {
        dispatch(reinstateDisease(id))
            .then(() => {
                dispatch(getAllDiseases());
            })
            .catch(error => {
                console.error('Failed to reinstate disease:', error);
            });
    };

    const handleEdit = (id) => {
        navigate(`/dashboard/disease-master-form/edit/${id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedDiseases.map(disease => disease.id) : []);
    };

    const onSearch = (query) => {
        if (query === '') {
            dispatch(getAllDiseases());
        } else {
            dispatch(getDiseaseByName(query));
        }
    };

    const handleAllClick = () => setStatusFilter('all');
    const handleActiveClick = () => setStatusFilter('active');
    const handleInactiveClick = () => setStatusFilter('inactive');
    const handleRefreshClick = () => {
        setSelectedOption('none');
        dispatch(getAllDiseases());
    }

    const columns = [
        { field: 'disease_name', headerName: 'Disease Name' },
        { field: 'disease_description', headerName: 'Disease Description' },
    ];

    const dataMapping = {
        ID: 'id',
        'Disease Name': 'disease_name',
        'Disease Description': 'disease_description',
        'Status': 'status',
    };

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Disease Type" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                        <Button onClick={handleAdd} sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}>
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={sortedDiseases.map(disease => ({
                                ...disease,
                                status: disease.status === 1 ? 'Active' : 'Inactive'
                            }))}
                            headNames={['ID', 'Disease Name', 'Disease Description', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="diseases.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Disease Report"
                        />
                    </ButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingInline: '1rem', ml: 5 }}>
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SearchBar placeholder="Search..." onSearch={onSearch} />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>

                <Box sx={{ mt: -1, maxHeight: '400px' }}>
                    <CustomTable
                        data={sortedDiseases}
                        columns={columns}
                        onDelete={permissions.can_delete ? handleDelete : null}
                        onEdit={permissions.can_edit ? handleEdit : null}
                        onReinstate={handleReinstate}
                        onSelectionChange={handleSelectionChange}
                        selectedRows={selectedRows}
                        onSelectAll={handleSelectAll}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.disease_name : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.disease_name : ''}
            />
        </Container>
    )
}

export default DiseaseTypePage