// controllers/pickListController.js
const PA_occupation_list = require('../Models/PA_occupation_list');

exports.getAllPA_occupation_list = async (req, res) => {
    try {
        const pickLists = await PA_occupation_list.getAll();
        res.status(200).json(pickLists);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching pick lists', error: error.message });
    }
};

exports.getPA_occupation_listByTypeName = async (req, res) => {
    const { typeName } = req.params;
    try {
        const pickLists = await PA_occupation_list.getByTypeName(typeName);
        res.status(200).json(pickLists);
    } catch (error) {
        res.status(500).json({ message: `Error fetching pick lists for type: ${typeName}`, error: error.message });
    }
};

exports.createPA_occupation_list = async (req, res) => {
    const pickListData = req.body;
    try {
        const newPA_occupation_list = await PA_occupation_list.create(pickListData);
        res.status(201).json(newPA_occupation_list);
    } catch (error) {
        res.status(500).json({ message: 'Error creating pick list', error: error.message });
    }
};

exports.updatePA_occupation_list = async (req, res) => {
    const { id } = req.params;
    const pickListData = req.body;
    try {
        const updatedPA_occupation_list = await PA_occupation_list.update(id, pickListData);
        res.status(200).json(updatedPA_occupation_list);
    } catch (error) {
        res.status(500).json({ message: `Error updating pick list with id: ${id}`, error: error.message });
    }
};

exports.deletePA_occupation_list = async (req, res) => {
    const { id } = req.params;
    try {
        await PA_occupation_list.delete(id);
        res.status(200).json({ message: 'Pick list item deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: `Error deleting pick list item with id: ${id}`, error: error.message });
    }
};

exports.softDeletePA_occupation_list = async (req, res) => {
    const { id } = req.params;
    try {
        const softDeletedPA_occupation_list = await PA_occupation_list.softDelete(id);
        res.status(200).json(softDeletedPA_occupation_list);
    } catch (error) {
        res.status(500).json({ message: `Error soft-deleting pick list item with id: ${id}`, error: error.message });
    }
};

exports.reinstatePA_occupation_list = async (req, res) => {
    const { id } = req.params;
    try {
        const reinstatedPA_occupation_list = await PA_occupation_list.reinstate(id);
        res.status(200).json(reinstatedPA_occupation_list);
    } catch (error) {
        res.status(500).json({ message: `Error reinstating pick list item with id: ${id}`, error: error.message });
    }
};
