import { createSlice } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import {
    getAllCustomerAddresses,
    getCustomerAddressById,
    createCustomerAddress,
    updateCustomerAddress,
    deleteCustomerAddress,
    getCustomerAddressByCustomerId,
} from '../../actions/action';

const initialState = {
    addresses: [], // List of all addresses
    currentAddress: null, // For viewing or editing a single address
    currentAddresses: null, // For viewing or editing a single address by customer id
    loading: false,
    error: null,
};

const customer_address_slice = createSlice({
    name: 'customerAddress',
    initialState,
    reducers: {
        clearCurrentAddress(state) {
            state.currentAddress = null; // Clear current address details
            state.currentAddresses = null;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Get all customer addresses
            .addCase(getAllCustomerAddresses.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getAllCustomerAddresses.fulfilled, (state, action) => {
                state.loading = false;
                state.addresses = action.payload;
            })
            .addCase(getAllCustomerAddresses.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch customer addresses');
            })

            // Get a customer address by ID
            .addCase(getCustomerAddressById.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getCustomerAddressById.fulfilled, (state, action) => {
                state.loading = false;
                state.currentAddresses = action.payload;
            })
            .addCase(getCustomerAddressById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch customer address');
            })

             // Get a customer address by Customer ID
             .addCase(getCustomerAddressByCustomerId.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.currentAddress = null;
            })
            .addCase(getCustomerAddressByCustomerId.fulfilled, (state, action) => {
                state.loading = false;
                if (action.payload === null || action.payload === undefined || action.payload === '') {
                    state.currentAddress = null;
                } else {
                    state.currentAddress = action.payload;
                }
            })
            .addCase(getCustomerAddressByCustomerId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch customer address');
            })

            // Create a customer address
            .addCase(createCustomerAddress.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createCustomerAddress.fulfilled, (state, action) => {
                state.loading = false;
                state.addresses.push(action.payload); // Add new address to list
                toast.success('Customer address created successfully');
            })
            .addCase(createCustomerAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to create customer address');
            })

            // Update a customer address
            .addCase(updateCustomerAddress.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(updateCustomerAddress.fulfilled, (state, action) => {
                state.loading = false;
                const index = state.addresses.findIndex((addr) => addr.id === action.payload.id);
                if (index !== -1) {
                    state.addresses[index] = action.payload; // Update address in list
                }
                toast.success('Customer address updated successfully');
            })
            .addCase(updateCustomerAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to update customer address');
            })

            // Delete a customer address
            .addCase(deleteCustomerAddress.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(deleteCustomerAddress.fulfilled, (state, action) => {
                state.loading = false;
                state.addresses = state.addresses.filter((addr) => addr.id !== action.payload); // Remove address
                toast.success('Customer address deleted successfully');
            })
            .addCase(deleteCustomerAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to delete customer address');
            });
    },
});

export const { clearCurrentAddress } = customer_address_slice.actions;

export default customer_address_slice.reducer;
