const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');


const createPayment = async (data) => {
    try {
        const result = await db('payment_master').insert(data);
        return result;
    } catch (error) {
        console.error("Error creating payment:", error);
        throw error;
    }
};

const updatePayment = async (id, data) => {
    try {
        const updatedData = {
            ...data,
            updated_at: getCurrentTimestamp()
        }
        const result = await db('payment_master').where('id', id).update(updatedData);
        return result;
    } catch (error) {
        console.error("Error updating payment:", error);
        throw error;
    }
};

const updatePaymentStatus = async (id, data) => {
    try {
        const updatedData = {
            ...data,
            updated_at: getCurrentTimestamp()
        }
        const result = await db('payment_master').where('id', id).update(updatedData);
        return result;
    } catch (error) {
        console.error("Error updating payment status:", error);
        throw error;
    }
};


const checkIfSuccess = async (proposal_number) => {
    try {
        // First try to get SUCCESS status payment
        let result = await db('payment_master')
            .where({
                'ProposalNumber': proposal_number,
                'status': 'SUCCESS'
            })
            .first();

        // If no SUCCESS payment found, get the latest payment
        if (!result) {
            result = await db('payment_master')
                .where('ProposalNumber', proposal_number)
                .orderBy('updated_at', 'desc')
                .first();
        }
        return result;
    } catch (error) {
        console.error("Error checking if success:", error);
        throw error;
    }
}

const create = async (data) => {
    try {
        const [result] = await db('payment_master').insert(data);
        return result;
    }
    catch (error) {
        console.error("Error creating payment:", error);
        throw error;
    }
}

const getByProposalNumber = async (proposal_number) => {
    try {
        const result = await db('payment_master')
            .where({ 'ProposalNumber': proposal_number })
            .orderBy('updated_at', 'desc')
            .first();
        return result;
    } catch (error) {
        console.error("Error fetching payment by proposal number:", error);
        throw error;
    }
}

module.exports = {
    createPayment,
    create,
    updatePayment,
    updatePaymentStatus,
    checkIfSuccess,
    getByProposalNumber
};
