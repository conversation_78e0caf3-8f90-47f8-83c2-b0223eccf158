// Ensure required modules are imported
const path = require('path');
const fs = require('fs');
const multer = require('multer');

// Define the base upload directory from environment variable
const uploadDir = process.env.UPLOAD_DIR;

// Check if the base uploads directory exists; if not, create it
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage settings
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Ensure agent_id is a string
    const agentId = Array.isArray(req.body.agent_id) ? req.body.agent_id[0] : req.body.agent_id;
    // Create agent-specific folder path
    const agentFolder = path.join(uploadDir, 'agent', agentId);

    // Create the folder if it doesn't exist
    if (!fs.existsSync(agentFolder)) {
      fs.mkdirSync(agentFolder, { recursive: true });
    }

    cb(null, agentFolder);
  },
  filename: (req, file, cb) => {
    const extension = path.extname(file.originalname);
    const filename = `${file.fieldname}${extension}`;
    cb(null, filename);
  },
});

// Add file filter to restrict file types
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only jpg, jpeg, png, and pdf files are allowed.'), false);
  }
};

// Create the multer instance with both storage and fileFilter
const upload = multer({
  storage,
  fileFilter
});

module.exports = upload;