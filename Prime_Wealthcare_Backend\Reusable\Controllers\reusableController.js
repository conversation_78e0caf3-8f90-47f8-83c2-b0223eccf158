const UserDetails = require('../Models/reusable');
const reusableModel = require('../Models/reusable');

exports.getEmployeeOrAgentById = async (req, res) => {
    const id = req.params.id;
    const data = await UserDetails.findById(id);
    res.status(200).json(data);
}

exports.getMasterProductsByQuotationId = async (req, res) => {
    const quotation_id = req.params.quotation_id;
    const data = await UserDetails.getMasterProductsByQuotationId(quotation_id);
    res.status(200).json(data);
}

exports.getNomineeRelations = async (req, res) => {
    const data = await UserDetails.getNomineeRelations();
    res.status(200).json(data);
}

exports.getAllUsers = async (req, res) => {
    try {
        const users = await reusableModel.getAllUsers();
        res.json({ success: true, data: users });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ success: false, message: 'Error fetching users' });
    }
};
