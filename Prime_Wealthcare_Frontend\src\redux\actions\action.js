import axios from '../../api/axios';
import { createAsyncThunk } from '@reduxjs/toolkit';
import instance from '../../api/axios';
import { toast } from 'react-toastify';
import { logout } from '../slices/authSlice'; // Import the logout action from the slice
import { getToken } from '../../utils/storage'; // You'll need to create this


// For form data logging
const logFormData = (formData) => {
    for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
            console.log(`📎 ${key}: File - ${value.name} (${value.type}, ${value.size} bytes)`);
        } else {
            console.log(`📝 ${key}: ${value}`);
        }
    }
};

// Reusable API
export const getEmployeeOrAgentById = createAsyncThunk(
    'reusable/getEmployeeOrAgentById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/reusable/getEmployeeOrAgentById/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to fetch all users (employees and agents)
export const fetchAllUsers = createAsyncThunk(
    'users/fetchAllUsers', // Corrected action type
    async (_, { rejectWithValue }) => {
        try {

            const response = await axios.get('/reusable/getAllUsers', {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('token')}` // Ensure token is included
                }
            });

            return response.data; // Return the data from the response
        } catch (error) {
            console.error('API request error:', error);
            return rejectWithValue(error.response?.data || { message: 'Network error' });
        }
    }
);

export const getMasterProductsByQuotationId = createAsyncThunk(
    'reusable/getMasterProductsByQuotationId',
    async (quotation_id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/reusable/getMasterProductsByQuotationId/${quotation_id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getNomineeRelations = createAsyncThunk(
    'reusable/getNomineeRelations',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/reusable/getNomineeRelations`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Roles API
export const getAllRoles = createAsyncThunk(
    'roles/getRoles',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/roles');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createRole = createAsyncThunk(
    'roles/createRole',
    async (data, { rejectWithValue }) => {
        try {
            const response = await instance.post('/roles', data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getRoleById = createAsyncThunk(
    'roles/getRoleById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/roles/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getRolesByName = createAsyncThunk(
    'roles/getRoleByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/roles/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateRole = createAsyncThunk(
    'roles/updateRole',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/roles/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteRole = createAsyncThunk(
    'roles/deleteRole',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/roles/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateRole = createAsyncThunk(
    'roles/reinstateRole',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/roles/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getFilterRoles = createAsyncThunk(
    'roles/filter',
    async (searchRoles, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/roles/criteria/${searchRoles}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Locations API
export const getAllLocations = createAsyncThunk(
    'locations/getLocations',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/locations');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createLocation = createAsyncThunk(
    'locations/createLocation',
    async (data, { rejectWithValue }) => {
        try {
            const response = await instance.post('/locations', data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//used
export const getLocationByPincode = createAsyncThunk(
    'locations/getLocationByPincode',
    async (pincode, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/pincode/${pincode}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getLocationByPincodeAndCity = createAsyncThunk(
    'locations/getLocationByPincodeAndCity',
    async ({ pincode, city }, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/pincode/${pincode}/${city}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAreasByPincode_City = createAsyncThunk(
    'locations/getAreasByPincode_City',
    async (pincode_city, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/areas/${pincode_city}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getLocationById = createAsyncThunk(
    'locations/getLocationById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateLocationByPincode = createAsyncThunk(
    'locations/updateLocationByPincode',
    async ({ pincode, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/locations/${pincode}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteLocationByPincode = createAsyncThunk(
    'locations/deleteLocationByPincode',
    async (selectedItem, { rejectWithValue }) => {
        try {
            await instance.delete(`/locations/${selectedItem.location_pincode}/${selectedItem.city_name}`);
            return selectedItem.pincode;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getStatesByLetter = createAsyncThunk(
    'locations/getStatesByLetter',
    async (letter, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/state/${letter}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getCitiesByLetter = createAsyncThunk(
    'locations/getCitiesByLetter',
    async (letter, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/city/${letter}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAreasByLetter = createAsyncThunk(
    'locations/getAreasByLetter',
    async (letter, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/area/${letter}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//used
export const getLocationDetailsByPincode = createAsyncThunk(
    'locations/getLocationDetailsByPincode',
    async (pincode, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/details/${pincode}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getLocationByCriteria = createAsyncThunk(
    'locations/getLocationByCriteria',
    async (searchCriteria, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/criteria/${searchCriteria}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getLocationByName = createAsyncThunk(
    'locations/getLocationByName',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/locations/search/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Areas API
export const getAllAreas = createAsyncThunk(
    'areas/getAreas',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/areas');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAreasByPincodeAndCity = createAsyncThunk(
    'areas/getAreasByPincodeAndCity',
    async ({ pincode, city }, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/areas/${pincode}/${city}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAreaById = createAsyncThunk(
    'areas/getAreasById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/areas/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createArea = createAsyncThunk(
    'areas/createArea',
    async (data, { rejectWithValue }) => {
        try {
            const response = await instance.post('/areas', data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateArea = createAsyncThunk(
    'areas/updateArea',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/areas/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteArea = createAsyncThunk(
    'areas/deleteArea',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/areas/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getLocationsWithAreas = createAsyncThunk(
    'locations/getLocationsWithAreas',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/locations/areas');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Main Product
export const getAllProducts = createAsyncThunk(
    'main-product/getAllProducts',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/main-product');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getProductById = createAsyncThunk(
    'main-product/getProductById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/main-product/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getProductByName = createAsyncThunk(
    'main-product/getProductByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/main-product/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createProduct = createAsyncThunk(
    'main-product/createProduct',
    async (productData, { dispatch, rejectWithValue }) => {
        try {
            const response = await instance.post('/main-product', productData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateProduct = createAsyncThunk(
    'main-product/updateProduct',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/main-product/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteProduct = createAsyncThunk(
    'main-product/deleteProduct',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/main-product/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateProduct = createAsyncThunk(
    'main-product/reinstateProduct',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/main-product/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const searchProduct = createAsyncThunk(
    'main-product/searchProduct',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/main-product/name/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getFilterData = createAsyncThunk(
    'main-product/filter',
    async (searchProduct, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/main-product/criteria/${searchProduct}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Master Products
export const getAllMasterProducts = createAsyncThunk(
    'master-product/getAllMasterProducts',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/product-master');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getMasterProductByMainProductAndInsuranceCompany = createAsyncThunk(
    'master-product/getMasterProductByMainProductAndInsuranceCompany',
    async ({ mainProductId, insuranceCompanyId }, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/product-master/getMasterProductByMainProductAndInsuranceCompany/${mainProductId}/${insuranceCompanyId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createMasterProduct = createAsyncThunk(
    'master-product/createMasterProduct',
    async (productData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/product-master', productData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getMasterProductById = createAsyncThunk(
    'master-product/getMasterProductById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/product-master/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateMasterProduct = createAsyncThunk(
    'master-product/updateMasterProduct',
    async ({ id, productData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/product-master/${id}`, productData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteMasterProduct = createAsyncThunk(
    'master-product/deleteMasterProduct',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/product-master/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateMasterProduct = createAsyncThunk(
    'master-product/reinstateMasterProduct',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/product-master/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const searchMasterProduct = createAsyncThunk(
    'product-master/searchMasterProduct',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/product-master/name/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getFilterMasterProducts = createAsyncThunk(
    'master-product/filter',
    async (searchProduct, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/product-master/criteria/${searchProduct}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Sub Products
export const getAllSubProducts = createAsyncThunk(
    'sub-product/getAllSubProducts',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/sub-product');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getSubProductByProductDetails = createAsyncThunk(
    'sub-product/getSubProductByProductDetails',
    async ({ mainProductId, insuranceCompanyId, productMasterId }, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/sub-product/product-details/${mainProductId}/${insuranceCompanyId}/${productMasterId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createSubProduct = createAsyncThunk(
    'sub-product/createSubProduct',
    async (productData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/sub-product', productData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getSubProductById = createAsyncThunk(
    'sub-product/getSubProductById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/sub-product/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateSubProduct = createAsyncThunk(
    'sub-product/updateSubProduct',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/sub-product/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteSubProduct = createAsyncThunk(
    'sub-product/deleteSubProduct',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/sub-product/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateSubProduct = createAsyncThunk(
    'sub-product/reinstateSubProduct',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/sub-product/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const searchSubProduct = createAsyncThunk(
    'product-master/searchSubProduct',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/sub-product/name/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getFilterSubProducts = createAsyncThunk(
    'sub-product/filterSubProduct',
    async (searchProduct, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/sub-product/criteria/${searchProduct}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Endorsment Type
export const getAllEndorsments = createAsyncThunk(
    'endorsment-type/getAllEndorsments',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/endorsment-type');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getEndorsmentById = createAsyncThunk(
    'endorsment-type/getEndorsmentById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/endorsment-type/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getEndorsmentByName = createAsyncThunk(
    'endorsment-type/getEndorsmentByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/endorsment-type/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createEndorsment = createAsyncThunk(
    'endorsment-type/createEndorsment',
    async (endorsmentData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/endorsment-type', endorsmentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateEndorsment = createAsyncThunk(
    'endorsment-type/updateEndorsment',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/endorsment-type/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteEndorsment = createAsyncThunk(
    'endorsment-type/deleteEndorsment',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/endorsment-type/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateEndorsment = createAsyncThunk(
    'endorsment-type/reinstateEndorsment',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/endorsment-type/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const searchEndorsment = createAsyncThunk(
    'endorsment-type/searchEndorsment',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/endorsment-type/name/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getFilterEndorsmentData = createAsyncThunk(
    'endorsment-type/filter',
    async (searchEndorsment, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/endorsment-type/criteria/${searchEndorsment}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//Disease Master Details
export const getAllDiseases = createAsyncThunk(
    'disease-master/getAllDiseases',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/disease-master');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getDiseaseById = createAsyncThunk(
    'disease-master/getDiseaseById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/disease-master/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getDiseaseByName = createAsyncThunk(
    'disease-master/getDiseaseByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/disease-master/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createDisease = createAsyncThunk(
    'disease-master/createDisease',
    async (diseaseData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/disease-master', diseaseData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateDisease = createAsyncThunk(
    'disease-master/updateDisease',
    async ({ id, filteredData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/disease-master/${id}`, filteredData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteDisease = createAsyncThunk(
    'disease-master/deleteDisease',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/disease-master/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateDisease = createAsyncThunk(
    'disease-master/reinstateDisease',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/disease-master/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const searchDisease = createAsyncThunk(
    'disease-master/searchDisease',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/disease-master/name/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getFilterDiseaseData = createAsyncThunk(
    'disease-master/filter',
    async (searchDisease, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/disease-master/criteria/${searchDisease}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Sum Insured Details
export const createSumInsured = createAsyncThunk(
    'sub-product/createSumInsured',
    async (sumInsuredData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/sub-product-age-sum', sumInsuredData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)
export const updateSumInsured = createAsyncThunk(
    'sub-product/updateSumInsured',
    async ({ id, sumInsuredData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/sub-product-age-sum/${id}`, sumInsuredData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)
export const deleteSumInsured = createAsyncThunk(
    'sub-product/deleteSumInsured',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/sub-product-age-sum/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

// Rider Details
export const createRidersDetails = createAsyncThunk(
    'sub-product/createRidersDetails',
    async (riderData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/sub-product-rider-details', riderData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

export const updateRiderDetails = createAsyncThunk(
    'sub-procuct/updateRiderDetails',
    async ({ id, riderData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/sub-product-rider-details/${id}`, riderData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

export const deleteRiderDetails = createAsyncThunk(
    'sub-product/deleteRiderDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/sub-product-rider-details/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

// Networks
export const getAllNetworks = createAsyncThunk(
    'networks/getAllNetworks',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/network');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getNetworkByInsuranceCompanyId = createAsyncThunk(
    'networks/getNetworkByInsuranceCompanyId',
    async (insuranceCompanyId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/network/insurance-company/${insuranceCompanyId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getNetworkById = createAsyncThunk(
    'networks/getNetworkById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/network/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getNetworkByName = createAsyncThunk(
    'networks/getNetworkByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/network/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getNetworksByCriteria = createAsyncThunk(
    'networks/getNetworksByCriteria',
    async (criteria, { rejectWithValue }) => {
        try {

            const response = await instance.get(`/network/criteria/${criteria}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createNetwork = createAsyncThunk(
    'networks/createNetwork',
    async (data, { rejectWithValue }) => {
        try {

            const response = await instance.post('/network', data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateNetwork = createAsyncThunk(
    'networks/updateNetwork',
    async ({ id, formData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/network/${id}`, formData);
            return response.data;
        } catch (error) {

            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteNetwork = createAsyncThunk(
    'networks/deleteNetwork',
    async (id, { rejectWithValue }) => {
        try {

            await instance.delete(`/network/${id}`);
            return id; // Return the deleted ID for reference
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateNetwork = createAsyncThunk(
    'networks/reinstateNetwork',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/network/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Agent Address
export const getAllAgentAddresses = createAsyncThunk(
    'agentAddress/getAllAgentAddresses',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/agent-address');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentAddressById = createAsyncThunk(
    'agentAddress/getAgentAddressById',
    async (id, { rejectWithValue }) => {
        try {
            // 
            const response = await instance.get(`/agent-address/${id}`);
            // 
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


export const createAgentAddress = createAsyncThunk(
    'agentAddress/createAgentAddress',
    async (formData, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/agent-address`, formData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateAgentAddress = createAsyncThunk(
    'agentAddress/updateAgentAddress',
    async ({ id, addressData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/agent-address/${id}`, addressData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteAgentAddress = createAsyncThunk(
    'agentAddress/deleteAgentAddress',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/agent-address/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


//Agent Details
export const getAllAgentDetails = createAsyncThunk(
    'agentDetails/getAllAgentDetails',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/agent-details');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentById = createAsyncThunk(
    'agentDetails/getAgentById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent-details/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentByBranchName = createAsyncThunk(
    'agentAddress/getAgentByBranchName',
    async (id, { rejectWithValue }) => {
        try {
            if (id === 'string') {
                return [];
            }
            const response = await instance.get(`/agent-details/branch_id/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentByIdForEdit = createAsyncThunk(
    'agentDetails/getAgentByIdForEdit',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent-details/edit/${id}`);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

export const createAgentDetails = createAsyncThunk(
    'agentDetails/createAgentDetails',
    async (agentData, { rejectWithValue }) => {
        try {
            logFormData(agentData);
            const response = await instance.post('/agent-details', agentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateAgentDetails = createAsyncThunk(
    'agentDetails/updateAgentDetails',
    async ({ id, formData }, { rejectWithValue }) => {
        try {
            logFormData(formData)
            const response = await instance.put(`/agent-details/${id}`, formData);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteAgentDetails = createAsyncThunk(
    'agentDetails/deleteAgentDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/agent-details/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const reinstateAgentDetails = createAsyncThunk(
    'agentDetails/reinstateAgentDetails',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/agent-details/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentsByCriteria = createAsyncThunk(
    'agentDetails/getAgentsByCriteria',
    async (criteria, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent-details/criteria/${criteria}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentsBySearch = createAsyncThunk(
    'agentDetails/getAgentBySearch',
    async (search, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent-details/search/${search}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)


//Insurance Types 
export const fetchInsuranceTypes = createAsyncThunk(
    'insuranceTypes/fetchInsuranceTypes',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get('/insurance-types');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to load insurance types');
        }
    }
);

// Pick List
export const getAllPickLists = createAsyncThunk(
    'pickList/getAllPickLists',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/pick-list');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

// PA Occupation List
export const getAllPAoccupationLists = createAsyncThunk(
    'PAoccupationList/getAllPAoccupationLists',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/PA-occupation-list');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

//Insurance Companies
export const fetchInsuranceCompanies = createAsyncThunk(
    'insuranceCompanies/fetchAll',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/insurance-companies');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const createInsuranceCompany = createAsyncThunk(
    'insuranceCompanies/create',
    async (data, { rejectWithValue }) => {
        try {
            const response = await instance.post('/insurance-companies', data);

            return response.data; // Adjusted to return the correct data structure
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const updateInsuranceCompany = createAsyncThunk(
    'insuranceCompanies/update',
    async ({ id, companyData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/insurance-companies/${id}`, companyData);
            return response.data; // Adjusted to return the correct data structure
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const fetchInsuranceCompanyById = createAsyncThunk(
    'insuranceCompanies/fetchById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/insurance-companies/${id}`);
            return response.data; // Adjusted to return the correct data structure
        } catch (error) {

            return rejectWithValue(error.response.data);
        }
    }
);

export const deleteInsuranceCompany = createAsyncThunk(
    'insuranceCompanies/delete',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/insurance-companies/${id}`);
            return response.data; // Adjusted to return the correct data structure
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const reinstateInsuranceCompany = createAsyncThunk(
    'insuranceCompanies/reinstate',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.patch(`/insurance-companies/reinstate/${id}`);
            return response.data.data; // Adjusted to return the correct data structure
        } catch (error) {

            return rejectWithValue(error.response.data);
        }
    }
);

export const fetchInsuranceCompanyByName = createAsyncThunk(
    'insuranceCompanies/fetchByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/insurance-companies/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    })

// Async thunk for fetching insurance companies by criteria
export const fetchInsuranceCompaniesByCriteria = createAsyncThunk(
    'insuranceCompanies/fetchByCriteria',
    async (criteria) => {
        try {
            const response = await axios.get(`/insurance-companies/criteria/${criteria}`);
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.message || 'Error fetching insurance companies');
        }
    }
);


// Insurance Branches
export const fetchInsuranceCompanyBranches = createAsyncThunk(
    'insuranceBranch/fetchInsuranceCompanyBranches',
    async (_, thunkAPI) => {
        try {
            const response = await instance.get('/insurance-company-branches');
            return response.data;  // Ensure this is the array of branch objects
        } catch (error) {

            return thunkAPI.rejectWithValue(error.response?.data || 'Failed to fetch branches');
        }
    }
);

export const fetchInsuranceCompanyBranchById = createAsyncThunk(
    'insuranceBranch/fetchInsuranceCompanyBranchById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/insurance-company-branches/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const fetchInsuranceCompanyBranchByInsuranceCompanyId = createAsyncThunk(
    'insuranceBranch/fetchInsuranceCompanyBranchByInsuranceCompanyId',
    async (id, { rejectWithValue }) => {
        try {
            if (typeof id === 'string') {
                return [];
            }
            const response = await instance.get(`/insurance-company-branches/insurance-coompany/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const createInsuranceCompanyBranch = createAsyncThunk(
    'insuranceBranch/createInsuranceCompanyBranch',
    async (branchData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/insurance-company-branches', branchData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const updateInsuranceCompanyBranch = createAsyncThunk(
    'insuranceBranch/updateInsuranceCompanyBranch',
    async ({ id, branchData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/insurance-company-branches/${id}`, branchData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const softDeleteInsuranceCompanyBranch = createAsyncThunk(
    'insuranceBranch/softDeleteInsuranceCompanyBranch',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/insurance-company-branches/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const reinstateInsuranceCompanyBranch = createAsyncThunk(
    'insuranceBranch/reinstateInsuranceCompanyBranch',
    async (id, { rejectWithValue }) => {
        try {

            const response = await instance.put(`/insurance-company-branches/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

// Action to fetch branches based on filter (e.g., newThisWeek, deactivatedLastWeek)
export const fetchInsuranceBranchesByCriteria = createAsyncThunk(
    'insuranceBranches/fetchInsuranceBranchesByCriteria',
    async (criteria) => {
        const response = await axios.get(`/insurance-company-branches/criteria/${criteria}`);
        return response.data;
    }
);

// Action to search branches by company name and branch name
export const searchInsuranceBranches = createAsyncThunk(
    'insuranceCompanies/fetchByName',
    async (name, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/insurance-company-branches/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to create a branch and agency code together
export const createBranchAndAgencyCode = createAsyncThunk(
    'insuranceBranch/createBranchAndAgencyCode',
    async ({ branchData, agencyCodeData }, { rejectWithValue }) => {
        try {
            const response = await axios.post('/insurance-company-branches/create-branch-and-agency-code', {
                branchData,
                agencyCodeData
            });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

//Agency Codes
export const fetchImfAgencyCodes = createAsyncThunk(
    'imfAgencyCode/fetchImfAgencyCodes',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/imf-agency-code');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const fetchImfAgencyCodeById = createAsyncThunk(
    'imfAgencyCode/fetchImfAgencyCodeById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/imf-agency-code/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const createImfAgencyCode = createAsyncThunk(
    'imfAgencyCode/createImfAgencyCode',
    async (agencyCodeData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/imf-agency-code', agencyCodeData);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const updateImfAgencyCode = createAsyncThunk(
    'imfAgencyCode/updateImfAgencyCode',
    async ({ id, agencyCodeData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/imf-agency-code/${id}`, agencyCodeData);

            return response.data;
        } catch (error) {

            return rejectWithValue(error.response.data);
        }
    }
);

export const softDeleteImfAgencyCode = createAsyncThunk(
    'imfAgencyCode/softDeleteImfAgencyCode',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/imf-agency-code/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const reinstateImfAgencyCode = createAsyncThunk(
    'imfAgencyCode/reinstateImfAgencyCode',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/imf-agency-code/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

// IMF Branches
export const fetchAllImfBranches = createAsyncThunk(
    'imfBranch/fetchAll',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/imf-branch');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const fetchImfBranchById = createAsyncThunk(
    'imfBranch/fetchById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/imf-branch/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const createImfBranch = createAsyncThunk(
    'imfBranch/create',
    async (newBranch, { rejectWithValue }) => {
        try {
            const response = await instance.post('/imf-branch', newBranch);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const updateImfBranch = createAsyncThunk(
    'imfBranch/update',
    async ({ id, updatedBranch }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/imf-branch/${id}`, updatedBranch);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const softDeleteImfBranch = createAsyncThunk(
    'imfBranch/softDelete',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/imf-branch/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const reinstateImfBranch = createAsyncThunk(
    'imfBranch/reinstate',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/imf-branch/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);
// Fetch IMF branches by criteria (new, deactivated, edited, etc.)
export const fetchIMFBranchesByCriteria = createAsyncThunk(
    'imfBranches/fetchIMFBranchesByCriteria',
    async (criteria, { rejectWithValue }) => {
        try {
            const response = await axios.get(`/imf-branch/criteria/${criteria}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Search IMF branches by name
export const searchIMFBranches = createAsyncThunk(
    'imfBranches/searchIMFBranches',
    async (name, { rejectWithValue }) => {
        try {
            const response = await axios.get(`/imf-branch/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//Commission Rate

// Fetch all commission rates
export const fetchCommissionRates = createAsyncThunk(
    'commissionRate/fetchAll',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/commission-rates');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

// Fetch commission rate by ID
export const fetchCommissionRateById = createAsyncThunk(
    'commissionRate/fetchById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/commission-rates/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

// Create a new commission rate
export const createCommissionRate = createAsyncThunk(
    'commissionRate/create',
    async (commissionRateData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/commission-rates', commissionRateData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

/* export const updateCommissionRate = createAsyncThunk(
  'commissionRate/updateCommissionRate',
  async ({ id, updateData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`/api/commission-rates/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating commission rate:', error);
      return rejectWithValue(error.response.data);
    }
  }
);

/* export const updateMultipleCommissionRates = createAsyncThunk(
  'commissionRates/updateMultiple',
  async (commissionData, { rejectWithValue }) => {
    try {
      

      const response = await instance.put('/commission-rates/update-multiple', commissionData);
      return response.data;
    } catch (error) {
      console.error("Error updating commission rates:", error);
      return rejectWithValue(error.response.data);
    }
  }
);
 */

/* 
export const updateMultipleCommissionRates = createAsyncThunk(
  'commissionRates/updateMultiple',
  async (commissionRatesData, { rejectWithValue }) => {

     // Check if commissionRates is an array
     if (!Array.isArray(commissionRatesData)) {
      console.error("Invalid format: commissionRates is not an array.");
      return rejectWithValue("Invalid input: commissionRates must be an array.");
  }

    try {
      const response = await axios.put('/commission-rates/update-multiple', commissionRatesData);
      return response.data; // This is the data returned after the successful update
    } catch (error) {
      console.error('Error updating multiple commission rates:', error);
      return rejectWithValue(error.response?.data || 'Failed to update commission rates');
    }
  }
);
 */

// Update a single commission rate
export const updateCommissionRate = createAsyncThunk(
    'commissionRate/updateCommissionRate',
    async ({ id, updateData }, { rejectWithValue }) => {
        try {
            const response = await axios.put(`/commission-rates/${id}`, updateData);
            return response.data; // Ensure this is the expected return structure
        } catch (error) {
            console.error('Error updating commission rate:', error);
            return rejectWithValue(error.response?.data || 'Failed to update commission rate');
        }
    }
);

// Update multiple commission rates
export const updateMultipleCommissionRates = createAsyncThunk(
    'commissionRates/updateMultiple',
    async (commissionRatesData, { rejectWithValue }) => {
        // Check if commissionRates is an array
        if (!Array.isArray(commissionRatesData)) {
            console.error("Invalid format: commissionRates is not an array.");
            return rejectWithValue("Invalid input: commissionRates must be an array.");
        }

        try {
            // Send the request in the correct format
            const response = await axios.put('/commission-rates/update-multiple', {
                commissionRates: commissionRatesData // Wrap the array in an object
            });

            return response.data; // Ensure this is the expected return structure
        } catch (error) {
            console.error('Error updating multiple commission rates:', error);
            return rejectWithValue(error.response?.data || 'Failed to update commission rates');
        }
    }
);

// Delete commission rate by ID
export const deleteCommissionRate = createAsyncThunk(
    'commissionRate/delete',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/commission-rates/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

// Reinstate a commission rate by ID
export const reinstateCommissionRate = createAsyncThunk(
    'commissionRate/reinstate',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/commission-rates/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);
export const fetchCommissionRatesByCompany = createAsyncThunk(
    'commissionRates/fetchByCompany',
    async (insuranceCompanyId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/commission-rates/insurance-company/${insuranceCompanyId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);
export const searchCommissionRate = createAsyncThunk(
    'commission-rate/searchCommissionRate',
    async (query, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/commission-rates/name/${query}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to get commission rates by specific criteria (new, deactivated, edited)
export const getCommissionRateByCriteria = createAsyncThunk(
    'commission-rate/filter',
    async (criteria, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/commission-rates/criteria/${criteria}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//Fetch employee
export const fetchEmployeeData = createAsyncThunk(
    'employeeInfoList/fetchEmployeeData',
    async (_, { rejectWithValue }) => {
        // 
        try {
            const response = await axios.get('/employee_info');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to load employee info');
        }
    }
);

// fetch employee by user id
export const fetchEmployeeByUserId = createAsyncThunk(
    'employeeList/fetchEmployeeByUserId',
    async (userId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_info/emp_info/${userId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);
// fetch employee by id
export const fetchEmployeeById = createAsyncThunk(
    'employeeList/fetchEmployeeById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_info/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

//create employee
export const createEmployeeInfo = createAsyncThunk(
    'employeeInfo/createEmployeeInfo',
    async (data, { rejectWithValue }) => {
        try {
            logFormData(data);
            const response = await axios.post('/employee_info', data, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return {
                id: response.data,
                ...data
            }
        } catch (error) {
            console.error('Error creating Employee Info :', error);
            return rejectWithValue(error.response?.data || 'Failed to create Employee Info');
        }
    }
);
// Update a single commission rate
export const updateEmployeeInfo = createAsyncThunk(
    'employeeInfo/updateEmployeeInfo',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            logFormData(data);
            const response = await axios.put(`/employee_info/${id}`, data,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });


            return response.data; // Ensure this is the expected return structure
        } catch (error) {
            console.error('Error updating Employee Info :', error);
            return rejectWithValue(error.response?.data || 'Failed to update Employee Info');
        }
    }
);

export const reinstateEmployee = createAsyncThunk(
    'employee/reinstateEmployee',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/employee_info/reinstate/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const softDeleteImfEmployee = createAsyncThunk(
    'EmployeeData/softDelete',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/employee_info/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const searchEmployee = createAsyncThunk(
    'employee/searchEmployee',
    async (name, { rejectWithValue }) => {
        try {
            const response = await axios.get(`/employee_info/name/${name}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const fetchEmployeeByCriteria = createAsyncThunk(
    'employee/fetchEmployeeByCriteria',
    async (criteria) => {
        try {
            const response = await axios.get(`/employee_info/criteria/${criteria}`);
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.message || 'Error fetching employee');
        }
    }
);

// Employee Salary
export const createEmployeeSalary = createAsyncThunk(
    'employeeSalary/createEmployeeSalary',
    async (data, { rejectWithValue }) => {
        try {
            const response = await axios.post('/employee_salary', data);
            return response.data;
        } catch (error) {
            console.error('Error creating Employee Salary :', error);
            return rejectWithValue(error.response?.data || 'Failed to create Employee Salary');
        }
    }
);

export const fetchAllEmployeeSalary = createAsyncThunk(
    'employeeSalary/fetchAllEmployeeSalary',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_salary/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const fetchEmployeeSalaryById = createAsyncThunk(
    'employeeSalary/fetchEmployeeSalary',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_salary/edit_salary/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

export const updateEmployeeSalary = createAsyncThunk(
    'employeeSalary/updateEmployeeSalary',
    async ({ id, data }, { rejectWithValue }) => {
        try {

            const response = await axios.put(`/employee_salary/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating Employee Salary :', error);
            return rejectWithValue(error.response?.data || 'Failed to update Employee Salary');
        }
    }
);

export const deleteEmployeeSalary = createAsyncThunk(
    'employeeSalary/deleteEmployeeSalary',
    async (id, { rejectWithValue }) => {
        try {
            const response = await axios.delete(`/employee_salary/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response.data);
        }
    }
);

// Async action for login
export const login = createAsyncThunk(
    'auth/login',
    async ({ userId, password }, { rejectWithValue }) => {
        try {
            const response = await axios.post('/auth/login', { userId, password });
            return response.data;  // Assuming the response contains the token and user data
        } catch (error) {
            toast.error(error.response?.data?.message || 'Login failed');
            return rejectWithValue(error.response?.data?.message || 'Login failed');
        }
    }
);

export const validateToken = createAsyncThunk(
    'auth/validateToken',
    async (token, { rejectWithValue }) => {
        try {
            const response = await axios.get(
                '/auth/validate-token',
                {
                    headers: {
                        'Authorization': `Bearer ${token}`, // Ensure proper token format
                    },
                }
            );

            return response.data; // Return validated user
        } catch (error) {
            console.error('Token validation error:', error); // Log any errors

            return rejectWithValue('Token validation failed');
        }
    }
);

export const logoutUser = createAsyncThunk(
    'auth/logout',
    async (_, { dispatch, rejectWithValue }) => {
        try {
            await axios.post('/auth/logout'); // Call the logout API
            dispatch(logout()); // Dispatch the logout action to clear Redux state
        } catch (error) {
            console.error('Logout failed:', error);
            return rejectWithValue(error.response?.data || 'Logout failed');
        }
    }
);


export const changePassword = createAsyncThunk(
    'auth/changePassword',
    async ({ newPassword, confirmPassword, userId, userType }, { rejectWithValue }) => {
        try {
            const response = await axios.post('/auth/change-password', {
                userId,
                newPassword,
                confirmPassword,
                userType
            });
            return response.data;
        } catch (error) {
            if (error.response && error.response.data) {
                return rejectWithValue(error.response.data);
            } else {
                return rejectWithValue({ message: 'Something went wrong' });
            }
        }
    }
);
export const forgotPassword = createAsyncThunk(
    '/auth/forgot-password',
    async ({ email }, { rejectWithValue }) => {
        try {
            const response = await axios.post('/auth/forgot-password', { email });
            return response.data;  // On success, return the server response (e.g., OTP sent message)
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Failed to send OTP');
        }
    }
);

export const verifyOTP = createAsyncThunk(
    'auth/verifyOTP',
    async ({ email, otp }, { rejectWithValue }) => {
        try {
            const response = await axios.post('/auth/verify-otp', { email, otp });
            return response.data;  // On success, return userId and userType
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'OTP verification failed');
        }
    }
);


export const resetPassword = createAsyncThunk(
    'auth/resetPassword',
    async ({ otp, newPassword, confirmPassword }, { rejectWithValue }) => {
        try {
            const response = await axios.post('/auth/reset-password', { otp, newPassword, confirmPassword });
            return response.data; // On success, return reset success message
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Failed to reset password');
        }
    }
);

//create Customer
export const createCustomerInfo = createAsyncThunk(

    'customerInfo/createCustomerInfo',

    async (data, { rejectWithValue }) => {
        // 
        try {

            const response = await instance.post('/customer_info', data);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
//getMemberByCustomerId
export const getMemberByCustomerId = createAsyncThunk(
    'customer/getMemberByCustomerId',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_member_info/customer_id/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getCustomerById = createAsyncThunk(
    'customer/getCustomerById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_info/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
// Roles API
export const getAllCustomer = createAsyncThunk(
    'customer/getAllCustomer',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/customer_info');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const updateCustomer = createAsyncThunk(
    'customer/updateCustomer',
    async ({ id, data }, { rejectWithValue }) => {
        try {

            const response = await instance.put(`/customer_info/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//create Customer
export const createCustomerMemberInfo = createAsyncThunk(
    'customerMemberInfo/createCustomerMemberInfo',
    async (data, { rejectWithValue }) => {
        // 
        try {
            const response = await instance.post('/customer_member_info', data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateMember = createAsyncThunk(
    'members/updateMember',
    async ({ id, data }, { rejectWithValue }) => {
        try {

            const response = await instance.put(`/customer_member_info/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteMember = createAsyncThunk(
    'members/deleteMember',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/customer_member_info/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const getMemberById = createAsyncThunk(
    'members/getMemberById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_member_info/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
// Roles API
export const getAllMembers = createAsyncThunk(
    'members/getAllMembers',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/customer_member_info');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
//create Customer grouping
export const createCustomerGrouping = createAsyncThunk(

    'customerGrouping/createCustomerGrouping',

    async (data, { rejectWithValue }) => {
        // 
        try {

            const response = await instance.post('/customer_grouping', data);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAllGroups = createAsyncThunk(
    'groups/getAllGroups',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/customer_grouping');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const updateGrouping = createAsyncThunk(
    'grouping/updateGrouping',
    async ({ id, data }, { rejectWithValue }) => {

        try {

            const response = await instance.put(`/customer_grouping/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
// Customer Address
export const getAllCustomerAddresses = createAsyncThunk(
    'customerAddress/getAllCustomerAddresses',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/customer_address');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getCustomerAddressById = createAsyncThunk(
    'customerAddress/getCustomerAddressById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_address/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const getCustomerAddressByCustomerId = createAsyncThunk(
    'customerAddress/getCustomerAddressByCustomerId',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_address/customer_id/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createCustomerAddress = createAsyncThunk(
    'customerAddress/createCustomerAddress',
    async (formData, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/customer_address`, formData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateCustomerAddress = createAsyncThunk(
    'customerAddress/updateCustomerAddress',
    async ({ id, addressData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/customer_address/${id}`, addressData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteCustomerAddress = createAsyncThunk(
    'customerAddress/deleteCustomerAddress',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/customer_address/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

//Customer Documentation
export const getAllCustomerDocuments = createAsyncThunk(
    'customerDocumentation/getAllDocuments',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/customer_documentation');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getCustomerDocumentById = createAsyncThunk(
    'customerDocumentation/getDocumentById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_documentation/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getCustomerDocumentsByCustomerId = createAsyncThunk(
    'customerDocumentation/getCustomerDocumentsByCustomerId',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/customer_documentation/customer/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createCustomerDocument = createAsyncThunk(
    'customerDocumentation/createDocument',
    async (documentData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/customer_documentation', documentData);
            return response.data;
        } catch (error) {
            console.error('Upload error:', error);
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateCustomerDocument = createAsyncThunk(
    'customerDocumentation/updateDocument',
    async ({ id, documentData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/customer_documentation/${id}`, documentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteCustomerDocument = createAsyncThunk(
    'customerDocumentation/deleteDocument',
    async (id, { rejectWithValue }) => {
        try {

            await instance.delete(`/customer_documentation/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Roles API
export const getCustomerAndAddress = createAsyncThunk(
    'customerAndAddress/CustomerAndAddress',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get('/customer_info/customerAndAddress');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createQuotation = async (quickquotationData) => {
    try {
        let response;
        if (quickquotationData.product_master_name === 'FG ADVANTAGE TOP UP') {
            response = await axios.post('/quick_quotations_advantage_topup/advtopupcreate', quickquotationData);
        }
        else if (quickquotationData.product_master_name === 'FG HEALTH ABSOLUTE') {
            response = await axios.post('/quick_quotations_health_absolute/healthabsolutecreate', quickquotationData);
        } else if (quickquotationData.product_master_name === 'FG HEALTH SURAKSHA') {
            response = await axios.post('/quick_quotations_health_suraksha/healthsurakshacreate', quickquotationData);
        }
        else if (quickquotationData.product_master_name === 'FG VARISHTHA BIMA') {
            response = await axios.post('/quick_quotations_varishta_bima/healthvaristabimacreate', quickquotationData);
        }
        else if (quickquotationData.product_master_name === 'FG ACCIDENT SURAKSHA') {
            response = await axios.post('/quick_quotations_accident_suraksha/accidentsurakshacreate', quickquotationData);
        }
        else {
            response = await axios.post('/quick_quotations_health_total/healthtotalcreate', quickquotationData);
        }
        return response.data;
    } catch (error) {
        throw error.response?.data || error.message;
    }
};

export const createFloaterOptions = async (quotationData) => {
    try {
        // Check the product_master_name to determine the correct endpoint
        let response;
        if (quotationData.product_master_name === 'FG ADVANTAGE TOP UP') {
            response = await axios.post('/quick_quotations_advantage_topup/advtopup-floater-options', quotationData);
        } else if (quotationData.product_master_name === 'FG HEALTH ABSOLUTE') {
            response = await axios.post('/quick_quotations_health_absolute/healthabsolute-floater-options', quotationData);
        } else if (quotationData.product_master_name === 'FG HEALTH SURAKSHA') {
            response = await axios.post('/quick_quotations_health_suraksha/healthsuraksha-floater-options', quotationData);
        } else if (quotationData.product_master_name === 'FG VARISHTHA BIMA') {
            response = await axios.post('/quick_quotations_varishta_bima/healthvarishtabima-floater-options', quotationData);
        } else {
            response = await axios.post('/quick_quotations_health_total/healthtotal-floater-options', quotationData);
        }
        return response.data;
    } catch (error) {
        throw error.response?.data || error.message;
    }
};


// ... existing code ...

export const createQuotationWithMembers = createAsyncThunk(
    'Quotation/createQuotation',
    async ({ quotationData, membersData }, { rejectWithValue }) => {
        try {
            let response;
            if (quotationData.productMasterId === 'FG ACCIDENT SURAKSHA') {
                response = await instance.post('/PA_quotation/PA_quotation', {
                    quotationData,
                    membersData
                });
            } else {
                response = await instance.post('/quotations/quotations-with-members', {
                    quotationData,
                    membersData
                });
            }
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// ... existing code ...

// Customer Address
export const getAllEmployeeAddresses = createAsyncThunk(
    'employeeAddress/getAllEmployeeAddresses',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/employee_address');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getEmployeeAddressById = createAsyncThunk(
    'employeeAddress/getEmployeeAddressById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_address/${id}`);


            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const getEmployeeAddressByEmployeeId = createAsyncThunk(
    'employeeAddress/getEmployeeAddressByEmployeeId',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_address/employee_id/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createEmployeeAddress = createAsyncThunk(
    'employeeAddress/createEmployeeAddress',
    async (formData, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/employee_address`, formData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updatemployeeAddress = createAsyncThunk(
    'employeeAddress/updateCustomerAddress',
    async ({ id, addressData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/employee_address/${id}`, addressData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteEmployeeAddress = createAsyncThunk(
    'employeeAddress/deleteEmployeeAddress',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/employee_address/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const getAllQuotationResponse = createAsyncThunk(
    'quotationResponse/getAllQuotationResponse',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/quotations/quotationResponse');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAllQuotationsByUserId = createAsyncThunk(
    'quotationResponse/getAllQuotationsByUserId',
    async (userId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/quotations/user/${userId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getQuotationResponseById = createAsyncThunk(
    'quotationResponse/getQuotationResponseById',
    async ({ id, tableSource }, { rejectWithValue }) => {
        try {
            let response;

            if (tableSource === 'quotations') {
                response = await instance.get(`/quotations/quotationResponse/${id}`);
            } else if (tableSource === 'pa_quotations') {
                response = await instance.get(`/pa_quotation/quotationResponse/${id}`);
            } else {
                throw new Error('Invalid source table specified');
            }

            return { tableSource, data: response.data }; // Return source table and data
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);



export const getQuotationByQuotationNumber = createAsyncThunk(
    'quotation/getQuotationByQuotationNumber',
    async (quotationNumber, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/quotations/quotation-number/${quotationNumber}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getQuotationDetailsByQuotationId = createAsyncThunk(
    'quotation/getQuotationDetailsByQuotationId',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/quotation_member/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


// Proposal API Integration
export const createProposal = createAsyncThunk(
    'proposal/createProposal',
    async (data, { rejectWithValue }) => {
        try {
            const response = await instance.post('/proposals/create', data, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateProposal = createAsyncThunk(
    'proposal/updateProposal',
    async ({ id, formData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(
                `/proposals/${id}`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


export const getProposalByQuotationNumber = createAsyncThunk(
    'proposal/getProposalByQuotationNumber',
    async (quotationNumber, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/proposals/quotation/${quotationNumber}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAllProposals = createAsyncThunk(
    'proposal/getAllProposals',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/proposals');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAllProposalsByUserId = createAsyncThunk(
    'proposal/getAllProposalsByUserId',
    async (userId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/proposals/user/${userId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const submitProposal = createAsyncThunk(
    'proposal/submitProposal',
    async (proposalData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/proposals/submit-proposal', proposalData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const submitPolicy = createAsyncThunk(
    'proposal/submitPolicy',
    async (policyData, { rejectWithValue }) => {
        try {

            const response = await instance.post('/proposals/submit-policy', policyData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getPolicyPdf = createAsyncThunk(
    'proposal/getPolicyPdf',
    async (policyId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/proposals/policy/${policyId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getProposalById = createAsyncThunk(
    'proposal/getProposalById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/proposals/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getProposalDetailsByPolicyNumber = createAsyncThunk(
    'proposal/getProposalDetailsByPolicyNumber',
    async ({ policy_number, policy_type }, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/proposals/policy-details/${policy_number}/${policy_type}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
)

// CKYC API Integration
export const checkForCkyc = createAsyncThunk(
    'ckyc/checkForCkyc',
    async (ckycData, { rejectWithValue }) => {
        try {
            // Route through your backend instead of calling external API directly
            const response = await instance.post('/proposals/check-for-ckyc', ckycData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const checkCKYCStatus = createAsyncThunk(
    'ckyc/checkCKYCStatus',
    async (ckycStatusData, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/proposals/check-ckyc-status`, ckycStatusData)
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Payment API Integration
export const submitPayment = createAsyncThunk(
    'payment/submitPayment',
    async (paymentData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/payment/submit-payment', paymentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createPayment = createAsyncThunk(
    'payment/createPayment',
    async (paymentData, { rejectWithValue }) => {
        try {
            const data = await instance.post('/payment', paymentData);
            return data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const getPaymentCount = createAsyncThunk(
    'payment/getPaymentCount',
    async (prefix, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/proposals/count/${prefix}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updatePaymentStatus = createAsyncThunk(
    'payment/updatePaymentStatus',
    async ({ id, status }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/payment/${id}`, { Status: status });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updatePayment = createAsyncThunk(
    'payment/updatePayment',
    async ({ id, paymentData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/payment/${id}`, paymentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const checkIfSuccess = createAsyncThunk(
    'payment/checkIfSuccess',
    async ({ proposal_number }, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/payment/check-success/${proposal_number}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getPaymentByProposalNumber = createAsyncThunk(
    'payment/getPaymentByProposalNumber',
    async (proposal_number, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/payment/getPaymentByProposalNumber/${proposal_number}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

export const payLater = createAsyncThunk(
    'payment/payLater',
    async ({ paymentData }, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/payment/pay-later/`, paymentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const checkPaymentStatus = createAsyncThunk(
    'payment/checkPayment',
    async (transactionId, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/payment/check-payment/${transactionId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

// Create page rights
export const createPageRights = createAsyncThunk(
    'pageRights/create',
    async (data, { rejectWithValue }) => {
        try {

            const response = await axios.post('/page-rights/create', data, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${localStorage.getItem('token')}`
                }
            });



            if (response.data.success) {
                return response.data;
            }
            return rejectWithValue(response.data);
        } catch (error) {
            console.error('Error in createPageRights:', error);
            return rejectWithValue(
                error.response?.data || {
                    success: false,
                    message: error.message || 'Network error'
                }
            );
        }
    }
);

// Get role rights
export const getRoleRights = createAsyncThunk(
    'pageRights/getRoleRights',
    async (roleId, { rejectWithValue }) => {
        try {
            const token = getToken();
            const response = await instance.get(`/page-rights/role/${roleId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Check access rights
export const checkUserAccess = createAsyncThunk(
    'auth/checkUserAccess',
    async (data, { rejectWithValue }) => {
        try {
            const response = await axios.post('/page-rights/check-access', data, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (response.data.success) {
                return response.data;
            }
            return rejectWithValue(response.data);
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: 'Network error' });
        }
    }
);

export const fetchUserAccessRights = createAsyncThunk(
    'auth/fetchUserAccessRights',
    async (userId, { rejectWithValue }) => {
        try {
            const response = await axios.get(`/page-rights/user/${userId}`, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('token')}` // Make sure token is included
                }
            });

            if (response.data.success) {
                return response.data;
            } else {
                return rejectWithValue(response.data);
            }
        } catch (error) {
            if (error.response?.status === 401) {
                // Handle unauthorized access
                // You might want to dispatch a logout action here
                return rejectWithValue({ message: 'Session expired. Please login again.' });
            }
            return rejectWithValue(error.response?.data || { message: 'Network error' });
        }
    }
);

/* export const fetchAllUsers = createAsyncThunk(
    'pageRights/fetchAllUsers',
    async (_, { rejectWithValue }) => {
        try {
            
            const response = await axios.get('/page-rights/users', {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            
            if (response.data.success) {
                return response.data;
            }
            
            return rejectWithValue(response.data);
        } catch (error) {
            console.error('API request error:', error);
            return rejectWithValue(error.response?.data || { message: 'Network error' });
        }
    }
); */


// Action to fetch all banks
export const fetchAllBanks = createAsyncThunk(
    'banks/fetchAll',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/bank_list'); // Adjust the endpoint as necessary
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createEmployeeBankDetails = createAsyncThunk(
    'employeeBank/createEmployeeBankDetails',
    async (data, { rejectWithValue }) => {
        try {
            const response = await instance.post('/employee_bank', data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to get employee bank details by employee ID
export const getEmployeeBankDetailsByEmployeeId = createAsyncThunk(
    'employeeBank/getEmployeeBankDetailsByEmployeeId',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_bank/employee_id/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to get all employee bank details
export const getAllEmployeeBankDetails = createAsyncThunk(
    'employeeBank/getAllEmployeeBankDetails',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/employee_bank');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to get employee bank details by ID
export const getEmployeeBankDetailsById = createAsyncThunk(
    'employeeBank/getEmployeeBankDetailsById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_bank/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to update employee bank details
export const updateEmployeeBankDetails = createAsyncThunk(
    'employeeBank/updateEmployeeBankDetails',
    async ({ id, data }, { rejectWithValue }) => {

        try {
            const response = await instance.put(`/employee_bank/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to delete employee bank details
export const deleteEmployeeBankDetails = createAsyncThunk(
    'employeeBank/deleteEmployeeBankDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/employee_bank/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createAgentBankDetails = createAsyncThunk(
    'agentBankDetails/createAgentBankDetails',
    async (agentBankData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/agent-bank', agentBankData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentBankDetailsByAgentId = createAsyncThunk(
    'agentBankDetails/getAgentBankDetailsByAgentId',
    async (agentId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent-bank/agent_id/${agentId}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAllAgentBankDetails = createAsyncThunk(
    'agentBankDetails/getAllAgentBankDetails',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/agent-bank');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAgentBankDetailsById = createAsyncThunk(
    'agentBankDetails/getAgentBankDetailsById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent-bank/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateAgentBankDetails = createAsyncThunk(
    'agentBankDetails/updateAgentBankDetails',
    async ({ id, data }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/agent-bank/${id}`, data);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteAgentBankDetails = createAsyncThunk(
    'agentBankDetails/deleteAgentBankDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/agent-bank/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteFirstBankDetails = createAsyncThunk(
    'employeeBank/deleteFirstBankDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/employee_bank/first_bank/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteSecondBankDetails = createAsyncThunk(
    'employeeBank/deleteSecondBankDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/employee_bank/second_bank/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteAgentFirstBankDetails = createAsyncThunk(
    'agentBankDetails/deleteAgentFirstBankDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/agent-bank/first_bank/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteAgentSecondBankDetails = createAsyncThunk(
    'agentBankDetails/deleteAgentSecondBankDetails',
    async (id, { rejectWithValue }) => {
        try {
            await instance.delete(`/agent-bank/second_bank/${id}`);
            return id;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
//add console to this below
export const getEmployeeByReportingManagerUserId = createAsyncThunk(
    'employee/getEmployeeByUserId',
    async (user_id, { rejectWithValue }) => {
        try {

            const response = await instance.get(`/employee_info/user_id/${user_id}`);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to fetch all agent loans
export const getAllAgentLoans = createAsyncThunk(
    'agentLoan/getAllAgentLoans',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/agent_loan');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to create a new agent loan
export const createAgentLoan = createAsyncThunk(
    'agentLoan/createAgentLoan',
    async (loanData, { rejectWithValue }) => {
        try {

            const response = await instance.post('/agent_loan', loanData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to update an agent loan by ID
export const updateAgentLoan = createAsyncThunk(
    'agentLoan/updateAgentLoan',
    async ({ id, loanData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/agent_loan/${id}`, loanData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to delete an agent loan by ID
export const deleteAgentLoan = createAsyncThunk(
    'agentLoan/deleteAgentLoan',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/agent_loan/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
// ... existing code ...

// Action to get an agent loan by ID
export const getAgentLoanById = createAsyncThunk(
    'agentLoan/getAgentLoanById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent_loan/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to get all EMIs related to a given loan ID
export const getAgentLoanEmis = createAsyncThunk(
    'agentLoan/getAgentLoanEmis',
    async (loanId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/agent_loan/${loanId}/emis`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to update an EMI by ID
export const updateAgentEmi = createAsyncThunk(
    'agentLoan/updateAgentEmi',
    async ({ id, emiData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/agent_loan/emis/${id}`, emiData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to delete an EMI by ID
export const deleteAgentEmi = createAsyncThunk(
    'agentLoan/deleteAgentEmi',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/agent_loan/emis/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


// Action to fetch all employee loans
export const getAllEmployeeLoans = createAsyncThunk(
    'employeeLoan/getAllEmployeeLoans',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/employee_loan');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to create a new employee loan
export const createEmployeeLoan = createAsyncThunk(
    'employeeLoan/createEmployeeLoan',
    async (loanData, { rejectWithValue }) => {
        try {

            const response = await instance.post('/employee_loan', loanData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to update an employee loan by ID
export const updateEmployeeLoan = createAsyncThunk(
    'employeeLoan/updateEmployeeLoan',
    async ({ id, loanData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/employee_loan/${id}`, loanData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to delete an employee loan by ID
export const deleteEmployeeLoan = createAsyncThunk(
    'employeeLoan/deleteEmployeeLoan',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/employee_loan/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
// ... existing code ...

// Action to get an employee loan by ID
export const getEmployeeLoanById = createAsyncThunk(
    'employeeLoan/getEmployeeLoanById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_loan/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to get all EMIs related to a given loan ID
export const getEmployeeLoanEmis = createAsyncThunk(
    'employeeLoan/getEmployeeLoanEmis',
    async (loanId, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/employee_loan/${loanId}/emis`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to update an EMI by ID
export const updateEmployeeEmi = createAsyncThunk(
    'employeeLoan/updateEmployeeEmi',
    async ({ id, emiData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/employee_loan/emis/${id}`, emiData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Action to delete an EMI by ID
export const deleteEmployeeEmi = createAsyncThunk(
    'employeeLoan/deleteEmployeeEmi',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/employee_loan/emis/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


// Task Management Actions
export const getAllTasks = createAsyncThunk(
    'tasks/getAllTasks',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/task');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createTask = createAsyncThunk(
    'tasks/createTask',
    async (taskData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/task', taskData);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateTask = createAsyncThunk(
    'tasks/updateTask',
    async ({ id, taskData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/task/${id}`, taskData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getTaskById = createAsyncThunk(
    'tasks/getTaskById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/task/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getTasksByAssignedTo = createAsyncThunk(
    'tasks/getTasksByAssignedTo',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/task/assigned-to/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getTasksByAssignedBy = createAsyncThunk(
    'tasks/getTasksByAssignedBy',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/task/assigned-by/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const deleteTask = createAsyncThunk(
    'tasks/deleteTask',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.delete(`/task/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const createTaskComment = createAsyncThunk(
    'tasks/createComment',
    async ({ taskId, commentData }, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/task/comments/${taskId}`, commentData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const addCommentReply = createAsyncThunk(
    'tasks/addCommentReply',
    async ({ commentId, replyData }, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/task/comments/reply/${commentId}`, replyData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const updateTaskNotification = createAsyncThunk(
    'tasks/updateTaskNotification',
    async ({ taskId, notificationData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/task/task-notifications/${taskId}`, notificationData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);
export const updateCommentNotification = createAsyncThunk(
    'tasks/updateCommentNotification',
    async ({ userId, taskId, commentId }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/task/comment-notifications/${taskId}`, {
                user_id: userId,
                task_id: taskId,
                comment_id: commentId,
                is_read: 1
            });

            return {
                commentId,
                taskId,
                ...response.data
            };

        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getTaskDetails = createAsyncThunk(
    'tasks/getTaskDetails',
    async (id, { rejectWithValue }) => {
        //
        try {
            const response = await instance.get(`/task/task-details/${id}`);
            //
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getTaskNotifications = createAsyncThunk(
    'tasks/getTaskNotifications',
    async (userId, { rejectWithValue }) => {

        try {
            const response = await instance.get(`/task/task-notifications/${userId}`);

            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Failed to fetch task notifications');
        }
    }
);

// Add to existing action.js file
export const createRolloverMigration = createAsyncThunk(
    'rolloverMigration/create',
    async ({ migrationData, memberData, paymentData }, { rejectWithValue }) => {
        try {
            const response = await instance.post('/rollover-migrations', { migrationData, memberData, paymentData });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getRolloverMigrationById = createAsyncThunk(
    'rolloverMigration/getById',
    async (id, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/rollover-migrations/${id}`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const getAllRolloverMigrations = createAsyncThunk(
    'rolloverMigration/getAll',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/rollover-migrations');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


// Add this action for updating rollover migration
export const updateRolloverMigration = createAsyncThunk(
    'rolloverMigration/update',
    async ({ id, migrationData }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/rollover-migrations/${id}`, {
                migrationData,

            });
            return response.data;
        } catch (error) {
            console.error('Error updating rollover migration:', error);
            return rejectWithValue(error.response?.data || 'Failed to update rollover migration');
        }
    }
);


export const deleteProposal = createAsyncThunk(
    'proposal/deleteProposal',
    async ({ id, proposal_type, proposal_number, remarks }, { rejectWithValue }) => {
        // async ({ id, proposal_type, remarks }, { rejectWithValue }) => {
        try {
            const response = await instance.post(`/proposals/${id}/${proposal_type}`, { remarks, proposal_number });
            // const response = await instance.post(`/proposals/${id}/${proposal_type}`, { remarks });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
)

// Charts

export const getTotalPremiumInTimePeriod = createAsyncThunk(
    'charts/getTotalPremiumInTimePeriod',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get(`/dashboard_charts/totalPremiumInTimePeriod`);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

export const fetchProposalsByCompany = createAsyncThunk(
    'dashboard/fetchProposalsByCompany',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get('/dashboard_charts/proposals-by-company')
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
        // try {
        //     const response = await axios.get('/dashboard_charts/proposals-by-company')
        //     return response.data;
        // } catch (error) {
        //     return rejectWithValue(error.response?.data || { message: error.message });
        // }
    }

);

export const fetchProposalsByAgent = createAsyncThunk(
    'dashboard/fetchProposalsByAgent',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get('/dashboard_charts/proposals-by-agent')
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

export const getCurrentFinancialYearProposals = createAsyncThunk(
    'dashboard/getCurrentFinancialYearProposals',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/dashboard_charts/proposalsForCurrentFinancialYear');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
)

export const getTop10Branches = createAsyncThunk(
    'dashboard/getTop10Branches',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/dashboard_charts/top10Branches');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
)

export const getTop20Agents = createAsyncThunk(
    'dashboard/getTop20Agents',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/dashboard_charts/top20Agents');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
)

// Generate report based on filters
export const generateReport = createAsyncThunk(
    'reports/generateReport',
    async (filterData, { rejectWithValue }) => {
        try {
            const response = await instance.post('/reports/generate', filterData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

// Get available financial years
export const getFinancialYears = createAsyncThunk(
    'reports/getFinancialYears',
    async (_, { rejectWithValue }) => {
        try {
            const response = await instance.get('/reports/financial-years');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);


export const saveRenewalsMapping = createAsyncThunk(
    'renewals/saveRenewalsMapping',
    async (data) => {
        try {
            const response = await axios.post('/renewals_mapping', data);
            return response.data;
        } catch (error) {
            throw error.response?.data?.message || 'Failed to save renewals mapping';
        }
    }
);

// Get all renewals
export const getAllRenewals = createAsyncThunk(
    'renewals/getAllRenewals',
    async () => {
        try {
            const response = await axios.get('/renewals_mapping');
            return response.data;
        } catch (error) {
            throw error.response?.data?.message || 'Failed to fetch renewals';
        }
    }
);

// agent bussiness transfer
export const transferBusiness = createAsyncThunk(
    'agent/transferBusiness',
    async ({ id, agent_id }, { rejectWithValue }) => {
        try {
            const response = await instance.put(`/proposals/agent-business-transfer/${id}`, { agent_id });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);