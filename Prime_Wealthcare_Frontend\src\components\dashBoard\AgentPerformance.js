import React, { useEffect, useRef } from 'react';
import { Chart } from 'chart.js/auto';
import { formatIndianValue } from '../../utils/Reusable';

const AgentPerformance = ({ chartData, chartType, title, height = '100%' }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (!chartData) return;

        const createChart = () => {
            if (chartRef.current) {
                if (chartInstance.current) {
                    chartInstance.current.destroy();
                }

                const ctx = chartRef.current.getContext('2d');

                if (chartType === 'stacked-bar') {
                    chartInstance.current = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: chartData.agents,
                            datasets: [
                                {
                                    label: 'NEW',
                                    data: chartData.data.NEW,
                                    backgroundColor: 'rgba(0, 200, 170, 0.85)',
                                    borderWidth: 1,
                                    borderColor: 'rgba(0, 200, 170, 1)',
                                    borderRadius: 4,
                                    stack: 'stack0',
                                    premium: chartData.premium?.NEW || []
                                },
                                {
                                    label: 'RENEWAL',
                                    data: chartData.data.RENEWAL,
                                    backgroundColor: 'rgba(54, 135, 255, 0.85)',
                                    borderWidth: 1,
                                    borderColor: 'rgba(54, 135, 255, 1)',
                                    borderRadius: 4,
                                    stack: 'stack0',
                                    premium: chartData.premium?.RENEWAL || []
                                },
                                {
                                    label: 'ROLLOVER',
                                    data: chartData.data.ROLLOVER,
                                    backgroundColor: 'rgba(255, 184, 0, 0.85)',
                                    borderWidth: 1,
                                    borderColor: 'rgba(255, 184, 0, 1)',
                                    borderRadius: 4,
                                    stack: 'stack0',
                                    premium: chartData.premium?.ROLLOVER || []
                                },
                                {
                                    label: 'MIGRATION',
                                    data: chartData.data.MIGRATION,
                                    backgroundColor: 'rgba(243, 124, 124, 0.85)',
                                    borderWidth: 1,
                                    borderColor: 'rgba(243, 124, 124, 1)',
                                    borderRadius: 4,
                                    stack: 'stack0',
                                    premium: chartData.premium?.MIGRATION || []
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: {
                                    stacked: true,
                                    grid: {
                                        display: false
                                    },
                                    // ticks: {
                                    //     maxRotation: 45,
                                    //     minRotation: 45
                                    // }
                                },
                                y: {
                                    stacked: true,
                                    title: {
                                        display: true,
                                        text: 'Proposal Count'
                                    },
                                    ticks: {
                                        beginAtZero: true
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.1)'
                                    }
                                }
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: title || 'Top 10 Performing Agents',
                                    font: { size: 18, weight: 'bold' },
                                    padding: { top: 10, bottom: 30 },
                                    color: '#333'
                                },
                                legend: {
                                    position: 'bottom',
                                    labels: { usePointStyle: true, padding: 15 }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function (context) {
                                            const datasetLabel = context.dataset.label;
                                            const value = context.parsed.y;
                                            const premium = context.dataset.premium[context.dataIndex];
                                            return [
                                                `${datasetLabel} Count: ${value}`,
                                                `Premium: ₹${premium?.toLocaleString('en-IN') || 0}`
                                            ];
                                        }
                                    }
                                },
                                datalabels: {
                                    display: function (context) {
                                        // Only show for the last dataset in the stack
                                        return context.datasetIndex === context.chart.data.datasets.length - 1;
                                    },
                                    anchor: 'end',
                                    align: 'top',
                                    formatter: function (value, context) {
                                        // Calculate total premium for this index across all datasets
                                        const totalPremium = context.chart.data.datasets.reduce((sum, dataset) => {
                                            return sum + (dataset.premium[context.dataIndex] || 0);
                                        }, 0);
                                        return '₹' + totalPremium.toLocaleString('en-IN');
                                    },
                                    color: '#333',
                                    font: { weight: 'bold' }
                                }
                            }
                        },
                        plugins: [{
                            id: 'valueLabels',
                            afterDatasetsDraw(chart) {
                                const { ctx, data } = chart;
                                const isMobileView = window.innerWidth <= 768;

                                // Calculate stacked totals
                                const stackTotals = {};
                                const premiumTotals = {};

                                data.datasets.forEach((dataset, datasetIndex) => {
                                    const meta = chart.getDatasetMeta(datasetIndex);

                                    meta.data.forEach((bar, index) => {
                                        const value = dataset.data[index] || 0;
                                        if (!stackTotals[index]) stackTotals[index] = 0;
                                        stackTotals[index] += value;

                                        // Premium totals
                                        const premium = dataset.premium?.[index] || 0;
                                        if (!premiumTotals[index]) premiumTotals[index] = 0;
                                        premiumTotals[index] += premium;
                                    });
                                });

                                // Replace the premium display section in the afterDatasetsDraw plugin
                                Object.keys(stackTotals).forEach((index, arrayIndex) => {
                                    const stackTotal = stackTotals[index];
                                    const premiumTotal = premiumTotals[index] || 0;

                                    // Find the topmost visible bar for this stack
                                    let topBar = null;
                                    for (let i = data.datasets.length - 1; i >= 0; i--) {
                                        const meta = chart.getDatasetMeta(i);
                                        if (!meta.hidden && meta.data[index] && data.datasets[i].data[index] > 0) {
                                            topBar = meta.data[index];
                                            break;
                                        }
                                    }

                                    if (topBar && stackTotal > 0) {
                                        const { x } = topBar.getCenterPoint();
                                        const y = topBar.getProps(['y']).y;

                                        ctx.save();
                                        ctx.textAlign = 'center';

                                        // Adjust font size and position for mobile
                                        ctx.font = isMobileView ? 'bold 10px Arial' : 'bold 11px Arial';
                                        ctx.fillStyle = '#008000';

                                        const formattedAmount = formatIndianValue(premiumTotal, isMobileView);

                                        // Adjust vertical spacing and position for mobile
                                        let yOffset;
                                        if (isMobileView) {
                                            yOffset = arrayIndex % 2 === 0 ? -25 : -10;
                                        } else {
                                            yOffset = -10;
                                        }

                                        ctx.fillText(formattedAmount, x, y + yOffset);
                                        ctx.restore();
                                    }
                                });

                                // Display individual bar values with adjusted size
                                chart.data.datasets.forEach((dataset, datasetIndex) => {
                                    const meta = chart.getDatasetMeta(datasetIndex);
                                    if (!meta.hidden) {
                                        meta.data.forEach((element, index) => {
                                            const value = dataset.data[index];
                                            if (value > (isMobileView ? 2 : 3)) {
                                                const position = element.getCenterPoint();
                                                ctx.textAlign = 'center';
                                                ctx.textBaseline = 'middle';
                                                ctx.fillStyle = '#fff';
                                                ctx.font = isMobileView ? '9px Arial' : 'bold 11px Arial';
                                                ctx.fillText(value, position.x, position.y);
                                            }
                                        });
                                    }
                                });
                            }
                        }]
                    });
                }
            }
        };

        createChart();

        return () => {
            if (chartInstance.current) {
                chartInstance.current.destroy();
            }
        };
    }, [chartData, chartType, title]);

    return (
        <div style={{ height, width: '100%', position: 'relative' }}>
            {!chartData && (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <p>No data available</p>
                </div>
            )}
            <canvas ref={chartRef}></canvas>
        </div>
    );
};

export default AgentPerformance;