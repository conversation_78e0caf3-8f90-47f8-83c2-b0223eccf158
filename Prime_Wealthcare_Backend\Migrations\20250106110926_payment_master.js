exports.up = function (knex) {
    return knex.schema.createTable('payment_master', (table) => {
        table.increments('id').primary();
        table.string('TransactionID').notNullable();// Primary Key
        table.string('PaymentType').notNullable();
        table.string('PaymentOption').notNullable();
        table.index('ProposalNumber');
        table.string('ProposalNumber').notNullable(); // Foreign Key
        table.string('UserIdentifier').notNullable();
        table.string('UserId').notNullable();
        table.string('FirstName').notNullable();
        table.string('LastName').notNullable();
        table.string('Mobile').notNullable();
        table.string('Email').notNullable();
        table.string('Vendor').notNullable();
        table.string('CheckSum').notNullable();
        table.string('Status').notNullable();

        // Cash payment fields
        table.integer('cash_amount').nullable();
        table.date('received_date').nullable();

        // Cheque payment fields
        table.integer('cheque_amount').nullable();
        table.string('cheque_number').nullable();
        table.date('cheque_date').nullable();
        table.string('bank_name').nullable();
        table.string('branch_name').nullable();

        // DD payment fields
        table.integer('dd_amount').nullable();
        table.string('dd_number').nullable();
        table.date('dd_date').nullable();

        // Online payment fields
        table.integer('online_amount').nullable();
        table.date('transaction_date').nullable();
        table.string('transaction_type').nullable();
        table.string('receipt_number').nullable();
        table.string('ResponseURL').notNullable();
        table.string('WS_P_ID').nullable();
        table.string('PGID', 255).nullable();

        table.timestamps(true, true);
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('payment_master');
};
