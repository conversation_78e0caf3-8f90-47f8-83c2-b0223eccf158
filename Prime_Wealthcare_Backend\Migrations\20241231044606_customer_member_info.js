exports.up = function (knex) {
    return knex.schema.createTable('customer_member_info', function (table) {
        table.increments('id').primary();
        table.integer('customer_id').unsigned().references('id').inTable('customer_personal_info').notNullable();
        table.string('full_name').notNullable();

        // Foreign key from pick_list for gender, education, marital status
        table.integer('relation_id').unsigned().references('id').inTable('pick_list').nullable();
        table.integer('gender_id').unsigned().references('id').inTable('pick_list').nullable();
        table.date('date_of_birth').nullable();        // Personal information
        table.string('mobile').nullable();
        table.string('email').nullable();
        table.string('aadhar_number').nullable();
        table.string('pan_number').nullable();
        table.integer('marital_status_id').unsigned().references('id').inTable('pick_list').nullable();
        table.timestamp('marriage_date').nullable();
        table.integer('member_occupation').unsigned().references('id').inTable('pick_list');

        table.string('member_height').nullable(); // Assuming height in meters or similar
        table.string('member_weight').nullable(); // Assuming weight in kilograms or similar
        table.string('isSmoking').nullable(); // Boolean for smoking status
        table.string('isTobacco').nullable(); // Boolean for tobacco usage
        table.string('smokingPerDay', 255).nullable(); // Number of cigarettes per day
        table.string('tobaccoPerDay', 255).nullable(); // Amount of tobacco per day
        table.string('diseaseDetails', 255).nullable(); // Details of diseases
        table.string('discarge_summaryFile', 255).nullable(); // Path to prescription file
        table.string('preExistingDisease').nullable(); // Description of pre-existing disease
        // Common Fields
        table.boolean('status').notNullable().defaultTo(true); // Status field (Active or Inactive)
        table.integer('created_by').notNullable().defaultTo(1);
        table.integer('updated_by').notNullable().defaultTo(1);
        // Timestamps
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('customer_member_info');
};
