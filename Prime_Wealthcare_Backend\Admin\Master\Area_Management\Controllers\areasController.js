const Area = require('../Models/areas');

// Get all areas
exports.getAreas = async (req, res, next) => {
    try {
        const data = await Area.getAllAreas();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get areas by pincode
exports.getAreasWithPincodeAndCity = async (req, res, next) => {
    try {
        const { pincode, city } = req.params;
        const data = await Area.getAreasByPincodeAndCity(pincode, city);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get areas by pincode_city
exports.getAreasByPincode_City = async (req, res, next) => {
    try {
        const { pincode_city } = req.params;
        const data = await Area.getAreasByPincode_City(pincode_city);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

exports.getAreaById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const data = await Area.getAreaById(id);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
}

// Create a new area
exports.createArea = async (req, res, next) => {
    try {
        const areaData = req.body;
        await Area.createArea(areaData);
        res.status(200).json({ message: 'Sub-area created successfully' });
    } catch (error) {
        next(error);
    }
};

// Update area by ID
exports.updateArea = async (req, res, next) => {
    try {
        const { id } = req.params;
        const areaData = req.body;
        await Area.updateArea(id, areaData);
        res.status(200).json({ message: 'Sub-area updated successfully' });
    } catch (error) {
        next(error);
    }
};

// Soft delete (deactivate) area by ID
exports.deleteArea = async (req, res, next) => {
    try {
        const { id } = req.params;
        await Area.deleteArea(id);
        res.status(200).json({ message: 'Sub-area deactivated successfully' });
    } catch (error) {
        next(error);
    }
};
