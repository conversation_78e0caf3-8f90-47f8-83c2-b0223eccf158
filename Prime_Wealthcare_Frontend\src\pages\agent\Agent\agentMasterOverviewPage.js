import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup, Typography, Grid, Avatar } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import { useNavigate, useParams } from 'react-router-dom';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useDispatch, useSelector } from 'react-redux';
import DetailsDropdown from '../../../components/table/DetailsDropdown';
import CustomSection from '../../../components/CustomSection';
import { getAgentBankDetailsByAgentId, getAgentById } from '../../../redux/actions/action';
import ExportToPDF from '../../../components/ExportToPDF';
import { renderAvatar, maskDOB, maskMobileNumber, maskEmail } from '../../../utils/Reusable';
import dayjs from 'dayjs';

function AgentMasterOverviewPage() {
    const { id } = useParams();
    const [selectedOption, setSelectedOption] = useState('none');
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const items = useSelector(state => state.areaManagementReducer.items);
    const [sortedItems, setSortedItems] = useState(items || []);

    const agentInformation = useSelector(state => state.agentReducer.agent);
    const agentBankDetails = useSelector(state => state.agentBankReducer.agentBankDetails);


    useEffect(() => {
        dispatch(getAgentById(id));
        dispatch(getAgentBankDetailsByAgentId(id));
    }, [id, dispatch])

    useEffect(() => {
        setSortedItems(items);
    }, [items]);

    const handleAdd = () => {
        navigate('/dashboard/agent-personal-information'); // Navigate to the create personal detail page
    };

    const [selectedRows, setSelectedRows] = useState([]);

    const dataMapping = {
        'Full Name': 'full_name',
        'User ID': 'agent_id',
        'Office Mobile': 'official_mobile',
        'Office Email': 'official_email',
        'Department': 'department_name',
        'Role': 'role_name',
        'Personal Mobile': 'personal_mobile',
        'Personal Email': 'personal_email',
        'Joining Date': 'date_of_joining',
        'Branch Name': 'branch_name',
        'City': 'branch_city',
        'Date of Birth': 'dob'
    }

    const handleCancel = () => {
        navigate('/dashboard/agent-master');
    };

    return (
        <Container maxWidth="xl">
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName='Agent' pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        <Button
                            onClick={handleAdd}
                            sx={{
                                borderTopRightRadius: 0,
                                borderBottomRightRadius: 0,
                                border: '1px solid', // Add border
                                borderColor: 'primary.main' // Use theme color for border
                            }}
                        >
                            New
                        </Button>
                        <ExportToPDF
                            data={agentInformation}
                            headNames={['Full Name', 'User ID', 'Office Mobile', 'Office Email', 'Department', 'Role', 'Personal Mobile', 'Personal Email', 'Joining Date', 'Branch Name', 'City', 'Date of Birth']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="Agent_Personal_Details.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Agent Report"
                        />
                        <Button
                            onClick={handleCancel}
                            sx={{
                                borderColor: 'red',
                                color: 'red',
                                borderTopLeftRadius: 0,
                                borderBottomLeftRadius: 0,
                                mr: '8px',
                                border: '1px solid' // Ensure the border is visible
                            }}
                        >
                            Cancel
                        </Button>
                    </ButtonGroup>
                </Box>
                <Grid container spacing={2}>
                    <CustomSection titles={['Overview', 'Personal Details', 'Address']} page='agent' />
                </Grid>
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem', borderBlock: '1px solid black' }}>
                    <Box mr={2}>
                        {renderAvatar(agentInformation?.photo, agentInformation?.full_name)}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                        <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Full Name:</strong> {agentInformation?.full_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>User ID:</strong> {agentInformation?.agent_id}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Mobile:</strong> {agentInformation?.official_mobile}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Email:</strong> {agentInformation?.official_email}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Department:</strong> {agentInformation?.department_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Role:</strong> {agentInformation?.role_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Mobile:</strong> {maskMobileNumber(agentInformation?.personal_mobile)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Email:</strong> {maskEmail(agentInformation?.personal_email)}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Joining Date:</strong> {dayjs(agentInformation?.date_of_joining).format('DD-MM-YYYY')}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Branch Name:</strong> {agentInformation?.branch_name}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>City:</strong> {agentInformation?.branch_city}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Date of Birth:</strong> {maskDOB(agentInformation?.dob)}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1.25rem' }}>
                    <DetailsDropdown
                        headerText={`Bank Details (${agentBankDetails?.length})`}
                        tableHeadings={['Account Holder Name', 'Account Number', 'Bank Name', 'IFSC Code', 'Bank Branch', 'Status', 'Created At', 'Updated At']}
                        tableData={agentBankDetails?.map(detail => ({
                            // 'Agent Name': detail.agent_name,
                            // 'User Id': detail.user_id,
                            'Account Holder Name': detail.account_holder_name,
                            'Account Number': detail.account_number,
                            'Bank Name': detail.bank_name,
                            'IFSC Code': detail.IFSC_code,
                            'Bank Branch': detail.branch_name,
                            'Status': detail.status,
                            'Created At': detail.created_at,
                            'Updated At': detail.updated_at,
                        }))}
                        // onEdit={() => navigate(`/dashboard/agent-bank-details-form/edit/${id}`)}
                        // onView={() => navigate(`/dashboard/agent-bank-details-form/view/${id}`)}
                        // handleCreate={agentBankDetails.length === 0 ? () => navigate(`/dashboard/agent-bank-details-form/${id}`) : null}
                        onEdit={agentInformation?.status !== 0 ? () => navigate(`/dashboard/agent-bank-details-form/edit/${id}`) : () => { }}
                        onView={() => navigate(`/dashboard/agent-bank-details-form/view/${id}`)}
                        handleCreate={agentInformation?.status !== 0 && agentBankDetails.length === 0
                            ? () => navigate(`/dashboard/agent-bank-details-form/${id}`)
                            : null}
                    />




                    {/* <DetailsDropdown
                        headerText={`Salary Details (${agentInformation?.salary?.length})`}
                        tableHeadings={['Agent Name', 'User Id', 'Joining Date', 'Gross Salary', 'Deductable', 'Referral Amount', 'Net Salary', 'Year', 'Created At', 'Updated At']}
                        tableData={agentInformation?.salary?.map(salary => ({
                            'Agent Name': salary.fullName,
                            'User Id': salary.userId,
                            'Joining Date': salary.joiningDate,
                            'Gross Salary': salary.grossSalary,
                            'Deductable': salary.deductable,
                            'Referral Amount': salary.referalAmount,
                            'Net Salary': salary.netSalary,
                            'Year': salary.year,
                            'Created At': salary.createdAt,
                            'Updated At': salary.updatedAt,
                        }))}
                    />

                    <DetailsDropdown
                        headerText={`Task Management (${agentInformation?.taskManagement?.length})`}
                        tableHeadings={['Assigned To', 'User Id', 'Department', 'In Progress', 'Completed']}
                        tableData={agentInformation?.taskManagement?.map(task => ({
                            'Assigned To': task.assignedTo,
                            'User Id': task.userId,
                            'Department': task.department,
                            'In Progress': task.inProgress,
                            'Completed': task.completed,
                        }))}
                    /> */}
                </Box>
            </Box>
        </Container>
    );
}

export default AgentMasterOverviewPage;
