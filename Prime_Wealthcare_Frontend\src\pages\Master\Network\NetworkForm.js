import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { getNetworkById, updateNetwork, createNetwork, getLocationByPincode, fetchInsuranceCompanies, getNetworkByName, getAllNetworks, getAreasByPincodeAndCity } from '../../../redux/actions/action';
import DropDown from '../../../components/table/DropDown';
import { trimFormData } from '../../../utils/Reusable';
import MultiSelectDropdown from '../../../components/table/MultiSelectDropdown';
import { FormControl } from '@mui/material';

function NetworkForm() {
    const { id } = useParams();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const network = useSelector(state => state.networkReducer.network);
    const networks = useSelector(state => state.networkReducer.networks);
    const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
    const locationData = useSelector(state => state.areaManagementReducer.locations);
    const [formData, setFormData] = useState({
        hospital_name: '',
        insurance_company_id: [],
        helpline_number: '',
        mobile_number: '',
        email_id: '',
        address_line_1: '',
        address_line_2: '',
        pincode: '',
        area: '',
        city: '',
        state: ''
    });
    const [errors, setErrors] = useState({
        hospital_name: false,
        insurance_company_id: false,
        helpline_number: false,
        mobile_number: false,
        email_id: false,
        pincode: false,
    });
    const [cityOptions, setCityOptions] = useState([]);
    const [areaOptions, setAreaOptions] = useState([]);
    const [selectedInsuranceCompany, setSelectedInsuranceCompany] = useState([]);

    useEffect(() => {
        dispatch(fetchInsuranceCompanies()).then(res => {
            console.log('these are the insurance companies', res.payload);
        });
        if (id) {
            dispatch(getNetworkById(id));
        } else {
            resetForm();
        }
    }, [id, dispatch]);

    useEffect(() => {
        if (network && id) {
            // Keep insurance company IDs as numbers
            const insuranceCompanyIds = network.insurance_company_id
                ? network.insurance_company_id.split(',').map(Number)
                : [];

            setFormData(prevFormData => ({
                ...prevFormData,
                hospital_name: network.hospital_name || '',
                insurance_company_id: insuranceCompanyIds,
                helpline_number: network.helpline_number || '',
                mobile_number: network.mobile_number || '',
                email_id: network.email_id || '',
                address_line_1: network.address_line_1 || '',
                address_line_2: network.address_line_2 || '',
                pincode: network.pincode || '',
                area: network.area || '',
                city: network.city || '',
                state: network.state || ''
            }));

            // Store the IDs in selectedInsuranceCompany instead of names
            setSelectedInsuranceCompany(insuranceCompanyIds);
        }
    }, [network, id]);

    useEffect(() => {
        if (String(formData.pincode).length === 6) {
            dispatch(getLocationByPincode(formData.pincode)).then((action) => {
                if (!action.payload || action.payload.length === 0) {
                    setErrors(prevErrors => ({ ...prevErrors, pincode: 'This pincode does not exist' }));
                    setFormData(prevFormData => ({
                        ...prevFormData,
                        area: '',
                        city: '',
                        state: ''
                    }));
                } else {
                    const uniqueCities = [...new Set(action.payload.map(location => location.city))];
                    const data = uniqueCities.map(city => ({ label: city, value: city }));
                    setCityOptions(data);
                    setFormData(prevFormData => ({
                        ...prevFormData,
                        state: network?.state || action.payload[0].state,
                        city: network?.city
                    }));
                }
            });
        }
    }, [formData.pincode, network?.city, dispatch]);

    useEffect(() => {
        if (formData.city && formData.pincode) {
            dispatch(getAreasByPincodeAndCity({ pincode: formData.pincode, city: formData.city })).then((action) => {
                if (action.payload.length > 0) {
                    const data = action.payload.map(area => ({ label: area.area, value: area.id }));
                    setAreaOptions(data);
                    setFormData(prevFormData => ({
                        ...prevFormData,
                        area: network?.area ? network.area : data.length === 1 ? data[0].value : ''
                    }));
                } else {
                    setAreaOptions([]);
                    setFormData(prevFormData => ({
                        ...prevFormData,
                        area: ''
                    }));
                }
            });
        }
        if (formData.city) {
            setErrors(prevErrors => ({
                ...prevErrors,
                city: ''
            }));
        }
    }, [formData.city, formData.pincode, network?.area, dispatch]);

    useEffect(() => {
        if (locationData && formData.pincode) {
            setFormData(prevFormData => ({
                ...prevFormData,
                area: locationData.area || '',
                city: locationData.city || '',
                state: locationData.state || ''
            }));
        }
    }, [locationData, formData.pincode]);

    // Reset form data
    const resetForm = () => {
        setFormData({
            hospital_name: '',
            insurance_company_id: '',
            helpline_number: '',
            mobile_number: '',
            email_id: '',
            address_line_1: '',
            address_line_2: '',
            pincode: '',
            area: '',
            city: '',
            state: ''
        });
        setSelectedInsuranceCompany([])
    };

    const validate = () => {
        let tempErrors = {};

        if (!formData.hospital_name) {
            tempErrors.hospital_name = "Hospital Name is required.";
        }
        if (formData.hospital_name) {
            const isDuplicate = networks.find(network => network.hospital_name === formData.hospital_name && network.id !== Number(id))
            if (isDuplicate) {
                tempErrors.hospital_name = "Hospital name already exists.";
            }
        }
        if (!formData.insurance_company_id ||
            (Array.isArray(formData.insurance_company_id) &&
                formData.insurance_company_id.length === 0)) {
            tempErrors.insurance_company_id = "Insurance Company is required.";
        }
        if (formData.email_id && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.email_id)) {
            tempErrors.email_id = 'Please enter a valid email address';
        }
        if (formData.helpline_number && !/^\d{10,12}$/.test(formData.helpline_number)) {
            tempErrors.helpline_number = 'Please enter a valid 10-12 digit helpline number';
        }
        if (formData.mobile_number && !/^[6-9]\d{9}$/.test(formData.mobile_number)) tempErrors.mobile_number = 'Please enter a valid mobile number'
        if (!formData.pincode) tempErrors.pincode = "Pincode is required.";
        if (!/^[1-9][0-9]{5}$/.test(formData.pincode)) {
            tempErrors.pincode = 'Pincode should be a 6-digit number starting with a non-zero digit';
        } else {
            if (locationData.length === 0) {
                tempErrors.pincode = 'This pincode does not exist'
            }
        }
        if (!formData.city) tempErrors.city = 'City is required.';
        setErrors(tempErrors);

        return Object.keys(tempErrors).length === 0;
    };

    const handleChange = (e) => {
        const { name, value } = e.target;

        // Check if the value is a space, or if the length is too long for pincode, helpline_number, or mobile_number
        if (value === ' ') {
            setErrors(prevErrors => ({
                ...prevErrors,
                [name]: 'Do not start with a whitespace character'
            }));
            return;
        }
        if ((name === 'pincode' && value.length > 6) ||
            (name === 'mobile_number' && value.length > 10) ||
            (name === 'helpline_number' && value.length > 12)) {
            return;
        }

        if (name === 'email_id' && value.charAt(value.length - 1) === ' ') {
            setErrors(prevErrors => ({
                ...prevErrors,
                [name]: 'Whitespace characters are not allowed in email'
            }));
            return;
        }

        let data = value;

        // Ensure that the value is numeric for pincode, helpline_number, or mobile_number
        if ((name === 'pincode' || name === 'helpline_number' || name === 'mobile_number') && !/^\d*$/.test(value)) {
            setErrors(prevErrors => ({
                ...prevErrors,
                [name]: 'Please enter a numeric value'
            }));
            return;
        }

        if (name !== 'insurance_company_id' && name !== 'area') {
            if (name === 'email_id') {
                data = value.toLowerCase().replace(/\s{2,}$/, ' ');
            } else if (name === 'address_line_1' || name === 'address_line_2') {
                const regex = /^[a-zA-Z0-9,.\-\s#/&]+$/;
                if (!regex.test(data) && data !== '') {
                    setErrors(prevErrors => ({
                        ...prevErrors,
                        [name]: 'Address must only contain letters, numbers, spaces, and special characters (,.-/#& )'
                    }));
                    return;
                }
                data = value.split(' ').map(word => {
                    return word.charAt(0).toUpperCase() + word.slice(1);
                }).join(' ').replace(/\s{2,}$/, ' ');
            }
            else {
                data = value.toUpperCase().replace(/\s{2,}$/, ' ');
            }
        }

        setFormData(prevFormData => ({
            ...prevFormData,
            [name]: data
        }));
        setErrors(prevErrors => ({
            ...prevErrors,
            [name]: false,
        }));
        if (name === 'pincode' && String(data).length !== 6) {
            setAreaOptions([]);
            setCityOptions([]);
            setFormData(prevFormData => ({
                ...prevFormData,
                area: '',
                city: '',
                state: ''
            }));
        }
    };

    const handleCompanyChange = (event) => {
        const selectedIds = event.target.value;
        setSelectedInsuranceCompany(selectedIds);

        setFormData(prev => ({
            ...prev,
            insurance_company_id: selectedIds,
        }));

        if (selectedIds.length > 0) {
            setErrors(prevErrors => ({
                ...prevErrors,
                insurance_company_id: null,
            }));
        } else {
            setErrors(prevErrors => ({
                ...prevErrors,
                insurance_company_id: "Insurance Company is required.",
            }));
        }
    };

    const handleNetworkDuplicateCheck = async () => {
        const { hospital_name } = formData;
        if (hospital_name) {
            await dispatch(getNetworkByName(hospital_name)).then((action) => {
                if (action.payload.length > 0) {

                    setErrors(prevErrors => ({
                        ...prevErrors,
                        hospital_name: 'Hospital name already exists.'
                    }));
                }
            });
        }
    };

    const handleNetworkCreationAndUpdate = () => {
        const isValid = validate();
        if (!isValid) return;

        const data = trimFormData(formData);
        const newData = {
            ...data,
            area: data.area ? data.area : null,
            insurance_company_id: Array.isArray(data.insurance_company_id)
                ? data.insurance_company_id.join(',')
                : data.insurance_company_id,
        };

        if (id) {
            dispatch(updateNetwork({ id, formData: newData }));
        } else {
            dispatch(createNetwork(newData));
        }

        setTimeout(() => {
            dispatch(getAllNetworks());
        }, 500);
    };

    const handleSaveAndNew = () => {
        if (validate()) {
            handleNetworkCreationAndUpdate();
            resetForm();
        }
    };

    const handleSave = () => {
        if (validate()) {
            handleNetworkCreationAndUpdate();
            navigate('/dashboard/network-list');
        }
    };

    const handleCancel = () => {
        navigate('/dashboard/network-list');
    };

    return (
        <form style={{ padding: ' 0 5rem' }}>
            <Grid container spacing={2}>
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
                    <Box sx={{ width: '100%', margin: '0 20px' }}>
                        <ModuleName moduleName="Network" pageName={id ? network?.status === 0 && id ? "View" : "Edit" : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={3} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!id && <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                        onClick={handleSaveAndNew}
                        disabled={id && network?.status === 0 && id}
                    >
                        Save & New
                    </Button>}

                    {(network?.status === 1 || !id) && (
                        <Button
                            variant="outlined"
                            size="small"
                            sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                            onClick={handleSave}
                        >
                            Save
                        </Button>
                    )}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>

                {/* Network Hospital */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
                        <h2>Network Hospital</h2>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ display: 'flex', justifyContent: 'center', margin: '1rem 2rem', 'paddingInline': '1rem' }}>
                    <Grid item xs={4} sx={{ margin: '0, 30px' }}>
                        <CustomTextField
                            label="Hospital Name"
                            name="hospital_name"
                            value={formData.hospital_name}
                            onChange={handleChange}
                            onBlur={handleNetworkDuplicateCheck}
                            fullWidth
                            helperText={errors.hospital_name || ''}
                            isDisabled={id ? true : false}
                            isRequired
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px' }}>
                        <FormControl fullWidth required error={Boolean(errors.insurance_company_id)}>
                            <MultiSelectDropdown
                                label="Insurance Company"
                                name="insurance_company_id"
                                required
                                options={insuranceCompanies.filter(company => company.status === 1).map(company => ({ label: company.short_name, value: company.id }))}
                                value={selectedInsuranceCompany}
                                onChange={handleCompanyChange}
                                fullWidth
                                disabled={id ? true : false}
                                helperText={errors.insurance_company_id || ''}
                            />
                        </FormControl>
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px' }}>
                        <CustomTextField
                            label="Help Line Number"
                            name="helpline_number"
                            value={formData.helpline_number}
                            onChange={handleChange}
                            type="tel"
                            pattern="[0-9]*"
                            isDisabled={network?.status === 0 && id}
                            helperText={errors.helpline_number}
                            inputProps={{
                                maxLength: 12,
                                onInput: (e) => {
                                    e.target.value = e.target.value.replace(/[^0-9]/g, '').slice(0, 12);
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px' }}>
                        <CustomTextField
                            label="Mobile Number"
                            name="mobile_number"
                            value={formData.mobile_number}
                            onChange={handleChange}
                            type="tel"
                            pattern="[0-9]{10}"
                            applyPrefix
                            isDisabled={network?.status === 0 && id}
                            helperText={errors.mobile_number}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px' }}>
                        <CustomTextField
                            label="Email Id"
                            name="email_id"
                            value={formData.email_id}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.email_id || ''}
                            isRequired
                            isDisabled={network?.status === 0 && id}
                        />
                    </Grid>
                </Grid>

                {/* Address Information */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
                        <h2>Address Information</h2>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ display: 'flex', justifyContent: 'center', margin: '1rem 2rem', 'paddingInline': '1rem' }}>
                    <Grid item xs={4} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Address Line 1"
                            name="address_line_1"
                            value={formData.address_line_1}
                            onChange={handleChange}
                            fullWidth
                            isDisabled={network?.status === 0 && id}
                            helperText={errors.address_line_1}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Address Line 2"
                            name="address_line_2"
                            value={formData.address_line_2}
                            onChange={handleChange}
                            fullWidth
                            isDisabled={network?.status === 0 && id}
                            helperText={errors.address_line_2}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="Pincode"
                            name="pincode"
                            value={formData.pincode}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.pincode || ''}
                            isRequired
                            isDisabled={network?.status === 0 && id}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <DropDown
                            label="City"
                            name="city"
                            options={cityOptions}
                            value={formData.city}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.city || ''}
                            required
                            disabled={network?.status === 0 && id}
                        />
                    </Grid>
                    <Grid item xs={4} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <DropDown
                            label="Area"
                            name="area"
                            options={areaOptions}
                            value={formData.area}
                            onChange={handleChange}
                            fullWidth
                            disabled={network?.status === 0 && id}
                        />
                    </Grid>

                    <Grid item xs={4} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
                        <CustomTextField
                            label="State"
                            name="state"
                            value={formData.state}
                            onChange={handleChange}
                            fullWidth
                            isDisabled
                            isRequired
                        />
                    </Grid>
                </Grid>
            </Grid>
        </form>
    );
}

export default NetworkForm;