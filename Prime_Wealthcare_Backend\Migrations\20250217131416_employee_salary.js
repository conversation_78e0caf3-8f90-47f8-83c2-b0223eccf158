exports.up = function (knex) {
    return knex.schema.createTable('employee_salary', (table) => {
        table.increments('id').primary();
        table.integer('employee_id').unsigned().references('id').inTable('employee_personal_info').onDelete('CASCADE');
        table.integer('gross_salary').notNullable();
        table.integer('pf_amount').nullable();
        table.integer('esic_amount').nullable();
        table.integer('insurance_amount').nullable();
        table.integer('deductible_amount').notNullable();
        table.integer('tenure').nullable();
        table.date('deductible_end_date').nullable();
        table.integer('net_salary').notNullable();
        table.string('year').notNullable();
        table.boolean('status').notNullable().defaultTo(true);
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('employee_salary');
};

