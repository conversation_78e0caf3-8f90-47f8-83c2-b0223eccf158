const EmployeeMaster = require('../Models/employee_address_model');

// Create new Employee
exports.createEmployeeAddress = async (req, res, next) => {
    try {
        const EmployeeData = req.body;
        await EmployeeMaster.create(EmployeeData);
        res.status(200).json({ message: 'Employee Address created successfully' });
    } catch (error) {
        next(error);
    }
};

// Get all Employee
exports.getEmployeeAddress = async (req, res, next) => {
    try {
        const data = await EmployeeMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get Employee by ID
exports.getCostomerAddressById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const Employee = await EmployeeMaster.findById(id);
        if (Employee) {
            res.status(200).json(Employee);
        } else {
            res.status(404).json({ message: 'Employee Address not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Get Employee by ID
exports.getEmployeeAddressByEmployeeId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const Employee = await EmployeeMaster.findByEmployeeId(id);
        // console.log('this is the employee address', Employee)
        res.status(200).json(Employee);
        // if (Employee) {
        // } else {
        //     res.status(404).json({ message: 'Employee not found' });
        // }
    } catch (error) {
        next(error);
    }
};
// Update Employee by ID
exports.updateEmployeeAddress = async (req, res, next) => {
    try {
        const { id } = req.params;
        const EmployeeData = req.body;
        const data = await EmployeeMaster.update(id, EmployeeData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
// Delete a role by ID
exports.deleteEmployeeAddress = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await EmployeeMaster.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Address not found' });
        }
        res.status(204).json({ message: 'group deleted successfully' });
    } catch (error) {
        next(error);
    }
};