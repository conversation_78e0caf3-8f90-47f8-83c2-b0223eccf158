/* // BarLoader.js
import React from 'react';
import LinearProgress from '@mui/material/LinearProgress';
import Box from '@mui/material/Box';

const BarLoader = ({ loading }) => {
    return (
        loading ? (
            <Box sx={{ width: '100%', position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1000 }}>
                <LinearProgress />
            </Box>
        ) : null
    );
};

export default BarLoader;
 */

import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';

const CircularLoader = ({ loading }) => {
    return (
        loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                <CircularProgress />
            </Box>
        ) : null
    );
};

export default CircularLoader

