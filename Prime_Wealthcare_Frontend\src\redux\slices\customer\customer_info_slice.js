import { createSlice } from '@reduxjs/toolkit';
import { createCustomerInfo, updateCustomer, getCustomerById, getAllCustomer, getCustomerAndAddress } from '../../actions/action';
import { toast } from 'react-toastify';

// customer Info Slice
const customer_info_slice = createSlice({
    name: 'customerInfo',
    initialState: {
        customer: [], // to store list of customers
        customerDetails: null,
        customerAndAddress: [], // Initialize with empty array // to store a single customer's details
        loading: false,
        error: null, // for error handling
    },
    reducers: {
        // Any additional synchronous actions can be defined here if needed
        clearCustomerDetails(state) {
            state.customerDetails = null;
            state.customerAndAddress = null;
        },
    },
    extraReducers: (builder) => {
        // Handle createCustomerInfo (creating a new customer)
        builder
            .addCase(createCustomerInfo.pending, (state) => {
                state.loading = true;
            })
            .addCase(createCustomerInfo.fulfilled, (state, action) => {
                state.loading = false;
                state.customer.push(action.payload); // Add the new customer to the list
                toast.success('Customer created successfully');
            })
            .addCase(createCustomerInfo.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to create Customer');
            })
            // Get All Customers
            .addCase(getAllCustomer.pending, (state) => {
                state.loading = true; // Use status for consistency
            })
            .addCase(getAllCustomer.fulfilled, (state, action) => {
                state.loading = false;
                state.customer = action.payload; // Update the customer list to use 'customer' instead of 'customers'
            })
            .addCase(getAllCustomer.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch customers');
            })

            // Get customer By Id
            .addCase(getCustomerById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getCustomerById.fulfilled, (state, action) => {
                state.loading = false;
                state.customerDetails = action.payload; // Store the single customer details
            })
            .addCase(getCustomerById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                //  toast.error('Failed to load Customer');
            })
            // Get All CustomersAddress
            .addCase(getCustomerAndAddress.pending, (state) => {
                state.loading = true; // Use status for consistency
            })
            .addCase(getCustomerAndAddress.fulfilled, (state, action) => {
                state.loading = false;
                state.customerAndAddress = action.payload; // Update the customer list to use 'customer' instead of 'customers'
            })
            .addCase(getCustomerAndAddress.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
                toast.error('Failed to fetch customers');
            });
    },
});

// Export the synchronous actions, if any
export const { clearCustomerDetails } = customer_info_slice.actions;

// Export the reducer to be included in the store
export default customer_info_slice.reducer;