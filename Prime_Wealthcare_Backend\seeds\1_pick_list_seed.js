exports.seed = async function (knex) {
  // Deletes ALL existing entries
  await knex('pick_list').del();

  // Inserts seed entries
  await knex('pick_list').insert([
    { type_name: 'Gender', api_name: 'M', label_name: 'Male', is_active: true, },
    { type_name: 'Gender', api_name: 'F', label_name: 'Female', is_active: true, },
    { type_name: 'Gender', api_name: 'O', label_name: 'Other', is_active: true, },
    { type_name: 'Marital Status', api_name: 'S', label_name: 'Single', is_active: true, },
    { type_name: 'Marital Status', api_name: 'M', label_name: 'Married', is_active: true, },
    { type_name: 'Marital Status', api_name: 'W', label_name: 'Widow', is_active: true, },
    { type_name: 'Marital Status', api_name: 'D', label_name: 'Divorced', is_active: true, },
    { type_name: 'Marital Status', api_name: 'L', label_name: 'Live In Relationship', is_active: true, },
    { type_name: 'Education', api_name: 'E_SSC', label_name: 'SSC', is_active: true, },
    { type_name: 'Education', api_name: 'E_HSC', label_name: 'HSC', is_active: true, },
    { type_name: 'Education', api_name: 'E_Graduation', label_name: 'Graduation', is_active: true, },
    { type_name: 'Education', api_name: 'E_Masters', label_name: 'Masters', is_active: true, },
    { type_name: 'Education', api_name: 'E_PostGraduate', label_name: 'Post Graduate', is_active: true, },
    { type_name: 'Insurance Type', api_name: 'INS_General', label_name: 'General Insurance', is_active: true, },
    { type_name: 'Insurance Type', api_name: 'INS_StandAlonehealth', label_name: 'Stand Alone Health', is_active: true, },
    { type_name: 'Insurance Type', api_name: 'INS_Life', label_name: 'Life Insurance', is_active: true, },
    { type_name: 'Commission Payable Type', api_name: 'COMMPAY_Receivable', label_name: 'Receivable', is_active: true, },
    { type_name: 'Commission Payable Type', api_name: 'COMMPAY_Payable', label_name: 'Payable', is_active: true, },
    { type_name: 'Commission Payable Type', api_name: 'COMMPAY_LeaderPayable', label_name: 'Leader Payable', is_active: true, },
    { type_name: 'Commission Source', api_name: 'COMMSOURCE_IRDA', label_name: 'IRDA', is_active: true, },
    { type_name: 'Commission Source', api_name: 'COMMSOURCE_ORC', label_name: 'ORC', is_active: true, },
    { type_name: 'Policy Type', api_name: 'POLICY_New', label_name: 'New', is_active: true, },
    { type_name: 'Policy Type', api_name: 'POLICY_Renewal', label_name: 'Renewal', is_active: true, },
    { type_name: 'Policy Type', api_name: 'POLICY_RollOver', label_name: 'Roll Over', is_active: true, },
    { type_name: 'Policy Type', api_name: 'POLICY_Endorsement', label_name: 'Endorsement', is_active: true, },
    { type_name: 'Policy Type', api_name: 'POLICY_Migration', label_name: 'Migration', is_active: true, },
    { type_name: 'Range Type', api_name: 'RANGE_SumInsured', label_name: 'Sum Insured', is_active: true, },
    { type_name: 'Range Type', api_name: 'RANGE_Premium', label_name: 'Premium', is_active: true, },
    { type_name: 'Endorsement Type', api_name: 'END_Financial', label_name: 'Financial', is_active: true, },
    { type_name: 'Endorsement Type', api_name: 'END_NonFinancial', label_name: 'Non Financial', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_HOSPITAL_BILL', label_name: 'Hospitalization Expenses', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_MEDICINE_BILL', label_name: 'Medicines Expenses', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_DIAGNOSTIC_TEST_BILL', label_name: 'Diagnostic Test Expenses', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_SURGERY_BILL', label_name: 'Surgery Expenses', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_CONSULTATION_BILL', label_name: 'Consultation Fees', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_AMBULANCE_BILL', label_name: 'Ambulance Charges', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_ROOM_RENT_BILL', label_name: 'Room Rent Charges', is_active: true, },
    { type_name: 'Claim Bill', api_name: 'CLM_MISC_BILL', label_name: 'Miscellaneous Expenses', is_active: true, },
    { type_name: 'Relation', api_name: 'SELF', label_name: 'SELF', is_active: true, },
    { type_name: 'Relation', api_name: 'SPOU', label_name: 'SPOUSE', is_active: true, },
    { type_name: 'Relation', api_name: 'SON', label_name: 'SON', is_active: true, },
    { type_name: 'Relation', api_name: 'DAUG', label_name: 'DAUGHTER', is_active: true, },
    { type_name: 'Relation', api_name: 'MOTH', label_name: 'MOTHER', is_active: true, },
    { type_name: 'Relation', api_name: 'FATH', label_name: 'FATHER', is_active: true, },
    { type_name: 'Relation', api_name: 'WIFE', label_name: 'WIFE', is_active: true, },
    { type_name: 'Relation', api_name: 'HUSB', label_name: 'HUSBAND', is_active: true, },
    { type_name: 'Relation', api_name: 'CHLD', label_name: 'CHILD', is_active: true, },
    { type_name: 'Document Type', api_name: 'DOC_Passport', label_name: 'PASSPORT', is_active: true, },
    { type_name: 'Document Type', api_name: 'DOC_DrivingLicense', label_name: 'DRIVING LICENSE', is_active: true, },
    { type_name: 'Document Type', api_name: 'DOC_AadharCard', label_name: 'AADHAR CARD', is_active: true, },
    { type_name: 'Document Type', api_name: 'DOC_PANCard', label_name: 'PAN CARD', is_active: true, },
    { type_name: 'Document Type', api_name: 'DOC_VoterID', label_name: 'VOTER ID', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_A_POS', label_name: 'A+', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_A_NEG', label_name: 'A-', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_B_POS', label_name: 'B+', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_B_NEG', label_name: 'B-', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_AB_POS', label_name: 'AB+', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_AB_NEG', label_name: 'AB-', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_O_POS', label_name: 'O+', is_active: true, },
    { type_name: 'Blood Group', api_name: 'BLOOD_O_NEG', label_name: 'O-', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_VITAL', label_name: 'VITAL', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_SUPERIOR', label_name: 'SUPERIOR', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_PREMIUM', label_name: 'PREMIUM', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_CLASSIC', label_name: 'CLASSIC', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_PLATINUM', label_name: 'PLATINUM', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_SIGNATURE', label_name: 'SIGNATURE', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_SUPREME', label_name: 'Supreme', is_active: true, },
    { type_name: 'Cover Type', api_name: 'COVER_ELITE', label_name: 'Elite', is_active: true, },
    { type_name: 'Payment Type', api_name: 'PAY_CASH', label_name: 'Cash', is_active: true, },
    { type_name: 'Payment Type', api_name: 'PAY_CHEQUE', label_name: 'Cheque', is_active: true, },
    { type_name: 'Payment Type', api_name: 'PAY_DD', label_name: 'DD', is_active: true, },
    { type_name: 'Payment Type', api_name: 'PAY_ONLINE', label_name: 'Online', is_active: true, },
    { type_name: 'Payment Type', api_name: 'PAY_CHEQUE_CASH', label_name: 'Cheque + Cash', is_active: true, },
    { type_name: 'Payment Type', api_name: 'PAY_DD_CASH', label_name: 'DD + Cash', is_active: true, },
    { type_name: 'Duration', api_name: '1', label_name: '1 Year', is_active: true, },
    { type_name: 'Duration', api_name: '2', label_name: '2 Years', is_active: true, },
    { type_name: 'Duration', api_name: '3', label_name: '3 Years', is_active: true, },
    { type_name: 'Future_genrali_Occupation', api_name: 'ACCT', label_name: 'Accountant', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ACTR', label_name: 'Actor/Actress', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ADVO', label_name: 'Advocate', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'AGET', label_name: 'Agent (Insurance)', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'AIRF', label_name: 'Air Force', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'AMEX', label_name: 'Admin Executive', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ARCH', label_name: 'Architect', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ARMY', label_name: 'Army', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BAPR', label_name: 'Baggage Porter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BARB', label_name: 'Barbers', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BARM', label_name: 'Barman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BEAU', label_name: 'Beauticians', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BLRM', label_name: 'Boilerman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BROK', label_name: 'Brokers', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BULD', label_name: 'Builder', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'BUSM', label_name: 'Businessman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CARP', label_name: 'Carpenter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CASH', label_name: 'Cashier', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CEME', label_name: 'Chemical Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CHPR', label_name: 'Choreographer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CMST', label_name: 'Chemist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'COME', label_name: 'Computer Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CONS', label_name: 'Consultant', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CONW', label_name: 'Construction Site Worker', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'COOK', label_name: 'Cook', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CROP', label_name: 'Crane Operator', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'CVLE', label_name: 'Civil Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DELI', label_name: 'Deliveryman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DENT', label_name: 'Dentist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DIEC', label_name: 'Die-Cutting Machine Operator', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DIR', label_name: 'Director', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DIVE', label_name: 'Diver (Commercial/Military)', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DNCR', label_name: 'Dancer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DOCT', label_name: 'Doctor', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DRAU', label_name: 'Draughtsman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'DRIV', label_name: 'Driver', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'EDPO', label_name: 'EDP Operator', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ELET', label_name: 'Electricians', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ENGR', label_name: 'Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'ESTA', label_name: 'Estate Agents', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'EXEC', label_name: 'Executive', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'EXHL', label_name: 'Explosives handler', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FACT', label_name: 'Factory Workers', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FARM', label_name: 'Farmer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FIRE', label_name: 'Fireman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FISH', label_name: 'Fisherman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FITR', label_name: 'Fitter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FKOP', label_name: 'Forklift Operator', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FLIG', label_name: 'Flight Steward/Stewardess', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FORE', label_name: 'Foreman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FORO', label_name: 'Forest Officer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FOUD', label_name: 'Foundary Worker', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'FURN', label_name: 'Furnacemen', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'GASA', label_name: 'Gas Attendant', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'GEOL', label_name: 'Geologist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'HAWK', label_name: 'Hawkers', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'HOAT', label_name: 'Hospital Attendant', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'HOTW', label_name: 'Hotel & Restaurant Waiters', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'HROF', label_name: 'Human Resource Officer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'HSWF', label_name: 'Housewife', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'JOCK', label_name: 'Jockey', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'JUDG', label_name: 'Judge', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'JUVN', label_name: 'Juvenille', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'JWLR', label_name: 'Jeweler', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'LABR', label_name: 'Labourer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'LABT', label_name: 'Laboratory Technician', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'LCTR', label_name: 'Lecturer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MACH', label_name: 'Machinist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MAIN', label_name: 'Maintenance Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MARE', label_name: 'Marine Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MARI', label_name: 'Mariner', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MASN', label_name: 'Mason/ Plasterer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MECH', label_name: 'Mechanic', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MEEN', label_name: 'Mechanical Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MGR', label_name: 'Manager', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MILK', label_name: 'Milkman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MINE', label_name: 'Mining Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MINR', label_name: 'Miner', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MKTG', label_name: 'Marketing Executive', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MOPR', label_name: 'Machine Operators', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MRTC', label_name: 'Train Driver', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'MUSI', label_name: 'Musician/ Singer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'NAVY', label_name: 'Navy', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'NURS', label_name: 'Nurse', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'OFFR', label_name: 'Officer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'OILW', label_name: 'Oil Refinery Worker', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'OTHR', label_name: 'Other Occupation', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PAIN', label_name: 'Painter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PATH', label_name: 'Pathologist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PENS', label_name: 'Pensioner', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PEON', label_name: 'Peon', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PHAR', label_name: 'Pharmacist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PLOT', label_name: 'Pilot', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PLTE', label_name: 'Plant Technician', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PLTN', label_name: 'Politician', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PLUM', label_name: 'Plumber', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'POLM', label_name: 'Police / Constable', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'POST', label_name: 'Postman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PRDE', label_name: 'Production Engineer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PRFS', label_name: 'Professional', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PROF', label_name: 'Professor', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PRPL', label_name: 'Principal', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PRST', label_name: 'Priest', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'PWRO', label_name: 'Power Plant Operator', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RADO', label_name: 'Radiologist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RECP', label_name: 'Receptionist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RETR', label_name: 'Retired', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RIGR', label_name: 'Rigger', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RPTR', label_name: 'Reporter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RTEC', label_name: 'Radio & TV Technician', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'RTHE', label_name: 'Radiotherapist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SAIL', label_name: 'Seaman/Sailor', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SALE', label_name: 'Salesmen', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SANI', label_name: 'Sanitary Inspector', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SCIE', label_name: 'Scientist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SECG', label_name: 'Security Guard', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SECR', label_name: 'Secretary', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SHCL', label_name: 'Shipping Clerk', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SHIP', label_name: 'Shipyard Worker', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SOCI', label_name: 'Social Worker', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SOPT', label_name: 'Site Operation Technologist', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SPOT', label_name: 'Sports Person', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'STDN', label_name: 'Student', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'STFM', label_name: 'Site Foreman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'STNP', label_name: 'Stenographer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'STUN', label_name: 'Stunt performer', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SUPV', label_name: 'Supervisor', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SVCM', label_name: 'Service', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'SWEP', label_name: 'Sweeper', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'TAIL', label_name: 'Tailor', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'TEAC', label_name: 'Teacher', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'TECH', label_name: 'Technician', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'TFPO', label_name: 'Traffic Police', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'TUTO', label_name: 'Tutor', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'UNDA', label_name: 'Underwriter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'UNEM', label_name: 'Unemployed', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'WELD', label_name: 'Welder', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'WIRE', label_name: 'Wireman', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'WOCU', label_name: 'Wood Cutter', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'XRAY', label_name: 'X-Ray Technician', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'ADM', label_name: 'Admiral', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'ADV', label_name: 'Advocate', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'BRIG', label_name: 'Brigadier', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'CDR', label_name: 'Cdr.', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'COL', label_name: 'Colonel', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'DR', label_name: 'Dr.', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'LT', label_name: 'Lieutenant', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MAJ', label_name: 'Major', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MAST', label_name: 'Master', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MISS', label_name: 'Miss', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MR', label_name: 'Mr.', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MRS', label_name: 'Mrs.', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MS', label_name: 'Ms.', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'PROF', label_name: 'Professor', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'SHRI', label_name: 'Shri', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'SIR', label_name: 'Sir', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'SMT', label_name: 'Shrimati', is_active: true },
    { type_name: 'Future_genrali_Salutation', api_name: 'MX', label_name: 'Mixed', is_active: true },
    { type_name: 'Future_genrali_Occupation', api_name: 'WRIT', label_name: 'Writer', is_active: 1 }

  ]);
};
