import React, { useEffect, useState } from 'react';
import { Box, Button, ButtonGroup, Container, Grid, IconButton, Typography } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import { renderAvatar, maskDOB, maskMobileNumber, maskEmail } from '../../../utils/Reusable';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import dayjs from 'dayjs';
import { Cancel, Save } from '@mui/icons-material';
import CustomEditableTable from '../../../components/table/CustomEditableTable';
import DetailsDropdown from '../../../components/table/DetailsDropdown';
import { getAgentById, getAgentLoanById, getAgentLoanEmis, updateAgentE<PERSON>, updateAgentLoan } from '../../../redux/actions/action';
import { toast } from 'react-toastify';
import { usePermissions } from '../../../hooks/usePermissions';

function AgentEmiDetails() {
    const { id } = useParams();
    const isViewMode = useLocation().pathname.includes('view');
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const agentInformation = useSelector(state => state.agentReducer.agent);
    const agentLoan = useSelector(state => state.agentLoanReducer.agentLoanDetails);
    const agentLoanEmisData = useSelector(state => state.agentLoanReducer.agentLoanEmis);
    const { user } = useSelector((state) => state.auth);
    const [agentLoanEmis, setAgentLoanEmis] = useState([]);
    const permissions = usePermissions('Loan', 'Agent Loan EMI');

    useEffect(() => {
        dispatch(getAgentLoanById(id)).then(res => {
            dispatch(getAgentById(res.payload?.agent_id));
            dispatch(getAgentLoanEmis(res.payload?.loan_id));
        });
    }, [dispatch, id]);

    useEffect(() => {
        setAgentLoanEmis(agentLoanEmisData);
    }, [agentLoanEmisData]);

    const handleCancel = () => {
        navigate('/dashboard/agent-loans');
    }

    const handleUpdate = (emiId, field, value) => {
        const emi = agentLoanEmis?.find(emi => emi.id === emiId);
        if (!value) {
            toast.error('Error updating EMI please select a date and try again');
            return;
        }
        const closingBalance = (calculateOutstandingAmount() - Number(emi?.emi_amount)) || 0;
        const updatedEmi = {
            agent_loan_id: agentLoan?.loan_id,
            paid_date: value ? dayjs(value).format('YYYY-MM-DD') : null,
            closing_balance: closingBalance,
            paid_amount: agentLoan?.loan_amount - (closingBalance || agentLoan?.loan_amount),
            updated_by: user?.userId,
            status: 0
        }
        dispatch(updateAgentEmi({ id: emiId, emiData: updatedEmi })).then(() => {
            dispatch(getAgentLoanById(id));
            dispatch(getAgentLoanEmis(agentLoan?.loan_id));
        });
    }

    const calculateOutstandingAmount = () => {
        const outstandingAmount = agentLoan?.loan_amount - (agentLoan?.paid_amount || 0);
        return outstandingAmount || agentLoan?.loan_amount
    }

    const calculatePaidAmount = () => {
        const totalPaidAmount = agentLoanEmis?.reduce((sum, emi) => {
            if (emi.status === false) {
                return sum + emi.emi_amount;
            }
        }, 0);
        return totalPaidAmount || 0;
    }

    const columns = [
        { field: 'month', headerName: 'Month', editable: false, inputType: 'text' },
        { field: 'year', headerName: 'Year', editable: false, inputType: 'text' },
        { field: 'emi_amount', headerName: 'EMI Amount', editable: false, inputType: 'text' },
        { field: 'closing_balance', headerName: 'Closing Balance', editable: false, inputType: 'text' },
        { field: 'paid_date', headerName: 'Paid Date', editable: true, inputType: 'date', minDate: agentLoan?.issue_date },
    ]

    return (
        <Container maxWidth="xxl">
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {/* Module Name and Buttons */}
                <Grid
                    container
                    sx={{
                        backgroundColor: 'white',
                        borderBottom: '2px solid #E0E0E0',
                        padding: '10px 0',
                        display: "flex"
                    }}
                >
                    {/* Header Row */}
                    <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{
                                width: '20px',
                                marginLeft: '20px',
                                backgroundColor: 'green'
                            }}
                        />
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ModuleName moduleName="Agent Loan EMI" pageName="View" />
                        </Box>
                    </Grid>

                    <Grid item xs={4} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                            <IconButton
                                onClick={handleCancel}
                                sx={{ color: 'red', mx: 0.5 }}
                            >
                                <Cancel />
                            </IconButton>
                        </Box>
                        <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
                            <Button
                                onClick={handleCancel}
                                variant="outlined"
                                size="small"
                                sx={{
                                    maxWidth: '100px',
                                    width: '100%',
                                    mx: 1.5,
                                    color: 'red',
                                    borderColor: 'red',
                                    textTransform: 'none'
                                }}
                            >
                                Cancel
                            </Button>
                        </Box>
                    </Grid>
                </Grid>
                {/* Agent Info Section */}
                <Box display="flex" alignItems="center" p={2} sx={{ padding: '1rem 1rem', borderBlock: '1px solid black' }}>
                    <Box mr={2}>
                        {renderAvatar(agentInformation?.photo, agentInformation?.full_name)}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                        <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Full Name:</strong> {agentInformation?.full_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>User ID:</strong> {agentInformation?.agent_id || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Mobile:</strong> {agentInformation?.official_mobile || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Office Email:</strong> {agentInformation?.official_email || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Department:</strong> {agentInformation?.department_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Role:</strong> {agentInformation?.role_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Mobile:</strong> {maskMobileNumber(agentInformation?.personal_mobile) || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Personal Email:</strong> {maskEmail(agentInformation?.personal_email) || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Joining Date:</strong> {dayjs(agentInformation?.date_of_joining).format('DD-MM-YYYY') || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Branch Name:</strong> {agentInformation?.branch_name || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>City:</strong> {agentInformation?.branch_city || 'N/A'}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6} md={4} lg={3}>
                                <Typography noWrap><strong>Date of Birth:</strong> {maskDOB(agentInformation?.dob) || 'N/A'}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </Box>

                {/* Loan Details Section */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem 3rem', borderRadius: '4px', mb: 2, display: 'flex', justifyContent: 'space-between' }}>
                        <h2>Loan Details</h2>
                    </Box>
                </Grid>
                <Grid container spacing={2} sx={{ alignItems: 'center', padding: '0 3rem' }}>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Loan Id:</strong> {agentLoan?.loan_id || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Loan Amount:</strong> {agentLoan?.loan_amount || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Outstanding:</strong> {calculateOutstandingAmount() || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Paid Amount:</strong> {agentLoan?.paid_amount || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>EMI:</strong> {agentLoan?.emi || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Issue Date:</strong> {dayjs(agentLoan?.issue_date).format('DD-MM-YYYY') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Start Date:</strong> {dayjs(agentLoan?.start_date).format('DD-MM-YYYY') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>End Date:</strong> {dayjs(agentLoan?.end_date).format('DD-MM-YYYY') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <Typography noWrap><strong>Tenure:</strong> {agentLoan?.tenure || 'N/A'}</Typography>
                    </Grid>
                </Grid>

                {/* EMI Details Table */}
                <Grid item xs={12}>
                    <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem 3rem', borderRadius: '4px', display: 'flex', justifyContent: 'space-between' }}>
                        <h2>EMI Details</h2>
                    </Box>
                </Grid>
                <CustomEditableTable
                    isCheckboxRequired={false}
                    data={agentLoanEmis}
                    columns={columns}
                    onCellEdit={permissions.can_edit && !isViewMode ? handleUpdate : null}
                    updateButtonText={['Paid', 'Unpaid']}
                    isDisabled={isViewMode || !permissions.can_edit}
                />
            </Box>
        </Container>
    );
}

export default AgentEmiDetails;
