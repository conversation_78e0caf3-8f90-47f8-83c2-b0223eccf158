import { createSlice } from '@reduxjs/toolkit';
import { createRole, deleteRole, getAllRoles, updateRole, reinstateRole, getRoleById, getFilterRoles, getRolesByName } from '../../actions/action';
import { toast } from 'react-toastify';

const roleSlice = createSlice({
  name: 'roles',
  initialState: {
    roles: [],
    role: null,
    status: 'idle',
    error: null,
  },
  extraReducers: (builder) => {
    builder
      // Fetch All Roles
      .addCase(getAllRoles.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getAllRoles.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.roles = action.payload;
      })
      .addCase(getAllRoles.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to load roles');
      })

      // Get Role By Id
      .addCase(getRoleById.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getRoleById.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.role = action.payload;
      })
      .addCase(getRoleById.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to load role');
      })

      // Get roles by name
      .addCase(getRolesByName.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getRolesByName.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.roles = action.payload;
      })
      .addCase(getRolesByName.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to fetch roles by name');
      })

      // Create Role
      .addCase(createRole.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createRole.fulfilled, (state, action) => {
        state.roles.push(action.payload);
        state.status = 'succeeded';
        toast.success('Role created successfully');
      })
      .addCase(createRole.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to create role');
      })

      // Update Role
      .addCase(updateRole.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(updateRole.fulfilled, (state, action) => {
        const index = state.roles.findIndex((role) => role.id === action.payload.id);
        if (index !== -1) {
          state.roles[index] = action.payload;
        }
        state.status = 'succeeded';
        toast.success('Role updated successfully');
      })
      .addCase(updateRole.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to update role');
      })

      // Delete Role
      .addCase(deleteRole.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(deleteRole.fulfilled, (state, action) => {
        state.roles = state.roles.filter((role) => role.id !== action.payload);
        state.status = 'succeeded';
        toast.success('Role deleted successfully');
      })
      .addCase(deleteRole.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to delete role');
      })

      // Reinstate Role
      .addCase(reinstateRole.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(reinstateRole.fulfilled, (state, action) => {
        const index = state.roles.findIndex((role) => role.id === action.payload.id);
        if (index !== -1) {
          state.roles[index] = action.payload;
        }
        state.status = 'succeeded';
        toast.success('Role reinstated successfully');
      })
      .addCase(reinstateRole.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to reinstate role');
      })

      // Get Filter Roles
      .addCase(getFilterRoles.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getFilterRoles.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.roles = action.payload;
      })
      .addCase(getFilterRoles.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to load filtered roles');
      });
  },
});

export default roleSlice.reducer;
