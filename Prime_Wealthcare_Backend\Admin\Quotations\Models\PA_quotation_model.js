const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const pa_soapService = require('../SoapService/PA_SoapService');

const createQuotationWithMembers = async (req, res) => {
    const { quotationData, membersData } = req.body;

    const trx = await db.transaction();
    try {
        // console.log('membersData', membersData);

        if (!Array.isArray(membersData)) {
            throw new Error('Members data must be an array');
        }
        const QuotationDataToSave = {
            quotation_number: quotationData.quotation_number,
            product: quotationData.product,
            insurance_company: quotationData.insurance_company,
            main_product: quotationData.main_product,
            sub_product_id: membersData[0]?.cover_type_id, // Access first member's cover_type_id
            customer_id: quotationData.customer_id,
            agent_id: quotationData.agent_id,
            status: quotationData.status,
            Created_by: quotationData.Created_by,
            duration: quotationData.duration,
        }
        const createdQuotation = await trx('pa_quotations').insert(QuotationDataToSave);
        const quotationId = createdQuotation[0]; // Get the last inserted ID
        const getPickListAPI = async (id) => {
            if (!id) return null;
            const pickListItem = await db('pick_list')
                .where('label_name', id)
                .first();
            return pickListItem ? pickListItem.api_name : null;
        };
        const membersWithQuotationId = Array.isArray(membersData)
            ? await Promise.all(membersData.map(async member => {
                // Calculate additional sum insured based on Y/N values
                const baseSum = Number(member.ad_sum_insured) || 0;
                const monthlyIncome = Number(member.annual_income) / 12;
                const ptdSum = Number(member.pt_sum_insured) || 0;

                // Initialize additional covers
                let additionalCovers = {
                    AA_suminsured: 0,
                    FT_suminsured: 0,
                    HC_suminsured: 0,
                    LP_suminsured: 0,
                    LS_suminsured: 0,
                    ME_suminsured: 0,
                    AM_suminsured: 0,
                    RF_suminsured: 0,
                    BB_suminsured: 0,
                    CS_suminsured: 0,

                };

                // Check if the member is SELF to calculate their own covers
                if (member.relation === 'SELF') {
                    additionalCovers = {
                        AA_suminsured: member.adaptationBenefits === 'Y' ? Math.min(ptdSum * 0.10, 50000) : 0,
                        FT_suminsured: member.familyTransportAllowance === 'Y' ? Math.min(baseSum * 0.10, 50000) : 0,
                        HC_suminsured: member.hospitalCashAllowance === 'Y' ? 2000 : 0,
                        LP_suminsured: member.loanProtector === 'Y' ? Math.min(baseSum * 0.02, 20000) : 0,
                        LS_suminsured: member.lifeSupportBenefit === 'Y' ? Math.min(ptdSum * 0.01, 10000) : 0,
                        ME_suminsured: member.accidentHospitalization === 'Y' ? Math.min(baseSum * 0.25, 10000) : 0,
                        AM_suminsured: member.accidentMedicalExpenses === 'Y' ? baseSum * 0.20 : 0,
                        RF_suminsured: member.repatriationAndFuneralExpenses === 'Y' ? Math.min(baseSum * 0.01, 12500) : 0,
                        BB_suminsured: member.brokenBones === 'Y' ? Math.min(monthlyIncome * 24, 1500000) : 0,
                        CS_suminsured: member.childEducationSupport === 'Y' ? Math.min(ptdSum * 0.1, 10000) : 0, // Updated to use childEducationSupport
                    };
                } else if (member.relation === 'DAUGHTER' || member.relation === 'SON') {
                    // For daughter, calculate 25% of self member's covers if applicable
                    const selfMember = membersData.find(m => m.relation === 'SELF');
                    // console.log(selfMember, "selfMember");
                    if (selfMember) {
                        additionalCovers = {
                            AA_suminsured: selfMember.adaptationBenefits === 'Y' ? Math.min(ptdSum * 0.10, 50000) * 0.25 : 0,
                            FT_suminsured: selfMember.familyTransportAllowance === 'Y' ? Math.min(baseSum * 0.10, 50000) * 0.25 : 0,
                            HC_suminsured: selfMember.hospitalCashAllowance === 'Y' ? 2000 * 0.25 : 0,
                            LP_suminsured: selfMember.loanProtector === 'Y' ? Math.min(baseSum * 0.02, 20000) * 0.25 : 0,
                            LS_suminsured: selfMember.lifeSupportBenefit === 'Y' ? Math.min(ptdSum * 0.01, 10000) * 0.25 : 0,
                            ME_suminsured: selfMember.accidentHospitalization === 'Y' ? Math.min(baseSum * 0.25, 10000) * 0.25 : 0,
                            AM_suminsured: selfMember.accidentMedicalExpenses === 'Y' ? baseSum * 0.20 * 0.25 : 0,
                            RF_suminsured: selfMember.repatriationAndFuneralExpenses === 'Y' ? Math.min(baseSum * 0.01, 12500) * 0.25 : 0,
                            BB_suminsured: selfMember.brokenBones === 'Y' ? Math.min(selfMember.annual_income * 24, 1500000) * 0.25 : 0,
                            CS_suminsured: selfMember.childEducationSupport === 'Y' ? Math.min(ptdSum * 0.1, 10000) * 0.25 : 0, // Updated calculation
                        };
                    }
                }
                else if (member.relation === 'SPOUSE') {
                    const selfMember = membersData.find(m => m.relation === 'SELF');
                    if (selfMember) {
                        if (member.occupation === 'HouseWife' || member.occupation === 'Unemployed') {
                            // Calculate 50% of self member's covers
                            additionalCovers = {
                                AA_suminsured: selfMember.adaptationBenefits === 'Y' ? Math.min(ptdSum * 0.10, 50000) * 0.50 : 0,
                                FT_suminsured: selfMember.familyTransportAllowance === 'Y' ? Math.min(baseSum * 0.10, 50000) * 0.50 : 0,
                                HC_suminsured: selfMember.hospitalCashAllowance === 'Y' ? 2000 * 0.50 : 0,
                                LP_suminsured: selfMember.loanProtector === 'Y' ? Math.min(baseSum * 0.02, 20000) * 0.50 : 0,
                                LS_suminsured: selfMember.lifeSupportBenefit === 'Y' ? Math.min(ptdSum * 0.01, 10000) * 0.50 : 0,
                                ME_suminsured: selfMember.accidentHospitalization === 'Y' ? Math.min(baseSum * 0.25, 10000) * 0.50 : 0,
                                AM_suminsured: selfMember.accidentMedicalExpenses === 'Y' ? baseSum * 0.20 * 0.25 : 0,
                                RF_suminsured: selfMember.repatriationAndFuneralExpenses === 'Y' ? Math.min(baseSum * 0.01, 12500) * 0.50 : 0,
                                BB_suminsured: selfMember.brokenBones === 'Y' ? Math.min(selfMember.annual_income * 24, 1500000) * 0.50 : 0,
                                CS_suminsured: selfMember.childEducationSupport === 'Y' ? Math.min(ptdSum * 0.1, 10000) * 0.50 : 0, // Updated calculation
                            };
                        } else {
                            // Calculate full covers for other occupations
                            additionalCovers = {
                                AA_suminsured: selfMember.adaptationBenefits === 'Y' ? Math.min(ptdSum * 0.10, 50000) : 0,
                                FT_suminsured: selfMember.familyTransportAllowance === 'Y' ? Math.min(baseSum * 0.10, 50000) : 0,
                                HC_suminsured: selfMember.hospitalCashAllowance === 'Y' ? 2000 : 0,
                                LP_suminsured: selfMember.loanProtector === 'Y' ? Math.min(baseSum * 0.02, 20000) : 0,
                                LS_suminsured: selfMember.lifeSupportBenefit === 'Y' ? Math.min(ptdSum * 0.01, 10000) : 0,
                                ME_suminsured: selfMember.accidentHospitalization === 'Y' ? Math.min(baseSum * 0.25, 10000) : 0,
                                AM_suminsured: selfMember.accidentMedicalExpenses === 'Y' ? baseSum * 0.20 : 0,
                                RF_suminsured: selfMember.repatriationAndFuneralExpenses === 'Y' ? Math.min(baseSum * 0.01, 12500) : 0,
                                BB_suminsured: selfMember.brokenBones === 'Y' ? Math.min(monthlyIncome * 24, 1500000) : 0,
                                CS_suminsured: selfMember.childEducationSupport === 'Y' ? Math.min(ptdSum * 0.1, 10000) : 0,
                            };
                        }
                    }
                }
                return {
                    ...member,
                    relation: await getPickListAPI(member.relation) || null,
                    occupation: await getPickListAPI(member.occupation) || null,
                    quotation_id: quotationId,
                    ...additionalCovers
                };
            }))
            : [];
        const filteredMembers = membersWithQuotationId.map(member => ({
            //member_id_no: member.member_id_no,
            member_id: member.member_id,
            pa_quotation_id: quotationId,
            insuredName: member.insuredName,
            relation: member.relation,
            annual_income: member.annual_income,
            AD_suminsured: member.ad_sum_insured,
            PP_suminsured: member.pp_sum_insured,
            PT_suminsured: member.pt_sum_insured,
            TT_suminsured: member.tt_sum_insured,
            RF_suminsured: member.RF_suminsured,
            AA_suminsured: member.AA_suminsured,
            CS_suminsured: member.CS_suminsured,
            FT_suminsured: member.FT_suminsured,
            HC_suminsured: member.HC_suminsured,
            LS_suminsured: member.LS_suminsured,
            ME_suminsured: member.ME_suminsured,
            BB_suminsured: member.BB_suminsured,
            AM_suminsured: member.AM_suminsured,
            LP_suminsured: member.LP_suminsured,

            status: member.status,
            Created_by: member.Created_by,
        }));
        // Insert all members
        await trx('pa_quotation_member').insert(filteredMembers);

        const customerId = quotationData.customer_id;

        // Fetch the pincode from Customer_address table
        const pincode = await getCostomerAddressByCustomerId(customerId);
        // Fetch the created quotation details
        const quotationDataWithPincode = {
            ...quotationData,
            pincode: pincode
        };

        // Add start and end dates based on duration
        const startDate = new Date(); // Current date
        const endDate = new Date();
        const duration = parseInt(quotationData.duration) || 12; // Default to 12 if not specified
        endDate.setMonth(endDate.getMonth() + duration); // Add months based on duration

        // Format dates as DD/MM/YYYY
        const formatDate = (date) => {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };

        // Add formatted dates to quotation data
        const quotationDataWithDates = {
            ...quotationDataWithPincode,
            start_date: formatDate(startDate),
            end_date: formatDate(endDate)
        };



        const soapResponse = await pa_soapService.sendPASoapRequest(
            membersWithQuotationId,  // memberData
            quotationDataWithDates,   // quotationData
            quotationId,              // quotationId
            trx
        );
        console.log('SOAP Service Response:', soapResponse);
        await trx.commit();

        res.status(201).json({
            message: 'Quotation created successfully with all related data',
            quotation_id: quotationId
        });
    }
    catch (error) {
        console.error(error);
        await trx.rollback();
        res.status(500).send({ message: 'Error creating quotation' });
        return;
    }

}

const getCostomerAddressByCustomerId = async (customerId) => {
    try {
        // Query the database for the customer address
        const address = await db('customer_address')
            .select('current_pincode')
            .where('customer_id', customerId)
            .first();

        if (!address) {
            throw new Error(`No address found for customer_id: ${customerId}`);
        }

        return address.current_pincode;
    } catch (error) {
        console.error('Error fetching customer address:', error);
        throw error;
    }
};

const getQuotationWithResponseAndMembersById = async (req, res) => {
    try {
        const { id } = req.params;

        // First, get the quotation data
        const quotation = await db('pa_quotations as q')
            .where('q.pa_quotation_id', id)
            .select(
                'q.pa_quotation_id',
                'q.duration',
                'q.quotation_number',
                'q.product',
                'q.insurance_company',
                'q.main_product',
                'q.sub_product_id',
                'q.customer_id',
                'q.agent_id',
                'q.status as quotation_status',
                db.raw("DATE_FORMAT(q.Created_at, '%d/%m/%Y') as quotation_created_at"),
                db.raw("DATE_FORMAT(q.Updated_at, '%d/%m/%Y') as quotation_updated_at")
            )
            .first();

        if (!quotation) {
            return res.status(404).json({
                success: false,
                message: 'Quotation not found'
            });
        }

        // Get all responses
        const responses = await db('pa_responses as sr')
            .where('sr.pa_quotation_id', id)
            .select(
                'sr.duration',
                'sr.id as response_id',
                'sr.full_payment_premium',
                'sr.full_payment_tax',
                'sr.total_full_payment',
                'sr.family_discount_perc',
                'sr.family_discount_amt',
                'sr.long_term_discount_percent',
                'sr.long_term_discount_amount',
                'sr.status as response_status',
                db.raw("DATE_FORMAT(sr.created_at, '%d/%m/%Y') as response_created_at"),
                db.raw("DATE_FORMAT(sr.updated_at, '%d/%m/%Y') as response_updated_at")
            )
            .orderBy('sr.id');

        // Get all members
        const members = await db('pa_quotation_member as qm')
            .where('qm.pa_quotation_id', id)
            .select(
                'qm.id as quotation_member_id',
                'qm.member_id',
                'qm.insuredName',
                'qm.relation',
                'qm.annual_income',
                'qm.AD_suminsured',
                'qm.PP_suminsured',
                'qm.PT_suminsured',
                'qm.TT_suminsured',
                'qm.AA_suminsured',
                'qm.CS_suminsured',
                'qm.FT_suminsured',
                'qm.HC_suminsured',
                'qm.RF_suminsured',
                'qm.LP_suminsured',
                'qm.LS_suminsured',
                'qm.ME_suminsured',
                'qm.AM_suminsured',
                'qm.BB_suminsured',
                'qm.status as member_status'
            );

        // Combine all data
        const result = {
            ...quotation,
            responses: responses,
            members: members
        };

        return res.status(200).json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('Error fetching quotation with responses and members by ID:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to fetch quotation with responses and members by ID'
        });
    }
};

module.exports = {
    createQuotationWithMembers,
    getCostomerAddressByCustomerId,
    getQuotationWithResponseAndMembersById,
}