const knexConfig = require('../../../../knexfile');
const knex = require('knex')(knexConfig.development);

const createInsuranceCompany = async (data) => {
  //

  const response = await knex('insurance_company').insert(data);
  // .then(([id]) => {
  //   return knex('insurance_company').where('id', id).first();
  // });
  // 
  return response;
};

const getAllInsuranceCompanies = async () => {
  const data = await knex('insurance_company')
    .select(
      'insurance_company.*',
      knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
      'locations.state',
      'areas.area',
    )
    .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
    .leftJoin('areas', 'insurance_company.area', 'areas.id')
    .groupBy('insurance_company.id', 'locations.state', 'areas.area');

  return data;
};

const getInsuranceCompanyById = async (id) => {
  return knex('insurance_company')
    .select(
      'insurance_company.*',
      knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
      'locations.state',
      'areas.id'
    )
    .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
    .leftJoin('areas', 'insurance_company.area', 'areas.id')
    .where('insurance_company.id', id)
    .groupBy('insurance_company.id', 'locations.state', 'areas.area')
    .first();
}

const updateInsuranceCompanyById = (id, data) => {
  return knex('insurance_company').where('id', id).update(data);
}

const deleteInsuranceCompanyById = (id) => {
  return knex('insurance_company').where('id', id).update({ status: 0 });
};

const reinstateInsuranceCompany = (id) => {
  return knex('insurance_company').where('id', id).update({ status: 1 });
};

// Add this method to find new insurance companies created last week
const newInsuranceCompaniesLastWeek = async () => {
  try {
    const companies = await knex('insurance_company')
      .select(
        'insurance_company.*',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area'
      )
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .whereBetween('insurance_company.created_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ])
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error('Error fetching new insurance companies created last week:', error);
    throw error;
  }
};

const newInsuranceCompaniesThisWeek = async () => {
  try {
    const companies = await knex('insurance_company')
      .select(
        'insurance_company.*',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area'
      )
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .whereBetween('insurance_company.created_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
        knex.raw('NOW()')
      ])
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error('Error fetching new insurance companies created this week:', error);
    throw error;
  }
};


// Add this method to find deactivated insurance companies updated this week
const deactivatedInsuranceCompaniesThisWeek = async () => {
  try {
    const companies = await knex('insurance_company')
      .select(
        'insurance_company.*',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area'
      )
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .where('insurance_company.status', 0)
      .whereBetween('insurance_company.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
        knex.raw('NOW()')
      ])
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error('Error fetching deactivated insurance companies updated this week:', error);
    throw error;
  }
};

// Add this method to find deactivated insurance companies updated last week
const deactivatedInsuranceCompaniesLastWeek = async () => {
  try {
    const companies = await knex('insurance_company')
      .select(
        'insurance_company.*',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area'
      )
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .where('insurance_company.status', 0)
      .whereBetween('insurance_company.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ])
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error('Error fetching deactivated insurance companies updated last week:', error);
    throw error;
  }
};

// Add this method to find edited insurance companies updated this week
const editedInsuranceCompaniesThisWeek = async () => {
  try {
    const companies = await knex('insurance_company')
      .select(
        'insurance_company.*',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area'
      )
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .whereBetween('insurance_company.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
        knex.raw('NOW()')
      ])
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error('Error fetching edited insurance companies updated this week:', error);
    throw error;
  }
};

// Add this method to find edited insurance companies updated last week
const editedInsuranceCompaniesLastWeek = async () => {
  try {
    const companies = await knex('insurance_company')
      .select(
        'insurance_company.*',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area'
      )
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .whereBetween('insurance_company.updated_at', [
        knex.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
        knex.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
      ])
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error('Error fetching edited insurance companies updated last week:', error);
    throw error;
  }
}

// Find insurance companies by name, state, and city
const getInsuranceCompaniesByName = async (name) => {
  try {
    const companies = await knex('insurance_company')
      .leftJoin('locations', 'insurance_company.pincode', 'locations.pincode')
      .leftJoin('areas', 'insurance_company.area', 'areas.id')
      .select(
        'insurance_company.id',
        'insurance_company.insurance_company_name',
        'insurance_company.ado_code',
        'insurance_company.help_line_no',
        knex.raw('GROUP_CONCAT(DISTINCT locations.city) as cities'),
        'locations.state',
        'areas.area',
        'insurance_company.*'
      )
      .where(function () {
        this.where('insurance_company.insurance_company_name', 'LIKE', `%${name}%`)
          .orWhere('insurance_company.ado_code', 'LIKE', `%${name}%`)
          .orWhere('insurance_company.help_line_no', 'LIKE', `%${name}%`)
          .orWhere('locations.city', 'LIKE', `%${name}%`)
          .orWhere('locations.state', 'LIKE', `%${name}%`);
      })
      .groupBy('insurance_company.id', 'locations.state', 'areas.area');

    return companies;
  } catch (error) {
    console.error(`Error fetching insurance companies by name: ${name}`, error);
    throw error;
  }
};



module.exports = {
  createInsuranceCompany,
  getAllInsuranceCompanies,
  getInsuranceCompanyById,
  updateInsuranceCompanyById,
  deleteInsuranceCompanyById,
  reinstateInsuranceCompany,
  getInsuranceCompaniesByName, // New method
  newInsuranceCompaniesLastWeek, // New method
  newInsuranceCompaniesThisWeek, // New method
  deactivatedInsuranceCompaniesThisWeek, // New method
  deactivatedInsuranceCompaniesLastWeek, // New method
  editedInsuranceCompaniesThisWeek, // New method
  editedInsuranceCompaniesLastWeek, // New method
};
