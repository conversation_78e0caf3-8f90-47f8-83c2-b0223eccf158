const dashboard = require('../Models/dashboardModel');
const currentDate = new Date();

exports.getTotalPremiumInTimePeriod = async (req, res) => {
    try {
        // Validate current date
        if (!(currentDate instanceof Date) || isNaN(currentDate)) {
            throw new Error('Invalid date');
        }

        const timePeriod = [
            {
                title: 'thisMonth',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), 1)),
                endDate: new Date(Date.UTC(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59)) // Last day of the current month
            },
            {
                title: 'pastMonth',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)),
                endDate: new Date(Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), 0, 23, 59, 59)) // Last day of the previous month
            },
            {
                title: 'thisFinancialYear',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), 3, 1)), // April 1 of the current financial year
                endDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
                    : new Date(Date.UTC(currentDate.getFullYear() + 1, 2, 31, 23, 59, 59)) // March 31 of the next financial year
            },
            {
                title: 'pastFinancialYear',
                startDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear() - 1, 3, 1)) // April 1 of the previous financial year
                    : new Date(Date.UTC(currentDate.getFullYear(), 3, 1)), // April 1 of the current financial year
                endDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
                    : new Date(Date.UTC(currentDate.getFullYear() - 1, 2, 31, 23, 59, 59)) // March 31 of the previous financial year
            },
            {
                title: 'thisQuarter',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3, 1)), // Start of the current quarter
                endDate: new Date(Date.UTC(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3 + 3, 0, 23, 59, 59)) // End of the current quarter
            },
            {
                title: 'pastQuarter',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), Math.floor((currentDate.getMonth() - 3) / 3) * 3, 1)), // Start of the previous quarter
                endDate: new Date(Date.UTC(currentDate.getFullYear(), Math.floor((currentDate.getMonth() - 3) / 3) * 3 + 3, 0, 23, 59, 59)) // End of the previous quarter
            },
            {
                title: 'cancel',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), 3, 1)), // April 1 of the current financial year
                endDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
                    : new Date(Date.UTC(currentDate.getFullYear() + 1, 2, 31, 23, 59, 59)) // March 31 of the next financial year
            },
            {
                title: 'pastCancel',
                startDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear() - 1, 3, 1)) // April 1 of the previous financial year
                    : new Date(Date.UTC(currentDate.getFullYear(), 3, 1)), // April 1 of the current financial year
                endDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
                    : new Date(Date.UTC(currentDate.getFullYear() - 1, 2, 31, 23, 59, 59)) // March 31 of the previous financial year
            },
        ];

        let data = {};
        for (let i = 0; i < timePeriod.length; i++) {
            const temp = timePeriod[i];
            const fetchedPremium = await dashboard.getTotalPremiumInTimePeriod(temp.title, temp.startDate, temp.endDate);
            // Using computed property name instead of literal 'tempTitle'
            data[temp.title] = fetchedPremium;
        }
        res.status(200).json({
            ...data
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

// New controller method for proposals by insurance company
exports.getProposalsByInsuranceCompany = async (req, res) => {
    try {
        const { fiscalYear } = req.query;
        let startDate, endDate;

        if (fiscalYear) {
            const [startYear] = fiscalYear.split('-');
            startDate = `${startYear}-04-01`;
            endDate = `${parseInt(startYear) + 1}-03-31`;
        } else {
            const today = new Date();
            const currentYear = today.getFullYear();
            const isBeforeApril = today.getMonth() < 3;
            const fiscalStartYear = isBeforeApril ? currentYear - 1 : currentYear;
            startDate = `${fiscalStartYear}-04-01`;
            endDate = `${fiscalStartYear + 1}-03-31`;
        }

        const allProposals = await dashboard.getProposalsByInsuranceCompany(startDate, endDate);

        // Group by insurance company
        const groupedData = allProposals.reduce((acc, item) => {
            const company = item.insurance_company;
            if (!acc[company]) {
                acc[company] = {
                    total_count: 0,
                    total_premium: 0,
                    types: {
                        NEW: { count: 0, premium: 0 },
                        RENEWAL: { count: 0, premium: 0 },
                        ROLLOVER: { count: 0, premium: 0 },
                        MIGRATION: { count: 0, premium: 0 }
                    }
                };
            }

            const type = item.proposal_type === 'RENEW' ? 'RENEWAL' :
                item.proposal_type === 'Roll Over' ? 'ROLLOVER' :
                    item.proposal_type === 'Migration' ? 'MIGRATION' : 'NEW';

            acc[company].total_count += item.count;
            acc[company].total_premium += item.premium;
            acc[company].types[type].count += item.count;
            acc[company].types[type].premium += item.premium;

            return acc;
        }, {});

        // Get top 10 companies by premium
        const top10Companies = Object.entries(groupedData)
            .sort(([, a], [, b]) => b.total_premium - a.total_premium)
            .slice(0, 10)
            .map(([company]) => company);

        const formattedData = {
            companies: top10Companies,
            proposalTypes: ['NEW', 'RENEWAL', 'ROLLOVER', 'MIGRATION'],
            data: {
                NEW: [],
                RENEWAL: [],
                ROLLOVER: [],
                MIGRATION: []
            },
            premium: {
                NEW: [],
                RENEWAL: [],
                ROLLOVER: [],
                MIGRATION: [],
                total: []
            },
            fiscalYear: `${startDate.split('-')[0]}-${endDate.split('-')[0]}`,
            totalProposals: [],
            totalPremium: []
        };

        // Fill in the data
        top10Companies.forEach(company => {
            const companyData = groupedData[company];
            
            formattedData.totalProposals.push(companyData.total_count);
            formattedData.totalPremium.push(Math.round(companyData.total_premium));

            Object.keys(formattedData.data).forEach(type => {
                formattedData.data[type].push(companyData.types[type].count);
                formattedData.premium[type].push(Math.round(companyData.types[type].premium));
            });

            formattedData.premium.total.push(Math.round(companyData.total_premium));
        });

        res.status(200).json({
            success: true,
            data: formattedData
        });

    } catch (error) {
        console.error('Error in getProposalsByInsuranceCompany:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching proposal data',
            error: error.message
        });
    }
};

exports.getCurrentFinancialYearProposals = async (req, res) => {
    try {
        const startDate = new Date(Date.UTC(currentDate.getFullYear(), 3, 1));// April 1 of the current financial year
        const endDate = currentDate.getMonth() < 3
            ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
            : new Date(Date.UTC(currentDate.getFullYear() + 1, 2, 31, 23, 59, 59)) // March 31 of the next financial year
        const data = await dashboard.getCurrentFinancialYearProposals(startDate, endDate);
        res.status(200).json(data)
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        })
    }
}

exports.getProposalsByAgent = async (req, res) => {
    try {

        // Get fiscal year parameters from request query or use current fiscal year
        const { fiscalYear } = req.query;

        // Calculate fiscal year start and end dates
        let startDate, endDate;

        if (fiscalYear) {
            const [startYear] = fiscalYear.split('-');
            startDate = `${startYear}-04-01`;
            endDate = `${parseInt(startYear) + 1}-03-31`;
        } else {
            const today = new Date();
            const currentYear = today.getFullYear();
            const isBeforeApril = today.getMonth() < 3;
            const fiscalStartYear = isBeforeApril ? currentYear - 1 : currentYear;
            startDate = `${fiscalStartYear}-04-01`;
            endDate = `${fiscalStartYear + 1}-03-31`;
        }

        // Get proposal data from model
        const response = await dashboard.getProposalsByAgent(startDate, endDate);

        // Format data for chart consumption using the model's response
        const formattedData = {
            agents: response.agents || [],
            proposalTypes: ['NEW', 'RENEWAL', 'ROLLOVER', 'MIGRATION'],
            data: response.data || {
                'NEW': [],
                'RENEWAL': [],
                'ROLLOVER': [],
                'MIGRATION': []
            },
            premium: response.premiums || {
                'NEW': [],
                'RENEWAL': [],
                'ROLLOVER': [],
                'MIGRATION': [],
                'total': []
            },
            fiscalYear: `${startDate.split('-')[0]}-${endDate.split('-')[0]}`,
            totalProposals: response.totalCounts || [],
            totalPremium: response.totalPremiums || []
        };

        res.status(200).json({
            success: true,
            data: formattedData
        });

    } catch (error) {
        console.error('Error in getProposalsByAgent:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching proposal data by agent',
            error: error.message
        });
    }
};

exports.getTop10Branches = async (req, res) => {
    try {
        const startDate = new Date(Date.UTC(currentDate.getFullYear(), 3, 1));// April 1 of the current financial year
        const endDate = currentDate.getMonth() < 3
            ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
            : new Date(Date.UTC(currentDate.getFullYear() + 1, 2, 31, 23, 59, 59)) // March 31 of the next financial year
        const data = await dashboard.getTop10Branches(startDate, endDate);
        res.status(200).json(data)
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        })
    }
}

exports.getTop20Agents = async (req, res) => {
    try {
        const timePeriod = [
            {
                title: 'thisMonth',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), 1)),
                endDate: new Date(Date.UTC(currentDate.getFullYear(), currentDate.getMonth() + 1, 0, 23, 59, 59)) // Last day of the current month
            },
            {
                title: 'thisFinancialYear',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), 3, 1)), // April 1 of the current financial year
                endDate: currentDate.getMonth() < 3
                    ? new Date(Date.UTC(currentDate.getFullYear(), 2, 31, 23, 59, 59)) // March 31 of the current financial year
                    : new Date(Date.UTC(currentDate.getFullYear() + 1, 2, 31, 23, 59, 59)) // March 31 of the next financial year
            },
            {
                title: 'thisQuarter',
                startDate: new Date(Date.UTC(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3, 1)), // Start of the current quarter
                endDate: new Date(Date.UTC(currentDate.getFullYear(), Math.floor(currentDate.getMonth() / 3) * 3 + 3, 0, 23, 59, 59)) // End of the current quarter
            }
        ];
        let data = {};
        for (let i = 0; i < timePeriod.length; i++) {
            const temp = timePeriod[i];
            const agents = await dashboard.getTop20Agents(temp.startDate, temp.endDate);
            data[temp.title] = agents;
        }
        

        res.status(200).json({
            ...data
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        })
    }
}