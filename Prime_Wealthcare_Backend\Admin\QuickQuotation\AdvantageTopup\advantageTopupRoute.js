const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { formatDateToDDMMYYYY, calculateDOBFromAgeBand } = require('../../../Reusable/reusable');
const { sendAdvTopupSOAPRequest } = require('./advTopupSoapService');

// Define sum insured options based on cover types
const COVER_TYPE_SUM_INSURED = {
    '50000': [500000],
    '100000': [500000],
    '200000': [500000, 750000, 1000000, 1500000, 2000000, 2500000],
    '300000': [500000, 750000, 1000000, 1500000, 2000000, 2500000],
    '400000': [500000, 750000, 1000000, 1500000, 2000000, 2500000],
    '500000': [500000, 750000, 1000000, 1500000, 2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
    '750000': [750000, 1000000, 1500000, 2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
    '1000000': [1000000, 1500000, 2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
    '1500000': [1500000, 2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
    '2000000': [2000000, 2500000, 3000000, 4000000, 5000000, 10000000],
    '2500000': [2500000, 3000000, 4000000, 5000000],
    '3000000': [3000000, 4000000, 5000000, 10000000],
    '4000000': [4000000, 5000000, 10000000]
};

// Add this helper function to calculate policy dates
const calculatePolicyDates = (duration) => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // Start from tomorrow

    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + parseInt(duration)); // Add duration years
    endDate.setDate(endDate.getDate() - 1); // Subtract one day

    return {
        startDate: formatDateToDDMMYYYY(startDate),
        endDate: formatDateToDDMMYYYY(endDate)
    };
}

// Add this new mapping constant at the top of the file with other constants
const COVER_TYPE_MAPPING = {
    'SUPREME': 'Supreme',
    'ELITE': 'Elite'
};

router.post('/advtopup-floater-options', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;

        // Validate input
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Temporary storage for results
        const tempResults = [];

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;

        // Iterate through sum insured options
        const sumInsuredOptions = COVER_TYPE_SUM_INSURED[members[0].deductible];

        for (const sumInsured of sumInsuredOptions) {
            // Generate numeric UID
            const numericUid = uuidv4();

            // Calculate policy dates based on duration
            const { startDate: policyStartDate, endDate: policyEndDate } = calculatePolicyDates(duration || '1');

            const policyType = family_type === 'individual' ? 'HTI' : 'HTF'; // HTI for individual, HTF for floater

            // Prepare SOAP data with current sum insured
            const soapData = {
                Product: normalizedProductName,
                PolicyHeader: {
                    PolicyStartDate: policyStartDate,
                    PolicyEndDate: policyEndDate,
                    AgentCode: "60000272",
                    BranchCode: "10",
                    MajorClass: "FAT",
                    ContractType: "FAT",
                    METHOD: "ENQ",
                    PolicyIssueType: "I"
                },
                Uid: numericUid,
                VendorCode: "webagg",
                VendorUserId: "webagg",
                Client: {
                    ClientType: "I",
                    CreationType: "C",
                    Address1: {
                        Pincode: pincode,
                        Country: "IND"
                    }
                },
                BeneficiaryDetails: {
                    Member: members.map((member, index) => {
                        const insuredDob = formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand)));
                        return {
                            MemberId: (index + 1).toString(),
                            InsuredName: `Member ${index + 1}`,
                            InsuredDob: insuredDob,
                            InsuredGender: member.gender || 'M',
                            InsuredOccpn: "SVCM",
                            PlanType: COVER_TYPE_MAPPING[cover_type] || cover_type,  // Convert SUPREME to Supreme
                            SumInsured: sumInsured,
                            Deductible: member.deductible,
                            Relation: member.relation,
                            Height: "170",
                            Weight: "70",
                            Smoking: 'N',
                            Tobacco: 'N',
                            IsGoodHealth: 'Y',
                            IsExistingAbsolutePolicy: 'N',
                            NomineeName: "Test Nominee",
                            NomineeRelation: "FATH",
                            NomineeAge: "45"
                        };
                    })
                },
                Risk: {
                    PolicyType: policyType,
                    Duration: duration || "1", // Use provided duration or default to 1
                    Installments: "FULL",
                    PaymentType: "CC"
                }
            };

            // Call the floater SOAP service
            const soapResponse = await sendAdvTopupSOAPRequest(soapData);

            // Extract relevant data from the SOAP response
            const outputRes = soapResponse?.policyDetails?.Root?.Policy[0]?.OutputRes[0]; // Access OutputRes
            const memberResults = soapResponse?.policyDetails?.Root?.Policy[0]?.InputParameters?.[0]?.BeneficiaryDetails[0]?.Member.map((member) => ({
                memberId: member.MemberId[0],
                coverType: member.CoverType[0],
                sumInsured: member.SumInsured[0],
                basePremium: member.PerPrsnPremium[0],
                relation: member.Relation[0],
            })) || []; // Handle member results extraction

            const outputResponseData = {
                premiumAmt: outputRes.PremiumAmt[0],
                serviceTax: outputRes.ServiceTax[0],
                premWithServiceTax: outputRes.PremWithServTax[0],
            };

            // Push the results into tempResults
            tempResults.push({
                sumInsured: sumInsured,
                requestId: numericUid,
                premiums: memberResults,
                outputResponse: outputResponseData,
                rawResponse: soapResponse
            });
        }

        // Respond with the temporary results
        res.json({
            status: 'success',
            coverType: cover_type,
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName,
                company_name: company_name,
                duration:duration
            },
            results: tempResults // Return all results after processing
        });

    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ error: error.message });
    }
});

router.post('/advtopupcreate', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name,duration } = req.body;
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Generate numeric UID
        const numericUid = uuidv4();

        // Calculate dates
        const startDate = new Date();
        startDate.setDate(startDate.getDate() + 1);
        const endDate = new Date(startDate);
        endDate.setFullYear(endDate.getFullYear() + 1);
        endDate.setDate(endDate.getDate() - 1);

        const policyStartDate = formatDateToDDMMYYYY(startDate);
        const policyEndDate = formatDateToDDMMYYYY(endDate);

        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopUp'
        }

        const normalizedProductName = productMapping[product_master_name] || product_master_name;

        const policyType = family_type === 'individual' ? 'HTI' : 'HTF';

        const soapData = {
            Product: normalizedProductName,
            PolicyHeader: {
                PolicyStartDate: policyStartDate,
                PolicyEndDate: policyEndDate,
                AgentCode: "60000272",
                BranchCode: "51",
                MajorClass: "FAT",
                ContractType: "FAT",
                METHOD: "ENQ",
                PolicyIssueType: "I"
            },
            Uid: numericUid,
            VendorCode: "webagg",
            VendorUserId: "webagg",
            Client: {
                ClientType: "I",
                CreationType: "C",
                Address1: {
                    Pincode: pincode,
                    Country: "IND"
                }
            },
            BeneficiaryDetails: {
                Member: members.map((member, index) => ({
                    MemberId: (index + 1).toString(),
                    InsuredName: `Member ${index + 1}`,
                    InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                    InsuredGender: member.gender || 'M',
                    InsuredOccpn: "SVCM",
                    PlanType: COVER_TYPE_MAPPING[cover_type] || cover_type,
                    SumInsured: member.sumInsured,
                    Deductible: member.deductible,
                    Relation: member.relation,
                    Height: "170",
                    Weight: "70",
                    Smoking: "N",
                    Tobacco: "N",
                    IsGoodHealth: "Y",
                    IsExistingAbsolutePolicy: "N",
                    NomineeName: "Test Nominee",
                    NomineeRelation: "FATH",
                    NomineeAge: "45"
                }))
            },
            Risk: {
                PolicyType: policyType,
                Duration: duration ,
                Installments: "FULL",
                PaymentType: "CC"
            }
        };

        const soapResponse = await sendAdvTopupSOAPRequest(soapData);

        // Add null checks for the SOAP response
        if (!soapResponse?.policyDetails?.Root?.Policy?.[0]) {
            throw new Error('Invalid SOAP response structure');
        }

        const outputRes = soapResponse.policyDetails.Root.Policy[0].OutputRes?.[0];
        if (!outputRes) {
            throw new Error('Missing OutputRes in SOAP response');
        }

        const memberResults = soapResponse?.policyDetails?.Root?.Policy[0]?.InputParameters?.[0]?.BeneficiaryDetails[0]?.Member.map((member) => ({
            memberId: member.MemberId[0],
            sumInsured: member.SumInsured[0], // Ensure SumInsured is accessed correctly
            basePremium: member.PerPrsnPremium[0],
            coverType: member.CoverType[0],
            relation: member.Relation[0],
        })) || []; // Handle member results extraction

        const outputResponseData = {
            premiumAmt: outputRes.PremiumAmt[0],
            serviceTax: outputRes.ServiceTax[0],
            premWithServiceTax: outputRes.PremWithServTax[0],
            // Add any other fields from outputRes that you want to store
        };
        // Log the member results to the console

        const formattedResponse = {
            status: 'success',
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName, // Added product to the response
                company_name: company_name,
                duration:duration
            },
            results: {
                premiums: memberResults,
                outputResponse: outputResponseData
            },
            rawResponse: soapResponse
        };


        res.json(formattedResponse);
    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;