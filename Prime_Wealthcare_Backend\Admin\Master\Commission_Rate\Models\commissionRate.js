const knexConfig = require('../../../../knexfile');
const db = require('knex')(knexConfig.development);

class CommissionRate {
  // Get all commission rates with related details
  static async getAll() {
    try {
      const commissionRates = await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )

      return commissionRates;
    } catch (error) {
      console.error('Error fetching commission rates:', error);
      throw error;
    }
  }

  // Get commission rate by ID with related details
  static async getById(id) {
    if (!id) throw new Error("Commission rate ID is required");

    try {
      const commissionRate = await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .where('commission_rate.id', id)
        .first();

      return commissionRate || null;
    } catch (error) {
      console.error(`Error fetching commission rate with ID: ${id}`, error);
      throw error;
    }
  }

  // Create a new commission rate
  static async create(commissionData) {
    try {
      const [commissionId] = await db('commission_rate').insert(commissionData).returning('id');
      return commissionId;
    } catch (error) {
      console.error('Error creating commission rate:', error);
      throw error;
    }
  }
  // Update a commission rate
  static async updateCommissionRate(id, updateData) {
    try {
      const result = await db('commission_rate')
        .where({ id }) // Filter by the given ID
        .update(updateData); // Update with the new data

      return result; // Return the result of the update
    } catch (error) {
      console.error("Error updating commission rate in model:", error);
      throw error; // Throw the error to be handled in the controller
    }
  }

  // Update multiple commission rates
  static async updateCommissionRates(commissionRates) {
    const promises = commissionRates.map(rate => {
      const { id, fixed_percentage } = rate;
      return db.query('UPDATE commission_rates SET fixed_percentage = ? WHERE id = ?', [fixed_percentage, id]);
    });
    await Promise.all(promises);
  }

  // Soft delete a commission rate by ID (set status to 0)
  static async delete(id) {
    try {
      const result = await db('commission_rate').where('id', id).update({ status: 0 });
      return result;
    } catch (error) {
      console.error(`Error deleting commission rate with ID: ${id}`, error);
      throw error;
    }
  }

  // Reinstate a commission rate by ID (set status to 1)
  static async reinstate(id) {
    try {
      const result = await db('commission_rate').where('id', id).update({ status: 1 });
      return result;
    } catch (error) {
      console.error(`Error reinstating commission rate with ID: ${id}`, error);
      throw error;
    }
  }

  // Search for commission rates by company name or product name
  static async getByName(name) {
    try {
      const commissionRates = await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name',

        )
        .where(function () {
          this.where('insurance_company.insurance_company_name', 'LIKE', `%${name}%`)
            .orWhere('main_product.main_product', 'LIKE', `%${name}%`)
            .orWhere('product_master.product_name', 'LIKE', `%${name}%`)
            .orWhere('sub_product.sub_product_name', 'LIKE', `%${name}%`)

        });

      return commissionRates;
    } catch (error) {
      console.error(`Error fetching commission rates by name: ${name}`, error);
      throw error;
    }
  }




  static async getCommissionRatesByInsuranceCompanyId(insuranceCompanyId) {
    try {
      const commissionRates = await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id') // Correct join
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .where('commission_rate.insurance_company_id', insuranceCompanyId); // Filter by insurance company ID

      return commissionRates;
    } catch (error) {
      console.error(`Error fetching commission rates for insurance company ID: ${insuranceCompanyId}`, error);
      throw error;
    }
  }

  static async insertCommissionRates() {
    const trx = await db.transaction(); // Start transaction
    try {
      // Fetch data from sub_product and pick_list
      const commissionRatesData = await trx
        .select(
          'sp.insurance_company_id',
          'sp.main_product_id',
          'sp.product_master_id',
          'sp.id as sub_product_id',
          'cs.label_name as commission_source',
          'ct.label_name as commission_type',
          'pt.label_name as policy_type',
          db.raw('0 as fixed_percentage'), // Default fixed percentage (0)
          db.raw('NULL as range_from'),       // Default to NULL for range_from
          db.raw('NULL as range_to'),         // Default to NULL for range_to
          db.raw('NULL as extra_percentage'), // Default to NULL for extra percentage
          db.raw('NULL as effective_from'),   // Default to NULL for effective_from
          db.raw('NULL as effective_to'),     // Default to NULL for effective_to

          db.raw('NOW() as created_at'),   // Timestamp for creation
          db.raw('NOW() as updated_at'),    // Timestamp for last update

        )
        .from('sub_product as sp')
        .crossJoin(
          trx.select('label_name')
            .from('pick_list')
            .where('type_name', 'Commission Source')
            .as('cs')
        )
        .crossJoin(
          trx.select('label_name')
            .from('pick_list')
            .where('type_name', 'Commission Payable Type')
            .as('ct')
        )
        .crossJoin(
          trx.select('label_name')
            .from('pick_list')
            .where('type_name', 'Policy Type')
            .as('pt')
        );

      // Insert commission rates into the commission_rate table
      await trx('commission_rate').insert(commissionRatesData);
      //await trx.batchInsert('commission_rate', commissionRatesData, 100);

      // Commit the transaction
      await trx.commit();
      
    } catch (error) {
      // Rollback the transaction if any error occurs
      await trx.rollback();
      console.error("Error inserting commission rates:", error);
      throw error;
    }
  }

  static async updateCommissionRates(updates) {
    const trx = await db.transaction();
    try {
      for (const update of updates) {
        const { id, fixed_percentage, ...otherFields } = update;

        // Ensure the fixed_percentage is a number and id is valid
        if (typeof fixed_percentage !== 'number' || typeof id !== 'number') {
          throw new Error('Invalid data: id must be a number and fixed_percentage must be a number.');
        }

        const updated = await trx('commission_rate')
          .where({ id })
          .update({
            fixed_percentage,
            updated_at: db.raw('NOW()'),
            ...otherFields, // Spread to include additional fields
          });

        if (updated === 0) {
          console.warn(`No records updated for ID: ${id}`);
        }
      }
      await trx.commit();
      return true;
    } catch (error) {
      await trx.rollback();
      console.error("Error updating commission rates:", error);
      throw error;
    }
  }

  static async newLastWeek() {
    try {
      return await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .where('commission_rate.created_at', '<', db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'));
    } catch (error) {
      throw error;
    }
  }

  // Fetch commission rates created this week
  static async newThisWeek() {
    try {
      return await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .whereBetween('commission_rate.created_at', [
          db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
          db.raw('NOW()')
        ]);
    } catch (error) {
      throw error;
    }
  }

  // Fetch deactivated commission rates updated this week
  static async deactivatedThisWeek() {
    try {
      return await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .where('commission_rate.status', 0)  // Specify the table for the 'status' column
        .whereBetween('commission_rate.updated_at', [
          db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
          db.raw('NOW()')
        ]);
    } catch (error) {
      throw error;
    }
  }

  // Fetch deactivated commission rates updated last week
  static async deactivatedLastWeek() {
    try {
      return await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .where('commission_rate.status', 0)  // Specify the table for the 'status' column
        .whereBetween('commission_rate.updated_at', [
          db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
          db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
        ]);
    } catch (error) {
      throw error;
    }
  }

  // Fetch commission rates edited this week
  static async editedThisWeek() {
    try {
      return await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .whereBetween('commission_rate.updated_at', [
          db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
          db.raw('NOW()')
        ]);
    } catch (error) {
      throw error;
    }
  }

  // Fetch commission rates edited last week
  static async editedLastWeek() {
    try {
      return await db('commission_rate')
        .join('insurance_company', 'commission_rate.insurance_company_id', 'insurance_company.id')
        .join('main_product', 'commission_rate.main_product_id', 'main_product.id')
        .join('product_master', 'commission_rate.product_master_id', 'product_master.id')
        .join('sub_product', 'commission_rate.sub_product_id', 'sub_product.id')
        .select(
          'commission_rate.*',
          'insurance_company.insurance_company_name',
          'main_product.main_product as main_product_name',
          'product_master.product_name as product_master_name',
          'sub_product.sub_product_name'
        )
        .whereBetween('commission_rate.updated_at', [
          db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
          db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
        ]);
    } catch (error) {
      throw error;
    }
  }
}
module.exports = CommissionRate;
