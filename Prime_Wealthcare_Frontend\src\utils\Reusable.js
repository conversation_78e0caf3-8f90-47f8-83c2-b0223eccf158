import crypto from 'crypto-js';
import { getPaymentCount } from '../redux/actions/action';
import { Avatar } from '@mui/material';
import AvatarImage from '../components/AvatarImage';
import dayjs from 'dayjs';
export const trimFormData = (formData) => {
    const trimmedData = {};
    for (const key in formData) {
        if (typeof formData[key] === 'string') {
            trimmedData[key] = formData[key].trim();
        } else {
            trimmedData[key] = formData[key];
        }
    }
    return trimmedData;
};

export const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${day}-${month}-${year}`;
};

export const generateChecksum = (formData) => {
    const dataArray = [
        formData.TransactionID,
        formData.PaymentOption,
        formData.ResponseURL,
        formData.ProposalNumber,
        formData.PremiumAmount,
        formData.UserIdentifier,
        formData.UserId,
        formData.FirstName,
        formData.LastName,
        formData.Mobile,
        formData.Email
    ];

    const concatenatedString = dataArray.join('|') + '|';
    return crypto.SHA256(concatenatedString).toString();

};

export const generateProposalNumber = async (userId, dispatch) => {
    const currentYear = new Date().getFullYear();

    // Get the count from the API response
    const prefix = `P-${currentYear}${userId}-`;
    const response = await dispatch(getPaymentCount(prefix));
    const currentCount = (response.payload || 0) + 1;
    const counterPadded = currentCount.toString().padStart(5, '0');
    const newProposalNumber = `P-${currentYear}${userId}-${counterPadded}`;
    return newProposalNumber
};

export const generateHash = (formData) => {
    const key = Math.floor(1000000000 + Math.random() * 9000000000);  // random 10 digit number
    const salt = Math.floor(1000000000 + Math.random() * 9000000000); // random 10 digit number

    const udf = [
        formData.FirstName,    // udf1
        formData.Email,        // udf2
        formData.ResponseURL,  // udf3
        formData.PaymentOption,// udf4
        formData.ProposalNumber// udf5
    ];

    const hashString = [
        key,                    // key
        formData.TransactionID, // txnid
        formData.PremiumAmount,// amount
        formData.ProductInfo,  // productinfo
        formData.FirstName,    // firstname
        formData.Email,        // email
        "", "", "", "", "",   // empty fields
        "&%#@?,:*"                  // salt
    ].join('|');

    return crypto.SHA512(hashString).toString();
};

export const formatToTwoDecimals = (value) => {
    // Handle empty or null values
    if (!value && value !== 0) return '';

    // Handle case where decimal point is being added
    if (typeof value === 'string' && value.endsWith('.')) {
        const baseNumber = value.slice(0, -1);
        return isNaN(Number(baseNumber)) ? '' : `${baseNumber}.0`;
    }

    // Handle case where decimal point is being removed
    if (typeof value === 'string' && value.includes('.') && value.split('.')[1] === '') {
        return value.slice(0, -1);
    }

    // Convert to number and check if it's valid
    const number = Number(value);
    if (isNaN(number)) return '';

    // If it's a whole number with no decimal point
    if (Number.isInteger(number) && !String(value).includes('.')) {
        return String(number);
    }

    // Truncate to 2 decimal places
    const truncated = Math.floor(number * 100) / 100;
    return truncated.toFixed(2);
};

export const calculateMaxSumInsured = (annualIncome, coverType, occupation, riskClass, relation) => {
    const monthlyIncome = annualIncome / 12;

    // Multiplying factors based on the table
    const multipliers = {
        'AD': 144,  // Accidental Death
        'PT': 144,  // Permanent Total Disability
        'PP': 144,  // Permanent Partial Disability
        'TTD': 24   // Temporary Total Disability
    };

    /*  // Check for Unemployed/Students with relation 'SELF'
     if ((relation === 'SELF') && (riskClass === 'Unemployed' || riskClass === 'Student') && ['AD', 'PT', 'PTD', 'PP'].includes(coverType)) {
         // Allow entering sum insured without considering monthly income
         return 1000000; // Allow maximum of ₹10 Lakhs
     } */

    // Special case for Unemployed/Student who is SELF
    if ((relation === 'SELF') && (occupation === 'Unemployed' || occupation === 'Student')) {
        if (['AD', 'PT', 'PP'].includes(coverType)) {
            return 1000000; // 10 lakhs maximum for AD, PTD, PPD
        }
        if (coverType === 'TTD') {
            return 0; // No TTD for Unemployed/Student
        }
    }


    // Calculate the maximum based on income
    const calculatedMax = monthlyIncome * (multipliers[coverType] || 0);

    // For AD, PP, and PT, cap at 30 lakhs
    if (['AD', 'PP', 'PT'].includes(coverType)) {
        return Math.min(calculatedMax, 3000000); // 30 lakhs = 3000000
    } else if (coverType === 'TTD') {
        // Convert riskClass to string for comparison if needed
        const riskClassStr = String(riskClass);

        if (riskClassStr === '1' || riskClass === 1) {
            return Math.min(calculatedMax, 5000000); // 50 lakhs for class 1
        } else if (riskClassStr === '2' || riskClass === 2) {
            return Math.min(calculatedMax, 2500000); // 25 lakhs for class 2
        }
        return Math.min(calculatedMax, 2500000); // Default cap
    }

    return calculatedMax;
};

export const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return 0;

    const birth = dayjs(dateOfBirth);
    if (!birth.isValid()) return 0;          // Optional: requires isValid() plugin if not included by default :contentReference[oaicite:3]{index=3}

    // diff in 'year' automatically truncates to integer years
    const age = dayjs().diff(birth, 'year');      // :contentReference[oaicite:4]{index=4}
    return age;
};

export const currentFinancialYear = () => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    if (currentMonth < 3) {
        return `${currentYear - 1}-${currentYear - 2000}`;
    }
    return `${currentYear}-${currentYear - 1999}`;
}

export const renderAvatar = (photo, full_name) => {
    return (
        <AvatarImage
            src={photo || ''}
            alt={full_name}
            size={photo ? 100 : 50}
        />
    );
};

export const maskDOB = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return `**/**/${date.getFullYear()}`; // Only show year
}

export const maskMobileNumber = (number) => {
    if (!number) return 'N/A';
    return number.slice(0, 1) + '******' + number.slice(7);
}

export const maskEmail = (email) => {
    if (!email) return 'N/A';
    const [localPart, domain] = email.split('@');
    const firstChar = localPart.charAt(0);
    const lastTwoChars = localPart.slice(-2);
    const maskedPart = '*'.repeat(Math.max(0, localPart.length - 3));
    return `${firstChar}${maskedPart}${lastTwoChars}@${domain}`;
}

export const formatIndianValue = (value, isMobileView = window.innerWidth <= 768) => {
    if (!value) return '₹0';

    // Convert string values to number
    const amount = typeof value === 'string' ?
        Number(value.replace('₹', '').replace(/,/g, '')) :
        Number(value);

    // Format for crores (≥ 1Cr) - show on all screen sizes
    if (amount >= 10000000) {
        return '₹' + (amount / 10000000).toFixed(2).replace(/\.?0+$/, '') + 'Cr';
    }
    // Format for lakhs (≥ 1L) - show on all screen sizes
    else if (amount >= 100000) {
        return '₹' + (amount / 100000).toFixed(2).replace(/\.?0+$/, '') + 'L';
    }
    // Format for thousands (≥ 1K) - only on mobile/tablet
    else if (isMobileView && amount >= 1000) {
        return '₹' + (amount / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    // Regular formatting
    return '₹' + amount.toLocaleString('en-IN');
};
