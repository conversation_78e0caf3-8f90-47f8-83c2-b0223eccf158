import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { getAllEndorsments, getEndorsmentById, updateEndorsment, createEndorsment } from '../../../redux/actions/action';
import DropDown from '../../../components/table/DropDown';
import { trimFormData } from '../../../utils/Reusable';

function EndorsmentTypeForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // State for form data and validation
  const [formData, setFormData] = useState({
    endorsment_type: '',
    endorsment_name: '',
    endorsment_description: '',
  });
  const [formErrors, setFormErrors] = useState({
    endorsment_type: false,
    endorsment_name: false,
    endorsment_description: false,
  });

  const endorsment = useSelector(state => state.endorsmentTypeReducer.endorsment);
  const endorsments = useSelector(state => state.endorsmentTypeReducer.endorsments);

  // Load all endorsements and the selected endorsement if in edit mode
  useEffect(() => {
    if (id) {
      dispatch(getEndorsmentById(id));
    }
  }, [id, dispatch]);

  // Update form data when endorsement is fetched, and avoid state updates if values are unchanged
  useEffect(() => {
    if (endorsment && id) {
      setFormData({
        endorsment_type: endorsment?.endorsment_type || '',
        endorsment_name: endorsment?.endorsment_name || '',
        endorsment_description: endorsment?.endorsment_description || '',
      })
    }
  }, [endorsment, id]);

  // Handle form input changes and validation
  const handleChange = (e) => {
    const { name, value } = e.target;

    if (value === ' ') {
      setFormErrors({
        ...formErrors,
        [name]: 'Do not start with a whitespace character'
      })
      return;
    }
    const data = name !== 'endorsment_type' ? value.toUpperCase().replace(/\s{2,}$/, ' ') : value
    setFormData(prevData => ({ ...prevData, [name]: data }));
    setFormErrors(prevErrors => ({ ...prevErrors, [name]: !value.trim() }));
  };

  // Validate form fields
  const validateForm = () => {
    let tempErrors = {};
    if (!formData.endorsment_type) {
      tempErrors.endorsment_type = "Endorsment Type is required"
    }

    if (!formData.endorsment_name) {
      tempErrors.endorsment_name = "Endorsment Name is required"
    } else if (formData.endorsment_name) {
      const isDuplicate = endorsments.find(emt => emt.endorsment_name === formData.endorsment_name && emt.id !== Number(id));
      if (isDuplicate) {
        tempErrors.endorsment_name = "Endorsment Name already exists"
      }
    }
    if (!formData.endorsment_description) {
      tempErrors.endorsment_description = "Endorsment Description is required"
    }
    setFormErrors(tempErrors);

    return Object.keys(tempErrors).length === 0;
  };

  // Handle form submission logic (Create or Update)
  const handleSubmit = () => {
    if (!validateForm()) return;
    const { endorsment_type, endorsment_name, endorsment_description } = formData;
    const temp = { endorsment_type, endorsment_name, endorsment_description };
    const data = trimFormData(temp);

    if (id) {
      dispatch(updateEndorsment({ id, data }));
    } else {
      dispatch(createEndorsment(data));
    }
    setTimeout(() => {
      dispatch(getAllEndorsments());
  }, 500);
  };

  // Handle 'Save & New' button click
  const handleSaveAndNew = () => {
    if (validateForm()) {
      handleSubmit();
      setFormData({
        endorsment_type: '',
        endorsment_name: '',
        endorsment_description: ''
      });
    }
  };

  // Handle 'Save' button click and navigate back to the list
  const handleSave = () => {
    if (validateForm()) {
      handleSubmit();
      navigate('/dashboard/endorsment-type');
    }
  };

  // Handle 'Cancel' button click to navigate back to the list
  const handleCancel = () => {
    navigate('/dashboard/endorsment-type');
  };

  return (
    <form>
      <Grid container spacing={2}>
        {/* Header */}
        <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
          <img src="/image.png" alt="module icon" style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }} />
          <Box sx={{ width: '100%', margin: '0 20px' }}>
            <ModuleName moduleName="Endorsment Type" pageName={id ? endorsment?.status === 0 ? "View" : "Edit" : "Create"} />
          </Box>
        </Grid>

        {/* Action buttons */}
        <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
          {!id && (
            <Button
              variant="outlined"
              size="small"
              sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
              onClick={handleSaveAndNew}
            >
              Save & New
            </Button>
          )}
          {(endorsment?.status === 1 || !id) && (
            <Button
              variant="outlined"
              size="small"
              sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
              onClick={handleSave}
            >
              Save
            </Button>
          )}
          <Button
            variant="outlined"
            size="small"
            sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </Grid>

        {/* Form fields */}
        <Grid item xs={12}>
          <Box sx={{ backgroundColor: '#f0f0f0', padding: '1rem', borderRadius: '4px', mb: 2 }}>
            <h2>Endorsment Information</h2>
          </Box>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
          {/* Endorsment Type Dropdown */}
          <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
            <DropDown
              label="Endorsment Type"
              name="endorsment_type"
              options={[
                { value: 'Financial', label: 'Financial' },
                { value: 'Non-Financial', label: 'Non-Financial' },
              ]}
              value={formData.endorsment_type}
              onChange={handleChange}
              fullWidth
              error={formErrors.endorsment_type}
              helperText={formErrors.endorsment_type || ''}
              disabled={id && endorsment?.status === 0}
              required
            />
          </Grid>

          {/* Endorsment Name */}
          <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
            <CustomTextField
              name="endorsment_name"
              label="Endorsment Name"
              value={formData.endorsment_name}
              onChange={handleChange}
              fullWidth
              error={formErrors.endorsment_name}
              helperText={formErrors.endorsment_name || ''}
              disabled={id && endorsment?.status === 0}
              isRequired
            />
          </Grid>

          {/* Endorsment Description */}
          <Grid item xs={3} sx={{ margin: '0, 30px', minWidth: 'max-content' }}>
            <CustomTextField
              name="endorsment_description"
              label="Endorsment Description"
              value={formData.endorsment_description}
              onChange={handleChange}
              fullWidth
              error={formErrors.endorsment_description}
              helperText={formErrors.endorsment_description || ''}
              disabled={id && endorsment?.status === 0}
              isRequired
            />
          </Grid>
        </Box>
      </Grid>
    </form>
  );
}

export default EndorsmentTypeForm;