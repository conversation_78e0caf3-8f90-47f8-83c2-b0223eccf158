const SubProductRider = require('../Models/subProductRider.js');

// Create new product
exports.create = async (req, res,next) => {
    try {
        const productData = req.body;
        const data = await SubProductRider.create(productData);
        const dataToSend = {
            ...productData,
            id: data
        }
        res.status(201).json(dataToSend);
    } catch (error) {
        next(error);
    }
};

// Update product by ID
exports.update = async (req, res, next) => {
    try {
        const { id } = req.params;
        const productData = req.body;
        await SubProductRider.update(id, productData);

        res.status(200).json('Product updated successfully');
    } catch (error) {
        next(error);
    }
};

// Soft delete (deactivate) product by ID
exports.delete = async (req, res, next) => {
    try {
        const { id } = req.params;
        await SubProductRider.delete(id);
        res.status(200).json({ message: 'Sub product deactivated successfully' });
    } catch (error) {
        next(error);
    }
};
