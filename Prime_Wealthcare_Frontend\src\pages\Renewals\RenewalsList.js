import React, { useState } from 'react';
import { Box, Container, Button, ButtonGroup, TextField, MenuItem } from '@mui/material';
import { Add as AddIcon, Download as DownloadIcon } from '@mui/icons-material';
import ModuleName from '../../components/table/ModuleName';
import CustomTable from '../../components/table/CustomTable';
import { useNavigate } from 'react-router-dom';
import { Tooltip, IconButton } from '@mui/material';
import GroupIcon from '@mui/icons-material/Group';

const RenewalsList = () => {
    const navigate = useNavigate();
    const [selectedRows, setSelectedRows] = useState([]);
    const [recordsPerPage, setRecordsPerPage] = useState(50);

    // Static data matching the screenshot format
    const staticData = [
        {
            id: 1,
            expiry_date: '2025-05-09',
            month: 'May',
            cust_name: '<PERSON><PERSON><PERSON> PATEL',
            customer_number: '**********',
            imf_branch_name: 'Ahmedabad',
            old_policy_number: 'POL-2024-001',
            insurance_company: 'HDFC ERGO GENERAL INSURANCE CO. LTD.',
            main_product: 'HEALTH',
            product: 'HEALTH SURAKSHA',
            sub_product: 'GOLD',
            members: [
                { id: 1, name: 'KUMAR PATEL' },
                { id: 2, name: 'MEENA PATEL' }
            ],
            premium: 25756,
            agent_code: 'AG001',
            agent_name: 'RAJESH SHAH',
            renewal_status: 'Pending'
        },
        {
            id: 2,
            expiry_date: '2025-06-15',
            month: 'June',
            cust_name: 'DASHRATH PAREKH',
            customer_number: '**********',
            imf_branch_name: 'Vadodara',
            old_policy_number: 'POL-2024-002',
            insurance_company: 'FUTURE GENERALI INDIA INSURANCE CO. LTD.',
            main_product: 'HEALTH',
            product: 'HEALTH ABSOLUTE',
            sub_product: 'CLASSIC',
            members: [
                { id: 3, name: 'DASHRATH PAREKH' },
                { id: 4, name: 'LATA PAREKH' },
                { id: 5, name: 'MEET PAREKH' }
            ],
            premium: 38697,
            agent_code: 'AG002',
            agent_name: 'PRIYA DESAI',
            renewal_status: 'Renewed'
        },
        {
            id: 3,
            expiry_date: '2025-04-30',
            month: 'April',
            cust_name: 'MEHUL SHAH',
            customer_number: '**********',
            imf_branch_name: 'Surat',
            old_policy_number: 'POL-2024-003',
            insurance_company: 'STAR HEALTH AND ALLIED INSURANCE CO. LTD.',
            main_product: 'HEALTH',
            product: 'FAMILY HEALTH OPTIMA',
            sub_product: 'PREMIUM',
            members: [
                { id: 6, name: 'MEHUL SHAH' }
            ],
            premium: 15899,
            agent_code: 'AG003',
            agent_name: 'AMIT PATEL',
            renewal_status: 'Not Renewed'
        },
        {
            id: 4,
            expiry_date: '2025-05-20',
            month: 'May',
            cust_name: 'PRITI JOSHI',
            customer_number: '**********',
            imf_branch_name: 'Rajkot',
            old_policy_number: 'POL-2024-004',
            insurance_company: 'CARE HEALTH INSURANCE LTD.',
            main_product: 'HEALTH',
            product: 'CARE ADVANTAGE',
            sub_product: 'SILVER',
            members: [
                { id: 7, name: 'PRITI JOSHI' },
                { id: 8, name: 'RAKESH JOSHI' }
            ],
            premium: 42500,
            agent_code: 'AG004',
            agent_name: 'SNEHA MEHTA',
            renewal_status: 'Pending'
        },
        {
            id: 5,
            expiry_date: '2025-06-05',
            month: 'June',
            cust_name: 'NITIN DESAI',
            customer_number: '**********',
            imf_branch_name: 'Bhavnagar',
            old_policy_number: 'POL-2024-005',
            insurance_company: 'NIVA BUPA HEALTH INSURANCE CO. LTD.',
            main_product: 'HEALTH',
            product: 'REASSURANCE',
            sub_product: 'PLATINUM',
            members: [
                { id: 9, name: 'NITIN DESAI' },
                { id: 10, name: 'REKHA DESAI' },
                { id: 11, name: 'ROHAN DESAI' },
                { id: 12, name: 'RIYA DESAI' }
            ],
            premium: 68900,
            agent_code: 'AG005',
            agent_name: 'KETAN SHAH',
            renewal_status: 'Renewed'
        }
    ];

    const columns = [
        { field: 'expiry_date', headerName: 'Expiry Date', width: 100 },
        { field: 'month', headerName: 'Month', width: 100 },
        { field: 'cust_name', headerName: 'Customer Name', width: 200 },
        { field: 'customer_number', headerName: 'Customer Number', width: 130 },
        { field: 'imf_branch_name', headerName: 'IMF Branch Name', width: 150 },
        { field: 'old_policy_number', headerName: 'Old Policy Number', width: 180 },
        { field: 'insurance_company', headerName: 'Insurance Company', width: 180 },
        { field: 'main_product', headerName: 'Main Product', width: 130 },
        { field: 'product', headerName: 'Product', width: 130 },
        { field: 'sub_product', headerName: 'Sub Product', width: 130 },
        {
            field: 'member_count',
            headerName: 'Number of Members',
            width: 140,
            renderCell: (params) => {
                const members = params.row.members || [];
                return (
                    <Tooltip
                        title={
                            <div style={{ padding: '8px' }}>
                                {members.map((member) => (
                                    <div key={member.id} style={{ padding: '2px 0' }}>
                                        {member.name}
                                    </div>
                                ))}
                            </div>
                        }
                        arrow
                    >
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                        }}>
                            <GroupIcon sx={{ fontSize: 20, color: '#528A7E' }} />
                            <span>{members.length}</span>
                        </div>
                    </Tooltip>
                );
            }
        },
        {
            field: 'premium',
            headerName: 'Premium',
            width: 120,
            valueFormatter: (params) => {
                if (params.value) {
                    return `₹${params.value.toLocaleString('en-IN')}`
                }
                return '';
            }
        },
        { field: 'agent_code', headerName: 'Agent Code', width: 120 },
        { field: 'agent_name', headerName: 'Agent Name', width: 180 },
        {
            field: 'renewal_status',
            headerName: 'Renewal Status',
            width: 130,
            renderCell: (params) => (
                <span style={{
                    color: params.value === 'Renewed' ? 'green' :
                        params.value === 'Pending' ? 'orange' : 'red'
                }}>
                    {params.value}
                </span>
            )
        }
    ];

    const handlePolicyClick = (policyNo) => {
        // Add your navigation or action logic here
    };

    const handleAdd = () => {
        navigate('/dashboard/generate-renewals');
    };

    const handleGenerateRenewal = () => {
        navigate('/dashboard/generate-renewals');
    };

    const handleView = (id) => {
    };

    const handleEdit = (id) => {
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prev => {
            if (prev.includes(id)) {
                return prev.filter(selectedId => selectedId !== id);
            } else {
                return [...prev, id];
            }
        });
    };

    const handleSelectAll = (isSelected) => {
        if (isSelected) {
            const allIds = staticData.map(item => item.id);
            setSelectedRows(allIds);
        } else {
            setSelectedRows([]);
        }
    };

    const handleDownload = () => {
        // Add your download logic here
    };

    return (
        <Container maxWidth="xl" sx={{ p: 0, m: 0 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* Header Section */}
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 2,
                    borderBottom: '1px solid #e0e0e0'
                }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{
                                width: '20px',
                                marginLeft: '20px',
                                backgroundColor: 'green'
                            }}
                        />
                        <ModuleName moduleName="Renewals" pageName="List" />
                    </Box>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <Button
                            variant="outlined"
                            startIcon={<AddIcon />}
                            onClick={handleGenerateRenewal}
                            sx={{
                                color: 'green',
                                borderColor: 'green',
                                '&:hover': {
                                    borderColor: 'green',
                                    backgroundColor: 'rgba(0, 128, 0, 0.1)'
                                }
                            }}
                        >
                            Generate Renewals
                        </Button>
                        <Button
                            variant="outlined"
                            onClick={handleDownload}
                            sx={{
                                minWidth: '40px',
                                width: '40px',
                                height: '40px',
                                padding: '8px',
                                color: 'primary.main',
                                borderColor: 'primary.main',
                                '&:hover': {
                                    borderColor: 'primary.main',
                                    backgroundColor: 'rgba(25, 118, 210, 0.04)'
                                }
                            }}
                        >
                            <DownloadIcon />
                        </Button>
                    </Box>
                </Box>

                {/* Table Section */}
                <Box sx={{ width: '100%', overflow: 'auto' }}>
                    <CustomTable
                        data={staticData}
                        columns={columns}
                        onEdit={handleEdit}
                        onView={handleView}
                        selectedRows={selectedRows}
                        onSelectionChange={handleSelectionChange}
                        onSelectAll={handleSelectAll}
                        showEyeIcon={true}
                        recordsPerPage={recordsPerPage}
                    />
                </Box>
            </Box>
        </Container>
    );
};

export default RenewalsList;