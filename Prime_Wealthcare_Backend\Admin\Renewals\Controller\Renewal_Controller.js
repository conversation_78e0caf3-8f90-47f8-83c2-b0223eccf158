const RenewalModel = require('../Models/Renewal_Model');

class RenewalController {
    static async saveRenewalsMapping(req, res) {
        try {
            const { insurance_company_id, renewals } = req.body;
            // const user_id = req.user.id; // Assuming user info is in request

            // Validate request data
            if (!insurance_company_id || !Array.isArray(renewals) || renewals.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid request data'
                });
            }

            // Validate required fields in each renewal
            const requiredFields = [
                'customer_name',
                'customer_number',
                'old_policy_number',
                'expiry_date'
            ];

            for (const renewal of renewals) {
                const missingFields = requiredFields.filter(field => !renewal[field]);
                if (missingFields.length > 0) {
                    return res.status(400).json({
                        success: false,
                        message: `Missing required fields: ${missingFields.join(', ')}`
                    });
                }
            }

            // Save renewals
            const savedRenewals = await RenewalModel.saveRenewalsMapping(
                renewals,
                insurance_company_id,
                // user_id
            );

            return res.status(201).json({
                success: true,
                message: 'Renewals mapped successfully',
                data: {
                    total_records: savedRenewals.length,
                    renewals: savedRenewals
                }
            });

        } catch (error) {
            console.error('Error in saveRenewalsMapping:', error);
            return res.status(500).json({
                success: false,
                message: 'Error saving renewals mapping',
                error: error.message
            });
        }
    }
}

module.exports = RenewalController;