exports.up = function (knex) {
  return knex.schema.createTable('imf_branches', function (table) {
    table.increments('id').primary();  // INT AUTO_INCREMENT PRIMARY KEY
    table.string('branch_name', 100).notNullable();  // VARCHAR(100)
    table.string('branch_code', 100).notNullable();  // VARCHAR(100)
    table.string('email', 100).notNullable();  // VARCHAR(100)
    table.string('help_line_number', 20).nullable();  // VARCHAR(20)
    table.string('branch_manager_name', 100).notNullable();
    table.string('branch_manager_number', 20).nullable();  // VARCHAR(20)

    table.string('address_line1', 255).nullable();  // VARCHAR(255)
    table.string('address_line2', 255).nullable();  // VARCHAR(255)
    table.integer('pincode', 255).notNullable();
    table.string('city', 255).notNullable();
    table.string('pincode_city').references('pincode_city').inTable('locations').onDelete('CASCADE');
    table.string('state', 255).notNullable();

    table.integer('area').unsigned().nullable()
      .references('id').inTable('areas') // Foreign key to `areas`
      .onDelete('CASCADE');



    table.boolean('status').notNullable().defaultTo(true);
    table.timestamps(true, true);  // created_at, updated_at
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable('imf_branches');
};
