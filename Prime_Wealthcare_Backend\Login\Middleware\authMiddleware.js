const jwt = require('jsonwebtoken');
const SECRET_KEY = process.env.SECRET_KEY;

// Ensure SECRET_KEY is set
if (!SECRET_KEY) {
    throw new Error('SECRET_KEY environment variable is not set');
}

const authenticateToken = (req, res, next) => {

    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) return res.status(401).json({ message: 'Access denied, no token provided' });

    jwt.verify(token, SECRET_KEY, (err, user) => {
        if (err) {
            console.error('Token verification failed:', err); // Log verification error
            return res.sendStatus(403); // Forbidden
        }
        req.user = user; // Attach user info to request
        next(); // Proceed to the next middleware or route handler
    });
};

module.exports = authenticateToken;
