import React, { useEffect, useState } from 'react';
import { Box, Container, Grid, Typo<PERSON>, Button, AppBar, Toolbar, TableContainer, Table, TableHead, TableBody, TableRow, TableCell, Paper, Chip } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import ModuleName from '../../../components/table/ModuleName';
import ExportToPdfQuickQuotation from '../../../components/ExportToPdfQuickQuotation';

const QuickQuotationList = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { quotationData } = location.state || {};

    const customerInfo = {
        companyName: quotationData?.policyDetails?.company_name || 'N/A',
        productType: quotationData?.policyDetails?.product || 'N/A',
        familyType: quotationData?.policyDetails?.policyType || 'N/A',
        duration:quotationData?.policyDetails?.duration || 'N/A',
       // product_master_name: quotationData?.policyDetails?.product || 'N/A'
    };

    const coverType = quotationData?.policyDetails?.cover_type || 'N/A';
    const outputResponse = quotationData?.results?.outputResponse || {};
    const members = quotationData?.results?.premiums || [];
    const totalFamilyPremium = outputResponse?.premiumAmt || '0';
    const totalFamilyServiceTax = outputResponse?.serviceTax || '0';
    const totalFamilyWithServiceTax = outputResponse?.premWithServiceTax || '0';
    const copayAmount = outputResponse?.coPayAmount || '0';
    const copayPercentage = outputResponse?.coPayPercentage || '0';
    const results = quotationData?.results || []; // Extract results from quotationData

    const [isLoggedIn, setIsLoggedIn] = useState(false);

    useEffect(() => {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        setIsLoggedIn(!!token);
    }, []);

    // Helper function to format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            maximumFractionDigits: 2
        }).format(amount);
    };

    return (
        <Box>

            <Container maxWidth="xl" sx={{ pb: 4 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Grid container spacing={2} sx={{ display: 'flex', justifyContent: 'space-between', paddingInline: '1rem' }}>
                        <Grid item xs={12} md={8} sx={{ display: 'flex', alignItems: 'center' }}>
                            <img
                                src="/image.png"
                                alt="module icon"
                                style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: '#528a7e' }}
                            />
                            <ModuleName moduleName="Quick Quotation" pageName="Overview" />
                        </Grid>

                        <Grid item xs={12} md={4} sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            alignItems: 'center',
                            gap: 2
                        }}>
                            
                            {/* Modify the ExportToPdfQuickQuotation component usage */}
                            {isLoggedIn && quotationData && (
                                <ExportToPdfQuickQuotation 
                                    quotationData={quotationData}
                                />
                            )}
                            
                            <Button
                             onClick={() => navigate(isLoggedIn ? '/dashboard/quick-quotation' : '/quick-quotation')}
                             variant="contained"
                                sx={{
                                    backgroundColor: '#528a7e',
                                }}
                            >
                                Create New
                            </Button>
                            {!isLoggedIn && (
                                <Button
                                    variant="contained"
                                    onClick={() => navigate('/')}
                                    sx={{
                                        backgroundColor: '#528A7E',
                                        color: 'white',
                                        '&:hover': {
                                            backgroundColor: '#397c63',
                                        },
                                        textTransform: 'none',
                                        fontWeight: 'bold',
                                    }}
                                >
                                    Login
                                </Button>
                            )}
                        </Grid>
                    </Grid>
                    {/* Customer Information Grid */}
                    <Box display="flex" alignItems="center" p={2} sx={{ borderBlock: '1px solid #e0e0e0', mb: 4 }}>
                        <Grid container spacing={2}>
                            {Object.entries(customerInfo)?.map(([key, value]) => (
                                <Grid item xs={12} md={4} key={key}>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                        <Typography variant="subtitle2" color="textSecondary" sx={{ minWidth: '80px' }}>
                                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:
                                        </Typography>
                                        <Typography variant="body1" fontWeight="medium">
                                            {value}
                                        </Typography>
                                    </Box>
                                </Grid>
                            ))}
                        </Grid>
                    </Box> 

                    {/* EMI Details Section for Accident Suraksha */}
                    {customerInfo.productType === 'AccidentSuraksha' && results?.emiDetails && (
                        <Box sx={{ mt: 0}}>
                            <Typography variant="h5" gutterBottom sx={{ mb: 3,textAlign: 'center' }}>Payment Options</Typography>
                            <Grid container spacing={3}>
                                {results.emiDetails.map((emi, index) => (
                                    <Grid item xs={12} md={4} key={index}>
                                        <Box
                                            sx={{
                                                p: 3,
                                                border: '1px solid #e0e0e0',
                                                borderRadius: 2,
                                                backgroundColor: '#fff',
                                                boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                                                height: '100%'
                                            }}
                                        >
                                            <Typography 
                                                variant="h6" 
                                                gutterBottom 
                                                sx={{ 
                                                    color: '#528a7e',
                                                    borderBottom: '2px solid #528a7e',
                                                    pb: 1,
                                                    mb: 2 
                                                }}
                                            >
                                                {emi.duration} Year Plan
                                            </Typography>

                                            {/* Premium Details */}
                                            <Box sx={{ mb: 3 }}>
                                                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                                                    Premium Breakdown
                                                </Typography>
                                                <Grid container spacing={1}>
                                                    <Grid item xs={12}>
                                                        <Box sx={{ 
                                                            display: 'flex', 
                                                            justifyContent: 'space-between',
                                                            py: 0.5
                                                        }}>
                                                            <Typography color="text.secondary">Base Premium:</Typography>
                                                            <Typography>{formatCurrency(emi.fullPayment.premium)}</Typography>
                                                        </Box>
                                                    </Grid>
                                                    <Grid item xs={12}>
                                                        <Box sx={{ 
                                                            display: 'flex', 
                                                            justifyContent: 'space-between',
                                                            py: 0.5
                                                        }}>
                                                            <Typography color="text.secondary">Service Tax:</Typography>
                                                            <Typography>{formatCurrency(emi.fullPayment.tax)}</Typography>
                                                        </Box>
                                                    </Grid>
                                                </Grid>
                                            </Box>

                                            {/* Discounts Section */}
                                            <Box sx={{ mb: 3 }}> 
                                                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                                                    Applied Discounts
                                                </Typography>
                                                
                                                {/* Family Discount */}
                                                {emi.discounts.family.percentage > 0 && (
                                                    <Box sx={{ 
                                                        display: 'flex', 
                                                        justifyContent: 'space-between',
                                                        py: 0.5
                                                    }}>
                                                        <Typography color="text.secondary">
                                                            Family Discount ({emi.discounts.family.percentage}%):
                                                        </Typography>
                                                        <Typography color="red">
                                                            (-{formatCurrency(emi.discounts.family.amount)})
                                                        </Typography>
                                                    </Box>
                                                )}

                                                {/* Long Term Discount */}
                                                {emi.discounts.longTerm.percentage > 0 && (
                                                    <Box sx={{ 
                                                        display: 'flex', 
                                                        justifyContent: 'space-between',
                                                        py: 0.5
                                                    }}>
                                                        <Typography color="text.secondary">
                                                            Long Term Discount ({emi.discounts.longTerm.percentage}%):
                                                        </Typography>
                                                        <Typography color="red">
                                                            (-{formatCurrency(emi.discounts.longTerm.amount)})
                                                        </Typography>
                                                    </Box>
                                                )}
                                            </Box>

                                            {/* Total Amount */}
                                            <Box sx={{ 
                                                mt: 2, 
                                                pt: 2, 
                                                borderTop: '2px solid #e0e0e0'
                                            }}>
                                                <Box sx={{ 
                                                    display: 'flex', 
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center'
                                                }}>
                                                    <Typography variant="subtitle1" fontWeight="bold">
                                                        Final Amount:
                                                    </Typography>
                                                    <Typography 
                                                        variant="h6" 
                                                        color='white' 
                                                        fontWeight="bold"
                                                        bgcolor='#528a7e'
                                                        borderRadius={1}
                                                        px={1}
                                                    >
                                                        {formatCurrency(emi.fullPayment.totalAmount)}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        </Box>
                                    </Grid>
                                ))}
                            </Grid>
                        </Box>
                    )}

                    {/* EMI Details Section */}
                    {results?.[0]?.emiDetails && (
                        <Box sx={{ mt: 4 }}>
                            <Typography variant="h6" gutterBottom>EMI Options</Typography>
                            <Grid container spacing={3}>
                                {results[0].emiDetails.map((emi, index) => (
                                    <Grid item xs={12} md={4} key={index}>
                                        <Box
                                            sx={{
                                                p: 2,
                                                border: '1px solid #e0e0e0',
                                                borderRadius: 1,
                                                backgroundColor: '#f9f9f9'
                                            }}
                                        >
                                            <Typography variant="h6" gutterBottom>
                                                {emi.duration} Year Plan
                                            </Typography>
                                            
                                            {/* Full Payment Details */}
                                            <Box sx={{ mb: 2 }}>
                                                <Typography variant="subtitle2" color="textSecondary">
                                                    Full Payment:
                                                </Typography>
                                                <Grid container spacing={1}>
                                                    <Grid item xs={12}>
                                                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                                            <Typography>Premium:</Typography>
                                                            <Typography>₹{emi.fullPayment.premium.toLocaleString()}</Typography>
                                                        </Box>
                                                    </Grid>
                                                    <Grid item xs={12}>
                                                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                                            <Typography>Tax:</Typography>
                                                            <Typography>₹{emi.fullPayment.tax.toLocaleString()}</Typography>
                                                        </Box>
                                                    </Grid>
                                                    <Grid item xs={12}>
                                                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                                            <Typography fontWeight="bold">Total Amount:</Typography>
                                                            <Typography fontWeight="bold" color="primary">
                                                                ₹{emi.fullPayment.totalAmount.toLocaleString()}
                                                            </Typography>
                                                        </Box>
                                                    </Grid>
                                                </Grid>
                                            </Box>

                                            {/* Discounts */}
                                            <Box>
                                                <Typography variant="subtitle2" color="textSecondary">
                                                    Applicable Discounts:
                                                </Typography>
                                                <Grid container spacing={1}>
                                                    {/* Family Discount */}
                                                    {emi.discounts.family.percentage > 0 && (
                                                        <Grid item xs={12}>
                                                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                                                <Typography>Family Discount ({emi.discounts.family.percentage}%):</Typography>
                                                                <Typography color="success.main">
                                                                    -₹{emi.discounts.family.amount.toLocaleString()}
                                                                </Typography>
                                                            </Box>
                                                        </Grid>
                                                    )}
                                                    
                                                    {/* Long Term Discount */}
                                                    {emi.discounts.longTerm.percentage > 0 && (
                                                        <Grid item xs={12}>
                                                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                                                <Typography>Long Term Discount ({emi.discounts.longTerm.percentage}%):</Typography>
                                                                <Typography color="success.main">
                                                                    -₹{emi.discounts.longTerm.amount.toLocaleString()}
                                                                </Typography>
                                                            </Box>
                                                        </Grid>
                                                    )}
                                                </Grid>
                                            </Box>
                                        </Box>
                                    </Grid>
                                ))}
                            </Grid>
                        </Box>
                    )}

                    {/* Individual Member Boxes - Hide for FG Accident Suraksha */}
                    {customerInfo.familyType === 'individual' && customerInfo.productType !== 'AccidentSuraksha' && (
                        <>
                            {/* Total Family Display */}
                            <Box sx={{ mt: 4, p: 3, border: '1px solid #e0e0e0', borderRadius: '8px', backgroundColor: '#f9f9f9', width: '70%', margin: '0 auto' }}>
                                <Typography variant="h6" align="left">Total Family Premium:</Typography>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} md={4}>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Premium:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                ₹{parseFloat(totalFamilyPremium).toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Service Tax:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                ₹{parseFloat(totalFamilyServiceTax).toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} md={4}>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Typography variant="subtitle2" color="textSecondary">
                                                Total Premium with Service Tax:
                                            </Typography>
                                            <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                ₹{parseFloat(totalFamilyWithServiceTax).toLocaleString()}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    {customerInfo.productType === 'FG VARISHTHA BIMA' && (
                                        <>
                                            <Grid item xs={12} md={4}>
                                                <Box sx={{ display: 'flex', gap: 1 }}>
                                                    <Typography variant="subtitle2" color="textSecondary">
                                                        Copay Percentage:
                                                    </Typography>
                                                    <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                        {copayPercentage}%
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                            <Grid item xs={12} md={4}>
                                                <Box sx={{ display: 'flex', gap: 1 }}>
                                                    <Typography variant="subtitle2" color="textSecondary">
                                                        Copay Amount:
                                                    </Typography>
                                                    <Typography variant="body1" fontWeight="bold" sx={{ color: 'green' }}>
                                                        ₹{parseFloat(copayAmount).toLocaleString()}
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                        </>
                                    )}
                                </Grid>
                            </Box>

                            {/* Individual Member Boxes */}
                            <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                                <Box sx={{ width: '70%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                                    {members?.map((member, index) => {
                                        const memberCoverType = member.coverType; // Assuming these fields exist in the member object
                                        const memberRelation = member.relation;
                                        const memberSumInsured = member.sumInsured;
                                        const memberPremium = member.basePremium; // Assuming basePremium is the correct field

                                        return (
                                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Box
                                                    sx={{
                                                        width: '100%',
                                                        padding: '20px',
                                                        borderRadius: '10px',
                                                        border: '1px solid #e0e0e0',
                                                        position: 'relative'
                                                    }}
                                                >
                                                    <Grid container spacing={2} alignItems="center">
                                                        {/* Cover Type */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                Cover Type:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold">
                                                                {memberCoverType}
                                                            </Typography>
                                                        </Grid>

                                                        {/* Relation */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                Relation:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold">
                                                                {memberRelation}
                                                            </Typography>
                                                        </Grid>

                                                        {/* Sum Insured */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                Sum Insured:
                                                            </Typography>
                                                            <Typography variant="body1" fontWeight="bold">
                                                                ₹{parseInt(memberSumInsured).toLocaleString()}
                                                            </Typography>
                                                        </Grid>

                                                        {/* Premium Amount */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">
                                                                Premium Amount:
                                                            </Typography>
                                                            <Typography
                                                                variant="body1"
                                                                fontWeight="bold"
                                                                sx={{
                                                                    px: 2,
                                                                    py: 0.5,
                                                                    borderRadius: 1
                                                                }}
                                                            >
                                                                ₹{parseFloat(memberPremium).toLocaleString()}
                                                            </Typography>
                                                        </Grid>
                                                    </Grid>
                                                </Box>
                                            </Box>
                                        );
                                    })}
                                </Box>
                            </Box>
                        </>
                    )}

                    {/* Plan Boxes for Floater */}
                    {customerInfo.familyType === 'floater' && (
                        <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                            <Box sx={{ width: '70%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                                {results?.map((result, index) => {
                                    const { outputResponse, premiums, sumInsured } = result;

                                    return (
                                        <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Box
                                                sx={{
                                                    width: '100%',
                                                    padding: '20px',
                                                    borderRadius: '10px',
                                                    border: '1px solid #e0e0e0',
                                                    position: 'relative',
                                                }}
                                            >
                                                {/* Total Family Premium Box */}
                                                <Box sx={{ backgroundColor: '#f9f9f9', padding: '15px', borderRadius: '8px', mb: 2 }}>
                                                    <Typography variant="h6" align="left">Total Family Premium:</Typography>
                                                    <Grid container spacing={2} alignItems="center">
                                                        {/* Cover Type */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">Sum Insured:</Typography>
                                                            <Typography variant="body1" fontWeight="bold">₹{parseInt(sumInsured).toLocaleString()}</Typography>
                                                        </Grid>

                                                        {/* Premium Amount */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">Total Premium Amount:</Typography>
                                                            <Typography variant="body1" fontWeight="bold">₹{parseFloat(outputResponse?.premiumAmt).toLocaleString()}</Typography>
                                                        </Grid>

                                                        {/* Service Tax */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">Service Tax:</Typography>
                                                            <Typography variant="body1" fontWeight="bold">₹{parseFloat(outputResponse?.serviceTax).toLocaleString()}</Typography>
                                                        </Grid>

                                                        {/* Total Premium with Service Tax */}
                                                        <Grid item xs={12} md={3}>
                                                            <Typography variant="subtitle2" color="textSecondary">Total Prem with Service Tax:</Typography>
                                                            <Typography variant="body1" fontWeight="bold">₹{parseFloat(outputResponse?.premWithServiceTax).toLocaleString()}</Typography>
                                                        </Grid>

                                                        {/* Add Copay details for FG VARISHTHA BIMA */}
                                                        {customerInfo.productType === 'FG VARISHTHA BIMA' && (
                                                            <>
                                                                <Grid item xs={12} md={3}>
                                                                    <Typography variant="subtitle2" color="textSecondary">
                                                                        Copay Percentage:
                                                                    </Typography>
                                                                    <Typography variant="body1" fontWeight="bold">
                                                                        {outputResponse?.coPayPercentage || '0'}%
                                                                    </Typography>
                                                                </Grid>
                                                                <Grid item xs={12} md={3}>
                                                                    <Typography variant="subtitle2" color="textSecondary">
                                                                        Copay Amount:
                                                                    </Typography>
                                                                    <Typography variant="body1" fontWeight="bold">
                                                                        ₹{parseFloat(outputResponse?.coPayAmount || '0').toLocaleString()}
                                                                    </Typography>
                                                                </Grid>
                                                            </>
                                                        )}
                                                    </Grid>
                                                </Box>

                                                {/* Member Premiums Display */}
                                                <Box sx={{ mt: 2, backgroundColor: 'white', padding: '15px', borderRadius: '8px' }}>
                                                    <Typography variant="h6">Member Premiums:</Typography>
                                                    <hr style={{ margin: '10px 0', borderColor: '#e0e0e0' }} />
                                                    {premiums?.map((member, memberIndex) => (
                                                        <Box key={memberIndex} sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                                                            <Typography variant="body1">
                                                                Relation: {member.relation}
                                                            </Typography>
                                                            <Typography variant="body1">
                                                                Premium: <span style={{ color: 'green' }}>₹{parseFloat(member.basePremium).toLocaleString()}</span>
                                                            </Typography>
                                                            <Typography variant="body1">
                                                                CoverType: {member.coverType}
                                                            </Typography>
                                                        </Box>
                                                    ))}
                                                </Box>
                                            </Box>
                                        </Box>
                                    );
                                })}
                            </Box>
                        </Box>
                    )}
                </Box>
            </Container>
        </Box>
    );
};

export default QuickQuotationList;