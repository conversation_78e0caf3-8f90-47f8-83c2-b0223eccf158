const express = require('express');
const router = express.Router();
const { sendSOAPRequest } = require('../HealthTotal/soapService');
const { v4: uuidv4 } = require('uuid');

const calculateDOBFromAgeBand = (ageBand) => {
    const currentYear = new Date().getFullYear();
    const [minAge, maxAge] = ageBand.split('-').map(Number);
    const averageAge = maxAge ? (minAge + maxAge) / 2 : minAge;
    const birthYear = currentYear - averageAge;
    return new Date(birthYear, 0, 1).toISOString().split('T')[0];
};

const formatDateToDDMMYYYY = (date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};

// Define sum insured options based on cover types
const COVER_TYPE_SUM_INSURED = {
    'VITAL': [500000, 1000000],
    'SUPERIOR': [1500000, 2000000, 2500000],
    'PREMIUM': [5000000, 10000000]
};

// Add this helper function at the top
const calculatePolicyDates = (duration) => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // Start from tomorrow
    
    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + parseInt(duration)); // Add duration years
    endDate.setDate(endDate.getDate() - 1); // Subtract one day
    
    return {
        startDate: formatDateToDDMMYYYY(startDate),
        endDate: formatDateToDDMMYYYY(endDate)
    };
};

router.post('/healthtotal-floater-options', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;


        // Validate input
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Validate cover type
        if (!COVER_TYPE_SUM_INSURED[cover_type]) {
            console.error(`Invalid cover type: ${cover_type}`);
            throw new Error(`Invalid cover type: ${cover_type}`);
        }

        // Temporary storage for results
        const tempResults = [];

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;
        
        // Iterate through sum insured options
        const sumInsuredOptions = COVER_TYPE_SUM_INSURED[cover_type];

        // Calculate policy dates based on duration
        const policyDates = calculatePolicyDates(duration || '1');

        for (const sumInsured of sumInsuredOptions) {

            // Generate numeric UID
            const numericUid = uuidv4();

            const soapData = {
                Product: normalizedProductName,
                PolicyHeader: {
                    PolicyStartDate: policyDates.startDate,
                    PolicyEndDate: policyDates.endDate,
                    AgentCode: "60000272",
                    BranchCode: "10",
                    MajorClass: "FHA",
                    ContractType: "FHA",
                    METHOD: "ENQ",
                    PolicyIssueType: "I"
                },
                Uid: numericUid,
                VendorCode: "webagg",
                VendorUserId: "webagg",
                Client: {
                    ClientType: "I",
                    CreationType: "C",
                    Address1: {
                        Pincode: pincode,
                        Country: "IND"
                    }
                },
                BeneficiaryDetails: {
                    Member: members.map((member, index) => {
                        const insuredDob = formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand)));
                        return {
                            MemberId: (index + 1).toString(),
                            InsuredName: `Member ${index + 1}`,
                            InsuredDob: insuredDob,
                            InsuredGender: member.gender || 'M',
                            InsuredOccpn: "SVCM",
                            CoverType: cover_type,
                            SumInsured: sumInsured,
                            Relation: member.relation,
                            Height: "170",
                            Weight: "70",
                            NomineeName: "Test Nominee",
                            NomineeRelation: "FATH",
                            NomineeAge: "45"
                        };
                    })
                },
                Risk: {
                    PolicyType: family_type === 'individual' ? 'HTI' : 'HTF',
                    Duration: duration || "1",
                    Installments: "FULL",
                    PaymentType: "CC"
                }
            };

            try {
                // Call the floater SOAP service
                const soapResponse = await sendSOAPRequest(soapData);
                // Check if the response contains an error
        if (soapResponse?.status === "error" || soapResponse?.policyDetails?.Root?.Status?.[0] === "Fail") {
            const errorMessage = soapResponse?.policyDetails?.Root?.ValidationError?.[0] || 
                                soapResponse?.message || 
                                "Unknown error occurred";
            
            return res.status(400).json({
                status: "error",
                message: errorMessage,
                timestamp: new Date().toISOString()
            });
        }

        // Only attempt to process the response if we have valid data
        const outputRes = soapResponse?.policyDetails?.Root?.Policy?.[0]?.OutputRes?.[0];
        
        // If outputRes is undefined, something is wrong with the response structure
        if (!outputRes) {
            return res.status(400).json({
                status: "error",
                message: "Invalid response structure from insurance provider",
                timestamp: new Date().toISOString(),
                rawResponse: soapResponse
            });
        }

                // Extract relevant data from the SOAP response
                //const outputRes = soapResponse?.policyDetails?.Root?.Policy[0]?.OutputRes[0]; // Access OutputRes
                const memberResults = soapResponse?.policyDetails?.Root?.Policy[0]?.InputParameters?.[0]?.BeneficiaryDetails[0]?.Member.map((member) => ({
                    memberId: member.MemberId[0],
                    coverType: member.CoverType[0],
                    sumInsured: member.SumInsured[0], // Ensure SumInsured is accessed correctly
                    basePremium: member.PerPrsnPremium[0],
                    relation: member.Relation[0],
                })) || []; // Handle member results extraction
                const outputResponseData = {
                    premiumAmt: outputRes.PremiumAmt[0],
                    serviceTax: outputRes.ServiceTax[0],
                    premWithServiceTax: outputRes.PremWithServTax[0],
                    // Add any other fields from outputRes that you want to store
                };
                
                // Push the results into tempResults
                tempResults.push({
                    sumInsured: sumInsured,
                    requestId: numericUid,
                    premiums: memberResults,
                    outputResponse: outputResponseData, // Store output response fields here
                    rawResponse: soapResponse
                });
            } catch (soapError) {
                console.error(`Error for sum insured ${sumInsured}:`, soapError);
                tempResults.push({
                    sumInsured: sumInsured,
                    error: soapError.message || 'Unknown error'
                });
            }
        }
        // Respond with the temporary results
        res.json({
            status: 'success',
            coverType: cover_type,
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName, // Added product to the response
                company_name: company_name,
                duration:duration
            },
            results: tempResults // Return all results after processing
        });

    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ 
            status: "error", 
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.post('/healthtotalcreate', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;

        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Generate numeric UID
        const numericUid = uuidv4();

        // Calculate policy dates based on duration
        const policyDates = calculatePolicyDates(duration || '1');

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
            // Add other mappings as needed
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name; // Default to the original name if not found

        // Map PolicyType based on family_type
        const policyType = family_type === 'individual' ? 'HTI' : 'HTF'; // HTI for individual, HTF for floater

        const soapData = {
            Product: normalizedProductName, // Use the normalized product name
            PolicyHeader: {
                PolicyStartDate: policyDates.startDate,
                PolicyEndDate: policyDates.endDate,
                AgentCode: "60000272",
                BranchCode: "10",
                MajorClass: "FHA",
                ContractType: "FHA",
                METHOD: "ENQ",
                PolicyIssueType: "I"
            },
            Uid: numericUid,
            VendorCode: "webagg",
            VendorUserId: "webagg",
            Client: {
                ClientType: "I",
                CreationType: "C",
                Address1: {
                    Pincode: pincode,
                    Country: "IND"
                }
            },
            BeneficiaryDetails: {
                Member: members.map((member, index) => ({
                    MemberId: (index + 1).toString(),
                    InsuredName: `Member ${index + 1}`,
                    InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                    InsuredGender: member.gender || 'M',
                    InsuredOccpn: "SVCM",
                    CoverType: cover_type,
                    SumInsured: member.sumInsured,
                    Relation: member.relation,
                    Height: "170",
                    Weight: "70",
                    NomineeName: "Test Nominee",
                    NomineeRelation: "FATH",
                    NomineeAge: "45"
                }))
            },
            Risk: {
                PolicyType: policyType,
                Duration: duration || "1",
                Installments: "FULL",
                PaymentType: "CC"
            }
        };

        const soapResponse = await sendSOAPRequest(soapData);

       // const outputRes = soapResponse?.policyDetails?.Root?.Policy[0]?.OutputRes[0]; // Access OutputRes
       
        // Check if the response contains an error
        if (soapResponse?.status === "error" || soapResponse?.policyDetails?.Root?.Status?.[0] === "Fail") {
            const errorMessage = soapResponse?.policyDetails?.Root?.ValidationError?.[0] || 
                                soapResponse?.message || 
                                "Unknown error occurred";
            
            return res.status(400).json({
                status: "error",
                message: errorMessage,
                timestamp: new Date().toISOString()
            });
        }

        // Only attempt to process the response if we have valid data
        const outputRes = soapResponse?.policyDetails?.Root?.Policy?.[0]?.OutputRes?.[0];
        
        // If outputRes is undefined, something is wrong with the response structure
        if (!outputRes) {
            return res.status(400).json({
                status: "error",
                message: "Invalid response structure from insurance provider",
                timestamp: new Date().toISOString(),
                rawResponse: soapResponse
            });
        }
        const memberResults = soapResponse?.policyDetails?.Root?.Policy[0]?.InputParameters?.[0]?.BeneficiaryDetails[0]?.Member.map((member) => ({
            memberId: member.MemberId[0],
            sumInsured: member.SumInsured[0], // Ensure SumInsured is accessed correctly
            basePremium: member.PerPrsnPremium[0],
            coverType: member.CoverType[0],
            relation: member.Relation[0],
        })) || []; // Handle member results extraction

        const outputResponseData = {
            premiumAmt: outputRes.PremiumAmt[0],
            serviceTax: outputRes.ServiceTax[0],
            premWithServiceTax: outputRes.PremWithServTax[0],
            // Add any other fields from outputRes that you want to store
        };

        const formattedResponse = {
            status: 'success',
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName, // Added product to the response
                company_name: company_name,
                duration:duration
            },
            results: {
                premiums: memberResults,
                outputResponse: outputResponseData
            },
            rawResponse: soapResponse
        };


        res.json(formattedResponse);
    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ 
            status: "error", 
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
