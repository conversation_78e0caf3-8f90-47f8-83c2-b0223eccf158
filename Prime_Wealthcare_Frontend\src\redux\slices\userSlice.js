import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { fetchAllUsers } from '../actions/action';

const userSlice = createSlice({
    name: 'users',
    initialState: {
        users: [],
        loading: false,
        error: null,
    },
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchAllUsers.pending, (state) => {
                state.loading = true;
            })
            .addCase(fetchAllUsers.fulfilled, (state, action) => {
                state.loading = false;
                state.users = action.payload.data; // Assuming the response structure has a data field
            })
            .addCase(fetchAllUsers.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload.message; // Handle error message
            });
    },
});

export default userSlice.reducer; 