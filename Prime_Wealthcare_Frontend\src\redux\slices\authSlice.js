import { createSlice } from '@reduxjs/toolkit';
import { login, changePassword, forgotPassword, verifyOTP, resetPassword, validateToken, logoutUser, fetchUserAccessRights } from '../actions/action';

const initialState = {
  token: sessionStorage.getItem('token') || localStorage.getItem('token') || null, // Retrieve token from sessionStorage or localStorage
  user: JSON.parse(sessionStorage.getItem('user') || localStorage.getItem('user') || 'null'),
  userId: null, // Add userId to store
  userType: null, // Add userType to store
  username: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  firstLogin: false,
  otpStatus: null,  // Track OTP sending status
  otpError: null,  // Track OTP errors
  email: null, // Add email field
  accessRights: [], // Add accessRights to the initial state
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // ... existing reducers ...
    logout: (state) => {
      state.user = null;
      state.userId = null; // Clear userId on logout
      state.userType = null; // Clear userType on logout
      state.isAuthenticated = false;
      state.firstLogin = false;
      state.token = null;  // Clear token on logout
      state.otpStatus = null;  // Clear OTP status on logout
      state.otpError = null;  // Clear OTP errors on logout
      state.email = null; // Clear email on logout
      state.accessRights = []; // Clear access rights on logout
      localStorage.removeItem('token'); // Clear token on logout
      sessionStorage.removeItem('token'); // Clear session token on logout
      sessionStorage.removeItem('user'); // Clear user data from sessionStorage
      localStorage.removeItem('user');
      state.username = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;  // Store the token from the login response
        state.isAuthenticated = true;
        state.userId = action.payload.userId; // Store userId from response
        state.userType = action.payload.userType; // Store userType from response

        if (action.payload.firstLogin) {
          state.firstLogin = true;
          state.userId = action.payload.userId;
          state.userType = action.payload.userType;
        } else {
          state.firstLogin = false;
        }
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        //state.error = action.payload?.message || 'Login failed';
        state.error = action.payload;
      })
      .addCase(changePassword.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.token;  // Store the token from the change password response
        state.isAuthenticated = true;
        // Ensure userId and userType are retained after password change
        state.userId = action.payload.userId || state.userId; // Retain userId if available in response
        state.userType = action.payload.userType || state.userType; // Retain userType if available in response
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Password change failed';
      })


      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.otpStatus = null;
        state.otpError = null;
      })
      .addCase(forgotPassword.fulfilled, (state, action) => {
        state.loading = false;
        state.otpStatus = action.payload.message; // Ensure the message is set here
        state.email = action.meta.arg.email;  // Store the email from forgotPassword action
        state.otpCode = action.payload.otp;  // Store the actual OTP code received from the backend (if returned)

      })

      .addCase(forgotPassword.rejected, (state, action) => {
        state.loading = false;
        state.otpError = action.payload;  // Store OTP error
      })

      // Verify OTP cases
      .addCase(verifyOTP.pending, (state) => {
        state.loading = true;
        state.otpError = null;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.loading = false;
        state.userId = action.payload.userId;  // Store userId from OTP verification
        state.userType = action.payload.userType;  // Store userType from OTP verification
        state.otpError = null;
        state.otpEntered = action.meta.arg.otp;  // Store the OTP entered by the user for verification

      })

      .addCase(verifyOTP.rejected, (state, action) => {
        state.loading = false;
        state.otpError = action.payload;  // Store OTP verification error
      })

      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
      })
      .addCase(resetPassword.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

    builder
      .addCase(validateToken.pending, (state) => {
        state.loading = true;
      })

      .addCase(validateToken.fulfilled, (state, action) => {
        state.user = {
          userId: action.payload.userId,
          userType: action.payload.userType,
          userName: action.payload.userName
        };

        //state.token = state.token; // Keep existing token since it's not in the response
        if (action.payload.token) {
          state.token = action.payload.token;
          localStorage.setItem('token', state.token); // Persist updated token
          sessionStorage.setItem('token', state.token);
          sessionStorage.setItem('displayName', state.userId);
          // Persist updated token
        }
        state.loading = false;
        state.isAuthenticated = true;
      })

      .addCase(validateToken.rejected, (state, action) => {
        console.error('Token validation failed:', action.payload); // Add logging here
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        state.token = null;
        state.isAuthenticated = false;
        state.error = action.payload;
        state.user = null; // Clear user information
      })

      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
      })
      .addCase(fetchUserAccessRights.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserAccessRights.fulfilled, (state, action) => {
        state.loading = false;
        state.accessRights = action.payload.data;
        state.error = null;
       
      })
      .addCase(fetchUserAccessRights.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message;
        // Don't clear accessRights on error to prevent UI flicker
      });
  },
});

export const { logout, resetError, setRedirecting } = authSlice.actions;
export default authSlice.reducer;
