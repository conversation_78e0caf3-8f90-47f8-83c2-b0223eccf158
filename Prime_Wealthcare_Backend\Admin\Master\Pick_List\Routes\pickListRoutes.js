// routes/pickListRoutes.js
const express = require('express');
const router = express.Router();
const pickListController = require('../Controllers/pickListController');

// Define routes
router.get('/', pickListController.getAllPickLists);
router.get('/:typeName', pickListController.getPickListsByTypeName);
router.post('/', pickListController.createPickList);
router.put('/:id', pickListController.updatePickList);
router.delete('/:id', pickListController.deletePickList);
router.post('/soft-delete/:id', pickListController.softDeletePickList);
router.post('/reinstate/:id', pickListController.reinstatePickList);

module.exports = router;
