exports.up = function (knex) {
    return knex.schema.hasTable('disease_master').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('disease_master', function (table) {
                table.increments('id').primary();
                table.string('disease_name', 255).notNullable().unique();
                table.string('disease_description', 512).notNullable();
                table.integer('created_by').notNullable().defaultTo(1);
                table.integer('updated_by').notNullable().defaultTo(1);
                table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable();
                table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable();
                table.boolean('status').notNullable().defaultTo(true);
            });
        }
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('disease_master');
};