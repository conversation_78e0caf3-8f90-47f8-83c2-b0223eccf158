const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');


const create = async (data) => {
    try {
        await db('customer_grouping').insert(data);

    } catch (error) {
        console.error('Error inserting Customer grouping :', error);
        throw error;
    }
};

const getAll = async () => {
    try {
        const customers = await db('customer_grouping').select('*');
        return customers;
    } catch (error) {
        console.error('Error retrieving customer grouping:', error);
        throw error;
    }
};

const findById = async (id) => {
    try {
        const customer = await db('customer_grouping').where({ id }).first();
        return customer;
    } catch (error) {
        throw error;
    }
};
const findByCustomerId = async (id) => {
    try {
        const customer = await db('customer_grouping').where('customer_id', id).select('*');
        return customer;
    } catch (error) {
        throw error;
    }
};

// Update Customer by ID
const update = async (id, customerData) => {
    if (!id) throw new Error("Customer grouping ID is required");

    try {
        customerData.updated_at = getCurrentTimestamp();

        const result = await db('customer_grouping').where('customer_id', id).update(customerData);
        if (result) {

        } else {
            console.error(`No Customer grouping found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating Customer grouping with ID: ${id}`, error);
        throw error;
    }
};
// Delete role by ID
const deleteById = async (id) => {
    try {
        const result = await db('customer_grouping').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    getAll,
    findById,
    update,
    deleteById,
    findByCustomerId
}