exports.up = function (knex) {
    return knex.schema.hasTable('proposals_pa').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('proposals_pa', (table) => {
                table.increments('id').primary();
                table.integer('quotation_id').unsigned().references('pa_quotation_id').inTable('pa_quotations').onDelete('CASCADE');
                table.integer('customer_id').notNullable();
                table.string('customer_salutation', 10).notNullable();
                table.string('quotation_number').nullable().unique(); // Quotation number

                table.string('ProposalNumber').references('ProposalNumber').inTable('payment_master').onDelete('CASCADE'); // Then create foreign key
                table.string('prev_proposal_number').references('ProposalNumber').inTable('payment_master').onDelete('CASCADE'); // Add foreign key reference
                table.integer('insurance_company').unsigned().references('id').inTable('insurance_company').onDelete('CASCADE');
                table.integer('insurance_branch').unsigned().references('id').inTable('insurance_branch').onDelete('CASCADE');
                table.integer('imf_code').unsigned();
                table.integer('imf_branch').unsigned().references('id').inTable('imf_branches').onDelete('CASCADE');
                table.integer('agent_code').unsigned().references('id').inTable('agents').onDelete('CASCADE'); // Agent code
                table.integer('product_type').unsigned().references('id').inTable('main_product').onDelete('CASCADE');
                table.integer('product_name').unsigned().references('id').inTable('product_master').onDelete('CASCADE');
                table.integer('sub_product').unsigned().references('id').inTable('sub_product').onDelete('CASCADE');
                table.string('member_type').notNullable();
                table.string('net_premium').notNullable(); // Net premium
                table.string('gst_amount').notNullable(); // GST amount
                table.string('gst_percentage').notNullable().defaultTo(18); // GST percentage
                table.string('family_discount_percentage').nullable(); // Family discount percentage
                table.string('family_discount_amount').nullable(); // Family discount amount
                table.string('long_term_discount_percentage').nullable(); // Long term discount percentage
                table.string('long_term_discount_amount').nullable(); // Long term discount amount
                table.string('total_premium').notNullable(); // Total premium
                table.integer('tenure').notNullable(); // Tenure
                table.string('start_date').nullable(); // Start date
                table.string('end_date').nullable(); // End date
                table.string('policy_issue_date').nullable(); // Policy issue date
                table.string('policy_pdf').nullable(); // Policy PDF
                table.string('proposal_Id').nullable(); // Proposal ID
                table.string('ckyc_number').nullable(); // CKYC number
                table.string('ckyc_link', 500).nullable(); // CKYC link
                table.string('ckyc_expiry_date').nullable(); // CKYC expiry date
                // proposal response
                table.string('client_id', 255).nullable(); // Client ID
                table.string('receipt_no', 255).nullable(); // Receipt No
                table.string('policy_number', 255).nullable(); // Policy number
                table.string('win_no', 255).nullable();  // Win No
                table.string('application_no', 255).nullable(); // Application No
                table.string('proposal_type').notNullable();

                // table.integer('payment_id').unsigned().references('id').inTable('payment_master');
                table.string('remarks', 255).nullable(); // Remarks
                table.string('status').notNullable(); // Status
                table.string('Created_by').notNullable(); // Created by
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now()); // Created timestamp
                table.string('Updated_by').nullable(); // Updated by
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now()); // Updated timestamp

            })

        }
    });
};

exports.down = function (knex) {
    return knex.schema.dropTableIfExists('proposals_pa');
};
