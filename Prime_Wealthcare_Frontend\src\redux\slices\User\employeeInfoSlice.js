import { createSlice } from '@reduxjs/toolkit';
import { createEmployeeInfo, fetchEmployeeData, fetchEmployeeById, reinstateEmployee, softDeleteImfEmployee, searchEmployee, fetchEmployeeByCriteria, getEmployeeByReportingManagerUserId, fetchEmployeeByUserId } from '../../actions/action';
import { toast } from 'react-toastify';

// Employee Info Slice
const employeeInfoSlice = createSlice({
  name: 'employeeInfo',
  initialState: {
    employees: [], // to store list of employees
    employeeDetails: null, // to store a single employee's details
    employeeDetail: null, // to store a single employee's details
    status: 'idle', // for fetching status
    error: null, // for error handling
  },
  reducers: {
    // Any additional synchronous actions can be defined here if needed
    clearEmployeeDetails(state) {
      state.employeeDetails = null;
      state.employeeDetail = null; // Clear the employee detail when needed
      state.employees = []; // Clear the employee details when needed
    },
  },
  extraReducers: (builder) => {
    // Handle fetchEmployeeData (fetching all employees)
    builder
      .addCase(fetchEmployeeData.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchEmployeeData.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.employees = action.payload;
      })
      .addCase(fetchEmployeeData.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      });

    // Handle fetchEmployeeById (fetching a specific employee by ID)
    builder
      .addCase(fetchEmployeeById.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchEmployeeById.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.employeeDetail = action.payload;
      })
      .addCase(fetchEmployeeById.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      });

    builder
      .addCase(createEmployeeInfo.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createEmployeeInfo.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.employees.push(action.payload);
        toast.success('Employee created successfully');
      })
      .addCase(createEmployeeInfo.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        //  toast.error('Failed to create employee');
      });
    builder
      .addCase(softDeleteImfEmployee.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(softDeleteImfEmployee.fulfilled, (state, action) => {
        const index = state.employees.findIndex(employee => employee.id === action.payload.id);
        if (index !== -1) {
          state.employees.splice(index, 1);
        }
        state.status = 'succeeded';
        //toast.success('Employee deleted successfully');
      })
      .addCase(softDeleteImfEmployee.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
        toast.error('Failed to delete employee');
      })

      // Search employee by name
      .addCase(searchEmployee.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchEmployee.fulfilled, (state, action) => {
        state.loading = false;
        state.employees = action.payload;
      })
      .addCase(searchEmployee.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
        //toast.error('Failed to find employee'); // Error toast
      })


      // Fetch Employee by criteria
      .addCase(fetchEmployeeByCriteria.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchEmployeeByCriteria.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.employees = action.payload; // Update with fetched data
      })
      .addCase(fetchEmployeeByCriteria.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      .addCase(getEmployeeByReportingManagerUserId.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getEmployeeByReportingManagerUserId.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.employeeDetails = action.payload;
      })
      .addCase(getEmployeeByReportingManagerUserId.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })
      // FETCH EMPLOYEE BY USER ID
      .addCase(fetchEmployeeByUserId.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchEmployeeByUserId.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.employeeDetails = action.payload;
      })
      .addCase(fetchEmployeeByUserId.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })



  },
});

// Export the synchronous actions, if any
export const { clearEmployeeDetails } = employeeInfoSlice.actions;

// Export the reducer to be included in the store
export default employeeInfoSlice.reducer;