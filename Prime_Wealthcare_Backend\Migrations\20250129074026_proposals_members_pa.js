exports.up = function (knex) {
    return knex.schema.hasTable('proposal_members_pa').then(function (exists) {
        if (!exists) {
            return knex.schema.createTable('proposal_members_pa', (table) => {
                table.increments('id').primary();
                table.integer('proposal_id').unsigned().references('id').inTable('proposals_pa').onDelete('CASCADE');
                table.integer('customer_member_id').unsigned().references('id').inTable('customer_member_info').onDelete('CASCADE').nullable();
                table.integer('member_id').nullable();
                table.string('relation').notNullable(); // Relation
                table.integer('occupation').unsigned().references('id').inTable('pick_list').onDelete('CASCADE');
                table.integer('annual_income').nullable();
                table.integer('ad_sum_insured').nullable();
                table.integer('pp_sum_insured').nullable();
                table.integer('pt_sum_insured').nullable();
                table.integer('tt_sum_insured').nullable();
                table.integer('lp_sum_insured').nullable();
                table.integer('ls_sum_insured').nullable();
                table.integer('me_sum_insured').nullable();
                table.integer('am_sum_insured').nullable();
                table.integer('bb_sum_insured').nullable();
                table.integer('rf_sum_insured').nullable();
                table.integer('aa_sum_insured').nullable();
                table.integer('cs_sum_insured').nullable();
                table.integer('hc_sum_insured').nullable();
                table.integer('ft_sum_insured').nullable();
                table.string('nominee_name').nullable(); // Nominee name
                table.string('nominee_gender').nullable(); // Nominee gender
                table.string('nominee_dob').nullable(); // Nominee date of birth
                table.integer('nominee_relation').nullable(); // Nominee relation
                table.string('appointee_name').nullable(); // Appointee name
                table.string('appointee_gender').nullable(); // Appointee gender
                table.string('appointee_dob').nullable(); // Appointee date of birth
                table.integer('appointee_relation').nullable(); // Appointee relation
                table.string('status').notNullable(); // Status
                table.string('Created_by').notNullable(); // Created by
                table.timestamp('Created_at').notNullable().defaultTo(knex.fn.now()); // Created timestamp
                table.integer('Updated_by').nullable(); // Updated by
                table.timestamp('Updated_at').nullable().defaultTo(knex.fn.now()); // Updated timestamp
            });
        }
    })
};
exports.down = function (knex) {
    return knex.schema.dropTableIfExists('proposal_members_pa');
};
