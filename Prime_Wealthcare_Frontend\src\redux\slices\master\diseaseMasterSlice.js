import { createSlice } from '@reduxjs/toolkit';
import {
    getAllDiseases,
    getDiseaseById,
    getDiseaseByName,
    createDisease,
    updateDisease,
    deleteDisease,
    reinstateDisease,
    getFilterDiseaseData,
} from '../../actions/action';
import { toast } from 'react-toastify'; // Assuming you're using react-toastify

const diseaseTypeSlice = createSlice({
    name: 'diseases',
    initialState: {
        diseases: [],
        disease: null,
        status: 'idle',
        error: null,
    },
    extraReducers: (builder) => {
        builder
            // Get All Diseases
            .addCase(getAllDiseases.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getAllDiseases.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.diseases = action.payload;
            })
            .addCase(getAllDiseases.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to fetch diseases');
            })

            // Get Disease By ID
            .addCase(getDiseaseById.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getDiseaseById.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.disease = action.payload;
            })
            .addCase(getDiseaseById.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to fetch disease details');
            })

            // Search Disease by Name
            .addCase(getDiseaseByName.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getDiseaseByName.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.diseases = action.payload;
            })
            .addCase(getDiseaseByName.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to find diseases');
            })

            // Create Disease
            .addCase(createDisease.pending, (state, action) => {
                state.status = 'pending';
            })
            .addCase(createDisease.fulfilled, (state, action) => {
                state.diseases.push(action.payload);
                toast.success('Disease created successfully');
            })
            .addCase(createDisease.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to create disease');
            })

            // Update Disease
            .addCase(updateDisease.pending, (state, action) => {
                state.status = 'pending';
            })
            .addCase(updateDisease.fulfilled, (state, action) => {
                const index = state.diseases.findIndex(disease => disease.id === action.payload.id);
                if (index !== -1) {
                    state.diseases[index] = action.payload;
                }
                toast.success('Disease updated successfully');
            })
            .addCase(updateDisease.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to update disease');
            })

            // Delete Disease
            .addCase(deleteDisease.pending, (state, action) => {
                state.status = 'pending';
            })
            .addCase(deleteDisease.fulfilled, (state, action) => {
                state.diseases = state.diseases.filter(disease => disease.id !== action.payload);
                toast.success('Disease deleted successfully');
            })
            .addCase(deleteDisease.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to delete disease');
            })

            // Reinstate Disease
            .addCase(reinstateDisease.pending, (state, action) => {
                state.loading = 'pending';
            })
            .addCase(reinstateDisease.fulfilled, (state, action) => {
                const index = state.diseases.findIndex(disease => disease.id === action.payload.id);
                if (index !== -1) {
                    state.diseases[index] = action.payload;
                }
                toast.success('Disease reinstated successfully');
            })
            .addCase(reinstateDisease.rejected, (state, action) => {
                state.error = action.payload;
                toast.error('Failed to reinstate disease');
            })

            // Filtered Results
            .addCase(getFilterDiseaseData.pending, (state, action) => {
                state.status = 'loading';
            })
            .addCase(getFilterDiseaseData.fulfilled, (state, action) => {
                state.status = 'succeeded';
                state.diseases = action.payload;
            })
            .addCase(getFilterDiseaseData.rejected, (state, action) => {
                state.status = 'failed';
                state.error = action.payload;
                toast.error('Failed to filter diseases');
            });
    },
});

export default diseaseTypeSlice.reducer;
