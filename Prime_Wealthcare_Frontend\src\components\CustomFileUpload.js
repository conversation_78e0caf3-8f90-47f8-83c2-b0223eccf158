import * as React from 'react';
import { useState, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import FormHelperText from '@mui/material/FormHelperText';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { Box } from '@mui/material';
import { pdfjs } from 'pdfjs-dist';
import FilePreviewModal from './FilePreviewModal';

const HiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: '100%',
    width: '100%',
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
});

export default function CustomFileUpload({
    label,
    helperText,
    disabled = false,
    onFileSelect,
    fileName = '',
    sx = {},
    fileType = 'document',
    reset = false,
    accept = '',
    name,
    error = false,
    isRequired,
    insertedFile = null,
    ...props
}) {
    const [selectedFileName, setSelectedFileName] = useState(fileName);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [filePreviewUrl, setFilePreviewUrl] = useState('');
    const [selectedFile, setSelectedFile] = useState(null);

    useEffect(() => {
        if (insertedFile) {
            if (!selectedFileName) {
                setSelectedFileName(`Uploaded ${insertedFile?.name}`);
            }
            setFilePreviewUrl(insertedFile?.url);
        } else if (insertedFile === null || insertedFile === undefined || insertedFile === '') {
            // Clear fields when insertedFile is null
            setSelectedFileName('');
            setFilePreviewUrl('');
            setSelectedFile(null);
        }
    }, [insertedFile]);

    useEffect(() => {
        setSelectedFileName(fileName);
    }, [fileName]);

    useEffect(() => {
        if (reset) {
            setSelectedFileName('');
            setFilePreviewUrl('');
        }
    }, [reset]);

    const handleFileChange = (event) => {
        if (event.target.files && event.target.files.length > 0) {
            const file = event.target.files[0];
            setSelectedFileName(file.name);
            setSelectedFile(file);

            if (file.type === 'application/pdf') {
                // Create Object URL for PDFs
                const url = URL.createObjectURL(file);
                setFilePreviewUrl(url);
            } else {
                // Use FileReader for images
                const reader = new FileReader();
                reader.onload = (e) => {
                    setFilePreviewUrl(e.target.result);
                };
                reader.readAsDataURL(file);
            }

            if (onFileSelect) {
                if (name) {
                    onFileSelect({
                        'image': file,
                        'section_name': name
                    });
                } else {
                    onFileSelect(file);
                }
            }
        }
    };

    // Function to determine if a URL is local
    const isLocalFileUrl = (url) => {
        // Handle case when url is an object with image and section_name
        if (url && typeof url === 'object' && 'section_name' in url) {
            return false; // Assuming these structured objects are local files
        }

        // Handle regular string URLs
        return url && (
            url.startsWith('blob:') ||
            url.startsWith('data:') ||
            (insertedFile && insertedFile.name && url !== insertedFile.url)
        );
    };

    // Function to extract actual URL from potentially complex URL objects
    const getActualFileUrl = (url) => {
        // Handle case when url is an object with image and section_name
        if (url && typeof url === 'object' && 'section_name' in url) {
            // Return the appropriate property based on your data structure
            return url.image?.url || url.image?.path || '';
        }

        // Handle case when url is empty but we have a selected file with a blob URL
        if ((!url || url === '') && selectedFile && selectedFileName.toLowerCase().endsWith('.pdf') && filePreviewUrl) {
            return filePreviewUrl;
        }

        return url; // Return as is if it's a simple string URL
    };

    const handleEyeClick = () => {
        setIsModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    // Clean up Object URL when component unmounts or when filePreviewUrl changes
    useEffect(() => {
        return () => {
            if (filePreviewUrl && typeof filePreviewUrl === 'string' && filePreviewUrl.startsWith('blob:')) {
                URL.revokeObjectURL(filePreviewUrl);
            }
        };
    }, [filePreviewUrl]);

    return (
        <div style={{ width: '100%', position: 'relative' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', position: 'relative' }}>
                <Button
                    component="label"
                    variant="outlined"
                    startIcon={<CloudUploadIcon />}
                    sx={{
                        width: '100%',
                        height: '56px',
                        padding: '16.5px 14px',
                        textTransform: 'none',
                        justifyContent: 'flex-start',
                        fontSize: '16px',
                        color: 'grey',
                        borderColor: error ? 'red' : 'grey',
                        backgroundColor: disabled ? '#e0e0e0' : 'transparent',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:before': {
                            content: '""',
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            bottom: 0,
                            width: '3px',
                            backgroundColor: isRequired ? 'red' : 'transparent',
                        },
                        '&:hover': {
                            borderColor: error ? 'red' : 'grey',
                        },
                        '& .MuiButton-startIcon': {
                            marginRight: '8px',
                        },
                        ...sx,
                    }}
                    disabled={disabled}
                >
                    {selectedFileName || label}
                    <HiddenInput
                        type="file"
                        onChange={handleFileChange}
                        accept={accept}
                        disabled={disabled}
                        {...props}
                    />
                </Button>
                {selectedFileName && (
                    <VisibilityIcon
                        onClick={handleEyeClick}
                        style={{
                            position: 'absolute',
                            right: '10px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            cursor: 'pointer',
                            color: 'grey',
                        }}
                    />
                )}
            </Box>
            {(helperText) && (
                <FormHelperText
                    sx={{
                        color: error ? 'red' : 'green',
                    }}
                >
                    {error ? helperText : !selectedFileName ? helperText : label?.split(' ').slice(1).join(' ') + ' selected successfully'}
                </FormHelperText>
            )}
            <FilePreviewModal
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                fileUrl={getActualFileUrl(filePreviewUrl)}
                fileName={selectedFileName}
                isLocalFile={isLocalFileUrl(filePreviewUrl)}
                rawFileData={selectedFile ? filePreviewUrl : null}
            />
        </div>
    );
}
