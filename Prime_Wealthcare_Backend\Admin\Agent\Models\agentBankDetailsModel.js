const e = require('cors');
const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');

// Create Agent Bank Details
const create = async (data) => {
    try {
        return await db('agent_bank_details').insert(data);
    } catch (error) {
        console.error('Error inserting Agent BankDetails:', error);
        throw error;
    }
};

// Get All Agent Bank Details
const getAll = async () => {
    try {
        const agents = await db('agent_bank_details').select('*');
        return agents;
    } catch (error) {
        console.error('Error retrieving Agent BankDetails:', error);
        throw error;
    }
};

// Find Agent Bank Details by ID
const findById = async (id) => {
    try {
        const agent = await db('agent_bank_details').where('agent_id', id).first();
        return agent;
    } catch (error) {
        throw error;
    }
};

// Find Agent Bank Details by Agent ID
const findByAgentId = async (agentId) => {
    try {
        const agent = await db('agent_bank_details')
            .leftJoin('bank_list as bank1', 'agent_bank_details.bank_name', '=', 'bank1.id')
            .leftJoin('bank_list as bank2', 'agent_bank_details.bank_name_2', '=', 'bank2.id')
            .join('agents', 'agent_bank_details.agent_id', '=', 'agents.id')
            .where({ 'agent_bank_details.agent_id': agentId })
            .select(
                'agents.agent_id as user_id',
                'agents.full_name as agent_name',
                'agent_bank_details.*',
                db.raw('COALESCE(bank1.label_name, bank2.label_name) as bank_name')
            );

        agent.forEach(record => {
            record.created_at = formatDate(record.created_at);
            record.updated_at = formatDate(record.updated_at);
            record.status = record.status ? 'Active' : 'Inactive';
        });

        return agent;
    } catch (error) {
        throw error;
    }
};



// Helper function to format date
const formatDate = (date) => {
    const d = new Date(date);
    return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
};

// Update Agent Bank Details by ID
const update = async (id, agentData) => {
    if (!id) throw new Error("Agent BankDetails ID is required");

    try {
        agentData.updated_at = getCurrentTimestamp();
        const result = await db('agent_bank_details').where('agent_id', id).update(agentData);
        if (!result) {
            console.error(`No Agent BankDetails found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating Agent BankDetails with ID: ${id}`, error);
        throw error;
    }
};

// Delete Agent Bank Details by ID
const deleteById = async (id) => {
    try {
        const result = await db('agent_bank_details').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

// Delete First Bank Details by ID
const deleteFirstBankById = async (id) => {
    try {
        const agent = await db('agent_bank_details').where('agent_id', id).first();
        if (agent.account_holder_name_2) {
            const result = await db('agent_bank_details').where('agent_id', id).update({
                account_holder_name: null,
                bank_name: null,
                branch_name: null,
                account_number: null,
                IFSC_code: null,
                account_Type: null,
                canceled_cheque: null,
                is_active_bank: 'second',
                updated_at: getCurrentTimestamp()
            });
            return result;
        }
        else {
            const result = await db('agent_bank_details').where('agent_id', id).del();
            return result;
        }

    } catch (error) {
        throw error;
    }
};

// Delete Second Bank Details by ID
const deleteSecondBankById = async (id) => {
    try {
        const agent = await db('agent_bank_details').where('agent_id', id).first();

        if (agent.account_holder_name) {
            const result = await db('agent_bank_details').where('agent_id', id).update({
                account_holder_name_2: null,
                bank_name_2: null,
                branch_name_2: null,
                account_number_2: null,
                IFSC_code_2: null,
                account_Type_2: null,
                canceled_cheque_2: null,
                is_active_bank: 'first',
                updated_at: getCurrentTimestamp()
            });
            return result;
        }
        else {
            const result = await db('agent_bank_details').where('agent_id', id).del();
            return result;
        }
    } catch (error) {
        throw error;
    }
};


module.exports = {
    create,
    getAll,
    findById,
    findByAgentId,
    update,
    deleteById,
    deleteFirstBankById,
    deleteSecondBankById
};
