const express = require('express');
const router = express.Router();
const { sendSOAPRequest } = require('../Health Suraksha/healthSurakshaSoapService');
const { v4: uuidv4 } = require('uuid');
const { formatDateToDDMMYYYY, calculateDOBFromAgeBand } = require('../../../Reusable/reusable');

// Add this helper function to calculate policy dates
const calculatePolicyDates = (duration) => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // Start from tomorrow

    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + parseInt(duration)); // Add duration years
    endDate.setDate(endDate.getDate() - 1); // Subtract one day

    return {
        startDate: formatDateToDDMMYYYY(startDate),
        endDate: formatDateToDDMMYYYY(endDate)
    };
}

// Define sum insured options based on cover types
const COVER_TYPE_SUM_INSURED = {
    'GOLD': [500000],
    'PLATINUM': [600000, 750000, 800000, 900000, 1000000],
    'TOPAZ': [100000, 200000, 300000, 400000, 500000],
    'RUBY': [600000, 750000, 1000000]
};

router.post('/healthsurakshacreate', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Generate numeric UID
        const numericUid = uuidv4();

        // Calculate policy dates based on duration
        const { startDate: policyStartDate, endDate: policyEndDate } = calculatePolicyDates(duration || '1');

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;

        const policyType = family_type === 'individual' ? 'FHI' : 'FHF'; // HAI for individual, HAF for floater

        const soapData = {
            Product: normalizedProductName,
            PolicyHeader: {
                PolicyStartDate: policyStartDate,
                PolicyEndDate: policyEndDate,
            },
            Uid: numericUid,
            VendorCode: "webagg",
            VendorUserId: "webagg",
            Client: {
                ClientType: "I",
                CreationType: "C",
                Address1: {
                    Pincode: pincode,
                    Country: "IND"
                }
            },
            BeneficiaryDetails: {
                Member: members.map((member, index) => ({
                    MemberId: (index + 1).toString(),
                    InsuredName: `Member ${index + 1}`,
                    InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                    InsuredGender: member.gender || 'M',
                    InsuredOccpn: "SVCM",
                    CoverType: cover_type,
                    SumInsured: member.sumInsured,
                    Relation: member.relation,
                    Height: "170",
                    Weight: "70",
                    Smoking: "N",
                    Tobacco: "N",
                    IsGoodHealth: "Y",
                    IsExistingAbsolutePolicy: "N",
                    NomineeName: "Test Nominee",
                    NomineeRelation: "FATH",
                    NomineeAge: "45"
                }))
            },
            Risk: {
                PolicyType: policyType,
                Duration: duration || "1", // Use provided duration or default to 1
                Installments: "FULL",
                PaymentType: "CC"
            }
        };

        const soapResponse = await sendSOAPRequest(soapData);
        console.log('Raw SOAP Response:', soapResponse);

        // Safely extract data from the response
        const policy = soapResponse?.policyDetails?.Root?.Policy?.[0];
        if (!policy) {
            throw new Error('Invalid response structure: Policy data not found');
        }

        const outputRes = policy.OutputRes?.[0];
        const inputParams = policy.InputParameters?.[0];

        // Safely extract member details
        const memberResults = inputParams?.BeneficiaryDetails?.[0]?.Member?.map(member => ({
            memberId: member.MemberId?.[0] || '',
            sumInsured: member.SumInsured?.[0] || '',
            basePremium: member.BeneBasePremium?.[0] || member.PerPrsnPremium?.[0] || '0',
            coverType: member.CoverType?.[0] || '',
            relation: member.Relation?.[0] || ''
        })) || [];

        // Safely extract output response data
        const outputResponseData = {
            premiumAmt: outputRes?.PremiumAmt?.[0] || '0',
            serviceTax: outputRes?.ServiceTax?.[0] || '0',
            premWithServiceTax: outputRes?.PremWithServTax?.[0] || '0'
        };

        const formattedResponse = {
            status: 'success',
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                product: normalizedProductName,
                company_name: company_name,
                duration: duration
            },
            results: {
                premiums: memberResults,
                outputResponse: outputResponseData
            }
        };

        res.json(formattedResponse);
    } catch (error) {
        console.error('Error processing request:', error);
        // Send a more detailed error response
        res.status(500).json({
            error: error.message,
            details: 'Error processing SOAP response'
        });
    }
});

// New route for healthabsolute-floater-options
router.post('/healthsuraksha-floater-options', async (req, res) => {
    try {
        const { company_name, members, family_type, cover_type, pincode, product_master_name, duration } = req.body;
        // Validate input
        if (!members || !Array.isArray(members)) {
            throw new Error('Members data is missing or not an array');
        }

        // Validate cover type
        if (!COVER_TYPE_SUM_INSURED[cover_type]) {
            console.error(`Invalid cover type: ${cover_type}`);
            throw new Error(`Invalid cover type: ${cover_type}`);
        }

        // Temporary storage for results
        const tempResults = [];

        // Normalize the product name
        const productMapping = {
            'HEALTH TOTAL': 'HealthTotal',
            'HEALTH ABSOLUTE': 'HealthAbsolute',
            'ADVANTAGE TOPUP': 'AdvantageTopup',
        };

        const normalizedProductName = productMapping[product_master_name] || product_master_name;

        // Iterate through sum insured options
        const sumInsuredOptions = COVER_TYPE_SUM_INSURED[cover_type];

        for (const sumInsured of sumInsuredOptions) {
            // Generate numeric UID
            const numericUid = uuidv4();

            // Calculate dates
            const startDate = new Date();
            startDate.setDate(startDate.getDate() + 1);
            const endDate = new Date(startDate);
            endDate.setFullYear(endDate.getFullYear() + 1);
            endDate.setDate(endDate.getDate() - 1);

            const policyStartDate = formatDateToDDMMYYYY(startDate);
            const policyEndDate = formatDateToDDMMYYYY(endDate);


            // Prepare SOAP data with current sum insured
            const soapData = {
                Product: normalizedProductName,
                PolicyHeader: {
                    PolicyStartDate: policyStartDate,
                    PolicyEndDate: policyEndDate,

                },
                Uid: numericUid,
                VendorCode: "webagg",
                VendorUserId: "webagg",
                Client: {
                    ClientType: "I",
                    CreationType: "C",
                    Address1: {
                        Pincode: pincode,
                        Country: "IND"
                    }
                },
                BeneficiaryDetails: {
                    Member: members.map((member, index) => ({
                        MemberId: (index + 1).toString(),
                        InsuredName: `Member ${index + 1}`,
                        InsuredDob: formatDateToDDMMYYYY(new Date(calculateDOBFromAgeBand(member.ageBand))),
                        InsuredGender: member.gender || 'M',
                        InsuredOccpn: "SVCM",
                        CoverType: cover_type,
                        SumInsured: sumInsured,
                        Relation: member.relation,
                        Height: "170",
                        Weight: "70",
                        Smoking: "N",
                        Tobacco: "N",
                        IsGoodHealth: "Y",
                        IsExistingAbsolutePolicy: "N",
                        NomineeName: "Test Nominee",
                        NomineeRelation: "FATH",
                        NomineeAge: "45"
                    }))
                },
                Risk: {
                    PolicyType: family_type === 'individual' ? 'FHI' : 'FHF', // HAI for individual, HAF for floater
                    Duration: duration || "1",
                    Installments: "FULL",
                    PaymentType: "CC"
                }
            };

            try {
                // Call the SOAP service
                const soapResponse = await sendSOAPRequest(soapData);
                // Safely extract data from the response
                const policy = soapResponse?.policyDetails?.Root?.Policy?.[0];
                if (!policy) {
                    throw new Error('Invalid response structure: Policy data not found');
                }

                const outputRes = policy.OutputRes?.[0];
                const inputParams = policy.InputParameters?.[0];

                // Safely extract member details
                const memberResults = inputParams?.BeneficiaryDetails?.[0]?.Member?.map(member => ({
                    memberId: member.MemberId?.[0] || '',
                    sumInsured: member.SumInsured?.[0] || '',
                    basePremium: member.BeneBasePremium?.[0] || member.PerPrsnPremium?.[0] || '0',
                    coverType: member.CoverType?.[0] || '',
                    relation: member.Relation?.[0] || ''
                })) || [];

                // Safely extract output response data
                const outputResponseData = {
                    premiumAmt: outputRes?.PremiumAmt?.[0] || '0',
                    serviceTax: outputRes?.ServiceTax?.[0] || '0',
                    premWithServiceTax: outputRes?.PremWithServTax?.[0] || '0'
                };

                // Push the results into tempResults
                tempResults.push({
                    sumInsured: sumInsured,
                    requestId: numericUid,
                    premiums: memberResults,
                    outputResponse: outputResponseData,
                    rawResponse: soapResponse
                });
            } catch (soapError) {
                console.error(`Error for sum insured ${sumInsured}:`, soapError);
                tempResults.push({
                    sumInsured: sumInsured,
                    error: soapError.message || 'Unknown error'
                });
            }
        }

        // Respond with the temporary results
        res.json({
            status: 'success',
            coverType: cover_type,
            policyDetails: {
                policyType: family_type,
                coverType: cover_type,
                company_name: company_name,
                product: normalizedProductName,
                duration: duration

            },
            results: tempResults // Return all results after processing
        });

    } catch (error) {
        console.error('Error processing request:', error);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;