exports.up = function (knex) {
    return knex.schema.createTable('otp', function (table) {
      table.increments('id').primary(); // Primary key
      table.string('email').notNullable(); // Email to send OTP
      table.string('otp_code', 6).notNullable(); // 6-digit OTP code
      table.timestamp('expires_at').notNullable(); // OTP expiration time
      table.string('user_type'); // Add the user_type column
      table.timestamps(true, true); // Automatically adds created_at and updated_at
    });
  };
  
  exports.down = function (knex) {
    return knex.schema.dropTable('otp');
  };
  