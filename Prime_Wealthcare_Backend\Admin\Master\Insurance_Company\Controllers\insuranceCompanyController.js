const InsuranceCompany = require('../Models/InsuranceCompany');

// Create a new insurance company
const createInsuranceCompany = async (req, res, next) => {
  try {
    //
    const result = await InsuranceCompany.createInsuranceCompany(req.body);

    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
};

// Get all insurance companies
const getAllInsuranceCompanies = async (req, res, next) => {
  try {
    const companies = await InsuranceCompany.getAllInsuranceCompanies();
    res.status(200).json(companies);
  } catch (error) {
    next(error);
  }
};

// Get a specific insurance company by ID
const getInsuranceCompanyById = async (req, res, next) => {
  try {
    const company = await InsuranceCompany.getInsuranceCompanyById(req.params.id);
    if (company) {
      res.status(200).json(company);
    } else {
      res.status(404).json({ success: false, message: 'Insurance company not found' });
    }
  } catch (error) {
    next(error);
  }
};

// Update an insurance company by ID
const updateInsuranceCompanyById = async (req, res, next) => {
  try {
    const result = await InsuranceCompany.updateInsuranceCompanyById(req.params.id, req.body);
    if (result > 0) {
      res.status(200).json(result);
    } else {
      res.status(404).json({ success: false, message: 'Insurance company not found' });
    }
  } catch (error) {
    next(error);
  }
};

// Soft delete an insurance company by ID
const deleteInsuranceCompanyById = async (req, res, next) => {
  try {
    const result = await InsuranceCompany.deleteInsuranceCompanyById(req.params.id);
    if (result > 0) {
      res.status(200).json({ success: true, message: 'Insurance company marked as inactive' });
    } else {
      res.status(404).json({ success: false, message: 'Insurance company not found' });
    }
  } catch (error) {
    next(error);
  }
};

// Reinstate an insurance company by ID
const reinstateInsuranceCompany = async (req, res, next) => {
  try {
    const result = await InsuranceCompany.reinstateInsuranceCompany(req.params.id);
    if (result > 0) {
      res.status(200).json({ success: true, message: 'Insurance company reinstated' });
    } else {
      res.status(404).json({ success: false, message: 'Insurance company not found' });
    }
  } catch (error) {
    next(error);
  }
}

const getInsuranceCompanyByName = async (req, res, next) => {
  try {
    const { name } = req.params;
    const companies = await InsuranceCompany.getInsuranceCompaniesByName(name);


    res.status(200).json(companies);

  } catch (error) {
    // console.error('Error fetching insurance company by name:', error);  // Log the actual error
    next(error);
  }
};


// Get insurance companies by criteria
const getInsuranceCompaniesByCriteria = async (req, res, next) => {
  try {
    const criteria = req.params.criteria.trim();
    ('Criteria received:', criteria);  // Add this for debugging

    let data;
    switch (criteria) {
      case 'none':
        data = await InsuranceCompany.getAllInsuranceCompanies();
        break;
      case 'newLastWeek':
        data = await InsuranceCompany.newInsuranceCompaniesLastWeek();
        break;
      case 'newThisWeek':
        data = await InsuranceCompany.newInsuranceCompaniesThisWeek();
        break;
      case 'deactivatedThisWeek':
        data = await InsuranceCompany.deactivatedInsuranceCompaniesThisWeek();
        break;
      case 'deactivatedLastWeek':
        data = await InsuranceCompany.deactivatedInsuranceCompaniesLastWeek();
        break;
      case 'editedThisWeek':
        data = await InsuranceCompany.editedInsuranceCompaniesThisWeek();
        break;
      case 'editedLastWeek':
        data = await InsuranceCompany.editedInsuranceCompaniesLastWeek();
        break;
      default:


        return res.status(400).json({ message: 'Invalid criteria' });
    }


    res.status(200).json(data);
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createInsuranceCompany,
  getAllInsuranceCompanies,
  getInsuranceCompanyById,
  updateInsuranceCompanyById,
  deleteInsuranceCompanyById,
  reinstateInsuranceCompany,
  getInsuranceCompanyByName,
  getInsuranceCompaniesByCriteria

};
