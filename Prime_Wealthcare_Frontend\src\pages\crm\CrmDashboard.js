import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Paper, Typography, Grid } from '@mui/material';
import { fetchProposalsByCompany, fetchProposalsByAgent, getTotalPremiumInTimePeriod, getCurrentFinancialYearProposals, getTop10Branches, getTop20Agent, getTop20Agents } from '../../redux/actions/action';
import InsuranceCompanyChart from '../../components/dashBoard/insuranceCompanyChart';
import AgentPerformance from '../../components/dashBoard/AgentPerformance';
import ProposalBarChart from '../../components/dashBoard/ProposalBarChart';
import SingleInformationCard from '../../components/dashBoard/SingleInformationCard';
import Top10BranchesChart from '../../components/dashBoard/Top10BranchesChart';
import Top20Agents from '../../components/dashBoard/Top20Agents';

export default function CrmDashboard() {
    const dispatch = useDispatch();
    const [innerWidth, setInnerWidth] = useState(window.innerWidth);
    const [premiumData, setPremiumData] = useState([]);
    const [proposalsCount, setProposalsCount] = useState([]);
    const [top10Branches, setTop10Branches] = useState([]);
    const [top20Agents, setTop20Agents] = useState([]);

    // Fetch data on component mount
    useEffect(() => {
        dispatch(fetchProposalsByCompany());
        dispatch(fetchProposalsByAgent());
        dispatch(getTotalPremiumInTimePeriod()).then(res => {
            setPremiumData(res.payload);
        })
        dispatch(getCurrentFinancialYearProposals()).then(res => {
            setProposalsCount(res.payload)
        });
        dispatch(getTop10Branches()).then(res => {
            setTop10Branches(res.payload);
        });
        dispatch(getTop20Agents()).then(res => {
            setTop20Agents(res.payload);
        })
    }, []);

    useEffect(() => {
        const handleResize = () => setInnerWidth(window.innerWidth);
        window.addEventListener('resize', handleResize);

        // Cleanup function to remove the event listener
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const getCurrentQuarter = () => {
        const month = new Date().getMonth() + 1; // getMonth() returns 0-11
        // Financial year quarters (April to March)
        if (month >= 4 && month <= 6) return 1;
        if (month >= 7 && month <= 9) return 2;
        if (month >= 10 && month <= 12) return 3;
        return 4;
    };

    const {
        proposalData,
        agentData,
        loading: { proposals: proposalsLoading, agents: agentsLoading },
        error: { proposals: proposalsError, agents: agentsError }
    } = useSelector(state => state.dashboard);

    return (

        <div style={{ height: '100vh', padding: '20px' }}>
            <Grid container spacing={2}>
                <Grid item xs={12} sm={6} lg={3}>
                    <SingleInformationCard
                        title='YTD'
                        currentData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.thisFinancialYear?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.thisFinancialYear?.otherProposalsPremium
                            }
                        ]}
                        contestedData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.pastFinancialYear?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.pastFinancialYear?.otherProposalsPremium
                            }
                        ]}
                    />
                </Grid>
                <Grid item xs={12} sm={6} lg={3}>
                    <SingleInformationCard
                        title='MTD'
                        currentData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.thisMonth?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.thisMonth?.otherProposalsPremium
                            }
                        ]}
                        contestedData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.pastMonth?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.pastMonth?.otherProposalsPremium
                            }
                        ]}
                    />
                </Grid>
                <Grid item xs={12} sm={6} lg={3}>
                    <SingleInformationCard
                        title={`Q${getCurrentQuarter()}`}
                        currentData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.thisQuarter?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.thisQuarter?.otherProposalsPremium
                            }
                        ]}
                        contestedData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.pastQuarter?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.pastQuarter?.otherProposalsPremium
                            }
                        ]}
                    />
                </Grid>
                <Grid item xs={12} sm={6} lg={3}>
                    <SingleInformationCard
                        title='Cancel'
                        currentData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.cancel?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.cancel?.otherProposalsPremium
                            }
                        ]}
                        contestedData={[
                            {
                                title: 'Fresh',
                                value: premiumData?.pastCancel?.newProposalsPremium
                            },
                            {
                                title: 'Others',
                                value: premiumData?.pastCancel?.otherProposalsPremium
                            }
                        ]}
                        reverseCompare={true}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Top20Agents data={top20Agents} cardHeight={220} />
                </Grid>
                <Grid item xs={12} sm={12} lg={6}>
                    <ProposalBarChart
                        height={'520px'}
                        proposalData={proposalsCount}
                    />
                </Grid>
                <Grid item xs={12} sm={12} lg={6}>
                    <Top10BranchesChart
                        height={'520px'}
                        branchData={top10Branches}
                    />
                </Grid>


                {/*  */}
                {/* Insurance Company Chart */}
                <Grid item xs={12} md={6} sx={{ paddingBottom: '20px' }}>
                    <Paper
                        elevation={3}
                        sx={{
                            p: 2,
                            borderRadius: 2,
                            height: '100%',
                            backgroundColor: '#ffffff',
                            display: 'flex',
                            flexDirection: 'column',
                            '&:hover': {
                                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
                            }
                        }}
                    >
                        <InsuranceCompanyChart
                            chartData={proposalData}
                            chartType="stacked-bar"
                            title={`Proposals by Insurance Company (FY ${proposalData?.fiscalYear || ''})`}
                        />
                    </Paper>
                </Grid>

                {/* Agent Performance Chart */}
                <Grid item xs={12} md={6} sx={{ paddingBottom: '20px' }}>
                    <Paper
                        elevation={3}
                        sx={{
                            p: 2,
                            borderRadius: 2,
                            height: '100%',
                            backgroundColor: '#ffffff',
                            display: 'flex',
                            flexDirection: 'column',
                            '&:hover': {
                                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)'
                            }
                        }}
                    >
                        <AgentPerformance
                            chartData={agentData}
                            chartType="stacked-bar"
                            title={`Top Performing Agents (FY ${agentData?.fiscalYear || ''})`}
                        />
                    </Paper>
                </Grid>

            </Grid>
        </div>

    );
}
