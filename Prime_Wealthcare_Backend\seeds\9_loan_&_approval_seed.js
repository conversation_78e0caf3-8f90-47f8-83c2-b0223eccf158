exports.seed = async function (knex) {
    await knex('pick_list').insert([
        { type_name: 'Loan_Type', api_name: 'AG-PER', label_name: 'Personal', is_active: true },
        { type_name: 'Loan_Type', api_name: 'AG-ADV', label_name: 'Advance Salary', is_active: true }, // show to only employees not agents
        { type_name: 'Loan_Type', api_name: 'AG-VEH', label_name: 'Motor Loan', is_active: true },
        { type_name: 'Loan_Type', api_name: 'AG-COM', label_name: 'Advance Commission', is_active: true }, //show to only agents not employees  
        { type_name: 'Approval_Status', api_name: 'APP', label_name: 'APPROVED', is_active: true },
        { type_name: 'Approval_Status', api_name: 'REJ', label_name: 'REJECTED', is_active: true },
        { type_name: 'Approval_Status', api_name: 'P<PERSON>', label_name: 'PENDING', is_active: true },
    ]);
};