import React, { useState, useEffect } from 'react'
import { FormControl, ToggleButtonGroup, ToggleButton, Typography, Grid, Box } from '@mui/material'

const QuestionToggle = ({ question, onChange, required, value, disabled }) => {
    const [localValue, setLocalValue] = useState(value)

    const handleChange = (event, newValue) => {
        if (newValue !== null) {
            setLocalValue(newValue)
            onChange(newValue)
        }
    }

    useEffect(() => {
        setLocalValue(value)
    }, [value])

    return (
        <FormControl
            fullWidth
            sx={{
                marginBottom: 2,
                border: '1px solid #ccc',
                borderRadius: 4,
                padding: 2,
                borderLeft: required ? '3px solid red' : '1px solid #ccc',
                opacity: disabled ? 0.9 : 1
            }}
        >
            <Box sx={{ marginBottom: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                    {question}
                </Typography>
            </Box>
            <Grid container spacing={2}>
                <Grid item xs={12}>
                    <ToggleButtonGroup
                        value={localValue}
                        exclusive
                        onChange={handleChange}
                        aria-label={question}
                        fullWidth
                        disabled={disabled}
                    >
                        <ToggleButton
                            value={true}
                            aria-label="Yes"
                            disabled={disabled}
                            sx={{
                                width: '50%',
                                borderRadius: 4,
                                border: '1px solid #528a7e',
                                '&.Mui-selected': {
                                    backgroundColor: '#90EE90',
                                    '&:hover': {
                                        backgroundColor: '#82d682'
                                    }
                                },
                                '&.Mui-disabled': {
                                    color: 'rgba(0, 0, 0, 0.38)'
                                }
                            }}
                        >
                            <Typography>Yes</Typography>
                        </ToggleButton>
                        <ToggleButton
                            value={false}
                            aria-label="No"
                            disabled={disabled}
                            sx={{
                                width: '50%',
                                borderRadius: 4,
                                border: '1px solid red',
                                '&.Mui-selected': {
                                    backgroundColor: '#ffcccb',
                                    '&:hover': {
                                        backgroundColor: '#ffbdbc'
                                    }
                                },
                                '&.Mui-disabled': {
                                    color: 'rgba(0, 0, 0, 0.38)'
                                }
                            }}
                        >
                            <Typography>No</Typography>
                        </ToggleButton>
                    </ToggleButtonGroup>
                </Grid>
            </Grid>
        </FormControl>
    )
}

export default QuestionToggle
