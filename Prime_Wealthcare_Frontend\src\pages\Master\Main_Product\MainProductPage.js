import React, { useEffect, useState } from 'react';
import { Box, Container, Button, ButtonGroup } from '@mui/material';
import ModuleName from '../../../components/table/ModuleName';
import CustomTable from '../../../components/table/CustomTable';
import SearchBar from '../../../components/table/SearchBar';
import IconActions from '../../../components/table/IconActions';
import DropDown from '../../../components/table/DropDown';
import { useNavigate } from 'react-router-dom';
import DeletePopup from '../../../components/DeletePopup';
import SuccessPopup from '../../../components/SuccessPopUp';
import {
    deleteProduct,
    getAllProducts,
    reinstateProduct,
    getProductByName,
    getFilterData,
} from '../../../redux/actions/action';
import { useDispatch, useSelector } from 'react-redux';
import ExportToPDF from '../../../components/ExportToPDF';
import { usePermissions } from '../../../hooks/usePermissions';

const MainProductPage = () => {
    const [selectedOption, setSelectedOption] = useState('none');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedRows, setSelectedRows] = useState([]);
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const [selectedItem, setSelectedItem] = useState(null);
    const [openSuccessPopup, setOpenSuccessPopup] = useState(false);
    const [openDeletePopup, setOpenDeletePopup] = useState(false);
    const products = useSelector(state => state.mainProductReducer.products);
    const [sortedProducts, setSortedProducts] = useState(products || []);
    const  permissions  = usePermissions('Master', 'Main Product');

    useEffect(() => {
        dispatch(getAllProducts());
    }, [dispatch]);

    useEffect(() => {
        const fetchProducts = () => {
            dispatch(getFilterData(selectedOption));
        };
        fetchProducts();
    }, [selectedOption, dispatch]);

    useEffect(() => {
        const filterProductsByStatus = () => {
            if (statusFilter === 'all') {
                setSortedProducts(products);
            } else if (statusFilter === 'none') {
                dispatch(getAllProducts());
            } else {
                setSortedProducts(products.filter(product => product.status === (statusFilter === 'active' ? 1 : 0)));
            }
        };

        filterProductsByStatus();
    }, [statusFilter, products]);

    const handleOpenDeletePopup = (item) => {
        setSelectedItem(item);
        setOpenDeletePopup(true);
    };

    const handleCloseDeletePopup = () => {
        setOpenDeletePopup(false);
        setSelectedItem(null);
    };

    const handleConfirmDelete = () => {
        dispatch(deleteProduct(selectedItem.id))
            .then(() => {
                dispatch(getAllProducts());
                setOpenDeletePopup(false);
                setOpenSuccessPopup(true);
            })
            .catch(error => {
                console.error('Failed to delete product:', error);
                setOpenDeletePopup(false);
            });
    };

    const handleCloseSuccessPopup = () => {
        setOpenSuccessPopup(false);
    };

    const handleAdd = () => {
        navigate('/dashboard/main-product-form');
    };

    const handleDelete = (id) => {
        handleOpenDeletePopup(products.find(product => product.id === id));
    };

    const handleReinstate = (id) => {
        dispatch(reinstateProduct(id))
            .then(() => {
                dispatch(getAllProducts());
            })
            .catch(error => {
                console.error('Failed to reinstate product:', error);
            });
    };

    const handleEdit = (id) => {
        navigate(`/dashboard/main-product-form/edit/${id}`);
    };

    const handleSelectionChange = (id) => {
        setSelectedRows(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(rowId => rowId !== id)
                : [...prevSelected, id]
        );
    };

    const handleSelectAll = (isSelected) => {
        setSelectedRows(isSelected ? sortedProducts.map(product => product.id) : []);
    };

    const onSearch = (query) => {
        if (query === '') {
            dispatch(getAllProducts());
        } else {
            dispatch(getProductByName(query));
        }
    };

    const handleAllClick = () => setStatusFilter('all');
    const handleActiveClick = () => setStatusFilter('active');
    const handleInactiveClick = () => setStatusFilter('inactive');
    const handleRefreshClick = () => {
        setSelectedOption('none');
        dispatch(getAllProducts());
    }

    const columns = [
        { field: 'id', headerName: 'ID' },
        { field: 'main_product', headerName: 'Main Product' },
    ];

    const dataMapping = {
        ID: 'id',
        'Main Product': 'main_product',
        Status: 'status',
    };

    return (
        <Container maxWidth="xl" style={{ paddingLeft: '0px', paddingRight: '0px', marginTop: '0px', marginBottom: '0px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <img
                            src="/image.png"
                            alt="module icon"
                            style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }}
                        />
                        <ModuleName moduleName="Main Product" pageName="List" />
                    </Box>
                    <ButtonGroup variant="outlined" sx={{ borderRadius: 1 }}>
                        {permissions.can_add && (
                        <Button onClick={handleAdd} sx={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}>
                            New
                        </Button>
                        )}
                        <ExportToPDF
                            data={sortedProducts.map(product => ({
                                ...product,
                                status: product.status === 1 ? 'Active' : 'Inactive'
                            }))}
                            headNames={['ID', 'Main Product', 'Status']}
                            selectedRows={selectedRows}
                            imageUrl="/logo.png"
                            watermarkUrl="/gray-logo.png"
                            fileName="main-products.pdf"
                            dataMapping={dataMapping}
                            headerTitle="Main Product Report"
                        />
                    </ButtonGroup>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: -1, paddingInline: '1rem', ml: 5 }}>
                    <DropDown
                        label=""
                        value={selectedOption}
                        onChange={(e) => setSelectedOption(e.target.value)}
                        options={[
                            { value: 'none', label: 'None' },
                            { value: 'newLastWeek', label: 'New Last Week' },
                            { value: 'newThisWeek', label: 'New this Week' },
                            { value: 'deactivatedThisWeek', label: 'Deactivated this Week' },
                            { value: 'deactivatedLastWeek', label: 'Deactivated Last Week' },
                            { value: 'editedLastWeek', label: 'Edited Last Week' },
                            { value: 'editedThisWeek', label: 'Edited This Week' },
                        ]}
                    />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SearchBar placeholder="Search..." onSearch={onSearch} />
                        <IconActions
                            onAllClick={handleAllClick}
                            onActiveClick={handleActiveClick}
                            onInactiveClick={handleInactiveClick}
                            onRefreshClick={handleRefreshClick}
                        />
                    </Box>
                </Box>

                <Box sx={{ mt: -1, maxHeight: '400px' }}>
                    <CustomTable
                        data={sortedProducts}
                        columns={columns}
                        onDelete={permissions.can_delete ? handleDelete : null}
                        onEdit={permissions.can_edit ? handleEdit : null}
                        onReinstate={handleReinstate}
                        onSelectionChange={handleSelectionChange}
                        selectedRows={selectedRows}
                        onSelectAll={handleSelectAll}
                    />
                </Box>
            </Box>

            <DeletePopup
                open={openDeletePopup}
                onClose={handleCloseDeletePopup}
                onConfirm={handleConfirmDelete}
                modulename={selectedItem ? selectedItem.main_product : ''}
            />
            <SuccessPopup
                open={openSuccessPopup}
                onClose={handleCloseSuccessPopup}
                modulename={selectedItem ? selectedItem.main_product : ''}
            />
        </Container>
    );
};

export default MainProductPage;
