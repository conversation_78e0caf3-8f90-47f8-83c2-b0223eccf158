const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp, formatDateToSQL } = require('../../../../Reusable/reusable');
const knex = require('knex')(knexConfig.development);

// Get all agency codes
const getAllAgencyCodes = async () => {
  try {
    return await knex('agency_code')
      .select('*')
      .where({ status: 1 });
  } catch (error) {
    console.error('Error fetching agency codes:', error);
    throw error;
  }
};

// Insert new agency code (with insurance branch creation if needed)
const insertAgencyCode1 = async (data) => {
  const {
    insurance_company_id,
    insurance_co_branch_name,
    agent_name,
    agency_code,
    license_no,
    license_valid_from,
    license_valid_till,
    imf_branch_id
  } = data;

  try {
    await knex.transaction(async (trx) => {
      // Find the existing branch
      const branch = await trx('insurance_branch')
        .where({ insurance_company_id, insurance_co_branch_name })
        .first();

      if (!branch) {
        // If branch doesn't exist, exit and don't insert anything
        throw new Error('Branch does not exist');
      }

      // Check if the agency code already exists for the branch ID (which is the branch's id)
      const existingAgencyCode = await trx('agency_code')
        .where({ agency_code, id: branch.id })  // Use branch's `id` for matching
        .first();

      if (existingAgencyCode) {
        // If the agency code already exists for this branch id, skip inserting
        throw new Error('Agency code already exists for this branch');
      }

      // Insert the new agency code using the same ID as the branch
      await trx('agency_code').insert({
        insurance_company_id,
        insurance_co_branch_name,
        agent_name,
        agency_code,
        license_no,
        license_valid_from,
        license_valid_till,
        imf_branch_id,
        id: branch.id,  // Use the branch's `id` as the agency code's `id`
      });
    });


  } catch (error) {
    console.error('Error inserting agency code:', error.message);
    throw new Error(error.message || 'Transaction failed');
  }
};


const insertAgencyCode = async (data) => {

  try {
    await knex.transaction(async (trx) => {
      const parseDate = (dateStr) => {
        const [day, month, year] = dateStr.split('/');
        return new Date(`${year}-${month}-${day}`).toISOString().slice(0, 19).replace('T', ' ');
      };

      const licenseValidFrom = parseDate(data.agencyCodeData.license_valid_from);
      const licenseValidTill = parseDate(data.agencyCodeData.license_valid_till);

      const formatedData = {
        ...data.agencyCodeData,
        license_valid_from: licenseValidFrom,
        license_valid_till: licenseValidTill
      }
      // Insert the new agency code for the existing branch
      await trx('agency_code').insert(formatedData);


    });
  } catch (error) {
    console.error('Error inserting agency code:', error.message);
    throw new Error(error.message || 'Transaction failed.');
  }
};


// Get agency code by ID
const getAgencyCodeById = async (id) => {
  try {
    return await knex('agency_code').where({ id }).first();
  } catch (error) {
    console.error('Error fetching agency code by id:', error);
    throw error;
  }
};

// Update agency code by ID
const updateAgencyCodeById = async (id, data) => {
  try {
    const result = await knex('agency_code')
      .where({ id }) // Ensure you're updating based on the correct ID
      .update({
        insurance_company_id: data.insurance_company_id,
        insurance_co_branch_name: data.insurance_co_branch_name,
        agent_name: data.agent_name,
        agency_code: data.agency_code,
        license_no: data.license_no,
        license_valid_from: data.license_valid_from,
        license_valid_till: data.license_valid_till,
        imf_branch_id: data.imf_branch_id
      });

    if (result === 0) {
      console.error(`No record found for id: ${id}`);
      throw new Error('No record updated');
    }
    return result;
  } catch (error) {
    console.error('Error updating agency code:', error); // Log the exact error for debugging
    throw error;
  }
};

// Soft delete agency code by ID
const deleteAgencyCodeById = async (id) => {
  try {
    await knex.transaction(async (trx) => {
      // Find the insurance branch using the agency code's branch name
      const agencyCode = await trx('agency_code').where({ id }).first();

      if (!agencyCode) {
        throw new Error('Agency code not found');
      }

      // Soft delete the agency code
      await trx('agency_code')
        .where({ id })
        .update({ status: 0, updated_at: getCurrentTimestamp() });

      // Soft delete the related insurance branch
      await trx('insurance_branch')
        .where({
          insurance_company_id: agencyCode.insurance_company_id,
          insurance_co_branch_name: agencyCode.insurance_co_branch_name,
        })
        .update({ status: 0 });
    });
    return { message: 'Agency code and related branch soft deleted successfully' };
  } catch (error) {
    console.error('Error deleting agency code and related branch:', error);
    throw error;
  }
};


const reinstateAgencyCodeById = async (id) => {
  try {
    await knex.transaction(async (trx) => {
      // Find the insurance branch using the agency code's branch name
      const agencyCode = await trx('agency_code').where({ id }).first();

      if (!agencyCode) {
        throw new Error('Agency code not found');
      }

      // Reinstate the agency code
      await trx('agency_code')
        .where({ id })
        .update({ status: 1 });

      // Reinstate the related insurance branch
      await trx('insurance_branch')
        .where({
          insurance_company_id: agencyCode.insurance_company_id,
          insurance_co_branch_name: agencyCode.insurance_co_branch_name,
        })
        .update({ status: 1 });
    });
    return { message: 'Agency code and related branch reinstated successfully' };
  } catch (error) {
    console.error('Error reinstating agency code and related branch:', error);
    throw error;
  }
};


// Get agency codes by branch name
const getAgencyCodesByBranchName = async (branchName) => {
  try {
    return await knex('agency_code')
      .select(
        'agency_code.agency_code',
        'agency_code.agent_name',
        'agency_code.license_no',
        'agency_code.license_valid_from',
        'agency_code.license_valid_till',
        // 'agency_code.imf_branch_name',
        'imf_branches.imf_branch_id',
        'insurance_branch.insurance_co_branch_name',
        'insurance_branch.insurance_company_id',
        'insurance_company.insurance_company_name'
      )
      .leftJoin('insurance_branch', 'agency_code.insurance_co_branch_name', 'insurance_branch.insurance_co_branch_name')
      .leftJoin('insurance_company', 'insurance_branch.insurance_company_id', 'insurance_company.id')
      .leftJoin('imf_branches', 'imf_branches.imf_branch_id', 'imf_branches.id')
      .where('agency_code.insurance_co_branch_name', branchName);
  } catch (error) {
    console.error('Error fetching agency codes by branch name:', error);
    throw error;
  }
};

module.exports = {
  getAllAgencyCodes,
  insertAgencyCode,
  getAgencyCodeById,
  updateAgencyCodeById,
  deleteAgencyCodeById,
  reinstateAgencyCodeById,
  getAgencyCodesByBranchName
};