const axios = require('axios');
const { parseStringPromise } = require('xml2js');
const { v4: uuidv4 } = require('uuid');
const knex = require('knex');
const knexConfig = require('../../../knexfile');

const db = knex(knexConfig.development);

// Environment variables
require('dotenv').config();

const SOAP_API_URL = "http://fgnluat.fggeneral.in/BO/Service.svc";
const SOAP_ACTION = "http://tempuri.org/IService/CreatePolicy";
const VENDOR_CODE = process.env.VENDOR_CODE;
const VENDOR_USER_ID = process.env.VENDOR_USER_ID;


// Generate Member XML dynamically
const generateMemberXML = (memberData) => {
    return memberData
        .map(
            (memberData, index) => `<Member>
            <MemberId>${index + 1}</MemberId>
            <AbhaNo />
            <InsuredName>${memberData.insuredName}</InsuredName>
            <InsuredDob>${formatDate(memberData.insuredDob)}</InsuredDob>  
            <InsuredGender></InsuredGender>
            <InsuredOccpn></InsuredOccpn>
            <CoverType>${memberData.coverType}</CoverType>
            <SumInsured>${memberData.sumInsured}</SumInsured>
            <DeductibleDiscount />
            <Relation>SELF</Relation>
            <NomineeName>Test Nominee</NomineeName>
            <NomineeRelation></NomineeRelation>
            <AnualIncome />
            <Height>170</Height>
            <Weight>60</Weight>
            <NomineeAge>45</NomineeAge>
            <AppointeeName />
        <AptRelWithNominee />
        <Smoking>N</Smoking>
        <Tobacco>N</Tobacco>
        <IsGoodHealth>Y</IsGoodHealth>
        <IsExistingAbsolutePolicy>N</IsExistingAbsolutePolicy>
        <AdditionalInformation />
        </Member>`
        )
        .join("");
};

// Generate SOAP body dynamically
const SOAP_BODY = (uid, quotationData, membersXML) => `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CreatePolicy>
            <tem:Product>HealthAbsolute</tem:Product>
            <tem:XML>
                <![CDATA[<Root>
  <Uid>${uid}</Uid>
   <VendorCode>${VENDOR_CODE}</VendorCode>
   <VendorUserId>${VENDOR_USER_ID}</VendorUserId>
  <SentToOutSourcePrint>0</SentToOutSourcePrint>
  <WinNo />
  <ApplicationNo />
  <PolicyHeader>
    <PolicyStartDate></PolicyStartDate>
    <PolicyEndDate></PolicyEndDate>
    <AgentCode>${process.env.AGENT_CODE}</AgentCode>
    <BranchCode>${process.env.BRANCH_CODE}</BranchCode>
    <MajorClass>${quotationData.majorClass}</MajorClass>
    <ContractType>${quotationData.contractType}</ContractType>
    <METHOD>ENQ</METHOD>
    <PolicyIssueType>${quotationData.policyIssueType}</PolicyIssueType>
    <PolicyNo />
    <ClientID></ClientID>
    <ReceiptNo />
  </PolicyHeader>
  <POS_MISP>
    <Type />
    <PanNo />
  </POS_MISP>
  <Client>
    <ClientCategory />
    <ClientType>I</ClientType>
    <CreationType>C</CreationType>
    <Salutation>MR</Salutation>
    <FirstName>shiv</FirstName>
    <LastName>dayal kumawat</LastName>
    <DOB></DOB>
    <Gender>M</Gender>
    <MaritalStatus>M</MaritalStatus>
    <Occupation>SVCM</Occupation>
    <PANNo>**********</PANNo>
    <GSTIN />
    <AadharNo />
    <CKYCNo></CKYCNo>
    <CKYCRefNo></CKYCRefNo>
    <EIANo />
    <Address1>
      <AddrLine1>403/404 Bhavani Skyline, Atabhai Road</AddrLine1>
      <AddrLine2>Nr. Piyusha Fast Food, Opp. Jogger's Park</AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${quotationData.pincode}</Pincode>
      <City>Bhavnagar</City>
      <State>Gujarat</State>
      <Country>IND</Country>
      <AddressType>R</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo>9829876493</MobileNo>
      <EmailAddr><EMAIL></EmailAddr>
    </Address1>
    <Address2>
      <AddrLine1>403/404 Bhavani Skyline, Atabhai Road</AddrLine1>
      <AddrLine2>Nr. Piyusha Fast Food, Opp. Jogger's Park</AddrLine2>
      <AddrLine3/>
      <Landmark />
      <Pincode>${quotationData.pincode}</Pincode>
      <City>Bhavnagar</City>
      <State>Gujarat</State>
      <Country>IND</Country>
      <AddressType>K</AddressType>
      <HomeTelNo />
      <OfficeTelNo />
      <FAXNO />
      <MobileNo />
      <EmailAddr />
    </Address2>
    <VIPFlag>N</VIPFlag>
    <VIPCategory />
  </Client>
  <Receipt>
    <UniqueTranKey></UniqueTranKey>
    <CheckType />
    <BSBCode />
    <TransactionDate></TransactionDate>
    <ReceiptType>IVR</ReceiptType>
    <Amount></Amount>
    <TCSAmount />
    <TranRefNo></TranRefNo>
    <TranRefNoDate></TranRefNoDate>
  </Receipt>
    <Risk>
    <PolicyType>HAI</PolicyType>
    <Duration>1</Duration>
    <Installments>FULL</Installments>
    <PaymentType>${quotationData.paymentType}</PaymentType>
    <IsFgEmployee>N</IsFgEmployee>
    <BranchReferenceID />
    <FGBankBranchStaffID />
    <BankStaffID />
    <BankCustomerID />
    <BancaChannel />
    <PartnerRefNo />
    <PayorID />
    <PayerName />
    <BeneficiaryDetails>
        ${membersXML}
    </BeneficiaryDetails>
  </Risk>
</Root>]]></tem:XML>
        </tem:CreatePolicy>
    </soapenv:Body>
</soapenv:Envelope>
`;

// Helper function to format date to DD/MM/YYYY
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
};

// Function to save SOAP response to database
const saveSoapResponse = async (quotationId, memberData, responseData) => {
    try {
        // Log the entire responseData for debugging
        // console.log('Full Response Data:', JSON.stringify(responseData, null, 2));

        // Extract CreatePolicyResult directly with safe navigation
        const createPolicyResult =
            responseData?.['s:Envelope']?.['s:Body']?.[0]?.['CreatePolicyResponse']?.[0]?.['CreatePolicyResult']?.[0];

        if (!createPolicyResult) {
            throw new Error('Unable to extract CreatePolicyResult from response');
        }

        // Log the createPolicyResult
        // console.log('Create Policy Result:', createPolicyResult);

        // Try parsing the XML content
        let xmlContent;
        try {
            xmlContent = await parseStringPromise(createPolicyResult);
            console.log('Parsed XML Content:', JSON.stringify(xmlContent, null, 2));
        } catch (parseError) {
            console.error('XML Parsing Error:', parseError);
            console.log('Unparsed Response:', createPolicyResult);
            throw parseError;
        }

        // Prepare data for each member with extensive logging
        const soapResponses = memberData.map((member, index) => {
            // console.log(`Processing Member ${index + 1}:`, member);

            // Find the corresponding member in the XML
            const xmlMember = xmlContent.Root.Policy[0].InputParameters[0].BeneficiaryDetails[0].Member
                .find(m => m.MemberId[0] === String(member.member_id));

            if (!xmlMember) {
                console.warn(`No matching XML member found for member_id: ${member.member_id}`);
            }

            // Extract data from the parsed XML
            const policyOutputRes = xmlContent.Root.Policy[0].OutputRes[0];

            // Comprehensive data extraction
            const responseObj = {
                quotation_id: quotationId,
                // member_id: member.member_id || null,
                member_name: member.insuredName,
                cover_type: member.coverType,
                sum_insured: member.sumInsured,
                //  response_xml: createPolicyResult,
                status: 'COMPLETED',

                // Premium and Tax Details - Direct extraction from parsed XML
                base_premium: extractNumericValue(policyOutputRes.BasePremium[0]),
                term_premium: extractNumericValue(policyOutputRes.TermPremium[0]),
                family_discount_rate: extractNumericValue(policyOutputRes.FmlyDiscRate[0]),
                family_discount: extractNumericValue(policyOutputRes.FamilyDiscount[0]),
                premium_without_service_tax: extractNumericValue(policyOutputRes.PremWithoutServTax[0]),
                premium_with_load: extractNumericValue(policyOutputRes.PremWithLoad[0]),
                premium_amount: extractNumericValue(policyOutputRes.PremiumAmt[0]),
                service_tax_rate: extractNumericValue(policyOutputRes.ServiceTaxRate[0]),
                service_tax: extractNumericValue(policyOutputRes.ServiceTax[0]),
                premium_with_service_tax: extractNumericValue(policyOutputRes.PremWithServTax[0]),

                // Additional member-specific details
                bmi: xmlMember ? extractNumericValue(xmlMember.BMI[0]) : null,
                bmi_loading_percent: xmlMember ? extractNumericValue(xmlMember.BMILoadingPercent[0]) : null,
                per_person_premium: xmlMember ? extractNumericValue(xmlMember.PerPersonPremium[0]) : null
            };

            // Log the final response object
            console.log(`Member ${index + 1} Response Object:`, JSON.stringify(responseObj, null, 2));

            return responseObj;
        });

        // Insert responses into soap_responses table
        const insertedResponses = await db('soap_responses').insert(soapResponses);

        return insertedResponses;
    } catch (error) {
        console.error('Comprehensive Error in saving SOAP response:', error);
        throw new Error(`Failed to save SOAP response: ${error.message}`);
    }
};

// Helper function to safely extract numeric values
const extractNumericValue = (value) => {
    if (value === undefined || value === null) return null;

    // Convert to string first to handle different input types
    const stringValue = String(value);

    // Remove any non-numeric characters except decimal point
    const numericString = stringValue.replace(/[^\d.]/g, '');

    // Convert to number
    const numericValue = parseFloat(numericString);

    // Return number or null if conversion fails
    return isNaN(numericValue) ? null : numericValue;
};

// Modified sendSOAPRequest function
const sendSOAPRequest = async (memberData, quotationData, quotationId) => {
    try {
        const headers = {
            "Content-Type": "text/xml; charset=utf-8",
            SOAPAction: SOAP_ACTION,
        };

        // Ensure memberData is an array
        const membersArray = Array.isArray(memberData) ? memberData : [memberData];

        // Generate unique UID and Member XML
        const uniqueUID = uuidv4();
        const membersXML = generateMemberXML(membersArray);

        // Generate SOAP body dynamically
        const requestBody = SOAP_BODY(uniqueUID, quotationData, membersXML);

        // Send the SOAP request
        const response = await axios.post(SOAP_API_URL, requestBody, { headers });

        // Parse the XML response
        const jsonResponse = await parseStringPromise(response.data);

        // Log the full response for debugging
        console.log('SOAP Response:', JSON.stringify(jsonResponse, null, 2));

        // Save the SOAP response to database
        await saveSoapResponse(quotationId, membersArray, jsonResponse);

        return jsonResponse;
    } catch (error) {
        console.error("SOAP Request Error Details:", {
            message: error.message,
            status: error.response ? error.response.status : "No status",
            data: error.response ? error.response.data : "No data",
        });

        // Optionally, save error response
        await saveSoapResponse(quotationId, memberData, {
            status: 'FAILED',
            error: error.message
        });

        throw new Error(`SOAP Request Failed: ${error.message}`);
    }
};

module.exports = { sendSOAPRequest };
