const knexConfig = require('../../../knexfile');
const { getCurrentTimestamp } = require('../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new agent address
const create = async (data) => {
    try {
        const { result } = await db('agent_address').insert(data);
        return result;
    } catch (error) {
        console.error("Error creating address:", error);
        throw error;
    }
};

// Find all agent addresses
const findAll = async () => {
    try {
        return await db('agent_address').select('*');
    } catch (error) {
        throw error;
    }
};

// Find an agent address by ID
const findById = async (id) => {
    try {
        const address = await db('agent_address').where({ agent_id: id }).first();
        return address;
    } catch (error) {
        throw error;
    }
};

// Update an agent address by ID
const updateById = async (id, data) => {
    try {
        // Add the formatted updated_at field to the data object
        data.updated_at = getCurrentTimestamp();

        const result = await db('agent_address')
            .where({ id })
            .update(data);
        return result;
    } catch (error) {
        throw error;
    }
};

// Delete (deactivate) an agent address by ID (soft delete)
const deleteById = async (id) => {
    try {
        const result = await db('agent_address').where({ id }).update({ status: false, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findAll,
    findById,
    updateById,
    deleteById,
};
