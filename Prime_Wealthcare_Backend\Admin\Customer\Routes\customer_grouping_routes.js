const express = require('express');
const customerGroupingController = require('../Controllers/cusomer_grouping_controller');
const router = express.Router();

// Route to create a new customer info
router.post('/', customerGroupingController.createCustomerGrouping);

router.get('/', customerGroupingController.getCustomerGrouping);

router.get('/:id', customerGroupingController.getCostomerGroupingById);

router.get('/customer_id/:id', customerGroupingController.getCostomerGroupingByCustomerId);

router.put('/:id', customerGroupingController.updateCustomerGrouping);

router.delete('/:id', customerGroupingController.deleteCustomerGrouping);

module.exports = router;