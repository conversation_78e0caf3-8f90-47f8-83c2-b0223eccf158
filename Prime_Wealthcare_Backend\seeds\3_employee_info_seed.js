const bcrypt = require('bcrypt');

exports.seed = async function (knex) {
  // Hash passwords
  const password1 = await bcrypt.hash('PIYUSH@123', 10);
  const password2 = await bcrypt.hash('MEHUL@123', 10);

  return knex('employee_personal_info').del()
    .then(async function () {
      return knex('employee_personal_info').insert([
        {
          id: 1,
          employee_full_name: 'PIYUSH PANDYA',
          gender: '1',
          education: '13',
          adhar_number: '1234-5678-9123',
          PAN_number: '**********',
          personal_email: '<EMAIL>',
          personal_mobile: **********,
          date_of_birth: '1990-01-01 00:00:00',
          blood_group: '47',
          marital_status: '4',
          marriage_date: null,
          driving_license_number: null,
          user_id: 'PWS-ADM001',
          password: password1,
          role_id: null,
          branch_id: null,
          first_reporting_manager_id: 1,
          second_reporting_manager_id: 1,
          official_email: null,
          official_mobile: null,
          date_of_joining: '2025-01-06 21:18:09',
          emp_photo: null,
          emp_adhar_front_pdf: null,
          emp_adhar_back_pdf: null,
          emp_PAN_pdf: null,
          emp_signed_offer_letter_pdf: null,
          emp_driving_license_pdf: null,
          status: 1,
          created_by: 1,
          updated_by: 1,
          created_at: '2025-01-06 21:18:09',
          updated_at: '2025-01-06 21:18:09'
        },
        {
          id: 2,
          employee_full_name: 'MEHUL PRADHAN',
          gender: '1',
          education: '12',
          adhar_number: '1234-5678-9123',
          PAN_number: '**********',
          personal_email: '<EMAIL>',
          personal_mobile: **********,
          date_of_birth: '1990-01-01 00:00:00',
          blood_group: '47',
          marital_status: '5',
          marriage_date: null,
          driving_license_number: null,
          user_id: 'PWS-ADM002',
          password: password2,
          role_id: null,
          branch_id: null,
          first_reporting_manager_id: 1,
          second_reporting_manager_id: 1,
          official_email: null,
          official_mobile: null,
          date_of_joining: '2025-01-06 21:18:09',
          emp_photo: null,
          emp_adhar_front_pdf: null,
          emp_adhar_back_pdf: null,
          emp_PAN_pdf: null,
          emp_signed_offer_letter_pdf: null,
          emp_driving_license_pdf: null,
          status: 1,
          created_by: 1,
          updated_by: 1,
          created_at: '2025-01-06 21:18:09',
          updated_at: '2025-01-06 21:18:09'
        }
      ]);
    });
};
