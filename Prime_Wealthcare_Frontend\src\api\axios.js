import axios from 'axios';
import  store  from '../redux/store';
import { logout } from '../redux/slices/authSlice';
import { getToken } from '../utils/storage';

const host = process.env.REACT_APP_HOST;
const port = process.env.REACT_APP_PORT;

// Create axios instance with base URL
const instance = axios.create({
  baseURL: port ? `${host}:${port}/` : `${host}/`,
});

// Request interceptor - adds token to all requests
axios.interceptors.request.use((config) => {
  const token = getToken();
  if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('Authorization header set:', config.headers.Authorization);
  }
  return config;
}, (error) => Promise.reject(error));



// Response interceptor - handles token-related errors
instance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Dispatch logout action to clear auth state
      store.dispatch(logout());
      // Redirect to login page
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);
 
export default instance;