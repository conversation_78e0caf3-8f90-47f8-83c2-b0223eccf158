import { createSlice } from '@reduxjs/toolkit';
import { saveRenewalsMapping } from '../../actions/action';

const initialState = {
    renewals: [],
    loading: false,
    error: null,
    success: false,
    totalRecords: 0
};

const renewalsSlice = createSlice({
    name: 'renewals',
    initialState,
    reducers: {
        clearRenewalsError: (state) => {
            state.error = null;
        },
        clearRenewalsSuccess: (state) => {
            state.success = false;
        },
        resetRenewalsState: (state) => {
            return initialState;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(saveRenewalsMapping.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.success = false;
            })
            .addCase(saveRenewalsMapping.fulfilled, (state, action) => {
                state.loading = false;
                state.success = true;
                state.renewals = action.payload.data;
                state.totalRecords = action.payload.total_records;
                state.error = null;
            })
            .addCase(saveRenewalsMapping.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                state.success = false;
            });
    }
});

// Export actions
export const {
    clearRenewalsError,
    clearRenewalsSuccess,
    resetRenewalsState
} = renewalsSlice.actions;

// Export selectors
export const selectRenewals = (state) => state.renewals.renewals;
export const selectRenewalsLoading = (state) => state.renewals.loading;
export const selectRenewalsError = (state) => state.renewals.error;
export const selectRenewalsSuccess = (state) => state.renewals.success;
export const selectRenewalsTotalRecords = (state) => state.renewals.totalRecords;

// Export reducer
export default renewalsSlice.reducer;