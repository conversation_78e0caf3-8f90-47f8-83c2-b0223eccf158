import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import CustomTextField from '../../../components/CustomTextField';
import Box from '@mui/material/Box';
import ModuleName from '../../../components/table/ModuleName';
import { createEmployeeSalary, createsalary, fetchAllEmployeeSalary, fetchEmployeeById, fetchEmployeeSalaryById, getAllsalarys, getsalaryById, getsalarysByName, updateEmployeeSalary, updatesalary } from '../../../redux/actions/action';
import { currentFinancialYear, formatDate, trimFormData } from '../../../utils/Reusable';
import dayjs from 'dayjs';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { FormControl } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { clearCurrentSalary } from '../../../redux/slices/User/employeeSalarySlice';

function SalaryForm() {

    const { id } = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();

    const isCreateMode = location.pathname.includes('create');
    const isViewMode = location.pathname.includes('view');

    const salary = useSelector(state => state.employeeSalaryReducer.currentSalary);
    const employeeInfo = useSelector(state => state.employeeInfoReducer.employeeDetail);

    const [formData, setFormData] = useState({
        employee_full_name: '',
        gross_salary: '',
        pf_amount: '',
        esic_amount: '',
        insurance_amount: '',
        deductible_amount: '',
        tenure: '',
        deductible_end_date: '',
        net_salary: '',
    });

    const [errors, setErrors] = useState({
        employee_full_name: false,
        gross_salary: false,
        pf_amount: false,
        esic_amount: false,
        deductible_amount: false,
        tenure_deductible_end_date: false,
        net_salary: false,
    })

    useEffect(() => {
        dispatch(fetchEmployeeById(id));
        if (id && !isCreateMode) {
            dispatch(fetchEmployeeSalaryById(id));
        } else {
            dispatch(clearCurrentSalary);
            resetFormData();
        }
    }, [id, dispatch]);

    useEffect(() => {
        if (salary && id) {
            setFormData(prevFormData => ({
                ...prevFormData,
                gross_salary: salary.gross_salary || '',
                pf_amount: salary.pf_amount || '',
                esic_amount: salary.esic_amount || 0,
                insurance_amount: salary.insurance_amount || 0,
                deductible_amount: salary.deductible_amount || '',
                deductible_end_date: dayjs(salary.deductible_end_date) || dayjs(new Date()),
                tenure: salary.tenure || '',
                net_salary: salary.net_salary || '',
            }));
        }
    }, [salary, id]);

    useEffect(() => {
        if (employeeInfo) {
            setFormData(prevFormData => ({
                ...prevFormData,
                employee_full_name: employeeInfo?.employee_full_name,
            }));
        }
    }, [employeeInfo]);

    const resetFormData = () => {
        setFormData({
            employee_full_name: '',
            gross_salary: '',
            pf_amount: '',
            esic_amount: '',
            insurance_amount: '',
            deductible_amount: '',
            tenure: '',
            deductible_end_date: dayjs(new Date()),
            net_salary: '',
        });
    };

    const validate = () => {
        let tempErrors = {};
        if (!formData.gross_salary) {
            tempErrors.gross_salary = 'Gross Amount is required';
        }
        if (!formData.deductible_amount) {
            tempErrors.deductible_amount = 'Deduction Amount is required';
        }
        if (!formData.tenure) {
            tempErrors.tenure = 'Tenure is required';
        }
        if (!formData.net_salary) {
            tempErrors.net_salary = 'Net Salary is required';
        }
        if (formData.net_salary) {
            if (formData.net_salary < 0) {
                tempErrors.net_salary = 'Net Salary cannot be negative';
            }
        }
        setErrors(tempErrors);

        // Return true if there are no errors
        return Object.keys(tempErrors).length === 0;
    };

    const handleChange = (e) => {
        const { name, value } = e.target;

        if (value === ' ') {
            setErrors({
                ...errors,
                [name]: 'Do not start with a whitespace character'
            })
            return;
        }
        let additional_fields = {};
        let additional_field_errors = {};
        if ((['gross_salary', 'pf_amount', 'esic_amount', 'insurance_amount', 'deductible_amount', 'tenure']).includes(name)) {
            if (!/^\d*?\d*$/.test(value) && value !== '') {
                setErrors({
                    ...errors,
                    [name]: 'Only numbers are allowed'
                });
                return;
            }
            const gross_salary = name === 'gross_salary' ? value : formData?.gross_salary || 0;
            const pf_amount = name === 'pf_amount' ? value : formData?.pf_amount || 0;
            const esic_amount = name === 'esic_amount' ? value : formData?.esic_amount || 0;
            const insurance_amount = name === 'insurance_amount' ? value : formData?.insurance_amount || 0;
            const deductible_amount = name === 'deductible_amount' ? value : formData?.deductible_amount || 0;

            if (name !== 'tenure') {
                additional_fields = {
                    net_salary: gross_salary - pf_amount - esic_amount - insurance_amount - deductible_amount
                }
                if (additional_fields.net_salary >= 0) {
                    additional_field_errors = {
                        ...additional_field_errors,
                        net_salary: ''
                    }
                }
            } else {
                additional_fields = {
                    deductible_end_date: dayjs().add(value, 'month')
                }
            }
        }
        const data = value.toUpperCase().replace(/\s{2,}$/, ' ')
        setFormData({
            ...formData,
            [name]: data,
            ...additional_fields
        });
        setErrors({
            ...errors,
            [name]: false,
            ...additional_field_errors
        })
    };

    const handlesalaryCreationAndUpdate = () => {
        const isValid = validate();
        if (!isValid) return; // Stop if the form is invalid

        const { employee_full_name, deductible_end_date, ...rest } = formData;
        const data = {
            employee_id: id,
            deductible_end_date: dayjs(deductible_end_date).format('YYYY-MM-DD'),
            year: currentFinancialYear(),
            ...rest
        };
        const filteredData = trimFormData(data);

        if (!isCreateMode) {
            dispatch(updateEmployeeSalary({ id, data: filteredData }));
            resetFormData();
        } else {
            dispatch(createEmployeeSalary(filteredData));
            handleCancel();
        }
    };

    const handleSave = () => {
        handlesalaryCreationAndUpdate();
        if (validate()) {
            handleCancel();
        }
    };

    const handleSaveAndNew = () => {
        handlesalaryCreationAndUpdate();
        if (validate()) {
            resetFormData();
        }
    };

    const handleCancel = () => {
        navigate(`/dashboard/employee-master-overview/${id}`);
    };

    return (
        <form encType="multipart/form-data" style={{ width: '100%' }}>
            <Grid container spacing={2}>
                <Grid item xs={8} style={{ display: 'flex', alignItems: 'center' }}>
                    <img src="/image.png" alt="module icon" style={{ width: '20px', padding: '10px,30px,10px,30px', marginLeft: '20px', backgroundColor: 'green' }} />

                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ModuleName moduleName="Employee Salary" pageName={id ? salary?.status === 0 ? "View" : "Edit" : "Create"} />
                    </Box>
                </Grid>
                <Grid item xs={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {!isCreateMode ? !isViewMode ? (

                        <>
                            {(salary?.status === 1 || !isCreateMode) && <Button
                                variant="outlined"
                                size="small"
                                sx={{
                                    maxWidth: '120px', // Increase width if needed
                                    width: '120px', // Set a fixed width
                                    mx: 0.5,
                                    color: 'green',
                                    borderColor: 'green',
                                    mt: 3,
                                    textTransform: 'none',
                                    fontSize: '0.875rem', // Adjust font size if needed
                                    whiteSpace: 'nowrap' // Prevent text from wrapping
                                }}
                                onClick={handleSaveAndNew}
                            >
                                Update & New
                            </Button>}
                            {(salary?.status === 1 || !id) && <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleSave}
                            >
                                Update
                            </Button>}
                        </>
                    ) : null : (
                        // Buttons for Create Mode
                        <>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleSaveAndNew}
                            >
                                Save & New
                            </Button>
                            <Button
                                variant="outlined"
                                size="small"
                                sx={{ maxWidth: '100px', width: '100%', mx: 0.5, color: 'green', borderColor: 'green', mt: 3, textTransform: 'none' }}
                                onClick={handleSave}
                            >
                                Save
                            </Button>
                        </>
                    )}
                    <Button
                        variant="outlined"
                        size="small"
                        sx={{ maxWidth: '100px', width: '100%', mx: 1.5, color: 'red', borderColor: 'red', mt: 3, textTransform: 'none' }}
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                </Grid>
                <Grid item xs={12}>
                    <Box
                        sx={{
                            backgroundColor: '#f0f0f0',
                            padding: '1rem 3rem',
                            borderRadius: '4px',
                            mb: 2,
                        }}
                    >
                        <h2>Salary Details</h2>
                    </Box>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '1rem', width: '100%' }}>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="employee_full_name"
                            label="Employee Full Name"
                            value={formData.employee_full_name}
                            onChange={handleChange}
                            fullWidth
                            isRequired
                            isDisabled
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="gross_salary"
                            label="Gross Amount"
                            value={formData.gross_salary}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.gross_salary}
                            isRequired
                            isDisabled={salary?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="pf_amount"
                            label="PF"
                            value={formData.pf_amount}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.pf_amount}
                            isDisabled={salary?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="esic_amount"
                            label="ESIC"
                            value={formData.esic_amount}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.esic_amount}
                            isDisabled={salary?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="insurance_amount"
                            label="Insurance"
                            value={formData.insurance_amount}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.insurance_amount}
                            isDisabled={salary?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="deductible_amount"
                            label="Deductible Amount"
                            value={formData.deductible_amount}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.deductible_amount}
                            isRequired
                            isDisabled={salary?.status === 0 || isViewMode}

                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="tenure"
                            label="Tenure(Months)"
                            value={formData.tenure}
                            onChange={handleChange}
                            fullWidth
                            helperText={errors.tenure}
                            isRequired
                            isDisabled={salary?.status === 0 || isViewMode}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <FormControl fullWidth>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    name='deductible_end_date'
                                    label="Deductible End Date"
                                    value={formData?.deductible_end_date ? dayjs(formData.deductible_end_date) : null}
                                    format="DD/MM/YYYY"
                                    disabled
                                />
                            </LocalizationProvider>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                        <CustomTextField
                            name="net_salary"
                            label="Net Payable"
                            value={formData.net_salary}
                            onChange={handleChange}
                            helperText={errors.net_salary}
                            fullWidth
                            isRequired
                            isDisabled
                        />
                    </Grid>
                </Box>
            </Grid>
        </form>
    );
}

export default SalaryForm;