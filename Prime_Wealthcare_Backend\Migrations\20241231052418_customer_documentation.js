exports.up = function (knex) {
    return knex.schema.createTable('customer_documents', function (table) {
        table.increments('id').primary();
        // Foreign key reference to customer_personal_info
        table.integer('customer_id').unsigned().references('id').inTable('customer_personal_info').notNullable();

        table.integer('member_id').unsigned().references('id').inTable('customer_member_info').nullable();

        // Document type (passport, driving license, etc.)
        table.integer('document_type_id').unsigned().references('id').inTable('pick_list').notNullable();

        // Document details
        table.string('document_id').notNullable();  // For document number
        table.string('document_path').notNullable(); // For storing document file path/url

        // Common Fields
        table.boolean('status').notNullable().defaultTo(true);
        table.integer('created_by').notNullable().defaultTo(1);
        table.integer('updated_by').notNullable().defaultTo(1);

        // Timestamps
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('customer_documents');
};
