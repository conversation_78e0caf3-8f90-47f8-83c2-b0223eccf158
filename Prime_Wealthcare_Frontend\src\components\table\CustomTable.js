import React, { useState, useEffect } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  Paper, TablePagination, Checkbox, IconButton, tableCellClasses, Typography,
  Box
} from '@mui/material';
import { styled } from '@mui/material/styles';
import EditIcon from '@mui/icons-material/Edit';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import RedEyeIconButton from '../eyeIconComponent';

function CustomTable({
  data = [], columns = [], onEdit, onDelete, onReinstate, onView,
  onSelectionChange, selectedRows, onSelectAll, showActions = true,
  hideDelete = false, showEyeIcon = false, handleViewData,
  isCheckboxRequired = true, isViewEnabled = true
}) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deletedIds, setDeletedIds] = useState(() => {
    const savedIds = localStorage.getItem('deletedIds');
    return savedIds ? JSON.parse(savedIds) : [];
  });

  useEffect(() => {
    localStorage.setItem('deletedIds', JSON.stringify(deletedIds));
  }, [deletedIds]);

  const StyledTableCell = styled(TableCell)(({ theme, status }) => ({
    [`&.${tableCellClasses.head}`]: {
      backgroundColor: "#528A7E",
      color: theme.palette.common.white,
      padding: '8px',
      textAlign: 'center',
    },
    [`&.${tableCellClasses.body}`]: {
      fontSize: 14,
      color: status === 0 ? 'red' : 'inherit',
      borderBottom: 'none',
      padding: '8px',
      textAlign: 'center',
    },
    width: `${100 / (columns.length + (isCheckboxRequired ? 1 : 0) + (showActions ? 1 : 0))}%`,
    '&.actions': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      gap: '8px',
    }
  }));

  const StyledTableRow = styled(TableRow)(({ theme, status }) => ({
    '&:nth-of-type(odd)': {
      backgroundColor: theme.palette.action.hover,
    },
    '&:last-child td, &:last-child th': {
      border: 0,
    },
  }));

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  };

  const handleSelectAll = (event) => {
    onSelectAll(event.target.checked);
  };

  const handleDelete = (id) => {
    onDelete(id);
    setDeletedIds((prev) => [...prev, id]);
  };

  const handleReinstate = (id) => {
    onReinstate(id);
    setDeletedIds((prev) => prev.filter(deletedId => deletedId !== id));
  };

  return (
    <Box sx={{ position: 'relative', width: '100%' }}>
      <TableContainer component={Paper} sx={{ width: '100%', marginInline: 'auto', paddingTop: '1rem' }}>
        <Table sx={{ tableLayout: 'auto' }} aria-label="customized table">
          <TableHead>
            <TableRow>
              {isCheckboxRequired && (
                <StyledTableCell padding="checkbox">
                  <Checkbox
                    onChange={handleSelectAll}
                    checked={data.length > 0 && selectedRows?.length === data.length}
                  />
                </StyledTableCell>
              )}
              {columns.map((column) => (
                <StyledTableCell key={column.field}>
                  {column.headerName}
                </StyledTableCell>
              ))}
              {showActions && <StyledTableCell>Action</StyledTableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.isArray(data) && data.length > 0 ? (
              data.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((item) => (
                <StyledTableRow key={item.id} status={item.status}>
                  {isCheckboxRequired && (
                    <StyledTableCell padding="checkbox">
                      <Checkbox
                        checked={selectedRows.includes(item.id)}
                        onChange={() => onSelectionChange(item.id)}
                      />
                    </StyledTableCell>
                  )}
                  {columns.map((column) => (
                    <StyledTableCell key={column.field} status={item.status} sx={{ margin: 'auto' }}>
                      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                        {column.renderCell ? column.renderCell({ row: item }) : item[column.field] || "N/A"}
                      </Box>
                    </StyledTableCell>
                  ))}
                  {showActions && (
                    <StyledTableCell className="actions" sx={{ margin: 'auto' }}>
                      {handleViewData && item.status !== 0 && (
                        <IconButton onClick={() => handleViewData(item.id)} sx={{ color: 'blue' }}>
                          <RemoveRedEyeOutlinedIcon />
                        </IconButton>
                      )}
                      {item.status === 0 ? (
                        <>
                          {isViewEnabled && onEdit && (
                            <IconButton onClick={() => onEdit(item.id)} sx={{ color: 'green', padding: '8px' }}>
                              <RemoveRedEyeOutlinedIcon />
                            </IconButton>
                          )}
                          {showEyeIcon && !deletedIds.includes(item.id) && (
                            <RedEyeIconButton onClick={() => onView(item.id)} />
                          )}
                          <IconButton onClick={() => handleReinstate(item.id)} sx={{ color: 'blue', padding: '8px' }}>
                            <RefreshIcon />
                          </IconButton>
                        </>
                      ) : (
                        <>
                          {showEyeIcon && !deletedIds.includes(item.id) && (
                            <RedEyeIconButton onClick={() => onView(item.id)} />
                          )}
                          {onEdit && (
                            <IconButton onClick={() => onEdit(item.id)} sx={{ color: 'green', padding: '8px' }}>
                              <EditIcon />
                            </IconButton>
                          )}
                          {!hideDelete && onDelete && (
                            <IconButton onClick={() => handleDelete(item.id)} sx={{ color: 'red', padding: '8px' }}>
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </>
                      )}
                    </StyledTableCell>
                  )}
                </StyledTableRow>
              ))
            ) : (
              <StyledTableRow>
                <StyledTableCell colSpan={columns.length + (showActions ? 2 : 1)}>
                  <Typography variant="body1" align="center" sx={{ padding: '1rem' }}>
                    No data present
                  </Typography>
                </StyledTableCell>
              </StyledTableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Box sx={{
        position: 'sticky',
        right: 0,
        bottom: 0,
        width: '100%',
        backgroundColor: 'white',
        borderTop: '1px solid rgba(224, 224, 224, 1)',
        display: 'flex',
        justifyContent: 'flex-end'
      }}>
        <TablePagination
          rowsPerPageOptions={[10, 15, 20]}
          component="div"
          count={Array.isArray(data) ? data.length : 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Box>
    </Box>
  );
}

export default CustomTable;