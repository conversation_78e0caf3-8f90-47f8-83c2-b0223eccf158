const EmployeeMaster = require('../Models/employee_bank_details_model');

// Create new Employee
exports.createEmployeeBankDetails = async (req, res, next) => {
    try {
        const EmployeeData = {
            ...req.body,
            // Set optional fields to null if they're empty or 0
            bank_name_2: req.body.bank_name_2 || null,
            account_holder_name_2: req.body.account_holder_name_2 || null,
            account_number_2: req.body.account_number_2 || null,
            IFSC_code_2: req.body.IFSC_code_2 || null,
            account_Type_2: req.body.account_Type_2 || null,
            branch_name_2: req.body.branch_name_2 || null,
            canceled_cheque_2: req.body.canceled_cheque_2 || null
        };
        await EmployeeMaster.create(EmployeeData);
        res.status(200).json({ message: 'Employee BankDetails created successfully' });
    } catch (error) {
        next(error);
    }
};

// Get all Employee
exports.getEmployeeBankDetails = async (req, res, next) => {
    try {
        const data = await EmployeeMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get Employee by ID
exports.getEmployeeBankDetailsById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const Employee = await EmployeeMaster.findById(id);
        if (Employee) {
            res.status(200).json(Employee);
        } else {
            res.status(404).json({ message: 'Employee BankDetails not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Get Employee by ID
exports.getEmployeeBankDetailsByEmployeeId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const Employee = await EmployeeMaster.findByEmployeeId(id);
        // console.log('this is the employee BankDetails', Employee)
        res.status(200).json(Employee);
        // if (Employee) {
        // } else {
        //     res.status(404).json({ message: 'Employee not found' });
        // }
    } catch (error) {
        next(error);
    }
};
// Update Employee by ID
exports.updateEmployeeBankDetails = async (req, res, next) => {
    try {
        // console.log('this is the employee BankDetails controller', req.body)
        const { id } = req.params;
        const EmployeeData = req.body;
        const data = await EmployeeMaster.update(id, EmployeeData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
// Delete a role by ID
exports.deleteEmployeeBankDetails = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await EmployeeMaster.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'BankDetails not found' });
        }
        res.status(204).json({ message: 'group deleted successfully' });
    } catch (error) {
        next(error);
    }
};
exports.deleteFirstBankById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await EmployeeMaster.deleteFirstBankById(id);
        res.status(204).json({ message: 'First Bank deleted successfully' });
    } catch (error) {
        next(error);
    }
};
exports.deleteSecondBankById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await EmployeeMaster.deleteSecondBankById(id);
        res.status(204).json({ message: 'Second Bank deleted successfully' });
    } catch (error) {
        next(error);
    }
};
