exports.up = function (knex) {
    return knex.schema.createTable('pa_responses', function (table) {
        table.increments('id').primary();
        table.integer('pa_quotation_id').unsigned().references('pa_quotation_id').inTable('pa_quotations');
        // Policy-level details
        table.integer('duration').nullable();
        table.decimal('full_payment_premium', 10, 2).nullable();
        table.decimal('full_payment_tax', 10, 2).nullable();
        table.decimal('total_full_payment', 10, 2).nullable();
        table.decimal('family_discount_perc', 10, 2).nullable();
        table.decimal('family_discount_amt', 10, 2).nullable();
        table.decimal('long_term_discount_percent', 10, 2).nullable();
        table.decimal('long_term_discount_amount', 10, 2).nullable();
        // Response metadata
        table.string('status').defaultTo('PENDING');
        table.timestamps(true, true);
    });
};

exports.down = function (knex) {
    return knex.schema.dropTable('pa_responses');
};