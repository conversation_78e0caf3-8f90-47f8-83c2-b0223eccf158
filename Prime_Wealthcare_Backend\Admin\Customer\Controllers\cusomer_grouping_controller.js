const CustomerMaster = require('../Models/customer_grouping_model');

// Create new Customer
exports.createCustomerGrouping = async (req, res, next) => {
    try {
        const customerData = req.body;
        await CustomerMaster.create(customerData);
        res.status(200).json({ message: 'Customer grouping created successfully' });
    } catch (error) {
        next(error);
    }
};

// Get all Customer
exports.getCustomerGrouping = async (req, res, next) => {
    try {
        const data = await CustomerMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get customer by ID
exports.getCostomerGroupingById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMaster.findById(id);
        if (customer) {
            res.status(200).json(customer);
        } else {
            res.status(404).json({ message: 'customer not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Get customer by ID
exports.getCostomerGroupingByCustomerId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMaster.findByCustomerId(id);
        if (customer) {
            res.status(200).json(customer);
        } else {
            res.status(404).json({ message: 'customer not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Update customer by ID
exports.updateCustomerGrouping = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customerData = req.body;
        const data = await CustomerMaster.update(id, customerData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
// Delete a role by ID
exports.deleteCustomerGrouping = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await CustomerMaster.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'grouping not found' });
        }
        res.status(204).json({ message: 'group deleted successfully' });
    } catch (error) {
        next(error);
    }
};