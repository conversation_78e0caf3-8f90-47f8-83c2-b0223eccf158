const AgentDetails = require('../Models/agentDetails'); // assuming this is the model for the agents_details table
const uploadDir = process.env.UPLOAD_DIR;

// Get all agents
exports.getAllAgents = async (req, res) => {
    try {
        const data = await AgentDetails.findAll();
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching agents', error });
    }
};

// Get agent by ID
exports.getAgentById = async (req, res) => {
    try {
        const id = req.params.id;
        const data = await AgentDetails.findById(id);
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching agent', error });
    }
};

exports.getAgentByBranchName = async (req, res) => {
    try {
        const branch_id = req.params.id;
        const data = await AgentDetails.getAgentByBranchName(branch_id);
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching agents', error });
    }
}

exports.getAgentByIdForEdit = async (req, res) => {
    try {
        const id = req.params.id;
        const data = await AgentDetails.findByIdForEdit(id);
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching agent', error });
    }
};

exports.getAgentBySearch = async (req, res) => {
    try {
        const { search } = req.params;
        const data = await AgentDetails.findByName(search);

        res.status(200).json(data);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching agent by search', error });
    }
}

// Create a new agent
exports.createAgent = async (req, res) => {
    try {
        const data = req.body;
        const files = req.files; // Access uploaded files

        // Log the received files for debugging
        // If you need to store file paths in the database, you can do so here
        if (files.aadhar_card_front) {
            const path = files.aadhar_card_front[0].path;
            data.aadhar_card_front = path.replace(uploadDir, '');
        }
        if (files.aadhar_card_back) {
            const path = files.aadhar_card_back[0].path;
            data.aadhar_card_back = path.replace(uploadDir, '');
        }
        if (files.driving_license_card) {
            const path = files.driving_license_card[0].path;
            data.driving_license_card = path.replace(uploadDir, '');
        }
        if (files.pan_card) {
            const path = files.pan_card[0].path;
            data.pan_card = path.replace(uploadDir, '');
        }
        if (files.photo) {
            const path = files.photo[0].path;
            data.photo = path.replace(uploadDir, '');
        }
        if (files.signed_offer_letter_card) {
            const path = files.signed_offer_letter_card[0].path || '';
            data.signed_offer_letter_card = path.replace(uploadDir, '');
        }
        // Proceed with creating the agent using the modified data
        const result = await AgentDetails.create(data);
        res.status(201).json(result);
    } catch (error) {
        console.error('Error creating agent:', error);
        res.status(500).json({ message: 'Error creating agent', error });
    }
};

// Update agent by ID
exports.updateAgent = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const files = req.files; // Access uploaded files

        // Handle file uploads consistently with create method
        if (files.aadhar_card_front) {
            const path = files.aadhar_card_front[0].path;
            data.aadhar_card_front = path.replace(uploadDir, '');
        }
        if (files.aadhar_card_back) {
            const path = files.aadhar_card_back[0].path;
            data.aadhar_card_back = path.replace(uploadDir, '');
        }
        if (files.driving_license_card) {
            const path = files.driving_license_card[0].path;
            data.driving_license_card = path.replace(uploadDir, '');
        }
        if (files.pan_card) {
            const path = files.pan_card[0].path;
            data.pan_card = path.replace(uploadDir, '');
        }
        if (files.photo) {
            const path = files.photo[0].path;
            data.photo = path.replace(uploadDir, '');
        }
        if (files.signed_offer_letter_card) {
            const path = files.signed_offer_letter_card[0].path || '';
            data.signed_offer_letter_card = path.replace(uploadDir, '');
        }
        await AgentDetails.updateById(id, data);
        res.status(200).json({ message: 'Agent updated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error updating agent', error });
    }
};

// Delete (deactivate) agent by ID
exports.deleteAgent = async (req, res) => {
    try {
        const id = req.params.id;
        await AgentDetails.softDeleteById(id);
        res.status(200).json({ message: 'Agent deactivated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deactivating agent', error });
    }
};

// Reinstate an agent by ID
exports.reinstateAgent = async (req, res) => {
    try {
        const id = req.params.id;
        const result = await AgentDetails.reinstateById(id);
        if (result) {
            res.status(200).json({ message: 'Agent reinstated successfully' });
        } else {
            res.status(404).json({ message: 'Agent not found' });
        }
    } catch (error) {
        res.status(500).json({ message: 'Error reinstating agent', error });
    }
};

// Get agents by specific criteria (new, deactivated, edited)
exports.getAgentsByCriteria = async (req, res) => {
    try {
        const criteria = req.params.criteria;
        let data;
        switch (criteria) {
            case 'none':
                data = await AgentDetails.findAll();
                break;
            case 'newLastWeek':
                data = await AgentDetails.newLastWeek();
                break;
            case 'newThisWeek':
                data = await AgentDetails.newThisWeek();
                break;
            case 'deactivatedThisWeek':
                data = await AgentDetails.deactivatedThisWeek();
                break;
            case 'deactivatedLastWeek':
                data = await AgentDetails.deactivatedLastWeek();
                break;
            case 'editedThisWeek':
                data = await AgentDetails.editedThisWeek();
                break;
            case 'editedLastWeek':
                data = await AgentDetails.editedLastWeek();
                break;
            case 'allActive':
                data = await AgentDetails.allActive();
                break;
            case 'allInactive':
                data = await AgentDetails.allInactive();
                break;
            default:
                return res.status(400).json({ message: 'Invalid criteria' });
        }

        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching agents', error });
    }
};