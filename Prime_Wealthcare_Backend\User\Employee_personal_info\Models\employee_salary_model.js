const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');

exports.create = async (data) => {
    try {
        const result = await db('employee_salary').insert(data);
        if (result) {
            await db('employee_salary')
                .where({ employee_id: data.employee_id })
                .whereNot('id', result[0])
                .update({ status: 0, updated_at: getCurrentTimestamp() });
        }
        return result;
    } catch (error) {
        console.log('Error creating the salary for employee ', error);
        throw error;
    }
}

exports.getAll = async (id) => {
    try {
        console.log('getting all the data');
        const Salaries = ((await db('employee_salary').where('employee_id', id).orderBy('created_at', 'desc')));
        return Salaries;    } catch (error) {
        console.error('Error retrieving Employee Salary:', error);
        throw error;
    }
}

exports.findByEmployeeId = async (id) => {
    try {
        const Employee = await db('employee_salary').where({ employee_id:id, status: 1 }).first();
        return Employee;
    } catch (error) {
        console.error('Error finding the data ', error);
        throw error;
    }
}

exports.findById = async (id) => {
    try {
        const Employee = await db('employee_salary').where({id}).first();
        return Employee;
    } catch (error) {
        console.error('Error finding the data ', error);
        throw error;
    }
}

exports.update = async (id, data) => {
    try {
        data.updated_at = getCurrentTimestamp();
        return await db('employee_salary').where({ employee_id: id, status : 1}).update(data);
    } catch (error) {
        console.error('Error updating the data ', error);
        throw error;
    }
}

exports.delete = async (id) => {
    try {
        return await db('employee_salary').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
    } catch (error) {
        console.error('Error deleting the data ', error);
        throw error;
    }
}