.Navbar {
    position: sticky;
    top: 0;
    z-index: 1100;
    background-color: #fff;

    .navbar-upper-section {
        display: flex;
        justify-content: space-between;
        padding: 1.25rem 5%;
        align-items: center;
        max-width: 1440px;
        margin: 0 auto;

        .company-logo {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .desktop-logo {
            height: 4rem;
            width: auto;
        }

        .mobile-logo {
            height: 2.5rem;
            width: auto;
        }

        .upper-section-right-side {
            display: flex;
            align-items: center;
            gap: 1.875rem;
            margin-left: auto;

            .upper-section-icon {
                width: 1.875rem;
                height: 1.875rem;
                object-fit: contain;
                cursor: pointer;
            }

            .user-name {
                color: #4c5157;
                font-family: "Poppins", sans-serif;
                font-size: 1.125rem;
                font-weight: 600;
                white-space: nowrap;
            }
        }
    }

    .navbar-lower-section {
        display: flex;
        align-items: center;
        background-color: #528a7e;
        gap: 0.5rem;
        padding: 0 1rem;

        .accordion {
            display: flex;
            flex-direction: column;
            background-color: #528a7e;
            color: #fff;
            box-shadow: none;
            min-width: min-content;

            .accordion-summary {
                min-height: 3.75rem;
                padding: 0;

                .accordion-summary-detail {
                    display: flex;
                    align-items: center;

                    .accordion-title {
                        font-family: "Poppins", sans-serif;
                        font-size: 1.15rem;
                        font-weight: 500;
                        white-space: nowrap;
                        text-transform: capitalize;
                    }

                    .expand-more-icon {
                        transition: transform 0.3s ease;
                    }
                }

                &:hover .expand-more-icon {
                    transform: rotate(180deg);
                }
            }

            .border {
                border-top: 2px solid #ddf2ed;
                border-bottom: 2px solid #ddf2ed;
            }

            .accordion-details-container {
                position: absolute;
                overflow-x: visible;
                border-radius: 0.625rem;
                z-index: 1000;
                border: 1px solid #ddf2ed;
                border-top: 2px solid #ddf2ed;
                background-color: #528a7e;

                .accordion-details {
                    background-color: #528a7e;
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    padding: 4px;
                    border-radius: 0.625rem;

                    .accordion-child-title {
                        font-family: "Poppins", sans-serif;
                        font-size: 1rem;
                        color: #fff;
                        cursor: pointer;
                        white-space: nowrap;
                        border-radius: 0.625rem;
                        padding: 0.5rem 1rem;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
        }

        .nine-dots {
            position: relative;

            .accordion-details-container {
                position: absolute;
                max-height: 65vh;
                border-radius: 0.625rem;
                z-index: 1000;
                border: 1px solid #ddf2ed;

                .child-accordion {
                    position: relative;
                    padding-inline: 0.5rem;

                    .accordion-summary {
                        display: flex;

                        .accordion-summary-detail {
                            display: flex;
                            justify-content: space-between;
                            width: 100%;
                        }

                        .expand-more-icon {
                            transform: rotate(-90deg);
                        }

                        &:hover .expand-more-icon {
                            transform: rotate(90deg);
                        }
                    }

                    .child-accordion-details-container {
                        position: absolute;
                        left: 102%;
                        top: 0;
                        border: 1px solid #ddf2ed;
                        border-top: 2px solid #ddf2ed;
                        border-radius: 0.625rem;
                        background-color: #528a7e;
                        overflow-y: auto;
                        overflow-x: hidden;
                        max-height: 65vh;
                        width: auto;

                        &::-webkit-scrollbar {
                            width: 8px;
                            overflow-x: hidden;
                        }

                        &::-webkit-scrollbar-track {
                            border-radius: 30px;
                            background: #528a7e;
                        }

                        &::-webkit-scrollbar-thumb {
                            background-color: #2c4a45;
                            border-radius: 10px;
                        }
                    }

                    &:hover {
                        background-color: #629d90;
                    }
                }
            }
        }
    }
}

.nested-accordion {
    box-shadow: none !important;
    background: transparent !important;
    margin-left: 1rem !important;

    .accordion-summary {
        min-height: 40px !important;
    }

    .accordion-child-title {
        font-size: 0.9rem;
    }
}

.nested-accordion-details-container {
    padding-left: 1rem;
}

.nested-menu-item {
    position: relative;
    width: 100%;

    .nested-parent {
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        .nested-arrow {
            transform: rotate(-90deg);
            font-size: 1.2rem;
        }
    }

    .nested-children-container {
        position: absolute;
        left: 101%;
        top: 0;
        border: 1px solid #ddf2ed;
        border-top: 2px solid #ddf2ed;
        border-radius: 0.625rem;
        background-color: #528a7e;

        .accordion-details {
            padding: 8px 16px;

            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
            }
        }
    }
}

.accordion-details {
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }
}

.nested-parent {
    .accordion-summary-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }
}

.arrow,
.nested-arrow {
    transition: transform 0.3s ease;
}

.rotated {
    transform: rotate(180deg);
}