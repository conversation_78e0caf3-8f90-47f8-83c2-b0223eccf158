const EmployeeLoan = require('../Models/employeeLoan');

// Get all employee loans
exports.getAllLoans = async (req, res) => {
    try {
        const data = await EmployeeLoan.findEmployeeLoans();
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching loans', error });
    }
};

// Get a loan by ID
exports.getLoanById = async (req, res) => {
    try {
        const id = req.params.id;
        const data = await EmployeeLoan.findEmployeeLoanById(id);
        if (!data) {
            return res.status(404).json({ message: 'Loan not found' });
        }
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching loan', error });
    }
};

// Create a new employee loan
exports.createLoan = async (req, res) => {
    try {
        const loanData = req.body;
        const result = await EmployeeLoan.create(loanData);
        res.status(201).json({ loanId: result });
    } catch (error) {
        res.status(500).json({ message: 'Error creating loan', error });
    }
};

// Update a loan by ID
exports.updateLoan = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const updated = await EmployeeLoan.updateLoan(id, data);
        if (updated === 0) {
            return res.status(404).json({ message: 'Loan not found' });
        }
        res.status(200).json({ message: 'Loan updated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error updating loan', error });
    }
};

// Soft delete a loan by ID
exports.deleteLoan = async (req, res) => {
    try {
        const id = req.params.id;
        const deleted = await EmployeeLoan.deleteLoan(id);
        if (deleted === 0) {
            return res.status(404).json({ message: 'Loan not found' });
        }
        res.status(200).json({ message: 'Loan deactivated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deactivating loan', error });
    }
};

// Get all EMIs related to a given loan ID
exports.getLoanEmi = async (req, res) => {
    try {
        const loanId = req.params.loanId;
        const data = await EmployeeLoan.findEmployeeLoanEmi(loanId);
        res.status(200).json(data);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching EMIs', error });
    }
};

// Update EMI by ID
exports.updateEmi = async (req, res) => {
    try {
        const id = req.params.id;
        const data = req.body;
        const updated = await EmployeeLoan.updateEmi(id, data);
        if (updated === 0) {
            return res.status(404).json({ message: 'EMI not found' });
        }
        res.status(200).json({ message: 'EMI updated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error updating EMI', error });
    }
};

// Soft delete an EMI by ID
exports.deleteEmi = async (req, res) => {
    try {
        const id = req.params.id;
        const deleted = await EmployeeLoan.deleteEmi(id);
        if (deleted === 0) {
            return res.status(404).json({ message: 'EMI not found' });
        }
        res.status(200).json({ message: 'EMI deactivated successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deactivating EMI', error });
    }
};
