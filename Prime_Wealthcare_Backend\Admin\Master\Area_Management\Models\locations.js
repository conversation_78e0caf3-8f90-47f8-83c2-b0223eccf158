const { json } = require('express');
const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Retrieve all locations
const getAllLocations = async () => {
    try {
        const locations = await db('locations')
            .leftJoin('areas', function () {
                this.on('locations.pincode', '=', 'areas.pincode')
                    .andOn('locations.city', '=', 'areas.city');
            })
            .select(
                db.raw("CONCAT(locations.id, '-', locations.city, '-', COALESCE(areas.area, '')) AS id"), // Combined ID, city, and area
                db.raw("COALESCE(areas.area, NULL) AS area_name"), // Sub-area name, NULL if not present
                'locations.city AS city_name',   // City name
                'locations.pincode AS location_pincode', // Location pincode
                'locations.state',               // State name
                'locations.status'               // Status
            )
        return locations;
    } catch (error) {
        console.error('Error fetching locations:', error);
        throw error;
    }
};

const getLocationById = async (locationId) => {
    try {
        const results = await db('locations')
            // .select('locations.id', 'locations.pincode', 'locations.city', 'locations.state', 'locations.status')
            .select('*')
            .where('locations.id', locationId);

        if (results.length === 0) return null;

        const location = {
            id: results[0].location_id,
            pincode: results[0].pincode,
            area: results[0].area,
            city: results[0].city,
            state: results[0].state,
            status: results[0].location_status,
        };
        return location;
    } catch (error) {
        console.error(`Error fetching location for id: ${locationId}`, error);
        throw error;
    }
};

// Insert a new location
const insertLocation = async (data) => {
    try {
        const newData = { ...data, pincode_city: `${data.pincode}${data.city.replace(/\s+/g, '')}` };
        const newLocation = await db('locations').insert(newData);
        return newLocation;
    } catch (error) {
        console.error('Error inserting location:', error);
        throw error;
    }
};

// Update a location by pincode
const updateLocation = async (pincode, data) => {
    data.updated_at = getCurrentTimestamp();
    try {
        await db('locations').where({ pincode }).update(data);
    } catch (error) {
        console.error(`Error updating location with pincode: ${pincode}`, error);
        throw error;
    }
};

// Delete a location by pincode (mark as inactive)
const deleteLocation = async (data) => {
    try {

        await db('locations').where({ pincode: data.pincode, city: data.city }).del();
    } catch (error) {
        console.error(`Error deleting location with pincode: ${data.pincode} and city: ${data.city}`, error);
        throw error;
    }
};

// Get location by pincode
const getLocationByPincode = async (pincode) => {
    try {
        const location = await db('locations')
            .where('pincode', 'like', `%${pincode}%`)
        return location;
    } catch (error) {
        console.error(`Error fetching location with pincode: ${pincode}`, error);
        throw error;
    }
};

// Get location and sub-area by pincode
const getLocationAndSubAreaByPincode = async (pincode) => {
    try {
        const results = await db('locations')
            .leftJoin('areas', 'locations.pincode', '=', 'areas.pincode')
            .select('locations.id as location_id', 'locations.pincode', 'locations.city', 'locations.state', 'locations.status as location_status',
                'areas.id as area_id', 'areas.area', 'areas.status as area_status')
            .where('locations.pincode', pincode);

        if (results.length === 0) return null;

        const location = {
            id: results[0].location_id,
            pincode: results[0].pincode,
            area: results[0].area,
            city: results[0].city,
            state: results[0].state,
            status: results[0].location_status,
            areas: []
        };

        results.forEach(row => {
            if (row.area_id) {
                location.areas.push({
                    id: row.area_id,
                    area: row.area,
                    status: row.area_status
                });
            }
        });

        return location;
    } catch (error) {
        console.error(`Error fetching location and sub-areas for pincode: ${pincode}`, error);
        throw error;
    }
};

// Get location by pincode and city
const getLocationByPincodeAndCity = async (pincode, city) => {
    try {


        // Remove spaces from the city string
        const formattedCity = city.replace(/\s+/g, '');

        // Log the formatted city


        // Fetch location by pincode first, then filter by formatted city
        const location = await db('locations')
            .where({ pincode })
            .andWhereRaw('REPLACE(city, " ", "") = ?', [formattedCity])
            .first();


        return location;
    } catch (error) {
        console.error(`Error fetching location with pincode: ${pincode} and city: ${city}`, error);
        throw error;
    }
};

const getLocationBySearch = async (query) => {
    try {
        const locations = await db('locations')
            .leftJoin('areas', function () {
                this.on('locations.pincode', '=', 'areas.pincode')
                    .andOn('locations.city', '=', 'areas.city');
            })
            .select(
                db.raw("CONCAT(locations.id, '-', locations.city, '-', COALESCE(areas.area, '')) AS id"),
                db.raw("COALESCE(areas.area, NULL) AS area_name"),
                'locations.city AS city_name',
                'locations.pincode AS location_pincode',
                'locations.state',
                'locations.status'
            )
            .where(function () {
                this.where('areas.area', 'LIKE', `%${query}%`)
                    .orWhere('locations.city', 'LIKE', `%${query}%`)
                    .orWhere('locations.pincode', 'LIKE', `%${query}%`)
                    .orWhere('locations.state', 'LIKE', `%${query}%`)
            });
        return locations;
    } catch (error) {
        console.error(`Error fetching locations with name: ${query}`, error);
        throw error;
    }
};

const newLastWeek = async () => {
    try {
        const locations = db('locations')
            .leftJoin('areas', function () {
                this.on('locations.pincode', '=', 'areas.pincode')
                    .andOn('locations.city', '=', 'areas.city');
            })
            .select(
                db.raw("CONCAT(locations.id, '-', locations.city, '-', COALESCE(areas.area, '')) AS id"),
                db.raw("COALESCE(areas.area, NULL) AS area_name"),
                'locations.city AS city_name',
                'locations.pincode AS location_pincode',
                'locations.state',
                'locations.status'
            )
            .whereBetween('locations.created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);

        return locations;
    } catch (error) {
        console.error('Error fetching locations created last week:', error);
        throw error;
    }
};

// Retrieve locations created this week
const newThisWeek = async () => {
    try {
        const locations = db('locations')
            .leftJoin('areas', function () {
                this.on('locations.pincode', '=', 'areas.pincode')
                    .andOn('locations.city', '=', 'areas.city');
            })
            .select(
                db.raw("CONCAT(locations.id, '-', locations.city, '-', COALESCE(areas.area, '')) AS id"),
                db.raw("COALESCE(areas.area, NULL) AS area_name"),
                'locations.city AS city_name',
                'locations.pincode AS location_pincode',
                'locations.state',
                'locations.status'
            )
            .whereBetween('locations.created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ]);

        return locations;
    } catch (error) {
        console.error('Error fetching locations created this week:', error);
        throw error;
    }
};

// Retrieve locations edited this week
const editedThisWeek = async () => {
    try {
        const locations = db('locations')
            .leftJoin('areas', function () {
                this.on('locations.pincode', '=', 'areas.pincode')
                    .andOn('locations.city', '=', 'areas.city');
            })
            .select(
                db.raw("CONCAT(locations.id, '-', locations.city, '-', COALESCE(areas.area, '')) AS id"),
                db.raw("COALESCE(areas.area, NULL) AS area_name"),
                'locations.city AS city_name',
                'locations.pincode AS location_pincode',
                'locations.state',
                'locations.status'
            )
            .whereBetween('locations.updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);

        return locations;
    } catch (error) {
        console.error('Error fetching edited locations updated this week:', error);
        throw error;
    }
};

// Retrieve locations edited last week
const editedLastWeek = async () => {
    try {
        const locations = db('locations')
            .leftJoin('areas', function () {
                this.on('locations.pincode', '=', 'areas.pincode')
                    .andOn('locations.city', '=', 'areas.city');
            })
            .select(
                db.raw("CONCAT(locations.id, '-', locations.city, '-', COALESCE(areas.area, '')) AS id"),
                db.raw("COALESCE(areas.area, NULL) AS area_name"),
                'locations.city AS city_name',
                'locations.pincode AS location_pincode',
                'locations.state',
                'locations.status'
            )
            .whereBetween('locations.updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);

        return locations;
    } catch (error) {
        console.error('Error fetching edited locations updated last week:', error);
        throw error;
    }
};

module.exports = {
    getAllLocations,
    insertLocation,
    updateLocation,
    getLocationById,
    deleteLocation,
    getLocationByPincode,
    getLocationByPincodeAndCity,
    getLocationAndSubAreaByPincode,
    getLocationBySearch,
    newLastWeek,
    newThisWeek,
    editedThisWeek,
    editedLastWeek,
};
