const knexConfig = require('../../../../knexfile');
const { getCurrentTimestamp } = require('../../../../Reusable/reusable');
const db = require('knex')(knexConfig.development);

// Create a new disease
const create = async (data) => {
    try {
        const { result } = await db('disease_master').insert(data);

        return result;
    } catch (error) {
        console.error("Error creating disease :", error);
        throw error;
    }
};

// Find all diseases
const findAll = async () => {
    try {
        return await db('disease_master').select('*');
    } catch (error) {
        throw error;
    }
};

// Find disease by ID
const findById = async (id) => {
    try {
        const disease = await db('disease_master').where({ id }).first();
        return disease;
    } catch (error) {
        throw error;
    }
};

// Find diseases by disease name (partial match)
const findByName = async (diseaseName) => {
    try {
        const diseases = await db('disease_master')
            .where('disease_name', 'like', `%${diseaseName}%`); // Ensure 'disease_name' is the correct column
        return diseases;
    } catch (error) {
        console.error("Error finding diseases by name:", error);
        throw error;
    }
};

const updateById = async (id, data) => {
    try {
        // Add the formatted updated_at field to the data object
        data.updated_at = getCurrentTimestamp();

        const result = await db('disease_master')
            .where({ id })
            .update(data);  // Use the passed data object for the update
        return result;
    } catch (error) {
        throw error;
    }
};



// Delete disease by ID (soft delete by updating status)
const deleteById = async (id) => {
    try {
        const result = await db('disease_master').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

// Reinstate disease by ID
const reinstate = async (id) => {
    try {
        const result = await db('disease_master').where({ id }).update({ status: 1, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

const newLastWeek = async () => {
    try {
        return await db('disease_master')
            .where('created_at', '<', db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'));
    } catch (error) {
        throw error;
    }
};

// New diseases created this week
const newThisWeek = async () => {
    try {
        const query = db('disease_master')
            .whereBetween('created_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 7 DAY)'),
                db.raw('NOW()')
            ]);



        return await query;
    } catch (error) {
        throw error;
    }
};

// Deactivated diseases updated this week
const deactivatedThisWeek = async () => {
    try {
        return await db('disease_master')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);
    } catch (error) {
        throw error;
    }
};

// Deactivated diseases updated last week
const deactivatedLastWeek = async () => {
    try {
        const query = db('disease_master')
            .where('status', 0)
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited diseases updated this week
const editedThisWeek = async () => {
    try {
        const query = db('disease_master')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)'),
                db.raw('NOW()')
            ]);

        return await query;
    } catch (error) {
        throw error;
    }
};

// Edited diseases updated last week
const editedLastWeek = async () => {
    try {
        return await db('disease_master')
            .whereBetween('updated_at', [
                db.raw('DATE_SUB(NOW(), INTERVAL 2 WEEK)'),
                db.raw('DATE_SUB(NOW(), INTERVAL 1 WEEK)')
            ]);
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    findAll,
    findById,
    findByName,
    updateById,
    deleteById,
    reinstate,
    newLastWeek,
    newThisWeek,
    deactivatedThisWeek,
    deactivatedLastWeek,
    editedThisWeek,
    editedLastWeek
};
