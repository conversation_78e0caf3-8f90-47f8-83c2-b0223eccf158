import React from 'react';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';

const CustomTextField = ({
    label,
    type,
    value,
    onChange,
    onFocus, // Add onFocus prop
    onBlur, // Add onBlur prop
    icon: IconComponent,
    applyPrefix,
    prefixSymbol,
    valuePlaceholder,
    isDisabled,
    isRequired,
    helperText,
    error,
    handleAdornmentClick,
    info,
    ...rest
}) => {
    return (
        <TextField
            label={label}
            type={type}
            value={value}
            onChange={onChange}
            onFocus={onFocus} // Call onFocus prop on focus
            onBlur={onBlur} // Call onBlur prop on blur
            variant="outlined"
            fullWidth
            disabled={isDisabled}
            helperText={helperText ? helperText : info} // Display 'info' if provided, else show 'helperText'
            InputProps={{
                startAdornment: (applyPrefix || prefixSymbol) && (
                    <InputAdornment position="start">
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            {applyPrefix && (
                                <>
                                    <span style={{ marginRight: '8px', color: '#888' }}>+91</span>
                                    <div
                                        style={{
                                            height: '40px',
                                            width: '1px',
                                            backgroundColor: 'grey',
                                            marginRight: '8px',
                                        }}
                                    />
                                </>
                            )}
                            {prefixSymbol && (
                                <span style={{ marginRight: '8px', color: '#888', backgroundColor: 'transparent' }}>{prefixSymbol}</span>
                            )}
                        </div>
                    </InputAdornment>
                ),
                endAdornment: IconComponent && (
                    <InputAdornment position="end">
                        <IconComponent onClick={handleAdornmentClick} cursor='pointer' />
                    </InputAdornment>
                ),
            }}
            InputLabelProps={{
                style: {
                    color: 'grey',
                    padding: '0 4px',
                },
            }}
            FormHelperTextProps={{
                style: {
                    color: helperText ? 'red' : 'green', // Green for info, red for required, default grey
                },
            }}
            sx={{
                zIndex: 100,
                '& .MuiOutlinedInput-root': {
                    backgroundColor: isDisabled ? 'rgba(0, 0, 0, 0.05)' : 'transparent', // Add darker background when disabled
                    '& fieldset': {
                        borderColor: 'grey', // Customize border color if needed
                    },
                    '&:hover fieldset': {
                        borderColor: 'green', // Customize border color on hover if needed
                    },
                    '&.Mui-focused fieldset': {
                        borderColor: 'green', // Customize border color when focused
                    },
                    '&:before': isRequired ? {
                        content: '""',
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '3px',
                        backgroundColor: 'red',
                        borderTopLeftRadius: '20px',
                        borderBottomLeftRadius: '20px',
                    } : {},
                },
                '& .MuiInputLabel-root': {
                    padding: '0 4px',
                    transition: 'all 0.2s ease-out',
                },
                '& .MuiInputLabel-shrink': {
                    transform: 'translate(14px, -6px) scale(0.75)', // Adjust label position when filled or focused
                },
                ...(isRequired && {
                    '& .MuiOutlinedInput-root:before': {
                        content: '""',
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '3px',
                        backgroundColor: 'red',
                        borderTopLeftRadius: '20px',
                        borderBottomLeftRadius: '20px',
                    }
                })
            }}
            {...rest}
        />
    );
};

export default CustomTextField;