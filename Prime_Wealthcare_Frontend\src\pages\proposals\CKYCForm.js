import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const CKYCForm = () => {
    const location = useLocation();

    // Parse state from URL parameters if present
    const queryParams = new URLSearchParams(location.search);
    const stateParam = queryParams.get('state');

    // Try to parse the state from URL parameter, fallback to location.state
    const stateData = stateParam ? JSON.parse(decodeURIComponent(stateParam)) : location.state;
    const { kycReqNo, icKycNo, returnUrl, kycUrl } = stateData || {};

    useEffect(() => {
        // Automatically submit the form when component mounts
        try {
            const form = document.getElementById('KYCFailedFormTag');
            if (form) {
                form.submit();
            }
        } catch (error) {
            console.error('Error submitting form:', error);
        }
    }, []);

    // Add error handling for missing data
    if (!stateData) {
        return <div>Error: Missing required parameters</div>;
    }

    return (
        <div>
            <table align="center" width="100%">
                <tbody>
                    <tr>
                        <td><strong>You are being redirected to KYC portal</strong></td>
                    </tr>
                    <tr>
                        <td><font color="blue">Please wait ...</font></td>
                    </tr>
                    <tr>
                        <td>(Please do not press 'Refresh' or 'Back' button)</td>
                    </tr>
                </tbody>
            </table>

            <form
                id="KYCFailedFormTag"
                method="post"
                name="redirect"
                action={kycUrl}
            >
                <input
                    type="hidden"
                    id="VISoF_KYC_Req_No"
                    name="VISoF_KYC_Req_No"
                    value={kycReqNo}
                />
                <input
                    type="hidden"
                    id="IC_KYC_No"
                    name="IC_KYC_No"
                    value={icKycNo}
                />
                <input
                    type="hidden"
                    name="VISoF_Return_URL"
                    id="VISoF_Return_URL"
                    value={returnUrl}
                />
                <input
                    id="KYCFailedForm"
                    type="submit"
                    form="KYCFailedFormTag"
                    value="Click me"
                />
            </form>
        </div>
    );
};

export default CKYCForm;