import React, { useState,useEffect } from 'react';
import { Grid, Box, Button, Typography } from '@mui/material';
import CustomTextField from '../../components/CustomTextField';
import MarkEmailUnreadOutlinedIcon from '@mui/icons-material/MarkEmailUnreadOutlined';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { forgotPassword } from '../../redux/actions/action';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const navigate = useNavigate(); 
  const dispatch = useDispatch();
  
  const { loading, error, otpStatus } = useSelector((state) => state.auth);


  const handleForgotPassword = () => {
    if (!email) {
      return alert('Please enter your registered email');
    }
    dispatch(forgotPassword({ email }))
      .catch((err) => {
        console.error('Failed to send OTP', err);
      });
  };

  // Navigate to OTP verification page when O<PERSON> is sent successfully
  useEffect(() => {
    if (otpStatus === 'OTP sent to your email.') {
      navigate('/otp-verification'); // Navigate when OTP is sent
    }
  }, [otpStatus, navigate]);

  return (
    <Grid container style={{ height: '100vh', overflow: 'hidden' }}>
      <Grid item xs={12} md={6} style={{ backgroundImage: `url(/background.png)`, backgroundSize: 'cover', backgroundPosition: 'left' }}>
      </Grid>

      <Grid
        item
        xs={12}
        md={6}
        display="flex"
        alignItems="center"
        justifyContent="center"
        style={{ backgroundColor: '#DDF2ED', padding: '12px', height: '100%' }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: 800,
            //padding: 2,
            borderRadius: 2,
            alignItems: 'center',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <img
              src="/logo1.png"
              alt="Logo"
              style={{ width: 250 }}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: 2 }}>
            <img
              src="/illustration.png"
              alt="Illustration"
              style={{ width: '50%' }}
            />
          </Box>

          <Box sx={{ textAlign: 'center', marginTop: 2}}>
            <Typography variant="h5" gutterBottom>
              Oops! You Forgot Password
            </Typography>
            <Typography variant="body2" gutterBottom>
              Please enter your registered email for recovery
            </Typography>
          </Box>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%',
              marginTop: 2,
            }}
          >
            <CustomTextField
              label="Enter Registered Email Id"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              margin="normal"
              icon={MarkEmailUnreadOutlinedIcon}
              sx={{ width: '100%', maxWidth: '400px' }}
            />
              {error && <Typography color="error">{error}</Typography>}
              {otpStatus && <Typography>OTP sent to your email.</Typography>}
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', marginTop: 4 ,
            pb: { xs: 1, md: 0 },
          }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleForgotPassword}
              sx={{ maxWidth: '250px', width: '100%', backgroundColor: '#1A6A62', padding: '12px 0' }}
            >
                {loading ? 'Sending...' : 'Send OTP'}
             
            </Button>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default ForgotPassword;
