import { createSlice } from '@reduxjs/toolkit';
import {
  fetchInsuranceCompanies,
  createInsuranceCompany,
  updateInsuranceCompany,
  deleteInsuranceCompany,
  fetchInsuranceCompanyById,
  reinstateInsuranceCompany,
  fetchInsuranceCompanyByName, // New action
  fetchInsuranceCompaniesByCriteria, // New action
} from '../../actions/action';

const insuranceCompanySlice = createSlice({
  name: 'insuranceCompanies',
  initialState: {
    insuranceCompanies: [],
    currentCompany: null,
    status: 'idle',
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    // Fetch all insurance companies
    builder
      .addCase(fetchInsuranceCompanies.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchInsuranceCompanies.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.insuranceCompanies = action.payload;
      })
      .addCase(fetchInsuranceCompanies.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Create a new insurance company
      .addCase(createInsuranceCompany.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createInsuranceCompany.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.insuranceCompanies.push(action.payload);
      })
      .addCase(createInsuranceCompany.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Update an existing insurance company
      .addCase(updateInsuranceCompany.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(updateInsuranceCompany.fulfilled, (state, action) => {
        const updatedCompany = action.payload;
        const index = state.insuranceCompanies.findIndex((company) => company.id === updatedCompany.id);
        if (index !== -1) {
          state.insuranceCompanies[index] = updatedCompany;
          state.status = 'succeeded';
        }
      })
      .addCase(updateInsuranceCompany.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Fetch a specific insurance company by ID
      .addCase(fetchInsuranceCompanyById.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchInsuranceCompanyById.fulfilled, (state, action) => {
        state.currentCompany = action.payload;
        state.status = 'succeeded';
      })
      .addCase(fetchInsuranceCompanyById.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Delete an insurance company
      .addCase(deleteInsuranceCompany.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(deleteInsuranceCompany.fulfilled, (state, action) => {
        state.insuranceCompanies = state.insuranceCompanies.filter((company) => company.id !== action.payload.id);
        state.status = 'succeeded';
      })
      .addCase(deleteInsuranceCompany.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Reinstate a deleted insurance company
      .addCase(reinstateInsuranceCompany.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(reinstateInsuranceCompany.fulfilled, (state, action) => {
        const reinstatedCompany = action.payload;
        if (reinstatedCompany && reinstatedCompany.id) {
          const index = state.insuranceCompanies.findIndex((company) => company.id === reinstatedCompany.id);

          if (index === -1) { // Only add if it doesn't already exist
            state.insuranceCompanies.push(reinstatedCompany);
            state.status = 'succeeded';
          }
        } else {
          state.status = 'failed';
          state.error = 'Invalid payload received';
        }
      })

      .addCase(reinstateInsuranceCompany.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Fetch insurance companies by name
      .addCase(fetchInsuranceCompanyByName.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchInsuranceCompanyByName.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.insuranceCompanies = action.payload; // Update with fetched data
      })
      .addCase(fetchInsuranceCompanyByName.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })

      // Fetch insurance companies by criteria
      .addCase(fetchInsuranceCompaniesByCriteria.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchInsuranceCompaniesByCriteria.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.insuranceCompanies = action.payload; // Update with fetched data
      })
      .addCase(fetchInsuranceCompaniesByCriteria.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload;
      })


  },
});

export default insuranceCompanySlice.reducer;