// CommissionRatesPage.jsx

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box, Container, Button, ButtonGroup, Grid,
  IconButton
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import { DataGrid } from '@mui/x-data-grid';
import TextField from '@mui/material/TextField';
import ModuleName from '../../../components/table/ModuleName';
import SearchBar from '../../../components/table/SearchBar';
import DropDown from '../../../components/table/DropDown';
import {
  fetchInsuranceCompanies,
  fetchCommissionRatesByCompany,
  updateMultipleCommissionRates, updateCommissionRate,
  searchCommissionRate
} from '../../../redux/actions/action';
import Popup from './PopUp';
import {
  markRowAsSaved, setAddButtonDisabled // Disable the button after saving
} from '../../../redux/slices/master/savedRowsSlice'; // Import the action
// import { markRowAsSaved , setAddButtonDisabled // Disable the button after saving
// } from '../../../redux/slices/Master/savedRowsSlice'; // Import the action
import { toast } from 'react-toastify';

const CommissionRatesPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const insuranceCompanies = useSelector(state => state.insuranceCompanyReducer.insuranceCompanies);
  const { loading, error } = useSelector((state) => state.commissionRateReducer);
  const savedRows = useSelector(state => state.savedRows.savedRows);
  //const isAddButtonDisabled = useSelector(state => state.savedRows.isAddButtonDisabled);


  const [selectedCompany, setSelectedCompany] = useState('');
  const [fixPercentValues, setFixPercentValues] = useState({});
  const [openPopup, setOpenPopup] = useState(false);
  const [currentRow, setCurrentRow] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [localCommissionRates, setLocalCommissionRates] = useState([]);
  const [isAddButtonDisabled, setIsAddButtonDisabled] = useState(false);

  useEffect(() => {
    dispatch(fetchInsuranceCompanies());
    dispatch(setAddButtonDisabled(false)); // Reset disabled state on mount
  }, [dispatch]);

  const handleCompanyChange = (e) => {
    setSelectedCompany(e.target.value);
  };

  const handleSubmit = () => {
    if (selectedCompany) {
      dispatch(fetchCommissionRatesByCompany(selectedCompany))
        .then((response) => {
          setLocalCommissionRates(response.payload);
        })
        .catch((error) => {
          console.error("Error fetching commission rates:", error);
          toast.error("Failed to fetch commission rates.");
        });
    }
  };

  const handleFixPercentChange = (id, value) => {
    const numericValue = value === '' ? '' : parseFloat(value);

    // Validate the input
    if (value === '' || (numericValue >= 0 && numericValue <= 99)) {
      setFixPercentValues((prevValues) => ({
        ...prevValues,
        [id]: numericValue
      }));
    }
  };

  const handleAddExtraClick = (row) => {
    const latestRow = localCommissionRates.find(r => r.id === row.id);
    if (latestRow) {
      setCurrentRow(latestRow);
      setOpenPopup(true);
    } else {
      console.error('Row not found in localCommissionRates:', row);
    }
  };

  const handleClosePopup = () => {
    setOpenPopup(false);
    setCurrentRow(null);
  };

  const handleSave = () => {
    const updates = Object.keys(fixPercentValues)
      .filter(id => fixPercentValues[id] !== undefined && fixPercentValues[id] !== '')
      .map(id => ({
        id: parseInt(id),
        fixed_percentage: parseFloat(fixPercentValues[id])
      }));

    if (updates.length > 0) {
      dispatch(updateMultipleCommissionRates(updates))
        .then((response) => {
          toast.success("Commission rates updated successfully");

          // Update localCommissionRates to reflect saved data
          const updatedRates = localCommissionRates.map(rate => {
            const updatedRate = updates.find(u => u.id === rate.id);
            return updatedRate ? { ...rate, fixed_percentage: updatedRate.fixed_percentage } : rate;
          });
          setLocalCommissionRates(updatedRates);
          

          // Optionally, navigate after a delay or keep the user on the page
           navigate('/dashboard/commission-rate-list');
        })
        .catch(err => {
          console.error('Error updating commission rates:', err);
          toast.error(`Failed to update commission rates: ${err.message}`);
        });
    } else {
      toast.warning("No changes made to save.");
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);

    if (query.trim() === "") {
      handleSubmit();
    } else {
      dispatch(searchCommissionRate(query))
        .then((response) => {
          setLocalCommissionRates(response.payload);
        })
        .catch((error) => {
          console.error("Error searching commission rates:", error);
          toast.error("Failed to search commission rates.");
        });
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/commission-rate-list');
  };

  const handlePopupSaveSuccess = (extraData) => {
    if (currentRow) {
      // Update the specific row with extraData
      const updatedRates = localCommissionRates.map(rate =>
        rate.id === currentRow.id
          ? {
              ...rate,
              ...extraData, // Merge extraData into the rate
            }
          : rate
      );
      setLocalCommissionRates(updatedRates);

      // Dispatch an action to update the Redux store with extraData
      dispatch( updateCommissionRate({ id: currentRow.id, ...extraData }));

      // Optionally, mark the row as saved if applicable
      dispatch(markRowAsSaved(currentRow.id));

      dispatch(setAddButtonDisabled(true)); // Disable the button after saving


      // Show success feedback
      toast.success("Extra data added successfully.");

      // Close the popup
      handleClosePopup();
    }
  };

  const filteredRows = localCommissionRates.filter(row => row.fixed_percentage <= 0);

  const renderFixedPercentage = (params) => (
    <TextField
      type="number"
      value={
        fixPercentValues[params.id] !== undefined
          ? fixPercentValues[params.id]
          : params.value || ''
      }
      onChange={(e) => handleFixPercentChange(params.id, e.target.value)}
      disabled={false}
      required
      sx={{ width: '80px', textAlign: 'center' }}
      inputProps={{
        min: 1, max: 99,
        style: {
          '-moz-appearance': 'textfield', // Firefox
          '-webkit-appearance': 'none',    // Safari and Chrome
          appearance: 'none',               // Other browsers
        },
      }}
      variant="standard"
    />
  );

  const renderAddOrEditButton = (params) => {
    const isSaved = savedRows.includes(params.row.id);
    return (
      <IconButton
        onClick={() => handleAddExtraClick(params.row)}
        sx={{ backgroundColor: '#528A7E', color: '#fff', '&:hover': { backgroundColor: '#3f6c62' } }}
        disabled={isSaved || isAddButtonDisabled}
      >
        <AddIcon />
      </IconButton>
    );
  };

  const columns = [
    { field: 'main_product_name', headerName: 'Main Product', width: 150, flex: 1 },
    { field: 'product_master_name', headerName: 'Product Name', width: 150, flex: 1 },
    { field: 'sub_product_name', headerName: 'Sub Product', width: 150, flex: 1 },
    { field: 'commission_type', headerName: 'Comm Type', width: 150, flex: 1 },
    { field: 'commission_source', headerName: 'Comm Source', width: 150, flex: 1 },
    { field: 'policy_type', headerName: 'Policy Type', width: 150, flex: 1 },
    {
      field: 'fixed_percentage',
      headerName: 'Fix%',
      width: 100,
      flex: 1,
      renderCell: renderFixedPercentage,
    },
    {
      field: 'addExtra',
      headerName: 'Add Extra',
      width: 100,
      flex: 1,
      renderCell: renderAddOrEditButton,
    },
  ];

  return (
    <Container maxWidth="xl"
      sx={{
        paddingLeft: 0, paddingRight: 0, '@media (min-width: 600px)': {
          paddingLeft: 0,
          paddingRight: 0,
        },
      }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        {/* Header Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <img
              src="/image.png"
              alt="module icon"
              style={{ width: '20px', marginLeft: '20px', backgroundColor: 'green' }}
            />
            <ModuleName moduleName="Commission Rate" pageName="Create" />
          </Box>
          <ButtonGroup variant="outlined" sx={{ borderRadius: 1, mr: 8 }}>
            <Button onClick={handleSave} sx={{ backgroundColor: '#528A7E', color: '#fff', '&:hover': { backgroundColor: '#3f6c62' } }}>
              Save
            </Button>
            <Button onClick={handleCancel} sx={{ borderColor: 'red', color: 'red' }}>
              Cancel
            </Button>
          </ButtonGroup>
        </Box>

        {/* Search and Title Section */}
        <Grid item xs={12}>
          <Box
            sx={{
              backgroundColor: '#f0f0f0',
              padding: '20px',
              borderRadius: '4px',
              height: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <h2 style={{ margin: 0, whiteSpace: 'nowrap' }}>Commission Rate Information</h2>
            <Box sx={{ width: '200px' }}>
              <SearchBar placeholder="Search......"
                onSearch={handleSearch} />
            </Box>
          </Box>
        </Grid>

        {/* Company Selection and Submit */}
        <Grid
          item
          xs={12}
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 2,
            mt: 2,
          }}
        >
          <DropDown
            label="Select Insurance Company"
            required
            options={[{ label: 'None', value: '' }, ...insuranceCompanies.map(company => ({ label: company.insurance_company_name, value: company.id }))]}
            value={selectedCompany}
            onChange={handleCompanyChange}
            width="250px"
          />
          <Button variant="contained" onClick={handleSubmit} sx={{ backgroundColor: '#528A7E', color: '#fff', '&:hover': { backgroundColor: '#3f6c62' } }}>
            Submit
          </Button>
        </Grid>

        {/* DataGrid Section */}
        <Box sx={{ height: 576, width: '100%' }}>
          <DataGrid
            rows={filteredRows}
            columns={columns}
            loading={loading}
            checkboxSelection
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            pagination
            sx={{
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#2e7d32',
              },
              '& .MuiDataGrid-columnHeaderTitle': {
                textAlign: 'center',
                width: '100%',
                fontWeight: 'bold',
              },
              '& .MuiDataGrid-columnHeader': {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              },
              '& .MuiDataGrid-cell': {
                backgroundColor: '#ffffff',
              },
            }}
          />
        </Box>
      </Box>

      {/* Popup Component */}
      <Popup
        open={openPopup}
        onClose={handleClosePopup}
        existingId={currentRow ? currentRow.id : null}
        rowData={currentRow}
        onSaveSuccess={handlePopupSaveSuccess} // Pass the callback to handle extra data
      />
    </Container>
  );
};

export default CommissionRatesPage;
