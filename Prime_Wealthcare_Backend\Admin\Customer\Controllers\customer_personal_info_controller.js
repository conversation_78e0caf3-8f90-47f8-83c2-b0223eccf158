const CustomerMaster = require('../Models/customer_personal_info_model');

// Create new Customer
exports.createCustomer = async (req, res, next) => {
    try {
        const customerData = req.body;
        const newCustomer = await CustomerMaster.create(customerData);
        res.status(200).json({ message: 'Customer has been created successfully!', id: newCustomer.id });
    } catch (error) {
        next(error);
    }
};

// Get all Customer
exports.getCustomer = async (req, res, next) => {
    try {
        const data = await CustomerMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get customer by ID
exports.getCostomerById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMaster.findById(id);
        if (customer) {
            res.status(200).json(customer);
        } else {
            res.status(404).json({ message: 'customer not found' });
        }
    } catch (error) {
        next(error);
    }
};
// Update customer by ID
exports.updateCustomer = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customerData = req.body;
        const data = await CustomerMaster.update(id, customerData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
exports.getCustomerAndAddress = async (req, res, next) => {
    try {
        // const { id } = req.params;
        // const customerData = req.body;
        const data = await CustomerMaster.fetchCustomerAndAddress();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};