const {
  getAllInsuranceBranches,
  insertInsuranceBranch,
  updateInsuranceBranch,
  deleteInsuranceBranch,
  reinstateInsuranceBranch,
  getBranchAndAgencyCode,
  getInsuranceBranchById,
  getInsuranceBranchesByName,       
  createBranchAndAgencyCode,
  newLastWeek,          // Add method for newLastWeek
  newThisWeek,         // Add method for newThisWeek
  deactivatedThisWeek, // Add method for deactivatedThisWeek
  deactivatedLastWeek, // Add method for deactivatedLastWeek
  editedThisWeek,      // Add method for editedThisWeek
  editedLastWeek,       // A
  getInsuranceBranchByInsuranceCompanyId
} = require('../Models/InsuranceBranch');

// Get all insurance branches with agency code
const getInsuranceBranches = async (req, res, next) => {
  try {
    const branches = await getAllInsuranceBranches();
    res.json(branches);
  } catch (error) {
    next(error);
  }
};

// Get insurance branch by ID
const getInsuranceBranchId = async (req, res, next) => {
  try {
    const branch = await getInsuranceBranchById(req.params.id);
    if (!branch) {
      return res.status(404).json({ error: 'Branch not found' });
    }
    res.json(branch);
  } catch (error) {
    next(error);
  }
};
const getInsuranceBranchInsuranceCompanyId = async (req, res, next) => {
  try {
    const branch = await getInsuranceBranchByInsuranceCompanyId(req.params.id);
    res.status(200).json(branch);
  } catch (error) {
    next(error);
  }
};

// Create a new insurance branch
const createInsuranceBranch = async (req, res, next) => {
  try {

    await insertInsuranceBranch(req.body);
    res.json({ message: 'Successfully inserted new insurance branch' });
  } catch (error) {
    next(error);
  }
};

// Update insurance branch by ID
const updateInsuranceBranchById = async (req, res, next) => {
  const { id } = req.params;
  const data = req.body;
  try {
    const result = await updateInsuranceBranch(id, data);
    res.status(200).json({ message: 'Branch updated successfully', result });
  } catch (error) {
    console.error('Error updating branch by ID:', error);
    next(error);
  }
};


// Soft delete insurance branch by ID
const deleteInsuranceBranchById = async (req, res, next) => {
  try {
    await deleteInsuranceBranch(req.params.id);
    res.json({ message: 'Successfully deactivated insurance branch' });
  } catch (error) {
    next(error);
  }
};

// Reinstate insurance branch by ID
const reinstateInsuranceBranchById = async (req, res, next) => {
  try {
    await reinstateInsuranceBranch(req.params.id);
    res.json({ message: 'Successfully reinstated insurance branch' });
  } catch (error) {
    next(error);
  }
};

// Create a new insurance branch and agency code in a transaction
const createBranchAndAgencyCodes = async (req, res, next) => {
  try {
    const { branchData, agencyCodeData } = req.body;
    await createBranchAndAgencyCode(branchData, agencyCodeData); // Use transaction to create both
    res.json({ message: 'Successfully created branch and agency code' });
  } catch (error) {
    next(error);
  }
};

const searchInsuranceBranch = async (req, res, next) => {
  const { name } = req.params;
   try {
    const branches = await getInsuranceBranchesByName(name);
     res.status(200).json(branches);
  } catch (error) {
    //console.error('Error searching insurance branches:', error);
    next(error);
  }
};

const getInsuranceBranchesByCriteria = async (req, res, next) => {
  const criteria = req.params.criteria;
  let data;

  try {
    switch (criteria) {
      case 'none':
        data = await getAllInsuranceBranches();
        break;
      case 'newLastWeek':
        data = await newLastWeek(); // Use method for newLastWeek
        break;
      case 'newThisWeek':
        data = await newThisWeek(); // Use method for newThisWeek
        break;
      case 'deactivatedThisWeek':
        data = await deactivatedThisWeek(); // Use method for deactivatedThisWeek
        break;
      case 'deactivatedLastWeek':
        data = await deactivatedLastWeek(); // Use method for deactivatedLastWeek
        break;
      case 'editedThisWeek':
        data = await editedThisWeek(); // Use method for editedThisWeek
        break;
      case 'editedLastWeek':
        data = await editedLastWeek(); // Use method for editedLastWeek
        break;
      default:
        return res.status(400).json({ message: 'Invalid criteria' });
    }

    res.status(200).json(data);
  } catch (error) {
    //console.error('Error fetching insurance branches by criteria:', error);
    next(error);
  }
};



module.exports = {
  getInsuranceBranches,
  getInsuranceBranchId,
  getInsuranceBranchInsuranceCompanyId,
  createInsuranceBranch,
  updateInsuranceBranchById,
  deleteInsuranceBranchById,
  reinstateInsuranceBranchById,
  createBranchAndAgencyCodes, 
  getInsuranceBranchesByCriteria,  // New: Handle dropdown filters
  searchInsuranceBranch     
  
};
