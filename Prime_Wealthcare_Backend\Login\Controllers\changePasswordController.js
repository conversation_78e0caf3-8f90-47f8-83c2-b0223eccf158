const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const knexConfig = require('../../knexfile');
const knex = require('knex')(knexConfig.development);
const SECRET_KEY = 'your_secret_key';
const SALT_ROUNDS = 10;

// Change password (First-time login)
const changePassword = async (req, res) => {
    const { userId, newPassword, confirmPassword, userType } = req.body;

    // Validate inputs
    if (newPassword !== confirmPassword) {
        return res.status(400).json({ message: 'Passwords do not match' });
    }

    if (newPassword.length < 6) {
        return res.status(400).json({ message: 'Password must be at least 6 characters long' });
    }

    try {
        // Hash the new password
        const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

        // Update the password in the correct table based on userType
        if (userType === 'agent') {
            // Update agent password
            await knex('agents').where({ agent_id: userId }).update({ password: hashedPassword });
        } else if (userType === 'employee') {
            // Update employee password
            await knex('employee_personal_info').where({ user_id: userId }).update({ password: hashedPassword });
        } else {
            return res.status(400).json({ message: 'Invalid user type' });
        }

        // Generate a new JWT token after password change
        const token = jwt.sign(
            { userId: userId, userType: userType },
            SECRET_KEY,
            { expiresIn: '1h' }
        );

        return res.status(200).json({ message: 'Password changed successfully. Please log in with the new password.', token });
    } catch (error) {
        return res.status(500).json({ message: error.message });
    }
};

module.exports = {
    changePassword,
};
