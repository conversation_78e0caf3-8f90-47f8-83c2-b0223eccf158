/**
 * Generates XML for nominee details with support for dynamic fields
 * @param {Object} nomineeData - Nominee data object
 * @param {Number} index - Optional index for ID generation
 * @param {Boolean} useEmptyFields - If true, returns completely empty fields
 * @returns {string} - XML string with nominee details
 */
const generateNomineeDetailsXML = (nomineeData = {}, index = 0, useEmptyFields = false) => {
    // If useEmptyFields is true, return completely empty fields
    if (useEmptyFields) {
        return `<NomineeDetails>
            <Nominee>
                <NomineeID>1</NomineeID>
                <NomineeName></NomineeName>
                <NomineeGender></NomineeGender>
                <NomineeDOB></NomineeDOB>
                <NomineeAge></NomineeAge>
                <NomineeMobileNo></NomineeMobileNo>
                <NomineeEmailID></NomineeEmailID>
                <NomineePresentAddress></NomineePresentAddress>
                <NomineePermanentAddress></NomineePermanentAddress>
                <NomineeRelationshipWithProposer></NomineeRelationshipWithProposer>
                <NomineePercentage>0</NomineePercentage>
                <NomineeAccountNo></NomineeAccountNo>
                <NomineeIFSCMICRCode></NomineeIFSCMICRCode>
                <NomineeBankName></NomineeBankName>
                <NomineeBankAccountHolderName></NomineeBankAccountHolderName>
                <Appointee>
                    <AppointeeID>1</AppointeeID>
                    <AppointeeName></AppointeeName>
                    <AppointeeGender></AppointeeGender>
                    <AppointeeDOB></AppointeeDOB>
                    <AppointeeAge></AppointeeAge>
                    <AppointeeMobileNo></AppointeeMobileNo>
                    <AppointeeEmailID></AppointeeEmailID>
                    <AppointeePresentAddress></AppointeePresentAddress>
                    <AppointeePermanentAddress></AppointeePermanentAddress>
                    <AppointeeRelationshipWithProposer></AppointeeRelationshipWithProposer>
                    <AppointeePercentage></AppointeePercentage>
                    <AppointeeAccountNo></AppointeeAccountNo>
                    <AppointeeIFSCMICRCode></AppointeeIFSCMICRCode>
                    <AppointeeBankName></AppointeeBankName>
                    <AppointeeBankAccountHolderName></AppointeeBankAccountHolderName>
                </Appointee>
            </Nominee>
        </NomineeDetails>`;
    }

    // Handle different data structures - some files use direct properties, others use nested objects
    const nominee = {
        // If nomineeData has direct properties (like in AdvantageToUpProposalSoapService)
        id: nomineeData.nominee_id || nomineeData.id || (index + 1),
        name: nomineeData.nominee_name || nomineeData.name || '',
        gender: nomineeData.nominee_gender || nomineeData.gender || '',
        dob: nomineeData.nominee_dob || nomineeData.dob || '',
        age: nomineeData.nominee_age || nomineeData.age || '',
        relation: nomineeData.nominee_relation || nomineeData.relationshipWithProposer || '',
        percentage: nomineeData.nominee_percentage || nomineeData.percentage || '0',
        
        // Optional fields with defaults
        mobileNo: nomineeData.nominee_mobile || nomineeData.mobileNo || '',
        emailId: nomineeData.nominee_email || nomineeData.emailId || '',
        presentAddress: nomineeData.nominee_present_address || nomineeData.presentAddress || '',
        permanentAddress: nomineeData.nominee_permanent_address || nomineeData.permanentAddress || '',
        accountNo: nomineeData.nominee_account_no || nomineeData.accountNo || '',
        ifscCode: nomineeData.nominee_ifsc || nomineeData.ifscCode || '',
        bankName: nomineeData.nominee_bank_name || nomineeData.bankName || '',
        accountHolderName: nomineeData.nominee_account_holder || nomineeData.accountHolderName || '',
    };
    
    // Handle appointee data similarly
    const appointee = {
        id: nomineeData.appointee_id || (nomineeData.appointee && nomineeData.appointee.id) || (index + 1),
        name: nomineeData.appointee_name || (nomineeData.appointee && nomineeData.appointee.name) || '',
        gender: nomineeData.appointee_gender || (nomineeData.appointee && nomineeData.appointee.gender) || '',
        dob: nomineeData.appointee_dob || (nomineeData.appointee && nomineeData.appointee.dob) || '',
        age: nomineeData.appointee_age || (nomineeData.appointee && nomineeData.appointee.age) || '',
        relation: nomineeData.appointee_relation || (nomineeData.appointee && nomineeData.appointee.relationshipWithProposer) || '',
        
        // Optional fields with defaults
        mobileNo: nomineeData.appointee_mobile || (nomineeData.appointee && nomineeData.appointee.mobileNo) || '',
        emailId: nomineeData.appointee_email || (nomineeData.appointee && nomineeData.appointee.emailId) || '',
        presentAddress: nomineeData.appointee_present_address || (nomineeData.appointee && nomineeData.appointee.presentAddress) || '',
        permanentAddress: nomineeData.appointee_permanent_address || (nomineeData.appointee && nomineeData.appointee.permanentAddress) || '',
        percentage: nomineeData.appointee_percentage || (nomineeData.appointee && nomineeData.appointee.percentage) || '',
        accountNo: nomineeData.appointee_account_no || (nomineeData.appointee && nomineeData.appointee.accountNo) || '',
        ifscCode: nomineeData.appointee_ifsc || (nomineeData.appointee && nomineeData.appointee.ifscCode) || '',
        bankName: nomineeData.appointee_bank_name || (nomineeData.appointee && nomineeData.appointee.bankName) || '',
        accountHolderName: nomineeData.appointee_account_holder || (nomineeData.appointee && nomineeData.appointee.accountHolderName) || '',
    };

    return `<NomineeDetails>
        <Nominee>
            <NomineeID>1</NomineeID>
            <NomineeName>${nominee.name}</NomineeName>
            <NomineeGender>${nominee.gender}</NomineeGender>
            <NomineeDOB>${nominee.dob}</NomineeDOB>
            <NomineeAge>${nominee.age}</NomineeAge>
            <NomineeMobileNo>${nominee.mobileNo}</NomineeMobileNo>
            <NomineeEmailID>${nominee.emailId}</NomineeEmailID>
            <NomineePresentAddress>${nominee.presentAddress}</NomineePresentAddress>
            <NomineePermanentAddress>${nominee.permanentAddress}</NomineePermanentAddress>
            <NomineeRelationshipWithProposer>${nominee.relation}</NomineeRelationshipWithProposer>
            <NomineePercentage>${nominee.percentage}</NomineePercentage>
            <NomineeAccountNo>${nominee.accountNo}</NomineeAccountNo>
            <NomineeIFSCMICRCode>${nominee.ifscCode}</NomineeIFSCMICRCode>
            <NomineeBankName>${nominee.bankName}</NomineeBankName>
            <NomineeBankAccountHolderName>${nominee.accountHolderName}</NomineeBankAccountHolderName>
            <Appointee>
                <AppointeeID>1</AppointeeID>
                <AppointeeName>${appointee.name}</AppointeeName>
                <AppointeeGender>${appointee.gender}</AppointeeGender>
                <AppointeeDOB>${appointee.dob}</AppointeeDOB>
                <AppointeeAge>${appointee.age}</AppointeeAge>
                <AppointeeMobileNo>${appointee.mobileNo}</AppointeeMobileNo>
                <AppointeeEmailID>${appointee.emailId}</AppointeeEmailID>
                <AppointeePresentAddress>${appointee.presentAddress}</AppointeePresentAddress>
                <AppointeePermanentAddress>${appointee.permanentAddress}</AppointeePermanentAddress>
                <AppointeeRelationshipWithProposer>${appointee.relation}</AppointeeRelationshipWithProposer>
                <AppointeePercentage>${appointee.percentage}</AppointeePercentage>
                <AppointeeAccountNo>${appointee.accountNo}</AppointeeAccountNo>
                <AppointeeIFSCMICRCode>${appointee.ifscCode}</AppointeeIFSCMICRCode>
                <AppointeeBankName>${appointee.bankName}</AppointeeBankName>
                <AppointeeBankAccountHolderName>${appointee.accountHolderName}</AppointeeBankAccountHolderName>
            </Appointee>
        </Nominee>
    </NomineeDetails>`;
};

module.exports = {
    generateNomineeDetailsXML
};