import { createSlice } from '@reduxjs/toolkit';
import {
    getLocationByPincode,
    getLocationDetailsByPincode,
    getAreasByPincodeAndCity,
    getAllLocations,
    createArea,
    getLocationById,
    getLocationByCriteria,
    getLocationByName,
    updateArea,
    deleteArea,
    createLocation,
    deleteLocationByPincode,
    getAreaById
} from '../../actions/action';
import { toast } from 'react-toastify'; // Import toast for notifications

const initialState = {
    locations: [],
    location: null,
    areas: [],
    area: null,
    loading: 'idle',
    error: null,
};

const dataSlice = createSlice({
    name: 'area',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        // Handling locations
        builder
            .addCase(createLocation.pending, (state) => {
                state.loading = true;
            })
            .addCase(createLocation.fulfilled, (state, action) => {
                state.loading = false;
                toast.success('Location created successfully');
            })
            .addCase(createLocation.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to create location');
            })
            // Get Location by Id
            
            .addCase(getLocationById.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getLocationById.fulfilled, (state, action) => {
                state.loading = false;
                state.location = action.payload;
            })
            .addCase(getLocationById.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load location');
            })

            // Get all locations
            .addCase(getAllLocations.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAllLocations.fulfilled, (state, action) => {
                state.loading = false;
                state.locations = action.payload;
            })
            .addCase(getAllLocations.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load all locations');
            })

            .addCase(getAreasByPincodeAndCity.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(getAreasByPincodeAndCity.fulfilled, (state, action) => {
                state.loading = false;
                state.areas = action.payload;
            })
            .addCase(getAreasByPincodeAndCity.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load areas');
            })

            .addCase(getAreaById.pending, (state) => {
                state.loading = true;
            })
            .addCase(getAreaById.fulfilled, (state, action) => {
                state.loading = false;
                state.area = action.payload;
            })
            .addCase(getAreaById.rejected, (state, action) => {
                    state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load area');
            })

            // Handling location details by pincode
            .addCase(getLocationDetailsByPincode.pending, (state) => {
                state.loading = true;
            })
            .addCase(getLocationDetailsByPincode.fulfilled, (state, action) => {
                state.loading = false;
                state.locations = action.payload;
            })
            .addCase(getLocationDetailsByPincode.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to load location details');
            })

            // Handle pending state for getLocationByPincode
            .addCase(getLocationByPincode.pending, (state) => {
                state.loading = true;
                state.error = null; // Reset error state
            })
            .addCase(getLocationByPincode.fulfilled, (state, action) => {
                state.loading = false;
                state.locations = action.payload; // Set the location object as an array
                state.error = null; // Clear any previous errors
            })
            .addCase(getLocationByPincode.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || action.error.message;
                toast.error('Failed to find location');
            })

            .addCase(getLocationByCriteria.pending, (state) => {
                state.loading = true;
                state.error = null; // Reset error state
            })
            .addCase(getLocationByCriteria.fulfilled, (state, action) => {
                state.loading = false;
                state.locations = action.payload; // Set the location object as an array
                state.error = null; // Clear any previous errors
            })
            .addCase(getLocationByName.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || action.error.message;
                toast.error('Failed to find location');
            })
            .addCase(getLocationByName.pending, (state) => {
                state.loading = true;
                state.error = null; // Reset error state
            })
            .addCase(getLocationByName.fulfilled, (state, action) => {
                state.loading = false;
                state.locations = action.payload; // Set the location object as an array
                state.error = null; // Clear any previous errors
            })
            .addCase(getLocationByCriteria.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || action.error.message;
                toast.error('Failed to find location');
            })
            .addCase(deleteLocationByPincode.pending, (state, action) => {
                state.loading = true;
                state.error = null
            })
            .addCase(deleteLocationByPincode.fulfilled, (state, action) => {
                state.loading = false;
                toast.success('Area deleted successfully');
            })
            .addCase(deleteLocationByPincode.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
                toast.error('Failed to delete location');
            })

            // Create area
            .addCase(createArea.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(createArea.fulfilled, (state, action) => {
                state.areas.push(action.payload);
                toast.success('Area created successfully');
            })
            .addCase(createArea.rejected, (state, action) => {
                state.error = action.error.message;
                toast.error('Failed to create area');
            })

            .addCase(updateArea.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(updateArea.fulfilled, (state, action) => {
                state.areas.push(action.payload);
                const index = state.areas.findIndex(area => area.id === action.payload.id);

                if (index !== -1) {
                    state.areas[index] = action.payload;
                }
                toast.success('Area updated successfully');
            })
            .addCase(updateArea.rejected, (state, action) => {
                state.error = action.error.message;
                toast.error('Failed to update area');
            })

            .addCase(deleteArea.pending, (state, action) => {
                state.loading = true;
            })
            .addCase(deleteArea.fulfilled, (state, action) => {
                state.areas = state.areas.filter(area => area.id !== action.payload)
                toast.success('Area deleted successfully');
            })
            .addCase(deleteArea.rejected, (state, action) => {
                state.error = action.error.message;
                toast.error('Failed to delete area');
            })

    },
});

export default dataSlice.reducer;
