/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable('tasks', function (table) {
    // Primary key
    table.increments('id').primary();

    // Required fields
    table.string('task_code', 255).notNullable();
    table.string('title', 255).nullable();
    table.text('description').nullable();

    // Foreign key references
    table.integer('assigned_to').unsigned().notNullable();
    table.foreign('assigned_to').references('id').inTable('employee_personal_info').onDelete('CASCADE');

    table.integer('assigned_by').unsigned().notNullable();
    table.foreign('assigned_by').references('id').inTable('employee_personal_info').onDelete('CASCADE');

    // Status enum
    table.enu('status', ['To-Do', 'In Progress', 'Completed']).defaultTo('To-Do');

    // Timestamps
    table.timestamp('created_at').defaultTo(knex.fn.now()); // Timestamp for record creation
    table.timestamp('updated_at').defaultTo(knex.fn.now()); // Timestamp for record update
    table.integer('created_by').unsigned().nullable();
    table.integer('updated_by').unsigned().nullable();
  }
  );
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTable('tasks');
};
