const CustomerMaster = require('../Models/customer_address_model');

// Create new Customer
exports.createCustomerAddress = async (req, res, next) => {
    try {
        const customerData = req.body;
        await CustomerMaster.create(customerData);
        res.status(200).json({ message: 'Customer Address created successfully' });
    } catch (error) {
        next(error);
    }
};

// Get all Customer
exports.getCustomerAddress = async (req, res, next) => {
    try {
        const data = await CustomerMaster.getAll();
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};

// Get customer by ID
exports.getCostomerAddressById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMaster.findById(id);
        res.status(200).json(customer);
        // if (customer) {
        // } else {
        //     res.status(404).json({ message: 'customer Address not found' });
        // }
    } catch (error) {
        next(error);
    }
};
// Get customer by ID
exports.getCostomerAddressByCustomerId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customer = await CustomerMaster.findByCustomerId(id);
        res.status(200).json(customer);
        // if (customer) {
        // } else {
        //     res.status(404).json({ message: 'customer not found' });
        // }
    } catch (error) {
        next(error);
    }
};
// Update customer by ID
exports.updateCustomerAddress = async (req, res, next) => {
    try {
        const { id } = req.params;
        const customerData = req.body;
        const data = await CustomerMaster.update(id, customerData);
        res.status(200).json(data);
    } catch (error) {
        next(error);
    }
};
// Delete a role by ID
exports.deleteCustomerAddress = async (req, res, next) => {
    try {
        const { id } = req.params;
        const affectedRows = await CustomerMaster.deleteById(id);
        if (!affectedRows) {
            return res.status(404).json({ message: 'Address not found' });
        }
        res.status(204).json({ message: 'group deleted successfully' });
    } catch (error) {
        next(error);
    }
};