exports.getCurrentTimestamp = () => {
    const now = new Date();
    return now.getFullYear() + '-' +
        ('0' + (now.getMonth() + 1)).slice(-2) + '-' +
        ('0' + now.getDate()).slice(-2) + ' ' +
        ('0' + now.getHours()).slice(-2) + ':' +
        ('0' + now.getMinutes()).slice(-2) + ':' +
        ('0' + now.getSeconds()).slice(-2);
};

exports.formatDateToDDMMYYYY = (date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};

exports.calculateDOBFromAgeBand = (ageBand) => {
    const currentYear = new Date().getFullYear();
    const [minAge, maxAge] = ageBand.split('-').map(Number);
    const averageAge = maxAge ? (minAge + maxAge) / 2 : minAge;
    const birthYear = currentYear - averageAge;
    return new Date(birthYear, 0, 1).toISOString().split('T')[0];
};

exports.toProperCase = (str) => {
    return str.toLowerCase().replace(/\b\w/g, letter => letter.toUpperCase());
};