const knexConfig = require('../../../knexfile');
const db = require('knex')(knexConfig.development);
const { getCurrentTimestamp } = require('../../../Reusable/reusable');


const create = async (data) => {
    try {
        console.log('this is the data from employee address', data)
        return await db('employee_address').insert(data);
    } catch (error) {
        console.error('Error inserting Employee Address :', error);
        throw error;
    }
};

const getAll = async () => {
    try {
        const Employees = await db('employee_address').select('*');
        return Employees;
    } catch (error) {
        console.error('Error retrieving Employee Address:', error);
        throw error;
    }
};

const findById = async (id) => {
    try {
        const Employee = await db('employee_address').where({ id }).first();
        return Employee;
    } catch (error) {
        throw error;
    }
};
const findByEmployeeId = async (id) => {
    try {
        const Employee = await db('employee_address').where('employee_id', id).first();
        return Employee;
    } catch (error) {
        throw error;
    }
};

// Update Employee by ID
const update = async (id, EmployeeData) => {
    if (!id) throw new Error("Employee Address ID is required");

    try {
        EmployeeData.updated_at = getCurrentTimestamp();

        const result = await db('employee_address').where('id', id).update(EmployeeData);
        if (result) {

        } else {
            console.error(`No Employee Address found with ID: ${id} to update`);
        }
    } catch (error) {
        console.error(`Error updating Employee Address with ID: ${id}`, error);
        throw error;
    }
};
// Delete role by ID
const deleteById = async (id) => {
    try {
        const result = await db('employee_address').where({ id }).update({ status: 0, updated_at: getCurrentTimestamp() });
        return result;
    } catch (error) {
        throw error;
    }
};

module.exports = {
    create,
    getAll,
    findById,
    update,
    deleteById,
    findByEmployeeId
}